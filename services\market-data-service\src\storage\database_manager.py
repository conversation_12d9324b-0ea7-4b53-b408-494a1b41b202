"""
Database Manager for Job Market Data
Handles storage and retrieval of job posting data
Part of FR-5.2: Job Market Data Ingestion Service
"""

import asyncio
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
import json
import os

import asyncpg
from sqlalchemy import create_engine, text
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker

from scrapers.base_scraper import JobPosting
from utils.config import Config

class DatabaseManager:
    """Manages database operations for job market data"""
    
    def __init__(self):
        self.config = Config()
        self.logger = logging.getLogger('database_manager')
        
        # Database connection settings
        self.db_url = self.config.get_database_url()
        self.async_engine = create_async_engine(self.db_url, echo=False)
        self.async_session = sessionmaker(
            self.async_engine, 
            class_=AsyncSession, 
            expire_on_commit=False
        )

    async def store_jobs(self, jobs: List[JobPosting]) -> int:
        """Store processed job postings in database"""
        if not jobs:
            return 0
        
        self.logger.info(f"Storing {len(jobs)} jobs in database")
        
        stored_count = 0
        batch_size = 50
        
        # Process jobs in batches
        for i in range(0, len(jobs), batch_size):
            batch = jobs[i:i + batch_size]
            
            try:
                batch_stored = await self._store_job_batch(batch)
                stored_count += batch_stored
                
                self.logger.debug(f"Stored batch {i//batch_size + 1}: {batch_stored} jobs")
                
            except Exception as e:
                self.logger.error(f"Error storing job batch {i//batch_size + 1}: {e}")
                continue
        
        self.logger.info(f"Successfully stored {stored_count} jobs")
        return stored_count

    async def _store_job_batch(self, jobs: List[JobPosting]) -> int:
        """Store a batch of jobs with upsert logic"""
        async with self.async_session() as session:
            try:
                stored_count = 0
                
                for job in jobs:
                    # Check if job already exists
                    existing_job = await self._find_existing_job(session, job)
                    
                    if existing_job:
                        # Update existing job if it's newer or has more data
                        if self._should_update_job(existing_job, job):
                            await self._update_job(session, existing_job['id'], job)
                            stored_count += 1
                    else:
                        # Insert new job
                        await self._insert_job(session, job)
                        stored_count += 1
                
                await session.commit()
                return stored_count
                
            except Exception as e:
                await session.rollback()
                self.logger.error(f"Error in job batch storage: {e}")
                raise

    async def _find_existing_job(self, session: AsyncSession, job: JobPosting) -> Optional[Dict]:
        """Find existing job by external ID or content similarity"""
        # First try to find by external ID
        if job.external_id:
            query = text("""
                SELECT id, external_id, title, company, location, scraped_at, updated_at
                FROM job_postings 
                WHERE external_id = :external_id AND source = :source
            """)
            
            result = await session.execute(query, {
                'external_id': job.external_id,
                'source': job.source
            })
            
            row = result.fetchone()
            if row:
                return dict(row._mapping)
        
        # If no external ID match, try content-based matching
        query = text("""
            SELECT id, external_id, title, company, location, scraped_at, updated_at
            FROM job_postings 
            WHERE title = :title 
            AND company = :company 
            AND location = :location
            AND source = :source
            AND scraped_at > :recent_cutoff
        """)
        
        result = await session.execute(query, {
            'title': job.title,
            'company': job.company,
            'location': job.location,
            'source': job.source,
            'recent_cutoff': datetime.now() - timedelta(days=7)
        })
        
        row = result.fetchone()
        return dict(row._mapping) if row else None

    def _should_update_job(self, existing_job: Dict, new_job: JobPosting) -> bool:
        """Determine if existing job should be updated with new data"""
        # Update if new job has more recent data
        if new_job.posted_date and existing_job.get('scraped_at'):
            if new_job.posted_date > existing_job['scraped_at']:
                return True
        
        # Update if new job has more complete data
        new_data_score = self._calculate_data_completeness(new_job)
        
        # For existing job, we'd need to fetch full data to compare
        # For now, update if new job seems significantly more complete
        return new_data_score > 0.7

    def _calculate_data_completeness(self, job: JobPosting) -> float:
        """Calculate completeness score for job data"""
        score = 0.0
        total_fields = 10
        
        # Required fields
        if job.title: score += 1
        if job.company: score += 1
        if job.description and len(job.description) > 100: score += 1
        
        # Optional but valuable fields
        if job.location: score += 1
        if job.salary_min or job.salary_max: score += 1
        if job.skills and len(job.skills) > 0: score += 1
        if job.requirements: score += 1
        if job.experience_level: score += 1
        if job.job_type: score += 1
        if job.industry: score += 1
        
        return score / total_fields

    async def _insert_job(self, session: AsyncSession, job: JobPosting):
        """Insert new job posting"""
        query = text("""
            INSERT INTO job_postings (
                title, company, description, requirements, skills, experience_level,
                location, salary_min, salary_max, job_type, industry, source,
                external_id, external_url, posted_date, expires_date, is_active,
                vector_embedding, scraped_at, created_at, updated_at
            ) VALUES (
                :title, :company, :description, :requirements, :skills, :experience_level,
                :location, :salary_min, :salary_max, :job_type, :industry, :source,
                :external_id, :external_url, :posted_date, :expires_date, :is_active,
                :vector_embedding, :scraped_at, :created_at, :updated_at
            )
        """)
        
        await session.execute(query, {
            'title': job.title,
            'company': job.company,
            'description': job.description,
            'requirements': job.requirements,
            'skills': json.dumps(job.skills) if job.skills else None,
            'experience_level': job.experience_level,
            'location': job.location,
            'salary_min': job.salary_min,
            'salary_max': job.salary_max,
            'job_type': job.job_type,
            'industry': job.industry,
            'source': job.source,
            'external_id': job.external_id,
            'external_url': job.external_url,
            'posted_date': job.posted_date,
            'expires_date': job.expires_date,
            'is_active': True,
            'vector_embedding': job.vector_embedding,
            'scraped_at': datetime.now(),
            'created_at': datetime.now(),
            'updated_at': datetime.now()
        })

    async def _update_job(self, session: AsyncSession, job_id: str, job: JobPosting):
        """Update existing job posting"""
        query = text("""
            UPDATE job_postings SET
                title = :title,
                company = :company,
                description = :description,
                requirements = :requirements,
                skills = :skills,
                experience_level = :experience_level,
                location = :location,
                salary_min = :salary_min,
                salary_max = :salary_max,
                job_type = :job_type,
                industry = :industry,
                external_url = :external_url,
                posted_date = :posted_date,
                expires_date = :expires_date,
                vector_embedding = :vector_embedding,
                scraped_at = :scraped_at,
                updated_at = :updated_at
            WHERE id = :job_id
        """)
        
        await session.execute(query, {
            'job_id': job_id,
            'title': job.title,
            'company': job.company,
            'description': job.description,
            'requirements': job.requirements,
            'skills': json.dumps(job.skills) if job.skills else None,
            'experience_level': job.experience_level,
            'location': job.location,
            'salary_min': job.salary_min,
            'salary_max': job.salary_max,
            'job_type': job.job_type,
            'industry': job.industry,
            'external_url': job.external_url,
            'posted_date': job.posted_date,
            'expires_date': job.expires_date,
            'vector_embedding': job.vector_embedding,
            'scraped_at': datetime.now(),
            'updated_at': datetime.now()
        })

    async def cleanup_old_jobs(self, cutoff_date: datetime) -> int:
        """Remove old and expired job postings"""
        async with self.async_session() as session:
            try:
                # Mark old jobs as inactive first
                deactivate_query = text("""
                    UPDATE job_postings 
                    SET is_active = false, updated_at = :now
                    WHERE (scraped_at < :cutoff_date OR expires_date < :now)
                    AND is_active = true
                """)
                
                result = await session.execute(deactivate_query, {
                    'cutoff_date': cutoff_date,
                    'now': datetime.now()
                })
                
                deactivated_count = result.rowcount
                
                # Delete very old jobs (older than 60 days)
                old_cutoff = datetime.now() - timedelta(days=60)
                delete_query = text("""
                    DELETE FROM job_postings 
                    WHERE scraped_at < :old_cutoff
                """)
                
                result = await session.execute(delete_query, {
                    'old_cutoff': old_cutoff
                })
                
                deleted_count = result.rowcount
                
                await session.commit()
                
                self.logger.info(f"Cleanup: {deactivated_count} jobs deactivated, {deleted_count} jobs deleted")
                return deactivated_count + deleted_count
                
            except Exception as e:
                await session.rollback()
                self.logger.error(f"Error during job cleanup: {e}")
                raise

    async def get_job_statistics(self) -> Dict[str, Any]:
        """Get comprehensive job market statistics"""
        async with self.async_session() as session:
            try:
                # Total active jobs
                total_query = text("SELECT COUNT(*) as count FROM job_postings WHERE is_active = true")
                total_result = await session.execute(total_query)
                total_jobs = total_result.fetchone()[0]
                
                # Jobs by source
                source_query = text("""
                    SELECT source, COUNT(*) as count 
                    FROM job_postings 
                    WHERE is_active = true 
                    GROUP BY source
                """)
                source_result = await session.execute(source_query)
                jobs_by_source = {row[0]: row[1] for row in source_result.fetchall()}
                
                # Jobs by experience level
                exp_query = text("""
                    SELECT experience_level, COUNT(*) as count 
                    FROM job_postings 
                    WHERE is_active = true AND experience_level IS NOT NULL
                    GROUP BY experience_level
                """)
                exp_result = await session.execute(exp_query)
                jobs_by_experience = {row[0]: row[1] for row in exp_result.fetchall()}
                
                # Jobs by industry
                industry_query = text("""
                    SELECT industry, COUNT(*) as count 
                    FROM job_postings 
                    WHERE is_active = true AND industry IS NOT NULL
                    GROUP BY industry 
                    ORDER BY count DESC 
                    LIMIT 10
                """)
                industry_result = await session.execute(industry_query)
                top_industries = {row[0]: row[1] for row in industry_result.fetchall()}
                
                # Average salary by experience level
                salary_query = text("""
                    SELECT experience_level, 
                           AVG(salary_min) as avg_min, 
                           AVG(salary_max) as avg_max
                    FROM job_postings 
                    WHERE is_active = true 
                    AND salary_min IS NOT NULL 
                    AND salary_max IS NOT NULL
                    GROUP BY experience_level
                """)
                salary_result = await session.execute(salary_query)
                salary_by_experience = {}
                for row in salary_result.fetchall():
                    salary_by_experience[row[0]] = {
                        'avg_min': int(row[1]) if row[1] else None,
                        'avg_max': int(row[2]) if row[2] else None
                    }
                
                # Recent scraping activity
                recent_query = text("""
                    SELECT DATE(scraped_at) as date, COUNT(*) as count
                    FROM job_postings 
                    WHERE scraped_at > :week_ago
                    GROUP BY DATE(scraped_at)
                    ORDER BY date DESC
                """)
                recent_result = await session.execute(recent_query, {
                    'week_ago': datetime.now() - timedelta(days=7)
                })
                recent_activity = {str(row[0]): row[1] for row in recent_result.fetchall()}
                
                return {
                    'total_active_jobs': total_jobs,
                    'jobs_by_source': jobs_by_source,
                    'jobs_by_experience': jobs_by_experience,
                    'top_industries': top_industries,
                    'salary_by_experience': salary_by_experience,
                    'recent_activity': recent_activity,
                    'last_updated': datetime.now().isoformat()
                }
                
            except Exception as e:
                self.logger.error(f"Error getting job statistics: {e}")
                raise

    async def update_scraping_statistics(self, scraping_results: Dict[str, Any]):
        """Update scraping statistics and metadata"""
        async with self.async_session() as session:
            try:
                # Store scraping run metadata
                query = text("""
                    INSERT INTO scraping_runs (
                        start_time, end_time, duration, total_jobs, 
                        processed_jobs, stored_jobs, scrapers_used, 
                        errors, created_at
                    ) VALUES (
                        :start_time, :end_time, :duration, :total_jobs,
                        :processed_jobs, :stored_jobs, :scrapers_used,
                        :errors, :created_at
                    )
                """)
                
                await session.execute(query, {
                    'start_time': scraping_results.get('start_time'),
                    'end_time': scraping_results.get('end_time'),
                    'duration': scraping_results.get('duration'),
                    'total_jobs': scraping_results.get('total_jobs', 0),
                    'processed_jobs': scraping_results.get('processed_jobs', 0),
                    'stored_jobs': scraping_results.get('stored_jobs', 0),
                    'scrapers_used': json.dumps(list(scraping_results.get('scrapers', {}).keys())),
                    'errors': json.dumps(scraping_results.get('errors', [])),
                    'created_at': datetime.now()
                })
                
                await session.commit()
                
            except Exception as e:
                await session.rollback()
                self.logger.error(f"Error updating scraping statistics: {e}")

    async def close(self):
        """Close database connections"""
        await self.async_engine.dispose()
