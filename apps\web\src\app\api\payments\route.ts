/**
 * Payment API Routes
 * 
 * Handles payment processing, subscription management, and billing operations
 */

import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { subscriptionService } from '@/lib/payments/subscription-service'
import { stripeService } from '@/lib/payments/stripe-service'
import { prisma } from '@/lib/db'
import { z } from 'zod'

// Request schemas
const CreateSubscriptionSchema = z.object({
  planId: z.string(),
  paymentMethodId: z.string().optional(),
  couponCode: z.string().optional(),
  billingCycle: z.enum(['monthly', 'yearly']).default('monthly')
})

const UpdateSubscriptionSchema = z.object({
  planId: z.string().optional(),
  cancelAtPeriodEnd: z.boolean().optional()
})

const CreateCheckoutSessionSchema = z.object({
  planId: z.string(),
  billingCycle: z.enum(['monthly', 'yearly']).default('monthly'),
  successUrl: z.string().url(),
  cancelUrl: z.string().url(),
  couponCode: z.string().optional(),
  trialDays: z.number().optional()
})

/**
 * GET /api/payments
 * Get payment-related data
 */
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const action = searchParams.get('action')

    switch (action) {
      case 'plans':
        const plans = await subscriptionService.getPlans()
        return NextResponse.json({ success: true, plans })

      case 'subscription':
        const subscription = await subscriptionService.getUserSubscription(session.user.id)
        return NextResponse.json({ success: true, subscription })

      case 'payment-history':
        const limit = parseInt(searchParams.get('limit') || '10')
        const payments = await subscriptionService.getPaymentHistory(session.user.id, limit)
        return NextResponse.json({ success: true, payments })

      case 'billing-portal':
        const returnUrl = searchParams.get('returnUrl') || `${process.env.NEXTAUTH_URL}/dashboard/billing`
        const portalUrl = await subscriptionService.createBillingPortalSession(
          session.user.id,
          returnUrl
        )
        return NextResponse.json({ success: true, url: portalUrl })

      default:
        return NextResponse.json({ error: 'Invalid action' }, { status: 400 })
    }
  } catch (error) {
    console.error('Payment GET error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

/**
 * POST /api/payments
 * Handle payment operations
 */
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { action } = body

    switch (action) {
      case 'create-subscription':
        const subscriptionData = CreateSubscriptionSchema.parse(body)
        const result = await subscriptionService.createSubscription({
          userId: session.user.id,
          planId: subscriptionData.planId,
          paymentMethodId: subscriptionData.paymentMethodId,
          couponCode: subscriptionData.couponCode
        })
        return NextResponse.json({ 
          success: true, 
          subscription: result.subscription,
          clientSecret: result.clientSecret
        })

      case 'create-checkout-session':
        const checkoutData = CreateCheckoutSessionSchema.parse(body)
        
        // Get or create Stripe customer
        const user = await prisma.user.findUnique({
          where: { id: session.user.id }
        })
        
        if (!user) {
          return NextResponse.json({ error: 'User not found' }, { status: 404 })
        }

        let customerId = ''
        const existingSubscription = await subscriptionService.getUserSubscription(session.user.id)
        
        if (existingSubscription?.stripeCustomerId) {
          customerId = existingSubscription.stripeCustomerId
        } else {
          const customer = await stripeService.createCustomer({
            email: user.email,
            name: user.name || undefined,
            metadata: { userId: user.id }
          })
          customerId = customer.id
        }

        // Get plan details
        const plan = await subscriptionService.getPlan(checkoutData.planId)
        if (!plan) {
          return NextResponse.json({ error: 'Plan not found' }, { status: 404 })
        }

        const priceId = checkoutData.billingCycle === 'yearly' 
          ? plan.stripePriceIdYearly 
          : plan.stripePriceIdMonthly

        if (!priceId) {
          return NextResponse.json({ error: 'Price not configured' }, { status: 400 })
        }

        const checkoutSession = await stripeService.createCheckoutSession(
          customerId,
          priceId,
          checkoutData.successUrl,
          checkoutData.cancelUrl,
          {
            mode: 'subscription',
            trialDays: checkoutData.trialDays,
            allowPromotionCodes: true
          }
        )

        return NextResponse.json({ 
          success: true, 
          sessionId: checkoutSession.id,
          url: checkoutSession.url
        })

      case 'create-payment-intent':
        const { amount, currency = 'usd', description } = body
        
        if (!amount || amount <= 0) {
          return NextResponse.json({ error: 'Invalid amount' }, { status: 400 })
        }

        const subscription = await subscriptionService.getUserSubscription(session.user.id)
        if (!subscription?.stripeCustomerId) {
          return NextResponse.json({ error: 'No customer found' }, { status: 400 })
        }

        const paymentIntent = await stripeService.createPaymentIntent({
          amount,
          currency,
          customerId: subscription.stripeCustomerId,
          description
        })

        return NextResponse.json({ 
          success: true, 
          clientSecret: paymentIntent.client_secret
        })

      default:
        return NextResponse.json({ error: 'Invalid action' }, { status: 400 })
    }
  } catch (error) {
    console.error('Payment POST error:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal server error' },
      { status: 500 }
    )
  }
}

/**
 * PUT /api/payments
 * Update payment-related data
 */
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { action } = body

    switch (action) {
      case 'update-subscription':
        const updateData = UpdateSubscriptionSchema.parse(body)
        const updatedSubscription = await subscriptionService.updateSubscription(
          session.user.id,
          updateData
        )
        return NextResponse.json({ 
          success: true, 
          subscription: updatedSubscription
        })

      case 'cancel-subscription':
        const { cancelAtPeriodEnd = true } = body
        const canceledSubscription = await subscriptionService.cancelSubscription(
          session.user.id,
          cancelAtPeriodEnd
        )
        return NextResponse.json({ 
          success: true, 
          subscription: canceledSubscription
        })

      case 'reactivate-subscription':
        const reactivatedSubscription = await subscriptionService.reactivateSubscription(
          session.user.id
        )
        return NextResponse.json({ 
          success: true, 
          subscription: reactivatedSubscription
        })

      default:
        return NextResponse.json({ error: 'Invalid action' }, { status: 400 })
    }
  } catch (error) {
    console.error('Payment PUT error:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal server error' },
      { status: 500 }
    )
  }
}

/**
 * DELETE /api/payments
 * Delete payment-related data
 */
export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const action = searchParams.get('action')

    switch (action) {
      case 'cancel-subscription-immediately':
        const canceledSubscription = await subscriptionService.cancelSubscription(
          session.user.id,
          false // Cancel immediately
        )
        return NextResponse.json({ 
          success: true, 
          subscription: canceledSubscription
        })

      default:
        return NextResponse.json({ error: 'Invalid action' }, { status: 400 })
    }
  } catch (error) {
    console.error('Payment DELETE error:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal server error' },
      { status: 500 }
    )
  }
}
