/**
 * Background Service Worker Tests
 * 
 * Comprehensive test suite for the background service worker
 * covering message handling, authentication, and API integration.
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import browser from 'webextension-polyfill'

// Mock browser APIs
vi.mock('webextension-polyfill', () => ({
  default: {
    runtime: {
      onInstalled: {
        addListener: vi.fn()
      },
      onMessage: {
        addListener: vi.fn()
      },
      sendMessage: vi.fn(),
      getManifest: vi.fn(() => ({ version: '1.0.0' }))
    },
    tabs: {
      onUpdated: {
        addListener: vi.fn()
      },
      onActivated: {
        addListener: vi.fn()
      },
      query: vi.fn(),
      sendMessage: vi.fn(),
      create: vi.fn()
    },
    commands: {
      onCommand: {
        addListener: vi.fn()
      }
    },
    storage: {
      local: {
        get: vi.fn(),
        set: vi.fn(),
        remove: vi.fn()
      },
      onChanged: {
        addListener: vi.fn()
      }
    },
    scripting: {
      executeScript: vi.fn()
    }
  }
}))

// Mock dependencies
vi.mock('../lib/auth/auth-manager', () => ({
  AuthManager: vi.fn().mockImplementation(() => ({
    initialize: vi.fn(),
    authenticate: vi.fn(),
    isAuthenticated: vi.fn(() => true),
    getToken: vi.fn(() => 'mock-token')
  }))
}))

vi.mock('../lib/api/api-client', () => ({
  ApiClient: vi.fn().mockImplementation(() => ({
    setAuthToken: vi.fn(),
    getUserProfile: vi.fn(),
    getAutofillData: vi.fn(),
    trackApplication: vi.fn()
  }))
}))

vi.mock('../lib/storage/storage-manager', () => ({
  StorageManager: vi.fn().mockImplementation(() => ({
    get: vi.fn(),
    set: vi.fn(),
    remove: vi.fn()
  }))
}))

vi.mock('../lib/analytics/analytics-manager', () => ({
  AnalyticsManager: vi.fn().mockImplementation(() => ({
    trackEvent: vi.fn(),
    trackError: vi.fn(),
    flush: vi.fn(),
    getStats: vi.fn()
  }))
}))

vi.mock('../lib/notifications/notification-manager', () => ({
  NotificationManager: vi.fn().mockImplementation(() => ({
    showWelcome: vi.fn(),
    showUpdate: vi.fn(),
    showToggleNotification: vi.fn(),
    showError: vi.fn()
  }))
}))

describe('BackgroundService', () => {
  let BackgroundService: any
  let backgroundService: any

  beforeEach(async () => {
    vi.clearAllMocks()
    
    // Import the BackgroundService class
    const module = await import('../background/background')
    BackgroundService = module.BackgroundService
    
    // Create a new instance for testing
    backgroundService = new BackgroundService()
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('Initialization', () => {
    it('should initialize successfully', async () => {
      expect(backgroundService).toBeDefined()
      expect(backgroundService.state).toBeDefined()
      expect(backgroundService.authManager).toBeDefined()
      expect(backgroundService.apiClient).toBeDefined()
    })

    it('should set up event listeners', () => {
      expect(browser.runtime.onInstalled.addListener).toHaveBeenCalled()
      expect(browser.runtime.onMessage.addListener).toHaveBeenCalled()
      expect(browser.tabs.onUpdated.addListener).toHaveBeenCalled()
      expect(browser.tabs.onActivated.addListener).toHaveBeenCalled()
    })

    it('should load stored state', async () => {
      const mockStoredData = {
        settings: { autoFillEnabled: true },
        userProfile: { id: 'user-123' },
        authToken: 'stored-token'
      }

      backgroundService.storageManager.get.mockResolvedValue(mockStoredData)
      
      await backgroundService.loadStoredState()
      
      expect(backgroundService.state.settings.autoFillEnabled).toBe(true)
      expect(backgroundService.state.userProfile.id).toBe('user-123')
      expect(backgroundService.state.isAuthenticated).toBe(true)
    })
  })

  describe('Message Handling', () => {
    it('should handle GET_STATE messages', async () => {
      const message = { type: 'GET_STATE' }
      const sender = { tab: { id: 123 } }
      
      const response = await backgroundService.handleMessage(message, sender, vi.fn())
      
      expect(response.success).toBe(true)
      expect(response.data).toHaveProperty('isAuthenticated')
      expect(response.data).toHaveProperty('settings')
    })

    it('should handle AUTHENTICATE messages with valid token', async () => {
      const message = { 
        type: 'AUTHENTICATE', 
        data: { token: 'valid-token' } 
      }
      const mockProfile = { id: 'user-123', email: '<EMAIL>' }
      
      backgroundService.apiClient.getUserProfile.mockResolvedValue(mockProfile)
      
      const response = await backgroundService.handleMessage(message, {}, vi.fn())
      
      expect(response.success).toBe(true)
      expect(response.data).toEqual(mockProfile)
      expect(backgroundService.state.isAuthenticated).toBe(true)
      expect(backgroundService.state.userProfile).toEqual(mockProfile)
    })

    it('should handle AUTHENTICATE messages with invalid token', async () => {
      const message = { 
        type: 'AUTHENTICATE', 
        data: { token: 'invalid-token' } 
      }
      
      backgroundService.apiClient.getUserProfile.mockRejectedValue(new Error('Invalid token'))
      
      const response = await backgroundService.handleMessage(message, {}, vi.fn())
      
      expect(response.success).toBe(false)
      expect(response.error).toBe('Invalid token')
    })

    it('should handle REQUEST_AUTOFILL messages', async () => {
      backgroundService.state.isAuthenticated = true
      
      const message = {
        type: 'REQUEST_AUTOFILL',
        data: {
          formFields: [
            { type: 'text', label: 'First Name', mappedTo: 'firstName' }
          ],
          jobDescription: 'Software Engineer position'
        }
      }
      
      const mockAutofillData = {
        firstName: 'John',
        email: '<EMAIL>'
      }
      
      backgroundService.apiClient.getAutofillData.mockResolvedValue(mockAutofillData)
      
      const response = await backgroundService.handleMessage(message, {}, vi.fn())
      
      expect(response.success).toBe(true)
      expect(response.data).toEqual(mockAutofillData)
    })

    it('should handle REQUEST_AUTOFILL when not authenticated', async () => {
      backgroundService.state.isAuthenticated = false
      
      const message = {
        type: 'REQUEST_AUTOFILL',
        data: { formFields: [] }
      }
      
      const response = await backgroundService.handleMessage(message, {}, vi.fn())
      
      expect(response.success).toBe(false)
      expect(response.error).toBe('Not authenticated')
    })

    it('should handle TRACK_APPLICATION messages', async () => {
      backgroundService.state.isAuthenticated = true
      backgroundService.state.settings.trackingEnabled = true
      
      const message = {
        type: 'TRACK_APPLICATION',
        data: {
          jobTitle: 'Software Engineer',
          company: 'Tech Corp',
          source: 'linkedin.com'
        }
      }
      
      backgroundService.apiClient.trackApplication.mockResolvedValue({ success: true })
      
      const response = await backgroundService.handleMessage(message, {}, vi.fn())
      
      expect(response.success).toBe(true)
      expect(backgroundService.apiClient.trackApplication).toHaveBeenCalledWith(message.data)
    })

    it('should handle UPDATE_SETTINGS messages', async () => {
      const message = {
        type: 'UPDATE_SETTINGS',
        data: { autoFillEnabled: false, confidenceThreshold: 0.9 }
      }
      
      const response = await backgroundService.handleMessage(message, {}, vi.fn())
      
      expect(response.success).toBe(true)
      expect(backgroundService.state.settings.autoFillEnabled).toBe(false)
      expect(backgroundService.state.settings.confidenceThreshold).toBe(0.9)
    })

    it('should handle unknown message types', async () => {
      const message = { type: 'UNKNOWN_TYPE' }
      
      const response = await backgroundService.handleMessage(message, {}, vi.fn())
      
      expect(response.success).toBe(false)
      expect(response.error).toBe('Unknown message type')
    })
  })

  describe('Tab Management', () => {
    it('should handle tab updates for supported sites', async () => {
      const tabId = 123
      const changeInfo = { status: 'complete' }
      const tab = { 
        id: tabId, 
        url: 'https://www.linkedin.com/jobs/view/123456' 
      }
      
      await backgroundService.handleTabUpdated(tabId, changeInfo, tab)
      
      expect(browser.scripting.executeScript).toHaveBeenCalledWith({
        target: { tabId },
        files: ['content/content.js']
      })
    })

    it('should ignore tab updates for unsupported sites', async () => {
      const tabId = 123
      const changeInfo = { status: 'complete' }
      const tab = { 
        id: tabId, 
        url: 'https://www.google.com' 
      }
      
      await backgroundService.handleTabUpdated(tabId, changeInfo, tab)
      
      expect(browser.scripting.executeScript).not.toHaveBeenCalled()
    })

    it('should handle tab activation', async () => {
      const activeInfo = { tabId: 456 }
      
      await backgroundService.handleTabActivated(activeInfo)
      
      expect(backgroundService.state.activeTab).toBe(456)
    })
  })

  describe('Installation Handling', () => {
    it('should handle first-time installation', async () => {
      const details = { reason: 'install' }
      
      await backgroundService.handleInstalled(details)
      
      expect(backgroundService.notificationManager.showWelcome).toHaveBeenCalled()
      expect(browser.tabs.create).toHaveBeenCalledWith({
        url: 'https://careercraft.onlinejobsearchhelp.com/extension/welcome'
      })
    })

    it('should handle extension updates', async () => {
      const details = { reason: 'update', previousVersion: '0.9.0' }
      
      await backgroundService.handleInstalled(details)
      
      expect(backgroundService.notificationManager.showUpdate).toHaveBeenCalled()
    })
  })

  describe('Command Handling', () => {
    it('should handle toggle-autofill command', async () => {
      backgroundService.state.settings.autoFillEnabled = true
      
      await backgroundService.handleCommand('toggle-autofill')
      
      expect(backgroundService.state.settings.autoFillEnabled).toBe(false)
      expect(backgroundService.notificationManager.showToggleNotification).toHaveBeenCalledWith(false)
    })

    it('should handle quick-fill command', async () => {
      backgroundService.state.activeTab = 123
      
      await backgroundService.handleCommand('quick-fill')
      
      expect(browser.tabs.sendMessage).toHaveBeenCalledWith(123, {
        type: 'QUICK_FILL'
      })
    })
  })

  describe('Authentication Management', () => {
    it('should initialize authentication with valid stored token', async () => {
      backgroundService.state.isAuthenticated = true
      const mockProfile = { id: 'user-123' }
      
      backgroundService.apiClient.getUserProfile.mockResolvedValue(mockProfile)
      
      await backgroundService.initializeAuth()
      
      expect(backgroundService.state.userProfile).toEqual(mockProfile)
      expect(backgroundService.state.userId).toBe('user-123')
    })

    it('should handle expired authentication tokens', async () => {
      backgroundService.state.isAuthenticated = true
      
      backgroundService.apiClient.getUserProfile.mockRejectedValue(new Error('Token expired'))
      
      await backgroundService.initializeAuth()
      
      expect(backgroundService.state.isAuthenticated).toBe(false)
      expect(backgroundService.storageManager.remove).toHaveBeenCalledWith(['authToken'])
    })
  })

  describe('Form Detection Handling', () => {
    it('should handle form detected notifications', async () => {
      const data = {
        url: 'https://www.linkedin.com/jobs/view/123',
        formType: 'job-application',
        fieldCount: 10,
        confidence: 0.95
      }
      const tabId = 123
      
      const response = await backgroundService.handleFormDetected(data, tabId)
      
      expect(response.success).toBe(true)
      expect(backgroundService.analyticsManager.trackEvent).toHaveBeenCalledWith('form_detected', {
        url: data.url,
        formType: data.formType,
        fieldCount: data.fieldCount
      })
    })
  })

  describe('Error Handling', () => {
    it('should handle API errors gracefully', async () => {
      const message = {
        type: 'REQUEST_AUTOFILL',
        data: { formFields: [] }
      }
      
      backgroundService.state.isAuthenticated = true
      backgroundService.apiClient.getAutofillData.mockRejectedValue(new Error('API Error'))
      
      const response = await backgroundService.handleMessage(message, {}, vi.fn())
      
      expect(response.success).toBe(false)
      expect(response.error).toBe('API Error')
      expect(backgroundService.analyticsManager.trackError).toHaveBeenCalled()
    })

    it('should handle storage errors gracefully', async () => {
      backgroundService.storageManager.set.mockRejectedValue(new Error('Storage Error'))
      
      const message = {
        type: 'UPDATE_SETTINGS',
        data: { autoFillEnabled: false }
      }
      
      // Should not throw error
      await expect(backgroundService.handleMessage(message, {}, vi.fn())).resolves.toBeDefined()
    })
  })

  describe('Periodic Tasks', () => {
    it('should set up periodic profile refresh', () => {
      // Verify that setInterval was called for profile refresh
      expect(setInterval).toHaveBeenCalled()
    })

    it('should set up periodic analytics flush', () => {
      // Verify that setInterval was called for analytics
      expect(setInterval).toHaveBeenCalled()
    })
  })

  describe('Site Support', () => {
    it('should identify supported sites correctly', () => {
      const supportedUrls = [
        'https://www.linkedin.com/jobs/view/123',
        'https://www.indeed.com/viewjob?jk=123',
        'https://www.glassdoor.com/job-listing/123'
      ]
      
      supportedUrls.forEach(url => {
        expect(backgroundService.isSupportedSite(url)).toBe(true)
      })
    })

    it('should reject unsupported sites', () => {
      const unsupportedUrls = [
        'https://www.google.com',
        'https://www.facebook.com',
        'https://www.twitter.com'
      ]
      
      unsupportedUrls.forEach(url => {
        expect(backgroundService.isSupportedSite(url)).toBe(false)
      })
    })
  })

  describe('Analytics Integration', () => {
    it('should track user events correctly', async () => {
      const message = {
        type: 'REQUEST_AUTOFILL',
        data: { formFields: [{ type: 'text' }] }
      }
      
      backgroundService.state.isAuthenticated = true
      backgroundService.apiClient.getAutofillData.mockResolvedValue({})
      
      await backgroundService.handleMessage(message, {}, vi.fn())
      
      expect(backgroundService.analyticsManager.trackEvent).toHaveBeenCalledWith('autofill_requested', {
        fieldCount: 1,
        customization: true
      })
    })

    it('should track errors appropriately', async () => {
      const error = new Error('Test error')
      
      backgroundService.analyticsManager.trackError('test_error', error)
      
      expect(backgroundService.analyticsManager.trackError).toHaveBeenCalledWith('test_error', error)
    })
  })
})
