'use client';

import { forwardRef } from 'react';
import { Resume } from '@careercraft/shared/types/resume';
import { Template, TemplateCustomization } from '@careercraft/shared/types/template';
import { ModernTemplate } from './templates/modern-template';
import { ClassicTemplate } from './templates/classic-template';
import { MinimalTemplate } from './templates/minimal-template';
import { CreativeTemplate } from './templates/creative-template';
import { ProfessionalTemplate } from './templates/professional-template';
import { cn } from '@/lib/utils';

interface TemplateRendererProps {
  resume: Resume;
  template: Template;
  customization?: TemplateCustomization;
  className?: string;
  scale?: number;
  interactive?: boolean;
  preview?: boolean;
}

export const TemplateRenderer = forwardRef<HTMLDivElement, TemplateRendererProps>(
  ({ resume, template, customization, className, scale = 1, interactive = false, preview = false }, ref) => {
    // Apply customizations to template style
    const appliedStyle = {
      ...template.style,
      ...customization?.customizations?.style,
    };

    // Apply customizations to template layout
    const appliedLayout = {
      ...template.layout,
      ...customization?.customizations?.layout,
    };

    // Create render context
    const renderContext = {
      template: {
        ...template,
        style: appliedStyle,
        layout: appliedLayout,
      },
      customization,
      resume,
      options: {
        preview,
        export: !preview && !interactive,
        interactive,
      },
    };

    // Select template component based on category
    const getTemplateComponent = () => {
      switch (template.category) {
        case 'modern':
          return ModernTemplate;
        case 'classic':
          return ClassicTemplate;
        case 'minimal':
          return MinimalTemplate;
        case 'creative':
          return CreativeTemplate;
        case 'professional':
          return ProfessionalTemplate;
        default:
          return ModernTemplate; // Default fallback
      }
    };

    const TemplateComponent = getTemplateComponent();

    return (
      <div
        ref={ref}
        className={cn(
          'template-renderer bg-white text-black',
          preview && 'shadow-lg rounded-lg overflow-hidden',
          className
        )}
        style={{
          transform: scale !== 1 ? `scale(${scale})` : undefined,
          transformOrigin: 'top left',
          width: scale !== 1 ? `${100 / scale}%` : undefined,
        }}
      >
        <TemplateComponent
          context={renderContext}
          interactive={interactive}
          preview={preview}
        />
      </div>
    );
  }
);

TemplateRenderer.displayName = 'TemplateRenderer';
