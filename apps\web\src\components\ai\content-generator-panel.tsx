'use client';

import { useState } from 'react';
import {
  ContentType,
  ContentContext,
  GenerationOptions,
  AIContentResponse,
  ContentLength,
  ContentStyle,
  ContentTone,
  ExperienceLevel,
} from '@careercraft/shared/types/ai';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Slider } from '@/components/ui/slider';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Icons } from '@/components/ui/icons';
import { LoadingSpinner } from '@/components/ui/loading';
import { ContentSuggestions } from './content-suggestions';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';

interface ContentGeneratorPanelProps {
  onContentGenerated: (content: string) => void;
  initialContext?: Partial<ContentContext>;
  contentType: ContentType;
  className?: string;
}

export function ContentGeneratorPanel({
  onContentGenerated,
  initialContext = {},
  contentType,
  className,
}: ContentGeneratorPanelProps) {
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedContent, setGeneratedContent] = useState<AIContentResponse | null>(null);
  const [context, setContext] = useState<ContentContext>({
    experienceLevel: ExperienceLevel.MID_LEVEL,
    tone: ContentTone.PROFESSIONAL,
    ...initialContext,
  });
  const [options, setOptions] = useState<GenerationOptions>({
    length: ContentLength.MEDIUM,
    style: ContentStyle.PARAGRAPH,
    includeKeywords: true,
    atsOptimized: true,
    industrySpecific: true,
    creativityLevel: 0.7,
    maxSuggestions: 3,
    language: 'en',
  });

  const handleGenerate = async () => {
    try {
      setIsGenerating(true);
      
      const response = await fetch('/api/ai/generate-content', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type: contentType,
          context,
          options,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to generate content');
      }

      const result = await response.json();
      setGeneratedContent(result);
      toast.success('Content generated successfully!');
    } catch (error) {
      console.error('Content generation error:', error);
      toast.error('Failed to generate content. Please try again.');
    } finally {
      setIsGenerating(false);
    }
  };

  const handleUseContent = (content: string) => {
    onContentGenerated(content);
    toast.success('Content applied successfully!');
  };

  const updateContext = (key: keyof ContentContext, value: any) => {
    setContext(prev => ({ ...prev, [key]: value }));
  };

  const updateOptions = (key: keyof GenerationOptions, value: any) => {
    setOptions(prev => ({ ...prev, [key]: value }));
  };

  const getContentTypeLabel = (type: ContentType): string => {
    const labels: Record<ContentType, string> = {
      [ContentType.PROFESSIONAL_SUMMARY]: 'Professional Summary',
      [ContentType.WORK_EXPERIENCE_DESCRIPTION]: 'Work Experience Description',
      [ContentType.ACHIEVEMENT_BULLET]: 'Achievement Bullet Point',
      [ContentType.SKILLS_SUGGESTION]: 'Skills Suggestion',
      [ContentType.COVER_LETTER]: 'Cover Letter',
      [ContentType.LINKEDIN_HEADLINE]: 'LinkedIn Headline',
      [ContentType.LINKEDIN_SUMMARY]: 'LinkedIn Summary',
      [ContentType.JOB_DESCRIPTION_OPTIMIZATION]: 'Job Description Optimization',
    };
    return labels[type] || 'Content';
  };

  return (
    <div className={cn('space-y-6', className)}>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Icons.sparkles className="h-5 w-5 text-primary" />
            AI Content Generator
          </CardTitle>
          <CardDescription>
            Generate {getContentTypeLabel(contentType).toLowerCase()} using AI
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="context" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="context">Context</TabsTrigger>
              <TabsTrigger value="options">Options</TabsTrigger>
              <TabsTrigger value="generate">Generate</TabsTrigger>
            </TabsList>

            <TabsContent value="context" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>Experience Level</Label>
                  <Select
                    value={context.experienceLevel}
                    onValueChange={(value) => updateContext('experienceLevel', value as ExperienceLevel)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value={ExperienceLevel.ENTRY_LEVEL}>Entry Level</SelectItem>
                      <SelectItem value={ExperienceLevel.MID_LEVEL}>Mid Level</SelectItem>
                      <SelectItem value={ExperienceLevel.SENIOR_LEVEL}>Senior Level</SelectItem>
                      <SelectItem value={ExperienceLevel.EXECUTIVE}>Executive</SelectItem>
                      <SelectItem value={ExperienceLevel.CAREER_CHANGE}>Career Change</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Tone</Label>
                  <Select
                    value={context.tone}
                    onValueChange={(value) => updateContext('tone', value as ContentTone)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value={ContentTone.PROFESSIONAL}>Professional</SelectItem>
                      <SelectItem value={ContentTone.CONFIDENT}>Confident</SelectItem>
                      <SelectItem value={ContentTone.APPROACHABLE}>Approachable</SelectItem>
                      <SelectItem value={ContentTone.DYNAMIC}>Dynamic</SelectItem>
                      <SelectItem value={ContentTone.TECHNICAL}>Technical</SelectItem>
                      <SelectItem value={ContentTone.CREATIVE}>Creative</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Current Role</Label>
                  <Input
                    placeholder="Software Engineer"
                    value={context.currentRole || ''}
                    onChange={(e) => updateContext('currentRole', e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label>Industry</Label>
                  <Input
                    placeholder="Technology"
                    value={context.industry || ''}
                    onChange={(e) => updateContext('industry', e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label>Target Job Title</Label>
                  <Input
                    placeholder="Senior Software Engineer"
                    value={context.targetJobTitle || ''}
                    onChange={(e) => updateContext('targetJobTitle', e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label>Target Company</Label>
                  <Input
                    placeholder="Google"
                    value={context.targetCompany || ''}
                    onChange={(e) => updateContext('targetCompany', e.target.value)}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label>Key Skills (comma-separated)</Label>
                <Input
                  placeholder="React, Node.js, TypeScript, AWS"
                  value={context.skills?.join(', ') || ''}
                  onChange={(e) => updateContext('skills', e.target.value.split(',').map(s => s.trim()).filter(Boolean))}
                />
              </div>

              <div className="space-y-2">
                <Label>Job Description (optional)</Label>
                <Textarea
                  placeholder="Paste the target job description here for better optimization..."
                  value={context.jobDescription || ''}
                  onChange={(e) => updateContext('jobDescription', e.target.value)}
                  rows={4}
                />
              </div>

              {contentType === ContentType.WORK_EXPERIENCE_DESCRIPTION && (
                <>
                  <div className="space-y-2">
                    <Label>Company</Label>
                    <Input
                      placeholder="Company name"
                      value={context.company || ''}
                      onChange={(e) => updateContext('company', e.target.value)}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label>Position</Label>
                    <Input
                      placeholder="Job title"
                      value={context.position || ''}
                      onChange={(e) => updateContext('position', e.target.value)}
                    />
                  </div>
                </>
              )}

              <div className="space-y-2">
                <Label>Custom Instructions (optional)</Label>
                <Textarea
                  placeholder="Any specific requirements or preferences..."
                  value={context.customInstructions || ''}
                  onChange={(e) => updateContext('customInstructions', e.target.value)}
                  rows={2}
                />
              </div>
            </TabsContent>

            <TabsContent value="options" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>Content Length</Label>
                  <Select
                    value={options.length}
                    onValueChange={(value) => updateOptions('length', value as ContentLength)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value={ContentLength.SHORT}>Short (1-2 sentences)</SelectItem>
                      <SelectItem value={ContentLength.MEDIUM}>Medium (3-4 sentences)</SelectItem>
                      <SelectItem value={ContentLength.LONG}>Long (5+ sentences)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Content Style</Label>
                  <Select
                    value={options.style}
                    onValueChange={(value) => updateOptions('style', value as ContentStyle)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value={ContentStyle.PARAGRAPH}>Paragraph</SelectItem>
                      <SelectItem value={ContentStyle.BULLET_POINTS}>Bullet Points</SelectItem>
                      <SelectItem value={ContentStyle.STRUCTURED}>Structured</SelectItem>
                      <SelectItem value={ContentStyle.CONVERSATIONAL}>Conversational</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Number of Suggestions</Label>
                  <Select
                    value={options.maxSuggestions.toString()}
                    onValueChange={(value) => updateOptions('maxSuggestions', parseInt(value))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="1">1 suggestion</SelectItem>
                      <SelectItem value="2">2 suggestions</SelectItem>
                      <SelectItem value="3">3 suggestions</SelectItem>
                      <SelectItem value="5">5 suggestions</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-4">
                <div className="space-y-2">
                  <Label>Creativity Level: {Math.round(options.creativityLevel * 100)}%</Label>
                  <Slider
                    value={[options.creativityLevel]}
                    onValueChange={([value]) => updateOptions('creativityLevel', value)}
                    max={1}
                    min={0}
                    step={0.1}
                    className="w-full"
                  />
                  <div className="flex justify-between text-xs text-muted-foreground">
                    <span>Conservative</span>
                    <span>Creative</span>
                  </div>
                </div>

                <div className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="atsOptimized"
                      checked={options.atsOptimized}
                      onCheckedChange={(checked) => updateOptions('atsOptimized', checked)}
                    />
                    <Label htmlFor="atsOptimized" className="text-sm">
                      ATS Optimized (recommended)
                    </Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="includeKeywords"
                      checked={options.includeKeywords}
                      onCheckedChange={(checked) => updateOptions('includeKeywords', checked)}
                    />
                    <Label htmlFor="includeKeywords" className="text-sm">
                      Include relevant keywords
                    </Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="industrySpecific"
                      checked={options.industrySpecific}
                      onCheckedChange={(checked) => updateOptions('industrySpecific', checked)}
                    />
                    <Label htmlFor="industrySpecific" className="text-sm">
                      Industry-specific content
                    </Label>
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="generate" className="space-y-4">
              <div className="text-center">
                <Button
                  onClick={handleGenerate}
                  disabled={isGenerating}
                  size="lg"
                  className="w-full sm:w-auto"
                >
                  {isGenerating && <LoadingSpinner size="sm" className="mr-2" />}
                  {isGenerating ? 'Generating...' : `Generate ${getContentTypeLabel(contentType)}`}
                </Button>
              </div>

              {generatedContent && (
                <ContentSuggestions
                  response={generatedContent}
                  onUseContent={handleUseContent}
                />
              )}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}
