"""
Market Data Service Main Application
Implements FR-5.2: Job Market Data Ingestion Service

Main orchestrator for job scraping, processing, and storage
"""

import asyncio
import logging
import os
import sys
from datetime import datetime, timedelta
from typing import List, Dict, Any
import schedule
import time
from dataclasses import asdict

# Add src to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__)))

from scrapers.linkedin_scraper import LinkedInScraper
from scrapers.indeed_scraper import IndeedScraper
from scrapers.company_scraper import CompanyScraper
from processors.job_processor import JobProcessor
from storage.database_manager import DatabaseManager
from utils.config import Config
from utils.logger import setup_logger

class MarketDataService:
    """Main service for job market data collection and processing"""
    
    def __init__(self):
        self.config = Config()
        self.logger = setup_logger('market_data_service')
        
        # Initialize components
        self.scrapers = {
            'linkedin': LinkedInScraper(),
            'indeed': IndeedScraper(),
            'company': CompanyScraper()
        }
        
        self.processor = JobProcessor()
        self.db_manager = DatabaseManager()
        
        # Default search parameters
        self.search_terms = [
            'Software Engineer',
            'Data Scientist',
            'Product Manager',
            'UX Designer',
            'DevOps Engineer',
            'Frontend Developer',
            'Backend Developer',
            'Full Stack Developer',
            'Machine Learning Engineer',
            'Technical Program Manager'
        ]
        
        self.locations = [
            'San Francisco, CA',
            'New York, NY',
            'Seattle, WA',
            'Austin, TX',
            'Boston, MA',
            'Los Angeles, CA',
            'Chicago, IL',
            'Denver, CO',
            'Remote',
            'United States'
        ]

    async def run_full_scraping_cycle(self) -> Dict[str, Any]:
        """Run complete job scraping cycle for all sources"""
        self.logger.info("Starting full job scraping cycle")
        
        start_time = datetime.now()
        results = {
            'start_time': start_time,
            'scrapers': {},
            'total_jobs': 0,
            'processed_jobs': 0,
            'stored_jobs': 0,
            'errors': []
        }
        
        try:
            # Run scrapers in parallel
            scraping_tasks = []
            for source_name, scraper in self.scrapers.items():
                if self.config.is_scraper_enabled(source_name):
                    task = self._run_scraper(source_name, scraper)
                    scraping_tasks.append(task)
            
            # Wait for all scrapers to complete
            scraper_results = await asyncio.gather(*scraping_tasks, return_exceptions=True)
            
            # Process results
            all_jobs = []
            for i, result in enumerate(scraper_results):
                source_name = list(self.scrapers.keys())[i]
                
                if isinstance(result, Exception):
                    self.logger.error(f"Scraper {source_name} failed: {result}")
                    results['errors'].append(f"{source_name}: {str(result)}")
                    results['scrapers'][source_name] = {'status': 'failed', 'jobs': 0}
                else:
                    jobs, scraper_stats = result
                    all_jobs.extend(jobs)
                    results['scrapers'][source_name] = scraper_stats
                    self.logger.info(f"Scraper {source_name} completed: {len(jobs)} jobs")
            
            results['total_jobs'] = len(all_jobs)
            
            # Process and store jobs
            if all_jobs:
                processed_jobs = await self.processor.process_job_batch(all_jobs)
                results['processed_jobs'] = len(processed_jobs)
                
                stored_count = await self.db_manager.store_jobs(processed_jobs)
                results['stored_jobs'] = stored_count
                
                self.logger.info(f"Stored {stored_count} jobs in database")
            
            # Update statistics
            await self.db_manager.update_scraping_statistics(results)
            
        except Exception as e:
            self.logger.error(f"Full scraping cycle failed: {e}")
            results['errors'].append(f"System error: {str(e)}")
        
        finally:
            results['end_time'] = datetime.now()
            results['duration'] = (results['end_time'] - start_time).total_seconds()
            
            self.logger.info(f"Scraping cycle completed in {results['duration']:.2f} seconds")
            self.logger.info(f"Total: {results['total_jobs']} scraped, {results['stored_jobs']} stored")
        
        return results

    async def _run_scraper(self, source_name: str, scraper) -> tuple:
        """Run individual scraper with error handling"""
        try:
            self.logger.info(f"Starting {source_name} scraper")
            
            jobs = await scraper.scrape_jobs(
                search_terms=self.search_terms,
                locations=self.locations,
                max_pages=self.config.get_max_pages(source_name)
            )
            
            stats = {
                'status': 'success',
                'jobs': len(jobs),
                'search_terms': len(self.search_terms),
                'locations': len(self.locations),
                'timestamp': datetime.now().isoformat()
            }
            
            return jobs, stats
            
        except Exception as e:
            self.logger.error(f"Scraper {source_name} error: {e}")
            raise

    async def run_incremental_update(self) -> Dict[str, Any]:
        """Run incremental update for recent job postings"""
        self.logger.info("Starting incremental job update")
        
        # Get jobs posted in last 24 hours
        cutoff_date = datetime.now() - timedelta(hours=24)
        
        # Run with limited scope
        limited_search_terms = self.search_terms[:5]  # Top 5 roles
        limited_locations = self.locations[:5]  # Top 5 locations
        
        results = {
            'type': 'incremental',
            'cutoff_date': cutoff_date,
            'search_terms': limited_search_terms,
            'locations': limited_locations
        }
        
        # Run scrapers with limited scope
        all_jobs = []
        for source_name, scraper in self.scrapers.items():
            if self.config.is_scraper_enabled(source_name):
                try:
                    jobs = await scraper.scrape_jobs(
                        search_terms=limited_search_terms,
                        locations=limited_locations,
                        max_pages=2  # Limited pages for incremental
                    )
                    
                    # Filter for recent jobs only
                    recent_jobs = [
                        job for job in jobs 
                        if job.posted_date and job.posted_date >= cutoff_date
                    ]
                    
                    all_jobs.extend(recent_jobs)
                    self.logger.info(f"{source_name}: {len(recent_jobs)} recent jobs")
                    
                except Exception as e:
                    self.logger.error(f"Incremental scraping failed for {source_name}: {e}")
        
        # Process and store
        if all_jobs:
            processed_jobs = await self.processor.process_job_batch(all_jobs)
            stored_count = await self.db_manager.store_jobs(processed_jobs)
            
            results.update({
                'total_jobs': len(all_jobs),
                'stored_jobs': stored_count
            })
        
        return results

    async def cleanup_old_jobs(self) -> int:
        """Remove expired and old job postings"""
        self.logger.info("Starting job cleanup")
        
        # Remove jobs older than 30 days
        cutoff_date = datetime.now() - timedelta(days=30)
        
        deleted_count = await self.db_manager.cleanup_old_jobs(cutoff_date)
        self.logger.info(f"Cleaned up {deleted_count} old job postings")
        
        return deleted_count

    def schedule_jobs(self):
        """Schedule periodic job scraping"""
        self.logger.info("Setting up job scheduling")
        
        # Full scraping cycle - daily at 2 AM
        schedule.every().day.at("02:00").do(
            lambda: asyncio.run(self.run_full_scraping_cycle())
        )
        
        # Incremental updates - every 4 hours
        schedule.every(4).hours.do(
            lambda: asyncio.run(self.run_incremental_update())
        )
        
        # Cleanup - weekly on Sunday at 3 AM
        schedule.every().sunday.at("03:00").do(
            lambda: asyncio.run(self.cleanup_old_jobs())
        )
        
        self.logger.info("Job scheduling configured")

    async def run_scheduler(self):
        """Run the job scheduler"""
        self.logger.info("Starting job scheduler")
        
        while True:
            try:
                schedule.run_pending()
                await asyncio.sleep(60)  # Check every minute
            except KeyboardInterrupt:
                self.logger.info("Scheduler stopped by user")
                break
            except Exception as e:
                self.logger.error(f"Scheduler error: {e}")
                await asyncio.sleep(300)  # Wait 5 minutes on error

    async def run_manual_scraping(self, sources: List[str] = None) -> Dict[str, Any]:
        """Run manual scraping for specific sources"""
        if sources is None:
            sources = list(self.scrapers.keys())
        
        self.logger.info(f"Running manual scraping for: {sources}")
        
        results = {'manual_run': True, 'sources': sources}
        
        # Run only specified scrapers
        all_jobs = []
        for source_name in sources:
            if source_name in self.scrapers:
                scraper = self.scrapers[source_name]
                try:
                    jobs = await scraper.scrape_jobs(
                        search_terms=self.search_terms[:3],  # Limited for manual run
                        locations=self.locations[:3],
                        max_pages=2
                    )
                    all_jobs.extend(jobs)
                    self.logger.info(f"Manual {source_name}: {len(jobs)} jobs")
                except Exception as e:
                    self.logger.error(f"Manual scraping failed for {source_name}: {e}")
        
        # Process and store
        if all_jobs:
            processed_jobs = await self.processor.process_job_batch(all_jobs)
            stored_count = await self.db_manager.store_jobs(processed_jobs)
            
            results.update({
                'total_jobs': len(all_jobs),
                'stored_jobs': stored_count
            })
        
        return results

async def main():
    """Main entry point"""
    service = MarketDataService()
    
    # Parse command line arguments
    import argparse
    parser = argparse.ArgumentParser(description='Market Data Service')
    parser.add_argument('--mode', choices=['full', 'incremental', 'manual', 'schedule'], 
                       default='schedule', help='Run mode')
    parser.add_argument('--sources', nargs='+', help='Sources for manual mode')
    
    args = parser.parse_args()
    
    try:
        if args.mode == 'full':
            results = await service.run_full_scraping_cycle()
            print(f"Full scraping completed: {results}")
            
        elif args.mode == 'incremental':
            results = await service.run_incremental_update()
            print(f"Incremental update completed: {results}")
            
        elif args.mode == 'manual':
            results = await service.run_manual_scraping(args.sources)
            print(f"Manual scraping completed: {results}")
            
        elif args.mode == 'schedule':
            service.schedule_jobs()
            await service.run_scheduler()
            
    except KeyboardInterrupt:
        print("Service stopped by user")
    except Exception as e:
        print(f"Service error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
