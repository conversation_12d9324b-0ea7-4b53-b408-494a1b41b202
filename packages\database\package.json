{"name": "@careercraft/database", "version": "2.0.0", "private": true, "main": "./src/index.ts", "types": "./src/index.ts", "scripts": {"db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:seed": "tsx ../../scripts/seed-sample-data.ts", "db:reset": "prisma migrate reset --force && npm run db:seed", "build": "tsc", "dev": "tsc --watch", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "type-check": "tsc --noEmit", "clean": "rm -rf dist .turbo node_modules/.cache coverage"}, "dependencies": {"@prisma/client": "^5.6.0"}, "devDependencies": {"@jest/globals": "^29.7.0", "@types/jest": "^29.5.8", "@types/node": "^20.10.0", "dotenv": "^16.3.1", "jest": "^29.7.0", "prisma": "^5.6.0", "ts-jest": "^29.1.1", "tsx": "^4.6.0", "typescript": "^5.3.0"}}