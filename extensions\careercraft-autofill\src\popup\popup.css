/**
 * Popup Styles
 * 
 * Tailwind CSS styles for the popup interface
 */

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom component styles */
.popup-container {
  @apply w-80 bg-white shadow-lg;
}

.popup-header {
  @apply bg-gradient-to-r from-blue-600 to-purple-600 text-white p-4;
}

.popup-content {
  @apply p-4;
}

.status-card {
  @apply bg-gray-50 border border-gray-200 rounded-lg p-3 mb-4;
}

.status-success {
  @apply bg-green-50 border-green-200;
}

.status-warning {
  @apply bg-yellow-50 border-yellow-200;
}

.status-error {
  @apply bg-red-50 border-red-200;
}

.action-button {
  @apply w-full py-3 px-4 rounded-lg font-medium transition-all duration-200 flex items-center justify-center space-x-2;
}

.action-button-primary {
  @apply bg-blue-600 text-white hover:bg-blue-700 hover:shadow-md transform hover:-translate-y-0.5;
}

.action-button-secondary {
  @apply bg-gray-100 text-gray-700 border border-gray-300 hover:bg-gray-200 hover:shadow-md transform hover:-translate-y-0.5;
}

.action-button:disabled {
  @apply opacity-50 cursor-not-allowed transform-none hover:shadow-none;
}

.stats-grid {
  @apply grid grid-cols-2 gap-3;
}

.stat-item {
  @apply bg-gray-50 p-2 rounded text-center;
}

.stat-value {
  @apply text-lg font-semibold text-gray-800;
}

.stat-label {
  @apply text-xs text-gray-600;
}

.form-detected-badge {
  @apply inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800;
}

.confidence-bar {
  @apply w-full bg-gray-200 rounded-full h-2;
}

.confidence-fill {
  @apply h-2 rounded-full transition-all duration-300;
}

.confidence-high {
  @apply bg-green-500;
}

.confidence-medium {
  @apply bg-yellow-500;
}

.confidence-low {
  @apply bg-red-500;
}

.loading-spinner {
  @apply animate-spin rounded-full h-5 w-5 border-b-2 border-white;
}

.notification {
  @apply fixed top-4 right-4 max-w-sm bg-white border border-gray-200 rounded-lg shadow-lg p-4 z-50;
}

.notification-success {
  @apply border-green-200 bg-green-50;
}

.notification-error {
  @apply border-red-200 bg-red-50;
}

.notification-warning {
  @apply border-yellow-200 bg-yellow-50;
}

.notification-info {
  @apply border-blue-200 bg-blue-50;
}

/* Animation classes */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

.fade-out {
  animation: fadeOut 0.3s ease-in-out;
}

.slide-up {
  animation: slideUp 0.3s ease-out;
}

.slide-down {
  animation: slideDown 0.3s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes fadeOut {
  from { opacity: 1; }
  to { opacity: 0; }
}

@keyframes slideUp {
  from { transform: translateY(10px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes slideDown {
  from { transform: translateY(-10px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

/* Responsive adjustments */
@media (max-width: 320px) {
  .popup-container {
    @apply w-full;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .popup-container {
    @apply bg-gray-900 text-white;
  }
  
  .status-card {
    @apply bg-gray-800 border-gray-700;
  }
  
  .action-button-secondary {
    @apply bg-gray-800 text-gray-200 border-gray-600 hover:bg-gray-700;
  }
  
  .stat-item {
    @apply bg-gray-800;
  }
  
  .stat-value {
    @apply text-gray-200;
  }
  
  .stat-label {
    @apply text-gray-400;
  }
}
