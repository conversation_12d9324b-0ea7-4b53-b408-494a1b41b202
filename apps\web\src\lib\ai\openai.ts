import OpenAI from 'openai'

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
})

export interface AIGenerationOptions {
  temperature?: number
  maxTokens?: number
  model?: string
}

export interface ResumeOptimizationRequest {
  content: string
  jobDescription?: string
  targetRole?: string
  industry?: string
  experienceLevel?: 'entry' | 'mid' | 'senior' | 'executive'
}

export interface ContentGenerationRequest {
  type: 'summary' | 'experience' | 'skills' | 'achievement' | 'cover_letter'
  context: {
    role?: string
    company?: string
    industry?: string
    experience?: string
    skills?: string[]
    achievements?: string[]
  }
  tone?: 'professional' | 'creative' | 'technical' | 'executive'
}

export interface ATSOptimizationRequest {
  resumeContent: string
  jobDescription: string
  targetKeywords?: string[]
}

export interface ATSOptimizationResult {
  score: number
  missingKeywords: string[]
  suggestions: string[]
  optimizedContent?: string
  keywordDensity: { [keyword: string]: number }
}

export class AIService {
  private static instance: AIService
  private readonly defaultModel = 'gpt-4-turbo-preview'
  private readonly defaultTemperature = 0.7
  private readonly defaultMaxTokens = 1000

  static getInstance(): AIService {
    if (!AIService.instance) {
      AIService.instance = new AIService()
    }
    return AIService.instance
  }

  /**
   * Generate optimized resume content based on job description
   */
  async optimizeResumeContent(
    request: ResumeOptimizationRequest,
    options: AIGenerationOptions = {}
  ): Promise<string> {
    const prompt = this.buildOptimizationPrompt(request)
    
    try {
      const response = await openai.chat.completions.create({
        model: options.model || this.defaultModel,
        messages: [
          {
            role: 'system',
            content: 'You are an expert resume writer and career coach with 15+ years of experience helping professionals optimize their resumes for ATS systems and hiring managers.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: options.temperature || this.defaultTemperature,
        max_tokens: options.maxTokens || this.defaultMaxTokens,
      })

      return response.choices[0]?.message?.content || ''
    } catch (error) {
      console.error('Error optimizing resume content:', error)
      throw new Error('Failed to optimize resume content')
    }
  }

  /**
   * Generate specific content sections for resume
   */
  async generateContent(
    request: ContentGenerationRequest,
    options: AIGenerationOptions = {}
  ): Promise<string> {
    const prompt = this.buildContentGenerationPrompt(request)
    
    try {
      const response = await openai.chat.completions.create({
        model: options.model || this.defaultModel,
        messages: [
          {
            role: 'system',
            content: this.getSystemPromptForContentType(request.type)
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: options.temperature || this.defaultTemperature,
        max_tokens: options.maxTokens || this.defaultMaxTokens,
      })

      return response.choices[0]?.message?.content || ''
    } catch (error) {
      console.error('Error generating content:', error)
      throw new Error('Failed to generate content')
    }
  }

  /**
   * Analyze resume for ATS optimization
   */
  async analyzeATSCompatibility(
    request: ATSOptimizationRequest,
    options: AIGenerationOptions = {}
  ): Promise<ATSOptimizationResult> {
    const prompt = this.buildATSAnalysisPrompt(request)
    
    try {
      const response = await openai.chat.completions.create({
        model: options.model || this.defaultModel,
        messages: [
          {
            role: 'system',
            content: 'You are an ATS (Applicant Tracking System) expert who analyzes resumes for keyword optimization and ATS compatibility. Provide detailed analysis in JSON format.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.3, // Lower temperature for more consistent analysis
        max_tokens: options.maxTokens || 1500,
      })

      const content = response.choices[0]?.message?.content || '{}'
      return JSON.parse(content) as ATSOptimizationResult
    } catch (error) {
      console.error('Error analyzing ATS compatibility:', error)
      throw new Error('Failed to analyze ATS compatibility')
    }
  }

  /**
   * Generate keyword suggestions based on job description
   */
  async generateKeywordSuggestions(
    jobDescription: string,
    currentResume: string,
    options: AIGenerationOptions = {}
  ): Promise<string[]> {
    const prompt = `
    Analyze this job description and current resume to suggest relevant keywords that should be included:

    Job Description:
    ${jobDescription}

    Current Resume:
    ${currentResume}

    Please provide a list of 10-15 important keywords and phrases that are missing from the resume but are relevant to the job. Focus on:
    - Technical skills and tools
    - Industry-specific terminology
    - Soft skills mentioned in the job description
    - Certifications or qualifications
    - Action verbs and achievement-oriented language

    Return only a JSON array of keywords, no additional text.
    `

    try {
      const response = await openai.chat.completions.create({
        model: options.model || this.defaultModel,
        messages: [
          {
            role: 'system',
            content: 'You are a keyword optimization expert. Return only valid JSON arrays.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.3,
        max_tokens: 500,
      })

      const content = response.choices[0]?.message?.content || '[]'
      return JSON.parse(content) as string[]
    } catch (error) {
      console.error('Error generating keyword suggestions:', error)
      return []
    }
  }

  private buildOptimizationPrompt(request: ResumeOptimizationRequest): string {
    return `
    Please optimize this resume content for better ATS compatibility and impact:

    Current Content:
    ${request.content}

    ${request.jobDescription ? `Target Job Description:\n${request.jobDescription}\n` : ''}
    ${request.targetRole ? `Target Role: ${request.targetRole}\n` : ''}
    ${request.industry ? `Industry: ${request.industry}\n` : ''}
    ${request.experienceLevel ? `Experience Level: ${request.experienceLevel}\n` : ''}

    Please provide an optimized version that:
    1. Incorporates relevant keywords from the job description
    2. Uses strong action verbs and quantifiable achievements
    3. Maintains ATS-friendly formatting
    4. Enhances readability and impact
    5. Keeps the same general structure and length

    Return only the optimized content, no additional commentary.
    `
  }

  private buildContentGenerationPrompt(request: ContentGenerationRequest): string {
    const { type, context, tone = 'professional' } = request

    let prompt = `Generate ${type} content with a ${tone} tone using this context:\n`
    
    if (context.role) prompt += `Role: ${context.role}\n`
    if (context.company) prompt += `Company: ${context.company}\n`
    if (context.industry) prompt += `Industry: ${context.industry}\n`
    if (context.experience) prompt += `Experience: ${context.experience}\n`
    if (context.skills?.length) prompt += `Skills: ${context.skills.join(', ')}\n`
    if (context.achievements?.length) prompt += `Achievements: ${context.achievements.join(', ')}\n`

    return prompt
  }

  private buildATSAnalysisPrompt(request: ATSOptimizationRequest): string {
    return `
    Analyze this resume for ATS compatibility against the job description:

    Resume Content:
    ${request.resumeContent}

    Job Description:
    ${request.jobDescription}

    ${request.targetKeywords?.length ? `Target Keywords: ${request.targetKeywords.join(', ')}\n` : ''}

    Please provide analysis in this exact JSON format:
    {
      "score": <number 0-100>,
      "missingKeywords": ["keyword1", "keyword2"],
      "suggestions": ["suggestion1", "suggestion2"],
      "keywordDensity": {"keyword": <density_percentage>}
    }

    Focus on:
    - Keyword matching and density
    - ATS-friendly formatting
    - Missing critical terms
    - Optimization opportunities
    `
  }

  private getSystemPromptForContentType(type: ContentGenerationRequest['type']): string {
    const prompts = {
      summary: 'You are an expert at writing compelling professional summaries that highlight key strengths and career achievements in 2-3 sentences.',
      experience: 'You are an expert at writing impactful work experience descriptions using strong action verbs and quantifiable achievements.',
      skills: 'You are an expert at organizing and presenting technical and soft skills in a way that matches job requirements.',
      achievement: 'You are an expert at transforming work accomplishments into powerful, quantifiable achievement statements.',
      cover_letter: 'You are an expert at writing personalized, compelling cover letters that connect candidate experience to job requirements.'
    }

    return prompts[type] || 'You are a professional resume writing expert.'
  }
}

export const aiService = AIService.getInstance()
