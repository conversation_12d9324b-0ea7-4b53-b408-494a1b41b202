import { z } from 'zod';

// API Response Types
export interface ApiResponse<T = unknown> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// User Types
export const UserProfileSchema = z.object({
  firstName: z.string().min(1, 'First name is required').max(50),
  lastName: z.string().min(1, 'Last name is required').max(50),
  phone: z.string().optional(),
  location: z.string().optional(),
  website: z.string().url().optional().or(z.literal('')),
  linkedinUrl: z.string().url().optional().or(z.literal('')),
  githubUrl: z.string().url().optional().or(z.literal('')),
  bio: z.string().max(500).optional(),
});

export type UserProfile = z.infer<typeof UserProfileSchema>;

// Resume Types
export const ExperienceSchema = z.object({
  id: z.string().optional(),
  company: z.string().min(1, 'Company is required'),
  position: z.string().min(1, 'Position is required'),
  location: z.string().optional(),
  startDate: z.date(),
  endDate: z.date().optional(),
  isCurrent: z.boolean().default(false),
  description: z.string().optional(),
  achievements: z.array(z.string()).default([]),
  displayOrder: z.number().default(0),
});

export type Experience = z.infer<typeof ExperienceSchema>;

export const EducationSchema = z.object({
  id: z.string().optional(),
  institution: z.string().min(1, 'Institution is required'),
  degree: z.string().min(1, 'Degree is required'),
  field: z.string().optional(),
  location: z.string().optional(),
  startDate: z.date(),
  endDate: z.date().optional(),
  gpa: z.string().optional(),
  description: z.string().optional(),
  displayOrder: z.number().default(0),
});

export type Education = z.infer<typeof EducationSchema>;

export const SkillSchema = z.object({
  id: z.string().optional(),
  name: z.string().min(1, 'Skill name is required'),
  category: z.string().optional(),
  level: z.enum(['BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT']).optional(),
  displayOrder: z.number().default(0),
});

export type Skill = z.infer<typeof SkillSchema>;

export const ProjectSchema = z.object({
  id: z.string().optional(),
  name: z.string().min(1, 'Project name is required'),
  description: z.string().optional(),
  url: z.string().url().optional().or(z.literal('')),
  githubUrl: z.string().url().optional().or(z.literal('')),
  technologies: z.array(z.string()).default([]),
  startDate: z.date().optional(),
  endDate: z.date().optional(),
  displayOrder: z.number().default(0),
});

export type Project = z.infer<typeof ProjectSchema>;

export const CertificationSchema = z.object({
  id: z.string().optional(),
  name: z.string().min(1, 'Certification name is required'),
  issuer: z.string().min(1, 'Issuer is required'),
  issueDate: z.date(),
  expiryDate: z.date().optional(),
  credentialId: z.string().optional(),
  url: z.string().url().optional().or(z.literal('')),
  displayOrder: z.number().default(0),
});

export type Certification = z.infer<typeof CertificationSchema>;

export const LanguageSchema = z.object({
  id: z.string().optional(),
  name: z.string().min(1, 'Language name is required'),
  proficiency: z.enum(['BASIC', 'CONVERSATIONAL', 'FLUENT', 'NATIVE']),
  displayOrder: z.number().default(0),
});

export type Language = z.infer<typeof LanguageSchema>;

export const CustomSectionSchema = z.object({
  id: z.string().optional(),
  title: z.string().min(1, 'Section title is required'),
  content: z.any(), // Flexible content structure
  displayOrder: z.number().default(0),
});

export type CustomSection = z.infer<typeof CustomSectionSchema>;

export const ResumeSchema = z.object({
  id: z.string().optional(),
  title: z.string().min(1, 'Resume title is required'),
  description: z.string().optional(),
  templateId: z.string().optional(),
  isPublic: z.boolean().default(false),
  experiences: z.array(ExperienceSchema).default([]),
  educations: z.array(EducationSchema).default([]),
  skills: z.array(SkillSchema).default([]),
  projects: z.array(ProjectSchema).default([]),
  certifications: z.array(CertificationSchema).default([]),
  languages: z.array(LanguageSchema).default([]),
  sections: z.array(CustomSectionSchema).default([]),
});

export type Resume = z.infer<typeof ResumeSchema>;

// Cover Letter Types
export const CoverLetterSchema = z.object({
  id: z.string().optional(),
  title: z.string().min(1, 'Cover letter title is required'),
  content: z.string().min(1, 'Content is required'),
  jobTitle: z.string().optional(),
  company: z.string().optional(),
  jobDescription: z.string().optional(),
});

export type CoverLetter = z.infer<typeof CoverLetterSchema>;

// AI Types
export interface AIGenerationRequest {
  type: 'BULLET_POINT' | 'SUMMARY' | 'COVER_LETTER' | 'REWRITE' | 'KEYWORDS';
  context: Record<string, unknown>;
  prompt?: string;
}

export interface AIGenerationResponse {
  content: string;
  suggestions?: string[];
  metadata?: Record<string, unknown>;
}

// Template Types
export interface Template {
  id: string;
  name: string;
  description?: string;
  category?: string;
  isPremium: boolean;
  isActive: boolean;
  config: TemplateConfig;
  preview?: string;
}

export interface TemplateConfig {
  layout: 'single-column' | 'two-column' | 'sidebar';
  colors: {
    primary: string;
    secondary: string;
    text: string;
    background: string;
  };
  fonts: {
    heading: string;
    body: string;
  };
  spacing: {
    section: number;
    item: number;
  };
  sections: {
    order: string[];
    visibility: Record<string, boolean>;
  };
}

// Analytics Types
export interface ResumeAnalytics {
  views: number;
  downloads: number;
  shares: number;
  lastViewed?: Date;
  topSources: Array<{ source: string; count: number }>;
}

// Error Types
export class AppError extends Error {
  constructor(
    message: string,
    public statusCode: number = 500,
    public code?: string
  ) {
    super(message);
    this.name = 'AppError';
  }
}

export class ValidationError extends AppError {
  constructor(message: string, public field?: string) {
    super(message, 400, 'VALIDATION_ERROR');
    this.name = 'ValidationError';
  }
}

export class NotFoundError extends AppError {
  constructor(resource: string) {
    super(`${resource} not found`, 404, 'NOT_FOUND');
    this.name = 'NotFoundError';
  }
}

export class UnauthorizedError extends AppError {
  constructor(message: string = 'Unauthorized') {
    super(message, 401, 'UNAUTHORIZED');
    this.name = 'UnauthorizedError';
  }
}

export class ForbiddenError extends AppError {
  constructor(message: string = 'Forbidden') {
    super(message, 403, 'FORBIDDEN');
    this.name = 'ForbiddenError';
  }
}
