/**
 * Cloud Storage Service Unit Tests
 * 
 * Tests for cloud storage operations and template synchronization
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { CloudStorageService, LocalStorageProvider, S3StorageProvider } from '@/lib/template-sync/cloud-storage'
import fs from 'fs'
import path from 'path'

// Mock fs module
vi.mock('fs', () => ({
  default: {
    promises: {
      writeFile: vi.fn(),
      readFile: vi.fn(),
      unlink: vi.fn(),
      access: vi.fn(),
      stat: vi.fn(),
      mkdir: vi.fn()
    },
    existsSync: vi.fn(),
    mkdirSync: vi.fn()
  }
}))

// Mock crypto module
vi.mock('crypto', () => ({
  createHash: vi.fn(() => ({
    update: vi.fn().mockReturnThis(),
    digest: vi.fn(() => 'mock-checksum')
  }))
}))

describe('CloudStorageService', () => {
  let service: CloudStorageService

  beforeEach(() => {
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('LocalStorageProvider', () => {
    let provider: LocalStorageProvider

    beforeEach(() => {
      provider = new LocalStorageProvider('./test-storage')
      ;(fs.existsSync as any).mockReturnValue(true)
    })

    describe('upload', () => {
      beforeEach(() => {
        ;(fs.promises.writeFile as any).mockResolvedValue(undefined)
      })

      it('should upload template to local storage', async () => {
        const uploadData = {
          templateId: 'template-1',
          userId: 'user-1',
          templateData: { name: 'Test Template', config: {} },
          metadata: {
            name: 'Test Template',
            size: 1024,
            checksum: 'test-checksum',
            contentType: 'application/json'
          }
        }

        const result = await provider.upload(uploadData)

        expect(fs.promises.writeFile).toHaveBeenCalledWith(
          expect.stringContaining('template-1_user-1.json'),
          expect.stringContaining('"name":"Test Template"'),
          'utf8'
        )

        expect(result.url).toBe('local://template-1_user-1.json')
        expect(result.checksum).toBe('mock-checksum')
        expect(result.uploadedAt).toBeInstanceOf(Date)
      })

      it('should handle upload errors', async () => {
        ;(fs.promises.writeFile as any).mockRejectedValue(new Error('Write failed'))

        const uploadData = {
          templateId: 'template-1',
          userId: 'user-1',
          templateData: {},
          metadata: {
            name: 'Test',
            size: 100,
            checksum: 'test',
            contentType: 'application/json'
          }
        }

        await expect(provider.upload(uploadData))
          .rejects.toThrow('Failed to upload template to local storage')
      })
    })

    describe('download', () => {
      beforeEach(() => {
        ;(fs.promises.readFile as any).mockResolvedValue('{"name":"Test Template"}')
      })

      it('should download template from local storage', async () => {
        const url = 'local://template-1_user-1.json'
        const result = await provider.download(url)

        expect(fs.promises.readFile).toHaveBeenCalledWith(
          expect.stringContaining('template-1_user-1.json'),
          'utf8'
        )

        expect(result).toEqual({ name: 'Test Template' })
      })

      it('should handle download errors', async () => {
        ;(fs.promises.readFile as any).mockRejectedValue(new Error('Read failed'))

        const url = 'local://template-1_user-1.json'

        await expect(provider.download(url))
          .rejects.toThrow('Failed to download template from local storage')
      })

      it('should handle invalid JSON', async () => {
        ;(fs.promises.readFile as any).mockResolvedValue('invalid json')

        const url = 'local://template-1_user-1.json'

        await expect(provider.download(url))
          .rejects.toThrow()
      })
    })

    describe('delete', () => {
      beforeEach(() => {
        ;(fs.promises.unlink as any).mockResolvedValue(undefined)
      })

      it('should delete template from local storage', async () => {
        const url = 'local://template-1_user-1.json'
        await provider.delete(url)

        expect(fs.promises.unlink).toHaveBeenCalledWith(
          expect.stringContaining('template-1_user-1.json')
        )
      })

      it('should handle delete errors', async () => {
        ;(fs.promises.unlink as any).mockRejectedValue(new Error('Delete failed'))

        const url = 'local://template-1_user-1.json'

        await expect(provider.delete(url))
          .rejects.toThrow('Failed to delete template from local storage')
      })
    })

    describe('exists', () => {
      it('should return true when file exists', async () => {
        ;(fs.promises.access as any).mockResolvedValue(undefined)

        const url = 'local://template-1_user-1.json'
        const exists = await provider.exists(url)

        expect(exists).toBe(true)
        expect(fs.promises.access).toHaveBeenCalledWith(
          expect.stringContaining('template-1_user-1.json')
        )
      })

      it('should return false when file does not exist', async () => {
        ;(fs.promises.access as any).mockRejectedValue(new Error('File not found'))

        const url = 'local://template-1_user-1.json'
        const exists = await provider.exists(url)

        expect(exists).toBe(false)
      })
    })

    describe('getMetadata', () => {
      beforeEach(() => {
        ;(fs.promises.readFile as any).mockResolvedValue('{"name":"Test"}')
        ;(fs.promises.stat as any).mockResolvedValue({
          size: 1024,
          mtime: new Date('2024-01-15')
        })
      })

      it('should return file metadata', async () => {
        const url = 'local://template-1_user-1.json'
        const metadata = await provider.getMetadata(url)

        expect(metadata.size).toBe(1024)
        expect(metadata.checksum).toBe('mock-checksum')
        expect(metadata.lastModified).toEqual(new Date('2024-01-15'))
        expect(metadata.contentType).toBe('application/json')
      })

      it('should handle metadata errors', async () => {
        ;(fs.promises.stat as any).mockRejectedValue(new Error('Stat failed'))

        const url = 'local://template-1_user-1.json'

        await expect(provider.getMetadata(url))
          .rejects.toThrow('Failed to get template metadata from local storage')
      })
    })
  })

  describe('S3StorageProvider', () => {
    let provider: S3StorageProvider

    beforeEach(() => {
      provider = new S3StorageProvider({
        accessKey: 'test-key',
        secretKey: 'test-secret',
        region: 'us-east-1',
        bucket: 'test-bucket'
      })
    })

    describe('upload', () => {
      it('should return mock upload result', async () => {
        const uploadData = {
          templateId: 'template-1',
          userId: 'user-1',
          templateData: { name: 'Test Template' },
          metadata: {
            name: 'Test Template',
            size: 1024,
            checksum: 'test-checksum',
            contentType: 'application/json'
          }
        }

        const result = await provider.upload(uploadData)

        expect(result.url).toBe('s3://test-bucket/templates/user-1/template-1.json')
        expect(result.checksum).toBe('test-checksum')
        expect(result.uploadedAt).toBeInstanceOf(Date)
      })
    })

    describe('download', () => {
      it('should throw error for mock implementation', async () => {
        const url = 's3://test-bucket/templates/user-1/template-1.json'

        await expect(provider.download(url))
          .rejects.toThrow('S3 download not implemented in mock')
      })
    })

    describe('delete', () => {
      it('should handle delete operation', async () => {
        const url = 's3://test-bucket/templates/user-1/template-1.json'
        
        // Should not throw error
        await expect(provider.delete(url)).resolves.not.toThrow()
      })
    })

    describe('exists', () => {
      it('should return true for mock implementation', async () => {
        const url = 's3://test-bucket/templates/user-1/template-1.json'
        const exists = await provider.exists(url)

        expect(exists).toBe(true)
      })
    })

    describe('getMetadata', () => {
      it('should return mock metadata', async () => {
        const url = 's3://test-bucket/templates/user-1/template-1.json'
        const metadata = await provider.getMetadata(url)

        expect(metadata.size).toBe(1024)
        expect(metadata.checksum).toBe('mock-checksum')
        expect(metadata.contentType).toBe('application/json')
      })
    })
  })

  describe('CloudStorageService', () => {
    describe('initialization', () => {
      it('should initialize with local storage provider', () => {
        const config = { provider: 'local' as const }
        const service = new CloudStorageService(config)

        expect(service).toBeInstanceOf(CloudStorageService)
      })

      it('should initialize with AWS S3 provider', () => {
        const config = {
          provider: 'aws' as const,
          accessKey: 'test-key',
          secretKey: 'test-secret',
          bucket: 'test-bucket',
          region: 'us-east-1'
        }

        const service = new CloudStorageService(config)

        expect(service).toBeInstanceOf(CloudStorageService)
      })

      it('should throw error for incomplete AWS configuration', () => {
        const config = {
          provider: 'aws' as const,
          accessKey: 'test-key'
          // Missing secretKey and bucket
        }

        expect(() => new CloudStorageService(config))
          .toThrow('AWS configuration incomplete')
      })
    })

    describe('uploadTemplate', () => {
      beforeEach(() => {
        service = new CloudStorageService({ provider: 'local' })
        ;(fs.existsSync as any).mockReturnValue(true)
        ;(fs.promises.writeFile as any).mockResolvedValue(undefined)
      })

      it('should upload template successfully', async () => {
        const uploadData = {
          templateId: 'template-1',
          userId: 'user-1',
          templateData: { name: 'Test Template' },
          metadata: {
            name: 'Test Template',
            size: 1024,
            checksum: 'test-checksum',
            contentType: 'application/json'
          }
        }

        const result = await service.uploadTemplate(uploadData)

        expect(result.url).toBe('local://template-1_user-1.json')
        expect(result.uploadedAt).toBeInstanceOf(Date)
      })

      it('should validate upload data', async () => {
        const invalidData = {
          templateId: '', // Invalid
          userId: 'user-1',
          templateData: {},
          metadata: {
            name: 'Test',
            size: 1024,
            checksum: 'test',
            contentType: 'application/json'
          }
        }

        await expect(service.uploadTemplate(invalidData))
          .rejects.toThrow()
      })
    })

    describe('syncTemplate', () => {
      beforeEach(() => {
        service = new CloudStorageService({ provider: 'local' })
        ;(fs.existsSync as any).mockReturnValue(true)
        ;(fs.promises.writeFile as any).mockResolvedValue(undefined)
      })

      it('should sync template successfully', async () => {
        const templateData = {
          name: 'Test Template',
          description: 'Test description',
          config: { layout: 'single-column' }
        }

        const result = await service.syncTemplate('template-1', 'user-1', templateData)

        expect(result.success).toBe(true)
        expect(result.url).toBe('local://template-1_user-1.json')
      })

      it('should handle sync errors', async () => {
        ;(fs.promises.writeFile as any).mockRejectedValue(new Error('Write failed'))

        const templateData = { name: 'Test Template' }

        const result = await service.syncTemplate('template-1', 'user-1', templateData)

        expect(result.success).toBe(false)
        expect(result.error).toBeDefined()
      })
    })

    describe('batchSyncTemplates', () => {
      beforeEach(() => {
        service = new CloudStorageService({ provider: 'local' })
        ;(fs.existsSync as any).mockReturnValue(true)
        ;(fs.promises.writeFile as any).mockResolvedValue(undefined)
      })

      it('should sync multiple templates', async () => {
        const templates = [
          {
            templateId: 'template-1',
            userId: 'user-1',
            templateData: { name: 'Template 1' }
          },
          {
            templateId: 'template-2',
            userId: 'user-1',
            templateData: { name: 'Template 2' }
          }
        ]

        const results = await service.batchSyncTemplates(templates)

        expect(results).toHaveLength(2)
        expect(results[0].success).toBe(true)
        expect(results[1].success).toBe(true)
        expect(results[0].templateId).toBe('template-1')
        expect(results[1].templateId).toBe('template-2')
      })

      it('should handle partial failures in batch sync', async () => {
        ;(fs.promises.writeFile as any)
          .mockResolvedValueOnce(undefined)
          .mockRejectedValueOnce(new Error('Write failed'))

        const templates = [
          {
            templateId: 'template-1',
            userId: 'user-1',
            templateData: { name: 'Template 1' }
          },
          {
            templateId: 'template-2',
            userId: 'user-1',
            templateData: { name: 'Template 2' }
          }
        ]

        const results = await service.batchSyncTemplates(templates)

        expect(results[0].success).toBe(true)
        expect(results[1].success).toBe(false)
        expect(results[1].error).toBeDefined()
      })
    })

    describe('downloadTemplate', () => {
      beforeEach(() => {
        service = new CloudStorageService({ provider: 'local' })
        ;(fs.promises.readFile as any).mockResolvedValue('{"name":"Test Template"}')
      })

      it('should download template successfully', async () => {
        const url = 'local://template-1_user-1.json'
        const templateData = await service.downloadTemplate(url)

        expect(templateData).toEqual({ name: 'Test Template' })
      })
    })

    describe('deleteTemplate', () => {
      beforeEach(() => {
        service = new CloudStorageService({ provider: 'local' })
        ;(fs.promises.unlink as any).mockResolvedValue(undefined)
      })

      it('should delete template successfully', async () => {
        const url = 'local://template-1_user-1.json'
        
        await expect(service.deleteTemplate(url)).resolves.not.toThrow()
      })
    })

    describe('templateExists', () => {
      beforeEach(() => {
        service = new CloudStorageService({ provider: 'local' })
      })

      it('should check template existence', async () => {
        ;(fs.promises.access as any).mockResolvedValue(undefined)

        const url = 'local://template-1_user-1.json'
        const exists = await service.templateExists(url)

        expect(exists).toBe(true)
      })

      it('should return false for non-existent template', async () => {
        ;(fs.promises.access as any).mockRejectedValue(new Error('Not found'))

        const url = 'local://template-1_user-1.json'
        const exists = await service.templateExists(url)

        expect(exists).toBe(false)
      })
    })

    describe('getTemplateMetadata', () => {
      beforeEach(() => {
        service = new CloudStorageService({ provider: 'local' })
        ;(fs.promises.readFile as any).mockResolvedValue('{"name":"Test"}')
        ;(fs.promises.stat as any).mockResolvedValue({
          size: 1024,
          mtime: new Date('2024-01-15')
        })
      })

      it('should get template metadata', async () => {
        const url = 'local://template-1_user-1.json'
        const metadata = await service.getTemplateMetadata(url)

        expect(metadata.size).toBe(1024)
        expect(metadata.contentType).toBe('application/json')
      })
    })

    describe('getStorageStats', () => {
      beforeEach(() => {
        service = new CloudStorageService({ provider: 'local' })
      })

      it('should return storage statistics', async () => {
        const stats = await service.getStorageStats('user-1')

        expect(stats.totalTemplates).toBe(0)
        expect(stats.totalSize).toBe(0)
        expect(stats.lastSync).toBeNull()
      })
    })
  })

  describe('error handling', () => {
    beforeEach(() => {
      service = new CloudStorageService({ provider: 'local' })
    })

    it('should handle provider initialization errors', () => {
      expect(() => new CloudStorageService({
        provider: 'aws' as const,
        // Missing required configuration
      })).toThrow()
    })

    it('should handle invalid template data', async () => {
      const invalidData = {
        templateId: 'template-1',
        userId: 'user-1',
        templateData: null, // Invalid
        metadata: {
          name: 'Test',
          size: 0,
          checksum: '',
          contentType: 'application/json'
        }
      }

      const result = await service.syncTemplate('template-1', 'user-1', invalidData)

      expect(result.success).toBe(false)
      expect(result.error).toBeDefined()
    })
  })
})
