#!/bin/bash
# CareerCraft Local Development - Prerequisites Installation (macOS/Linux)

echo "🚀 CareerCraft Local Development Setup - macOS/Linux"
echo "================================================="

# Detect OS
if [[ "$OSTYPE" == "darwin"* ]]; then
    OS="macos"
    echo "📱 Detected: macOS"
elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
    OS="linux"
    echo "🐧 Detected: Linux"
else
    echo "❌ Unsupported OS: $OSTYPE"
    exit 1
fi

# Install Homebrew (macOS) or update package manager (Linux)
if [[ "$OS" == "macos" ]]; then
    if ! command -v brew &> /dev/null; then
        echo "📦 Installing Homebrew..."
        /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
    fi
    
    echo "📦 Updating Homebrew..."
    brew update
    
    # Install packages via Homebrew
    echo "📦 Installing Node.js..."
    brew install node
    
    echo "📦 Installing Git..."
    brew install git
    
    echo "📦 Installing PostgreSQL..."
    brew install postgresql@14
    brew services start postgresql@14
    
    echo "📦 Installing Redis..."
    brew install redis
    brew services start redis
    
    echo "📦 Installing Stripe CLI..."
    brew install stripe/stripe-cli/stripe
    
elif [[ "$OS" == "linux" ]]; then
    # Update package manager
    echo "📦 Updating package manager..."
    sudo apt update
    
    # Install Node.js
    echo "📦 Installing Node.js..."
    curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
    sudo apt-get install -y nodejs
    
    # Install Git
    echo "📦 Installing Git..."
    sudo apt-get install -y git
    
    # Install PostgreSQL
    echo "📦 Installing PostgreSQL..."
    sudo apt-get install -y postgresql postgresql-contrib
    sudo systemctl start postgresql
    sudo systemctl enable postgresql
    
    # Install Redis
    echo "📦 Installing Redis..."
    sudo apt-get install -y redis-server
    sudo systemctl start redis-server
    sudo systemctl enable redis-server
    
    # Install Stripe CLI
    echo "📦 Installing Stripe CLI..."
    curl -s https://packages.stripe.dev/api/security/keypair/stripe-cli-gpg/public | gpg --dearmor | sudo tee /usr/share/keyrings/stripe.gpg
    echo "deb [signed-by=/usr/share/keyrings/stripe.gpg] https://packages.stripe.dev/stripe-cli-debian-local stable main" | sudo tee -a /etc/apt/sources.list.d/stripe.list
    sudo apt update
    sudo apt install stripe
fi

# Install global npm packages
echo "📦 Installing global npm packages..."
npm install -g @prisma/cli
npm install -g typescript
npm install -g ts-node
npm install -g @playwright/test

# Verify installations
echo "✅ Verifying installations..."

if command -v node &> /dev/null; then
    echo "✅ Node.js: $(node --version)"
else
    echo "❌ Node.js installation failed"
fi

if command -v npm &> /dev/null; then
    echo "✅ npm: $(npm --version)"
else
    echo "❌ npm not available"
fi

if command -v git &> /dev/null; then
    echo "✅ Git: $(git --version)"
else
    echo "❌ Git installation failed"
fi

if command -v psql &> /dev/null; then
    echo "✅ PostgreSQL: $(psql --version)"
else
    echo "❌ PostgreSQL installation failed"
fi

if command -v redis-cli &> /dev/null; then
    if redis-cli ping &> /dev/null; then
        echo "✅ Redis: Available"
    else
        echo "⚠️  Redis installed but not running"
    fi
else
    echo "❌ Redis installation failed"
fi

if command -v stripe &> /dev/null; then
    echo "✅ Stripe CLI: $(stripe --version)"
else
    echo "❌ Stripe CLI installation failed"
fi

echo ""
echo "🎉 Prerequisites installation complete!"
echo "📝 Next steps:"
echo "   1. Restart your terminal"
echo "   2. Continue with project setup"
echo "   3. Configure PostgreSQL user (if needed)"

# PostgreSQL setup for Linux
if [[ "$OS" == "linux" ]]; then
    echo ""
    echo "🔧 PostgreSQL Setup (Linux):"
    echo "   Run these commands to set up PostgreSQL:"
    echo "   sudo -u postgres createuser --interactive"
    echo "   sudo -u postgres createdb careercraft_local"
fi

echo "✅ All services should be running!"
