# CareerCraft Complete Local Testing Setup (Windows PowerShell)
# Run as Administrator

Write-Host "🚀 CareerCraft Complete Local Testing Setup" -ForegroundColor Green
Write-Host "===========================================" -ForegroundColor Green
Write-Host ""

# Check if running as Administrator
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "❌ Please run this script as Administrator" -ForegroundColor Red
    Write-Host "   Right-click PowerShell and select 'Run as Administrator'" -ForegroundColor Yellow
    exit 1
}

# Step 1: Install Prerequisites
Write-Host "📦 Step 1: Installing Prerequisites" -ForegroundColor Yellow
Write-Host "-----------------------------------" -ForegroundColor Yellow

if (Test-Path "setup\install-prerequisites.ps1") {
    & "setup\install-prerequisites.ps1"
} else {
    Write-Host "❌ Prerequisites script not found. Please ensure you're in the correct directory." -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "✅ Prerequisites installation complete!" -ForegroundColor Green
Write-Host ""

# Step 2: Create Project
Write-Host "📁 Step 2: Creating Project Structure" -ForegroundColor Yellow
Write-Host "-------------------------------------" -ForegroundColor Yellow

# Convert and run the bash script using Git Bash or WSL if available
if (Get-Command "bash" -ErrorAction SilentlyContinue) {
    bash setup/create-project.sh
} else {
    Write-Host "⚠️  Bash not found. Creating project manually..." -ForegroundColor Yellow
    
    # Manual project creation for Windows
    $projectName = "careercraft-local"
    
    if (Test-Path $projectName) {
        $response = Read-Host "Directory $projectName already exists. Remove it? (y/n)"
        if ($response -eq "y" -or $response -eq "Y") {
            Remove-Item -Recurse -Force $projectName
            Write-Host "🗑️  Removed existing directory" -ForegroundColor Yellow
        } else {
            Write-Host "❌ Aborting setup" -ForegroundColor Red
            exit 1
        }
    }
    
    Write-Host "📦 Creating Next.js project..." -ForegroundColor Yellow
    npx create-next-app@latest $projectName --typescript --tailwind --eslint --app --src-dir --import-alias "@/*"
    
    Set-Location $projectName
    
    Write-Host "📦 Installing dependencies..." -ForegroundColor Yellow
    
    # Install core dependencies
    npm install @prisma/client prisma
    npm install next-auth "@auth/prisma-adapter"
    npm install stripe "@stripe/stripe-js"
    npm install openai
    npm install "@radix-ui/react-dialog" "@radix-ui/react-dropdown-menu" "@radix-ui/react-select"
    npm install "@radix-ui/react-tabs" "@radix-ui/react-toast" "@radix-ui/react-tooltip"
    npm install lucide-react
    npm install "@hookform/resolvers" react-hook-form zod
    npm install axios swr
    npm install recharts
    npm install react-pdf "@react-pdf/renderer" jspdf html2canvas
    npm install date-fns
    npm install clsx tailwind-merge
    npm install redis
    npm install nodemailer "@types/nodemailer"
    
    # Install dev dependencies
    npm install -D "@types/node" "@types/react" "@types/react-dom"
    npm install -D eslint-config-prettier prettier
    npm install -D jest "@testing-library/react" "@testing-library/jest-dom" "@testing-library/user-event"
    npm install -D jest-environment-jsdom
    npm install -D playwright "@playwright/test"
    npm install -D vitest "@vitejs/plugin-react"
    npm install -D prisma-erd-generator
    npm install -D cross-env concurrently tsx
}

Write-Host ""
Write-Host "✅ Project structure created!" -ForegroundColor Green
Write-Host ""

# Step 3: Copy Setup Files
Write-Host "📋 Step 3: Setting Up Configuration Files" -ForegroundColor Yellow
Write-Host "-----------------------------------------" -ForegroundColor Yellow

# Copy setup files
if (Test-Path "..\setup\src") {
    Copy-Item -Recurse -Force "..\setup\src\*" "src\"
}
Copy-Item "..\setup\jest.config.js" "."
Copy-Item "..\setup\jest.setup.js" "."
Copy-Item "..\setup\playwright.config.ts" "."
if (Test-Path "..\setup\tests") {
    Copy-Item -Recurse -Force "..\setup\tests" "."
}

Write-Host "✅ Configuration files copied!" -ForegroundColor Green
Write-Host ""

# Step 4: Database Setup
Write-Host "🗄️  Step 4: Setting Up Database" -ForegroundColor Yellow
Write-Host "-------------------------------" -ForegroundColor Yellow

# Create .env.local
$envContent = @'
# Database
DATABASE_URL="postgresql://careercraft_user:local_password@localhost:5432/careercraft_local"

# NextAuth
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-local-secret-key-change-this-in-production"

# Google OAuth (Get from Google Cloud Console)
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"

# GitHub OAuth (Get from GitHub Developer Settings)
GITHUB_ID="your-github-client-id"
GITHUB_SECRET="your-github-client-secret"

# Stripe (Test Mode - Get from Stripe Dashboard)
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY="pk_test_..."
STRIPE_SECRET_KEY="sk_test_..."
STRIPE_WEBHOOK_SECRET="whsec_..."

# OpenAI (Get from OpenAI Platform)
OPENAI_API_KEY="sk-..."

# Redis
REDIS_URL="redis://localhost:6379"

# Email (Local testing)
EMAIL_SERVER="smtp://localhost:1025"
EMAIL_FROM="noreply@localhost"

# App Settings
NODE_ENV="development"
NEXT_PUBLIC_APP_URL="http://localhost:3000"
'@

$envContent | Out-File -FilePath ".env.local" -Encoding UTF8

# Copy Prisma schema
if (-not (Test-Path "prisma")) {
    New-Item -ItemType Directory -Path "prisma"
}
Copy-Item "..\setup\prisma-schema.prisma" "prisma\schema.prisma"

# Database setup
Write-Host "🔧 Setting up PostgreSQL database..." -ForegroundColor Yellow

# Check if PostgreSQL is running
try {
    $pgStatus = & pg_isready -h localhost -p 5432 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ PostgreSQL is running" -ForegroundColor Green
        
        # Create database and user
        Write-Host "👤 Creating database user and database..." -ForegroundColor Yellow
        
        & psql -U postgres -c "CREATE USER careercraft_user WITH PASSWORD 'local_password';" 2>$null
        & psql -U postgres -c "CREATE DATABASE careercraft_local OWNER careercraft_user;" 2>$null
        & psql -U postgres -c "GRANT ALL PRIVILEGES ON DATABASE careercraft_local TO careercraft_user;" 2>$null
        & psql -U postgres -c "ALTER USER careercraft_user CREATEDB;" 2>$null
        
        Write-Host "✅ Database setup complete!" -ForegroundColor Green
    } else {
        Write-Host "❌ PostgreSQL is not running. Please start PostgreSQL service." -ForegroundColor Red
        Write-Host "   You can start it from Windows Services or pgAdmin." -ForegroundColor Yellow
    }
} catch {
    Write-Host "⚠️  Could not connect to PostgreSQL. Please ensure it's installed and running." -ForegroundColor Yellow
}

# Generate Prisma client and push schema
Write-Host "⚙️  Setting up Prisma..." -ForegroundColor Yellow
npx prisma generate
npx prisma db push

Write-Host ""
Write-Host "✅ Database setup complete!" -ForegroundColor Green
Write-Host ""

# Step 5: Install Dependencies
Write-Host "📦 Step 5: Final Dependency Installation" -ForegroundColor Yellow
Write-Host "---------------------------------------" -ForegroundColor Yellow

npm install

Write-Host ""
Write-Host "✅ Dependencies installed!" -ForegroundColor Green
Write-Host ""

# Step 6: Create Startup Script
Write-Host "🚀 Step 6: Creating Startup Scripts" -ForegroundColor Yellow
Write-Host "-----------------------------------" -ForegroundColor Yellow

# Create Windows startup script
$startupScript = @'
@echo off
echo Starting CareerCraft Local Development
echo ========================================

echo Checking services...

REM Check if PostgreSQL is running
pg_isready -h localhost -p 5432 >nul 2>&1
if %errorlevel% neq 0 (
    echo PostgreSQL is not running. Please start PostgreSQL service.
    echo You can start it from Windows Services or pgAdmin.
    pause
    exit /b 1
)

echo PostgreSQL is running!

REM Check if Redis is running (optional)
redis-cli ping >nul 2>&1
if %errorlevel% neq 0 (
    echo Redis is not running. Starting Redis...
    start redis-server
)

echo Services are running!
echo.

echo Starting Next.js development server...
echo App will be available at: http://localhost:3000
echo Database admin at: http://localhost:5555 (run npm run db:studio in another terminal)
echo.
echo Useful commands:
echo    npm run test          - Run unit tests
echo    npm run test:e2e      - Run E2E tests
echo    npm run db:studio     - Open database admin
echo    npm run stripe:listen - Listen for Stripe webhooks
echo.

npm run dev
'@

$startupScript | Out-File -FilePath "start-dev.bat" -Encoding ASCII

Write-Host "✅ Startup scripts created!" -ForegroundColor Green
Write-Host ""

# Step 7: Final Setup
Write-Host "🧪 Step 7: Running Initial Tests" -ForegroundColor Yellow
Write-Host "--------------------------------" -ForegroundColor Yellow

Write-Host "🔧 Type checking..." -ForegroundColor Yellow
npm run type-check

Write-Host "🧪 Running unit tests..." -ForegroundColor Yellow
npm run test -- --passWithNoTests

Write-Host "🎭 Installing Playwright browsers..." -ForegroundColor Yellow
npx playwright install

Write-Host ""
Write-Host "✅ Initial tests complete!" -ForegroundColor Green
Write-Host ""

# Final Summary
Write-Host "🎉 SETUP COMPLETE!" -ForegroundColor Green
Write-Host "==================" -ForegroundColor Green
Write-Host ""
Write-Host "📊 Setup Summary:" -ForegroundColor Yellow
Write-Host "   ✅ Prerequisites installed" -ForegroundColor Green
Write-Host "   ✅ Project structure created" -ForegroundColor Green
Write-Host "   ✅ Database configured" -ForegroundColor Green
Write-Host "   ✅ Dependencies installed" -ForegroundColor Green
Write-Host "   ✅ Tests configured" -ForegroundColor Green
Write-Host "   ✅ Development environment ready" -ForegroundColor Green
Write-Host ""
Write-Host "🔧 Next Steps:" -ForegroundColor Yellow
Write-Host "   1. Configure your API keys in .env.local:" -ForegroundColor White
Write-Host "      - GOOGLE_CLIENT_ID and GOOGLE_CLIENT_SECRET" -ForegroundColor Gray
Write-Host "      - GITHUB_ID and GITHUB_SECRET" -ForegroundColor Gray
Write-Host "      - OPENAI_API_KEY" -ForegroundColor Gray
Write-Host "      - STRIPE_PUBLISHABLE_KEY and STRIPE_SECRET_KEY" -ForegroundColor Gray
Write-Host ""
Write-Host "   2. Start the development server:" -ForegroundColor White
Write-Host "      start-dev.bat" -ForegroundColor Gray
Write-Host ""
Write-Host "   3. Visit http://localhost:3000 to see your app!" -ForegroundColor White
Write-Host ""
Write-Host "📚 Testing Commands:" -ForegroundColor Yellow
Write-Host "   npm run test           - Unit tests" -ForegroundColor Gray
Write-Host "   npm run test:e2e       - End-to-end tests" -ForegroundColor Gray
Write-Host "   npm run test:coverage  - Coverage report" -ForegroundColor Gray
Write-Host "   npm run db:studio      - Database admin UI" -ForegroundColor Gray
Write-Host ""
Write-Host "🎯 Epic Testing Checklist:" -ForegroundColor Yellow
Write-Host "   □ Epic 8.0: Payment system (Stripe integration)" -ForegroundColor Gray
Write-Host "   □ Epic 1.0: Career intelligence (OpenAI integration)" -ForegroundColor Gray
Write-Host "   □ Epic 6.0: Browser extension (autofill testing)" -ForegroundColor Gray
Write-Host "   □ Epic 2.0: Collaboration features" -ForegroundColor Gray
Write-Host "   □ Epic 3.0: Template marketplace" -ForegroundColor Gray
Write-Host "   □ Epic 4.0: External integrations" -ForegroundColor Gray
Write-Host "   □ Epic 5.0: Mobile responsiveness" -ForegroundColor Gray
Write-Host "   □ Epic 7.0: Advanced AI features" -ForegroundColor Gray
Write-Host ""
Write-Host "🚀 Happy coding! Your CareerCraft local testing environment is ready!" -ForegroundColor Green

Write-Host ""
Write-Host "🎉 Setup script completed! Check the output above for any errors." -ForegroundColor Green
Write-Host "If successful, navigate to the careercraft-local folder and run start-dev.bat" -ForegroundColor Yellow
