/**
 * Site Adapter
 * 
 * Provides site-specific optimizations and adaptations
 * for major job boards and company career pages.
 */

export interface SiteConfig {
  name: string
  selectors: SiteSelectors
  fieldMappings: { [key: string]: string[] }
  formDetection: FormDetectionConfig
  metadata: MetadataConfig
}

export interface SiteSelectors {
  forms: string[]
  jobTitle: string[]
  company: string[]
  jobDescription: string[]
  applicationButton: string[]
}

export interface FormDetectionConfig {
  keywords: string[]
  excludeSelectors: string[]
  confidenceBoost: number
}

export interface MetadataConfig {
  titleSelectors: string[]
  companySelectors: string[]
  locationSelectors: string[]
}

export class SiteAdapter {
  private currentSite: string = ''
  private siteConfig: SiteConfig | null = null
  private siteConfigs: Map<string, SiteConfig> = new Map()

  constructor() {
    this.initializeSiteConfigs()
  }

  /**
   * Configure adapter for specific site
   */
  configure(hostname: string): void {
    this.currentSite = hostname
    this.siteConfig = this.getSiteConfig(hostname)
  }

  /**
   * Extract metadata from page
   */
  extractMetadata(document: Document): {
    company?: string
    jobTitle?: string
    location?: string
    jobDescription?: string
  } {
    if (!this.siteConfig) {
      return this.extractGenericMetadata(document)
    }

    const metadata: any = {}

    // Extract company
    for (const selector of this.siteConfig.metadata.companySelectors) {
      const element = document.querySelector(selector)
      if (element?.textContent?.trim()) {
        metadata.company = element.textContent.trim()
        break
      }
    }

    // Extract job title
    for (const selector of this.siteConfig.metadata.titleSelectors) {
      const element = document.querySelector(selector)
      if (element?.textContent?.trim()) {
        metadata.jobTitle = element.textContent.trim()
        break
      }
    }

    // Extract location
    for (const selector of this.siteConfig.metadata.locationSelectors) {
      const element = document.querySelector(selector)
      if (element?.textContent?.trim()) {
        metadata.location = element.textContent.trim()
        break
      }
    }

    return metadata
  }

  /**
   * Extract job description
   */
  extractJobDescription(document: Document): string {
    if (!this.siteConfig) {
      return this.extractGenericJobDescription(document)
    }

    for (const selector of this.siteConfig.selectors.jobDescription) {
      const element = document.querySelector(selector)
      if (element?.textContent?.trim()) {
        return element.textContent.trim()
      }
    }

    return ''
  }

  /**
   * Determine field type with site-specific logic
   */
  determineFieldType(
    element: HTMLElement,
    label: string,
    placeholder: string
  ): string | null {
    if (!this.siteConfig) {
      return null
    }

    const combinedText = `${label} ${placeholder}`.toLowerCase()

    // Check site-specific field mappings
    for (const [fieldType, keywords] of Object.entries(this.siteConfig.fieldMappings)) {
      if (keywords.some(keyword => combinedText.includes(keyword.toLowerCase()))) {
        return fieldType
      }
    }

    return null
  }

  /**
   * Classify form with site-specific logic
   */
  classifyForm(
    formElement: HTMLFormElement,
    fields: any[],
    metadata: any
  ): { type: 'job-application' | 'profile' | 'other', confidence: number } | null {
    if (!this.siteConfig) {
      return null
    }

    let confidence = 0
    const formText = formElement.textContent?.toLowerCase() || ''

    // Check site-specific keywords
    const keywordMatches = this.siteConfig.formDetection.keywords.filter(keyword =>
      formText.includes(keyword.toLowerCase())
    ).length

    confidence += (keywordMatches / this.siteConfig.formDetection.keywords.length) * 0.4

    // Apply site-specific confidence boost
    confidence += this.siteConfig.formDetection.confidenceBoost

    // Check if form should be excluded
    const shouldExclude = this.siteConfig.formDetection.excludeSelectors.some(selector => {
      try {
        return formElement.matches(selector) || formElement.querySelector(selector)
      } catch {
        return false
      }
    })

    if (shouldExclude) {
      return { type: 'other', confidence: 0 }
    }

    // Determine type based on confidence
    let type: 'job-application' | 'profile' | 'other' = 'other'
    if (confidence > 0.7) {
      type = 'job-application'
    } else if (confidence > 0.4) {
      type = 'profile'
    }

    return { type, confidence: Math.min(confidence, 0.95) }
  }

  /**
   * Get site-specific form selectors
   */
  getFormSelectors(): string[] {
    return this.siteConfig?.selectors.forms || ['form']
  }

  /**
   * Check if site is supported
   */
  isSupported(hostname: string): boolean {
    return this.siteConfigs.has(hostname) || this.siteConfigs.has(this.getDomain(hostname))
  }

  /**
   * Get site configuration
   */
  private getSiteConfig(hostname: string): SiteConfig | null {
    // Try exact hostname match first
    if (this.siteConfigs.has(hostname)) {
      return this.siteConfigs.get(hostname)!
    }

    // Try domain match
    const domain = this.getDomain(hostname)
    if (this.siteConfigs.has(domain)) {
      return this.siteConfigs.get(domain)!
    }

    return null
  }

  /**
   * Extract domain from hostname
   */
  private getDomain(hostname: string): string {
    const parts = hostname.split('.')
    if (parts.length >= 2) {
      return parts.slice(-2).join('.')
    }
    return hostname
  }

  /**
   * Extract generic metadata when no site config available
   */
  private extractGenericMetadata(document: Document): {
    company?: string
    jobTitle?: string
    location?: string
  } {
    const metadata: any = {}

    // Try to extract from page title
    const title = document.title
    if (title.includes(' at ')) {
      const parts = title.split(' at ')
      metadata.jobTitle = parts[0].trim()
      metadata.company = parts[1].split(' - ')[0].trim()
    }

    // Try to extract from meta tags
    const ogTitle = document.querySelector('meta[property="og:title"]')?.getAttribute('content')
    if (ogTitle && !metadata.jobTitle) {
      metadata.jobTitle = ogTitle
    }

    return metadata
  }

  /**
   * Extract generic job description
   */
  private extractGenericJobDescription(document: Document): string {
    const selectors = [
      '[data-testid="job-description"]',
      '.job-description',
      '.job-details',
      '.description',
      '#job-description',
      '.posting-description'
    ]

    for (const selector of selectors) {
      const element = document.querySelector(selector)
      if (element?.textContent?.trim()) {
        return element.textContent.trim()
      }
    }

    return ''
  }

  /**
   * Initialize site configurations
   */
  private initializeSiteConfigs(): void {
    // LinkedIn configuration
    this.siteConfigs.set('linkedin.com', {
      name: 'LinkedIn',
      selectors: {
        forms: [
          'form[data-test-id="apply-form"]',
          'form.jobs-apply-form',
          '.jobs-easy-apply-modal form',
          '.application-outlet form'
        ],
        jobTitle: [
          '.jobs-unified-top-card__job-title',
          '.job-title',
          'h1[data-test-id="job-title"]'
        ],
        company: [
          '.jobs-unified-top-card__company-name',
          '.job-company-name',
          '[data-test-id="job-company-name"]'
        ],
        jobDescription: [
          '.jobs-description-content__text',
          '.job-description',
          '[data-test-id="job-description"]'
        ],
        applicationButton: [
          '.jobs-apply-button',
          '[data-test-id="apply-button"]'
        ]
      },
      fieldMappings: {
        'firstName': ['first name', 'given name'],
        'lastName': ['last name', 'family name', 'surname'],
        'email': ['email', 'email address'],
        'phone': ['phone', 'mobile', 'telephone'],
        'resume': ['resume', 'cv', 'curriculum vitae'],
        'coverLetter': ['cover letter', 'covering letter'],
        'linkedin': ['linkedin', 'linkedin profile', 'profile url']
      },
      formDetection: {
        keywords: ['apply', 'application', 'resume', 'cover letter', 'easy apply'],
        excludeSelectors: ['.search-form', '.message-form'],
        confidenceBoost: 0.2
      },
      metadata: {
        titleSelectors: [
          '.jobs-unified-top-card__job-title h1',
          '.job-title h1',
          'h1[data-test-id="job-title"]'
        ],
        companySelectors: [
          '.jobs-unified-top-card__company-name a',
          '.job-company-name a',
          '[data-test-id="job-company-name"] a'
        ],
        locationSelectors: [
          '.jobs-unified-top-card__bullet',
          '.job-location',
          '[data-test-id="job-location"]'
        ]
      }
    })

    // Indeed configuration
    this.siteConfigs.set('indeed.com', {
      name: 'Indeed',
      selectors: {
        forms: [
          'form[data-testid="apply-form"]',
          '.ia-ApplyForm',
          '.apply-form',
          '#apply-form'
        ],
        jobTitle: [
          '[data-testid="job-title"]',
          '.jobsearch-JobInfoHeader-title',
          'h1.jobTitle'
        ],
        company: [
          '[data-testid="company-name"]',
          '.jobsearch-InlineCompanyRating',
          '.company-name'
        ],
        jobDescription: [
          '[data-testid="job-description"]',
          '.jobsearch-jobDescriptionText',
          '.job-description'
        ],
        applicationButton: [
          '[data-testid="apply-button"]',
          '.ia-ApplyButton',
          '.apply-button'
        ]
      },
      fieldMappings: {
        'firstName': ['first name', 'given name'],
        'lastName': ['last name', 'family name'],
        'email': ['email', 'email address'],
        'phone': ['phone', 'phone number'],
        'resume': ['resume', 'cv'],
        'coverLetter': ['cover letter', 'message']
      },
      formDetection: {
        keywords: ['apply', 'application', 'resume', 'indeed apply'],
        excludeSelectors: ['.search-form', '.review-form'],
        confidenceBoost: 0.15
      },
      metadata: {
        titleSelectors: [
          '[data-testid="job-title"] span',
          '.jobsearch-JobInfoHeader-title span'
        ],
        companySelectors: [
          '[data-testid="company-name"] span',
          '.jobsearch-InlineCompanyRating span'
        ],
        locationSelectors: [
          '[data-testid="job-location"]',
          '.jobsearch-JobInfoHeader-subtitle'
        ]
      }
    })

    // Glassdoor configuration
    this.siteConfigs.set('glassdoor.com', {
      name: 'Glassdoor',
      selectors: {
        forms: [
          '.apply-form',
          '.job-apply-form',
          'form[data-test="apply-form"]'
        ],
        jobTitle: [
          '[data-test="job-title"]',
          '.job-title',
          'h1.jobTitle'
        ],
        company: [
          '[data-test="employer-name"]',
          '.employer-name',
          '.company-name'
        ],
        jobDescription: [
          '[data-test="job-description"]',
          '.job-description',
          '.jobDescriptionContent'
        ],
        applicationButton: [
          '[data-test="apply-button"]',
          '.apply-btn'
        ]
      },
      fieldMappings: {
        'firstName': ['first name'],
        'lastName': ['last name'],
        'email': ['email'],
        'phone': ['phone'],
        'resume': ['resume', 'cv'],
        'coverLetter': ['cover letter']
      },
      formDetection: {
        keywords: ['apply', 'application', 'resume'],
        excludeSelectors: ['.search-form', '.review-form'],
        confidenceBoost: 0.1
      },
      metadata: {
        titleSelectors: ['[data-test="job-title"]'],
        companySelectors: ['[data-test="employer-name"]'],
        locationSelectors: ['[data-test="job-location"]']
      }
    })

    // Generic configuration for other sites
    this.siteConfigs.set('*', {
      name: 'Generic',
      selectors: {
        forms: ['form'],
        jobTitle: ['h1', '.job-title', '.position-title'],
        company: ['.company-name', '.employer-name'],
        jobDescription: ['.job-description', '.description'],
        applicationButton: ['.apply-button', '.apply-btn']
      },
      fieldMappings: {
        'firstName': ['first name', 'given name', 'fname'],
        'lastName': ['last name', 'family name', 'lname', 'surname'],
        'email': ['email', 'email address', 'e-mail'],
        'phone': ['phone', 'telephone', 'mobile', 'cell'],
        'resume': ['resume', 'cv', 'curriculum vitae'],
        'coverLetter': ['cover letter', 'covering letter', 'message']
      },
      formDetection: {
        keywords: ['apply', 'application', 'job', 'career', 'position'],
        excludeSelectors: ['.search-form', '.contact-form', '.newsletter-form'],
        confidenceBoost: 0
      },
      metadata: {
        titleSelectors: ['h1', '.job-title', '.position-title'],
        companySelectors: ['.company-name', '.employer-name'],
        locationSelectors: ['.location', '.job-location']
      }
    })
  }

  /**
   * Get current site name
   */
  getCurrentSiteName(): string {
    return this.siteConfig?.name || 'Unknown'
  }

  /**
   * Get site-specific application button selector
   */
  getApplicationButtonSelector(): string[] {
    return this.siteConfig?.selectors.applicationButton || ['.apply-button', '.apply-btn']
  }

  /**
   * Check if element should be excluded from form detection
   */
  shouldExcludeElement(element: HTMLElement): boolean {
    if (!this.siteConfig) {
      return false
    }

    return this.siteConfig.formDetection.excludeSelectors.some(selector => {
      try {
        return element.matches(selector) || element.closest(selector)
      } catch {
        return false
      }
    })
  }
}
