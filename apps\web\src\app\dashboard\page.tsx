'use client';

import { useSession } from 'next-auth/react';
import { DashboardLayout } from '@/components/dashboard/DashboardLayout';
import { DashboardOverview } from '@/components/dashboard/DashboardOverview';
import { LoadingPage } from '@/components/ui/loading';

export default function DashboardPage() {
  const { data: session, status } = useSession();

  if (status === 'loading') {
    return (
      <DashboardLayout currentPage="dashboard">
        <LoadingPage message="Loading your dashboard..." />
      </DashboardLayout>
    );
  }

  if (!session) {
    return (
      <DashboardLayout currentPage="dashboard">
        <div className="text-center glass-panel p-8 rounded-2xl">
          <p className="text-lg">Please sign in to access your dashboard.</p>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout currentPage="dashboard">
      <DashboardOverview />
    </DashboardLayout>
  );
}


