/**
 * CareerCraft Browser Extension - Popup Interface
 * 
 * Main popup interface for the browser extension
 */

import React, { useState, useEffect } from 'react'
import browser from 'webextension-polyfill'
import { 
  Target, 
  Zap, 
  Settings, 
  BarChart3, 
  CheckCircle, 
  AlertCircle, 
  Loader2,
  User,
  LogIn,
  LogOut
} from 'lucide-react'

interface ExtensionState {
  isAuthenticated: boolean
  userId?: string
  settings: any
  formDetectionEnabled: boolean
}

interface DetectedForm {
  type: string
  confidence: number
  fieldCount: number
  company?: string
  jobTitle?: string
}

const Popup: React.FC = () => {
  const [state, setState] = useState<ExtensionState | null>(null)
  const [detectedForms, setDetectedForms] = useState<DetectedForm[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isProcessing, setIsProcessing] = useState(false)
  const [currentTab, setCurrentTab] = useState<'main' | 'settings' | 'analytics'>('main')

  useEffect(() => {
    initializePopup()
  }, [])

  const initializePopup = async () => {
    try {
      // Get extension state
      const stateResponse = await browser.runtime.sendMessage({ type: 'GET_STATE' })
      if (stateResponse.success) {
        setState(stateResponse.data)
      }

      // Get detected forms from current tab
      const tabs = await browser.tabs.query({ active: true, currentWindow: true })
      if (tabs[0]?.id) {
        try {
          const formsResponse = await browser.tabs.sendMessage(tabs[0].id, {
            type: 'GET_DETECTED_FORMS'
          })
          if (formsResponse.success) {
            setDetectedForms(formsResponse.data)
          }
        } catch (error) {
          // Content script might not be injected
          console.log('Content script not available')
        }
      }
    } catch (error) {
      console.error('Failed to initialize popup:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleAuthenticate = async () => {
    try {
      // Open CareerCraft authentication page
      const authUrl = 'https://careercraft.onlinejobsearchhelp.com/extension/auth'
      await browser.tabs.create({ url: authUrl })
      window.close()
    } catch (error) {
      console.error('Authentication failed:', error)
    }
  }

  const handleLogout = async () => {
    try {
      await browser.storage.local.remove(['authToken', 'userProfile'])
      setState(prev => prev ? { ...prev, isAuthenticated: false, userId: undefined } : null)
    } catch (error) {
      console.error('Logout failed:', error)
    }
  }

  const handleQuickFill = async () => {
    if (detectedForms.length === 0) return

    setIsProcessing(true)
    try {
      const tabs = await browser.tabs.query({ active: true, currentWindow: true })
      if (tabs[0]?.id) {
        const response = await browser.tabs.sendMessage(tabs[0].id, {
          type: 'QUICK_FILL'
        })
        
        if (response.success) {
          // Show success and close popup
          setTimeout(() => window.close(), 1000)
        }
      }
    } catch (error) {
      console.error('Quick fill failed:', error)
    } finally {
      setIsProcessing(false)
    }
  }

  const handleCustomFill = async () => {
    if (detectedForms.length === 0) return

    setIsProcessing(true)
    try {
      const tabs = await browser.tabs.query({ active: true, currentWindow: true })
      if (tabs[0]?.id) {
        const response = await browser.tabs.sendMessage(tabs[0].id, {
          type: 'FILL_FORM',
          data: { formIndex: 0, customization: true }
        })
        
        if (response.success) {
          setTimeout(() => window.close(), 1000)
        }
      }
    } catch (error) {
      console.error('Custom fill failed:', error)
    } finally {
      setIsProcessing(false)
    }
  }

  const toggleFormDetection = async () => {
    if (!state) return

    const newEnabled = !state.formDetectionEnabled
    await browser.runtime.sendMessage({
      type: 'UPDATE_SETTINGS',
      data: { formDetectionEnabled: newEnabled }
    })

    setState(prev => prev ? { ...prev, formDetectionEnabled: newEnabled } : null)
  }

  const openSettings = () => {
    browser.runtime.openOptionsPage()
    window.close()
  }

  if (isLoading) {
    return (
      <div className="w-80 h-96 flex items-center justify-center bg-white">
        <Loader2 className="w-8 h-8 animate-spin text-blue-600" />
      </div>
    )
  }

  if (!state) {
    return (
      <div className="w-80 h-96 flex items-center justify-center bg-white">
        <div className="text-center">
          <AlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <p className="text-gray-600">Failed to load extension state</p>
        </div>
      </div>
    )
  }

  return (
    <div className="w-80 bg-white shadow-lg">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Target className="w-6 h-6" />
            <h1 className="text-lg font-semibold">CareerCraft</h1>
          </div>
          <div className="flex items-center space-x-2">
            {state.isAuthenticated ? (
              <button
                onClick={handleLogout}
                className="p-1 hover:bg-white/20 rounded"
                title="Logout"
              >
                <LogOut className="w-4 h-4" />
              </button>
            ) : (
              <button
                onClick={handleAuthenticate}
                className="p-1 hover:bg-white/20 rounded"
                title="Login"
              >
                <LogIn className="w-4 h-4" />
              </button>
            )}
            <button
              onClick={openSettings}
              className="p-1 hover:bg-white/20 rounded"
              title="Settings"
            >
              <Settings className="w-4 h-4" />
            </button>
          </div>
        </div>
      </div>

      {/* Authentication Required */}
      {!state.isAuthenticated && (
        <div className="p-4 text-center">
          <User className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h2 className="text-lg font-semibold text-gray-800 mb-2">
            Sign In Required
          </h2>
          <p className="text-gray-600 mb-4">
            Connect your CareerCraft account to start autofilling job applications.
          </p>
          <button
            onClick={handleAuthenticate}
            className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors"
          >
            Sign In to CareerCraft
          </button>
        </div>
      )}

      {/* Main Content */}
      {state.isAuthenticated && (
        <div className="p-4">
          {/* Form Detection Status */}
          <div className="mb-4">
            <div className="flex items-center justify-between mb-2">
              <h3 className="font-semibold text-gray-800">Form Detection</h3>
              <button
                onClick={toggleFormDetection}
                className={`w-12 h-6 rounded-full transition-colors ${
                  state.formDetectionEnabled ? 'bg-green-500' : 'bg-gray-300'
                }`}
              >
                <div
                  className={`w-5 h-5 bg-white rounded-full transition-transform ${
                    state.formDetectionEnabled ? 'translate-x-6' : 'translate-x-0.5'
                  }`}
                />
              </button>
            </div>
            
            {detectedForms.length > 0 ? (
              <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                <div className="flex items-center space-x-2 mb-2">
                  <CheckCircle className="w-5 h-5 text-green-600" />
                  <span className="text-green-800 font-medium">
                    {detectedForms.length} Form{detectedForms.length > 1 ? 's' : ''} Detected
                  </span>
                </div>
                {detectedForms[0] && (
                  <div className="text-sm text-green-700">
                    <div>Type: {detectedForms[0].type}</div>
                    <div>Confidence: {Math.round(detectedForms[0].confidence * 100)}%</div>
                    <div>Fields: {detectedForms[0].fieldCount}</div>
                    {detectedForms[0].company && (
                      <div>Company: {detectedForms[0].company}</div>
                    )}
                    {detectedForms[0].jobTitle && (
                      <div>Position: {detectedForms[0].jobTitle}</div>
                    )}
                  </div>
                )}
              </div>
            ) : (
              <div className="bg-gray-50 border border-gray-200 rounded-lg p-3">
                <div className="flex items-center space-x-2">
                  <AlertCircle className="w-5 h-5 text-gray-500" />
                  <span className="text-gray-600">No job application forms detected</span>
                </div>
              </div>
            )}
          </div>

          {/* Action Buttons */}
          {detectedForms.length > 0 && (
            <div className="space-y-3 mb-4">
              <button
                onClick={handleQuickFill}
                disabled={isProcessing}
                className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center justify-center space-x-2"
              >
                {isProcessing ? (
                  <Loader2 className="w-5 h-5 animate-spin" />
                ) : (
                  <Zap className="w-5 h-5" />
                )}
                <span>Quick Fill</span>
              </button>

              <button
                onClick={handleCustomFill}
                disabled={isProcessing}
                className="w-full bg-purple-600 text-white py-3 px-4 rounded-lg hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center justify-center space-x-2"
              >
                {isProcessing ? (
                  <Loader2 className="w-5 h-5 animate-spin" />
                ) : (
                  <Target className="w-5 h-5" />
                )}
                <span>Custom Fill</span>
              </button>
            </div>
          )}

          {/* Quick Stats */}
          <div className="border-t pt-4">
            <h4 className="font-semibold text-gray-800 mb-2">Quick Stats</h4>
            <div className="grid grid-cols-2 gap-3 text-sm">
              <div className="bg-blue-50 p-2 rounded">
                <div className="text-blue-600 font-medium">Applications</div>
                <div className="text-blue-800">12 this week</div>
              </div>
              <div className="bg-green-50 p-2 rounded">
                <div className="text-green-600 font-medium">Success Rate</div>
                <div className="text-green-800">94%</div>
              </div>
            </div>
          </div>

          {/* Footer Links */}
          <div className="border-t pt-4 mt-4">
            <div className="flex justify-between text-sm">
              <button
                onClick={() => browser.tabs.create({ url: 'https://careercraft.onlinejobsearchhelp.com/dashboard' })}
                className="text-blue-600 hover:text-blue-800"
              >
                Open Dashboard
              </button>
              <button
                onClick={() => browser.tabs.create({ url: 'https://careercraft.onlinejobsearchhelp.com/help' })}
                className="text-gray-600 hover:text-gray-800"
              >
                Help & Support
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default Popup
