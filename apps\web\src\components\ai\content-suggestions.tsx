'use client';

import { useState } from 'react';
import { AIContentResponse, ContentSuggestion } from '@careercraft/shared/types/ai';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Icons } from '@/components/ui/icons';
import { Progress } from '@/components/ui/progress';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';

interface ContentSuggestionsProps {
  response: AIContentResponse;
  onUseContent: (content: string) => void;
  className?: string;
}

export function ContentSuggestions({ response, onUseContent, className }: ContentSuggestionsProps) {
  const [selectedSuggestion, setSelectedSuggestion] = useState<string | null>(null);
  const [editedContent, setEditedContent] = useState<string>('');
  const [expandedSuggestions, setExpandedSuggestions] = useState<Set<string>>(new Set());

  const handleSelectSuggestion = (suggestion: ContentSuggestion) => {
    setSelectedSuggestion(suggestion.id);
    setEditedContent(suggestion.content);
  };

  const handleUseContent = () => {
    if (editedContent.trim()) {
      onUseContent(editedContent);
      setSelectedSuggestion(null);
      setEditedContent('');
    } else {
      toast.error('Please select or edit content before using it');
    }
  };

  const handleCopyContent = (content: string) => {
    navigator.clipboard.writeText(content);
    toast.success('Content copied to clipboard');
  };

  const toggleSuggestionExpanded = (suggestionId: string) => {
    const newExpanded = new Set(expandedSuggestions);
    if (newExpanded.has(suggestionId)) {
      newExpanded.delete(suggestionId);
    } else {
      newExpanded.add(suggestionId);
    }
    setExpandedSuggestions(newExpanded);
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return 'text-green-600';
    if (confidence >= 0.6) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getATSScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  return (
    <div className={cn('space-y-6', className)}>
      {/* Generation Metadata */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Icons.barChart className="h-5 w-5" />
            Generation Results
          </CardTitle>
          <CardDescription>
            Generated {response.suggestions.length} suggestions in {response.metadata.processingTime}ms
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Overall Confidence</span>
                <span className={cn('text-sm font-bold', getConfidenceColor(response.metadata.confidence))}>
                  {Math.round(response.metadata.confidence * 100)}%
                </span>
              </div>
              <Progress value={response.metadata.confidence * 100} className="h-2" />
            </div>
            
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">ATS Score</span>
                <span className={cn('text-sm font-bold', getATSScoreColor(response.metadata.atsOptimization.score))}>
                  {response.metadata.atsOptimization.score}/100
                </span>
              </div>
              <Progress value={response.metadata.atsOptimization.score} className="h-2" />
            </div>
            
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Readability</span>
                <span className={cn('text-sm font-bold', getATSScoreColor(response.metadata.atsOptimization.readabilityScore))}>
                  {response.metadata.atsOptimization.readabilityScore}/100
                </span>
              </div>
              <Progress value={response.metadata.atsOptimization.readabilityScore} className="h-2" />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Content Suggestions */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Content Suggestions</h3>
        
        {response.suggestions.map((suggestion, index) => (
          <Card 
            key={suggestion.id}
            className={cn(
              'cursor-pointer transition-all duration-200',
              selectedSuggestion === suggestion.id && 'ring-2 ring-primary'
            )}
          >
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-base">
                  Suggestion {index + 1}
                </CardTitle>
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className={getConfidenceColor(suggestion.confidence)}>
                    {Math.round(suggestion.confidence * 100)}% confidence
                  </Badge>
                  <Badge variant="outline" className={getATSScoreColor(suggestion.atsScore)}>
                    ATS: {suggestion.atsScore}/100
                  </Badge>
                </div>
              </div>
            </CardHeader>
            
            <CardContent className="space-y-4">
              {/* Content */}
              <div 
                className="p-4 bg-muted rounded-lg cursor-pointer hover:bg-muted/80 transition-colors"
                onClick={() => handleSelectSuggestion(suggestion)}
              >
                <p className="text-sm leading-relaxed">{suggestion.content}</p>
              </div>

              {/* Keywords */}
              {suggestion.keywords.length > 0 && (
                <div>
                  <h4 className="text-sm font-medium mb-2">Keywords Used:</h4>
                  <div className="flex flex-wrap gap-1">
                    {suggestion.keywords.map((keyword, keywordIndex) => (
                      <Badge key={keywordIndex} variant="secondary" className="text-xs">
                        {keyword}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}

              {/* Reasoning and Details */}
              <Collapsible>
                <CollapsibleTrigger
                  className="flex items-center gap-2 text-sm text-muted-foreground hover:text-foreground transition-colors"
                  onClick={() => toggleSuggestionExpanded(suggestion.id)}
                >
                  <Icons.chevronDown 
                    className={cn(
                      'h-4 w-4 transition-transform',
                      expandedSuggestions.has(suggestion.id) && 'rotate-180'
                    )} 
                  />
                  View details and reasoning
                </CollapsibleTrigger>
                <CollapsibleContent className="mt-3 space-y-3">
                  <div>
                    <h4 className="text-sm font-medium mb-1">AI Reasoning:</h4>
                    <p className="text-sm text-muted-foreground">{suggestion.reasoning}</p>
                  </div>
                  
                  {suggestion.improvements.length > 0 && (
                    <div>
                      <h4 className="text-sm font-medium mb-1">Suggested Improvements:</h4>
                      <ul className="text-sm text-muted-foreground space-y-1">
                        {suggestion.improvements.map((improvement, improvementIndex) => (
                          <li key={improvementIndex} className="flex items-start gap-2">
                            <Icons.lightBulb className="h-3 w-3 mt-0.5 flex-shrink-0" />
                            {improvement}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                  
                  {suggestion.alternatives.length > 0 && (
                    <div>
                      <h4 className="text-sm font-medium mb-1">Alternative Phrases:</h4>
                      <div className="flex flex-wrap gap-1">
                        {suggestion.alternatives.map((alternative, altIndex) => (
                          <Badge key={altIndex} variant="outline" className="text-xs">
                            {alternative}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                </CollapsibleContent>
              </Collapsible>

              {/* Actions */}
              <div className="flex gap-2 pt-2">
                <Button
                  size="sm"
                  onClick={() => handleSelectSuggestion(suggestion)}
                  variant={selectedSuggestion === suggestion.id ? 'default' : 'outline'}
                >
                  <Icons.edit className="h-4 w-4 mr-1" />
                  {selectedSuggestion === suggestion.id ? 'Selected' : 'Edit & Use'}
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => onUseContent(suggestion.content)}
                >
                  <Icons.check className="h-4 w-4 mr-1" />
                  Use As-Is
                </Button>
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => handleCopyContent(suggestion.content)}
                >
                  <Icons.copy className="h-4 w-4 mr-1" />
                  Copy
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Content Editor */}
      {selectedSuggestion && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Icons.edit className="h-5 w-5" />
              Edit Content
            </CardTitle>
            <CardDescription>
              Make any adjustments to the selected content before using it
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Textarea
              value={editedContent}
              onChange={(e) => setEditedContent(e.target.value)}
              rows={6}
              placeholder="Edit the content here..."
            />
            
            <div className="flex gap-2">
              <Button onClick={handleUseContent}>
                <Icons.check className="h-4 w-4 mr-2" />
                Use This Content
              </Button>
              <Button 
                variant="outline" 
                onClick={() => {
                  setSelectedSuggestion(null);
                  setEditedContent('');
                }}
              >
                Cancel
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
