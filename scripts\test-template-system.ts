#!/usr/bin/env tsx

/**
 * Template System Testing Script
 * 
 * This script performs comprehensive testing of the template system:
 * 1. Template data models and validation
 * 2. Template rendering and customization
 * 3. PDF export functionality
 * 4. Template marketplace features
 * 5. Performance and optimization
 * 6. API endpoints and data persistence
 */

import { config } from 'dotenv';
import { join } from 'path';
import { 
  templateSchema,
  templateStyleSchema,
  templateLayoutSchema,
  exportOptionsSchema,
} from '@careercraft/shared/schemas/template';
import {
  Template,
  TemplateStyle,
  TemplateLayout,
  ExportOptions,
  TemplateCategory,
} from '@careercraft/shared/types/template';
import { defaultTemplates } from '@careercraft/shared/data/default-templates';

// Load environment variables
config({ path: join(__dirname, '../.env.local') });

interface TestResult {
  name: string;
  status: 'PASS' | 'FAIL' | 'SKIP';
  duration: number;
  error?: string;
  details?: any;
}

class TemplateSystemTester {
  private results: TestResult[] = [];

  private async runTest(name: string, testFn: () => Promise<any>): Promise<void> {
    const startTime = Date.now();
    console.log(`🎨 Running: ${name}`);

    try {
      const result = await testFn();
      const duration = Date.now() - startTime;
      
      this.results.push({
        name,
        status: 'PASS',
        duration,
        details: result,
      });
      
      console.log(`✅ PASS: ${name} (${duration}ms)`);
    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      
      this.results.push({
        name,
        status: 'FAIL',
        duration,
        error: errorMessage,
      });
      
      console.log(`❌ FAIL: ${name} (${duration}ms) - ${errorMessage}`);
    }
  }

  async testTemplateDataModels(): Promise<void> {
    await this.runTest('Template Data Models', async () => {
      // Test TemplateStyle interface
      const templateStyle: TemplateStyle = {
        fontFamily: 'Inter, sans-serif',
        fontSize: {
          heading1: 32,
          heading2: 20,
          heading3: 16,
          body: 14,
          small: 12,
        },
        fontWeight: {
          normal: 400,
          medium: 500,
          semibold: 600,
          bold: 700,
        },
        lineHeight: {
          tight: 1.2,
          normal: 1.5,
          relaxed: 1.7,
        },
        colors: {
          primary: '#2563eb',
          secondary: '#64748b',
          accent: '#0ea5e9',
          text: '#1e293b',
          textLight: '#64748b',
          background: '#ffffff',
          border: '#e2e8f0',
          divider: '#cbd5e1',
        },
        spacing: {
          xs: 4,
          sm: 8,
          md: 16,
          lg: 24,
          xl: 32,
          xxl: 48,
        },
        layout: {
          pageMargin: {
            top: 40,
            right: 40,
            bottom: 40,
            left: 40,
          },
          sectionSpacing: 32,
          itemSpacing: 16,
          columnGap: 24,
        },
        components: {
          header: {
            alignment: 'left',
            showDivider: true,
          },
          section: {
            titleStyle: 'underline',
            titleCase: 'normal',
            spacing: 'normal',
          },
          list: {
            bulletStyle: 'bullet',
            indentation: 16,
          },
          contact: {
            layout: 'horizontal',
            showIcons: true,
            separator: '•',
          },
        },
      };

      // Test TemplateLayout interface
      const templateLayout: TemplateLayout = {
        type: 'single-column',
        columns: [
          {
            id: 'main',
            width: 100,
            sections: ['personal_info', 'work_experience', 'education'],
            order: 0,
          },
        ],
        header: {
          height: 120,
          sections: ['personal_info'],
          style: 'minimal',
        },
      };

      return {
        templateStyleValid: !!templateStyle.fontFamily,
        templateLayoutValid: !!templateLayout.type,
        colorsCount: Object.keys(templateStyle.colors).length,
        spacingLevels: Object.keys(templateStyle.spacing).length,
      };
    });
  }

  async testTemplateValidation(): Promise<void> {
    await this.runTest('Template Validation Schemas', async () => {
      // Test valid template style
      const validStyle = {
        fontFamily: 'Inter, sans-serif',
        fontSize: {
          heading1: 32,
          heading2: 20,
          heading3: 16,
          body: 14,
          small: 12,
        },
        fontWeight: {
          normal: 400,
          medium: 500,
          semibold: 600,
          bold: 700,
        },
        lineHeight: {
          tight: 1.2,
          normal: 1.5,
          relaxed: 1.7,
        },
        colors: {
          primary: '#2563eb',
          secondary: '#64748b',
          accent: '#0ea5e9',
          text: '#1e293b',
          textLight: '#64748b',
          background: '#ffffff',
          border: '#e2e8f0',
          divider: '#cbd5e1',
        },
        spacing: {
          xs: 4,
          sm: 8,
          md: 16,
          lg: 24,
          xl: 32,
          xxl: 48,
        },
        layout: {
          pageMargin: {
            top: 40,
            right: 40,
            bottom: 40,
            left: 40,
          },
          sectionSpacing: 32,
          itemSpacing: 16,
          columnGap: 24,
        },
        components: {
          header: {
            alignment: 'left' as const,
            showDivider: true,
          },
          section: {
            titleStyle: 'underline' as const,
            titleCase: 'normal' as const,
            spacing: 'normal' as const,
          },
          list: {
            bulletStyle: 'bullet' as const,
            indentation: 16,
          },
          contact: {
            layout: 'horizontal' as const,
            showIcons: true,
            separator: '•',
          },
        },
      };

      const styleResult = templateStyleSchema.safeParse(validStyle);
      if (!styleResult.success) {
        throw new Error(`Template style validation failed: ${styleResult.error.message}`);
      }

      // Test invalid style (invalid color format)
      const invalidStyle = {
        ...validStyle,
        colors: {
          ...validStyle.colors,
          primary: 'invalid-color',
        },
      };

      const invalidStyleResult = templateStyleSchema.safeParse(invalidStyle);
      if (invalidStyleResult.success) {
        throw new Error('Invalid template style should fail validation');
      }

      // Test valid layout
      const validLayout = {
        type: 'single-column' as const,
        columns: [
          {
            id: 'main',
            width: 100,
            sections: ['personal_info', 'work_experience'],
            order: 0,
          },
        ],
        header: {
          height: 120,
          sections: ['personal_info'],
          style: 'minimal' as const,
        },
      };

      const layoutResult = templateLayoutSchema.safeParse(validLayout);
      if (!layoutResult.success) {
        throw new Error(`Template layout validation failed: ${layoutResult.error.message}`);
      }

      return {
        styleValidationPassed: true,
        layoutValidationPassed: true,
        invalidDataRejected: true,
      };
    });
  }

  async testDefaultTemplates(): Promise<void> {
    await this.runTest('Default Templates Data', async () => {
      if (defaultTemplates.length === 0) {
        throw new Error('No default templates found');
      }

      // Test each default template
      for (const template of defaultTemplates) {
        // Validate template structure
        if (!template.name || !template.description) {
          throw new Error(`Invalid template: ${template.name || 'unnamed'}`);
        }

        if (!Object.values(TemplateCategory).includes(template.category)) {
          throw new Error(`Invalid category for template: ${template.name}`);
        }

        // Validate style
        const styleResult = templateStyleSchema.safeParse(template.style);
        if (!styleResult.success) {
          throw new Error(`Invalid style for template ${template.name}: ${styleResult.error.message}`);
        }

        // Validate layout
        const layoutResult = templateLayoutSchema.safeParse(template.layout);
        if (!layoutResult.success) {
          throw new Error(`Invalid layout for template ${template.name}: ${layoutResult.error.message}`);
        }
      }

      const categories = [...new Set(defaultTemplates.map(t => t.category))];
      const premiumTemplates = defaultTemplates.filter(t => t.isPremium);
      const popularTemplates = defaultTemplates.filter(t => t.isPopular);

      return {
        totalTemplates: defaultTemplates.length,
        categoriesCount: categories.length,
        premiumCount: premiumTemplates.length,
        popularCount: popularTemplates.length,
        allTemplatesValid: true,
      };
    });
  }

  async testExportOptions(): Promise<void> {
    await this.runTest('Export Options Validation', async () => {
      // Test valid export options
      const validOptions: ExportOptions = {
        format: 'pdf',
        quality: 'high',
        pageSize: 'a4',
        orientation: 'portrait',
        margins: {
          top: 20,
          right: 20,
          bottom: 20,
          left: 20,
        },
        includeMetadata: true,
        watermark: {
          text: 'CareerCraft',
          opacity: 0.1,
          position: 'center',
        },
      };

      const validResult = exportOptionsSchema.safeParse(validOptions);
      if (!validResult.success) {
        throw new Error(`Export options validation failed: ${validResult.error.message}`);
      }

      // Test invalid options
      const invalidOptions = {
        ...validOptions,
        format: 'invalid-format',
      };

      const invalidResult = exportOptionsSchema.safeParse(invalidOptions);
      if (invalidResult.success) {
        throw new Error('Invalid export options should fail validation');
      }

      // Test different formats
      const formats = ['pdf', 'docx', 'html', 'png', 'jpg'];
      for (const format of formats) {
        const formatOptions = { ...validOptions, format };
        const formatResult = exportOptionsSchema.safeParse(formatOptions);
        if (!formatResult.success) {
          throw new Error(`Format ${format} should be valid`);
        }
      }

      return {
        exportOptionsValidationPassed: true,
        supportedFormats: formats.length,
        watermarkSupported: true,
      };
    });
  }

  async testTemplateCategories(): Promise<void> {
    await this.runTest('Template Categories', async () => {
      const categories = Object.values(TemplateCategory);
      
      if (categories.length === 0) {
        throw new Error('No template categories defined');
      }

      const expectedCategories = [
        TemplateCategory.MODERN,
        TemplateCategory.CLASSIC,
        TemplateCategory.MINIMAL,
        TemplateCategory.CREATIVE,
        TemplateCategory.PROFESSIONAL,
      ];

      for (const expectedCategory of expectedCategories) {
        if (!categories.includes(expectedCategory)) {
          throw new Error(`Expected category missing: ${expectedCategory}`);
        }
      }

      // Test that default templates cover main categories
      const defaultTemplateCategories = [...new Set(defaultTemplates.map(t => t.category))];
      const mainCategoriesCovered = expectedCategories.every(cat => 
        defaultTemplateCategories.includes(cat)
      );

      if (!mainCategoriesCovered) {
        throw new Error('Default templates do not cover all main categories');
      }

      return {
        totalCategories: categories.length,
        expectedCategoriesPresent: expectedCategories.length,
        defaultTemplatesCoverMainCategories: true,
      };
    });
  }

  async testTemplateCustomization(): Promise<void> {
    await this.runTest('Template Customization Features', async () => {
      const template = defaultTemplates[0];
      
      // Test customization options
      const customizationOptions = template.customizable;
      
      if (!customizationOptions) {
        throw new Error('Template should have customization options');
      }

      const expectedOptions = ['colors', 'fonts', 'layout', 'spacing'];
      for (const option of expectedOptions) {
        if (!(option in customizationOptions)) {
          throw new Error(`Customization option missing: ${option}`);
        }
      }

      // Test style customization
      const originalStyle = template.style;
      const customStyle = {
        ...originalStyle,
        colors: {
          ...originalStyle.colors,
          primary: '#ff6b6b',
          secondary: '#4ecdc4',
        },
      };

      const customStyleResult = templateStyleSchema.safeParse(customStyle);
      if (!customStyleResult.success) {
        throw new Error('Custom style should be valid');
      }

      return {
        customizationOptionsPresent: true,
        colorCustomizationSupported: customizationOptions.colors,
        fontCustomizationSupported: customizationOptions.fonts,
        layoutCustomizationSupported: customizationOptions.layout,
        spacingCustomizationSupported: customizationOptions.spacing,
      };
    });
  }

  async testComponentStructure(): Promise<void> {
    await this.runTest('Template Component Structure', async () => {
      const fs = require('fs');
      const path = require('path');

      const templateComponentsDir = join(__dirname, '../apps/web/src/components/templates');
      const requiredComponents = [
        'template-renderer.tsx',
        'template-selector.tsx',
        'templates/modern-template.tsx',
      ];

      const missingComponents = [];
      for (const component of requiredComponents) {
        const componentPath = join(templateComponentsDir, component);
        if (!fs.existsSync(componentPath)) {
          missingComponents.push(component);
        }
      }

      if (missingComponents.length > 0) {
        throw new Error(`Missing template components: ${missingComponents.join(', ')}`);
      }

      // Check export components
      const exportComponentsDir = join(__dirname, '../apps/web/src/components/export');
      const requiredExportComponents = [
        'pdf-export.tsx',
      ];

      const missingExportComponents = [];
      for (const component of requiredExportComponents) {
        const componentPath = join(exportComponentsDir, component);
        if (!fs.existsSync(componentPath)) {
          missingExportComponents.push(component);
        }
      }

      if (missingExportComponents.length > 0) {
        throw new Error(`Missing export components: ${missingExportComponents.join(', ')}`);
      }

      return {
        totalTemplateComponents: requiredComponents.length,
        totalExportComponents: requiredExportComponents.length,
        allComponentsPresent: true,
      };
    });
  }

  async testAPIEndpoints(): Promise<void> {
    await this.runTest('Template API Endpoints', async () => {
      const fs = require('fs');
      const path = require('path');

      const apiDir = join(__dirname, '../apps/web/src/app/api');
      const requiredEndpoints = [
        'templates/route.ts',
        'export/route.ts',
        'export/[id]/route.ts',
      ];

      const missingEndpoints = [];
      for (const endpoint of requiredEndpoints) {
        const endpointPath = join(apiDir, endpoint);
        if (!fs.existsSync(endpointPath)) {
          missingEndpoints.push(endpoint);
        }
      }

      if (missingEndpoints.length > 0) {
        throw new Error(`Missing API endpoints: ${missingEndpoints.join(', ')}`);
      }

      // Check if endpoints have proper HTTP methods
      const templatesFile = join(apiDir, 'templates/route.ts');
      const templatesContent = fs.readFileSync(templatesFile, 'utf8');

      const requiredMethods = ['GET', 'POST'];
      const missingMethods = [];

      for (const method of requiredMethods) {
        if (!templatesContent.includes(`export async function ${method}`)) {
          missingMethods.push(method);
        }
      }

      if (missingMethods.length > 0) {
        throw new Error(`Missing HTTP methods in templates/route.ts: ${missingMethods.join(', ')}`);
      }

      return {
        totalEndpoints: requiredEndpoints.length,
        totalMethods: requiredMethods.length,
        allEndpointsPresent: true,
        allMethodsPresent: true,
      };
    });
  }

  async runAllTests(): Promise<void> {
    console.log('🎨 Starting Template System Tests...\n');

    await this.testTemplateDataModels();
    await this.testTemplateValidation();
    await this.testDefaultTemplates();
    await this.testExportOptions();
    await this.testTemplateCategories();
    await this.testTemplateCustomization();
    await this.testComponentStructure();
    await this.testAPIEndpoints();

    await this.printSummary();
  }

  private async printSummary(): Promise<void> {
    console.log('\n📊 Template System Test Summary');
    console.log('================================');

    const passed = this.results.filter(r => r.status === 'PASS').length;
    const failed = this.results.filter(r => r.status === 'FAIL').length;
    const total = this.results.length;

    console.log(`Total Tests: ${total}`);
    console.log(`Passed: ${passed}`);
    console.log(`Failed: ${failed}`);
    console.log(`Success Rate: ${((passed / total) * 100).toFixed(1)}%`);

    if (failed > 0) {
      console.log('\n❌ Failed Tests:');
      this.results
        .filter(r => r.status === 'FAIL')
        .forEach(r => {
          console.log(`  - ${r.name}: ${r.error}`);
        });
    }

    const totalTime = this.results.reduce((sum, r) => sum + r.duration, 0);
    console.log(`\nTotal Execution Time: ${totalTime}ms`);

    if (failed === 0) {
      console.log('\n🎉 All template system tests passed! System is ready for production.');
    } else {
      console.log('\n⚠️  Some tests failed. Please check your template system implementation.');
      process.exit(1);
    }
  }
}

// Run the tests
async function main() {
  const tester = new TemplateSystemTester();
  await tester.runAllTests();
}

if (require.main === module) {
  main().catch(console.error);
}
