/**
 * Job Matching API - Applications
 * 
 * Handles job application management and tracking
 */

import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { jobMatchingService, JobApplicationDataSchema } from '@/lib/job-matching/service'
import { z } from 'zod'

// Request schemas
const UpdateApplicationSchema = z.object({
  applicationId: z.string(),
  status: z.enum(['applied', 'screening', 'interview', 'offer', 'rejected', 'withdrawn']).optional(),
  notes: z.string().optional(),
  interviewDates: z.array(z.object({
    type: z.string(),
    date: z.string(),
    location: z.string().optional(),
    interviewer: z.string().optional()
  })).optional(),
  followUpDate: z.string().optional(),
  salaryOffered: z.number().optional(),
  metadata: z.record(z.any()).optional()
})

const GetApplicationsSchema = z.object({
  status: z.string().optional(),
  limit: z.number().default(50),
  offset: z.number().default(0)
})

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const action = body.action

    if (action === 'apply') {
      const applicationData = {
        ...body,
        userId: session.user.id
      }

      const application = await jobMatchingService.applyToJob(applicationData)

      return NextResponse.json({
        success: true,
        application,
        message: 'Application submitted successfully'
      })
    }

    if (action === 'bulk-apply') {
      const { jobIds, resumeId, coverLetterId, notes } = body

      if (!Array.isArray(jobIds) || jobIds.length === 0) {
        return NextResponse.json(
          { error: 'Job IDs array required' },
          { status: 400 }
        )
      }

      const applications = []
      const errors = []

      for (const jobId of jobIds) {
        try {
          const application = await jobMatchingService.applyToJob({
            userId: session.user.id,
            jobPostingId: jobId,
            resumeId,
            coverLetterId,
            notes
          })
          applications.push(application)
        } catch (error) {
          errors.push({
            jobId,
            error: error instanceof Error ? error.message : 'Unknown error'
          })
        }
      }

      return NextResponse.json({
        success: true,
        applications,
        errors,
        message: `Applied to ${applications.length} jobs successfully`
      })
    }

    return NextResponse.json(
      { error: 'Invalid action' },
      { status: 400 }
    )
  } catch (error) {
    console.error('Job application error:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const action = searchParams.get('action')

    if (action === 'list' || !action) {
      const status = searchParams.get('status') || undefined
      const limit = parseInt(searchParams.get('limit') || '50')
      const offset = parseInt(searchParams.get('offset') || '0')

      const applications = await jobMatchingService.getApplications(session.user.id, {
        status,
        limit
      })

      return NextResponse.json({
        success: true,
        applications,
        total: applications.length,
        hasMore: applications.length === limit
      })
    }

    if (action === 'stats') {
      const allApplications = await jobMatchingService.getApplications(session.user.id)

      const stats = {
        total: allApplications.length,
        applied: allApplications.filter(app => app.status === 'applied').length,
        screening: allApplications.filter(app => app.status === 'screening').length,
        interview: allApplications.filter(app => app.status === 'interview').length,
        offer: allApplications.filter(app => app.status === 'offer').length,
        rejected: allApplications.filter(app => app.status === 'rejected').length,
        withdrawn: allApplications.filter(app => app.status === 'withdrawn').length,
        responseRate: 0,
        averageResponseTime: 0
      }

      // Calculate response rate
      const responded = stats.screening + stats.interview + stats.offer + stats.rejected
      stats.responseRate = stats.total > 0 ? Math.round((responded / stats.total) * 100) : 0

      // Calculate average response time (simplified)
      stats.averageResponseTime = 7 // days

      return NextResponse.json({
        success: true,
        stats
      })
    }

    if (action === 'timeline') {
      const applications = await jobMatchingService.getApplications(session.user.id, { limit: 100 })

      // Group applications by month
      const timeline = applications.reduce((acc: any, app) => {
        const month = app.appliedDate.toISOString().substring(0, 7) // YYYY-MM
        if (!acc[month]) {
          acc[month] = {
            month,
            applied: 0,
            responses: 0,
            offers: 0
          }
        }
        acc[month].applied++
        if (['screening', 'interview', 'offer', 'rejected'].includes(app.status)) {
          acc[month].responses++
        }
        if (app.status === 'offer') {
          acc[month].offers++
        }
        return acc
      }, {})

      return NextResponse.json({
        success: true,
        timeline: Object.values(timeline)
      })
    }

    return NextResponse.json(
      { error: 'Invalid action' },
      { status: 400 }
    )
  } catch (error) {
    console.error('Get applications error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const action = body.action

    if (action === 'update-status') {
      const { applicationId, status } = UpdateApplicationSchema.parse(body)

      const success = await jobMatchingService.updateApplicationStatus(
        applicationId,
        status!,
        session.user.id
      )

      if (!success) {
        return NextResponse.json(
          { error: 'Application not found or access denied' },
          { status: 404 }
        )
      }

      return NextResponse.json({
        success: true,
        message: 'Application status updated successfully'
      })
    }

    if (action === 'update') {
      const { applicationId, ...updateData } = UpdateApplicationSchema.parse(body)

      // This would update the application with the provided data
      // For now, we'll just update the status if provided
      if (updateData.status) {
        const success = await jobMatchingService.updateApplicationStatus(
          applicationId,
          updateData.status,
          session.user.id
        )

        if (!success) {
          return NextResponse.json(
            { error: 'Application not found or access denied' },
            { status: 404 }
          )
        }
      }

      return NextResponse.json({
        success: true,
        message: 'Application updated successfully'
      })
    }

    if (action === 'add-note') {
      const { applicationId, note } = body

      if (!note || typeof note !== 'string') {
        return NextResponse.json(
          { error: 'Note content required' },
          { status: 400 }
        )
      }

      // This would add a note to the application
      // For now, we'll simulate success
      return NextResponse.json({
        success: true,
        message: 'Note added successfully'
      })
    }

    if (action === 'schedule-interview') {
      const { applicationId, interviewData } = body

      if (!interviewData || !interviewData.date) {
        return NextResponse.json(
          { error: 'Interview date required' },
          { status: 400 }
        )
      }

      // This would schedule an interview for the application
      // For now, we'll simulate success
      return NextResponse.json({
        success: true,
        message: 'Interview scheduled successfully'
      })
    }

    return NextResponse.json(
      { error: 'Invalid action' },
      { status: 400 }
    )
  } catch (error) {
    console.error('Update application error:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const applicationId = searchParams.get('applicationId')

    if (!applicationId) {
      return NextResponse.json(
        { error: 'Application ID required' },
        { status: 400 }
      )
    }

    // Update status to withdrawn instead of deleting
    const success = await jobMatchingService.updateApplicationStatus(
      applicationId,
      'withdrawn',
      session.user.id
    )

    if (!success) {
      return NextResponse.json(
        { error: 'Application not found or access denied' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'Application withdrawn successfully'
    })
  } catch (error) {
    console.error('Delete application error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
