/**
 * WebSocket Server for Real-time Collaboration
 * 
 * Handles WebSocket connections, message routing, and real-time communication
 * for collaborative resume editing.
 */

import { WebSocketServer, WebSocket } from 'ws'
import { IncomingMessage } from 'http'
import { parse } from 'url'
import { verify } from 'jsonwebtoken'
import { z } from 'zod'

// Message schemas
export const CollaborationMessageSchema = z.object({
  type: z.enum(['change', 'presence', 'comment', 'permission', 'cursor', 'join', 'leave']),
  sessionId: z.string(),
  userId: z.string(),
  timestamp: z.number(),
  data: z.any()
})

export const ChangeOperationSchema = z.object({
  type: z.enum(['insert', 'delete', 'retain', 'replace']),
  path: z.array(z.string()),
  value: z.any().optional(),
  length: z.number().optional(),
  attributes: z.record(z.any()).optional(),
  position: z.number().optional()
})

export const PresenceInfoSchema = z.object({
  userId: z.string(),
  userName: z.string(),
  userAvatar: z.string().optional(),
  status: z.enum(['active', 'idle', 'away']),
  lastSeen: z.number(),
  cursor: z.object({
    sectionPath: z.string(),
    position: z.number()
  }).optional()
})

export type CollaborationMessage = z.infer<typeof CollaborationMessageSchema>
export type ChangeOperation = z.infer<typeof ChangeOperationSchema>
export type PresenceInfo = z.infer<typeof PresenceInfoSchema>

interface AuthenticatedWebSocket extends WebSocket {
  userId: string
  sessionId: string
  userName: string
  userAvatar?: string
  lastActivity: number
}

interface CollaborationSession {
  id: string
  resumeId: string
  ownerId: string
  clients: Set<AuthenticatedWebSocket>
  presence: Map<string, PresenceInfo>
  lastActivity: number
}

export class CollaborationWebSocketServer {
  private wss: WebSocketServer
  private sessions: Map<string, CollaborationSession> = new Map()
  private userSessions: Map<string, Set<string>> = new Map()
  private heartbeatInterval: NodeJS.Timeout
  private cleanupInterval: NodeJS.Timeout

  constructor(port: number = 8080) {
    this.wss = new WebSocketServer({ 
      port,
      verifyClient: this.verifyClient.bind(this)
    })

    this.wss.on('connection', this.handleConnection.bind(this))
    
    // Start heartbeat and cleanup intervals
    this.heartbeatInterval = setInterval(this.sendHeartbeat.bind(this), 30000) // 30 seconds
    this.cleanupInterval = setInterval(this.cleanupInactiveSessions.bind(this), 300000) // 5 minutes

    console.log(`🤝 Collaboration WebSocket server started on port ${port}`)
  }

  private async verifyClient(info: { origin: string; secure: boolean; req: IncomingMessage }): Promise<boolean> {
    try {
      const url = parse(info.req.url || '', true)
      const token = url.query.token as string
      
      if (!token) {
        console.log('❌ WebSocket connection rejected: No token provided')
        return false
      }

      // Verify JWT token
      const decoded = verify(token, process.env.NEXTAUTH_SECRET || 'secret') as any
      
      if (!decoded.sub) {
        console.log('❌ WebSocket connection rejected: Invalid token')
        return false
      }

      return true
    } catch (error) {
      console.log('❌ WebSocket connection rejected: Token verification failed', error)
      return false
    }
  }

  private async handleConnection(ws: WebSocket, req: IncomingMessage) {
    try {
      const url = parse(req.url || '', true)
      const token = url.query.token as string
      const sessionId = url.query.sessionId as string

      if (!token || !sessionId) {
        ws.close(1008, 'Missing required parameters')
        return
      }

      // Verify and decode token
      const decoded = verify(token, process.env.NEXTAUTH_SECRET || 'secret') as any
      const userId = decoded.sub
      const userName = decoded.name || 'Unknown User'
      const userAvatar = decoded.picture

      // Enhance WebSocket with user info
      const authWs = ws as AuthenticatedWebSocket
      authWs.userId = userId
      authWs.sessionId = sessionId
      authWs.userName = userName
      authWs.userAvatar = userAvatar
      authWs.lastActivity = Date.now()

      // Add to session
      await this.addClientToSession(authWs, sessionId)

      // Set up message handling
      authWs.on('message', (data) => this.handleMessage(authWs, data))
      authWs.on('close', () => this.handleDisconnection(authWs))
      authWs.on('error', (error) => this.handleError(authWs, error))

      // Send welcome message
      this.sendToClient(authWs, {
        type: 'join',
        sessionId,
        userId,
        timestamp: Date.now(),
        data: { message: 'Connected to collaboration session' }
      })

      console.log(`✅ User ${userName} (${userId}) joined session ${sessionId}`)
    } catch (error) {
      console.error('❌ Error handling WebSocket connection:', error)
      ws.close(1011, 'Internal server error')
    }
  }

  private async addClientToSession(ws: AuthenticatedWebSocket, sessionId: string) {
    let session = this.sessions.get(sessionId)
    
    if (!session) {
      // Create new session
      session = {
        id: sessionId,
        resumeId: '', // Will be set from database
        ownerId: '', // Will be set from database
        clients: new Set(),
        presence: new Map(),
        lastActivity: Date.now()
      }
      this.sessions.set(sessionId, session)
    }

    // Add client to session
    session.clients.add(ws)
    session.lastActivity = Date.now()

    // Update user presence
    const presenceInfo: PresenceInfo = {
      userId: ws.userId,
      userName: ws.userName,
      userAvatar: ws.userAvatar,
      status: 'active',
      lastSeen: Date.now()
    }
    session.presence.set(ws.userId, presenceInfo)

    // Track user sessions
    if (!this.userSessions.has(ws.userId)) {
      this.userSessions.set(ws.userId, new Set())
    }
    this.userSessions.get(ws.userId)!.add(sessionId)

    // Broadcast user joined to other clients
    this.broadcastToSession(sessionId, {
      type: 'presence',
      sessionId,
      userId: ws.userId,
      timestamp: Date.now(),
      data: {
        action: 'joined',
        user: presenceInfo,
        activeUsers: Array.from(session.presence.values())
      }
    }, ws.userId)
  }

  private handleMessage(ws: AuthenticatedWebSocket, data: Buffer) {
    try {
      const message = JSON.parse(data.toString())
      const validatedMessage = CollaborationMessageSchema.parse(message)

      // Update last activity
      ws.lastActivity = Date.now()
      const session = this.sessions.get(ws.sessionId)
      if (session) {
        session.lastActivity = Date.now()
        
        // Update user presence
        const presence = session.presence.get(ws.userId)
        if (presence) {
          presence.lastSeen = Date.now()
          presence.status = 'active'
        }
      }

      // Route message based on type
      switch (validatedMessage.type) {
        case 'change':
          this.handleChangeMessage(ws, validatedMessage)
          break
        case 'presence':
          this.handlePresenceMessage(ws, validatedMessage)
          break
        case 'comment':
          this.handleCommentMessage(ws, validatedMessage)
          break
        case 'cursor':
          this.handleCursorMessage(ws, validatedMessage)
          break
        default:
          console.warn(`⚠️  Unknown message type: ${validatedMessage.type}`)
      }
    } catch (error) {
      console.error('❌ Error handling message:', error)
      this.sendToClient(ws, {
        type: 'error' as any,
        sessionId: ws.sessionId,
        userId: ws.userId,
        timestamp: Date.now(),
        data: { error: 'Invalid message format' }
      })
    }
  }

  private handleChangeMessage(ws: AuthenticatedWebSocket, message: CollaborationMessage) {
    // Validate change operation
    try {
      const operation = ChangeOperationSchema.parse(message.data.operation)
      
      // Broadcast change to all other clients in session
      this.broadcastToSession(ws.sessionId, {
        ...message,
        timestamp: Date.now(),
        data: {
          ...message.data,
          operation,
          author: {
            userId: ws.userId,
            userName: ws.userName,
            userAvatar: ws.userAvatar
          }
        }
      }, ws.userId)

      console.log(`📝 Change broadcast from ${ws.userName} in session ${ws.sessionId}`)
    } catch (error) {
      console.error('❌ Invalid change operation:', error)
    }
  }

  private handlePresenceMessage(ws: AuthenticatedWebSocket, message: CollaborationMessage) {
    const session = this.sessions.get(ws.sessionId)
    if (!session) return

    const presence = session.presence.get(ws.userId)
    if (presence) {
      // Update presence info
      Object.assign(presence, message.data, {
        lastSeen: Date.now()
      })

      // Broadcast presence update
      this.broadcastToSession(ws.sessionId, {
        ...message,
        timestamp: Date.now(),
        data: {
          action: 'updated',
          user: presence,
          activeUsers: Array.from(session.presence.values())
        }
      })
    }
  }

  private handleCommentMessage(ws: AuthenticatedWebSocket, message: CollaborationMessage) {
    // Add author info to comment
    const commentData = {
      ...message.data,
      author: {
        userId: ws.userId,
        userName: ws.userName,
        userAvatar: ws.userAvatar
      },
      timestamp: Date.now()
    }

    // Broadcast comment to all clients in session
    this.broadcastToSession(ws.sessionId, {
      ...message,
      timestamp: Date.now(),
      data: commentData
    })

    console.log(`💬 Comment from ${ws.userName} in session ${ws.sessionId}`)
  }

  private handleCursorMessage(ws: AuthenticatedWebSocket, message: CollaborationMessage) {
    const session = this.sessions.get(ws.sessionId)
    if (!session) return

    const presence = session.presence.get(ws.userId)
    if (presence) {
      presence.cursor = message.data.cursor
      presence.lastSeen = Date.now()

      // Broadcast cursor update to other clients
      this.broadcastToSession(ws.sessionId, {
        ...message,
        timestamp: Date.now()
      }, ws.userId)
    }
  }

  private handleDisconnection(ws: AuthenticatedWebSocket) {
    const session = this.sessions.get(ws.sessionId)
    if (session) {
      // Remove client from session
      session.clients.delete(ws)
      session.presence.delete(ws.userId)

      // Remove from user sessions tracking
      const userSessions = this.userSessions.get(ws.userId)
      if (userSessions) {
        userSessions.delete(ws.sessionId)
        if (userSessions.size === 0) {
          this.userSessions.delete(ws.userId)
        }
      }

      // Broadcast user left to remaining clients
      this.broadcastToSession(ws.sessionId, {
        type: 'presence',
        sessionId: ws.sessionId,
        userId: ws.userId,
        timestamp: Date.now(),
        data: {
          action: 'left',
          userId: ws.userId,
          activeUsers: Array.from(session.presence.values())
        }
      })

      // Clean up empty sessions
      if (session.clients.size === 0) {
        this.sessions.delete(ws.sessionId)
        console.log(`🧹 Cleaned up empty session ${ws.sessionId}`)
      }
    }

    console.log(`👋 User ${ws.userName} (${ws.userId}) left session ${ws.sessionId}`)
  }

  private handleError(ws: AuthenticatedWebSocket, error: Error) {
    console.error(`❌ WebSocket error for user ${ws.userId}:`, error)
  }

  private sendToClient(ws: AuthenticatedWebSocket, message: CollaborationMessage) {
    if (ws.readyState === WebSocket.OPEN) {
      ws.send(JSON.stringify(message))
    }
  }

  private broadcastToSession(sessionId: string, message: CollaborationMessage, excludeUserId?: string) {
    const session = this.sessions.get(sessionId)
    if (!session) return

    session.clients.forEach(client => {
      if (client.userId !== excludeUserId && client.readyState === WebSocket.OPEN) {
        client.send(JSON.stringify(message))
      }
    })
  }

  private sendHeartbeat() {
    this.sessions.forEach((session, sessionId) => {
      session.clients.forEach(client => {
        if (client.readyState === WebSocket.OPEN) {
          this.sendToClient(client, {
            type: 'presence',
            sessionId,
            userId: client.userId,
            timestamp: Date.now(),
            data: { action: 'heartbeat' }
          })
        }
      })
    })
  }

  private cleanupInactiveSessions() {
    const now = Date.now()
    const inactiveThreshold = 30 * 60 * 1000 // 30 minutes

    this.sessions.forEach((session, sessionId) => {
      if (now - session.lastActivity > inactiveThreshold) {
        console.log(`🧹 Cleaning up inactive session ${sessionId}`)
        
        // Close all connections in session
        session.clients.forEach(client => {
          client.close(1000, 'Session inactive')
        })
        
        this.sessions.delete(sessionId)
      }
    })
  }

  public getSessionInfo(sessionId: string) {
    const session = this.sessions.get(sessionId)
    if (!session) return null

    return {
      id: session.id,
      clientCount: session.clients.size,
      activeUsers: Array.from(session.presence.values()),
      lastActivity: session.lastActivity
    }
  }

  public getUserSessions(userId: string): string[] {
    return Array.from(this.userSessions.get(userId) || [])
  }

  public close() {
    clearInterval(this.heartbeatInterval)
    clearInterval(this.cleanupInterval)
    this.wss.close()
    console.log('🔌 Collaboration WebSocket server closed')
  }
}

// Singleton instance
let wsServer: CollaborationWebSocketServer | null = null

export function getWebSocketServer(): CollaborationWebSocketServer {
  if (!wsServer) {
    wsServer = new CollaborationWebSocketServer()
  }
  return wsServer
}

export function closeWebSocketServer() {
  if (wsServer) {
    wsServer.close()
    wsServer = null
  }
}
