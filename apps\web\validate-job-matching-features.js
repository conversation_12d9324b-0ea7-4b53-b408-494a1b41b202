/**
 * Job Matching Features Validation Script
 * 
 * Validates smart job matching and AI recommendations implementation
 */

const fs = require('fs')
const path = require('path')

class JobMatchingFeaturesValidator {
  constructor() {
    this.results = {
      files: { checked: 0, missing: 0, present: 0 },
      services: { checked: 0, missing: 0, present: 0 },
      apis: { checked: 0, missing: 0, present: 0 },
      components: { checked: 0, missing: 0, present: 0 },
      tests: { checked: 0, missing: 0, present: 0 },
      config: { checked: 0, missing: 0, present: 0 }
    }
  }

  log(message, type = 'info') {
    const colors = {
      info: '\x1b[36m',    // Cyan
      success: '\x1b[32m', // Green
      error: '\x1b[31m',   // Red
      warning: '\x1b[33m', // Yellow
      reset: '\x1b[0m'     // Reset
    }
    
    console.log(`${colors[type]}${message}${colors.reset}`)
  }

  checkFileExists(filePath, description) {
    const fullPath = path.join(__dirname, filePath)
    const exists = fs.existsSync(fullPath)
    
    if (exists) {
      this.log(`✅ ${description}: Found`, 'success')
      return true
    } else {
      this.log(`❌ ${description}: Missing (${filePath})`, 'error')
      return false
    }
  }

  checkFileContent(filePath, searchTerms, description) {
    const fullPath = path.join(__dirname, filePath)
    
    if (!fs.existsSync(fullPath)) {
      this.log(`❌ ${description}: File not found`, 'error')
      return false
    }

    try {
      const content = fs.readFileSync(fullPath, 'utf8')
      const missingTerms = searchTerms.filter(term => !content.includes(term))
      
      if (missingTerms.length === 0) {
        this.log(`✅ ${description}: All required content found`, 'success')
        return true
      } else {
        this.log(`⚠️  ${description}: Missing content: ${missingTerms.join(', ')}`, 'warning')
        return false
      }
    } catch (error) {
      this.log(`❌ ${description}: Error reading file - ${error.message}`, 'error')
      return false
    }
  }

  validateFileStructure() {
    this.log('\n🎯 Validating Job Matching File Structure...', 'info')
    
    const requiredFiles = [
      'src/lib/job-matching/service.ts',
      'src/lib/job-matching/ai-engine.ts',
      'src/app/api/job-matching/jobs/route.ts',
      'src/app/api/job-matching/applications/route.ts',
      'src/app/api/job-matching/preferences/route.ts',
      'src/components/job-matching/JobSearch.tsx',
      'src/components/job-matching/JobRecommendations.tsx',
      'src/components/job-matching/ApplicationTracker.tsx',
      'src/test/job-matching/job-matching-service.test.ts',
      'src/test/job-matching/ai-engine.test.ts',
      'src/test/job-matching/job-matching-api.test.ts',
      'src/test/job-matching/job-matching-components.test.tsx',
      'docs/features/smart-job-matching-spec.md'
    ]

    let present = 0
    let total = requiredFiles.length

    requiredFiles.forEach(file => {
      this.results.files.checked++
      if (this.checkFileExists(file, `File: ${file}`)) {
        present++
        this.results.files.present++
      } else {
        this.results.files.missing++
      }
    })

    this.log(`📊 File Structure: ${present}/${total} files present`, present === total ? 'success' : 'warning')
    return present === total
  }

  validateJobMatchingServices() {
    this.log('\n🤖 Validating Job Matching Services...', 'info')
    
    const serviceChecks = [
      {
        file: 'src/lib/job-matching/service.ts',
        description: 'Job Matching Service Implementation',
        requiredContent: ['JobMatchingService', 'searchJobs', 'getRecommendations', 'calculateMatchScore', 'applyToJob', 'updateApplicationStatus']
      },
      {
        file: 'src/lib/job-matching/ai-engine.ts',
        description: 'AI Recommendation Engine Implementation',
        requiredContent: ['AIRecommendationEngine', 'extractSkillsFromResume', 'analyzeJobPosting', 'analyzeJobCompatibility', 'predictCareerPath', 'generateInterviewQuestions']
      }
    ]

    let passed = 0
    let total = serviceChecks.length

    serviceChecks.forEach(check => {
      this.results.services.checked++
      if (this.checkFileContent(check.file, check.requiredContent, check.description)) {
        passed++
        this.results.services.present++
      } else {
        this.results.services.missing++
      }
    })

    this.log(`📊 Job Matching Services: ${passed}/${total} checks passed`, passed === total ? 'success' : 'warning')
    return passed === total
  }

  validateJobMatchingAPIs() {
    this.log('\n🌐 Validating Job Matching API Routes...', 'info')
    
    const apiChecks = [
      {
        file: 'src/app/api/job-matching/jobs/route.ts',
        description: 'Jobs API Routes',
        requiredContent: ['GET', 'POST', 'PUT', 'searchJobs', 'getRecommendations', 'analyzeJobPosting', 'generateInterviewQuestions']
      },
      {
        file: 'src/app/api/job-matching/applications/route.ts',
        description: 'Applications API Routes',
        requiredContent: ['GET', 'POST', 'PUT', 'DELETE', 'applyToJob', 'getApplications', 'updateApplicationStatus']
      },
      {
        file: 'src/app/api/job-matching/preferences/route.ts',
        description: 'Preferences API Routes',
        requiredContent: ['GET', 'POST', 'PUT', 'updateJobPreferences', 'predictCareerPath', 'extractSkillsFromResume']
      }
    ]

    let passed = 0
    let total = apiChecks.length

    apiChecks.forEach(check => {
      this.results.apis.checked++
      if (this.checkFileContent(check.file, check.requiredContent, check.description)) {
        passed++
        this.results.apis.present++
      } else {
        this.results.apis.missing++
      }
    })

    this.log(`📊 Job Matching APIs: ${passed}/${total} checks passed`, passed === total ? 'success' : 'warning')
    return passed === total
  }

  validateJobMatchingComponents() {
    this.log('\n⚛️  Validating Job Matching Components...', 'info')
    
    const componentChecks = [
      {
        file: 'src/components/job-matching/JobSearch.tsx',
        description: 'Job Search Component',
        requiredContent: ['JobSearch', 'searchJobs', 'handleSaveJob', 'handleApplyToJob', 'formatSalary']
      },
      {
        file: 'src/components/job-matching/JobRecommendations.tsx',
        description: 'Job Recommendations Component',
        requiredContent: ['JobRecommendations', 'loadRecommendations', 'refreshRecommendations', 'handleRecommendationAction', 'markAsViewed']
      },
      {
        file: 'src/components/job-matching/ApplicationTracker.tsx',
        description: 'Application Tracker Component',
        requiredContent: ['ApplicationTracker', 'loadApplications', 'updateApplicationStatus', 'addNote', 'getStatusColor']
      }
    ]

    let passed = 0
    let total = componentChecks.length

    componentChecks.forEach(check => {
      this.results.components.checked++
      if (this.checkFileContent(check.file, check.requiredContent, check.description)) {
        passed++
        this.results.components.present++
      } else {
        this.results.components.missing++
      }
    })

    this.log(`📊 Job Matching Components: ${passed}/${total} checks passed`, passed === total ? 'success' : 'warning')
    return passed === total
  }

  validateJobMatchingTests() {
    this.log('\n🧪 Validating Job Matching Test Files...', 'info')
    
    const testChecks = [
      {
        file: 'src/test/job-matching/job-matching-service.test.ts',
        description: 'Job Matching Service Tests',
        requiredContent: ['describe', 'it', 'expect', 'JobMatchingService', 'searchJobs', 'calculateMatchScore', 'applyToJob']
      },
      {
        file: 'src/test/job-matching/ai-engine.test.ts',
        description: 'AI Engine Tests',
        requiredContent: ['describe', 'it', 'expect', 'AIRecommendationEngine', 'extractSkillsFromResume', 'analyzeJobPosting', 'analyzeJobCompatibility']
      },
      {
        file: 'src/test/job-matching/job-matching-api.test.ts',
        description: 'Job Matching API Tests',
        requiredContent: ['describe', 'it', 'expect', 'NextRequest', 'job-matching/jobs', 'job-matching/applications']
      },
      {
        file: 'src/test/job-matching/job-matching-components.test.tsx',
        description: 'Job Matching Component Tests',
        requiredContent: ['describe', 'it', 'expect', 'render', 'JobSearch', 'JobRecommendations', 'ApplicationTracker']
      }
    ]

    let passed = 0
    let total = testChecks.length

    testChecks.forEach(check => {
      this.results.tests.checked++
      if (this.checkFileContent(check.file, check.requiredContent, check.description)) {
        passed++
        this.results.tests.present++
      } else {
        this.results.tests.missing++
      }
    })

    this.log(`📊 Job Matching Tests: ${passed}/${total} checks passed`, passed === total ? 'success' : 'warning')
    return passed === total
  }

  validateConfiguration() {
    this.log('\n⚙️  Validating Job Matching Configuration...', 'info')
    
    const configChecks = [
      {
        file: 'package.json',
        description: 'Package.json Job Matching Scripts',
        requiredContent: ['test:job-matching', 'test:job-matching:service', 'test:job-matching:ai', 'test:job-matching:api']
      },
      {
        file: 'packages/database/prisma/schema.prisma',
        description: 'Database Schema',
        requiredContent: ['JobPosting', 'JobApplication', 'JobRecommendation', 'UserJobPreferences', 'SkillAssessment', 'InterviewPreparation']
      }
    ]

    let passed = 0
    let total = configChecks.length

    configChecks.forEach(check => {
      this.results.config.checked++
      if (this.checkFileContent(check.file, check.requiredContent, check.description)) {
        passed++
        this.results.config.present++
      } else {
        this.results.config.missing++
      }
    })

    this.log(`📊 Job Matching Configuration: ${passed}/${total} checks passed`, passed === total ? 'success' : 'warning')
    return passed === total
  }

  generateReport() {
    this.log('\n📊 Job Matching Features Validation Report', 'info')
    this.log('=' .repeat(60), 'info')
    
    const categories = ['files', 'services', 'apis', 'components', 'tests', 'config']
    let totalChecked = 0
    let totalPresent = 0
    let totalMissing = 0

    categories.forEach(category => {
      const result = this.results[category]
      totalChecked += result.checked
      totalPresent += result.present
      totalMissing += result.missing

      const status = result.missing === 0 ? '✅' : '⚠️ '
      const categoryName = category.charAt(0).toUpperCase() + category.slice(1)
      
      this.log(
        `${status} ${categoryName}: ${result.present}/${result.checked} present`,
        result.missing === 0 ? 'success' : 'warning'
      )
    })

    this.log('=' .repeat(60), 'info')
    this.log(`📈 Overall: ${totalPresent}/${totalChecked} checks passed`, 
             totalMissing === 0 ? 'success' : 'warning')
    
    const completionRate = totalChecked > 0 ? (totalPresent / totalChecked * 100).toFixed(1) : 0
    this.log(`📊 Completion Rate: ${completionRate}%`, 
             completionRate >= 90 ? 'success' : completionRate >= 70 ? 'warning' : 'error')

    // Recommendations
    this.log('\n💡 Job Matching Features Status:', 'info')
    if (totalMissing === 0) {
      this.log('🎉 All job matching features are properly implemented!', 'success')
      this.log('✨ Ready for AI-powered job recommendations and application tracking', 'success')
    } else {
      this.log(`🔧 ${totalMissing} items need attention`, 'warning')
      if (this.results.files.missing > 0) {
        this.log('📁 Create missing files and implement required functionality', 'warning')
      }
      if (this.results.tests.missing > 0) {
        this.log('🧪 Add comprehensive tests for job matching features', 'warning')
      }
      if (this.results.config.missing > 0) {
        this.log('⚙️  Update configuration files for job matching', 'warning')
      }
    }

    return totalMissing === 0
  }

  async validate() {
    this.log('🚀 Starting Job Matching Features Validation...', 'info')
    this.log('📅 ' + new Date().toISOString(), 'info')
    
    const results = [
      this.validateFileStructure(),
      this.validateJobMatchingServices(),
      this.validateJobMatchingAPIs(),
      this.validateJobMatchingComponents(),
      this.validateJobMatchingTests(),
      this.validateConfiguration()
    ]

    const allValid = this.generateReport()
    
    this.log('\n🏁 Job Matching Validation Complete!', 'info')
    return allValid
  }
}

// Run validation if this script is executed directly
if (require.main === module) {
  const validator = new JobMatchingFeaturesValidator()
  validator.validate()
    .then(success => {
      process.exit(success ? 0 : 1)
    })
    .catch(error => {
      console.error('Validation failed:', error)
      process.exit(1)
    })
}

module.exports = JobMatchingFeaturesValidator
