# Database
DATABASE_URL="postgresql://username:password@localhost:5432/careercraft_v2"

# NextAuth.js
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-nextauth-secret-key-here"

# OAuth Providers
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"
GITHUB_CLIENT_ID="your-github-client-id"
GITHUB_CLIENT_SECRET="your-github-client-secret"

# OpenAI
OPENAI_API_KEY="your-openai-api-key"

# Email (Optional - for transactional emails)
SMTP_HOST="smtp.gmail.com"
SMTP_PORT="587"
SMTP_USER="<EMAIL>"
SMTP_PASSWORD="your-app-password"
FROM_EMAIL="<EMAIL>"

# File Storage (Optional - for resume uploads)
AWS_ACCESS_KEY_ID="your-aws-access-key"
AWS_SECRET_ACCESS_KEY="your-aws-secret-key"
AWS_REGION="us-east-1"
AWS_S3_BUCKET="careercraft-uploads"

# Redis (Optional - for caching)
REDIS_URL="redis://localhost:6379"

# Sentry (Optional - for error tracking)
SENTRY_DSN="your-sentry-dsn"
SENTRY_ORG="your-sentry-org"
SENTRY_PROJECT="careercraft-v2"

# App Configuration
NEXT_PUBLIC_APP_URL="http://localhost:3000"
NEXT_PUBLIC_APP_NAME="CareerCraft"

# Feature Flags
NEXT_PUBLIC_ENABLE_ANALYTICS="true"
NEXT_PUBLIC_ENABLE_CHAT_SUPPORT="false"

# Rate Limiting
RATE_LIMIT_REQUESTS_PER_MINUTE="100"

# AI Service URLs (for microservices deployment)
AI_SERVICE_URL="http://localhost:3001"
PDF_SERVICE_URL="http://localhost:3002"

# Google Analytics (Optional)
NEXT_PUBLIC_GA_MEASUREMENT_ID="G-XXXXXXXXXX"

# Site Verification
GOOGLE_SITE_VERIFICATION="your-google-site-verification-code"
