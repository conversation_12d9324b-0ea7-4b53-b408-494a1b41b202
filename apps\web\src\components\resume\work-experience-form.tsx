'use client';

import { useState } from 'react';
import { useForm, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { workExperienceSchema, type WorkExperienceInput } from '@careercraft/shared/schemas/resume';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Icons } from '@/components/ui/icons';
import { cn } from '@/lib/utils';
import { nanoid } from 'nanoid';

interface WorkExperienceFormProps {
  initialData?: WorkExperienceInput;
  onSubmit: (data: WorkExperienceInput) => void;
  onCancel?: () => void;
  isLoading?: boolean;
  className?: string;
}

export function WorkExperienceForm({
  initialData,
  onSubmit,
  onCancel,
  isLoading = false,
  className,
}: WorkExperienceFormProps) {
  const [isCurrentRole, setIsCurrentRole] = useState(initialData?.isCurrentRole || false);

  const {
    register,
    handleSubmit,
    formState: { errors, isDirty, isValid },
    watch,
    control,
    setValue,
  } = useForm<WorkExperienceInput>({
    resolver: zodResolver(workExperienceSchema),
    defaultValues: {
      id: nanoid(),
      company: '',
      position: '',
      location: '',
      startDate: '',
      endDate: '',
      isCurrentRole: false,
      description: '',
      achievements: [''],
      technologies: [],
      ...initialData,
    },
    mode: 'onChange',
  });

  const {
    fields: achievementFields,
    append: appendAchievement,
    remove: removeAchievement,
  } = useFieldArray({
    control,
    name: 'achievements',
  });

  const {
    fields: technologyFields,
    append: appendTechnology,
    remove: removeTechnology,
  } = useFieldArray({
    control,
    name: 'technologies',
  });

  const watchedData = watch();

  const handleFormSubmit = (data: WorkExperienceInput) => {
    // Filter out empty achievements and technologies
    const cleanedData = {
      ...data,
      achievements: data.achievements.filter(achievement => achievement.trim() !== ''),
      technologies: data.technologies?.filter(tech => tech.trim() !== '') || [],
    };
    onSubmit(cleanedData);
  };

  const handleCurrentRoleChange = (checked: boolean) => {
    setIsCurrentRole(checked);
    setValue('isCurrentRole', checked);
    if (checked) {
      setValue('endDate', '');
    }
  };

  return (
    <Card className={cn('w-full', className)}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Icons.briefcase className="h-5 w-5" />
          Work Experience
        </CardTitle>
        <CardDescription>
          Add your professional work experience and achievements
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="company">
                Company <span className="text-destructive">*</span>
              </Label>
              <Input
                id="company"
                {...register('company')}
                placeholder="Google"
                className={cn(errors.company && 'border-destructive')}
              />
              {errors.company && (
                <p className="text-sm text-destructive">{errors.company.message}</p>
              )}
            </div>
            <div className="space-y-2">
              <Label htmlFor="position">
                Position <span className="text-destructive">*</span>
              </Label>
              <Input
                id="position"
                {...register('position')}
                placeholder="Software Engineer"
                className={cn(errors.position && 'border-destructive')}
              />
              {errors.position && (
                <p className="text-sm text-destructive">{errors.position.message}</p>
              )}
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="location">
              Location <span className="text-destructive">*</span>
            </Label>
            <Input
              id="location"
              {...register('location')}
              placeholder="San Francisco, CA"
              className={cn(errors.location && 'border-destructive')}
            />
            {errors.location && (
              <p className="text-sm text-destructive">{errors.location.message}</p>
            )}
          </div>

          {/* Date Range */}
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="startDate">
                  Start Date <span className="text-destructive">*</span>
                </Label>
                <Input
                  id="startDate"
                  type="month"
                  {...register('startDate')}
                  className={cn(errors.startDate && 'border-destructive')}
                />
                {errors.startDate && (
                  <p className="text-sm text-destructive">{errors.startDate.message}</p>
                )}
              </div>
              <div className="space-y-2">
                <Label htmlFor="endDate">
                  End Date {!isCurrentRole && <span className="text-destructive">*</span>}
                </Label>
                <Input
                  id="endDate"
                  type="month"
                  {...register('endDate')}
                  disabled={isCurrentRole}
                  className={cn(
                    errors.endDate && 'border-destructive',
                    isCurrentRole && 'opacity-50'
                  )}
                />
                {errors.endDate && (
                  <p className="text-sm text-destructive">{errors.endDate.message}</p>
                )}
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="isCurrentRole"
                checked={isCurrentRole}
                onCheckedChange={handleCurrentRoleChange}
              />
              <Label htmlFor="isCurrentRole" className="text-sm">
                I currently work here
              </Label>
            </div>
          </div>

          {/* Description */}
          <div className="space-y-2">
            <Label htmlFor="description">
              Job Description <span className="text-destructive">*</span>
            </Label>
            <Textarea
              id="description"
              {...register('description')}
              placeholder="Describe your role and responsibilities..."
              rows={4}
              className={cn(errors.description && 'border-destructive')}
            />
            {errors.description && (
              <p className="text-sm text-destructive">{errors.description.message}</p>
            )}
            <p className="text-xs text-muted-foreground">
              {watchedData.description?.length || 0}/1000 characters
            </p>
          </div>

          {/* Achievements */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Label>Key Achievements</Label>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => appendAchievement('')}
                disabled={achievementFields.length >= 10}
              >
                <Icons.plus className="h-4 w-4 mr-2" />
                Add Achievement
              </Button>
            </div>
            <div className="space-y-3">
              {achievementFields.map((field, index) => (
                <div key={field.id} className="flex gap-2">
                  <div className="flex-1 space-y-2">
                    <Input
                      {...register(`achievements.${index}`)}
                      placeholder="Increased team productivity by 30% through process optimization"
                      className={cn(errors.achievements?.[index] && 'border-destructive')}
                    />
                    {errors.achievements?.[index] && (
                      <p className="text-sm text-destructive">
                        {errors.achievements[index]?.message}
                      </p>
                    )}
                  </div>
                  <Button
                    type="button"
                    variant="outline"
                    size="icon"
                    onClick={() => removeAchievement(index)}
                    disabled={achievementFields.length <= 1}
                  >
                    <Icons.trash className="h-4 w-4" />
                  </Button>
                </div>
              ))}
            </div>
          </div>

          {/* Technologies */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Label>Technologies Used (Optional)</Label>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => appendTechnology('')}
                disabled={technologyFields.length >= 20}
              >
                <Icons.plus className="h-4 w-4 mr-2" />
                Add Technology
              </Button>
            </div>
            {technologyFields.length > 0 && (
              <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                {technologyFields.map((field, index) => (
                  <div key={field.id} className="flex gap-2">
                    <Input
                      {...register(`technologies.${index}`)}
                      placeholder="React"
                      className="text-sm"
                    />
                    <Button
                      type="button"
                      variant="outline"
                      size="icon"
                      onClick={() => removeTechnology(index)}
                    >
                      <Icons.x className="h-3 w-3" />
                    </Button>
                  </div>
                ))}
              </div>
            )}
          </div>

          <Separator />

          {/* Form Actions */}
          <div className="flex flex-col sm:flex-row gap-3">
            <Button
              type="submit"
              disabled={!isDirty || !isValid || isLoading}
              className="flex-1 sm:flex-none"
            >
              {isLoading && <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />}
              Save Work Experience
            </Button>
            {onCancel && (
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={isLoading}
                className="flex-1 sm:flex-none"
              >
                Cancel
              </Button>
            )}
          </div>

          {/* Form Status */}
          {isDirty && (
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <Icons.alertCircle className="h-4 w-4" />
              You have unsaved changes
            </div>
          )}
        </form>
      </CardContent>
    </Card>
  );
}
