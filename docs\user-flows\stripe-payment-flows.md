# Epic 8.0: Stripe Payment Integration - User Flow Diagrams

## Overview
This document contains comprehensive user flow diagrams for all payment and subscription management scenarios in the CareerCraft SaaS platform.

## 1. Subscription Signup Flow

```mermaid
flowchart TD
    A[User visits pricing page] --> B{User authenticated?}
    B -->|No| C[Redirect to login/signup]
    B -->|Yes| D[Display pricing plans]
    C --> E[Complete authentication]
    E --> D
    D --> F[User selects plan]
    F --> G{Plan type?}
    G -->|Free| H[Activate free plan immediately]
    G -->|Premium/Enterprise| I[Redirect to payment form]
    H --> J[Show success message]
    H --> K[Update user permissions]
    I --> L[Load Stripe Elements]
    L --> M[User enters payment details]
    M --> N[Validate payment information]
    N --> O{Validation successful?}
    O -->|No| P[Show error message]
    P --> M
    O -->|Yes| Q[Create Stripe customer]
    Q --> R[Create Stripe subscription]
    R --> S{Payment successful?}
    S -->|No| T[Show payment failed message]
    T --> U[Offer retry or different payment method]
    U --> M
    S -->|Yes| V[Store subscription in database]
    V --> W[Send confirmation email]
    W --> X[Redirect to dashboard]
    X --> Y[Show welcome message]
    Y --> Z[Enable premium features]
    J --> AA[Redirect to dashboard]
    K --> AA
```

## 2. Plan Upgrade Flow

```mermaid
flowchart TD
    A[User clicks upgrade button] --> B[Load current subscription]
    B --> C[Display available upgrades]
    C --> D[User selects new plan]
    D --> E{Requires payment method?}
    E -->|No| F[Calculate proration]
    E -->|Yes| G[Show payment method form]
    G --> H[User enters/updates payment info]
    H --> I[Validate payment method]
    I --> J{Validation successful?}
    J -->|No| K[Show error message]
    K --> H
    J -->|Yes| F
    F --> L[Show upgrade preview]
    L --> M[Display prorated amount]
    M --> N[User confirms upgrade]
    N --> O[Update Stripe subscription]
    O --> P{Update successful?}
    P -->|No| Q[Show error message]
    Q --> R[Offer support contact]
    P -->|Yes| S[Update database]
    S --> T[Process immediate payment]
    T --> U{Payment successful?}
    U -->|No| V[Revert subscription]
    V --> W[Show payment failed message]
    U -->|Yes| X[Update user permissions]
    X --> Y[Send upgrade confirmation email]
    Y --> Z[Show success message]
    Z --> AA[Redirect to dashboard]
    W --> BB[Offer retry or support]
```

## 3. Payment Method Management Flow

```mermaid
flowchart TD
    A[User accesses billing settings] --> B[Load current payment methods]
    B --> C[Display payment methods list]
    C --> D{User action?}
    D -->|Add new method| E[Show add payment form]
    D -->|Update existing| F[Show update form]
    D -->|Delete method| G[Confirm deletion]
    D -->|Set as default| H[Update default method]
    E --> I[Load Stripe Elements]
    I --> J[User enters payment details]
    J --> K[Validate and save to Stripe]
    K --> L{Save successful?}
    L -->|No| M[Show error message]
    M --> J
    L -->|Yes| N[Update database]
    N --> O[Show success message]
    F --> P[Pre-fill current details]
    P --> Q[User updates information]
    Q --> R[Validate and update Stripe]
    R --> S{Update successful?}
    S -->|No| T[Show error message]
    T --> Q
    S -->|Yes| U[Update database]
    U --> V[Show success message]
    G --> W{Has other methods?}
    W -->|No| X[Show cannot delete message]
    W -->|Yes| Y[Delete from Stripe]
    Y --> Z[Remove from database]
    Z --> AA[Show deletion confirmation]
    H --> BB[Update Stripe customer]
    BB --> CC[Update database]
    CC --> DD[Show default updated message]
    O --> EE[Refresh payment methods list]
    V --> EE
    AA --> EE
    DD --> EE
    X --> EE
```

## 4. Invoice and Billing History Flow

```mermaid
flowchart TD
    A[User accesses billing history] --> B[Load user subscription]
    B --> C[Fetch invoices from database]
    C --> D[Display invoices list]
    D --> E{User action?}
    E -->|View invoice| F[Load invoice details]
    E -->|Download PDF| G[Generate/fetch PDF]
    E -->|Pay overdue| H[Process payment]
    E -->|Filter/search| I[Apply filters]
    F --> J[Display invoice breakdown]
    J --> K[Show line items]
    K --> L[Display payment status]
    L --> M{Invoice status?}
    M -->|Paid| N[Show payment date]
    M -->|Pending| O[Show due date]
    M -->|Failed| P[Show retry payment option]
    G --> Q{PDF exists?}
    Q -->|Yes| R[Download existing PDF]
    Q -->|No| S[Generate PDF from Stripe]
    S --> T[Cache PDF URL]
    T --> U[Download PDF]
    H --> V[Load payment form]
    V --> W[Process payment intent]
    W --> X{Payment successful?}
    X -->|No| Y[Show payment failed]
    X -->|Yes| Z[Update invoice status]
    Z --> AA[Send payment confirmation]
    AA --> BB[Refresh invoice list]
    I --> CC[Filter invoices by criteria]
    CC --> DD[Display filtered results]
    N --> EE[Back to invoice list]
    O --> EE
    P --> EE
    R --> EE
    U --> EE
    Y --> EE
    BB --> EE
    DD --> EE
```

## 5. Subscription Cancellation Flow

```mermaid
flowchart TD
    A[User clicks cancel subscription] --> B[Show cancellation options]
    B --> C{Cancellation type?}
    C -->|Immediate| D[Confirm immediate cancellation]
    C -->|End of period| E[Confirm end-of-period cancellation]
    C -->|Pause/downgrade| F[Show alternative options]
    D --> G[Show immediate cancellation warning]
    G --> H[User confirms immediate cancellation]
    H --> I[Cancel Stripe subscription immediately]
    I --> J{Cancellation successful?}
    J -->|No| K[Show error message]
    J -->|Yes| L[Update database status]
    L --> M[Revoke premium features immediately]
    M --> N[Send cancellation confirmation email]
    N --> O[Show cancellation success]
    E --> P[Show end-of-period details]
    P --> Q[Display remaining access period]
    Q --> R[User confirms end-of-period cancellation]
    R --> S[Set Stripe subscription to cancel at period end]
    S --> T{Update successful?}
    T -->|No| U[Show error message]
    T -->|Yes| V[Update database with cancellation date]
    V --> W[Send cancellation scheduled email]
    W --> X[Show scheduled cancellation message]
    F --> Y[Display downgrade options]
    Y --> Z[User selects alternative]
    Z --> AA{Alternative type?}
    AA -->|Downgrade| BB[Redirect to plan change flow]
    AA -->|Pause| CC[Show pause subscription option]
    AA -->|Feedback| DD[Show feedback form]
    CC --> EE[Pause subscription for selected period]
    EE --> FF[Send pause confirmation]
    DD --> GG[Collect cancellation feedback]
    GG --> HH[Store feedback for analysis]
    HH --> II[Offer retention incentive]
    II --> JJ{User accepts incentive?}
    JJ -->|Yes| KK[Apply discount/benefit]
    JJ -->|No| LL[Proceed with cancellation]
    K --> MM[Offer support contact]
    U --> MM
    O --> NN[Redirect to account page]
    X --> NN
    FF --> NN
    KK --> NN
    LL --> E
```

## 6. Payment Failure Recovery Flow

```mermaid
flowchart TD
    A[Payment failure webhook received] --> B[Update payment status in database]
    B --> C[Load user subscription details]
    C --> D[Send payment failure notification email]
    D --> E[Set subscription to past_due status]
    E --> F[Create retry schedule]
    F --> G[User receives failure notification]
    G --> H{User action?}
    H -->|Update payment method| I[Redirect to payment method page]
    H -->|Retry payment| J[Process retry with existing method]
    H -->|Contact support| K[Redirect to support]
    H -->|No action| L[Wait for automatic retry]
    I --> M[User updates payment information]
    M --> N[Validate new payment method]
    N --> O{Validation successful?}
    O -->|No| P[Show validation error]
    P --> M
    O -->|Yes| Q[Save new payment method]
    Q --> R[Retry failed payment]
    J --> R
    R --> S{Retry successful?}
    S -->|No| T[Increment retry count]
    T --> U{Max retries reached?}
    U -->|No| V[Schedule next retry]
    U -->|Yes| W[Cancel subscription]
    S -->|Yes| X[Update subscription to active]
    X --> Y[Send payment success email]
    Y --> Z[Restore full access]
    L --> AA[Automatic retry triggered]
    AA --> BB[Process payment with existing method]
    BB --> CC{Payment successful?}
    CC -->|No| DD[Follow retry schedule]
    CC -->|Yes| EE[Restore subscription]
    V --> FF[Wait for next retry period]
    FF --> AA
    W --> GG[Send subscription cancelled email]
    GG --> HH[Revoke access to premium features]
    HH --> II[Offer reactivation option]
    DD --> T
    EE --> X
    K --> JJ[Create support ticket]
    JJ --> KK[Support team follows up]
    Z --> LL[User continues with service]
    II --> MM[User can reactivate if desired]
```

## 7. Usage Tracking and Limits Flow

```mermaid
flowchart TD
    A[User performs billable action] --> B[Check current subscription plan]
    B --> C[Load plan limits and current usage]
    C --> D{Within limits?}
    D -->|Yes| E[Allow action]
    D -->|No| F[Check if soft or hard limit]
    F --> G{Limit type?}
    G -->|Soft limit| H[Allow action with warning]
    G -->|Hard limit| I[Block action]
    E --> J[Record usage in database]
    H --> K[Show upgrade suggestion]
    K --> J
    I --> L[Show limit reached message]
    L --> M[Offer upgrade options]
    J --> N[Update usage counters]
    N --> O{Usage threshold reached?}
    O -->|No| P[Continue normal operation]
    O -->|Yes| Q[Send usage notification]
    Q --> R{Notification type?}
    R -->|Warning| S[Send 80% usage warning]
    R -->|Limit reached| T[Send limit reached notification]
    R -->|Overage| U[Send overage notification]
    S --> V[Suggest plan upgrade]
    T --> W[Block further usage]
    U --> X[Calculate overage charges]
    X --> Y[Add to next invoice]
    M --> Z{User action?}
    Z -->|Upgrade plan| AA[Redirect to upgrade flow]
    Z -->|Contact sales| BB[Redirect to sales contact]
    Z -->|Dismiss| CC[Return to application]
    V --> DD[User can continue with warning]
    W --> EE[User must upgrade to continue]
    Y --> FF[Continue with overage billing]
    AA --> GG[Process plan upgrade]
    GG --> HH[Update limits immediately]
    HH --> II[Allow continued usage]
    BB --> JJ[Sales team follows up]
    CC --> KK[Usage remains blocked]
    P --> LL[Normal application flow]
    DD --> LL
    FF --> LL
    II --> LL
```

## 8. Admin Revenue Dashboard Flow

```mermaid
flowchart TD
    A[Admin accesses revenue dashboard] --> B[Load authentication and permissions]
    B --> C{Admin authorized?}
    C -->|No| D[Show access denied]
    C -->|Yes| E[Load dashboard data]
    E --> F[Fetch revenue metrics from database]
    F --> G[Calculate key performance indicators]
    G --> H[Display revenue overview]
    H --> I[Show MRR/ARR trends]
    I --> J[Display subscription metrics]
    J --> K[Show churn and retention rates]
    K --> L{Admin action?}
    L -->|View detailed report| M[Load detailed analytics]
    L -->|Export data| N[Generate export file]
    L -->|Filter by date range| O[Apply date filters]
    L -->|Drill down by plan| P[Show plan-specific metrics]
    L -->|View customer details| Q[Load customer analytics]
    M --> R[Display comprehensive report]
    R --> S[Show revenue breakdown]
    S --> T[Display customer segments]
    T --> U[Show growth projections]
    N --> V{Export format?}
    V -->|CSV| W[Generate CSV export]
    V -->|PDF| X[Generate PDF report]
    V -->|Excel| Y[Generate Excel file]
    O --> Z[Refresh dashboard with filters]
    Z --> AA[Update all metrics]
    P --> BB[Show plan performance]
    BB --> CC[Display upgrade/downgrade flows]
    CC --> DD[Show plan conversion rates]
    Q --> EE[Load customer list]
    EE --> FF[Show customer lifetime value]
    FF --> GG[Display payment history]
    GG --> HH[Show usage patterns]
    W --> II[Download CSV file]
    X --> JJ[Download PDF report]
    Y --> KK[Download Excel file]
    U --> LL[Admin can take action based on insights]
    AA --> LL
    DD --> LL
    HH --> LL
    II --> LL
    JJ --> LL
    KK --> LL
    D --> MM[Redirect to login]
```

These user flows provide comprehensive coverage of all payment and subscription management scenarios in the CareerCraft SaaS platform, ensuring a smooth user experience and robust business operations.
