/**
 * Collaboration State Management
 * 
 * Zustand store for managing real-time collaboration state
 */

import { create } from 'zustand'
import { subscribeWithSelector } from 'zustand/middleware'
import { CollaborationWebSocketClient } from './websocket-client'
import { ChangeOperation, PresenceInfo } from './websocket-server'
import OperationalTransform from './operational-transform'

export interface CollaborationUser {
  userId: string
  userName: string
  userAvatar?: string
  status: 'active' | 'idle' | 'away'
  lastSeen: number
  cursor?: {
    sectionPath: string
    position: number
  }
}

export interface CollaborationComment {
  id: string
  userId: string
  userName: string
  userAvatar?: string
  sectionPath: string
  content: string
  parentId?: string
  isResolved: boolean
  timestamp: number
  replies: CollaborationComment[]
}

export interface CollaborationChange {
  id: string
  operation: ChangeOperation
  userId: string
  userName: string
  timestamp: number
  applied: boolean
}

export interface CollaborationPermission {
  userId: string
  permissionLevel: 'view' | 'comment' | 'edit' | 'admin'
  grantedBy: string
}

export interface CollaborationState {
  // Connection state
  isConnected: boolean
  isConnecting: boolean
  connectionError: string | null
  
  // Session info
  sessionId: string | null
  sessionToken: string | null
  resumeId: string | null
  ownerId: string | null
  
  // Users and presence
  activeUsers: CollaborationUser[]
  userPermissions: CollaborationPermission[]
  currentUser: CollaborationUser | null
  
  // Changes and operations
  pendingChanges: CollaborationChange[]
  appliedChanges: CollaborationChange[]
  documentVersion: number
  
  // Comments
  comments: CollaborationComment[]
  
  // WebSocket client
  client: CollaborationWebSocketClient | null
  
  // Actions
  connect: (sessionId: string, token: string) => Promise<void>
  disconnect: () => void
  sendChange: (operation: ChangeOperation) => void
  sendPresence: (status: 'active' | 'idle' | 'away', cursor?: { sectionPath: string; position: number }) => void
  sendComment: (sectionPath: string, content: string, parentId?: string) => void
  sendCursor: (sectionPath: string, position: number) => void
  applyRemoteChange: (change: CollaborationChange) => void
  addComment: (comment: CollaborationComment) => void
  resolveComment: (commentId: string) => void
  updateUserPresence: (users: CollaborationUser[]) => void
  setPermissions: (permissions: CollaborationPermission[]) => void
  reset: () => void
}

const initialState = {
  isConnected: false,
  isConnecting: false,
  connectionError: null,
  sessionId: null,
  sessionToken: null,
  resumeId: null,
  ownerId: null,
  activeUsers: [],
  userPermissions: [],
  currentUser: null,
  pendingChanges: [],
  appliedChanges: [],
  documentVersion: 0,
  comments: [],
  client: null
}

export const useCollaborationStore = create<CollaborationState>()(
  subscribeWithSelector((set, get) => ({
    ...initialState,

    connect: async (sessionId: string, token: string) => {
      const state = get()
      
      if (state.isConnected || state.isConnecting) {
        return
      }

      set({ isConnecting: true, connectionError: null })

      try {
        // Create WebSocket client
        const client = new CollaborationWebSocketClient({
          sessionId,
          token,
          serverUrl: process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:8080'
        })

        // Set up event listeners
        client.on('connected', () => {
          set({ 
            isConnected: true, 
            isConnecting: false, 
            sessionId,
            sessionToken: token,
            client 
          })
        })

        client.on('disconnected', () => {
          set({ 
            isConnected: false, 
            isConnecting: false,
            connectionError: 'Connection lost'
          })
        })

        client.on('error', (error) => {
          set({ 
            isConnected: false, 
            isConnecting: false,
            connectionError: error.message 
          })
        })

        client.on('change', (operation, author) => {
          const change: CollaborationChange = {
            id: `${Date.now()}-${Math.random()}`,
            operation,
            userId: author.userId,
            userName: author.userName,
            timestamp: Date.now(),
            applied: false
          }
          
          get().applyRemoteChange(change)
        })

        client.on('presence', (users) => {
          const collaborationUsers: CollaborationUser[] = users.map(user => ({
            userId: user.userId,
            userName: user.userName,
            userAvatar: user.userAvatar,
            status: user.status,
            lastSeen: user.lastSeen,
            cursor: user.cursor
          }))
          
          get().updateUserPresence(collaborationUsers)
        })

        client.on('comment', (commentData) => {
          const comment: CollaborationComment = {
            id: commentData.id || `${Date.now()}-${Math.random()}`,
            userId: commentData.author.userId,
            userName: commentData.author.userName,
            userAvatar: commentData.author.userAvatar,
            sectionPath: commentData.sectionPath,
            content: commentData.content,
            parentId: commentData.parentId,
            isResolved: false,
            timestamp: commentData.timestamp,
            replies: []
          }
          
          get().addComment(comment)
        })

        client.on('userJoined', (user) => {
          const collaborationUser: CollaborationUser = {
            userId: user.userId,
            userName: user.userName,
            userAvatar: user.userAvatar,
            status: user.status,
            lastSeen: user.lastSeen,
            cursor: user.cursor
          }
          
          set(state => ({
            activeUsers: [...state.activeUsers.filter(u => u.userId !== user.userId), collaborationUser]
          }))
        })

        client.on('userLeft', (userId) => {
          set(state => ({
            activeUsers: state.activeUsers.filter(u => u.userId !== userId)
          }))
        })

        // Connect to server
        await client.connect()
        
      } catch (error) {
        set({ 
          isConnecting: false, 
          connectionError: error instanceof Error ? error.message : 'Connection failed' 
        })
        throw error
      }
    },

    disconnect: () => {
      const { client } = get()
      
      if (client) {
        client.disconnect()
      }
      
      set(initialState)
    },

    sendChange: (operation: ChangeOperation) => {
      const { client, isConnected } = get()
      
      if (!client || !isConnected) {
        console.warn('Cannot send change: not connected')
        return
      }

      // Add to pending changes
      const change: CollaborationChange = {
        id: `${Date.now()}-${Math.random()}`,
        operation,
        userId: 'current-user', // Will be set by server
        userName: 'Current User',
        timestamp: Date.now(),
        applied: false
      }

      set(state => ({
        pendingChanges: [...state.pendingChanges, change],
        documentVersion: state.documentVersion + 1
      }))

      client.sendChange(operation)
    },

    sendPresence: (status: 'active' | 'idle' | 'away', cursor?: { sectionPath: string; position: number }) => {
      const { client, isConnected } = get()
      
      if (!client || !isConnected) return
      
      client.sendPresence(status, cursor)
      
      // Update current user status
      set(state => ({
        currentUser: state.currentUser ? {
          ...state.currentUser,
          status,
          cursor,
          lastSeen: Date.now()
        } : null
      }))
    },

    sendComment: (sectionPath: string, content: string, parentId?: string) => {
      const { client, isConnected } = get()
      
      if (!client || !isConnected) return
      
      client.sendComment(sectionPath, content, parentId)
    },

    sendCursor: (sectionPath: string, position: number) => {
      const { client, isConnected } = get()
      
      if (!client || !isConnected) return
      
      client.sendCursor(sectionPath, position)
      
      // Update current user cursor
      set(state => ({
        currentUser: state.currentUser ? {
          ...state.currentUser,
          cursor: { sectionPath, position }
        } : null
      }))
    },

    applyRemoteChange: (change: CollaborationChange) => {
      set(state => {
        // Transform the operation against pending changes
        let transformedOperation = change.operation
        
        for (const pendingChange of state.pendingChanges) {
          const result = OperationalTransform.transform(
            transformedOperation,
            pendingChange.operation,
            'right' // Remote changes have lower priority
          )
          transformedOperation = result.operation1
        }

        const transformedChange = {
          ...change,
          operation: transformedOperation,
          applied: true
        }

        return {
          appliedChanges: [...state.appliedChanges, transformedChange],
          documentVersion: state.documentVersion + 1
        }
      })
    },

    addComment: (comment: CollaborationComment) => {
      set(state => {
        if (comment.parentId) {
          // Add as reply to existing comment
          const updateComments = (comments: CollaborationComment[]): CollaborationComment[] => {
            return comments.map(c => {
              if (c.id === comment.parentId) {
                return {
                  ...c,
                  replies: [...c.replies, comment]
                }
              }
              if (c.replies.length > 0) {
                return {
                  ...c,
                  replies: updateComments(c.replies)
                }
              }
              return c
            })
          }
          
          return {
            comments: updateComments(state.comments)
          }
        } else {
          // Add as top-level comment
          return {
            comments: [...state.comments, comment]
          }
        }
      })
    },

    resolveComment: (commentId: string) => {
      set(state => {
        const updateComments = (comments: CollaborationComment[]): CollaborationComment[] => {
          return comments.map(c => {
            if (c.id === commentId) {
              return { ...c, isResolved: true }
            }
            if (c.replies.length > 0) {
              return {
                ...c,
                replies: updateComments(c.replies)
              }
            }
            return c
          })
        }
        
        return {
          comments: updateComments(state.comments)
        }
      })
    },

    updateUserPresence: (users: CollaborationUser[]) => {
      set({ activeUsers: users })
    },

    setPermissions: (permissions: CollaborationPermission[]) => {
      set({ userPermissions: permissions })
    },

    reset: () => {
      const { client } = get()
      if (client) {
        client.disconnect()
      }
      set(initialState)
    }
  }))
)

// Selectors for common use cases
export const useCollaborationConnection = () => {
  const store = useCollaborationStore()
  return {
    isConnected: store.isConnected,
    isConnecting: store.isConnecting,
    connectionError: store.connectionError,
    connect: store.connect,
    disconnect: store.disconnect
  }
}

export const useCollaborationUsers = () => {
  const store = useCollaborationStore()
  return {
    activeUsers: store.activeUsers,
    currentUser: store.currentUser,
    userPermissions: store.userPermissions
  }
}

export const useCollaborationChanges = () => {
  const store = useCollaborationStore()
  return {
    pendingChanges: store.pendingChanges,
    appliedChanges: store.appliedChanges,
    documentVersion: store.documentVersion,
    sendChange: store.sendChange
  }
}

export const useCollaborationComments = () => {
  const store = useCollaborationStore()
  return {
    comments: store.comments,
    sendComment: store.sendComment,
    resolveComment: store.resolveComment
  }
}
