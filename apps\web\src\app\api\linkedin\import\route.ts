/**
 * LinkedIn Import API
 * 
 * Handles importing LinkedIn profile data to resumes
 */

import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { linkedInService, LinkedInImportRequestSchema } from '@/lib/linkedin/service'
import { z } from 'zod'

// Apply import request schema
const ApplyImportRequestSchema = z.object({
  resumeId: z.string(),
  importId: z.string(),
  sections: z.array(z.enum(['personalInfo', 'experience', 'education', 'skills']))
})

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { resumeId, sections } = LinkedInImportRequestSchema.parse(body)

    // Check if user has LinkedIn connected
    const hasLinkedIn = await linkedInService.hasLinkedInProfile(session.user.id)
    if (!hasLinkedIn) {
      return NextResponse.json(
        { error: 'LinkedIn account not connected' },
        { status: 400 }
      )
    }

    // Get LinkedIn profile data
    const profileData = await linkedInService.getLinkedInProfile(session.user.id)
    if (!profileData) {
      return NextResponse.json(
        { error: 'LinkedIn profile data not found' },
        { status: 404 }
      )
    }

    // For this demo, we'll use the stored profile data
    // In a real implementation, you might want to refresh the data
    const importRequest = { resumeId, sections }
    
    // Create a mock access token since we're using stored data
    // In production, you'd need to handle token refresh
    const mockAccessToken = 'stored_profile_data'
    
    const result = await linkedInService.importLinkedInData(
      session.user.id,
      mockAccessToken,
      importRequest
    )

    if (!result.success) {
      return NextResponse.json(
        { error: 'Import failed', details: result.errors },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      importId: result.importId,
      importedData: result.importedData,
      message: 'LinkedIn data imported successfully'
    })
  } catch (error) {
    console.error('LinkedIn import error:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { resumeId, importId, sections } = ApplyImportRequestSchema.parse(body)

    // Apply imported data to resume
    const success = await linkedInService.applyImportToResume(
      session.user.id,
      resumeId,
      importId,
      sections
    )

    if (!success) {
      return NextResponse.json(
        { error: 'Failed to apply import to resume' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'Import applied to resume successfully'
    })
  } catch (error) {
    console.error('LinkedIn apply import error:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const action = searchParams.get('action')

    if (action === 'status') {
      // Get LinkedIn connection status
      const status = await linkedInService.getConnectionStatus(session.user.id)
      return NextResponse.json(status)
    }

    if (action === 'history') {
      // Get import history
      const limit = parseInt(searchParams.get('limit') || '10')
      const history = await linkedInService.getImportHistory(session.user.id, limit)
      return NextResponse.json({ history })
    }

    return NextResponse.json(
      { error: 'Invalid action' },
      { status: 400 }
    )
  } catch (error) {
    console.error('LinkedIn import GET error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
