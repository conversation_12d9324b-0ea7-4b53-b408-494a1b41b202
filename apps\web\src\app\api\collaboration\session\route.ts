/**
 * Collaboration Session API
 * 
 * Handles collaboration session management
 */

import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { collaborationService, CreateSessionSchema, InviteUserSchema } from '@/lib/collaboration/service'
import { z } from 'zod'

// Request schemas
const GetSessionSchema = z.object({
  sessionId: z.string().optional(),
  sessionToken: z.string().optional()
})

const RemoveUserSchema = z.object({
  sessionId: z.string(),
  userId: z.string()
})

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { resumeId, expiresIn } = CreateSessionSchema.parse(body)

    // Create collaboration session
    const collaborationSession = await collaborationService.createSession({
      resumeId,
      ownerId: session.user.id,
      expiresIn
    })

    return NextResponse.json({
      success: true,
      session: collaborationSession,
      message: 'Collaboration session created successfully'
    })
  } catch (error) {
    console.error('Collaboration session creation error:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const sessionId = searchParams.get('sessionId')
    const sessionToken = searchParams.get('sessionToken')
    const action = searchParams.get('action')

    if (action === 'list') {
      // Get user's collaboration sessions
      const sessions = await collaborationService.getUserSessions(session.user.id)
      return NextResponse.json({ sessions })
    }

    if (sessionId) {
      // Get session by ID
      const collaborationSession = await collaborationService.getSession(sessionId)
      
      if (!collaborationSession) {
        return NextResponse.json(
          { error: 'Session not found' },
          { status: 404 }
        )
      }

      // Check if user has permission to access session
      const permission = await collaborationService.getUserPermission(sessionId, session.user.id)
      if (!permission) {
        return NextResponse.json(
          { error: 'Access denied' },
          { status: 403 }
        )
      }

      return NextResponse.json({ session: collaborationSession })
    }

    if (sessionToken) {
      // Get session by token
      const collaborationSession = await collaborationService.getSessionByToken(sessionToken)
      
      if (!collaborationSession) {
        return NextResponse.json(
          { error: 'Session not found or expired' },
          { status: 404 }
        )
      }

      return NextResponse.json({ session: collaborationSession })
    }

    return NextResponse.json(
      { error: 'Session ID or token required' },
      { status: 400 }
    )
  } catch (error) {
    console.error('Collaboration session get error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const action = body.action

    if (action === 'invite') {
      const { sessionId, userId, permissionLevel } = InviteUserSchema.parse(body)
      
      await collaborationService.inviteUser({
        sessionId,
        userId,
        permissionLevel,
        grantedBy: session.user.id
      })

      return NextResponse.json({
        success: true,
        message: 'User invited successfully'
      })
    }

    if (action === 'remove') {
      const { sessionId, userId } = RemoveUserSchema.parse(body)
      
      await collaborationService.removeUser(sessionId, userId, session.user.id)

      return NextResponse.json({
        success: true,
        message: 'User removed successfully'
      })
    }

    return NextResponse.json(
      { error: 'Invalid action' },
      { status: 400 }
    )
  } catch (error) {
    console.error('Collaboration session update error:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const sessionId = searchParams.get('sessionId')

    if (!sessionId) {
      return NextResponse.json(
        { error: 'Session ID required' },
        { status: 400 }
      )
    }

    // Check if user is session owner
    const collaborationSession = await collaborationService.getSession(sessionId)
    if (!collaborationSession || collaborationSession.ownerId !== session.user.id) {
      return NextResponse.json(
        { error: 'Access denied' },
        { status: 403 }
      )
    }

    // Delete session
    const success = await collaborationService.deleteSession(sessionId)
    
    if (!success) {
      return NextResponse.json(
        { error: 'Failed to delete session' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'Session deleted successfully'
    })
  } catch (error) {
    console.error('Collaboration session delete error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
