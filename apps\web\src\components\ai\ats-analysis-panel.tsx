'use client';

import { useState, useEffect } from 'react';
import {
  ATSOptimizationData,
  ATSIssue,
  ATSRecommendation,
  IssueSeverity,
  EffortLevel,
} from '@careercraft/shared/types/ai';
import { Resume } from '@careercraft/shared/types/resume';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Icons } from '@/components/ui/icons';
import { LoadingSpinner } from '@/components/ui/loading';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';

interface ATSAnalysisPanelProps {
  resume: Resume;
  onRecommendationApply?: (recommendation: ATSRecommendation) => void;
  className?: string;
}

export function ATSAnalysisPanel({ resume, onRecommendationApply, className }: ATSAnalysisPanelProps) {
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisData, setAnalysisData] = useState<ATSOptimizationData | null>(null);
  const [jobDescription, setJobDescription] = useState('');

  useEffect(() => {
    // Auto-analyze on component mount
    handleAnalyze();
  }, [resume.id]);

  const handleAnalyze = async () => {
    try {
      setIsAnalyzing(true);
      
      const response = await fetch('/api/ai/analyze-ats', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          resumeId: resume.id,
          jobDescription: jobDescription || undefined,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to analyze resume');
      }

      const result = await response.json();
      setAnalysisData(result.analysis);
      toast.success('ATS analysis completed!');
    } catch (error) {
      console.error('ATS analysis error:', error);
      toast.error('Failed to analyze resume. Please try again.');
    } finally {
      setIsAnalyzing(false);
    }
  };

  const getSeverityColor = (severity: IssueSeverity) => {
    switch (severity) {
      case IssueSeverity.CRITICAL:
        return 'text-red-600 bg-red-50 border-red-200';
      case IssueSeverity.HIGH:
        return 'text-orange-600 bg-orange-50 border-orange-200';
      case IssueSeverity.MEDIUM:
        return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case IssueSeverity.LOW:
        return 'text-blue-600 bg-blue-50 border-blue-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getEffortColor = (effort: EffortLevel) => {
    switch (effort) {
      case EffortLevel.LOW:
        return 'text-green-600';
      case EffortLevel.MEDIUM:
        return 'text-yellow-600';
      case EffortLevel.HIGH:
        return 'text-red-600';
      default:
        return 'text-gray-600';
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreDescription = (score: number) => {
    if (score >= 90) return 'Excellent - Your resume is highly optimized for ATS systems';
    if (score >= 80) return 'Good - Your resume should perform well with most ATS systems';
    if (score >= 60) return 'Fair - Some improvements needed for better ATS compatibility';
    if (score >= 40) return 'Poor - Significant improvements needed for ATS optimization';
    return 'Critical - Major changes required for ATS compatibility';
  };

  return (
    <div className={cn('space-y-6', className)}>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Icons.shield className="h-5 w-5 text-primary" />
            ATS Optimization Analysis
          </CardTitle>
          <CardDescription>
            Analyze your resume for Applicant Tracking System compatibility
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label>Job Description (optional)</Label>
            <Textarea
              placeholder="Paste the job description here for targeted analysis..."
              value={jobDescription}
              onChange={(e) => setJobDescription(e.target.value)}
              rows={4}
              className="text-gray-900 placeholder-gray-500"
            />
            <p className="text-xs text-muted-foreground">
              Adding a job description will provide more targeted keyword analysis and recommendations.
            </p>
          </div>

          <Button 
            onClick={handleAnalyze} 
            disabled={isAnalyzing}
            className="w-full sm:w-auto"
          >
            {isAnalyzing && <LoadingSpinner size="sm" className="mr-2" />}
            {isAnalyzing ? 'Analyzing...' : 'Analyze Resume'}
          </Button>
        </CardContent>
      </Card>

      {analysisData && (
        <Tabs defaultValue="overview" className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="issues">Issues</TabsTrigger>
            <TabsTrigger value="recommendations">Recommendations</TabsTrigger>
            <TabsTrigger value="keywords">Keywords</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>ATS Compatibility Score</CardTitle>
                <CardDescription>
                  Overall assessment of how well your resume will perform with ATS systems
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="text-center">
                    <div className={cn('text-6xl font-bold', getScoreColor(analysisData.score))}>
                      {analysisData.score}
                    </div>
                    <div className="text-lg text-muted-foreground">out of 100</div>
                    <p className="text-sm mt-2">{getScoreDescription(analysisData.score)}</p>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Keyword Match</span>
                        <span className={cn('text-sm font-bold', getScoreColor(analysisData.keywordMatch))}>
                          {analysisData.keywordMatch}%
                        </span>
                      </div>
                      <Progress value={analysisData.keywordMatch} className="h-2" />
                    </div>
                    
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Format Compliance</span>
                        <span className={cn('text-sm font-bold', getScoreColor(analysisData.formatCompliance))}>
                          {analysisData.formatCompliance}%
                        </span>
                      </div>
                      <Progress value={analysisData.formatCompliance} className="h-2" />
                    </div>
                    
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Readability</span>
                        <span className={cn('text-sm font-bold', getScoreColor(analysisData.readabilityScore))}>
                          {analysisData.readabilityScore}%
                        </span>
                      </div>
                      <Progress value={analysisData.readabilityScore} className="h-2" />
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Issues Found</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {Object.entries(
                      analysisData.issues.reduce((acc, issue) => {
                        acc[issue.severity] = (acc[issue.severity] || 0) + 1;
                        return acc;
                      }, {} as Record<IssueSeverity, number>)
                    ).map(([severity, count]) => (
                      <div key={severity} className="flex items-center justify-between">
                        <Badge variant="outline" className={getSeverityColor(severity as IssueSeverity)}>
                          {severity}
                        </Badge>
                        <span className="text-sm font-medium">{count}</span>
                      </div>
                    ))}
                    {analysisData.issues.length === 0 && (
                      <p className="text-sm text-muted-foreground">No issues found!</p>
                    )}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Quick Stats</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Total Issues:</span>
                      <span className="font-medium">{analysisData.issues.length}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Recommendations:</span>
                      <span className="font-medium">{analysisData.recommendations.length}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Critical Issues:</span>
                      <span className="font-medium text-red-600">
                        {analysisData.issues.filter(i => i.severity === IssueSeverity.CRITICAL).length}
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="issues" className="space-y-4">
            {analysisData.issues.length > 0 ? (
              analysisData.issues.map((issue) => (
                <Card key={issue.id} className={cn('border-l-4', getSeverityColor(issue.severity))}>
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-base">{issue.description}</CardTitle>
                      <div className="flex items-center gap-2">
                        <Badge variant="outline" className={getSeverityColor(issue.severity)}>
                          {issue.severity}
                        </Badge>
                        <Badge variant="outline">
                          Impact: {issue.impact}%
                        </Badge>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <div>
                        <span className="text-sm font-medium">Location: </span>
                        <span className="text-sm text-muted-foreground">{issue.location}</span>
                      </div>
                      <div>
                        <span className="text-sm font-medium">Suggestion: </span>
                        <span className="text-sm">{issue.suggestion}</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
            ) : (
              <Card>
                <CardContent className="text-center py-8">
                  <Icons.checkCircle className="h-12 w-12 mx-auto text-green-500 mb-4" />
                  <h3 className="text-lg font-medium mb-2">No Issues Found!</h3>
                  <p className="text-muted-foreground">
                    Your resume looks great from an ATS perspective.
                  </p>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="recommendations" className="space-y-4">
            {analysisData.recommendations.length > 0 ? (
              analysisData.recommendations.map((recommendation) => (
                <Card key={recommendation.id}>
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-base">{recommendation.title}</CardTitle>
                      <div className="flex items-center gap-2">
                        <Badge variant="outline">
                          Priority: {recommendation.priority}/10
                        </Badge>
                        <Badge variant="outline" className={getEffortColor(recommendation.effort)}>
                          {recommendation.effort} effort
                        </Badge>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <p className="text-sm">{recommendation.description}</p>
                      <div>
                        <span className="text-sm font-medium">How to implement: </span>
                        <span className="text-sm text-muted-foreground">{recommendation.implementation}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm">
                          Expected impact: <span className="font-medium">{recommendation.expectedImpact}%</span>
                        </span>
                        {onRecommendationApply && (
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => onRecommendationApply(recommendation)}
                          >
                            Apply Suggestion
                          </Button>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
            ) : (
              <Card>
                <CardContent className="text-center py-8">
                  <Icons.lightBulb className="h-12 w-12 mx-auto text-yellow-500 mb-4" />
                  <h3 className="text-lg font-medium mb-2">No Recommendations</h3>
                  <p className="text-muted-foreground">
                    Your resume is well-optimized! Consider adding a job description for more targeted suggestions.
                  </p>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="keywords" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Keyword Analysis</CardTitle>
                <CardDescription>
                  Analysis of keywords in your resume and suggestions for improvement
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8">
                  <Icons.search className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium mb-2">Keyword Analysis</h3>
                  <p className="text-muted-foreground">
                    Detailed keyword analysis will be available in the next update.
                  </p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      )}
    </div>
  );
}
