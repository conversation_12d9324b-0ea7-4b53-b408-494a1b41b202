/**
 * Career Intelligence System Tests
 * 
 * Comprehensive test suite for Epic 5.0: Career Intelligence Engine
 * Tests profile vectorization, market analysis, and career insights
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { ProfileVectorizer } from '@/lib/career-intelligence/profile-vectorizer'
import { CareerIntelligenceService } from '@/lib/career-intelligence/service'

// Mock dependencies
vi.mock('@/lib/db', () => ({
  prisma: {
    resume: {
      findUnique: vi.fn(),
    },
    userProfileVector: {
      upsert: vi.fn(),
      findUnique: vi.fn(),
    },
    marketAnalysis: {
      create: vi.fn(),
      findMany: vi.fn(),
    },
  },
}))

vi.mock('@/lib/ai/openai', () => ({
  openai: {
    embeddings: {
      create: vi.fn(),
    },
    chat: {
      completions: {
        create: vi.fn(),
      },
    },
  },
}))

// Mock data
const mockResumeData = {
  id: 'resume-123',
  personalInfo: JSON.stringify({
    name: '<PERSON>',
    email: '<EMAIL>',
    location: 'San Francisco, CA',
    summary: 'Experienced software engineer with 5 years in full-stack development'
  }),
  experiences: [
    {
      company: 'Tech Corp',
      position: 'Senior Software Engineer',
      location: 'San Francisco, CA',
      startDate: new Date('2020-01-01'),
      endDate: new Date('2023-12-31'),
      description: 'Led development of React applications',
      achievements: JSON.stringify(['Increased performance by 40%', 'Led team of 5 developers'])
    },
    {
      company: 'StartupXYZ',
      position: 'Software Developer',
      location: 'San Francisco, CA',
      startDate: new Date('2018-06-01'),
      endDate: new Date('2019-12-31'),
      description: 'Built full-stack web applications',
      achievements: JSON.stringify(['Launched 3 major features'])
    }
  ],
  educations: [
    {
      institution: 'UC Berkeley',
      degree: 'Bachelor of Science',
      field: 'Computer Science',
      location: 'Berkeley, CA',
      startDate: new Date('2014-09-01'),
      endDate: new Date('2018-05-31'),
      gpa: '3.8'
    }
  ],
  skills: [
    { name: 'JavaScript', category: 'Programming', level: 'Expert' },
    { name: 'React', category: 'Framework', level: 'Advanced' },
    { name: 'Node.js', category: 'Backend', level: 'Advanced' },
    { name: 'Python', category: 'Programming', level: 'Intermediate' }
  ],
  projects: [
    {
      name: 'E-commerce Platform',
      description: 'Built scalable e-commerce solution',
      technologies: JSON.stringify(['React', 'Node.js', 'PostgreSQL']),
      url: 'https://github.com/johndoe/ecommerce'
    }
  ]
}

const mockEmbedding = Array.from({ length: 1536 }, () => Math.random())

describe('ProfileVectorizer', () => {
  let vectorizer: ProfileVectorizer
  let mockPrisma: any
  let mockOpenAI: any

  beforeEach(() => {
    vectorizer = new ProfileVectorizer()
    mockPrisma = vi.mocked(require('@/lib/db').prisma)
    mockOpenAI = vi.mocked(require('@/lib/ai/openai').openai)
    
    // Reset mocks
    vi.clearAllMocks()
  })

  describe('extractProfileData', () => {
    it('should extract complete profile data from resume', async () => {
      mockPrisma.resume.findUnique.mockResolvedValue(mockResumeData)

      const result = await vectorizer.extractProfileData('resume-123')

      expect(result).toEqual({
        personalInfo: {
          name: 'John Doe',
          email: '<EMAIL>',
          location: 'San Francisco, CA',
          summary: 'Experienced software engineer with 5 years in full-stack development'
        },
        experience: [
          {
            company: 'Tech Corp',
            position: 'Senior Software Engineer',
            location: 'San Francisco, CA',
            startDate: new Date('2020-01-01'),
            endDate: new Date('2023-12-31'),
            description: 'Led development of React applications',
            achievements: ['Increased performance by 40%', 'Led team of 5 developers']
          },
          {
            company: 'StartupXYZ',
            position: 'Software Developer',
            location: 'San Francisco, CA',
            startDate: new Date('2018-06-01'),
            endDate: new Date('2019-12-31'),
            description: 'Built full-stack web applications',
            achievements: ['Launched 3 major features']
          }
        ],
        education: [
          {
            institution: 'UC Berkeley',
            degree: 'Bachelor of Science',
            field: 'Computer Science',
            location: 'Berkeley, CA',
            startDate: new Date('2014-09-01'),
            endDate: new Date('2018-05-31'),
            gpa: '3.8'
          }
        ],
        skills: [
          { name: 'JavaScript', category: 'Programming', level: 'Expert' },
          { name: 'React', category: 'Framework', level: 'Advanced' },
          { name: 'Node.js', category: 'Backend', level: 'Advanced' },
          { name: 'Python', category: 'Programming', level: 'Intermediate' }
        ],
        projects: [
          {
            name: 'E-commerce Platform',
            description: 'Built scalable e-commerce solution',
            technologies: ['React', 'Node.js', 'PostgreSQL'],
            url: 'https://github.com/johndoe/ecommerce'
          }
        ]
      })
    })

    it('should throw error for non-existent resume', async () => {
      mockPrisma.resume.findUnique.mockResolvedValue(null)

      await expect(vectorizer.extractProfileData('invalid-id')).rejects.toThrow('Resume not found: invalid-id')
    })
  })

  describe('generateProfileText', () => {
    it('should generate comprehensive profile text', () => {
      const profileData = {
        personalInfo: {
          summary: 'Experienced software engineer'
        },
        experience: [
          {
            company: 'Tech Corp',
            position: 'Senior Engineer',
            startDate: new Date('2020-01-01'),
            endDate: new Date('2023-12-31'),
            description: 'Led development',
            achievements: ['Improved performance']
          }
        ],
        education: [
          {
            institution: 'UC Berkeley',
            degree: 'BS',
            field: 'Computer Science',
            startDate: new Date('2014-09-01'),
            endDate: new Date('2018-05-31')
          }
        ],
        skills: [
          { name: 'JavaScript', level: 'Expert' },
          { name: 'React', level: 'Advanced' }
        ],
        projects: [
          {
            name: 'E-commerce Platform',
            description: 'Built platform',
            technologies: ['React', 'Node.js']
          }
        ]
      }

      const result = vectorizer.generateProfileText(profileData)

      expect(result).toContain('Professional Summary: Experienced software engineer')
      expect(result).toContain('Work Experience: Senior Engineer at Tech Corp')
      expect(result).toContain('Education: BS in Computer Science from UC Berkeley')
      expect(result).toContain('Skills: JavaScript (Expert), React (Advanced)')
      expect(result).toContain('Projects: E-commerce Platform')
    })
  })

  describe('createEmbedding', () => {
    it('should create vector embedding using OpenAI', async () => {
      mockOpenAI.embeddings.create.mockResolvedValue({
        data: [{ embedding: mockEmbedding }]
      })

      const result = await vectorizer.createEmbedding('test text')

      expect(result).toEqual(mockEmbedding)
      expect(mockOpenAI.embeddings.create).toHaveBeenCalledWith({
        model: 'text-embedding-ada-002',
        input: 'test text',
        encoding_format: 'float'
      })
    })

    it('should handle OpenAI API errors', async () => {
      mockOpenAI.embeddings.create.mockRejectedValue(new Error('API Error'))

      await expect(vectorizer.createEmbedding('test text')).rejects.toThrow('Failed to create profile embedding')
    })
  })

  describe('extractSkills', () => {
    it('should extract skills using AI analysis', async () => {
      const mockSkills = ['JavaScript', 'React', 'Node.js', 'Python']
      mockOpenAI.chat.completions.create.mockResolvedValue({
        choices: [{ message: { content: JSON.stringify(mockSkills) } }]
      })

      const result = await vectorizer.extractSkills('Resume with JavaScript and React experience')

      expect(result).toEqual(mockSkills)
      expect(mockOpenAI.chat.completions.create).toHaveBeenCalledWith({
        model: 'gpt-4',
        messages: [{ role: 'user', content: expect.stringContaining('extract all technical and professional skills') }],
        temperature: 0.1,
        max_tokens: 500
      })
    })

    it('should fallback to regex extraction on AI failure', async () => {
      mockOpenAI.chat.completions.create.mockRejectedValue(new Error('API Error'))

      const result = await vectorizer.extractSkills('Experience with JavaScript, React, and Python development')

      expect(result).toContain('JavaScript')
      expect(result).toContain('React')
      expect(result).toContain('Python')
    })
  })

  describe('determineExperienceLevel', () => {
    it('should determine ENTRY level for new graduates', () => {
      const experience = []
      const result = vectorizer.determineExperienceLevel(experience)
      expect(result).toBe('ENTRY')
    })

    it('should determine MID level for 2-5 years experience', () => {
      const experience = [
        {
          company: 'Company A',
          position: 'Developer',
          startDate: new Date('2021-01-01'),
          endDate: new Date('2023-12-31')
        }
      ]
      const result = vectorizer.determineExperienceLevel(experience)
      expect(result).toBe('MID')
    })

    it('should determine SENIOR level for 5+ years experience', () => {
      const experience = [
        {
          company: 'Company A',
          position: 'Developer',
          startDate: new Date('2018-01-01'),
          endDate: new Date('2023-12-31')
        }
      ]
      const result = vectorizer.determineExperienceLevel(experience)
      expect(result).toBe('SENIOR')
    })

    it('should determine EXECUTIVE level for leadership roles', () => {
      const experience = [
        {
          company: 'Company A',
          position: 'Director of Engineering',
          startDate: new Date('2020-01-01'),
          endDate: new Date('2023-12-31')
        }
      ]
      const result = vectorizer.determineExperienceLevel(experience)
      expect(result).toBe('EXECUTIVE')
    })
  })

  describe('extractPrimaryRole', () => {
    it('should extract most recent role as primary', () => {
      const experience = [
        {
          company: 'Old Company',
          position: 'Junior Developer',
          startDate: new Date('2018-01-01'),
          endDate: new Date('2020-12-31')
        },
        {
          company: 'Current Company',
          position: 'Senior Engineer',
          startDate: new Date('2021-01-01'),
          endDate: new Date('2023-12-31')
        }
      ]
      const result = vectorizer.extractPrimaryRole(experience)
      expect(result).toBe('Senior Engineer')
    })

    it('should return default for no experience', () => {
      const result = vectorizer.extractPrimaryRole([])
      expect(result).toBe('Professional')
    })
  })
})

describe('CareerIntelligenceService', () => {
  let service: CareerIntelligenceService
  let mockPrisma: any

  beforeEach(() => {
    service = new CareerIntelligenceService()
    mockPrisma = vi.mocked(require('@/lib/db').prisma)
    vi.clearAllMocks()
  })

  describe('generateCareerInsights', () => {
    it('should generate comprehensive career insights', async () => {
      // Mock profile vector creation
      const mockProfileVector = {
        vector: mockEmbedding,
        skillsExtracted: ['JavaScript', 'React', 'Node.js'],
        experienceLevel: 'SENIOR' as const,
        primaryRole: 'Senior Software Engineer',
        industries: ['Technology'],
        locations: ['San Francisco, CA']
      }

      // Mock database calls
      mockPrisma.resume.findUnique.mockResolvedValue(mockResumeData)
      mockPrisma.userProfileVector.upsert.mockResolvedValue({})
      mockPrisma.marketAnalysis.create.mockResolvedValue({})

      // Mock OpenAI calls
      const mockOpenAI = vi.mocked(require('@/lib/ai/openai').openai)
      mockOpenAI.embeddings.create.mockResolvedValue({
        data: [{ embedding: mockEmbedding }]
      })
      mockOpenAI.chat.completions.create.mockResolvedValue({
        choices: [{ message: { content: JSON.stringify(['JavaScript', 'React', 'Node.js']) } }]
      })

      const result = await service.generateCareerInsights('user-123', 'resume-123')

      expect(result).toHaveProperty('salaryEstimate')
      expect(result).toHaveProperty('marketFit')
      expect(result).toHaveProperty('skillGaps')
      expect(result).toHaveProperty('careerPaths')
      expect(result).toHaveProperty('marketData')

      // Validate salary estimate structure
      expect(result.salaryEstimate).toHaveProperty('min')
      expect(result.salaryEstimate).toHaveProperty('max')
      expect(result.salaryEstimate).toHaveProperty('confidence')
      expect(result.salaryEstimate).toHaveProperty('currency')

      // Validate market fit structure
      expect(result.marketFit).toHaveProperty('score')
      expect(result.marketFit).toHaveProperty('description')
      expect(result.marketFit).toHaveProperty('strengths')
      expect(result.marketFit).toHaveProperty('improvements')

      // Validate skill gaps structure
      expect(result.skillGaps).toHaveProperty('missing')
      expect(result.skillGaps).toHaveProperty('recommended')
      expect(result.skillGaps).toHaveProperty('trending')

      // Validate career paths structure
      expect(result.careerPaths).toHaveProperty('current')
      expect(result.careerPaths).toHaveProperty('nextSteps')
      expect(result.careerPaths).toHaveProperty('alternatives')

      // Validate market data structure
      expect(result.marketData).toHaveProperty('totalJobs')
      expect(result.marketData).toHaveProperty('averageSalary')
      expect(result.marketData).toHaveProperty('topCompanies')
      expect(result.marketData).toHaveProperty('hotSkills')
      expect(result.marketData).toHaveProperty('locations')
    })

    it('should handle errors gracefully', async () => {
      mockPrisma.resume.findUnique.mockRejectedValue(new Error('Database error'))

      await expect(service.generateCareerInsights('user-123', 'resume-123')).rejects.toThrow('Failed to generate career insights')
    })
  })

  describe('createOrUpdateProfileVector', () => {
    it('should create new profile vector', async () => {
      mockPrisma.resume.findUnique.mockResolvedValue(mockResumeData)
      mockPrisma.userProfileVector.upsert.mockResolvedValue({})

      const mockOpenAI = vi.mocked(require('@/lib/ai/openai').openai)
      mockOpenAI.embeddings.create.mockResolvedValue({
        data: [{ embedding: mockEmbedding }]
      })
      mockOpenAI.chat.completions.create.mockResolvedValue({
        choices: [{ message: { content: JSON.stringify(['JavaScript', 'React']) } }]
      })

      const result = await service.createOrUpdateProfileVector('user-123', 'resume-123')

      expect(result).toHaveProperty('vector')
      expect(result).toHaveProperty('skillsExtracted')
      expect(result).toHaveProperty('experienceLevel')
      expect(result).toHaveProperty('primaryRole')
      expect(result).toHaveProperty('industries')
      expect(result).toHaveProperty('locations')

      expect(mockPrisma.userProfileVector.upsert).toHaveBeenCalledWith({
        where: {
          userId_resumeId: {
            userId: 'user-123',
            resumeId: 'resume-123'
          }
        },
        update: expect.objectContaining({
          profileVector: expect.any(String),
          skillsExtracted: expect.any(String),
          experienceLevel: expect.any(String),
          primaryRole: expect.any(String),
          industries: expect.any(String),
          locations: expect.any(String),
          lastUpdated: expect.any(Date)
        }),
        create: expect.objectContaining({
          userId: 'user-123',
          resumeId: 'resume-123',
          profileVector: expect.any(String),
          skillsExtracted: expect.any(String),
          experienceLevel: expect.any(String),
          primaryRole: expect.any(String),
          industries: expect.any(String),
          locations: expect.any(String)
        })
      })
    })
  })
})

describe('Career Intelligence API Integration', () => {
  it('should validate salary estimation accuracy', () => {
    const service = new CareerIntelligenceService()
    
    // Test salary calculation logic
    const seniorEngineerSalary = service['getBaseSalaryForRole']('Senior Software Engineer')
    const experienceMultiplier = service['getExperienceMultiplier']('SENIOR')
    
    expect(seniorEngineerSalary).toBeGreaterThan(80000)
    expect(experienceMultiplier).toBeGreaterThan(1.0)
  })

  it('should validate market fit scoring', () => {
    const service = new CareerIntelligenceService()
    
    const description = service['getMarketFitDescription'](0.85)
    expect(description).toContain('Strong market fit')
    
    const lowDescription = service['getMarketFitDescription'](0.5)
    expect(lowDescription).toContain('Developing market fit')
  })

  it('should validate skill extraction patterns', () => {
    const vectorizer = new ProfileVectorizer()
    
    const skills = vectorizer['extractSkillsFallback']('Experience with JavaScript, React, Python, and AWS development')
    
    expect(skills).toContain('JavaScript')
    expect(skills).toContain('React')
    expect(skills).toContain('Python')
    expect(skills).toContain('AWS')
  })

  it('should validate career progression logic', () => {
    const service = new CareerIntelligenceService()
    
    const nextSteps = service['getNextStepsForRole']('Software Engineer', 'MID')
    
    expect(nextSteps).toHaveLength(2)
    expect(nextSteps[0].role).toContain('Senior')
    expect(nextSteps[1].role).toContain('Lead')
  })
})

describe('Performance and Scalability', () => {
  it('should handle large skill sets efficiently', () => {
    const vectorizer = new ProfileVectorizer()
    const largeSkillSet = Array.from({ length: 100 }, (_, i) => `Skill${i}`)
    
    const profileData = {
      personalInfo: { summary: 'Test' },
      experience: [],
      education: [],
      skills: largeSkillSet.map(skill => ({ name: skill })),
      projects: []
    }
    
    const text = vectorizer.generateProfileText(profileData)
    expect(text).toContain('Skills:')
    expect(text.length).toBeGreaterThan(100)
  })

  it('should validate vector dimension consistency', async () => {
    const vectorizer = new ProfileVectorizer()
    expect(vectorizer['VECTOR_DIMENSION']).toBe(1536) // OpenAI embedding dimension
  })
})
