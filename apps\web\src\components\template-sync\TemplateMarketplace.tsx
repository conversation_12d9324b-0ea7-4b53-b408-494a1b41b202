'use client'

import { useState, useEffect } from 'react'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Separator } from '@/components/ui/separator'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Textarea } from '@/components/ui/textarea'
import { 
  Search, 
  Star, 
  Download, 
  Heart, 
  ShoppingCart,
  Filter,
  SortAsc,
  Eye,
  TrendingUp,
  Award,
  Crown,
  DollarSign,
  Users,
  Calendar,
  ExternalLink
} from 'lucide-react'
import { formatDistanceToNow } from 'date-fns'
import { toast } from 'sonner'

interface MarketplaceTemplate {
  id: string
  name: string
  description?: string
  category?: string
  tags: string[]
  preview?: string
  thumbnail?: string
  difficulty?: string
  price: number
  rating: number
  reviewCount: number
  downloadCount: number
  isFeatured: boolean
  seller: {
    id: string
    name?: string
    email: string
  }
  createdAt: Date
  updatedAt: Date
}

interface TemplateCategory {
  id: string
  name: string
  count: number
}

interface TemplateMarketplaceProps {
  className?: string
  onTemplateSelect?: (template: MarketplaceTemplate) => void
}

export function TemplateMarketplace({ className, onTemplateSelect }: TemplateMarketplaceProps) {
  const [templates, setTemplates] = useState<MarketplaceTemplate[]>([])
  const [featuredTemplates, setFeaturedTemplates] = useState<MarketplaceTemplate[]>([])
  const [categories, setCategories] = useState<TemplateCategory[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCategory, setSelectedCategory] = useState<string>('')
  const [priceRange, setPriceRange] = useState<{ min: number; max: number }>({ min: 0, max: 100 })
  const [sortBy, setSortBy] = useState<string>('popular')
  const [showFilters, setShowFilters] = useState(false)
  const [selectedTemplate, setSelectedTemplate] = useState<MarketplaceTemplate | null>(null)
  const [reviewText, setReviewText] = useState('')
  const [reviewRating, setReviewRating] = useState(5)

  useEffect(() => {
    loadFeaturedTemplates()
    loadCategories()
    searchTemplates()
  }, [])

  useEffect(() => {
    searchTemplates()
  }, [searchQuery, selectedCategory, sortBy])

  const loadFeaturedTemplates = async () => {
    try {
      const response = await fetch('/api/template-marketplace?action=featured&limit=6')
      
      if (!response.ok) {
        throw new Error('Failed to load featured templates')
      }

      const data = await response.json()
      setFeaturedTemplates(data.templates.map((template: any) => ({
        ...template,
        createdAt: new Date(template.createdAt),
        updatedAt: new Date(template.updatedAt)
      })))
    } catch (error) {
      console.error('Error loading featured templates:', error)
    }
  }

  const loadCategories = async () => {
    try {
      const response = await fetch('/api/template-marketplace?action=categories')
      
      if (!response.ok) {
        throw new Error('Failed to load categories')
      }

      const data = await response.json()
      setCategories(data.categories)
    } catch (error) {
      console.error('Error loading categories:', error)
    }
  }

  const searchTemplates = async () => {
    try {
      setLoading(true)
      
      const params = new URLSearchParams({
        action: 'search',
        ...(searchQuery && { query: searchQuery }),
        ...(selectedCategory && { category: selectedCategory }),
        sortBy,
        limit: '20',
        offset: '0'
      })

      const response = await fetch(`/api/template-marketplace?${params}`)
      
      if (!response.ok) {
        throw new Error('Failed to search templates')
      }

      const data = await response.json()
      setTemplates(data.templates.map((template: any) => ({
        ...template,
        createdAt: new Date(template.createdAt),
        updatedAt: new Date(template.updatedAt)
      })))
    } catch (error) {
      console.error('Error searching templates:', error)
      toast.error('Failed to search templates')
    } finally {
      setLoading(false)
    }
  }

  const purchaseTemplate = async (templateId: string) => {
    try {
      const response = await fetch('/api/template-marketplace', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'purchase',
          templateId
        })
      })

      if (!response.ok) {
        throw new Error('Failed to purchase template')
      }

      const data = await response.json()
      
      if (data.success) {
        toast.success('Template purchased successfully!')
        // Handle template data
        if (onTemplateSelect && data.templateData) {
          onTemplateSelect(data.templateData)
        }
      } else {
        toast.error('Purchase failed')
      }
    } catch (error) {
      console.error('Error purchasing template:', error)
      toast.error('Failed to purchase template')
    }
  }

  const addReview = async (templateId: string) => {
    try {
      const response = await fetch('/api/template-marketplace', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'review',
          templateId,
          rating: reviewRating,
          reviewText
        })
      })

      if (!response.ok) {
        throw new Error('Failed to add review')
      }

      toast.success('Review added successfully!')
      setReviewText('')
      setReviewRating(5)
      setSelectedTemplate(null)
    } catch (error) {
      console.error('Error adding review:', error)
      toast.error('Failed to add review')
    }
  }

  const trackTemplateView = async (templateId: string) => {
    try {
      await fetch('/api/template-marketplace', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'track-usage',
          templateId,
          actionType: 'view',
          metadata: {
            deviceType: 'desktop',
            userAgent: navigator.userAgent
          }
        })
      })
    } catch (error) {
      console.error('Error tracking template view:', error)
    }
  }

  const formatPrice = (price: number) => {
    return price === 0 ? 'Free' : `$${price.toFixed(2)}`
  }

  const getDifficultyColor = (difficulty?: string) => {
    switch (difficulty) {
      case 'beginner': return 'bg-green-100 text-green-800'
      case 'intermediate': return 'bg-yellow-100 text-yellow-800'
      case 'advanced': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`w-4 h-4 ${
          i < Math.floor(rating) 
            ? 'text-yellow-400 fill-current' 
            : 'text-gray-300'
        }`}
      />
    ))
  }

  return (
    <div className={className}>
      {/* Header */}
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Template Marketplace</h1>
        <p className="text-gray-600">Discover and download professional resume templates</p>
      </div>

      {/* Search and Filters */}
      <Card className="mb-6">
        <CardContent className="p-4">
          <div className="flex space-x-4 mb-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Search templates..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={selectedCategory} onValueChange={setSelectedCategory}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="All Categories" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">All Categories</SelectItem>
                {categories.map((category) => (
                  <SelectItem key={category.id} value={category.id}>
                    {category.name} ({category.count})
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger className="w-40">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="popular">Most Popular</SelectItem>
                <SelectItem value="newest">Newest</SelectItem>
                <SelectItem value="rating">Highest Rated</SelectItem>
                <SelectItem value="price_low">Price: Low to High</SelectItem>
                <SelectItem value="price_high">Price: High to Low</SelectItem>
              </SelectContent>
            </Select>
            <Button
              variant="outline"
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center space-x-1"
            >
              <Filter className="w-4 h-4" />
              <span>Filters</span>
            </Button>
          </div>

          {showFilters && (
            <div className="border-t pt-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <Label>Price Range</Label>
                  <div className="flex items-center space-x-2 mt-1">
                    <Input
                      type="number"
                      placeholder="Min"
                      value={priceRange.min}
                      onChange={(e) => setPriceRange(prev => ({ ...prev, min: parseFloat(e.target.value) || 0 }))}
                      className="w-20"
                    />
                    <span>-</span>
                    <Input
                      type="number"
                      placeholder="Max"
                      value={priceRange.max}
                      onChange={(e) => setPriceRange(prev => ({ ...prev, max: parseFloat(e.target.value) || 100 }))}
                      className="w-20"
                    />
                  </div>
                </div>
                <div>
                  <Label>Difficulty</Label>
                  <Select>
                    <SelectTrigger className="mt-1">
                      <SelectValue placeholder="Any Difficulty" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">Any Difficulty</SelectItem>
                      <SelectItem value="beginner">Beginner</SelectItem>
                      <SelectItem value="intermediate">Intermediate</SelectItem>
                      <SelectItem value="advanced">Advanced</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label>Rating</Label>
                  <Select>
                    <SelectTrigger className="mt-1">
                      <SelectValue placeholder="Any Rating" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">Any Rating</SelectItem>
                      <SelectItem value="4">4+ Stars</SelectItem>
                      <SelectItem value="3">3+ Stars</SelectItem>
                      <SelectItem value="2">2+ Stars</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      <Tabs defaultValue="browse" className="space-y-4">
        <TabsList>
          <TabsTrigger value="browse">Browse Templates</TabsTrigger>
          <TabsTrigger value="featured">Featured</TabsTrigger>
          <TabsTrigger value="collections">Collections</TabsTrigger>
        </TabsList>

        <TabsContent value="browse">
          {loading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[...Array(6)].map((_, i) => (
                <Card key={i} className="animate-pulse">
                  <div className="h-48 bg-gray-200 rounded-t-lg" />
                  <CardContent className="p-4">
                    <div className="space-y-2">
                      <div className="h-4 bg-gray-200 rounded w-3/4" />
                      <div className="h-3 bg-gray-200 rounded w-1/2" />
                      <div className="h-3 bg-gray-200 rounded w-2/3" />
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {templates.map((template) => (
                <Card key={template.id} className="group hover:shadow-lg transition-shadow">
                  <div className="relative">
                    {template.thumbnail ? (
                      <img
                        src={template.thumbnail}
                        alt={template.name}
                        className="w-full h-48 object-cover rounded-t-lg"
                      />
                    ) : (
                      <div className="w-full h-48 bg-gradient-to-br from-blue-100 to-purple-100 rounded-t-lg flex items-center justify-center">
                        <span className="text-gray-500">No Preview</span>
                      </div>
                    )}
                    
                    {template.isFeatured && (
                      <Badge className="absolute top-2 left-2 bg-yellow-500">
                        <Crown className="w-3 h-3 mr-1" />
                        Featured
                      </Badge>
                    )}
                    
                    <div className="absolute top-2 right-2 flex space-x-1">
                      <Button
                        size="sm"
                        variant="secondary"
                        className="opacity-0 group-hover:opacity-100 transition-opacity"
                        onClick={() => {
                          trackTemplateView(template.id)
                          onTemplateSelect?.(template)
                        }}
                      >
                        <Eye className="w-4 h-4" />
                      </Button>
                      <Button
                        size="sm"
                        variant="secondary"
                        className="opacity-0 group-hover:opacity-100 transition-opacity"
                      >
                        <Heart className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                  
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between mb-2">
                      <h3 className="font-semibold text-gray-900 line-clamp-1">
                        {template.name}
                      </h3>
                      <div className="text-lg font-bold text-green-600">
                        {formatPrice(template.price)}
                      </div>
                    </div>
                    
                    <p className="text-sm text-gray-600 mb-3 line-clamp-2">
                      {template.description || 'Professional resume template'}
                    </p>
                    
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center space-x-1">
                        {renderStars(template.rating)}
                        <span className="text-sm text-gray-600 ml-1">
                          ({template.reviewCount})
                        </span>
                      </div>
                      <div className="flex items-center space-x-1 text-sm text-gray-500">
                        <Download className="w-4 h-4" />
                        <span>{template.downloadCount}</span>
                      </div>
                    </div>
                    
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center space-x-2">
                        {template.difficulty && (
                          <Badge className={getDifficultyColor(template.difficulty)}>
                            {template.difficulty}
                          </Badge>
                        )}
                        {template.category && (
                          <Badge variant="outline">
                            {template.category}
                          </Badge>
                        )}
                      </div>
                      <div className="flex items-center space-x-1 text-xs text-gray-500">
                        <Users className="w-3 h-3" />
                        <span>{template.seller.name || 'Anonymous'}</span>
                      </div>
                    </div>
                    
                    <div className="flex space-x-2">
                      <Button
                        className="flex-1"
                        onClick={() => purchaseTemplate(template.id)}
                      >
                        <ShoppingCart className="w-4 h-4 mr-1" />
                        {template.price === 0 ? 'Download' : 'Purchase'}
                      </Button>
                      <Dialog>
                        <DialogTrigger asChild>
                          <Button variant="outline" size="sm">
                            <Star className="w-4 h-4" />
                          </Button>
                        </DialogTrigger>
                        <DialogContent>
                          <DialogHeader>
                            <DialogTitle>Rate Template</DialogTitle>
                            <DialogDescription>
                              Share your experience with this template
                            </DialogDescription>
                          </DialogHeader>
                          <div className="space-y-4">
                            <div>
                              <Label>Rating</Label>
                              <div className="flex space-x-1 mt-1">
                                {[1, 2, 3, 4, 5].map((star) => (
                                  <button
                                    key={star}
                                    onClick={() => setReviewRating(star)}
                                    className="focus:outline-none"
                                  >
                                    <Star
                                      className={`w-6 h-6 ${
                                        star <= reviewRating
                                          ? 'text-yellow-400 fill-current'
                                          : 'text-gray-300'
                                      }`}
                                    />
                                  </button>
                                ))}
                              </div>
                            </div>
                            <div>
                              <Label htmlFor="review">Review (Optional)</Label>
                              <Textarea
                                id="review"
                                placeholder="Share your thoughts about this template..."
                                value={reviewText}
                                onChange={(e) => setReviewText(e.target.value)}
                              />
                            </div>
                          </div>
                          <DialogFooter>
                            <Button onClick={() => addReview(template.id)}>
                              Submit Review
                            </Button>
                          </DialogFooter>
                        </DialogContent>
                      </Dialog>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="featured">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {featuredTemplates.map((template) => (
              <Card key={template.id} className="group hover:shadow-lg transition-shadow">
                <div className="relative">
                  {template.thumbnail ? (
                    <img
                      src={template.thumbnail}
                      alt={template.name}
                      className="w-full h-48 object-cover rounded-t-lg"
                    />
                  ) : (
                    <div className="w-full h-48 bg-gradient-to-br from-yellow-100 to-orange-100 rounded-t-lg flex items-center justify-center">
                      <Crown className="w-12 h-12 text-yellow-500" />
                    </div>
                  )}
                  
                  <Badge className="absolute top-2 left-2 bg-yellow-500">
                    <Crown className="w-3 h-3 mr-1" />
                    Featured
                  </Badge>
                </div>
                
                <CardContent className="p-4">
                  <div className="flex items-start justify-between mb-2">
                    <h3 className="font-semibold text-gray-900">
                      {template.name}
                    </h3>
                    <div className="text-lg font-bold text-green-600">
                      {formatPrice(template.price)}
                    </div>
                  </div>
                  
                  <p className="text-sm text-gray-600 mb-3">
                    {template.description || 'Premium featured template'}
                  </p>
                  
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-1">
                      {renderStars(template.rating)}
                      <span className="text-sm text-gray-600 ml-1">
                        ({template.reviewCount})
                      </span>
                    </div>
                    <div className="flex items-center space-x-1 text-sm text-gray-500">
                      <TrendingUp className="w-4 h-4" />
                      <span>Trending</span>
                    </div>
                  </div>
                  
                  <Button
                    className="w-full"
                    onClick={() => purchaseTemplate(template.id)}
                  >
                    <Award className="w-4 h-4 mr-1" />
                    Get Featured Template
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="collections">
          <div className="text-center py-12">
            <Users className="w-12 h-12 mx-auto text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Template Collections</h3>
            <p className="text-gray-500 mb-4">
              Curated collections of templates for specific industries and roles
            </p>
            <Button variant="outline">
              Browse Collections
            </Button>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
