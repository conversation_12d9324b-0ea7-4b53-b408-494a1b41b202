/**
 * LinkedIn API Client
 * 
 * Handles LinkedIn OAuth integration and profile data fetching
 * for auto-populating resume information from LinkedIn profiles.
 */

import { z } from 'zod'

// LinkedIn API Configuration
const LINKEDIN_CONFIG = {
  clientId: process.env.LINKEDIN_CLIENT_ID!,
  clientSecret: process.env.LINKEDIN_CLIENT_SECRET!,
  redirectUri: process.env.LINKEDIN_REDIRECT_URI!,
  scope: 'r_liteprofile r_emailaddress w_member_social',
  apiVersion: 'v2'
}

// LinkedIn Profile Data Schema
const LinkedInProfileSchema = z.object({
  id: z.string(),
  firstName: z.object({
    localized: z.record(z.string()),
    preferredLocale: z.object({
      country: z.string(),
      language: z.string()
    })
  }),
  lastName: z.object({
    localized: z.record(z.string()),
    preferredLocale: z.object({
      country: z.string(),
      language: z.string()
    })
  }),
  profilePicture: z.object({
    displayImage: z.string()
  }).optional(),
  headline: z.string().optional(),
  summary: z.string().optional(),
  location: z.object({
    name: z.string()
  }).optional(),
  industry: z.string().optional(),
  positions: z.object({
    values: z.array(z.object({
      id: z.number(),
      title: z.string(),
      summary: z.string().optional(),
      startDate: z.object({
        month: z.number().optional(),
        year: z.number()
      }),
      endDate: z.object({
        month: z.number().optional(),
        year: z.number()
      }).optional(),
      isCurrent: z.boolean().optional(),
      company: z.object({
        id: z.number(),
        name: z.string(),
        size: z.string().optional(),
        type: z.string().optional(),
        industry: z.string().optional()
      })
    }))
  }).optional(),
  educations: z.object({
    values: z.array(z.object({
      id: z.number(),
      schoolName: z.string(),
      fieldOfStudy: z.string().optional(),
      degree: z.string().optional(),
      startDate: z.object({
        year: z.number()
      }).optional(),
      endDate: z.object({
        year: z.number()
      }).optional(),
      grade: z.string().optional(),
      activities: z.string().optional()
    }))
  }).optional(),
  skills: z.object({
    values: z.array(z.object({
      id: z.number(),
      skill: z.object({
        name: z.string()
      })
    }))
  }).optional()
})

export type LinkedInProfile = z.infer<typeof LinkedInProfileSchema>

// Transformed Resume Data Schema
export const ResumeDataSchema = z.object({
  personalInfo: z.object({
    firstName: z.string(),
    lastName: z.string(),
    email: z.string().optional(),
    phone: z.string().optional(),
    location: z.string().optional(),
    profileImage: z.string().optional(),
    headline: z.string().optional(),
    summary: z.string().optional()
  }),
  experience: z.array(z.object({
    id: z.string(),
    company: z.string(),
    position: z.string(),
    startDate: z.string(),
    endDate: z.string().optional(),
    current: z.boolean().optional(),
    description: z.string().optional(),
    location: z.string().optional(),
    industry: z.string().optional()
  })),
  education: z.array(z.object({
    id: z.string(),
    institution: z.string(),
    degree: z.string().optional(),
    fieldOfStudy: z.string().optional(),
    startDate: z.string().optional(),
    endDate: z.string().optional(),
    grade: z.string().optional(),
    activities: z.string().optional()
  })),
  skills: z.array(z.object({
    id: z.string(),
    name: z.string(),
    category: z.string().optional()
  }))
})

export type ResumeData = z.infer<typeof ResumeDataSchema>

export class LinkedInClient {
  private baseUrl = 'https://api.linkedin.com'
  
  /**
   * Generate LinkedIn OAuth authorization URL
   */
  getAuthorizationUrl(state?: string): string {
    const params = new URLSearchParams({
      response_type: 'code',
      client_id: LINKEDIN_CONFIG.clientId,
      redirect_uri: LINKEDIN_CONFIG.redirectUri,
      scope: LINKEDIN_CONFIG.scope,
      ...(state && { state })
    })

    return `https://www.linkedin.com/oauth/v2/authorization?${params.toString()}`
  }

  /**
   * Exchange authorization code for access token
   */
  async getAccessToken(code: string): Promise<string> {
    const response = await fetch('https://www.linkedin.com/oauth/v2/accessToken', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        grant_type: 'authorization_code',
        code,
        client_id: LINKEDIN_CONFIG.clientId,
        client_secret: LINKEDIN_CONFIG.clientSecret,
        redirect_uri: LINKEDIN_CONFIG.redirectUri,
      }),
    })

    if (!response.ok) {
      const error = await response.text()
      throw new Error(`Failed to get access token: ${error}`)
    }

    const data = await response.json()
    return data.access_token
  }

  /**
   * Fetch LinkedIn profile data
   */
  async getProfile(accessToken: string): Promise<LinkedInProfile> {
    const profileFields = [
      'id',
      'firstName',
      'lastName',
      'profilePicture(displayImage~:playableStreams)',
      'headline',
      'summary',
      'location',
      'industry'
    ].join(',')

    const response = await fetch(
      `${this.baseUrl}/v2/people/~:(${profileFields})`,
      {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'X-Restli-Protocol-Version': '2.0.0',
        },
      }
    )

    if (!response.ok) {
      const error = await response.text()
      throw new Error(`Failed to fetch profile: ${error}`)
    }

    const data = await response.json()
    return LinkedInProfileSchema.parse(data)
  }

  /**
   * Fetch LinkedIn positions (work experience)
   */
  async getPositions(accessToken: string): Promise<any> {
    const response = await fetch(
      `${this.baseUrl}/v2/positions`,
      {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'X-Restli-Protocol-Version': '2.0.0',
        },
      }
    )

    if (!response.ok) {
      const error = await response.text()
      throw new Error(`Failed to fetch positions: ${error}`)
    }

    return response.json()
  }

  /**
   * Fetch LinkedIn educations
   */
  async getEducations(accessToken: string): Promise<any> {
    const response = await fetch(
      `${this.baseUrl}/v2/educations`,
      {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'X-Restli-Protocol-Version': '2.0.0',
        },
      }
    )

    if (!response.ok) {
      const error = await response.text()
      throw new Error(`Failed to fetch educations: ${error}`)
    }

    return response.json()
  }

  /**
   * Fetch LinkedIn skills
   */
  async getSkills(accessToken: string): Promise<any> {
    const response = await fetch(
      `${this.baseUrl}/v2/skills`,
      {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'X-Restli-Protocol-Version': '2.0.0',
        },
      }
    )

    if (!response.ok) {
      const error = await response.text()
      throw new Error(`Failed to fetch skills: ${error}`)
    }

    return response.json()
  }

  /**
   * Transform LinkedIn profile data to resume format
   */
  transformToResumeData(
    profile: LinkedInProfile,
    positions?: any,
    educations?: any,
    skills?: any
  ): ResumeData {
    // Extract name from localized fields
    const firstName = Object.values(profile.firstName.localized)[0] || ''
    const lastName = Object.values(profile.lastName.localized)[0] || ''

    // Transform personal information
    const personalInfo = {
      firstName,
      lastName,
      location: profile.location?.name,
      profileImage: profile.profilePicture?.displayImage,
      headline: profile.headline,
      summary: profile.summary
    }

    // Transform work experience
    const experience = positions?.values?.map((pos: any, index: number) => ({
      id: `linkedin-exp-${pos.id || index}`,
      company: pos.company.name,
      position: pos.title,
      startDate: pos.startDate ? `${pos.startDate.year}-${pos.startDate.month || 1}-01` : '',
      endDate: pos.endDate ? `${pos.endDate.year}-${pos.endDate.month || 12}-01` : undefined,
      current: pos.isCurrent || false,
      description: pos.summary,
      industry: pos.company.industry
    })) || []

    // Transform education
    const education = educations?.values?.map((edu: any, index: number) => ({
      id: `linkedin-edu-${edu.id || index}`,
      institution: edu.schoolName,
      degree: edu.degree,
      fieldOfStudy: edu.fieldOfStudy,
      startDate: edu.startDate ? `${edu.startDate.year}-01-01` : undefined,
      endDate: edu.endDate ? `${edu.endDate.year}-12-01` : undefined,
      grade: edu.grade,
      activities: edu.activities
    })) || []

    // Transform skills
    const skillsData = skills?.values?.map((skill: any, index: number) => ({
      id: `linkedin-skill-${skill.id || index}`,
      name: skill.skill.name,
      category: 'Professional' // Default category
    })) || []

    return {
      personalInfo,
      experience,
      education,
      skills: skillsData
    }
  }
}

export const linkedInClient = new LinkedInClient()
