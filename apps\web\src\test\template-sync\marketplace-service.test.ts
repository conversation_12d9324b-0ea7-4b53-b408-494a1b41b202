/**
 * Template Marketplace Service Unit Tests
 * 
 * Tests for template marketplace operations, discovery, and transactions
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { TemplateMarketplaceService } from '@/lib/template-sync/marketplace-service'

// Mock Prisma
const mockPrisma = {
  template: {
    findMany: vi.fn(),
    findUnique: vi.fn(),
    update: vi.fn(),
    count: vi.fn()
  },
  templateMarketplace: {
    create: vi.fn(),
    findUnique: vi.fn(),
    update: vi.fn()
  },
  templatePurchase: {
    findUnique: vi.fn(),
    findFirst: vi.fn(),
    create: vi.fn(),
    count: vi.fn()
  },
  templateReview: {
    upsert: vi.fn(),
    findMany: vi.fn(),
    findMany: vi.fn()
  },
  templateCollection: {
    create: vi.fn(),
    findMany: vi.fn()
  },
  templateCollectionItem: {
    createMany: vi.fn()
  },
  templateUsageAnalytics: {
    create: vi.fn(),
    findMany: vi.fn()
  },
  user: {
    findUnique: vi.fn()
  }
}

vi.mock('@/lib/db', () => ({
  prisma: mockPrisma
}))

describe('TemplateMarketplaceService', () => {
  let service: TemplateMarketplaceService

  beforeEach(() => {
    service = new TemplateMarketplaceService()
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('searchTemplates', () => {
    const mockTemplates = [
      {
        id: 'template-1',
        name: 'Professional Resume',
        description: 'Clean professional template',
        category: 'professional',
        tags: JSON.stringify(['modern', 'clean']),
        preview: 'preview1.jpg',
        thumbnail: 'thumb1.jpg',
        difficulty: 'beginner',
        rating: 4.5,
        usageCount: 150,
        createdBy: 'user-1',
        createdAt: new Date(),
        updatedAt: new Date(),
        marketplaceListing: {
          price: 0,
          downloadCount: 100,
          isFeatured: false,
          status: 'approved'
        },
        _count: {
          reviews: 25
        }
      },
      {
        id: 'template-2',
        name: 'Creative Portfolio',
        description: 'Creative design template',
        category: 'creative',
        tags: JSON.stringify(['creative', 'portfolio']),
        preview: 'preview2.jpg',
        thumbnail: 'thumb2.jpg',
        difficulty: 'intermediate',
        rating: 4.8,
        usageCount: 200,
        createdBy: 'user-2',
        createdAt: new Date(),
        updatedAt: new Date(),
        marketplaceListing: {
          price: 9.99,
          downloadCount: 75,
          isFeatured: true,
          status: 'approved'
        },
        _count: {
          reviews: 40
        }
      }
    ]

    beforeEach(() => {
      mockPrisma.template.findMany.mockResolvedValue(mockTemplates)
      mockPrisma.template.count.mockResolvedValue(2)
      mockPrisma.user.findUnique
        .mockResolvedValueOnce({ id: 'user-1', name: 'John Doe', email: '<EMAIL>' })
        .mockResolvedValueOnce({ id: 'user-2', name: 'Jane Smith', email: '<EMAIL>' })
    })

    it('should search templates with basic criteria', async () => {
      const criteria = {
        query: 'professional',
        limit: 20,
        offset: 0,
        sortBy: 'popular' as const
      }

      const result = await service.searchTemplates(criteria)

      expect(mockPrisma.template.findMany).toHaveBeenCalledWith({
        where: {
          marketplaceListing: {
            status: 'approved'
          },
          isActive: true,
          OR: [
            { name: { contains: 'professional', mode: 'insensitive' } },
            { description: { contains: 'professional', mode: 'insensitive' } }
          ]
        },
        include: {
          marketplaceListing: true,
          _count: {
            select: {
              reviews: true
            }
          }
        },
        orderBy: { usageCount: 'desc' },
        take: 20,
        skip: 0
      })

      expect(result.templates).toHaveLength(2)
      expect(result.total).toBe(2)
      expect(result.hasMore).toBe(false)
      expect(result.templates[0].tags).toEqual(['modern', 'clean'])
      expect(result.templates[0].seller.name).toBe('John Doe')
    })

    it('should filter by category', async () => {
      const criteria = {
        category: 'creative',
        limit: 20,
        offset: 0
      }

      await service.searchTemplates(criteria)

      expect(mockPrisma.template.findMany).toHaveBeenCalledWith({
        where: expect.objectContaining({
          category: 'creative'
        }),
        include: expect.any(Object),
        orderBy: expect.any(Object),
        take: 20,
        skip: 0
      })
    })

    it('should filter by price range', async () => {
      const criteria = {
        priceRange: { min: 5, max: 15 },
        limit: 20,
        offset: 0
      }

      await service.searchTemplates(criteria)

      expect(mockPrisma.template.findMany).toHaveBeenCalledWith({
        where: expect.objectContaining({
          marketplaceListing: {
            status: 'approved',
            price: {
              gte: 5,
              lte: 15
            }
          }
        }),
        include: expect.any(Object),
        orderBy: expect.any(Object),
        take: 20,
        skip: 0
      })
    })

    it('should sort by different criteria', async () => {
      const testCases = [
        { sortBy: 'newest' as const, expectedOrderBy: { createdAt: 'desc' } },
        { sortBy: 'rating' as const, expectedOrderBy: { rating: 'desc' } },
        { sortBy: 'price_low' as const, expectedOrderBy: { marketplaceListing: { price: 'asc' } } },
        { sortBy: 'price_high' as const, expectedOrderBy: { marketplaceListing: { price: 'desc' } } }
      ]

      for (const testCase of testCases) {
        mockPrisma.template.findMany.mockClear()
        
        await service.searchTemplates({
          sortBy: testCase.sortBy,
          limit: 20,
          offset: 0
        })

        expect(mockPrisma.template.findMany).toHaveBeenCalledWith({
          where: expect.any(Object),
          include: expect.any(Object),
          orderBy: testCase.expectedOrderBy,
          take: 20,
          skip: 0
        })
      }
    })

    it('should handle pagination correctly', async () => {
      const criteria = {
        limit: 10,
        offset: 20
      }

      const result = await service.searchTemplates(criteria)

      expect(result.hasMore).toBe(false) // 2 total, offset 20 means no more
    })
  })

  describe('getFeaturedTemplates', () => {
    const mockFeaturedTemplates = [
      {
        id: 'featured-1',
        name: 'Featured Template',
        marketplaceListing: {
          isFeatured: true,
          status: 'approved',
          price: 19.99,
          downloadCount: 500
        },
        _count: { reviews: 100 },
        createdBy: 'user-1'
      }
    ]

    beforeEach(() => {
      mockPrisma.template.findMany.mockResolvedValue(mockFeaturedTemplates)
      mockPrisma.user.findUnique.mockResolvedValue({
        id: 'user-1',
        name: 'Featured Creator',
        email: '<EMAIL>'
      })
    })

    it('should return featured templates', async () => {
      const templates = await service.getFeaturedTemplates(5)

      expect(mockPrisma.template.findMany).toHaveBeenCalledWith({
        where: {
          marketplaceListing: {
            status: 'approved',
            isFeatured: true
          },
          isActive: true
        },
        include: {
          marketplaceListing: true,
          _count: {
            select: {
              reviews: true
            }
          }
        },
        orderBy: {
          usageCount: 'desc'
        },
        take: 5
      })

      expect(templates).toHaveLength(1)
      expect(templates[0].isFeatured).toBe(true)
    })
  })

  describe('publishTemplate', () => {
    const mockTemplate = {
      id: 'template-1',
      createdBy: 'user-1',
      marketplaceListing: null
    }

    beforeEach(() => {
      mockPrisma.template.findUnique.mockResolvedValue(mockTemplate)
      mockPrisma.templateMarketplace.create.mockResolvedValue({
        id: 'listing-1'
      })
    })

    it('should publish template to marketplace', async () => {
      const publishData = {
        templateId: 'template-1',
        price: 9.99,
        isFeatured: false
      }

      const listingId = await service.publishTemplate(publishData, 'user-1')

      expect(mockPrisma.templateMarketplace.create).toHaveBeenCalledWith({
        data: {
          templateId: 'template-1',
          sellerId: 'user-1',
          price: 9.99,
          isFeatured: false,
          status: 'pending'
        }
      })

      expect(listingId).toBe('listing-1')
    })

    it('should throw error for non-existent template', async () => {
      mockPrisma.template.findUnique.mockResolvedValue(null)

      const publishData = {
        templateId: 'non-existent',
        price: 9.99
      }

      await expect(service.publishTemplate(publishData, 'user-1'))
        .rejects.toThrow('Template not found')
    })

    it('should throw error when user does not own template', async () => {
      const templateOwnedByOther = {
        ...mockTemplate,
        createdBy: 'other-user'
      }

      mockPrisma.template.findUnique.mockResolvedValue(templateOwnedByOther)

      const publishData = {
        templateId: 'template-1',
        price: 9.99
      }

      await expect(service.publishTemplate(publishData, 'user-1'))
        .rejects.toThrow('You can only publish your own templates')
    })

    it('should throw error when template is already published', async () => {
      const publishedTemplate = {
        ...mockTemplate,
        marketplaceListing: { id: 'existing-listing' }
      }

      mockPrisma.template.findUnique.mockResolvedValue(publishedTemplate)

      const publishData = {
        templateId: 'template-1',
        price: 9.99
      }

      await expect(service.publishTemplate(publishData, 'user-1'))
        .rejects.toThrow('Template is already published')
    })
  })

  describe('purchaseTemplate', () => {
    const mockTemplate = {
      id: 'template-1',
      name: 'Test Template',
      description: 'Test description',
      config: JSON.stringify({ layout: 'test' }),
      category: 'professional',
      tags: JSON.stringify(['test']),
      marketplaceListing: {
        id: 'listing-1',
        price: 9.99,
        status: 'approved'
      }
    }

    beforeEach(() => {
      mockPrisma.template.findUnique.mockResolvedValue(mockTemplate)
      mockPrisma.templatePurchase.findUnique.mockResolvedValue(null)
      mockPrisma.templatePurchase.create.mockResolvedValue({
        id: 'purchase-1'
      })
      mockPrisma.templateMarketplace.update.mockResolvedValue({})
    })

    it('should purchase template successfully', async () => {
      const result = await service.purchaseTemplate('template-1', 'user-1')

      expect(mockPrisma.templatePurchase.create).toHaveBeenCalledWith({
        data: {
          templateMarketplaceId: 'listing-1',
          userId: 'user-1',
          price: 9.99,
          paymentMethod: 'free',
          paymentId: expect.stringContaining('free_')
        }
      })

      expect(mockPrisma.templateMarketplace.update).toHaveBeenCalledWith({
        where: { id: 'listing-1' },
        data: {
          downloadCount: {
            increment: 1
          }
        }
      })

      expect(result.success).toBe(true)
      expect(result.purchaseId).toBe('purchase-1')
      expect(result.templateData).toBeDefined()
    })

    it('should return existing purchase if already purchased', async () => {
      const existingPurchase = {
        id: 'existing-purchase'
      }

      mockPrisma.templatePurchase.findUnique.mockResolvedValue(existingPurchase)

      const result = await service.purchaseTemplate('template-1', 'user-1')

      expect(mockPrisma.templatePurchase.create).not.toHaveBeenCalled()
      expect(result.success).toBe(true)
      expect(result.purchaseId).toBe('existing-purchase')
    })

    it('should throw error for non-existent template', async () => {
      mockPrisma.template.findUnique.mockResolvedValue(null)

      await expect(service.purchaseTemplate('non-existent', 'user-1'))
        .rejects.toThrow('Template not found or not available for purchase')
    })

    it('should throw error for unapproved template', async () => {
      const unapprovedTemplate = {
        ...mockTemplate,
        marketplaceListing: {
          ...mockTemplate.marketplaceListing,
          status: 'pending'
        }
      }

      mockPrisma.template.findUnique.mockResolvedValue(unapprovedTemplate)

      await expect(service.purchaseTemplate('template-1', 'user-1'))
        .rejects.toThrow('Template is not approved for sale')
    })
  })

  describe('addReview', () => {
    beforeEach(() => {
      mockPrisma.templatePurchase.findFirst.mockResolvedValue({
        id: 'purchase-1'
      })
      mockPrisma.templateReview.upsert.mockResolvedValue({})
    })

    it('should add review for purchased template', async () => {
      const reviewData = {
        templateId: 'template-1',
        rating: 5,
        reviewText: 'Excellent template!'
      }

      await service.addReview(reviewData, 'user-1')

      expect(mockPrisma.templateReview.upsert).toHaveBeenCalledWith({
        where: {
          templateId_userId: {
            templateId: 'template-1',
            userId: 'user-1'
          }
        },
        update: {
          rating: 5,
          reviewText: 'Excellent template!',
          isVerifiedPurchase: true,
          updatedAt: expect.any(Date)
        },
        create: {
          templateId: 'template-1',
          userId: 'user-1',
          rating: 5,
          reviewText: 'Excellent template!',
          isVerifiedPurchase: true
        }
      })
    })

    it('should add review for non-purchased template', async () => {
      mockPrisma.templatePurchase.findFirst.mockResolvedValue(null)

      const reviewData = {
        templateId: 'template-1',
        rating: 4,
        reviewText: 'Good template'
      }

      await service.addReview(reviewData, 'user-1')

      expect(mockPrisma.templateReview.upsert).toHaveBeenCalledWith({
        where: expect.any(Object),
        update: expect.objectContaining({
          isVerifiedPurchase: false
        }),
        create: expect.objectContaining({
          isVerifiedPurchase: false
        })
      })
    })
  })

  describe('getTemplateReviews', () => {
    const mockReviews = [
      {
        id: 'review-1',
        rating: 5,
        reviewText: 'Great template!',
        isVerifiedPurchase: true,
        createdAt: new Date(),
        updatedAt: new Date(),
        user: {
          id: 'user-1',
          name: 'John Doe'
        }
      },
      {
        id: 'review-2',
        rating: 4,
        reviewText: 'Good quality',
        isVerifiedPurchase: false,
        createdAt: new Date(),
        updatedAt: new Date(),
        user: {
          id: 'user-2',
          name: 'Jane Smith'
        }
      }
    ]

    beforeEach(() => {
      mockPrisma.templateReview.findMany.mockResolvedValue(mockReviews)
    })

    it('should return template reviews', async () => {
      const reviews = await service.getTemplateReviews('template-1', 10, 0)

      expect(mockPrisma.templateReview.findMany).toHaveBeenCalledWith({
        where: { templateId: 'template-1' },
        include: {
          user: {
            select: {
              id: true,
              name: true
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        },
        take: 10,
        skip: 0
      })

      expect(reviews).toHaveLength(2)
      expect(reviews[0].rating).toBe(5)
      expect(reviews[0].user.name).toBe('John Doe')
    })
  })

  describe('trackTemplateUsage', () => {
    beforeEach(() => {
      mockPrisma.templateUsageAnalytics.create.mockResolvedValue({})
    })

    it('should track template usage', async () => {
      const metadata = {
        deviceType: 'desktop',
        userAgent: 'Mozilla/5.0...'
      }

      await service.trackTemplateUsage('template-1', 'user-1', 'view', metadata)

      expect(mockPrisma.templateUsageAnalytics.create).toHaveBeenCalledWith({
        data: {
          templateId: 'template-1',
          userId: 'user-1',
          actionType: 'view',
          deviceType: 'desktop',
          userAgent: 'Mozilla/5.0...',
          ipAddress: undefined,
          metadata: JSON.stringify(metadata)
        }
      })
    })

    it('should handle tracking errors gracefully', async () => {
      mockPrisma.templateUsageAnalytics.create.mockRejectedValue(new Error('Database error'))

      // Should not throw error
      await expect(service.trackTemplateUsage('template-1', 'user-1', 'view'))
        .resolves.not.toThrow()
    })
  })

  describe('createCollection', () => {
    beforeEach(() => {
      mockPrisma.templateCollection.create.mockResolvedValue({
        id: 'collection-1'
      })
      mockPrisma.templateCollectionItem.createMany.mockResolvedValue({})
    })

    it('should create template collection', async () => {
      const collectionData = {
        name: 'Professional Templates',
        description: 'Collection of professional templates',
        templateIds: ['template-1', 'template-2'],
        isPublic: true
      }

      const collectionId = await service.createCollection(collectionData, 'user-1')

      expect(mockPrisma.templateCollection.create).toHaveBeenCalledWith({
        data: {
          name: 'Professional Templates',
          description: 'Collection of professional templates',
          createdBy: 'user-1',
          isPublic: true
        }
      })

      expect(mockPrisma.templateCollectionItem.createMany).toHaveBeenCalledWith({
        data: [
          {
            collectionId: 'collection-1',
            templateId: 'template-1',
            orderIndex: 0
          },
          {
            collectionId: 'collection-1',
            templateId: 'template-2',
            orderIndex: 1
          }
        ]
      })

      expect(collectionId).toBe('collection-1')
    })

    it('should create collection without templates', async () => {
      const collectionData = {
        name: 'Empty Collection',
        templateIds: [],
        isPublic: false
      }

      await service.createCollection(collectionData, 'user-1')

      expect(mockPrisma.templateCollectionItem.createMany).not.toHaveBeenCalled()
    })
  })
})
