{"name": "@careercraft/shared", "version": "0.1.0", "description": "Shared types, schemas, and utilities for CareerCraft", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "type-check": "tsc --noEmit"}, "dependencies": {"zod": "^3.22.4", "nanoid": "^5.0.4"}, "devDependencies": {"typescript": "^5.3.2", "@types/node": "^20.10.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./types/*": {"types": "./dist/types/*.d.ts", "default": "./dist/types/*.js"}, "./schemas/*": {"types": "./dist/schemas/*.d.ts", "default": "./dist/schemas/*.js"}, "./data/*": {"types": "./dist/data/*.d.ts", "default": "./dist/data/*.js"}}}