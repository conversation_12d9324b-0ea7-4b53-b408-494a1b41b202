"""
Job Data Processor
Processes and enriches scraped job data with AI analysis
Part of FR-5.2: Job Market Data Ingestion Service
"""

import asyncio
import logging
import json
from typing import List, Dict, Any, Optional
from datetime import datetime
import hashlib
import re

from scrapers.base_scraper import JobPosting
from utils.ai_client import AIClient
from utils.config import Config

class JobProcessor:
    """Processes and enriches job posting data"""
    
    def __init__(self):
        self.config = Config()
        self.ai_client = AIClient()
        self.logger = logging.getLogger('job_processor')
        
        # Industry classification mapping
        self.industry_keywords = {
            'Technology': ['software', 'tech', 'engineering', 'developer', 'programmer', 'data', 'ai', 'ml'],
            'Finance': ['finance', 'banking', 'investment', 'trading', 'fintech', 'accounting'],
            'Healthcare': ['health', 'medical', 'hospital', 'pharma', 'biotech', 'clinical'],
            'Retail': ['retail', 'ecommerce', 'commerce', 'sales', 'marketing', 'consumer'],
            'Education': ['education', 'university', 'school', 'teaching', 'academic', 'research'],
            'Manufacturing': ['manufacturing', 'production', 'industrial', 'automotive', 'aerospace'],
            'Media': ['media', 'entertainment', 'gaming', 'content', 'creative', 'design'],
            'Consulting': ['consulting', 'advisory', 'strategy', 'management', 'business'],
            'Government': ['government', 'public', 'federal', 'state', 'municipal', 'defense'],
            'Non-profit': ['nonprofit', 'ngo', 'foundation', 'charity', 'social']
        }

    async def process_job_batch(self, jobs: List[JobPosting]) -> List[JobPosting]:
        """Process a batch of job postings"""
        self.logger.info(f"Processing batch of {len(jobs)} jobs")
        
        processed_jobs = []
        
        # Process jobs in smaller batches to avoid overwhelming APIs
        batch_size = 10
        for i in range(0, len(jobs), batch_size):
            batch = jobs[i:i + batch_size]
            
            # Process batch concurrently
            tasks = [self.process_single_job(job) for job in batch]
            batch_results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Collect successful results
            for result in batch_results:
                if isinstance(result, JobPosting):
                    processed_jobs.append(result)
                elif isinstance(result, Exception):
                    self.logger.error(f"Job processing failed: {result}")
            
            # Rate limiting between batches
            await asyncio.sleep(1)
        
        self.logger.info(f"Successfully processed {len(processed_jobs)} jobs")
        return processed_jobs

    async def process_single_job(self, job: JobPosting) -> JobPosting:
        """Process a single job posting with AI enhancement"""
        try:
            # 1. Generate unique job hash
            job.external_id = job.external_id or self._generate_job_hash(job)
            
            # 2. Classify industry if not set
            if not job.industry:
                job.industry = self._classify_industry(job)
            
            # 3. Enhance job description with AI
            if self.config.enable_ai_processing:
                enhanced_data = await self._enhance_with_ai(job)
                if enhanced_data:
                    job = self._apply_ai_enhancements(job, enhanced_data)
            
            # 4. Extract and normalize skills
            if not job.skills:
                job.skills = self._extract_skills(job)
            else:
                job.skills = self._normalize_skills(job.skills)
            
            # 5. Standardize experience level
            job.experience_level = self._standardize_experience_level(job)
            
            # 6. Normalize job type
            job.job_type = self._normalize_job_type(job)
            
            # 7. Generate vector embedding
            if self.config.enable_vectorization:
                job.vector_embedding = await self._generate_job_vector(job)
            
            # 8. Add processing metadata
            job.processed_at = datetime.now()
            
            return job
            
        except Exception as e:
            self.logger.error(f"Error processing job {job.title} at {job.company}: {e}")
            raise

    def _generate_job_hash(self, job: JobPosting) -> str:
        """Generate unique hash for job posting"""
        # Create hash from key job attributes
        hash_string = f"{job.title}|{job.company}|{job.location}|{job.description[:100]}"
        return hashlib.md5(hash_string.encode()).hexdigest()[:12]

    def _classify_industry(self, job: JobPosting) -> str:
        """Classify job industry based on content"""
        text = f"{job.title} {job.company} {job.description}".lower()
        
        # Score each industry based on keyword matches
        industry_scores = {}
        for industry, keywords in self.industry_keywords.items():
            score = sum(1 for keyword in keywords if keyword in text)
            if score > 0:
                industry_scores[industry] = score
        
        # Return industry with highest score, or 'Other' if no matches
        if industry_scores:
            return max(industry_scores, key=industry_scores.get)
        return 'Other'

    async def _enhance_with_ai(self, job: JobPosting) -> Optional[Dict[str, Any]]:
        """Enhance job data using AI analysis"""
        try:
            prompt = f"""
            Analyze this job posting and extract structured information:
            
            Title: {job.title}
            Company: {job.company}
            Location: {job.location}
            Description: {job.description[:1000]}
            
            Please provide a JSON response with:
            1. "skills": Array of technical and professional skills mentioned
            2. "experience_level": One of "ENTRY", "MID", "SENIOR", "EXECUTIVE"
            3. "job_type": One of "FULL_TIME", "PART_TIME", "CONTRACT", "REMOTE"
            4. "salary_estimate": Estimated salary range if not explicitly mentioned
            5. "requirements": Key requirements and qualifications
            6. "benefits": Mentioned benefits and perks
            7. "industry": Most relevant industry classification
            8. "seniority_indicators": Words/phrases indicating seniority level
            
            Return only valid JSON.
            """
            
            response = await self.ai_client.analyze_job_posting(prompt)
            
            if response:
                try:
                    return json.loads(response)
                except json.JSONDecodeError:
                    self.logger.warning(f"Invalid JSON response from AI for job {job.title}")
                    return None
            
        except Exception as e:
            self.logger.error(f"AI enhancement failed for job {job.title}: {e}")
            return None

    def _apply_ai_enhancements(self, job: JobPosting, enhanced_data: Dict[str, Any]) -> JobPosting:
        """Apply AI enhancements to job posting"""
        try:
            # Update skills if AI found more
            if enhanced_data.get('skills') and len(enhanced_data['skills']) > len(job.skills or []):
                job.skills = enhanced_data['skills']
            
            # Update experience level if more accurate
            if enhanced_data.get('experience_level'):
                job.experience_level = enhanced_data['experience_level']
            
            # Update job type if more accurate
            if enhanced_data.get('job_type'):
                job.job_type = enhanced_data['job_type']
            
            # Add requirements if not present
            if enhanced_data.get('requirements') and not job.requirements:
                job.requirements = enhanced_data['requirements']
            
            # Update industry if AI has better classification
            if enhanced_data.get('industry'):
                job.industry = enhanced_data['industry']
            
            # Add salary estimate if not present
            if enhanced_data.get('salary_estimate') and not job.salary_min:
                salary_est = enhanced_data['salary_estimate']
                if isinstance(salary_est, dict):
                    job.salary_min = salary_est.get('min')
                    job.salary_max = salary_est.get('max')
            
        except Exception as e:
            self.logger.error(f"Error applying AI enhancements: {e}")
        
        return job

    def _extract_skills(self, job: JobPosting) -> List[str]:
        """Extract skills from job posting text"""
        text = f"{job.title} {job.description} {job.requirements or ''}".lower()
        
        # Common technical skills patterns
        skill_patterns = [
            # Programming languages
            r'\b(javascript|typescript|python|java|c\+\+|c#|php|ruby|go|rust|swift|kotlin|scala|r|matlab)\b',
            # Frameworks and libraries
            r'\b(react|vue|angular|node\.?js|express|django|flask|spring|laravel|rails|asp\.?net)\b',
            # Databases
            r'\b(sql|mysql|postgresql|mongodb|redis|elasticsearch|oracle|sqlite|cassandra|dynamodb)\b',
            # Cloud and DevOps
            r'\b(aws|azure|gcp|docker|kubernetes|jenkins|git|github|gitlab|terraform|ansible)\b',
            # Web technologies
            r'\b(html|css|sass|scss|tailwind|bootstrap|jquery|rest|graphql|api)\b',
            # Data and AI
            r'\b(machine learning|deep learning|tensorflow|pytorch|pandas|numpy|tableau|power bi|spark)\b',
            # Other tools
            r'\b(jira|confluence|slack|figma|adobe|photoshop|sketch|linux|windows|macos)\b'
        ]
        
        skills = set()
        for pattern in skill_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            skills.update(matches)
        
        # Clean and normalize skills
        normalized_skills = []
        for skill in skills:
            # Normalize common variations
            skill = skill.replace('.', '').replace('#', 'Sharp')
            skill = skill.title()
            
            # Skip very short or common words
            if len(skill) > 2 and skill.lower() not in ['and', 'the', 'for', 'with']:
                normalized_skills.append(skill)
        
        return sorted(list(set(normalized_skills)))

    def _normalize_skills(self, skills: List[str]) -> List[str]:
        """Normalize and deduplicate skills list"""
        if not skills:
            return []
        
        normalized = set()
        for skill in skills:
            # Clean skill name
            clean_skill = re.sub(r'[^\w\s\+\#\.]', '', skill).strip()
            if len(clean_skill) > 1:
                normalized.add(clean_skill.title())
        
        return sorted(list(normalized))

    def _standardize_experience_level(self, job: JobPosting) -> str:
        """Standardize experience level classification"""
        if job.experience_level:
            level = job.experience_level.upper()
            if level in ['ENTRY', 'MID', 'SENIOR', 'EXECUTIVE']:
                return level
        
        # Analyze job title and description for experience indicators
        text = f"{job.title} {job.description}".lower()
        
        # Executive level indicators
        if any(word in text for word in ['director', 'vp', 'vice president', 'chief', 'head of', 'executive']):
            return 'EXECUTIVE'
        
        # Senior level indicators
        if any(word in text for word in ['senior', 'sr.', 'lead', 'principal', 'staff', '5+ years', '7+ years']):
            return 'SENIOR'
        
        # Entry level indicators
        if any(word in text for word in ['entry', 'junior', 'jr.', 'graduate', 'new grad', '0-2 years', 'internship']):
            return 'ENTRY'
        
        # Mid level indicators
        if any(word in text for word in ['mid', 'intermediate', '3+ years', '4+ years', '2-5 years']):
            return 'MID'
        
        # Default to MID if unclear
        return 'MID'

    def _normalize_job_type(self, job: JobPosting) -> str:
        """Normalize job type classification"""
        if job.job_type:
            job_type = job.job_type.upper()
            if job_type in ['FULL_TIME', 'PART_TIME', 'CONTRACT', 'REMOTE']:
                return job_type
        
        # Analyze text for job type indicators
        text = f"{job.title} {job.description}".lower()
        
        if 'remote' in text or 'work from home' in text:
            return 'REMOTE'
        elif any(word in text for word in ['contract', 'contractor', 'freelance', 'temporary']):
            return 'CONTRACT'
        elif any(word in text for word in ['part-time', 'part time', 'hourly']):
            return 'PART_TIME'
        else:
            return 'FULL_TIME'

    async def _generate_job_vector(self, job: JobPosting) -> Optional[str]:
        """Generate vector embedding for job posting"""
        try:
            # Create comprehensive text representation
            job_text = self._create_job_text_representation(job)
            
            # Generate embedding using AI client
            vector = await self.ai_client.create_embedding(job_text)
            
            if vector:
                # Store as JSON string
                return json.dumps(vector)
            
        except Exception as e:
            self.logger.error(f"Vector generation failed for job {job.title}: {e}")
            return None

    def _create_job_text_representation(self, job: JobPosting) -> str:
        """Create comprehensive text representation for vectorization"""
        sections = []
        
        # Job title and company
        sections.append(f"Job Title: {job.title}")
        sections.append(f"Company: {job.company}")
        
        # Location and job type
        if job.location:
            sections.append(f"Location: {job.location}")
        if job.job_type:
            sections.append(f"Job Type: {job.job_type}")
        if job.experience_level:
            sections.append(f"Experience Level: {job.experience_level}")
        
        # Description
        if job.description:
            sections.append(f"Description: {job.description}")
        
        # Requirements
        if job.requirements:
            sections.append(f"Requirements: {job.requirements}")
        
        # Skills
        if job.skills:
            sections.append(f"Skills: {', '.join(job.skills)}")
        
        # Industry
        if job.industry:
            sections.append(f"Industry: {job.industry}")
        
        # Salary information
        if job.salary_min and job.salary_max:
            sections.append(f"Salary Range: ${job.salary_min:,} - ${job.salary_max:,}")
        elif job.salary_min:
            sections.append(f"Salary: ${job.salary_min:,}+")
        
        return '\n\n'.join(sections)

    def validate_processed_job(self, job: JobPosting) -> bool:
        """Validate that processed job meets quality standards"""
        # Required fields
        if not job.title or not job.company or not job.description:
            return False
        
        # Minimum description length
        if len(job.description) < 50:
            return False
        
        # Valid experience level
        if job.experience_level not in ['ENTRY', 'MID', 'SENIOR', 'EXECUTIVE']:
            return False
        
        # Valid job type
        if job.job_type not in ['FULL_TIME', 'PART_TIME', 'CONTRACT', 'REMOTE']:
            return False
        
        # Reasonable salary range
        if job.salary_min and job.salary_max:
            if job.salary_min > job.salary_max or job.salary_min < 20000 or job.salary_max > 1000000:
                return False
        
        return True
