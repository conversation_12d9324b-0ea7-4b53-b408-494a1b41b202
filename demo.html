<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CareerCraft - Feature Demo</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <style>
        .feature-card {
            transition: all 0.3s ease;
        }
        .feature-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        .demo-section {
            min-height: 400px;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <h1 class="text-2xl font-bold text-gray-900">CareerCraft</h1>
                    </div>
                </div>
                <nav class="hidden md:flex space-x-8">
                    <a href="#features" class="text-gray-500 hover:text-gray-900">Features</a>
                    <a href="#demo" class="text-gray-500 hover:text-gray-900">Demo</a>
                    <a href="#templates" class="text-gray-500 hover:text-gray-900">Templates</a>
                    <a href="#ai" class="text-gray-500 hover:text-gray-900">AI Features</a>
                </nav>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="bg-gradient-to-r from-blue-600 to-purple-600 text-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
            <div class="text-center">
                <h1 class="text-4xl md:text-6xl font-bold mb-6">
                    Build Your Perfect Resume with AI
                </h1>
                <p class="text-xl md:text-2xl mb-8 text-blue-100">
                    Professional resume builder with AI-powered content generation and ATS optimization
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <button class="bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
                        Start Building
                    </button>
                    <button class="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-colors">
                        View Demo
                    </button>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="py-20">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                    Everything You Need to Land Your Dream Job
                </h2>
                <p class="text-xl text-gray-600">
                    Comprehensive tools and AI-powered features to create outstanding resumes
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Resume Builder -->
                <div class="feature-card bg-white p-6 rounded-xl shadow-lg">
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                        <i data-lucide="file-text" class="w-6 h-6 text-blue-600"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-2">Resume Builder</h3>
                    <p class="text-gray-600 mb-4">
                        Intuitive drag-and-drop builder with real-time preview and professional templates.
                    </p>
                    <ul class="text-sm text-gray-500 space-y-1">
                        <li>✓ Real-time preview</li>
                        <li>✓ Drag & drop sections</li>
                        <li>✓ Auto-save functionality</li>
                        <li>✓ Multiple export formats</li>
                    </ul>
                </div>

                <!-- AI Content Generation -->
                <div class="feature-card bg-white p-6 rounded-xl shadow-lg">
                    <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-4">
                        <i data-lucide="sparkles" class="w-6 h-6 text-purple-600"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-2">AI Content Generation</h3>
                    <p class="text-gray-600 mb-4">
                        Generate professional content with AI assistance tailored to your experience level.
                    </p>
                    <ul class="text-sm text-gray-500 space-y-1">
                        <li>✓ Professional summaries</li>
                        <li>✓ Job descriptions</li>
                        <li>✓ Achievement bullets</li>
                        <li>✓ Skills suggestions</li>
                    </ul>
                </div>

                <!-- ATS Optimization -->
                <div class="feature-card bg-white p-6 rounded-xl shadow-lg">
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-4">
                        <i data-lucide="shield-check" class="w-6 h-6 text-green-600"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-2">ATS Optimization</h3>
                    <p class="text-gray-600 mb-4">
                        Ensure your resume passes Applicant Tracking Systems with our optimization tools.
                    </p>
                    <ul class="text-sm text-gray-500 space-y-1">
                        <li>✓ ATS compatibility score</li>
                        <li>✓ Keyword analysis</li>
                        <li>✓ Format validation</li>
                        <li>✓ Improvement suggestions</li>
                    </ul>
                </div>

                <!-- Professional Templates -->
                <div class="feature-card bg-white p-6 rounded-xl shadow-lg">
                    <div class="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center mb-4">
                        <i data-lucide="layout-template" class="w-6 h-6 text-indigo-600"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-2">Professional Templates</h3>
                    <p class="text-gray-600 mb-4">
                        Choose from a variety of professionally designed templates for every industry.
                    </p>
                    <ul class="text-sm text-gray-500 space-y-1">
                        <li>✓ 20+ templates</li>
                        <li>✓ Industry-specific designs</li>
                        <li>✓ Customizable colors</li>
                        <li>✓ Mobile-responsive</li>
                    </ul>
                </div>

                <!-- Export Options -->
                <div class="feature-card bg-white p-6 rounded-xl shadow-lg">
                    <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mb-4">
                        <i data-lucide="download" class="w-6 h-6 text-orange-600"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-2">Multiple Export Formats</h3>
                    <p class="text-gray-600 mb-4">
                        Export your resume in various formats optimized for different use cases.
                    </p>
                    <ul class="text-sm text-gray-500 space-y-1">
                        <li>✓ PDF (high quality)</li>
                        <li>✓ DOCX (editable)</li>
                        <li>✓ HTML (web-ready)</li>
                        <li>✓ Print-optimized</li>
                    </ul>
                </div>

                <!-- Analytics -->
                <div class="feature-card bg-white p-6 rounded-xl shadow-lg">
                    <div class="w-12 h-12 bg-pink-100 rounded-lg flex items-center justify-center mb-4">
                        <i data-lucide="bar-chart-3" class="w-6 h-6 text-pink-600"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-2">Resume Analytics</h3>
                    <p class="text-gray-600 mb-4">
                        Track your resume performance and get insights on how to improve.
                    </p>
                    <ul class="text-sm text-gray-500 space-y-1">
                        <li>✓ View tracking</li>
                        <li>✓ Download analytics</li>
                        <li>✓ Performance insights</li>
                        <li>✓ A/B testing</li>
                    </ul>
                </div>
            </div>
        </div>
    </section>

    <!-- Demo Section -->
    <section id="demo" class="bg-gray-100 py-20">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                    See CareerCraft in Action
                </h2>
                <p class="text-xl text-gray-600">
                    Interactive demo of our key features
                </p>
            </div>

            <div class="bg-white rounded-2xl shadow-xl overflow-hidden">
                <div class="flex border-b">
                    <button class="demo-tab px-6 py-4 font-medium text-blue-600 border-b-2 border-blue-600" data-tab="builder">
                        Resume Builder
                    </button>
                    <button class="demo-tab px-6 py-4 font-medium text-gray-500 hover:text-gray-700" data-tab="ai">
                        AI Features
                    </button>
                    <button class="demo-tab px-6 py-4 font-medium text-gray-500 hover:text-gray-700" data-tab="templates">
                        Templates
                    </button>
                </div>

                <div class="demo-section p-8">
                    <!-- Resume Builder Demo -->
                    <div id="builder-demo" class="demo-content">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                            <div>
                                <h3 class="text-2xl font-bold mb-4">Intuitive Resume Builder</h3>
                                <p class="text-gray-600 mb-6">
                                    Build your resume with our easy-to-use interface. Add sections, 
                                    customize content, and see changes in real-time.
                                </p>
                                <div class="space-y-4">
                                    <div class="flex items-center p-4 bg-blue-50 rounded-lg">
                                        <i data-lucide="user" class="w-5 h-5 text-blue-600 mr-3"></i>
                                        <span>Personal Information</span>
                                    </div>
                                    <div class="flex items-center p-4 bg-green-50 rounded-lg">
                                        <i data-lucide="briefcase" class="w-5 h-5 text-green-600 mr-3"></i>
                                        <span>Work Experience</span>
                                    </div>
                                    <div class="flex items-center p-4 bg-purple-50 rounded-lg">
                                        <i data-lucide="graduation-cap" class="w-5 h-5 text-purple-600 mr-3"></i>
                                        <span>Education</span>
                                    </div>
                                    <div class="flex items-center p-4 bg-orange-50 rounded-lg">
                                        <i data-lucide="zap" class="w-5 h-5 text-orange-600 mr-3"></i>
                                        <span>Skills & Certifications</span>
                                    </div>
                                </div>
                            </div>
                            <div class="bg-gray-50 rounded-lg p-6">
                                <h4 class="font-semibold mb-4">Live Preview</h4>
                                <div class="bg-white rounded shadow-sm p-4 text-sm">
                                    <div class="border-b pb-2 mb-3">
                                        <h5 class="font-bold">John Doe</h5>
                                        <p class="text-gray-600">Software Engineer</p>
                                    </div>
                                    <div class="space-y-2">
                                        <div>
                                            <h6 class="font-semibold text-xs">EXPERIENCE</h6>
                                            <p class="text-xs">Senior Developer at TechCorp</p>
                                        </div>
                                        <div>
                                            <h6 class="font-semibold text-xs">EDUCATION</h6>
                                            <p class="text-xs">BS Computer Science</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- AI Features Demo -->
                    <div id="ai-demo" class="demo-content hidden">
                        <div class="text-center mb-8">
                            <h3 class="text-2xl font-bold mb-4">AI-Powered Content Generation</h3>
                            <p class="text-gray-600">
                                Let AI help you write compelling resume content that stands out.
                            </p>
                        </div>
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                            <div class="space-y-6">
                                <div class="p-6 border rounded-lg">
                                    <h4 class="font-semibold mb-3">Professional Summary Generator</h4>
                                    <div class="space-y-3">
                                        <input type="text" placeholder="Your current role" class="w-full p-2 border rounded">
                                        <input type="text" placeholder="Years of experience" class="w-full p-2 border rounded">
                                        <input type="text" placeholder="Key skills" class="w-full p-2 border rounded">
                                        <button class="w-full bg-blue-600 text-white py-2 rounded hover:bg-blue-700">
                                            Generate Summary
                                        </button>
                                    </div>
                                </div>
                                <div class="p-6 border rounded-lg">
                                    <h4 class="font-semibold mb-3">ATS Optimization Score</h4>
                                    <div class="flex items-center justify-between mb-2">
                                        <span>Overall Score</span>
                                        <span class="font-bold text-green-600">85/100</span>
                                    </div>
                                    <div class="w-full bg-gray-200 rounded-full h-2">
                                        <div class="bg-green-600 h-2 rounded-full" style="width: 85%"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="bg-gray-50 rounded-lg p-6">
                                <h4 class="font-semibold mb-4">Generated Content</h4>
                                <div class="bg-white rounded p-4 text-sm">
                                    <p class="italic text-gray-600 mb-4">
                                        "Experienced software engineer with 5+ years of expertise in full-stack 
                                        development. Proven track record of delivering scalable solutions using 
                                        modern technologies including React, Node.js, and cloud platforms."
                                    </p>
                                    <div class="space-y-2">
                                        <h6 class="font-semibold text-xs">SUGGESTED IMPROVEMENTS:</h6>
                                        <ul class="text-xs space-y-1">
                                            <li>• Add quantifiable achievements</li>
                                            <li>• Include industry-specific keywords</li>
                                            <li>• Optimize for ATS scanning</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Templates Demo -->
                    <div id="templates-demo" class="demo-content hidden">
                        <div class="text-center mb-8">
                            <h3 class="text-2xl font-bold mb-4">Professional Templates</h3>
                            <p class="text-gray-600">
                                Choose from our collection of professionally designed templates.
                            </p>
                        </div>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <div class="border rounded-lg overflow-hidden hover:shadow-lg transition-shadow">
                                <div class="h-48 bg-gradient-to-br from-blue-500 to-blue-600 p-4 text-white text-xs">
                                    <div class="bg-white/20 rounded p-2 mb-2">
                                        <div class="h-2 bg-white/60 rounded mb-1"></div>
                                        <div class="h-1 bg-white/40 rounded"></div>
                                    </div>
                                    <div class="space-y-1">
                                        <div class="h-1 bg-white/60 rounded"></div>
                                        <div class="h-1 bg-white/40 rounded w-3/4"></div>
                                    </div>
                                </div>
                                <div class="p-4">
                                    <h4 class="font-semibold">Modern Professional</h4>
                                    <p class="text-sm text-gray-600">Clean and contemporary design</p>
                                </div>
                            </div>
                            <div class="border rounded-lg overflow-hidden hover:shadow-lg transition-shadow">
                                <div class="h-48 bg-gradient-to-br from-gray-700 to-gray-800 p-4 text-white text-xs">
                                    <div class="border-b border-white/20 pb-2 mb-2">
                                        <div class="h-2 bg-white/60 rounded mb-1"></div>
                                        <div class="h-1 bg-white/40 rounded w-1/2"></div>
                                    </div>
                                    <div class="space-y-1">
                                        <div class="h-1 bg-white/60 rounded"></div>
                                        <div class="h-1 bg-white/40 rounded w-2/3"></div>
                                    </div>
                                </div>
                                <div class="p-4">
                                    <h4 class="font-semibold">Classic Executive</h4>
                                    <p class="text-sm text-gray-600">Traditional and elegant layout</p>
                                </div>
                            </div>
                            <div class="border rounded-lg overflow-hidden hover:shadow-lg transition-shadow">
                                <div class="h-48 bg-gradient-to-br from-green-500 to-green-600 p-4 text-white text-xs">
                                    <div class="flex gap-2 mb-2">
                                        <div class="flex-1 bg-white/20 rounded p-1">
                                            <div class="h-1 bg-white/60 rounded"></div>
                                        </div>
                                        <div class="flex-1 bg-white/20 rounded p-1">
                                            <div class="h-1 bg-white/40 rounded"></div>
                                        </div>
                                    </div>
                                    <div class="space-y-1">
                                        <div class="h-1 bg-white/60 rounded"></div>
                                        <div class="h-1 bg-white/40 rounded w-4/5"></div>
                                    </div>
                                </div>
                                <div class="p-4">
                                    <h4 class="font-semibold">Creative Portfolio</h4>
                                    <p class="text-sm text-gray-600">Perfect for creative professionals</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="bg-blue-600 text-white py-20">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 class="text-3xl md:text-4xl font-bold mb-4">
                Ready to Build Your Perfect Resume?
            </h2>
            <p class="text-xl mb-8 text-blue-100">
                Join thousands of professionals who have landed their dream jobs with CareerCraft
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <button class="bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
                    Get Started Free
                </button>
                <button class="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-colors">
                    View Pricing
                </button>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center">
                <h3 class="text-2xl font-bold mb-4">CareerCraft</h3>
                <p class="text-gray-400 mb-6">
                    The most advanced resume builder with AI-powered features
                </p>
                <div class="flex justify-center space-x-6">
                    <a href="#" class="text-gray-400 hover:text-white">Privacy</a>
                    <a href="#" class="text-gray-400 hover:text-white">Terms</a>
                    <a href="#" class="text-gray-400 hover:text-white">Support</a>
                </div>
            </div>
        </div>
    </footer>

    <script>
        // Initialize Lucide icons
        lucide.createIcons();

        // Demo tab functionality
        document.querySelectorAll('.demo-tab').forEach(tab => {
            tab.addEventListener('click', () => {
                // Remove active state from all tabs
                document.querySelectorAll('.demo-tab').forEach(t => {
                    t.classList.remove('text-blue-600', 'border-b-2', 'border-blue-600');
                    t.classList.add('text-gray-500');
                });
                
                // Add active state to clicked tab
                tab.classList.remove('text-gray-500');
                tab.classList.add('text-blue-600', 'border-b-2', 'border-blue-600');
                
                // Hide all demo content
                document.querySelectorAll('.demo-content').forEach(content => {
                    content.classList.add('hidden');
                });
                
                // Show selected demo content
                const targetDemo = tab.getAttribute('data-tab') + '-demo';
                document.getElementById(targetDemo).classList.remove('hidden');
            });
        });

        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    </script>
</body>
</html>
