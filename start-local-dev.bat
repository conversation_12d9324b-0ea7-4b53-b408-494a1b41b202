@echo off
REM CareerCraft Local Development Startup Script for Windows
REM This script sets up and starts the development environment

echo.
echo ========================================
echo   CareerCraft - Local Development
echo ========================================
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Node.js is not installed or not in PATH
    echo Please install Node.js 18+ from https://nodejs.org/
    pause
    exit /b 1
)

echo [INFO] Node.js version: 
node --version

REM Check if we're in the right directory
if not exist "package.json" (
    echo [ERROR] Please run this script from the careercraft-v2 root directory
    pause
    exit /b 1
)

REM Create .env.local if it doesn't exist
if not exist ".env.local" (
    echo [INFO] Creating .env.local file...
    (
        echo # Database
        echo DATABASE_URL="file:./dev.db"
        echo.
        echo # NextAuth.js
        echo NEXTAUTH_URL="http://localhost:3000"
        echo NEXTAUTH_SECRET="your-secret-key-change-this-in-production"
        echo.
        echo # OAuth Providers ^(Optional - for testing auth^)
        echo GOOGLE_CLIENT_ID="your-google-client-id"
        echo GOOGLE_CLIENT_SECRET="your-google-client-secret"
        echo GITHUB_ID="your-github-client-id"
        echo GITHUB_SECRET="your-github-client-secret"
        echo.
        echo # AI Services ^(Optional - for testing AI features^)
        echo OPENAI_API_KEY="your-openai-api-key"
        echo.
        echo # Feature Flags
        echo ENABLE_AI_FEATURES="true"
        echo ENABLE_PREMIUM_FEATURES="true"
        echo ENABLE_ANALYTICS="false"
    ) > .env.local
    echo [SUCCESS] Created .env.local file
    echo [WARNING] Please update with your actual credentials if needed
    echo [INFO] The app will work with mock data even without real API keys
) else (
    echo [SUCCESS] .env.local file already exists
)

echo.
echo [INFO] Installing dependencies...
call npm install
if %errorlevel% neq 0 (
    echo [ERROR] Failed to install dependencies
    pause
    exit /b 1
)

echo.
echo [INFO] Building packages...
call npm run build:packages
if %errorlevel% neq 0 (
    echo [ERROR] Failed to build packages
    pause
    exit /b 1
)

echo.
echo [INFO] Setting up database...
call npm run db:push
if %errorlevel% neq 0 (
    echo [WARNING] Database setup failed, but continuing...
)

echo.
echo [INFO] Seeding database with sample data...
call npm run db:seed
if %errorlevel% neq 0 (
    echo [WARNING] Database seeding failed, but continuing...
)

echo.
echo [INFO] Running quick tests...
echo.
echo Testing Database Layer...
call npm run test:db
echo.
echo Testing Authentication...
call npm run test:auth
echo.
echo Testing UI Components...
call npm run test:ui

echo.
echo ========================================
echo   Setup Complete! Starting Dev Server
echo ========================================
echo.
echo The application will be available at:
echo   http://localhost:3000
echo.
echo Demo accounts:
echo   Email: <EMAIL>
echo   Email: <EMAIL>
echo   Email: <EMAIL>
echo.
echo Press Ctrl+C to stop the development server
echo.

REM Start the development server
call npm run dev
