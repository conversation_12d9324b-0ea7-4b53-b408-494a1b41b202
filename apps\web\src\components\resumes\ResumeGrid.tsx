'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuSeparator, 
  DropdownMenuTrigger 
} from '@/components/ui/dropdown-menu'
import { 
  FileText, 
  Plus, 
  MoreVertical, 
  Edit, 
  Download, 
  Copy, 
  Trash2, 
  Eye,
  Calendar,
  Clock,
  Star,
  Globe
} from 'lucide-react'
import { formatDistanceToNow } from 'date-fns'

interface Resume {
  id: string
  title: string
  description?: string
  status: 'DRAFT' | 'PUBLISHED' | 'ARCHIVED'
  templateId?: string
  templateName?: string
  isPublic: boolean
  publicUrl?: string
  createdAt: Date
  updatedAt: Date
  sections?: {
    personalInfo?: any
    experience?: any[]
    education?: any[]
    skills?: any[]
  }
}

interface ResumeGridProps {
  searchQuery?: string
  statusFilter?: 'all' | 'draft' | 'published' | 'archived'
}

export function ResumeGrid({ searchQuery = '', statusFilter = 'all' }: ResumeGridProps) {
  const router = useRouter()
  const [resumes, setResumes] = useState<Resume[]>([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    fetchResumes()
  }, [])

  const fetchResumes = async () => {
    try {
      // Mock data for now - replace with actual API call
      const mockResumes: Resume[] = [
        {
          id: '1',
          title: 'Software Engineer Resume',
          description: 'Full-stack developer position at tech companies',
          status: 'PUBLISHED',
          templateId: 'modern-1',
          templateName: 'Modern Professional',
          isPublic: true,
          publicUrl: 'https://careercraft.com/resume/abc123',
          createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 7 days ago
          updatedAt: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
          sections: {
            personalInfo: { name: 'John Doe', email: '<EMAIL>' },
            experience: [{ company: 'Tech Corp', position: 'Senior Developer' }],
            education: [{ institution: 'University', degree: 'Computer Science' }],
            skills: ['React', 'Node.js', 'TypeScript']
          }
        },
        {
          id: '2',
          title: 'Product Manager Resume',
          description: 'Product management roles in SaaS companies',
          status: 'DRAFT',
          templateId: 'classic-1',
          templateName: 'Classic Professional',
          isPublic: false,
          createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), // 3 days ago
          updatedAt: new Date(Date.now() - 5 * 60 * 60 * 1000), // 5 hours ago
          sections: {
            personalInfo: { name: 'Jane Smith', email: '<EMAIL>' },
            experience: [{ company: 'SaaS Inc', position: 'Product Manager' }],
            education: [{ institution: 'Business School', degree: 'MBA' }],
            skills: ['Product Strategy', 'Analytics', 'Leadership']
          }
        },
        {
          id: '3',
          title: 'Data Scientist Resume',
          description: 'Machine learning and analytics positions',
          status: 'ARCHIVED',
          templateId: 'creative-1',
          templateName: 'Creative Modern',
          isPublic: false,
          createdAt: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000), // 14 days ago
          updatedAt: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000), // 10 days ago
          sections: {
            personalInfo: { name: 'Alex Johnson', email: '<EMAIL>' },
            experience: [{ company: 'Data Corp', position: 'Data Scientist' }],
            education: [{ institution: 'Tech University', degree: 'Data Science' }],
            skills: ['Python', 'Machine Learning', 'SQL']
          }
        }
      ]

      // Filter resumes based on search and status
      let filteredResumes = mockResumes

      if (searchQuery) {
        filteredResumes = filteredResumes.filter(resume =>
          resume.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
          resume.description?.toLowerCase().includes(searchQuery.toLowerCase())
        )
      }

      if (statusFilter !== 'all') {
        filteredResumes = filteredResumes.filter(resume =>
          resume.status.toLowerCase() === statusFilter.toLowerCase()
        )
      }

      setResumes(filteredResumes)
    } catch (error) {
      console.error('Failed to fetch resumes:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const getStatusColor = (status: Resume['status']) => {
    switch (status) {
      case 'PUBLISHED':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
      case 'DRAFT':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400'
      case 'ARCHIVED':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'
    }
  }

  const handleEdit = (resumeId: string) => {
    router.push(`/dashboard/resumes/${resumeId}/edit`)
  }

  const handleView = (resumeId: string) => {
    router.push(`/dashboard/resumes/${resumeId}`)
  }

  const handleDownload = async (resumeId: string) => {
    // Implement download functionality
    console.log('Downloading resume:', resumeId)
  }

  const handleDuplicate = async (resumeId: string) => {
    // Implement duplicate functionality
    console.log('Duplicating resume:', resumeId)
  }

  const handleDelete = async (resumeId: string) => {
    // Implement delete functionality
    console.log('Deleting resume:', resumeId)
  }

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {[...Array(6)].map((_, i) => (
          <Card key={i} className="glass-card animate-pulse">
            <CardContent className="p-6">
              <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-1/2 mb-4"></div>
              <div className="h-20 bg-gray-200 rounded mb-4"></div>
              <div className="flex justify-between">
                <div className="h-6 bg-gray-200 rounded w-16"></div>
                <div className="h-6 bg-gray-200 rounded w-8"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  if (resumes.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="glass-panel p-8 rounded-2xl max-w-md mx-auto">
          <FileText className="w-16 h-16 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">No resumes found</h3>
          <p className="text-muted-foreground mb-6">
            {searchQuery || statusFilter !== 'all' 
              ? 'Try adjusting your search or filters'
              : 'Create your first resume to get started'
            }
          </p>
          <Button 
            onClick={() => router.push('/dashboard/resumes/new')}
            className="glass-card bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white border-0"
          >
            <Plus className="w-4 h-4 mr-2" />
            Create Resume
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {resumes.map((resume) => (
        <Card key={resume.id} className="glass-card hover:scale-105 transition-all duration-200 group">
          <CardHeader className="pb-3">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <CardTitle className="text-lg font-semibold line-clamp-1">
                  {resume.title}
                </CardTitle>
                <CardDescription className="line-clamp-2 mt-1">
                  {resume.description || 'No description'}
                </CardDescription>
              </div>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="icon" className="opacity-0 group-hover:opacity-100 transition-opacity">
                    <MoreVertical className="w-4 h-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="glass-card">
                  <DropdownMenuItem onClick={() => handleView(resume.id)}>
                    <Eye className="mr-2 h-4 w-4" />
                    View
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleEdit(resume.id)}>
                    <Edit className="mr-2 h-4 w-4" />
                    Edit
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleDownload(resume.id)}>
                    <Download className="mr-2 h-4 w-4" />
                    Download
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleDuplicate(resume.id)}>
                    <Copy className="mr-2 h-4 w-4" />
                    Duplicate
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem 
                    onClick={() => handleDelete(resume.id)}
                    className="text-red-600"
                  >
                    <Trash2 className="mr-2 h-4 w-4" />
                    Delete
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </CardHeader>

          <CardContent className="pt-0">
            {/* Template and Status */}
            <div className="flex items-center justify-between mb-4">
              <Badge variant="secondary" className="glass-input">
                {resume.templateName || 'No Template'}
              </Badge>
              <Badge className={getStatusColor(resume.status)}>
                {resume.status}
              </Badge>
            </div>

            {/* Resume Preview */}
            <div className="bg-white dark:bg-gray-800 rounded-lg p-4 mb-4 min-h-[120px] border border-gray-200 dark:border-gray-700">
              <div className="space-y-2">
                <div className="h-3 bg-gray-300 dark:bg-gray-600 rounded w-3/4"></div>
                <div className="h-2 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
                <div className="space-y-1 mt-3">
                  <div className="h-2 bg-gray-200 dark:bg-gray-700 rounded w-full"></div>
                  <div className="h-2 bg-gray-200 dark:bg-gray-700 rounded w-4/5"></div>
                  <div className="h-2 bg-gray-200 dark:bg-gray-700 rounded w-3/5"></div>
                </div>
              </div>
            </div>

            {/* Metadata */}
            <div className="space-y-2 text-xs text-muted-foreground">
              <div className="flex items-center space-x-2">
                <Calendar className="w-3 h-3" />
                <span>Created {formatDistanceToNow(resume.createdAt, { addSuffix: true })}</span>
              </div>
              <div className="flex items-center space-x-2">
                <Clock className="w-3 h-3" />
                <span>Updated {formatDistanceToNow(resume.updatedAt, { addSuffix: true })}</span>
              </div>
              {resume.isPublic && (
                <div className="flex items-center space-x-2">
                  <Globe className="w-3 h-3" />
                  <span>Public</span>
                </div>
              )}
            </div>

            {/* Action Buttons */}
            <div className="flex space-x-2 mt-4">
              <Button 
                variant="outline" 
                size="sm" 
                className="flex-1 glass-input"
                onClick={() => handleView(resume.id)}
              >
                <Eye className="w-3 h-3 mr-1" />
                View
              </Button>
              <Button 
                size="sm" 
                className="flex-1 glass-card bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white border-0"
                onClick={() => handleEdit(resume.id)}
              >
                <Edit className="w-3 h-3 mr-1" />
                Edit
              </Button>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
