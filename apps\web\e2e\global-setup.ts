import { chromium, FullConfig } from '@playwright/test'

async function globalSetup(config: FullConfig) {
  console.log('🚀 Starting global setup for E2E tests...')

  // Launch browser for setup
  const browser = await chromium.launch()
  const page = await browser.newPage()

  try {
    // Setup test database or mock services if needed
    console.log('📊 Setting up test environment...')

    // You could setup test data here
    // await setupTestDatabase()
    
    // Or setup mock services
    // await setupMockServices()

    // Verify the application is running
    const baseURL = config.projects[0].use?.baseURL || 'http://localhost:3000'
    
    console.log(`🌐 Checking if application is running at ${baseURL}...`)
    
    try {
      await page.goto(baseURL, { timeout: 30000 })
      console.log('✅ Application is running and accessible')
    } catch (error) {
      console.error('❌ Application is not accessible:', error)
      throw new Error(`Application at ${baseURL} is not accessible. Make sure it's running.`)
    }

    // Setup authentication state if needed
    console.log('🔐 Setting up authentication state...')
    
    // You could create a test user session here
    // await page.context().storageState({ path: 'e2e/auth.json' })

    console.log('✅ Global setup completed successfully')

  } catch (error) {
    console.error('❌ Global setup failed:', error)
    throw error
  } finally {
    await browser.close()
  }
}

export default globalSetup
