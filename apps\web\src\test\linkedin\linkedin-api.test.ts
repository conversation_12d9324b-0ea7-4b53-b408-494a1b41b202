/**
 * LinkedIn API Routes Integration Tests
 * 
 * Tests for LinkedIn authentication and import API endpoints
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { NextRequest } from 'next/server'
import { getServerSession } from 'next-auth'
import { GET as authGet, POST as authPost, DELETE as authDelete } from '@/app/api/linkedin/auth/route'
import { GET as importGet, POST as importPost, PUT as importPut } from '@/app/api/linkedin/import/route'

// Mock dependencies
vi.mock('next-auth', () => ({
  getServerSession: vi.fn()
}))

vi.mock('@/lib/auth', () => ({
  authOptions: {}
}))

vi.mock('@/lib/linkedin/client', () => ({
  linkedInClient: {
    getAuthorizationUrl: vi.fn(),
    getAccessToken: vi.fn(),
    getProfile: vi.fn()
  }
}))

vi.mock('@/lib/linkedin/service', () => ({
  linkedInService: {
    saveLinkedInProfile: vi.fn(),
    hasLinkedInProfile: vi.fn(),
    getLinkedInProfile: vi.fn(),
    importLinkedInData: vi.fn(),
    applyImportToResume: vi.fn(),
    getConnectionStatus: vi.fn(),
    getImportHistory: vi.fn(),
    disconnectLinkedIn: vi.fn()
  }
}))

describe('LinkedIn API Routes', () => {
  const mockSession = {
    user: {
      id: 'test-user-id',
      email: '<EMAIL>',
      name: 'Test User'
    }
  }

  beforeEach(() => {
    vi.clearAllMocks()
    ;(getServerSession as any).mockResolvedValue(mockSession)
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('LinkedIn Auth Routes', () => {
    describe('GET /api/linkedin/auth', () => {
      it('should generate authorization URL', async () => {
        const { linkedInClient } = await import('@/lib/linkedin/client')
        ;(linkedInClient.getAuthorizationUrl as any).mockReturnValue(
          'https://linkedin.com/oauth/authorize?client_id=test'
        )

        const request = new NextRequest('http://localhost:3000/api/linkedin/auth?action=authorize')
        const response = await authGet(request)
        const data = await response.json()

        expect(response.status).toBe(200)
        expect(data.authUrl).toContain('https://linkedin.com/oauth/authorize')
        expect(data.state).toContain('user_test-user-id_')
      })

      it('should require authentication', async () => {
        ;(getServerSession as any).mockResolvedValue(null)

        const request = new NextRequest('http://localhost:3000/api/linkedin/auth?action=authorize')
        const response = await authGet(request)
        const data = await response.json()

        expect(response.status).toBe(401)
        expect(data.error).toBe('Unauthorized')
      })

      it('should handle invalid action', async () => {
        const request = new NextRequest('http://localhost:3000/api/linkedin/auth?action=invalid')
        const response = await authGet(request)
        const data = await response.json()

        expect(response.status).toBe(400)
        expect(data.error).toBe('Invalid action')
      })
    })

    describe('POST /api/linkedin/auth', () => {
      it('should handle OAuth callback successfully', async () => {
        const { linkedInClient } = await import('@/lib/linkedin/client')
        const { linkedInService } = await import('@/lib/linkedin/service')

        const mockProfile = {
          id: 'linkedin-123',
          firstName: { localized: { 'en_US': 'John' } },
          lastName: { localized: { 'en_US': 'Doe' } },
          headline: 'Software Engineer'
        }

        ;(linkedInClient.getAccessToken as any).mockResolvedValue('access-token')
        ;(linkedInClient.getProfile as any).mockResolvedValue(mockProfile)
        ;(linkedInService.saveLinkedInProfile as any).mockResolvedValue(undefined)

        const requestBody = {
          action: 'callback',
          code: 'auth-code',
          state: 'user_test-user-id_123456789'
        }

        const request = new NextRequest('http://localhost:3000/api/linkedin/auth', {
          method: 'POST',
          body: JSON.stringify(requestBody),
          headers: { 'Content-Type': 'application/json' }
        })

        const response = await authPost(request)
        const data = await response.json()

        expect(response.status).toBe(200)
        expect(data.success).toBe(true)
        expect(data.message).toBe('LinkedIn account connected successfully')
        expect(data.profile.name).toBe('John Doe')
        expect(data.profile.headline).toBe('Software Engineer')
      })

      it('should handle OAuth errors', async () => {
        const requestBody = {
          action: 'callback',
          error: 'access_denied'
        }

        const request = new NextRequest('http://localhost:3000/api/linkedin/auth', {
          method: 'POST',
          body: JSON.stringify(requestBody),
          headers: { 'Content-Type': 'application/json' }
        })

        const response = await authPost(request)
        const data = await response.json()

        expect(response.status).toBe(400)
        expect(data.error).toContain('LinkedIn OAuth error: access_denied')
      })

      it('should validate state parameter', async () => {
        const requestBody = {
          action: 'callback',
          code: 'auth-code',
          state: 'invalid-state'
        }

        const request = new NextRequest('http://localhost:3000/api/linkedin/auth', {
          method: 'POST',
          body: JSON.stringify(requestBody),
          headers: { 'Content-Type': 'application/json' }
        })

        const response = await authPost(request)
        const data = await response.json()

        expect(response.status).toBe(400)
        expect(data.error).toBe('Invalid state parameter')
      })

      it('should handle missing authorization code', async () => {
        const requestBody = {
          action: 'callback',
          state: 'user_test-user-id_123456789'
        }

        const request = new NextRequest('http://localhost:3000/api/linkedin/auth', {
          method: 'POST',
          body: JSON.stringify(requestBody),
          headers: { 'Content-Type': 'application/json' }
        })

        const response = await authPost(request)
        const data = await response.json()

        expect(response.status).toBe(400)
        expect(data.error).toBe('Authorization code is required')
      })
    })

    describe('DELETE /api/linkedin/auth', () => {
      it('should disconnect LinkedIn account', async () => {
        const { linkedInService } = await import('@/lib/linkedin/service')
        ;(linkedInService.disconnectLinkedIn as any).mockResolvedValue(true)

        const request = new NextRequest('http://localhost:3000/api/linkedin/auth', {
          method: 'DELETE'
        })

        const response = await authDelete(request)
        const data = await response.json()

        expect(response.status).toBe(200)
        expect(data.success).toBe(true)
        expect(data.message).toBe('LinkedIn account disconnected successfully')
      })

      it('should handle disconnect errors', async () => {
        const { linkedInService } = await import('@/lib/linkedin/service')
        ;(linkedInService.disconnectLinkedIn as any).mockResolvedValue(false)

        const request = new NextRequest('http://localhost:3000/api/linkedin/auth', {
          method: 'DELETE'
        })

        const response = await authDelete(request)
        const data = await response.json()

        expect(response.status).toBe(500)
        expect(data.error).toBe('Failed to disconnect LinkedIn account')
      })
    })
  })

  describe('LinkedIn Import Routes', () => {
    describe('POST /api/linkedin/import', () => {
      it('should import LinkedIn data successfully', async () => {
        const { linkedInService } = await import('@/lib/linkedin/service')

        const mockImportResult = {
          success: true,
          importId: 'import-123',
          importedData: {
            personalInfo: { firstName: 'John', lastName: 'Doe' },
            experience: []
          }
        }

        ;(linkedInService.hasLinkedInProfile as any).mockResolvedValue(true)
        ;(linkedInService.getLinkedInProfile as any).mockResolvedValue({
          id: 'linkedin-123'
        })
        ;(linkedInService.importLinkedInData as any).mockResolvedValue(mockImportResult)

        const requestBody = {
          resumeId: 'resume-123',
          sections: ['personalInfo', 'experience']
        }

        const request = new NextRequest('http://localhost:3000/api/linkedin/import', {
          method: 'POST',
          body: JSON.stringify(requestBody),
          headers: { 'Content-Type': 'application/json' }
        })

        const response = await importPost(request)
        const data = await response.json()

        expect(response.status).toBe(200)
        expect(data.success).toBe(true)
        expect(data.importId).toBe('import-123')
        expect(data.importedData).toEqual(mockImportResult.importedData)
      })

      it('should handle missing LinkedIn connection', async () => {
        const { linkedInService } = await import('@/lib/linkedin/service')
        ;(linkedInService.hasLinkedInProfile as any).mockResolvedValue(false)

        const requestBody = {
          resumeId: 'resume-123',
          sections: ['personalInfo']
        }

        const request = new NextRequest('http://localhost:3000/api/linkedin/import', {
          method: 'POST',
          body: JSON.stringify(requestBody),
          headers: { 'Content-Type': 'application/json' }
        })

        const response = await importPost(request)
        const data = await response.json()

        expect(response.status).toBe(400)
        expect(data.error).toBe('LinkedIn account not connected')
      })

      it('should handle import failures', async () => {
        const { linkedInService } = await import('@/lib/linkedin/service')

        ;(linkedInService.hasLinkedInProfile as any).mockResolvedValue(true)
        ;(linkedInService.getLinkedInProfile as any).mockResolvedValue({})
        ;(linkedInService.importLinkedInData as any).mockResolvedValue({
          success: false,
          errors: ['Import failed']
        })

        const requestBody = {
          resumeId: 'resume-123',
          sections: ['personalInfo']
        }

        const request = new NextRequest('http://localhost:3000/api/linkedin/import', {
          method: 'POST',
          body: JSON.stringify(requestBody),
          headers: { 'Content-Type': 'application/json' }
        })

        const response = await importPost(request)
        const data = await response.json()

        expect(response.status).toBe(500)
        expect(data.error).toBe('Import failed')
        expect(data.details).toEqual(['Import failed'])
      })
    })

    describe('PUT /api/linkedin/import', () => {
      it('should apply import to resume successfully', async () => {
        const { linkedInService } = await import('@/lib/linkedin/service')
        ;(linkedInService.applyImportToResume as any).mockResolvedValue(true)

        const requestBody = {
          resumeId: 'resume-123',
          importId: 'import-123',
          sections: ['personalInfo', 'experience']
        }

        const request = new NextRequest('http://localhost:3000/api/linkedin/import', {
          method: 'PUT',
          body: JSON.stringify(requestBody),
          headers: { 'Content-Type': 'application/json' }
        })

        const response = await importPut(request)
        const data = await response.json()

        expect(response.status).toBe(200)
        expect(data.success).toBe(true)
        expect(data.message).toBe('Import applied to resume successfully')
      })

      it('should handle apply failures', async () => {
        const { linkedInService } = await import('@/lib/linkedin/service')
        ;(linkedInService.applyImportToResume as any).mockResolvedValue(false)

        const requestBody = {
          resumeId: 'resume-123',
          importId: 'import-123',
          sections: ['personalInfo']
        }

        const request = new NextRequest('http://localhost:3000/api/linkedin/import', {
          method: 'PUT',
          body: JSON.stringify(requestBody),
          headers: { 'Content-Type': 'application/json' }
        })

        const response = await importPut(request)
        const data = await response.json()

        expect(response.status).toBe(500)
        expect(data.error).toBe('Failed to apply import to resume')
      })
    })

    describe('GET /api/linkedin/import', () => {
      it('should get connection status', async () => {
        const { linkedInService } = await import('@/lib/linkedin/service')
        
        const mockStatus = {
          connected: true,
          lastSynced: new Date(),
          profileData: { name: 'John Doe' }
        }

        ;(linkedInService.getConnectionStatus as any).mockResolvedValue(mockStatus)

        const request = new NextRequest('http://localhost:3000/api/linkedin/import?action=status')
        const response = await importGet(request)
        const data = await response.json()

        expect(response.status).toBe(200)
        expect(data).toEqual(mockStatus)
      })

      it('should get import history', async () => {
        const { linkedInService } = await import('@/lib/linkedin/service')
        
        const mockHistory = [
          {
            id: 'import-1',
            resumeTitle: 'Software Engineer Resume',
            status: 'completed',
            createdAt: new Date().toISOString(),
            importedSections: ['personalInfo', 'experience']
          }
        ]

        ;(linkedInService.getImportHistory as any).mockResolvedValue(mockHistory)

        const request = new NextRequest('http://localhost:3000/api/linkedin/import?action=history&limit=5')
        const response = await importGet(request)
        const data = await response.json()

        expect(response.status).toBe(200)
        expect(data.history).toEqual(mockHistory)
      })

      it('should handle invalid action', async () => {
        const request = new NextRequest('http://localhost:3000/api/linkedin/import?action=invalid')
        const response = await importGet(request)
        const data = await response.json()

        expect(response.status).toBe(400)
        expect(data.error).toBe('Invalid action')
      })
    })
  })
})
