import { GET } from '@/app/api/health/route'
import { prisma } from '@/lib/prisma'

// Mock Prisma
jest.mock('@/lib/prisma', () => ({
  prisma: {
    $queryRaw: jest.fn(),
  },
}))

describe('/api/health', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('should return healthy status when database is connected', async () => {
    // Mock successful database connection
    ;(prisma.$queryRaw as jest.Mock).mockResolvedValue([{ '?column?': 1 }])

    const response = await GET()
    const data = await response.json()

    expect(response.status).toBe(200)
    expect(data.status).toBe('healthy')
    expect(data.services.database).toBe('connected')
    expect(data.timestamp).toBeDefined()
    expect(data.version).toBe('1.0.0')
  })

  it('should return unhealthy status when database connection fails', async () => {
    // Mock database connection failure
    ;(prisma.$queryRaw as jest.Mock).mockRejectedValue(new Error('Connection failed'))

    const response = await GET()
    const data = await response.json()

    expect(response.status).toBe(500)
    expect(data.status).toBe('unhealthy')
    expect(data.error).toBe('Database connection failed')
  })

  it('should show service configuration status', async () => {
    ;(prisma.$queryRaw as jest.Mock).mockResolvedValue([{ '?column?': 1 }])

    // Set environment variables
    process.env.OPENAI_API_KEY = 'test-key'
    process.env.STRIPE_SECRET_KEY = 'test-key'

    const response = await GET()
    const data = await response.json()

    expect(data.services.openai).toBe('configured')
    expect(data.services.stripe).toBe('configured')

    // Clean up
    delete process.env.OPENAI_API_KEY
    delete process.env.STRIPE_SECRET_KEY
  })
})
