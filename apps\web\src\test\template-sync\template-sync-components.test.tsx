/**
 * Template Sync Components Unit Tests
 * 
 * Tests for template sync and marketplace React components
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { TemplateSyncDashboard } from '@/components/template-sync/TemplateSyncDashboard'
import { TemplateMarketplace } from '@/components/template-sync/TemplateMarketplace'
import { CloudTemplateBuilder } from '@/components/template-sync/CloudTemplateBuilder'

// Mock sonner for toast notifications
vi.mock('sonner', () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn(),
    info: vi.fn(),
    warning: vi.fn()
  }
}))

// Mock date-fns
vi.mock('date-fns', () => ({
  formatDistanceToNow: vi.fn(() => '2 hours ago'),
  format: vi.fn(() => '2024-01-15 10:30 AM')
}))

// Mock fetch for API calls
global.fetch = vi.fn()

describe('Template Sync Components', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    
    ;(global.fetch as any).mockResolvedValue({
      ok: true,
      json: () => Promise.resolve({})
    })
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('TemplateSyncDashboard', () => {
    const mockSyncStatus = {
      totalTemplates: 10,
      syncedTemplates: 8,
      pendingSync: 1,
      syncErrors: 1,
      conflicts: 2,
      lastSyncTime: new Date('2024-01-15T10:30:00Z')
    }

    const mockConflicts = [
      {
        id: 'conflict-1',
        templateId: 'template-1',
        conflictType: 'concurrent_edit',
        localVersion: { name: 'Local Version' },
        remoteVersion: { name: 'Remote Version' },
        timestamp: new Date('2024-01-15T09:00:00Z')
      }
    ]

    beforeEach(() => {
      ;(global.fetch as any)
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ status: mockSyncStatus })
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ conflicts: mockConflicts })
        })
    })

    it('should render sync status dashboard', async () => {
      render(<TemplateSyncDashboard />)

      await waitFor(() => {
        expect(screen.getByText('Template Sync Status')).toBeInTheDocument()
        expect(screen.getByText('Total Templates')).toBeInTheDocument()
        expect(screen.getByText('10')).toBeInTheDocument()
        expect(screen.getByText('8')).toBeInTheDocument()
        expect(screen.getByText('1')).toBeInTheDocument()
        expect(screen.getByText('2')).toBeInTheDocument()
      })
    })

    it('should display sync progress correctly', async () => {
      render(<TemplateSyncDashboard />)

      await waitFor(() => {
        expect(screen.getByText('80%')).toBeInTheDocument() // 8/10 * 100
      })
    })

    it('should show conflicts tab with conflict details', async () => {
      render(<TemplateSyncDashboard />)

      await waitFor(() => {
        expect(screen.getByText('Conflicts (1)')).toBeInTheDocument()
      })

      const conflictsTab = screen.getByText('Conflicts (1)')
      fireEvent.click(conflictsTab)

      await waitFor(() => {
        expect(screen.getByText('Template Conflict')).toBeInTheDocument()
        expect(screen.getByText('Concurrent edits detected')).toBeInTheDocument()
        expect(screen.getByText('Local Version')).toBeInTheDocument()
        expect(screen.getByText('Remote Version')).toBeInTheDocument()
      })
    })

    it('should handle sync all templates', async () => {
      const user = userEvent.setup()
      const { toast } = require('sonner')

      ;(global.fetch as any)
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ status: mockSyncStatus })
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ conflicts: [] })
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            result: {
              syncedTemplates: 5,
              errors: []
            }
          })
        })

      render(<TemplateSyncDashboard />)

      await waitFor(() => {
        expect(screen.getByText('Sync All')).toBeInTheDocument()
      })

      const syncButton = screen.getByText('Sync All')
      await user.click(syncButton)

      await waitFor(() => {
        expect(toast.success).toHaveBeenCalledWith('Successfully synced 5 templates')
      })
    })

    it('should handle conflict resolution', async () => {
      const user = userEvent.setup()
      const { toast } = require('sonner')

      ;(global.fetch as any)
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ status: mockSyncStatus })
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ conflicts: mockConflicts })
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ success: true })
        })

      render(<TemplateSyncDashboard />)

      await waitFor(() => {
        expect(screen.getByText('Conflicts (1)')).toBeInTheDocument()
      })

      const conflictsTab = screen.getByText('Conflicts (1)')
      fireEvent.click(conflictsTab)

      await waitFor(() => {
        expect(screen.getByText('Keep Local')).toBeInTheDocument()
      })

      const keepLocalButton = screen.getByText('Keep Local')
      await user.click(keepLocalButton)

      await waitFor(() => {
        expect(toast.success).toHaveBeenCalledWith('Conflict resolved successfully')
      })
    })

    it('should show version history for selected template', async () => {
      const user = userEvent.setup()
      const mockVersions = [
        {
          id: 'version-1',
          versionNumber: 1,
          versionName: 'Version 1',
          changesSummary: 'Initial version',
          createdAt: new Date(),
          createdBy: { name: 'John Doe' }
        }
      ]

      ;(global.fetch as any)
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ status: mockSyncStatus })
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ conflicts: [] })
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ history: mockVersions })
        })

      render(<TemplateSyncDashboard />)

      await waitFor(() => {
        expect(screen.getByText('Version History')).toBeInTheDocument()
      })

      const historyTab = screen.getByText('Version History')
      fireEvent.click(historyTab)

      await waitFor(() => {
        expect(screen.getByText('Select Template')).toBeInTheDocument()
      })

      const templateSelect = screen.getByRole('combobox')
      await user.selectOptions(templateSelect, 'template-1')

      await waitFor(() => {
        expect(screen.getByText('Version 1')).toBeInTheDocument()
        expect(screen.getByText('Initial version')).toBeInTheDocument()
      })
    })

    it('should handle API errors gracefully', async () => {
      const { toast } = require('sonner')

      ;(global.fetch as any).mockRejectedValue(new Error('Network error'))

      render(<TemplateSyncDashboard />)

      await waitFor(() => {
        expect(toast.error).toHaveBeenCalledWith('Failed to load sync status')
      })
    })

    it('should show loading state initially', () => {
      ;(global.fetch as any).mockImplementation(() => new Promise(() => {})) // Never resolves

      render(<TemplateSyncDashboard />)

      expect(document.querySelectorAll('.animate-pulse')).toHaveLength(1)
    })
  })

  describe('TemplateMarketplace', () => {
    const mockTemplates = [
      {
        id: 'template-1',
        name: 'Professional Resume',
        description: 'Clean professional template',
        category: 'professional',
        tags: ['modern', 'clean'],
        price: 0,
        rating: 4.5,
        reviewCount: 25,
        downloadCount: 100,
        isFeatured: false,
        difficulty: 'beginner',
        seller: { id: 'user-1', name: 'John Doe', email: '<EMAIL>' },
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        id: 'template-2',
        name: 'Creative Portfolio',
        description: 'Creative design template',
        category: 'creative',
        tags: ['creative', 'portfolio'],
        price: 9.99,
        rating: 4.8,
        reviewCount: 40,
        downloadCount: 75,
        isFeatured: true,
        difficulty: 'intermediate',
        seller: { id: 'user-2', name: 'Jane Smith', email: '<EMAIL>' },
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ]

    const mockCategories = [
      { id: 'professional', name: 'Professional', count: 45 },
      { id: 'creative', name: 'Creative', count: 32 }
    ]

    beforeEach(() => {
      ;(global.fetch as any)
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ templates: mockTemplates.slice(0, 1) })
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ categories: mockCategories })
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ templates: mockTemplates })
        })
    })

    it('should render marketplace interface', async () => {
      render(<TemplateMarketplace />)

      await waitFor(() => {
        expect(screen.getByText('Template Marketplace')).toBeInTheDocument()
        expect(screen.getByText('Discover and download professional resume templates')).toBeInTheDocument()
        expect(screen.getByPlaceholderText('Search templates...')).toBeInTheDocument()
      })
    })

    it('should display templates in grid layout', async () => {
      render(<TemplateMarketplace />)

      await waitFor(() => {
        expect(screen.getByText('Professional Resume')).toBeInTheDocument()
        expect(screen.getByText('Creative Portfolio')).toBeInTheDocument()
        expect(screen.getByText('Free')).toBeInTheDocument()
        expect(screen.getByText('$9.99')).toBeInTheDocument()
      })
    })

    it('should handle template search', async () => {
      const user = userEvent.setup()

      ;(global.fetch as any)
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ templates: [] })
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ categories: mockCategories })
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ templates: [mockTemplates[0]] })
        })

      render(<TemplateMarketplace />)

      const searchInput = screen.getByPlaceholderText('Search templates...')
      await user.type(searchInput, 'professional')

      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalledWith(
          expect.stringContaining('action=search&query=professional')
        )
      })
    })

    it('should handle category filtering', async () => {
      const user = userEvent.setup()

      render(<TemplateMarketplace />)

      await waitFor(() => {
        expect(screen.getByText('All Categories')).toBeInTheDocument()
      })

      const categorySelect = screen.getByDisplayValue('All Categories')
      await user.click(categorySelect)

      const professionalOption = screen.getByText('Professional (45)')
      await user.click(professionalOption)

      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalledWith(
          expect.stringContaining('category=professional')
        )
      })
    })

    it('should handle template purchase', async () => {
      const user = userEvent.setup()
      const { toast } = require('sonner')

      ;(global.fetch as any)
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ templates: [] })
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ categories: mockCategories })
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ templates: mockTemplates })
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            success: true,
            purchaseId: 'purchase-1',
            templateData: { name: 'Professional Resume' }
          })
        })

      render(<TemplateMarketplace />)

      await waitFor(() => {
        expect(screen.getByText('Professional Resume')).toBeInTheDocument()
      })

      const downloadButton = screen.getAllByText('Download')[0]
      await user.click(downloadButton)

      await waitFor(() => {
        expect(toast.success).toHaveBeenCalledWith('Template purchased successfully!')
      })
    })

    it('should handle template review submission', async () => {
      const user = userEvent.setup()
      const { toast } = require('sonner')

      render(<TemplateMarketplace />)

      await waitFor(() => {
        expect(screen.getByText('Professional Resume')).toBeInTheDocument()
      })

      // Find and click the star rating button
      const starButtons = screen.getAllByRole('button')
      const ratingButton = starButtons.find(button => 
        button.querySelector('svg') && button.getAttribute('class')?.includes('Star')
      )

      if (ratingButton) {
        await user.click(ratingButton)

        await waitFor(() => {
          expect(screen.getByText('Rate Template')).toBeInTheDocument()
        })

        // Submit review
        ;(global.fetch as any).mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ success: true })
        })

        const submitButton = screen.getByRole('button', { name: 'Submit Review' })
        await user.click(submitButton)

        await waitFor(() => {
          expect(toast.success).toHaveBeenCalledWith('Review added successfully!')
        })
      }
    })

    it('should show featured templates tab', async () => {
      const user = userEvent.setup()

      render(<TemplateMarketplace />)

      await waitFor(() => {
        expect(screen.getByText('Featured')).toBeInTheDocument()
      })

      const featuredTab = screen.getByText('Featured')
      await user.click(featuredTab)

      await waitFor(() => {
        expect(screen.getByText('Creative Portfolio')).toBeInTheDocument()
        expect(screen.getByText('Featured')).toBeInTheDocument()
      })
    })

    it('should handle template selection callback', async () => {
      const onTemplateSelect = vi.fn()

      render(<TemplateMarketplace onTemplateSelect={onTemplateSelect} />)

      await waitFor(() => {
        expect(screen.getByText('Professional Resume')).toBeInTheDocument()
      })

      const templateTitle = screen.getByText('Professional Resume')
      fireEvent.click(templateTitle)

      expect(onTemplateSelect).toHaveBeenCalledWith(mockTemplates[0])
    })

    it('should show loading state', () => {
      ;(global.fetch as any).mockImplementation(() => new Promise(() => {})) // Never resolves

      render(<TemplateMarketplace />)

      expect(document.querySelectorAll('.animate-pulse')).toHaveLength(6)
    })

    it('should handle API errors', async () => {
      const { toast } = require('sonner')

      ;(global.fetch as any).mockRejectedValue(new Error('Network error'))

      render(<TemplateMarketplace />)

      await waitFor(() => {
        expect(toast.error).toHaveBeenCalledWith('Failed to search templates')
      })
    })
  })

  describe('CloudTemplateBuilder', () => {
    const mockTemplate = {
      id: 'template-1',
      name: 'Test Template',
      description: 'Test description',
      category: 'professional',
      tags: ['test'],
      elements: [
        {
          id: 'element-1',
          type: 'text',
          content: 'Sample Text',
          styles: { fontSize: 16, color: '#000000' },
          position: { x: 10, y: 10, width: 80, height: 40 }
        }
      ],
      globalStyles: {
        fontFamily: 'Inter',
        primaryColor: '#1f2937',
        secondaryColor: '#6b7280',
        backgroundColor: '#ffffff',
        pageMargin: 20
      },
      layout: {
        columns: 1,
        spacing: 16,
        responsive: true
      }
    }

    it('should render template builder interface', () => {
      render(<CloudTemplateBuilder />)

      expect(screen.getByText('Untitled Template')).toBeInTheDocument()
      expect(screen.getByText('Elements')).toBeInTheDocument()
      expect(screen.getByText('Styles')).toBeInTheDocument()
      expect(screen.getByText('Settings')).toBeInTheDocument()
    })

    it('should add new text element', async () => {
      const user = userEvent.setup()

      render(<CloudTemplateBuilder />)

      const addTextButton = screen.getByText('Text')
      await user.click(addTextButton)

      await waitFor(() => {
        expect(screen.getByText('New Text')).toBeInTheDocument()
      })
    })

    it('should handle element selection and style editing', async () => {
      const user = userEvent.setup()

      render(<CloudTemplateBuilder />)

      // Add a text element first
      const addTextButton = screen.getByText('Text')
      await user.click(addTextButton)

      await waitFor(() => {
        expect(screen.getByText('New Text')).toBeInTheDocument()
      })

      // Click on the element to select it
      const textElement = screen.getByText('New Text')
      await user.click(textElement)

      // Switch to styles tab
      const stylesTab = screen.getByText('Styles')
      await user.click(stylesTab)

      await waitFor(() => {
        expect(screen.getByText('Element Styles')).toBeInTheDocument()
        expect(screen.getByText('Content')).toBeInTheDocument()
        expect(screen.getByText('Font Size')).toBeInTheDocument()
      })
    })

    it('should handle template saving', async () => {
      const user = userEvent.setup()
      const { toast } = require('sonner')

      ;(global.fetch as any).mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({ success: true })
      })

      render(<CloudTemplateBuilder />)

      const saveButton = screen.getByText('Save')
      await user.click(saveButton)

      await waitFor(() => {
        expect(toast.success).toHaveBeenCalledWith('Template saved successfully!')
      })
    })

    it('should handle view mode changes', async () => {
      const user = userEvent.setup()

      render(<CloudTemplateBuilder />)

      // Find mobile view button
      const viewModeButtons = screen.getAllByRole('button')
      const mobileButton = viewModeButtons.find(button => 
        button.querySelector('svg') && button.getAttribute('class')?.includes('Smartphone')
      )

      if (mobileButton) {
        await user.click(mobileButton)

        // Canvas should change size for mobile view
        const canvas = document.querySelector('[style*="375px"]')
        expect(canvas).toBeInTheDocument()
      }
    })

    it('should handle template export', async () => {
      const user = userEvent.setup()

      render(<CloudTemplateBuilder />)

      const exportButton = screen.getByText('Export')
      await user.click(exportButton)

      await waitFor(() => {
        expect(screen.getByText('Export Template')).toBeInTheDocument()
        expect(screen.getByText('Export as JSON')).toBeInTheDocument()
        expect(screen.getByText('Export as PDF')).toBeInTheDocument()
        expect(screen.getByText('Export as HTML')).toBeInTheDocument()
      })
    })

    it('should handle undo/redo operations', async () => {
      const user = userEvent.setup()

      render(<CloudTemplateBuilder />)

      // Add an element to create history
      const addTextButton = screen.getByText('Text')
      await user.click(addTextButton)

      await waitFor(() => {
        expect(screen.getByText('New Text')).toBeInTheDocument()
      })

      // Find undo button
      const undoButton = screen.getAllByRole('button').find(button => 
        button.querySelector('svg') && button.getAttribute('title') === 'Undo'
      )

      if (undoButton) {
        await user.click(undoButton)
        // Element should be removed after undo
        expect(screen.queryByText('New Text')).not.toBeInTheDocument()
      }
    })

    it('should handle template settings updates', async () => {
      const user = userEvent.setup()

      render(<CloudTemplateBuilder />)

      const settingsTab = screen.getByText('Settings')
      await user.click(settingsTab)

      await waitFor(() => {
        expect(screen.getByText('Template Settings')).toBeInTheDocument()
      })

      const nameInput = screen.getByDisplayValue('Untitled Template')
      await user.clear(nameInput)
      await user.type(nameInput, 'My Custom Template')

      expect(nameInput).toHaveValue('My Custom Template')
    })

    it('should handle element duplication and deletion', async () => {
      const user = userEvent.setup()

      render(<CloudTemplateBuilder />)

      // Add a text element
      const addTextButton = screen.getByText('Text')
      await user.click(addTextButton)

      await waitFor(() => {
        expect(screen.getByText('New Text')).toBeInTheDocument()
      })

      // Find duplicate button in elements panel
      const elementsTab = screen.getByText('Elements')
      await user.click(elementsTab)

      // Look for copy/duplicate button
      const copyButtons = screen.getAllByRole('button')
      const copyButton = copyButtons.find(button => 
        button.querySelector('svg') && button.getAttribute('class')?.includes('Copy')
      )

      if (copyButton) {
        await user.click(copyButton)
        // Should have two elements now
        expect(screen.getAllByText(/New Text/)).toHaveLength(2)
      }
    })
  })

  describe('Integration Tests', () => {
    it('should handle network errors gracefully', async () => {
      const { toast } = require('sonner')

      ;(global.fetch as any).mockRejectedValue(new Error('Network error'))

      render(<TemplateSyncDashboard />)

      await waitFor(() => {
        expect(toast.error).toHaveBeenCalled()
      })
    })

    it('should handle malformed API responses', async () => {
      const { toast } = require('sonner')

      ;(global.fetch as any).mockResolvedValue({
        ok: true,
        json: () => Promise.reject(new Error('Invalid JSON'))
      })

      render(<TemplateMarketplace />)

      await waitFor(() => {
        expect(toast.error).toHaveBeenCalled()
      })
    })

    it('should show appropriate loading states', () => {
      ;(global.fetch as any).mockImplementation(() => new Promise(() => {})) // Never resolves

      render(<TemplateSyncDashboard />)

      expect(document.querySelectorAll('.animate-pulse')).toHaveLength(1)
    })
  })
})
