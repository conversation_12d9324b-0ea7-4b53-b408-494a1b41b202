# Development Guide

## Quick Start

### Prerequisites
- Node.js 18+ 
- PostgreSQL 14+
- Git

### Setup
```bash
# Clone the repository
git clone <repository-url>
cd careercraft-v2

# Run setup script
chmod +x scripts/setup.sh
./scripts/setup.sh

# Or on Windows
scripts\setup.bat
```

### Environment Configuration
1. Copy `.env.example` to `.env.local`
2. Update the following required variables:
   ```env
   DATABASE_URL="postgresql://username:password@localhost:5432/careercraft_v2"
   NEXTAUTH_SECRET="your-secret-key-here"
   OPENAI_API_KEY="your-openai-api-key"
   ```

### Database Setup
```bash
# Generate Prisma client
npm run db:generate

# Push schema to database
npm run db:push

# (Optional) Seed with sample data
npm run db:seed
```

### Start Development
```bash
npm run dev
```

Visit `http://localhost:3000` to see the application.

## Project Structure

```
careercraft-v2/
├── apps/
│   └── web/                    # Next.js frontend application
│       ├── src/
│       │   ├── app/           # App Router pages
│       │   ├── components/    # React components
│       │   ├── lib/          # Utility libraries
│       │   ├── hooks/        # Custom React hooks
│       │   └── store/        # State management
│       ├── public/           # Static assets
│       └── package.json
├── packages/
│   ├── database/             # Prisma schema and client
│   ├── types/               # Shared TypeScript types
│   ├── ui/                  # Shared UI components
│   └── utils/               # Shared utilities
├── docs/                    # Documentation
└── scripts/                 # Build and setup scripts
```

## Development Workflow

### 1. Feature Development
```bash
# Create feature branch
git checkout -b feature/resume-builder

# Make changes
# ... code changes ...

# Run tests
npm run test

# Run linting
npm run lint

# Type check
npm run type-check

# Commit changes
git add .
git commit -m "feat: add resume builder component"

# Push and create PR
git push origin feature/resume-builder
```

### 2. Database Changes
```bash
# Modify schema in packages/database/prisma/schema.prisma

# Generate new client
npm run db:generate

# Create migration
npm run db:migrate

# Or push changes directly (development)
npm run db:push
```

### 3. Adding New Components
```bash
# Create component in appropriate package
# For shared components:
packages/ui/src/components/new-component.tsx

# For app-specific components:
apps/web/src/components/feature/new-component.tsx
```

## Code Standards

### TypeScript
- Strict mode enabled
- No `any` types allowed
- Proper type definitions for all functions
- Use Zod for runtime validation

### React
- Functional components with hooks
- Custom hooks for reusable logic
- Proper error boundaries
- Accessibility considerations

### Styling
- TailwindCSS for styling
- CSS variables for theming
- Responsive design first
- Dark mode support

### Testing
- Unit tests for utilities and hooks
- Integration tests for API routes
- E2E tests for critical user flows
- >80% code coverage target

## Available Scripts

### Root Level
```bash
npm run dev          # Start all development servers
npm run build        # Build all packages
npm run test         # Run all tests
npm run lint         # Lint all packages
npm run type-check   # Type check all packages
npm run clean        # Clean all build artifacts
```

### Database
```bash
npm run db:generate  # Generate Prisma client
npm run db:push      # Push schema to database
npm run db:migrate   # Create and run migrations
npm run db:studio    # Open Prisma Studio
npm run db:seed      # Seed database with sample data
```

### Web App Specific
```bash
cd apps/web
npm run dev          # Start Next.js dev server
npm run build        # Build for production
npm run start        # Start production server
npm run test:e2e     # Run E2E tests
```

## Environment Variables

### Required
- `DATABASE_URL` - PostgreSQL connection string
- `NEXTAUTH_SECRET` - Secret for NextAuth.js
- `OPENAI_API_KEY` - OpenAI API key for AI features

### Optional
- `GOOGLE_CLIENT_ID` - Google OAuth
- `GITHUB_CLIENT_ID` - GitHub OAuth
- `SENTRY_DSN` - Error tracking
- `REDIS_URL` - Caching (defaults to memory)

## Debugging

### Database Issues
```bash
# Check database connection
npm run db:studio

# Reset database
npm run db:migrate reset

# View logs
docker logs careercraft-postgres
```

### API Issues
```bash
# Check API routes
curl http://localhost:3000/api/health

# View server logs
npm run dev -- --debug
```

### Frontend Issues
```bash
# Check bundle analysis
npm run build -- --analyze

# Debug React components
# Use React Developer Tools browser extension
```

## Performance Optimization

### Frontend
- Code splitting with dynamic imports
- Image optimization with Next.js Image
- Bundle analysis and optimization
- Lazy loading for non-critical components

### Backend
- Database query optimization
- Redis caching for frequent queries
- API response compression
- Rate limiting implementation

### Monitoring
- Sentry for error tracking
- Core Web Vitals monitoring
- API performance metrics
- Database query analysis

## Deployment

### Development
```bash
# Deploy to Vercel (frontend)
vercel deploy

# Deploy services to Railway
railway deploy
```

### Production
```bash
# Build and test
npm run build
npm run test

# Deploy with CI/CD
git push origin main
```

## Troubleshooting

### Common Issues

1. **Node modules issues**
   ```bash
   rm -rf node_modules package-lock.json
   npm install
   ```

2. **Database connection errors**
   - Check PostgreSQL is running
   - Verify DATABASE_URL format
   - Check firewall settings

3. **TypeScript errors**
   ```bash
   npm run type-check
   # Fix reported errors
   ```

4. **Build failures**
   ```bash
   npm run clean
   npm run build
   ```

### Getting Help
- Check existing GitHub issues
- Review documentation in `/docs`
- Ask in team Slack channel
- Create detailed bug report with reproduction steps

## Contributing

1. Fork the repository
2. Create feature branch
3. Make changes following code standards
4. Add tests for new functionality
5. Update documentation
6. Submit pull request

### Pull Request Checklist
- [ ] Tests pass
- [ ] Linting passes
- [ ] Type checking passes
- [ ] Documentation updated
- [ ] Breaking changes documented
- [ ] Performance impact considered
