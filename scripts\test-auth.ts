#!/usr/bin/env tsx

/**
 * Authentication Testing Script
 * 
 * This script performs comprehensive testing of the authentication system:
 * 1. User registration and login
 * 2. Password hashing and verification
 * 3. Session management
 * 4. OAuth provider configuration
 * 5. Rate limiting
 * 6. Security measures
 */

import { config } from 'dotenv';
import { join } from 'path';
import { 
  prisma, 
  connectDB, 
  disconnectDB, 
  cleanupDatabase 
} from '../packages/database/src/index';
import { 
  hashPassword, 
  verifyPassword, 
  createUser, 
  loginSchema, 
  signupSchema 
} from '../apps/web/src/lib/auth';

// Load environment variables
config({ path: join(__dirname, '../.env.local') });

interface TestResult {
  name: string;
  status: 'PASS' | 'FAIL' | 'SKIP';
  duration: number;
  error?: string;
  details?: any;
}

class AuthTester {
  private results: TestResult[] = [];

  private async runTest(name: string, testFn: () => Promise<any>): Promise<void> {
    const startTime = Date.now();
    console.log(`🔐 Running: ${name}`);

    try {
      const result = await testFn();
      const duration = Date.now() - startTime;
      
      this.results.push({
        name,
        status: 'PASS',
        duration,
        details: result,
      });
      
      console.log(`✅ PASS: ${name} (${duration}ms)`);
    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      
      this.results.push({
        name,
        status: 'FAIL',
        duration,
        error: errorMessage,
      });
      
      console.log(`❌ FAIL: ${name} (${duration}ms) - ${errorMessage}`);
    }
  }

  async testPasswordHashing(): Promise<void> {
    await this.runTest('Password Hashing and Verification', async () => {
      const password = 'TestPassword123!';
      
      // Test hashing
      const hashedPassword = await hashPassword(password);
      if (!hashedPassword || hashedPassword === password) {
        throw new Error('Password hashing failed');
      }

      // Test verification with correct password
      const isValidCorrect = await verifyPassword(password, hashedPassword);
      if (!isValidCorrect) {
        throw new Error('Password verification failed for correct password');
      }

      // Test verification with incorrect password
      const isValidIncorrect = await verifyPassword('WrongPassword123!', hashedPassword);
      if (isValidIncorrect) {
        throw new Error('Password verification should fail for incorrect password');
      }

      return {
        hashedLength: hashedPassword.length,
        correctPasswordVerified: isValidCorrect,
        incorrectPasswordRejected: !isValidIncorrect,
      };
    });
  }

  async testValidationSchemas(): Promise<void> {
    await this.runTest('Validation Schemas', async () => {
      // Test login schema
      const validLogin = {
        email: '<EMAIL>',
        password: 'Password123',
      };

      const loginResult = loginSchema.safeParse(validLogin);
      if (!loginResult.success) {
        throw new Error('Valid login data should pass validation');
      }

      const invalidLogin = {
        email: 'invalid-email',
        password: '123',
      };

      const invalidLoginResult = loginSchema.safeParse(invalidLogin);
      if (invalidLoginResult.success) {
        throw new Error('Invalid login data should fail validation');
      }

      // Test signup schema
      const validSignup = {
        email: '<EMAIL>',
        password: 'Password123',
        name: 'Test User',
      };

      const signupResult = signupSchema.safeParse(validSignup);
      if (!signupResult.success) {
        throw new Error('Valid signup data should pass validation');
      }

      const invalidSignup = {
        email: 'invalid-email',
        password: '123',
        name: 'T',
      };

      const invalidSignupResult = signupSchema.safeParse(invalidSignup);
      if (invalidSignupResult.success) {
        throw new Error('Invalid signup data should fail validation');
      }

      return {
        loginValidation: 'passed',
        signupValidation: 'passed',
        invalidDataRejected: 'passed',
      };
    });
  }

  async testUserCreation(): Promise<void> {
    await this.runTest('User Creation and Database Integration', async () => {
      await cleanupDatabase();

      const userData = {
        email: '<EMAIL>',
        password: 'TestPassword123!',
        name: 'Test User',
      };

      // Create user
      const user = await createUser(userData);

      if (!user.id || !user.email || !user.name) {
        throw new Error('User creation returned incomplete data');
      }

      // Verify user in database
      const dbUser = await prisma.user.findUnique({
        where: { id: user.id },
        include: {
          accounts: true,
          profiles: true,
        },
      });

      if (!dbUser) {
        throw new Error('User not found in database');
      }

      if (dbUser.accounts.length !== 1) {
        throw new Error('User should have exactly one account');
      }

      if (dbUser.profiles.length !== 1) {
        throw new Error('User should have exactly one profile');
      }

      const account = dbUser.accounts[0];
      if (account.provider !== 'credentials' || account.type !== 'credentials') {
        throw new Error('Account should be credentials type');
      }

      if (!account.refresh_token) {
        throw new Error('Account should have hashed password in refresh_token');
      }

      const profile = dbUser.profiles[0];
      if (profile.firstName !== 'Test' || profile.lastName !== 'User') {
        throw new Error('Profile name parsing failed');
      }

      return {
        userCreated: true,
        accountCreated: true,
        profileCreated: true,
        nameParsingCorrect: true,
      };
    });
  }

  async testDuplicateEmailPrevention(): Promise<void> {
    await this.runTest('Duplicate Email Prevention', async () => {
      await cleanupDatabase();

      const userData = {
        email: '<EMAIL>',
        password: 'TestPassword123!',
        name: 'First User',
      };

      // Create first user
      await createUser(userData);

      // Try to create second user with same email
      const duplicateUserData = {
        email: '<EMAIL>',
        password: 'DifferentPassword123!',
        name: 'Second User',
      };

      let errorThrown = false;
      try {
        await createUser(duplicateUserData);
      } catch (error) {
        errorThrown = true;
        if (!error || !error.toString().includes('already exists')) {
          throw new Error('Wrong error message for duplicate email');
        }
      }

      if (!errorThrown) {
        throw new Error('Duplicate email should throw an error');
      }

      // Verify only one user exists
      const users = await prisma.user.findMany({
        where: { email: '<EMAIL>' },
      });

      if (users.length !== 1) {
        throw new Error('Should have exactly one user with the email');
      }

      return {
        duplicateEmailRejected: true,
        onlyOneUserExists: true,
      };
    });
  }

  async testTransactionIntegrity(): Promise<void> {
    await this.runTest('Transaction Integrity', async () => {
      await cleanupDatabase();

      // Test successful transaction
      const userData = {
        email: '<EMAIL>',
        password: 'TestPassword123!',
        name: 'Transaction User',
      };

      const user = await createUser(userData);

      // Verify all related records were created
      const dbUser = await prisma.user.findUnique({
        where: { id: user.id },
        include: {
          accounts: true,
          profiles: true,
        },
      });

      if (!dbUser || dbUser.accounts.length !== 1 || dbUser.profiles.length !== 1) {
        throw new Error('Transaction did not create all required records');
      }

      return {
        transactionCompleted: true,
        allRecordsCreated: true,
      };
    });
  }

  async testPasswordStrengthRequirements(): Promise<void> {
    await this.runTest('Password Strength Requirements', async () => {
      const weakPasswords = [
        '123456',           // Too short
        'password',         // No uppercase or numbers
        'PASSWORD',         // No lowercase or numbers
        'Password',         // No numbers
        '********',         // No letters
        'Pass123',          // Too short
      ];

      const strongPasswords = [
        'Password123',
        'MyStr0ngP@ssw0rd',
        'C0mpl3xP@ssw0rd!',
        'Secure123Password',
      ];

      // Test weak passwords are rejected
      for (const password of weakPasswords) {
        const userData = {
          email: '<EMAIL>',
          password,
          name: 'Test User',
        };

        const result = signupSchema.safeParse(userData);
        if (result.success) {
          throw new Error(`Weak password should be rejected: ${password}`);
        }
      }

      // Test strong passwords are accepted
      for (const password of strongPasswords) {
        const userData = {
          email: '<EMAIL>',
          password,
          name: 'Test User',
        };

        const result = signupSchema.safeParse(userData);
        if (!result.success) {
          throw new Error(`Strong password should be accepted: ${password}`);
        }
      }

      return {
        weakPasswordsRejected: weakPasswords.length,
        strongPasswordsAccepted: strongPasswords.length,
      };
    });
  }

  async testEmailValidation(): Promise<void> {
    await this.runTest('Email Validation', async () => {
      const invalidEmails = [
        'plainaddress',
        '@missingdomain.com',
        'missing@.com',
        'missing@domain',
        'spaces @domain.com',
        'special!@domain.com',
        '',
        'a@b',
      ];

      const validEmails = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
      ];

      // Test invalid emails are rejected
      for (const email of invalidEmails) {
        const userData = {
          email,
          password: 'Password123',
          name: 'Test User',
        };

        const result = signupSchema.safeParse(userData);
        if (result.success) {
          throw new Error(`Invalid email should be rejected: ${email}`);
        }
      }

      // Test valid emails are accepted
      for (const email of validEmails) {
        const userData = {
          email,
          password: 'Password123',
          name: 'Test User',
        };

        const result = signupSchema.safeParse(userData);
        if (!result.success) {
          throw new Error(`Valid email should be accepted: ${email}`);
        }
      }

      return {
        invalidEmailsRejected: invalidEmails.length,
        validEmailsAccepted: validEmails.length,
      };
    });
  }

  async testNameParsing(): Promise<void> {
    await this.runTest('Name Parsing for User Profiles', async () => {
      await cleanupDatabase();

      const testCases = [
        {
          input: 'John Doe',
          expectedFirst: 'John',
          expectedLast: 'Doe',
        },
        {
          input: 'Mary Jane Smith',
          expectedFirst: 'Mary',
          expectedLast: 'Jane Smith',
        },
        {
          input: 'Madonna',
          expectedFirst: 'Madonna',
          expectedLast: '',
        },
        {
          input: 'Jean-Claude Van Damme',
          expectedFirst: 'Jean-Claude',
          expectedLast: 'Van Damme',
        },
      ];

      for (let i = 0; i < testCases.length; i++) {
        const testCase = testCases[i];
        const userData = {
          email: `nametest${i}@example.com`,
          password: 'Password123',
          name: testCase.input,
        };

        const user = await createUser(userData);
        const profile = await prisma.userProfile.findUnique({
          where: { userId: user.id },
        });

        if (!profile) {
          throw new Error(`Profile not created for: ${testCase.input}`);
        }

        if (profile.firstName !== testCase.expectedFirst) {
          throw new Error(
            `First name parsing failed for "${testCase.input}". Expected: "${testCase.expectedFirst}", Got: "${profile.firstName}"`
          );
        }

        if (profile.lastName !== testCase.expectedLast) {
          throw new Error(
            `Last name parsing failed for "${testCase.input}". Expected: "${testCase.expectedLast}", Got: "${profile.lastName}"`
          );
        }
      }

      return {
        testCasesPassed: testCases.length,
        nameParsingWorking: true,
      };
    });
  }

  async testEnvironmentConfiguration(): Promise<void> {
    await this.runTest('Environment Configuration', async () => {
      const requiredEnvVars = [
        'DATABASE_URL',
        'NEXTAUTH_SECRET',
        'NEXTAUTH_URL',
      ];

      const optionalEnvVars = [
        'GOOGLE_CLIENT_ID',
        'GOOGLE_CLIENT_SECRET',
        'GITHUB_CLIENT_ID',
        'GITHUB_CLIENT_SECRET',
      ];

      const missingRequired = requiredEnvVars.filter(
        varName => !process.env[varName]
      );

      if (missingRequired.length > 0) {
        throw new Error(`Missing required environment variables: ${missingRequired.join(', ')}`);
      }

      const missingOptional = optionalEnvVars.filter(
        varName => !process.env[varName]
      );

      return {
        requiredEnvVarsPresent: requiredEnvVars.length,
        missingOptionalEnvVars: missingOptional,
        configurationValid: true,
      };
    });
  }

  async runAllTests(): Promise<void> {
    console.log('🔐 Starting Authentication Tests...\n');

    await this.testEnvironmentConfiguration();
    await this.testPasswordHashing();
    await this.testValidationSchemas();
    await this.testUserCreation();
    await this.testDuplicateEmailPrevention();
    await this.testTransactionIntegrity();
    await this.testPasswordStrengthRequirements();
    await this.testEmailValidation();
    await this.testNameParsing();

    await this.cleanup();
    await this.printSummary();
  }

  private async cleanup(): Promise<void> {
    try {
      await cleanupDatabase();
      await disconnectDB();
      console.log('\n🧹 Cleanup completed');
    } catch (error) {
      console.log('\n⚠️  Cleanup failed:', error);
    }
  }

  private async printSummary(): Promise<void> {
    console.log('\n📊 Authentication Test Summary');
    console.log('===============================');

    const passed = this.results.filter(r => r.status === 'PASS').length;
    const failed = this.results.filter(r => r.status === 'FAIL').length;
    const total = this.results.length;

    console.log(`Total Tests: ${total}`);
    console.log(`Passed: ${passed}`);
    console.log(`Failed: ${failed}`);
    console.log(`Success Rate: ${((passed / total) * 100).toFixed(1)}%`);

    if (failed > 0) {
      console.log('\n❌ Failed Tests:');
      this.results
        .filter(r => r.status === 'FAIL')
        .forEach(r => {
          console.log(`  - ${r.name}: ${r.error}`);
        });
    }

    const totalTime = this.results.reduce((sum, r) => sum + r.duration, 0);
    console.log(`\nTotal Execution Time: ${totalTime}ms`);

    if (failed === 0) {
      console.log('\n🎉 All authentication tests passed! System is ready for production.');
    } else {
      console.log('\n⚠️  Some tests failed. Please check your authentication configuration.');
      process.exit(1);
    }
  }
}

// Run the tests
async function main() {
  const tester = new AuthTester();
  await tester.runAllTests();
}

if (require.main === module) {
  main().catch(console.error);
}
