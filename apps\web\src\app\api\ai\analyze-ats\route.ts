import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { analyzeATSSchema } from '@careercraft/shared/schemas/ai';
import { ATSOptimizer } from '@/lib/ai/ats-optimizer';
import { prisma } from '@careercraft/database';

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    
    // Validate the request body
    const validationResult = analyzeATSSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          error: 'Validation failed',
          details: validationResult.error.errors,
        },
        { status: 400 }
      );
    }

    const { resumeId, jobDescription, targetKeywords } = validationResult.data;

    // Verify resume ownership
    const resume = await prisma.resume.findFirst({
      where: {
        id: resumeId,
        userId: session.user.id,
      },
    });

    if (!resume) {
      return NextResponse.json(
        { error: 'Resume not found' },
        { status: 404 }
      );
    }

    // Check user's AI usage limits
    const today = new Date().toISOString().split('T')[0];
    const usageToday = await prisma.aIUsageAnalytics.findFirst({
      where: {
        userId: session.user.id,
        date: today,
      },
    });

    // Simple rate limiting - 20 ATS analyses per day for free users
    if (usageToday && usageToday.requestCount >= 20) {
      return NextResponse.json(
        { error: 'Daily ATS analysis limit exceeded. Please upgrade your plan.' },
        { status: 429 }
      );
    }

    // Initialize ATS optimizer
    const optimizer = new ATSOptimizer();

    // Analyze resume
    const analysis = await optimizer.analyzeResume(resume, jobDescription);

    // Track usage
    await prisma.aIUsageAnalytics.upsert({
      where: {
        userId_date: {
          userId: session.user.id,
          date: today,
        },
      },
      update: {
        requestCount: {
          increment: 1,
        },
      },
      create: {
        userId: session.user.id,
        period: 'day',
        date: today,
        requestCount: 1,
        tokensUsed: 0,
        contentTypes: {},
        averageConfidence: 0.85,
        successRate: 1.0,
        cost: 0.005, // Mock cost for ATS analysis
      },
    });

    // Store the analysis results
    await prisma.aTSAnalysis.create({
      data: {
        userId: session.user.id,
        resumeId,
        jobDescription,
        targetKeywords,
        analysis,
        score: analysis.score,
      },
    });

    return NextResponse.json({
      analysis,
      suggestions: [], // Could include content suggestions based on analysis
      recommendations: analysis.recommendations,
    });
  } catch (error) {
    console.error('ATS analysis error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
