/**
 * LinkedIn Client Unit Tests
 *
 * Tests for LinkedIn API integration and data transformation
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { LinkedInClient } from '@/lib/linkedin/client'

// Mock environment variables
vi.mock('process', () => ({
  env: {
    LINKEDIN_CLIENT_ID: 'test-client-id',
    LINKEDIN_CLIENT_SECRET: 'test-client-secret',
    LINKEDIN_REDIRECT_URI: 'http://localhost:3000/api/linkedin/callback'
  }
}))

// Mock fetch globally
global.fetch = vi.fn()

describe('LinkedInClient', () => {
  let client: LinkedInClient
  const mockFetch = global.fetch as any

  beforeEach(() => {
    client = new LinkedInClient()
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('getAuthorizationUrl', () => {
    it('should generate correct authorization URL', () => {
      const url = client.getAuthorizationUrl('test-state')
      
      expect(url).toContain('https://www.linkedin.com/oauth/v2/authorization')
      expect(url).toContain('response_type=code')
      expect(url).toContain('client_id=')
      expect(url).toContain('redirect_uri=')
      expect(url).toContain('scope=')
      expect(url).toContain('state=test-state')
    })

    it('should generate URL without state parameter', () => {
      const url = client.getAuthorizationUrl()
      
      expect(url).toContain('https://www.linkedin.com/oauth/v2/authorization')
      expect(url).not.toContain('state=')
    })
  })

  describe('getAccessToken', () => {
    it('should exchange authorization code for access token', async () => {
      const mockResponse = {
        access_token: 'test-access-token',
        token_type: 'Bearer',
        expires_in: 5184000
      }

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse
      })

      const token = await client.getAccessToken('test-code')

      expect(token).toBe('test-access-token')
      expect(mockFetch).toHaveBeenCalledWith(
        'https://www.linkedin.com/oauth/v2/accessToken',
        expect.objectContaining({
          method: 'POST',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
          }
        })
      )
    })

    it('should handle token exchange errors', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        text: async () => 'Invalid authorization code'
      })

      await expect(client.getAccessToken('invalid-code')).rejects.toThrow(
        'Failed to get access token: Invalid authorization code'
      )
    })
  })

  describe('getProfile', () => {
    it('should fetch LinkedIn profile successfully', async () => {
      const mockProfile = {
        id: 'test-id',
        firstName: {
          localized: { 'en_US': 'John' },
          preferredLocale: { country: 'US', language: 'en' }
        },
        lastName: {
          localized: { 'en_US': 'Doe' },
          preferredLocale: { country: 'US', language: 'en' }
        },
        headline: 'Software Engineer',
        summary: 'Experienced developer'
      }

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockProfile
      })

      const profile = await client.getProfile('test-token')

      expect(profile).toEqual(mockProfile)
      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining('/v2/people/~:'),
        expect.objectContaining({
          headers: {
            'Authorization': 'Bearer test-token',
            'X-Restli-Protocol-Version': '2.0.0'
          }
        })
      )
    })

    it('should handle profile fetch errors', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        text: async () => 'Unauthorized'
      })

      await expect(client.getProfile('invalid-token')).rejects.toThrow(
        'Failed to fetch profile: Unauthorized'
      )
    })
  })

  describe('getPositions', () => {
    it('should fetch LinkedIn positions successfully', async () => {
      const mockPositions = {
        values: [
          {
            id: 1,
            title: 'Software Engineer',
            summary: 'Developed web applications',
            startDate: { year: 2020, month: 1 },
            endDate: { year: 2023, month: 12 },
            company: {
              id: 123,
              name: 'TechCorp',
              industry: 'Technology'
            }
          }
        ]
      }

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockPositions
      })

      const positions = await client.getPositions('test-token')

      expect(positions).toEqual(mockPositions)
      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining('/v2/positions'),
        expect.objectContaining({
          headers: {
            'Authorization': 'Bearer test-token',
            'X-Restli-Protocol-Version': '2.0.0'
          }
        })
      )
    })
  })

  describe('getEducations', () => {
    it('should fetch LinkedIn educations successfully', async () => {
      const mockEducations = {
        values: [
          {
            id: 1,
            schoolName: 'University of Technology',
            fieldOfStudy: 'Computer Science',
            degree: 'Bachelor of Science',
            startDate: { year: 2016 },
            endDate: { year: 2020 }
          }
        ]
      }

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockEducations
      })

      const educations = await client.getEducations('test-token')

      expect(educations).toEqual(mockEducations)
    })
  })

  describe('getSkills', () => {
    it('should fetch LinkedIn skills successfully', async () => {
      const mockSkills = {
        values: [
          {
            id: 1,
            skill: { name: 'JavaScript' }
          },
          {
            id: 2,
            skill: { name: 'React' }
          }
        ]
      }

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockSkills
      })

      const skills = await client.getSkills('test-token')

      expect(skills).toEqual(mockSkills)
    })
  })

  describe('transformToResumeData', () => {
    it('should transform LinkedIn data to resume format', () => {
      const mockProfile = {
        id: 'test-id',
        firstName: {
          localized: { 'en_US': 'John' },
          preferredLocale: { country: 'US', language: 'en' }
        },
        lastName: {
          localized: { 'en_US': 'Doe' },
          preferredLocale: { country: 'US', language: 'en' }
        },
        headline: 'Software Engineer',
        summary: 'Experienced developer',
        location: { name: 'San Francisco, CA' }
      }

      const mockPositions = {
        values: [
          {
            id: 1,
            title: 'Senior Software Engineer',
            summary: 'Led development team',
            startDate: { year: 2020, month: 1 },
            endDate: { year: 2023, month: 12 },
            isCurrent: false,
            company: {
              id: 123,
              name: 'TechCorp',
              industry: 'Technology'
            }
          }
        ]
      }

      const mockEducations = {
        values: [
          {
            id: 1,
            schoolName: 'University of Technology',
            fieldOfStudy: 'Computer Science',
            degree: 'Bachelor of Science',
            startDate: { year: 2016 },
            endDate: { year: 2020 }
          }
        ]
      }

      const mockSkills = {
        values: [
          { id: 1, skill: { name: 'JavaScript' } },
          { id: 2, skill: { name: 'React' } }
        ]
      }

      const resumeData = client.transformToResumeData(
        mockProfile,
        mockPositions,
        mockEducations,
        mockSkills
      )

      // Verify personal info transformation
      expect(resumeData.personalInfo.firstName).toBe('John')
      expect(resumeData.personalInfo.lastName).toBe('Doe')
      expect(resumeData.personalInfo.headline).toBe('Software Engineer')
      expect(resumeData.personalInfo.summary).toBe('Experienced developer')
      expect(resumeData.personalInfo.location).toBe('San Francisco, CA')

      // Verify experience transformation
      expect(resumeData.experience).toHaveLength(1)
      expect(resumeData.experience[0].company).toBe('TechCorp')
      expect(resumeData.experience[0].position).toBe('Senior Software Engineer')
      expect(resumeData.experience[0].startDate).toBe('2020-1-01')
      expect(resumeData.experience[0].endDate).toBe('2023-12-01')
      expect(resumeData.experience[0].current).toBe(false)

      // Verify education transformation
      expect(resumeData.education).toHaveLength(1)
      expect(resumeData.education[0].institution).toBe('University of Technology')
      expect(resumeData.education[0].degree).toBe('Bachelor of Science')
      expect(resumeData.education[0].fieldOfStudy).toBe('Computer Science')

      // Verify skills transformation
      expect(resumeData.skills).toHaveLength(2)
      expect(resumeData.skills[0].name).toBe('JavaScript')
      expect(resumeData.skills[1].name).toBe('React')
    })

    it('should handle missing optional data', () => {
      const mockProfile = {
        id: 'test-id',
        firstName: {
          localized: { 'en_US': 'John' },
          preferredLocale: { country: 'US', language: 'en' }
        },
        lastName: {
          localized: { 'en_US': 'Doe' },
          preferredLocale: { country: 'US', language: 'en' }
        }
      }

      const resumeData = client.transformToResumeData(mockProfile)

      expect(resumeData.personalInfo.firstName).toBe('John')
      expect(resumeData.personalInfo.lastName).toBe('Doe')
      expect(resumeData.experience).toEqual([])
      expect(resumeData.education).toEqual([])
      expect(resumeData.skills).toEqual([])
    })

    it('should handle current positions correctly', () => {
      const mockProfile = {
        id: 'test-id',
        firstName: {
          localized: { 'en_US': 'John' },
          preferredLocale: { country: 'US', language: 'en' }
        },
        lastName: {
          localized: { 'en_US': 'Doe' },
          preferredLocale: { country: 'US', language: 'en' }
        }
      }

      const mockPositions = {
        values: [
          {
            id: 1,
            title: 'Current Position',
            startDate: { year: 2023, month: 1 },
            isCurrent: true,
            company: {
              id: 123,
              name: 'CurrentCorp'
            }
          }
        ]
      }

      const resumeData = client.transformToResumeData(mockProfile, mockPositions)

      expect(resumeData.experience[0].current).toBe(true)
      expect(resumeData.experience[0].endDate).toBeUndefined()
    })
  })
})
