/**
 * Market Intelligence Dashboard
 * Advanced market analysis and trends visualization
 * Part of Milestone 1.3: Market Analysis Engine
 */

'use client'

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Progress } from '@/components/ui/progress'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  LineChart, Line, AreaChart, Area, BarChart, Bar, PieChart, Pie, Cell,
  XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer 
} from 'recharts'
import { 
  TrendingUp, TrendingDown, DollarSign, Users, MapPin, 
  Briefcase, Target, AlertTriangle, Lightbulb, Clock,
  BarChart3, <PERSON><PERSON>hart as PieChartIcon, Activity
} from 'lucide-react'
import { MarketAnalysis } from '@/lib/market-analysis/market-analysis-engine'

interface MarketDashboardProps {
  userId?: string
}

export default function MarketDashboard({ userId }: MarketDashboardProps) {
  const [analysis, setAnalysis] = useState<MarketAnalysis | null>(null)
  const [trends, setTrends] = useState<any>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  
  // Filter states
  const [targetRole, setTargetRole] = useState('')
  const [targetLocation, setTargetLocation] = useState('')
  const [targetSkills, setTargetSkills] = useState('')
  const [analysisType, setAnalysisType] = useState<'REAL_TIME' | 'HISTORICAL' | 'PREDICTIVE'>('REAL_TIME')
  const [timeframe, setTimeframe] = useState('30d')

  useEffect(() => {
    loadMarketData()
  }, [])

  const loadMarketData = async () => {
    setLoading(true)
    setError(null)
    
    try {
      // Load market analysis
      const analysisParams = new URLSearchParams({
        type: analysisType,
        timeframe,
        ...(targetRole && { role: targetRole }),
        ...(targetLocation && { location: targetLocation }),
        ...(targetSkills && { skills: targetSkills })
      })

      const [analysisResponse, trendsResponse] = await Promise.all([
        fetch(`/api/market-intelligence?${analysisParams}`),
        fetch(`/api/market-intelligence/trends?type=all&timeframe=${timeframe}`)
      ])

      if (!analysisResponse.ok || !trendsResponse.ok) {
        throw new Error('Failed to load market data')
      }

      const analysisData = await analysisResponse.json()
      const trendsData = await trendsResponse.json()

      setAnalysis(analysisData.data)
      setTrends(trendsData.data)

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load market data')
    } finally {
      setLoading(false)
    }
  }

  const handleAnalysisUpdate = () => {
    loadMarketData()
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Analyzing market data...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <Alert className="mb-6">
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          {error}
          <Button 
            variant="outline" 
            size="sm" 
            className="ml-4"
            onClick={loadMarketData}
          >
            Retry
          </Button>
        </AlertDescription>
      </Alert>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header and Controls */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Market Intelligence</h1>
          <p className="text-gray-600">Real-time job market analysis and trends</p>
        </div>
        
        <div className="flex flex-wrap gap-2">
          <Select value={analysisType} onValueChange={(value: any) => setAnalysisType(value)}>
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="REAL_TIME">Real-time</SelectItem>
              <SelectItem value="HISTORICAL">Historical</SelectItem>
              <SelectItem value="PREDICTIVE">Predictive</SelectItem>
            </SelectContent>
          </Select>
          
          <Select value={timeframe} onValueChange={setTimeframe}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">7 days</SelectItem>
              <SelectItem value="30d">30 days</SelectItem>
              <SelectItem value="90d">90 days</SelectItem>
              <SelectItem value="1y">1 year</SelectItem>
            </SelectContent>
          </Select>
          
          <Button onClick={handleAnalysisUpdate} className="bg-blue-600 hover:bg-blue-700">
            <Activity className="h-4 w-4 mr-2" />
            Update Analysis
          </Button>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Analysis Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium mb-2">Target Role</label>
              <Input
                placeholder="e.g., Software Engineer"
                value={targetRole}
                onChange={(e) => setTargetRole(e.target.value)}
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">Location</label>
              <Input
                placeholder="e.g., San Francisco, CA"
                value={targetLocation}
                onChange={(e) => setTargetLocation(e.target.value)}
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">Skills (comma-separated)</label>
              <Input
                placeholder="e.g., React, Node.js, Python"
                value={targetSkills}
                onChange={(e) => setTargetSkills(e.target.value)}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {analysis && (
        <>
          {/* Key Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Total Jobs</p>
                    <p className="text-2xl font-bold">{analysis.metrics.totalJobs.toLocaleString()}</p>
                  </div>
                  <Briefcase className="h-8 w-8 text-blue-600" />
                </div>
                <div className="mt-2">
                  <Badge variant={analysis.insights.demandTrend === 'GROWING' ? 'default' : 'secondary'}>
                    {analysis.insights.demandTrend}
                  </Badge>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Average Salary</p>
                    <p className="text-2xl font-bold">${analysis.metrics.averageSalary.toLocaleString()}</p>
                  </div>
                  <DollarSign className="h-8 w-8 text-green-600" />
                </div>
                <div className="mt-2">
                  <Badge variant={analysis.insights.salaryTrend === 'INCREASING' ? 'default' : 'secondary'}>
                    {analysis.insights.salaryTrend}
                  </Badge>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Market Health</p>
                    <p className="text-2xl font-bold">{analysis.insights.marketHealth}</p>
                  </div>
                  <Target className="h-8 w-8 text-purple-600" />
                </div>
                <div className="mt-2">
                  <Progress 
                    value={analysis.confidence} 
                    className="h-2"
                  />
                  <p className="text-xs text-gray-500 mt-1">{analysis.confidence}% confidence</p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Competition</p>
                    <p className="text-2xl font-bold">{analysis.insights.competitionLevel}</p>
                  </div>
                  <Users className="h-8 w-8 text-orange-600" />
                </div>
                <div className="mt-2">
                  <Badge variant="outline">
                    {analysis.metrics.topCompanies.length} companies
                  </Badge>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Main Content Tabs */}
          <Tabs defaultValue="overview" className="space-y-4">
            <TabsList className="grid w-full grid-cols-5">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="trends">Trends</TabsTrigger>
              <TabsTrigger value="skills">Skills</TabsTrigger>
              <TabsTrigger value="predictions">Predictions</TabsTrigger>
              <TabsTrigger value="recommendations">Recommendations</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-4">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Salary Distribution */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <BarChart3 className="h-5 w-5" />
                      Salary by Experience Level
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ResponsiveContainer width="100%" height={300}>
                      <BarChart data={analysis.metrics.experienceLevels}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="level" />
                        <YAxis />
                        <Tooltip formatter={(value) => [`$${value.toLocaleString()}`, 'Average Salary']} />
                        <Bar dataKey="avgSalary" fill="#3B82F6" />
                      </BarChart>
                    </ResponsiveContainer>
                  </CardContent>
                </Card>

                {/* Industry Distribution */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <PieChartIcon className="h-5 w-5" />
                      Industry Breakdown
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ResponsiveContainer width="100%" height={300}>
                      <PieChart>
                        <Pie
                          data={analysis.metrics.industryBreakdown.slice(0, 6)}
                          dataKey="jobCount"
                          nameKey="industry"
                          cx="50%"
                          cy="50%"
                          outerRadius={100}
                          fill="#8884d8"
                        >
                          {analysis.metrics.industryBreakdown.slice(0, 6).map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={`hsl(${index * 60}, 70%, 50%)`} />
                          ))}
                        </Pie>
                        <Tooltip />
                        <Legend />
                      </PieChart>
                    </ResponsiveContainer>
                  </CardContent>
                </Card>
              </div>

              {/* Top Companies and Locations */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Top Hiring Companies</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {analysis.metrics.topCompanies.slice(0, 8).map((company, index) => (
                        <div key={company.company} className="flex items-center justify-between">
                          <div className="flex items-center gap-3">
                            <span className="text-sm font-medium text-gray-500">#{index + 1}</span>
                            <span className="font-medium">{company.company}</span>
                          </div>
                          <div className="text-right">
                            <p className="font-semibold">{company.jobCount} jobs</p>
                            {company.avgSalary > 0 && (
                              <p className="text-sm text-gray-600">${company.avgSalary.toLocaleString()}</p>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Top Locations</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {analysis.metrics.locationDistribution.slice(0, 8).map((location, index) => (
                        <div key={location.location} className="flex items-center justify-between">
                          <div className="flex items-center gap-3">
                            <span className="text-sm font-medium text-gray-500">#{index + 1}</span>
                            <span className="font-medium">{location.location}</span>
                          </div>
                          <div className="text-right">
                            <p className="font-semibold">{location.jobCount} jobs</p>
                            {location.avgSalary > 0 && (
                              <p className="text-sm text-gray-600">${location.avgSalary.toLocaleString()}</p>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="trends" className="space-y-4">
              {trends && (
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Demand Timeline */}
                  <Card>
                    <CardHeader>
                      <CardTitle>Job Demand Timeline</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <ResponsiveContainer width="100%" height={300}>
                        <LineChart data={analysis.metrics.demandTrends}>
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="date" />
                          <YAxis />
                          <Tooltip />
                          <Line type="monotone" dataKey="jobCount" stroke="#3B82F6" strokeWidth={2} />
                        </LineChart>
                      </ResponsiveContainer>
                    </CardContent>
                  </Card>

                  {/* Salary Timeline */}
                  <Card>
                    <CardHeader>
                      <CardTitle>Average Salary Timeline</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <ResponsiveContainer width="100%" height={300}>
                        <AreaChart data={analysis.metrics.demandTrends}>
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="date" />
                          <YAxis />
                          <Tooltip formatter={(value) => [`$${value.toLocaleString()}`, 'Average Salary']} />
                          <Area type="monotone" dataKey="avgSalary" stroke="#10B981" fill="#10B981" fillOpacity={0.3} />
                        </AreaChart>
                      </ResponsiveContainer>
                    </CardContent>
                  </Card>
                </div>
              )}
            </TabsContent>

            <TabsContent value="skills" className="space-y-4">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Top Skills */}
                <Card>
                  <CardHeader>
                    <CardTitle>Most In-Demand Skills</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {analysis.metrics.topSkills.slice(0, 10).map((skill, index) => (
                        <div key={skill.skill} className="flex items-center justify-between">
                          <div className="flex items-center gap-3">
                            <span className="text-sm font-medium text-gray-500">#{index + 1}</span>
                            <span className="font-medium">{skill.skill}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <Badge variant="outline">{skill.demand} jobs</Badge>
                            {skill.growth > 0 ? (
                              <TrendingUp className="h-4 w-4 text-green-600" />
                            ) : (
                              <TrendingDown className="h-4 w-4 text-red-600" />
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                {/* Emerging Skills */}
                <Card>
                  <CardHeader>
                    <CardTitle>Emerging Skills</CardTitle>
                    <CardDescription>Skills with highest growth rates</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {analysis.insights.emergingSkills.slice(0, 10).map((skill, index) => (
                        <div key={skill.skill} className="flex items-center justify-between">
                          <span className="font-medium">{skill.skill}</span>
                          <div className="flex items-center gap-2">
                            <Badge variant="default">+{skill.growth}%</Badge>
                            <Progress value={skill.adoption} className="w-16 h-2" />
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="predictions" className="space-y-4">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Salary Forecast */}
                <Card>
                  <CardHeader>
                    <CardTitle>Salary Forecast</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {analysis.predictions.salaryForecast.map((forecast, index) => (
                        <div key={forecast.period} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                          <div>
                            <p className="font-medium">{forecast.period}</p>
                            <p className="text-sm text-gray-600">{forecast.confidence}% confidence</p>
                          </div>
                          <p className="text-lg font-bold">${forecast.predictedSalary.toLocaleString()}</p>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                {/* Demand Forecast */}
                <Card>
                  <CardHeader>
                    <CardTitle>Demand Forecast</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {analysis.predictions.demandForecast.map((forecast, index) => (
                        <div key={forecast.period} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                          <div>
                            <p className="font-medium">{forecast.period}</p>
                            <p className="text-sm text-gray-600">{forecast.confidence}% confidence</p>
                          </div>
                          <p className="text-lg font-bold">{forecast.predictedDemand.toLocaleString()} jobs</p>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Automation Risk */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <AlertTriangle className="h-5 w-5" />
                    Automation Risk Assessment
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-between mb-4">
                    <div>
                      <p className="text-2xl font-bold">{analysis.predictions.automationRisk.risk}%</p>
                      <p className="text-gray-600">Risk Level</p>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">{analysis.predictions.automationRisk.timeframe}</p>
                      <p className="text-sm text-gray-600">Estimated timeframe</p>
                    </div>
                  </div>
                  <Progress value={analysis.predictions.automationRisk.risk} className="mb-4" />
                  {analysis.predictions.automationRisk.affectedRoles.length > 0 && (
                    <div>
                      <p className="font-medium mb-2">Potentially affected roles:</p>
                      <div className="flex flex-wrap gap-2">
                        {analysis.predictions.automationRisk.affectedRoles.map((role, index) => (
                          <Badge key={index} variant="outline">{role}</Badge>
                        ))}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="recommendations" className="space-y-4">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Career Moves */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Target className="h-5 w-5" />
                      Recommended Career Moves
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {analysis.recommendations.careerMoves.map((move, index) => (
                        <div key={index} className="p-4 border rounded-lg">
                          <div className="flex items-center justify-between mb-2">
                            <h4 className="font-medium">{move.move}</h4>
                            <Badge variant="outline">Difficulty: {move.difficulty}/10</Badge>
                          </div>
                          <p className="text-sm text-gray-600 mb-2">{move.rationale}</p>
                          <div className="flex items-center gap-2">
                            <Clock className="h-4 w-4 text-gray-500" />
                            <span className="text-sm text-gray-600">{move.timeline}</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                {/* Skill Development */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Lightbulb className="h-5 w-5" />
                      Skill Development Priorities
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {analysis.recommendations.skillDevelopment.slice(0, 6).map((skill, index) => (
                        <div key={index} className="p-4 border rounded-lg">
                          <div className="flex items-center justify-between mb-2">
                            <h4 className="font-medium">{skill.skill}</h4>
                            <Badge variant="default">ROI: {skill.roi}%</Badge>
                          </div>
                          <p className="text-sm text-gray-600 mb-2">{skill.learningPath}</p>
                          <Progress value={skill.priority * 10} className="h-2" />
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Location and Industry Recommendations */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <MapPin className="h-5 w-5" />
                      Location Recommendations
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {analysis.recommendations.locationRecommendations.map((location, index) => (
                        <div key={index} className="p-3 bg-gray-50 rounded-lg">
                          <div className="flex items-center justify-between mb-2">
                            <h4 className="font-medium">{location.location}</h4>
                            <span className="font-bold">${location.salary.toLocaleString()}</span>
                          </div>
                          <div className="flex flex-wrap gap-1">
                            {location.advantages.map((advantage, i) => (
                              <Badge key={i} variant="outline" className="text-xs">{advantage}</Badge>
                            ))}
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Timing Recommendations</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {analysis.recommendations.timingRecommendations.map((timing, index) => (
                        <div key={index} className="p-3 bg-gray-50 rounded-lg">
                          <h4 className="font-medium mb-1">{timing.action}</h4>
                          <p className="text-sm text-gray-600 mb-2">{timing.rationale}</p>
                          <Badge variant="default">{timing.timing}</Badge>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
          </Tabs>
        </>
      )}
    </div>
  )
}
