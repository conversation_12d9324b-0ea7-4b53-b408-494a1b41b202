/**
 * AI Content Generation and ATS Optimization Types
 * 
 * This file contains all the TypeScript types and interfaces
 * for the AI-powered features of CareerCraft.
 */

export interface AIContentRequest {
  type: ContentType;
  context: ContentContext;
  options: GenerationOptions;
  userId: string;
  resumeId?: string;
}

export enum ContentType {
  PROFESSIONAL_SUMMARY = 'professional_summary',
  WORK_EXPERIENCE_DESCRIPTION = 'work_experience_description',
  ACHIEVEMENT_BULLET = 'achievement_bullet',
  SKILLS_SUGGESTION = 'skills_suggestion',
  COVER_LETTER = 'cover_letter',
  LINKEDIN_HEADLINE = 'linkedin_headline',
  LINKEDIN_SUMMARY = 'linkedin_summary',
  JOB_DESCRIPTION_OPTIMIZATION = 'job_description_optimization',
}

export interface ContentContext {
  // Personal information
  firstName?: string;
  lastName?: string;
  currentRole?: string;
  industry?: string;
  experienceLevel: ExperienceLevel;
  
  // Target job information
  targetJobTitle?: string;
  targetCompany?: string;
  targetIndustry?: string;
  jobDescription?: string;
  
  // Existing content to improve
  existingContent?: string;
  
  // Work experience context
  company?: string;
  position?: string;
  responsibilities?: string[];
  achievements?: string[];
  technologies?: string[];
  
  // Skills and qualifications
  skills?: string[];
  certifications?: string[];
  education?: string;
  
  // Additional context
  tone?: ContentTone;
  keywords?: string[];
  customInstructions?: string;
}

export enum ExperienceLevel {
  ENTRY_LEVEL = 'entry_level',
  MID_LEVEL = 'mid_level',
  SENIOR_LEVEL = 'senior_level',
  EXECUTIVE = 'executive',
  CAREER_CHANGE = 'career_change',
}

export enum ContentTone {
  PROFESSIONAL = 'professional',
  CONFIDENT = 'confident',
  APPROACHABLE = 'approachable',
  DYNAMIC = 'dynamic',
  TECHNICAL = 'technical',
  CREATIVE = 'creative',
}

export interface GenerationOptions {
  length: ContentLength;
  style: ContentStyle;
  includeKeywords: boolean;
  atsOptimized: boolean;
  industrySpecific: boolean;
  creativityLevel: number; // 0-1, where 0 is conservative and 1 is creative
  maxSuggestions: number;
  language: string;
}

export enum ContentLength {
  SHORT = 'short',      // 1-2 sentences
  MEDIUM = 'medium',    // 3-4 sentences
  LONG = 'long',        // 5+ sentences
}

export enum ContentStyle {
  BULLET_POINTS = 'bullet_points',
  PARAGRAPH = 'paragraph',
  STRUCTURED = 'structured',
  CONVERSATIONAL = 'conversational',
}

export interface AIContentResponse {
  id: string;
  requestId: string;
  suggestions: ContentSuggestion[];
  metadata: GenerationMetadata;
  createdAt: string;
}

export interface ContentSuggestion {
  id: string;
  content: string;
  confidence: number; // 0-1
  reasoning: string;
  keywords: string[];
  atsScore: number; // 0-100
  improvements: string[];
  alternatives: string[];
}

export interface GenerationMetadata {
  model: string;
  processingTime: number;
  tokensUsed: number;
  confidence: number;
  atsOptimization: ATSOptimizationData;
  keywordAnalysis: KeywordAnalysis;
}

// ATS Optimization Types
export interface ATSOptimizationData {
  score: number; // 0-100
  issues: ATSIssue[];
  recommendations: ATSRecommendation[];
  keywordMatch: number; // 0-100
  formatCompliance: number; // 0-100
  readabilityScore: number; // 0-100
}

export interface ATSIssue {
  id: string;
  type: ATSIssueType;
  severity: IssueSeverity;
  description: string;
  location: string;
  suggestion: string;
  impact: number; // 0-100
}

export enum ATSIssueType {
  MISSING_KEYWORDS = 'missing_keywords',
  POOR_FORMATTING = 'poor_formatting',
  COMPLEX_LANGUAGE = 'complex_language',
  MISSING_SECTIONS = 'missing_sections',
  INCONSISTENT_DATES = 'inconsistent_dates',
  SPECIAL_CHARACTERS = 'special_characters',
  LONG_SENTENCES = 'long_sentences',
  WEAK_ACTION_VERBS = 'weak_action_verbs',
}

export enum IssueSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}

export interface ATSRecommendation {
  id: string;
  type: RecommendationType;
  priority: number; // 1-10
  title: string;
  description: string;
  implementation: string;
  expectedImpact: number; // 0-100
  effort: EffortLevel;
}

export enum RecommendationType {
  KEYWORD_OPTIMIZATION = 'keyword_optimization',
  CONTENT_IMPROVEMENT = 'content_improvement',
  FORMAT_ENHANCEMENT = 'format_enhancement',
  SECTION_ADDITION = 'section_addition',
  LANGUAGE_SIMPLIFICATION = 'language_simplification',
}

export enum EffortLevel {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
}

// Keyword Analysis Types
export interface KeywordAnalysis {
  extractedKeywords: ExtractedKeyword[];
  missingKeywords: string[];
  keywordDensity: Record<string, number>;
  industryKeywords: string[];
  skillKeywords: string[];
  actionVerbs: string[];
  recommendations: KeywordRecommendation[];
}

export interface ExtractedKeyword {
  keyword: string;
  frequency: number;
  relevance: number; // 0-1
  category: KeywordCategory;
  importance: number; // 0-1
}

export enum KeywordCategory {
  SKILL = 'skill',
  TECHNOLOGY = 'technology',
  INDUSTRY = 'industry',
  ROLE = 'role',
  CERTIFICATION = 'certification',
  SOFT_SKILL = 'soft_skill',
  ACTION_VERB = 'action_verb',
}

export interface KeywordRecommendation {
  keyword: string;
  category: KeywordCategory;
  priority: number; // 1-10
  reasoning: string;
  suggestedPlacement: string[];
  alternatives: string[];
}

// Content Templates and Patterns
export interface ContentTemplate {
  id: string;
  name: string;
  description: string;
  type: ContentType;
  industry: string[];
  experienceLevel: ExperienceLevel[];
  template: string;
  variables: TemplateVariable[];
  examples: string[];
  tags: string[];
}

export interface TemplateVariable {
  name: string;
  type: VariableType;
  required: boolean;
  description: string;
  defaultValue?: string;
  options?: string[];
}

export enum VariableType {
  TEXT = 'text',
  NUMBER = 'number',
  SELECT = 'select',
  MULTISELECT = 'multiselect',
  BOOLEAN = 'boolean',
}

// Industry-Specific Data
export interface IndustryData {
  id: string;
  name: string;
  description: string;
  commonRoles: string[];
  keySkills: string[];
  technologies: string[];
  certifications: string[];
  keywords: string[];
  trends: string[];
  salaryRanges: SalaryRange[];
}

export interface SalaryRange {
  role: string;
  experienceLevel: ExperienceLevel;
  minSalary: number;
  maxSalary: number;
  currency: string;
  location: string;
}

// AI Model Configuration
export interface AIModelConfig {
  provider: AIProvider;
  model: string;
  apiKey: string;
  baseUrl?: string;
  maxTokens: number;
  temperature: number;
  topP: number;
  frequencyPenalty: number;
  presencePenalty: number;
}

export enum AIProvider {
  OPENAI = 'openai',
  ANTHROPIC = 'anthropic',
  GOOGLE = 'google',
  AZURE = 'azure',
  HUGGINGFACE = 'huggingface',
}

// Usage Analytics
export interface AIUsageAnalytics {
  userId: string;
  period: 'day' | 'week' | 'month';
  requestCount: number;
  tokensUsed: number;
  contentTypes: Record<ContentType, number>;
  averageConfidence: number;
  successRate: number;
  cost: number;
  date: string;
}

// Content Quality Metrics
export interface ContentQualityMetrics {
  readabilityScore: number; // Flesch-Kincaid
  sentimentScore: number; // -1 to 1
  professionalismScore: number; // 0-100
  uniquenessScore: number; // 0-100
  keywordDensity: number; // 0-100
  actionVerbCount: number;
  quantifiableAchievements: number;
  industryRelevance: number; // 0-100
}

// Feedback and Learning
export interface ContentFeedback {
  id: string;
  contentId: string;
  userId: string;
  rating: number; // 1-5
  feedback: string;
  improvements: string[];
  wasUsed: boolean;
  wasModified: boolean;
  finalContent?: string;
  createdAt: string;
}

// API Response Types
export interface AIContentListResponse {
  suggestions: AIContentResponse[];
  total: number;
  page: number;
  limit: number;
}

export interface ATSAnalysisResponse {
  analysis: ATSOptimizationData;
  suggestions: ContentSuggestion[];
  recommendations: ATSRecommendation[];
}

// Utility Types
export type ContentGenerationRequest = Omit<AIContentRequest, 'userId'>;
export type ContentOptimizationRequest = {
  content: string;
  context: ContentContext;
  targetJobDescription?: string;
};

export type AIContentFormData = {
  type: ContentType;
  context: Partial<ContentContext>;
  options: Partial<GenerationOptions>;
};

// Error Types
export interface AIError {
  code: string;
  message: string;
  details?: any;
  retryable: boolean;
}

// Rate Limiting
export interface RateLimit {
  requests: number;
  tokens: number;
  windowMs: number;
  resetTime: string;
}

// Content Cache
export interface ContentCache {
  key: string;
  content: AIContentResponse;
  expiresAt: string;
  hitCount: number;
}
