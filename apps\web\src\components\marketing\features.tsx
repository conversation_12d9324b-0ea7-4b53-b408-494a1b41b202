import { Card, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Icons } from '@/components/ui/icons';

export function Features() {
  return (
    <section className="py-12 md:py-24 lg:py-32 bg-gray-50 dark:bg-gray-900">
      <div className="container px-4 md:px-6">
        <div className="flex flex-col items-center justify-center space-y-4 text-center">
          <div className="space-y-2">
            <h2 className="text-3xl font-bold tracking-tighter sm:text-5xl">
              Why Choose CareerCraft?
            </h2>
            <p className="max-w-[900px] text-gray-500 md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed dark:text-gray-400">
              Our platform combines cutting-edge AI technology with proven resume best practices 
              to help you create documents that get results.
            </p>
          </div>
        </div>
        <div className="mx-auto grid max-w-5xl items-center gap-6 py-12 lg:grid-cols-3 lg:gap-12">
          <Card>
            <CardHeader>
              <Icons.fileText className="h-10 w-10 text-primary" />
              <CardTitle>AI-Powered Content</CardTitle>
              <CardDescription>
                Generate compelling bullet points and summaries tailored to your experience and target roles.
              </CardDescription>
            </CardHeader>
          </Card>
          <Card>
            <CardHeader>
              <Icons.checkCircle className="h-10 w-10 text-primary" />
              <CardTitle>ATS Optimization</CardTitle>
              <CardDescription>
                Ensure your resume passes applicant tracking systems with our keyword optimization tools.
              </CardDescription>
            </CardHeader>
          </Card>
          <Card>
            <CardHeader>
              <Icons.layout className="h-10 w-10 text-primary" />
              <CardTitle>Professional Templates</CardTitle>
              <CardDescription>
                Choose from dozens of professionally designed templates that work across all industries.
              </CardDescription>
            </CardHeader>
          </Card>
        </div>
      </div>
    </section>
  );
}
