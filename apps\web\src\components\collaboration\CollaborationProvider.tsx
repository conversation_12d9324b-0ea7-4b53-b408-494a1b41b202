'use client'

import { createContext, useContext, useEffect, useState, ReactNode } from 'react'
import { useSession } from 'next-auth/react'
import { useCollaborationStore } from '@/lib/collaboration/store'
import { toast } from 'sonner'

interface CollaborationContextType {
  isEnabled: boolean
  sessionId: string | null
  isConnected: boolean
  isConnecting: boolean
  connectionError: string | null
  startCollaboration: (resumeId: string) => Promise<string | null>
  joinCollaboration: (sessionToken: string) => Promise<boolean>
  stopCollaboration: () => void
}

const CollaborationContext = createContext<CollaborationContextType | null>(null)

interface CollaborationProviderProps {
  children: ReactNode
  resumeId?: string
  autoConnect?: boolean
}

export function CollaborationProvider({ 
  children, 
  resumeId, 
  autoConnect = false 
}: CollaborationProviderProps) {
  const { data: session } = useSession()
  const [isEnabled, setIsEnabled] = useState(false)
  const [sessionToken, setSessionToken] = useState<string | null>(null)
  
  const {
    sessionId,
    isConnected,
    isConnecting,
    connectionError,
    connect,
    disconnect,
    reset
  } = useCollaborationStore()

  useEffect(() => {
    // Enable collaboration if user is authenticated
    setIsEnabled(!!session?.user)
  }, [session])

  useEffect(() => {
    // Auto-connect if enabled and session token is available
    if (autoConnect && isEnabled && sessionToken && session?.accessToken) {
      connect(sessionToken, session.accessToken as string).catch(error => {
        console.error('Auto-connect failed:', error)
        toast.error('Failed to connect to collaboration session')
      })
    }
  }, [autoConnect, isEnabled, sessionToken, session?.accessToken, connect])

  useEffect(() => {
    // Show connection status notifications
    if (connectionError) {
      toast.error(`Collaboration error: ${connectionError}`)
    } else if (isConnected) {
      toast.success('Connected to collaboration session')
    }
  }, [isConnected, connectionError])

  const startCollaboration = async (targetResumeId: string): Promise<string | null> => {
    if (!isEnabled || !session?.user?.id) {
      toast.error('Authentication required for collaboration')
      return null
    }

    try {
      // Create collaboration session
      const response = await fetch('/api/collaboration/session', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          resumeId: targetResumeId,
          expiresIn: 24 * 60 * 60 * 1000 // 24 hours
        })
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to create collaboration session')
      }

      const { session: collaborationSession } = await response.json()
      const token = collaborationSession.sessionToken

      setSessionToken(token)

      // Connect to WebSocket
      await connect(collaborationSession.id, session.accessToken as string)

      toast.success('Collaboration session started')
      return token
    } catch (error) {
      console.error('Failed to start collaboration:', error)
      toast.error(error instanceof Error ? error.message : 'Failed to start collaboration')
      return null
    }
  }

  const joinCollaboration = async (token: string): Promise<boolean> => {
    if (!isEnabled || !session?.user?.id) {
      toast.error('Authentication required for collaboration')
      return false
    }

    try {
      // Get session info by token
      const response = await fetch(`/api/collaboration/session?sessionToken=${token}`)
      
      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Invalid collaboration session')
      }

      const { session: collaborationSession } = await response.json()
      
      setSessionToken(token)

      // Connect to WebSocket
      await connect(collaborationSession.id, session.accessToken as string)

      toast.success('Joined collaboration session')
      return true
    } catch (error) {
      console.error('Failed to join collaboration:', error)
      toast.error(error instanceof Error ? error.message : 'Failed to join collaboration')
      return false
    }
  }

  const stopCollaboration = () => {
    disconnect()
    setSessionToken(null)
    reset()
    toast.info('Left collaboration session')
  }

  const contextValue: CollaborationContextType = {
    isEnabled,
    sessionId,
    isConnected,
    isConnecting,
    connectionError,
    startCollaboration,
    joinCollaboration,
    stopCollaboration
  }

  return (
    <CollaborationContext.Provider value={contextValue}>
      {children}
    </CollaborationContext.Provider>
  )
}

export function useCollaboration() {
  const context = useContext(CollaborationContext)
  if (!context) {
    throw new Error('useCollaboration must be used within a CollaborationProvider')
  }
  return context
}

// Hook for checking if collaboration is active
export function useIsCollaborating() {
  const { isConnected, sessionId } = useCollaboration()
  return isConnected && !!sessionId
}

// Hook for collaboration permissions
export function useCollaborationPermissions() {
  const { userPermissions } = useCollaborationStore()
  const { data: session } = useSession()
  
  const currentUserPermission = userPermissions.find(
    p => p.userId === session?.user?.id
  )
  
  const canView = !!currentUserPermission
  const canComment = currentUserPermission?.permissionLevel && 
    ['comment', 'edit', 'admin'].includes(currentUserPermission.permissionLevel)
  const canEdit = currentUserPermission?.permissionLevel && 
    ['edit', 'admin'].includes(currentUserPermission.permissionLevel)
  const canAdmin = currentUserPermission?.permissionLevel === 'admin'
  
  return {
    permission: currentUserPermission?.permissionLevel || null,
    canView,
    canComment,
    canEdit,
    canAdmin
  }
}
