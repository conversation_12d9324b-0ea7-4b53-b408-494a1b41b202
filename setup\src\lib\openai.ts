import OpenAI from 'openai'

export const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY!,
})

export async function generateResumeContent(prompt: string) {
  try {
    const completion = await openai.chat.completions.create({
      model: 'gpt-4',
      messages: [
        {
          role: 'system',
          content: 'You are a professional resume writer. Generate high-quality, ATS-optimized resume content based on the user input.',
        },
        {
          role: 'user',
          content: prompt,
        },
      ],
      max_tokens: 1000,
      temperature: 0.7,
    })

    return completion.choices[0]?.message?.content || ''
  } catch (error) {
    console.error('OpenAI API error:', error)
    throw new Error('Failed to generate content')
  }
}

export async function analyzeCareerProfile(profileData: any) {
  try {
    const completion = await openai.chat.completions.create({
      model: 'gpt-4',
      messages: [
        {
          role: 'system',
          content: 'You are a career counselor. Analyze the career profile and provide insights, recommendations, and career path suggestions.',
        },
        {
          role: 'user',
          content: `Analyze this career profile: ${JSON.stringify(profileData)}`,
        },
      ],
      max_tokens: 1500,
      temperature: 0.7,
    })

    return completion.choices[0]?.message?.content || ''
  } catch (error) {
    console.error('OpenAI API error:', error)
    throw new Error('Failed to analyze career profile')
  }
}

export async function generateProfileEmbedding(profileText: string) {
  try {
    const response = await openai.embeddings.create({
      model: 'text-embedding-ada-002',
      input: profileText,
    })

    return response.data[0]?.embedding || []
  } catch (error) {
    console.error('OpenAI embedding error:', error)
    throw new Error('Failed to generate profile embedding')
  }
}
