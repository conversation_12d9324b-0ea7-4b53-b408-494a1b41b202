/**
 * Stripe Webhook Endpoint
 * 
 * Handles incoming Stripe webhook events for payment and subscription updates
 */

import { NextRequest, NextResponse } from 'next/server'
import { headers } from 'next/headers'
import { stripeService } from '@/lib/payments/stripe-service'
import { webhookHandler } from '@/lib/payments/webhook-handler'

/**
 * POST /api/webhooks/stripe
 * Handle Stripe webhook events
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.text()
    const headersList = headers()
    const signature = headersList.get('stripe-signature')

    if (!signature) {
      console.error('Missing Stripe signature')
      return NextResponse.json(
        { error: 'Missing signature' },
        { status: 400 }
      )
    }

    const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET
    if (!webhookSecret) {
      console.error('Missing Stripe webhook secret')
      return NextResponse.json(
        { error: 'Webhook secret not configured' },
        { status: 500 }
      )
    }

    // Verify webhook signature
    let event
    try {
      event = stripeService.verifyWebhookSignature(body, signature, webhookSecret)
    } catch (error) {
      console.error('Webhook signature verification failed:', error)
      return NextResponse.json(
        { error: 'Invalid signature' },
        { status: 400 }
      )
    }

    // Process the webhook event
    try {
      await webhookHandler.handleWebhook(event)
      
      console.log(`Successfully processed webhook event: ${event.type} (${event.id})`)
      
      return NextResponse.json({ 
        success: true, 
        eventId: event.id,
        eventType: event.type
      })
    } catch (error) {
      console.error(`Error processing webhook event ${event.id}:`, error)
      
      // Return 500 to tell Stripe to retry
      return NextResponse.json(
        { error: 'Webhook processing failed' },
        { status: 500 }
      )
    }
  } catch (error) {
    console.error('Webhook endpoint error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

/**
 * GET /api/webhooks/stripe
 * Health check for webhook endpoint
 */
export async function GET() {
  return NextResponse.json({ 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    endpoint: 'stripe-webhook'
  })
}
