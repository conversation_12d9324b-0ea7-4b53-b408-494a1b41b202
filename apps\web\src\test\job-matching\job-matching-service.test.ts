/**
 * Job Matching Service Unit Tests
 * 
 * Tests for job matching, recommendations, and application management
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { JobMatchingService } from '@/lib/job-matching/service'

// Mock Prisma
const mockPrisma = {
  jobPosting: {
    findMany: vi.fn(),
    findUnique: vi.fn(),
    create: vi.fn(),
    update: vi.fn(),
    deleteMany: vi.fn()
  },
  jobApplication: {
    create: vi.fn(),
    findMany: vi.fn(),
    findFirst: vi.fn(),
    updateMany: vi.fn(),
    deleteMany: vi.fn()
  },
  jobRecommendation: {
    create: vi.fn(),
    findMany: vi.fn(),
    findUnique: vi.fn(),
    updateMany: vi.fn(),
    deleteMany: vi.fn()
  },
  userJobPreferences: {
    findUnique: vi.fn(),
    upsert: vi.fn(),
    create: vi.fn(),
    update: vi.fn()
  },
  user: {
    findUnique: vi.fn()
  }
}

vi.mock('@/lib/db', () => ({
  prisma: mockPrisma
}))

describe('JobMatchingService', () => {
  let service: JobMatchingService

  beforeEach(() => {
    service = new JobMatchingService()
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('searchJobs', () => {
    const mockJobs = [
      {
        id: 'job-1',
        title: 'Software Developer',
        company: 'Tech Corp',
        description: 'Develop amazing software',
        location: 'San Francisco',
        salaryMin: 80000,
        salaryMax: 120000,
        employmentType: 'full-time',
        remoteType: 'hybrid',
        experienceLevel: 'mid',
        skills: JSON.stringify(['JavaScript', 'React', 'Node.js']),
        benefits: JSON.stringify(['Health insurance', '401k']),
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        id: 'job-2',
        title: 'Senior Frontend Developer',
        company: 'Startup Inc',
        description: 'Build user interfaces',
        location: 'Remote',
        salaryMin: 100000,
        salaryMax: 150000,
        employmentType: 'full-time',
        remoteType: 'remote',
        experienceLevel: 'senior',
        skills: JSON.stringify(['React', 'TypeScript', 'CSS']),
        benefits: JSON.stringify(['Flexible hours', 'Stock options']),
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ]

    beforeEach(() => {
      mockPrisma.jobPosting.findMany.mockResolvedValue(mockJobs)
    })

    it('should search jobs with basic criteria', async () => {
      const criteria = {
        query: 'developer',
        location: 'San Francisco',
        limit: 10,
        offset: 0
      }

      const result = await service.searchJobs(criteria)

      expect(mockPrisma.jobPosting.findMany).toHaveBeenCalledWith({
        where: {
          isActive: true,
          OR: [
            { title: { contains: 'developer', mode: 'insensitive' } },
            { company: { contains: 'developer', mode: 'insensitive' } },
            { description: { contains: 'developer', mode: 'insensitive' } }
          ],
          location: { contains: 'San Francisco', mode: 'insensitive' }
        },
        orderBy: [
          { postedDate: 'desc' },
          { createdAt: 'desc' }
        ],
        take: 10,
        skip: 0
      })

      expect(result).toHaveLength(2)
      expect(result[0].skills).toEqual(['JavaScript', 'React', 'Node.js'])
      expect(result[0].benefits).toEqual(['Health insurance', '401k'])
    })

    it('should filter by remote type', async () => {
      const criteria = {
        remoteType: 'remote' as const,
        limit: 10,
        offset: 0
      }

      await service.searchJobs(criteria)

      expect(mockPrisma.jobPosting.findMany).toHaveBeenCalledWith({
        where: {
          isActive: true,
          remoteType: 'remote'
        },
        orderBy: [
          { postedDate: 'desc' },
          { createdAt: 'desc' }
        ],
        take: 10,
        skip: 0
      })
    })

    it('should filter by salary range', async () => {
      const criteria = {
        salaryMin: 90000,
        salaryMax: 130000,
        limit: 10,
        offset: 0
      }

      await service.searchJobs(criteria)

      expect(mockPrisma.jobPosting.findMany).toHaveBeenCalledWith({
        where: {
          isActive: true,
          AND: [
            {
              OR: [
                { salaryMin: { gte: 90000 } },
                { salaryMax: { gte: 90000 } }
              ]
            },
            {
              OR: [
                { salaryMin: { lte: 130000 } },
                { salaryMax: { lte: 130000 } }
              ]
            }
          ]
        },
        orderBy: [
          { postedDate: 'desc' },
          { createdAt: 'desc' }
        ],
        take: 10,
        skip: 0
      })
    })

    it('should filter by companies', async () => {
      const criteria = {
        companies: ['Tech Corp', 'Startup Inc'],
        limit: 10,
        offset: 0
      }

      await service.searchJobs(criteria)

      expect(mockPrisma.jobPosting.findMany).toHaveBeenCalledWith({
        where: {
          isActive: true,
          company: { in: ['Tech Corp', 'Startup Inc'] }
        },
        orderBy: [
          { postedDate: 'desc' },
          { createdAt: 'desc' }
        ],
        take: 10,
        skip: 0
      })
    })
  })

  describe('getRecommendations', () => {
    const mockRecommendations = [
      {
        id: 'rec-1',
        userId: 'user-1',
        jobPostingId: 'job-1',
        matchScore: 85,
        reasoning: JSON.stringify({ skillMatch: true, locationMatch: true }),
        isViewed: false,
        isSaved: false,
        isDismissed: false,
        recommendedAt: new Date(),
        viewedAt: null,
        jobPosting: {
          id: 'job-1',
          title: 'Software Developer',
          company: 'Tech Corp',
          description: 'Great opportunity',
          skills: JSON.stringify(['JavaScript', 'React']),
          benefits: JSON.stringify(['Health insurance'])
        }
      }
    ]

    beforeEach(() => {
      mockPrisma.userJobPreferences.findUnique.mockResolvedValue(null)
      mockPrisma.jobApplication.findMany.mockResolvedValue([])
      mockPrisma.jobRecommendation.findMany.mockResolvedValue(mockRecommendations)
    })

    it('should get user recommendations', async () => {
      const result = await service.getRecommendations('user-1', 10)

      expect(mockPrisma.jobRecommendation.findMany).toHaveBeenCalledWith({
        where: {
          userId: 'user-1',
          isDismissed: false,
          jobPostingId: { notIn: [] }
        },
        include: {
          jobPosting: true
        },
        orderBy: [
          { matchScore: 'desc' },
          { recommendedAt: 'desc' }
        ],
        take: 10
      })

      expect(result).toHaveLength(1)
      expect(result[0].reasoning).toEqual({ skillMatch: true, locationMatch: true })
      expect(result[0].jobPosting?.skills).toEqual(['JavaScript', 'React'])
    })

    it('should exclude applied jobs from recommendations', async () => {
      const appliedJobs = [{ jobPostingId: 'job-1' }]
      mockPrisma.jobApplication.findMany.mockResolvedValue(appliedJobs)

      await service.getRecommendations('user-1', 10)

      expect(mockPrisma.jobRecommendation.findMany).toHaveBeenCalledWith({
        where: {
          userId: 'user-1',
          isDismissed: false,
          jobPostingId: { notIn: ['job-1'] }
        },
        include: {
          jobPosting: true
        },
        orderBy: [
          { matchScore: 'desc' },
          { recommendedAt: 'desc' }
        ],
        take: 10
      })
    })
  })

  describe('calculateMatchScore', () => {
    const mockUser = {
      id: 'user-1',
      resumes: [{
        id: 'resume-1',
        status: 'PUBLISHED',
        updatedAt: new Date()
      }],
      skillAssessments: [
        { skillName: 'JavaScript' },
        { skillName: 'React' },
        { skillName: 'Node.js' }
      ],
      jobPreferences: {
        preferredLocations: JSON.stringify(['San Francisco']),
        salaryMin: 80000,
        experienceLevel: 'mid'
      }
    }

    const mockJobPosting = {
      id: 'job-1',
      title: 'Software Developer',
      company: 'Tech Corp',
      description: 'Great job',
      location: 'San Francisco',
      salaryMax: 120000,
      experienceLevel: 'mid',
      skills: JSON.stringify(['JavaScript', 'React', 'TypeScript'])
    }

    beforeEach(() => {
      mockPrisma.user.findUnique.mockResolvedValue(mockUser)
      mockPrisma.jobPosting.findUnique.mockResolvedValue(mockJobPosting)
    })

    it('should calculate match score correctly', async () => {
      const score = await service.calculateMatchScore('user-1', 'job-1')

      expect(score).toBeGreaterThan(0)
      expect(score).toBeLessThanOrEqual(100)
    })

    it('should return 0 for user without resume', async () => {
      mockPrisma.user.findUnique.mockResolvedValue({
        ...mockUser,
        resumes: []
      })

      const score = await service.calculateMatchScore('user-1', 'job-1')

      expect(score).toBe(0)
    })

    it('should return 0 for non-existent job', async () => {
      mockPrisma.jobPosting.findUnique.mockResolvedValue(null)

      const score = await service.calculateMatchScore('user-1', 'job-1')

      expect(score).toBe(0)
    })
  })

  describe('applyToJob', () => {
    const mockJobPosting = {
      id: 'job-1',
      title: 'Software Developer',
      company: 'Tech Corp',
      isActive: true
    }

    const mockApplication = {
      id: 'app-1',
      userId: 'user-1',
      jobPostingId: 'job-1',
      resumeId: 'resume-1',
      status: 'applied',
      appliedDate: new Date(),
      lastUpdated: new Date(),
      notes: 'Excited about this opportunity',
      metadata: null,
      interviewDates: null,
      jobPosting: mockJobPosting
    }

    beforeEach(() => {
      mockPrisma.jobApplication.findFirst.mockResolvedValue(null)
      mockPrisma.jobPosting.findUnique.mockResolvedValue(mockJobPosting)
      mockPrisma.jobApplication.create.mockResolvedValue(mockApplication)
    })

    it('should create job application successfully', async () => {
      const applicationData = {
        userId: 'user-1',
        jobPostingId: 'job-1',
        resumeId: 'resume-1',
        notes: 'Excited about this opportunity'
      }

      const result = await service.applyToJob(applicationData)

      expect(mockPrisma.jobApplication.create).toHaveBeenCalledWith({
        data: {
          userId: 'user-1',
          jobPostingId: 'job-1',
          resumeId: 'resume-1',
          coverLetterId: undefined,
          notes: 'Excited about this opportunity',
          metadata: null,
          status: 'applied',
          appliedDate: expect.any(Date),
          lastUpdated: expect.any(Date)
        },
        include: {
          jobPosting: true
        }
      })

      expect(result.id).toBe('app-1')
      expect(result.status).toBe('applied')
    })

    it('should throw error for duplicate application', async () => {
      mockPrisma.jobApplication.findFirst.mockResolvedValue(mockApplication)

      const applicationData = {
        userId: 'user-1',
        jobPostingId: 'job-1'
      }

      await expect(service.applyToJob(applicationData)).rejects.toThrow('You have already applied to this job')
    })

    it('should throw error for inactive job', async () => {
      mockPrisma.jobPosting.findUnique.mockResolvedValue({
        ...mockJobPosting,
        isActive: false
      })

      const applicationData = {
        userId: 'user-1',
        jobPostingId: 'job-1'
      }

      await expect(service.applyToJob(applicationData)).rejects.toThrow('Job posting not found or no longer active')
    })

    it('should throw error for non-existent job', async () => {
      mockPrisma.jobPosting.findUnique.mockResolvedValue(null)

      const applicationData = {
        userId: 'user-1',
        jobPostingId: 'job-1'
      }

      await expect(service.applyToJob(applicationData)).rejects.toThrow('Job posting not found or no longer active')
    })
  })

  describe('updateApplicationStatus', () => {
    it('should update application status successfully', async () => {
      mockPrisma.jobApplication.updateMany.mockResolvedValue({ count: 1 })

      const result = await service.updateApplicationStatus('app-1', 'interview', 'user-1')

      expect(mockPrisma.jobApplication.updateMany).toHaveBeenCalledWith({
        where: {
          id: 'app-1',
          userId: 'user-1'
        },
        data: {
          status: 'interview',
          lastUpdated: expect.any(Date)
        }
      })

      expect(result).toBe(true)
    })

    it('should return false for non-existent application', async () => {
      mockPrisma.jobApplication.updateMany.mockResolvedValue({ count: 0 })

      const result = await service.updateApplicationStatus('app-1', 'interview', 'user-1')

      expect(result).toBe(false)
    })

    it('should throw error for invalid status', async () => {
      await expect(service.updateApplicationStatus('app-1', 'invalid', 'user-1')).rejects.toThrow('Invalid application status')
    })
  })

  describe('getApplications', () => {
    const mockApplications = [
      {
        id: 'app-1',
        userId: 'user-1',
        jobPostingId: 'job-1',
        status: 'applied',
        appliedDate: new Date(),
        lastUpdated: new Date(),
        metadata: JSON.stringify({ source: 'website' }),
        interviewDates: JSON.stringify([{ date: '2024-01-15', type: 'phone' }]),
        jobPosting: {
          id: 'job-1',
          title: 'Software Developer',
          company: 'Tech Corp',
          skills: JSON.stringify(['JavaScript', 'React']),
          benefits: JSON.stringify(['Health insurance'])
        }
      }
    ]

    beforeEach(() => {
      mockPrisma.jobApplication.findMany.mockResolvedValue(mockApplications)
    })

    it('should get user applications', async () => {
      const result = await service.getApplications('user-1')

      expect(mockPrisma.jobApplication.findMany).toHaveBeenCalledWith({
        where: { userId: 'user-1' },
        include: {
          jobPosting: true
        },
        orderBy: { appliedDate: 'desc' },
        take: 50
      })

      expect(result).toHaveLength(1)
      expect(result[0].metadata).toEqual({ source: 'website' })
      expect(result[0].interviewDates).toEqual([{ date: '2024-01-15', type: 'phone' }])
    })

    it('should filter applications by status', async () => {
      await service.getApplications('user-1', { status: 'interview' })

      expect(mockPrisma.jobApplication.findMany).toHaveBeenCalledWith({
        where: { 
          userId: 'user-1',
          status: 'interview'
        },
        include: {
          jobPosting: true
        },
        orderBy: { appliedDate: 'desc' },
        take: 50
      })
    })

    it('should limit applications', async () => {
      await service.getApplications('user-1', { limit: 10 })

      expect(mockPrisma.jobApplication.findMany).toHaveBeenCalledWith({
        where: { userId: 'user-1' },
        include: {
          jobPosting: true
        },
        orderBy: { appliedDate: 'desc' },
        take: 10
      })
    })
  })

  describe('updateJobPreferences', () => {
    it('should update job preferences successfully', async () => {
      mockPrisma.userJobPreferences.upsert.mockResolvedValue({})

      const preferences = {
        userId: 'user-1',
        preferredTitles: ['Software Developer', 'Frontend Developer'],
        preferredLocations: ['San Francisco', 'Remote'],
        salaryMin: 80000,
        salaryMax: 120000,
        employmentTypes: ['full-time'],
        experienceLevel: 'mid'
      }

      const result = await service.updateJobPreferences(preferences)

      expect(mockPrisma.userJobPreferences.upsert).toHaveBeenCalledWith({
        where: { userId: 'user-1' },
        update: {
          preferredTitles: JSON.stringify(['Software Developer', 'Frontend Developer']),
          preferredLocations: JSON.stringify(['San Francisco', 'Remote']),
          salaryMin: 80000,
          salaryMax: 120000,
          employmentTypes: JSON.stringify(['full-time']),
          experienceLevel: 'mid',
          preferredCompanies: null,
          remotePreferences: null,
          industryPreferences: null,
          companySizePreferences: null,
          notificationPreferences: null,
          updatedAt: expect.any(Date)
        },
        create: {
          userId: 'user-1',
          preferredTitles: JSON.stringify(['Software Developer', 'Frontend Developer']),
          preferredLocations: JSON.stringify(['San Francisco', 'Remote']),
          salaryMin: 80000,
          salaryMax: 120000,
          employmentTypes: JSON.stringify(['full-time']),
          experienceLevel: 'mid',
          preferredCompanies: null,
          remotePreferences: null,
          industryPreferences: null,
          companySizePreferences: null,
          notificationPreferences: null,
          createdAt: expect.any(Date),
          updatedAt: expect.any(Date)
        }
      })

      expect(result).toBe(true)
    })
  })

  describe('generateRecommendations', () => {
    const mockUser = {
      id: 'user-1',
      jobPreferences: {
        preferredLocations: JSON.stringify(['San Francisco']),
        experienceLevel: 'mid',
        salaryMin: 80000
      },
      skillAssessments: [
        { skillName: 'JavaScript' },
        { skillName: 'React' }
      ]
    }

    const mockJobs = [
      {
        id: 'job-1',
        title: 'Software Developer',
        company: 'Tech Corp',
        description: 'Great job',
        location: 'San Francisco',
        experienceLevel: 'mid',
        salaryMin: 90000,
        isActive: true
      }
    ]

    beforeEach(() => {
      mockPrisma.user.findUnique.mockResolvedValue(mockUser)
      mockPrisma.jobPosting.findMany.mockResolvedValue(mockJobs)
      mockPrisma.jobRecommendation.findUnique.mockResolvedValue(null)
      mockPrisma.jobRecommendation.create.mockResolvedValue({})
    })

    it('should generate recommendations for user', async () => {
      // Mock the calculateMatchScore method
      vi.spyOn(service, 'calculateMatchScore').mockResolvedValue(75)

      const result = await service.generateRecommendations('user-1')

      expect(mockPrisma.jobRecommendation.create).toHaveBeenCalledWith({
        data: {
          userId: 'user-1',
          jobPostingId: 'job-1',
          matchScore: 75,
          reasoning: JSON.stringify({
            skillMatch: true,
            locationMatch: true,
            salaryMatch: true
          }),
          recommendedAt: expect.any(Date)
        }
      })

      expect(result).toBe(1)
    })

    it('should skip low match scores', async () => {
      vi.spyOn(service, 'calculateMatchScore').mockResolvedValue(25)

      const result = await service.generateRecommendations('user-1')

      expect(mockPrisma.jobRecommendation.create).not.toHaveBeenCalled()
      expect(result).toBe(0)
    })

    it('should skip existing recommendations', async () => {
      mockPrisma.jobRecommendation.findUnique.mockResolvedValue({ id: 'existing' })

      const result = await service.generateRecommendations('user-1')

      expect(mockPrisma.jobRecommendation.create).not.toHaveBeenCalled()
      expect(result).toBe(0)
    })

    it('should throw error for non-existent user', async () => {
      mockPrisma.user.findUnique.mockResolvedValue(null)

      await expect(service.generateRecommendations('user-1')).rejects.toThrow('User not found')
    })
  })

  describe('markRecommendationViewed', () => {
    it('should mark recommendation as viewed', async () => {
      mockPrisma.jobRecommendation.updateMany.mockResolvedValue({ count: 1 })

      const result = await service.markRecommendationViewed('rec-1', 'user-1')

      expect(mockPrisma.jobRecommendation.updateMany).toHaveBeenCalledWith({
        where: {
          id: 'rec-1',
          userId: 'user-1'
        },
        data: {
          isViewed: true,
          viewedAt: expect.any(Date)
        }
      })

      expect(result).toBe(true)
    })

    it('should return false for non-existent recommendation', async () => {
      mockPrisma.jobRecommendation.updateMany.mockResolvedValue({ count: 0 })

      const result = await service.markRecommendationViewed('rec-1', 'user-1')

      expect(result).toBe(false)
    })
  })

  describe('updateRecommendationStatus', () => {
    it('should save recommendation', async () => {
      mockPrisma.jobRecommendation.updateMany.mockResolvedValue({ count: 1 })

      const result = await service.updateRecommendationStatus('rec-1', 'user-1', 'save')

      expect(mockPrisma.jobRecommendation.updateMany).toHaveBeenCalledWith({
        where: {
          id: 'rec-1',
          userId: 'user-1'
        },
        data: {
          isSaved: true
        }
      })

      expect(result).toBe(true)
    })

    it('should dismiss recommendation', async () => {
      mockPrisma.jobRecommendation.updateMany.mockResolvedValue({ count: 1 })

      const result = await service.updateRecommendationStatus('rec-1', 'user-1', 'dismiss')

      expect(mockPrisma.jobRecommendation.updateMany).toHaveBeenCalledWith({
        where: {
          id: 'rec-1',
          userId: 'user-1'
        },
        data: {
          isDismissed: true
        }
      })

      expect(result).toBe(true)
    })
  })
})
