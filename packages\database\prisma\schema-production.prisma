// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
  output   = "../src/generated"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

// User Management
model User {
  id            String    @id @default(cuid())
  email         String    @unique
  name          String?
  image         String?
  emailVerified DateTime?
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  // Relations
  accounts Account[]
  sessions Session[]
  resumes  Resume[]
  profiles UserProfile[]

  // AI Relations
  aiContentResponses AIContentResponse[]
  aiUsageAnalytics   AIUsageAnalytics[]
  atsAnalyses        ATSAnalysis[]
  contentFeedback    ContentFeedback[]

  @@map("users")
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map("accounts")
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
  @@map("verification_tokens")
}

// User Profile for additional information
model UserProfile {
  id          String   @id @default(cuid())
  userId      String   @unique
  firstName   String?
  lastName    String?
  phone       String?
  location    String?
  website     String?
  linkedinUrl String?
  githubUrl   String?
  bio         String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("user_profiles")
}

// Resume Management
model Resume {
  id          String      @id @default(cuid())
  userId      String
  title       String
  description String?
  templateId  String?
  isPublic    Boolean     @default(false)
  publicUrl   String?     @unique
  status      ResumeStatus @default(DRAFT)
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt

  // Relations
  user         User           @relation(fields: [userId], references: [id], onDelete: Cascade)
  template     Template?      @relation(fields: [templateId], references: [id])
  experiences  Experience[]
  educations   Education[]
  skills       Skill[]
  projects     Project[]
  certifications Certification[]
  languages    Language[]
  sections     CustomSection[]
  coverLetters CoverLetter[]
  analytics    ResumeAnalytics[]
  atsAnalyses  ATSAnalysis[]

  @@map("resumes")
}

enum ResumeStatus {
  DRAFT
  PUBLISHED
  ARCHIVED
}

// Resume Sections
model Experience {
  id           String   @id @default(cuid())
  resumeId     String
  company      String
  position     String
  location     String?
  startDate    DateTime
  endDate      DateTime?
  isCurrent    Boolean  @default(false)
  description  String?
  achievements Json?    // Array of achievement strings
  displayOrder Int      @default(0)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  resume Resume @relation(fields: [resumeId], references: [id], onDelete: Cascade)

  @@map("experiences")
}

model Education {
  id           String   @id @default(cuid())
  resumeId     String
  institution  String
  degree       String
  field        String?
  location     String?
  startDate    DateTime
  endDate      DateTime?
  gpa          String?
  description  String?
  displayOrder Int      @default(0)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  resume Resume @relation(fields: [resumeId], references: [id], onDelete: Cascade)

  @@map("educations")
}

model Skill {
  id           String    @id @default(cuid())
  resumeId     String
  name         String
  category     String?   // e.g., "Technical", "Soft Skills", "Languages"
  level        SkillLevel?
  displayOrder Int       @default(0)
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt

  resume Resume @relation(fields: [resumeId], references: [id], onDelete: Cascade)

  @@map("skills")
}

enum SkillLevel {
  BEGINNER
  INTERMEDIATE
  ADVANCED
  EXPERT
}

model Project {
  id           String   @id @default(cuid())
  resumeId     String
  name         String
  description  String?
  url          String?
  githubUrl    String?
  technologies Json?    // Array of technology strings
  startDate    DateTime?
  endDate      DateTime?
  displayOrder Int      @default(0)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  resume Resume @relation(fields: [resumeId], references: [id], onDelete: Cascade)

  @@map("projects")
}

model Certification {
  id           String   @id @default(cuid())
  resumeId     String
  name         String
  issuer       String
  issueDate    DateTime
  expiryDate   DateTime?
  credentialId String?
  url          String?
  displayOrder Int      @default(0)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  resume Resume @relation(fields: [resumeId], references: [id], onDelete: Cascade)

  @@map("certifications")
}

model Language {
  id           String         @id @default(cuid())
  resumeId     String
  name         String
  proficiency  LanguageLevel
  displayOrder Int            @default(0)
  createdAt    DateTime       @default(now())
  updatedAt    DateTime       @updatedAt

  resume Resume @relation(fields: [resumeId], references: [id], onDelete: Cascade)

  @@map("languages")
}

enum LanguageLevel {
  BASIC
  CONVERSATIONAL
  FLUENT
  NATIVE
}

model CustomSection {
  id           String   @id @default(cuid())
  resumeId     String
  title        String
  content      Json     // Flexible content structure
  displayOrder Int      @default(0)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  resume Resume @relation(fields: [resumeId], references: [id], onDelete: Cascade)

  @@map("custom_sections")
}

// Templates
model Template {
  id          String   @id @default(cuid())
  name        String
  description String?
  category    String?
  isPremium   Boolean  @default(false)
  isActive    Boolean  @default(true)
  config      Json     // Template configuration and styling
  preview     String?  // Preview image URL
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  resumes Resume[]

  @@map("templates")
}

// Cover Letters
model CoverLetter {
  id          String   @id @default(cuid())
  resumeId    String?
  userId      String
  title       String
  content     String
  jobTitle    String?
  company     String?
  jobDescription String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  resume Resume? @relation(fields: [resumeId], references: [id], onDelete: SetNull)

  @@map("cover_letters")
}

// Analytics and Tracking
model ResumeAnalytics {
  id         String   @id @default(cuid())
  resumeId   String
  event      String   // e.g., "view", "download", "share"
  metadata   Json?    // Additional event data
  ipAddress  String?
  userAgent  String?
  createdAt  DateTime @default(now())

  resume Resume @relation(fields: [resumeId], references: [id], onDelete: Cascade)

  @@map("resume_analytics")
}

// AI Content Generation History
model AIGeneration {
  id        String          @id @default(cuid())
  userId    String
  type      AIGenerationType
  prompt    String
  response  String
  metadata  Json?           // Additional context like model used, tokens, etc.
  createdAt DateTime        @default(now())

  @@map("ai_generations")
}

enum AIGenerationType {
  BULLET_POINT
  SUMMARY
  COVER_LETTER
  REWRITE
  KEYWORDS
}

// Enhanced AI Content Generation Models
model AIContentResponse {
  id          String   @id @default(cuid())
  userId      String
  requestId   String
  type        String   // ContentType enum
  context     Json     // ContentContext
  options     Json     // GenerationOptions
  suggestions Json     // ContentSuggestion[]
  metadata    Json     // GenerationMetadata
  createdAt   DateTime @default(now())

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("ai_content_responses")
}

model AIUsageAnalytics {
  id                String   @id @default(cuid())
  userId            String
  period            String   // day, week, month
  requestCount      Int      @default(0)
  tokensUsed        Int      @default(0)
  contentTypes      Json     @default("{}")
  averageConfidence Float    @default(0)
  successRate       Float    @default(0)
  cost              Float    @default(0)
  date              String   // YYYY-MM-DD format
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, date])
  @@map("ai_usage_analytics")
}

model ATSAnalysis {
  id             String    @id @default(cuid())
  userId         String
  resumeId       String
  jobDescription String?
  targetKeywords String[]
  analysis       Json      // ATSOptimizationData
  score          Int       // 0-100
  createdAt      DateTime  @default(now())

  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  resume Resume @relation(fields: [resumeId], references: [id], onDelete: Cascade)

  @@map("ats_analyses")
}

model ContentFeedback {
  id           String   @id @default(cuid())
  contentId    String
  userId       String
  rating       Int      // 1-5
  feedback     String
  improvements String[]
  wasUsed      Boolean  @default(false)
  wasModified  Boolean  @default(false)
  finalContent String?
  createdAt    DateTime @default(now())

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("content_feedback")
}
