'use client'

import { useState, useEffect } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { DashboardLayout } from '@/components/dashboard/DashboardLayout'
import { TemplateGallery } from '@/components/templates/TemplateGallery'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import {
  ArrowLeft,
  ArrowRight,
  FileText,
  Palette,
  User,
  CheckCircle,
  Sparkles
} from 'lucide-react'

interface Template {
  id: string
  name: string
  description: string
  category: string
  isPremium: boolean
}

interface ResumeData {
  title: string
  description: string
  templateId: string
}

export default function NewResumePage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const preselectedTemplate = searchParams.get('template')

  const [currentStep, setCurrentStep] = useState(1)
  const [selectedTemplate, setSelectedTemplate] = useState<Template | null>(null)
  const [resumeData, setResumeData] = useState<ResumeData>({
    title: '',
    description: '',
    templateId: ''
  })
  const [isCreating, setIsCreating] = useState(false)

  const steps = [
    { id: 1, name: 'Choose Template', icon: Palette },
    { id: 2, name: 'Resume Details', icon: FileText },
    { id: 3, name: 'Create Resume', icon: Sparkles }
  ]

  useEffect(() => {
    if (preselectedTemplate) {
      // If template is preselected, skip to step 2
      setResumeData(prev => ({ ...prev, templateId: preselectedTemplate }))
      setCurrentStep(2)
    }
  }, [preselectedTemplate])

  const handleTemplateSelect = (template: Template) => {
    setSelectedTemplate(template)
    setResumeData(prev => ({ ...prev, templateId: template.id }))
  }

  const handleNext = () => {
    if (currentStep < steps.length) {
      setCurrentStep(currentStep + 1)
    }
  }

  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
    } else {
      router.push('/dashboard/resumes')
    }
  }

  const handleCreateResume = async () => {
    if (!resumeData.title || !resumeData.templateId) {
      return
    }

    setIsCreating(true)
    try {
      // Create resume API call would go here
      console.log('Creating resume:', resumeData)

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000))

      // Redirect to resume editor
      router.push(`/dashboard/resumes/new-resume-id/edit`)
    } catch (error) {
      console.error('Failed to create resume:', error)
    } finally {
      setIsCreating(false)
    }
  }

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-6">
            <div className="text-center">
              <h2 className="text-2xl font-bold mb-2">Choose Your Template</h2>
              <p className="text-muted-foreground">
                Select a professional template that matches your style and industry
              </p>
            </div>
            <TemplateGallery
              onSelectTemplate={handleTemplateSelect}
              showSelectButton={true}
            />
          </div>
        )

      case 2:
        return (
          <div className="max-w-2xl mx-auto space-y-6">
            <div className="text-center">
              <h2 className="text-2xl font-bold mb-2">Resume Details</h2>
              <p className="text-muted-foreground">
                Give your resume a name and description to help you organize it
              </p>
            </div>

            {selectedTemplate && (
              <Card className="glass-card">
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <CheckCircle className="w-5 h-5 text-green-600" />
                    <span>Selected Template</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center space-x-4">
                    <div className="w-16 h-20 bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-800 dark:to-gray-900 rounded border">
                      <div className="p-2 space-y-1">
                        <div className="h-1 bg-gray-300 dark:bg-gray-600 rounded w-3/4"></div>
                        <div className="h-1 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
                        <div className="space-y-0.5 mt-2">
                          <div className="h-0.5 bg-gray-200 dark:bg-gray-700 rounded w-full"></div>
                          <div className="h-0.5 bg-gray-200 dark:bg-gray-700 rounded w-4/5"></div>
                        </div>
                      </div>
                    </div>
                    <div>
                      <h3 className="font-semibold">{selectedTemplate.name}</h3>
                      <p className="text-sm text-muted-foreground">{selectedTemplate.description}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            <Card className="glass-card">
              <CardContent className="p-6 space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="title">Resume Title *</Label>
                  <Input
                    id="title"
                    placeholder="e.g., Software Engineer Resume, Marketing Manager CV"
                    value={resumeData.title}
                    onChange={(e) => setResumeData(prev => ({ ...prev, title: e.target.value }))}
                    className="glass-input"
                  />
                  <p className="text-xs text-muted-foreground">
                    Choose a descriptive name to help you identify this resume later
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">Description (Optional)</Label>
                  <Textarea
                    id="description"
                    placeholder="e.g., Tailored for senior software engineering positions at tech companies"
                    value={resumeData.description}
                    onChange={(e) => setResumeData(prev => ({ ...prev, description: e.target.value }))}
                    className="glass-input"
                    rows={3}
                  />
                  <p className="text-xs text-muted-foreground">
                    Add notes about the target role or company for this resume
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        )

      case 3:
        return (
          <div className="max-w-2xl mx-auto text-center space-y-6">
            <div>
              <h2 className="text-2xl font-bold mb-2">Ready to Create!</h2>
              <p className="text-muted-foreground">
                Review your selections and create your new resume
              </p>
            </div>

            <Card className="glass-card">
              <CardContent className="p-6 space-y-4">
                <div className="flex items-center justify-between">
                  <span className="font-medium">Resume Title:</span>
                  <span>{resumeData.title}</span>
                </div>
                {resumeData.description && (
                  <div className="flex items-start justify-between">
                    <span className="font-medium">Description:</span>
                    <span className="text-right max-w-xs">{resumeData.description}</span>
                  </div>
                )}
                <div className="flex items-center justify-between">
                  <span className="font-medium">Template:</span>
                  <span>{selectedTemplate?.name}</span>
                </div>
              </CardContent>
            </Card>

            <Button
              size="lg"
              onClick={handleCreateResume}
              disabled={isCreating || !resumeData.title}
              className="w-full glass-card bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white border-0"
            >
              {isCreating ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                  Creating Resume...
                </>
              ) : (
                <>
                  <Sparkles className="w-4 h-4 mr-2" />
                  Create My Resume
                </>
              )}
            </Button>
          </div>
        )

      default:
        return null
    }
  }

  return (
    <DashboardLayout currentPage="resumes">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              Create New Resume
            </h1>
            <p className="text-muted-foreground mt-1">
              Build a professional resume in minutes with our AI-powered tools
            </p>
          </div>
        </div>

        {/* Progress Steps */}
        <div className="glass-panel p-4 rounded-2xl">
          <div className="flex items-center justify-between">
            {steps.map((step, index) => {
              const Icon = step.icon
              const isActive = currentStep === step.id
              const isCompleted = currentStep > step.id

              return (
                <div key={step.id} className="flex items-center">
                  <div className={`
                    flex items-center space-x-2 px-4 py-2 rounded-lg transition-all
                    ${isActive ? 'glass-card bg-gradient-to-r from-blue-600/20 to-purple-600/20' : ''}
                    ${isCompleted ? 'text-green-600' : ''}
                  `}>
                    <div className={`
                      w-8 h-8 rounded-full flex items-center justify-center
                      ${isActive ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white' : ''}
                      ${isCompleted ? 'bg-green-600 text-white' : ''}
                      ${!isActive && !isCompleted ? 'bg-gray-200 dark:bg-gray-700' : ''}
                    `}>
                      {isCompleted ? (
                        <CheckCircle className="w-4 h-4" />
                      ) : (
                        <Icon className="w-4 h-4" />
                      )}
                    </div>
                    <span className={`font-medium ${isActive ? 'text-blue-600' : ''}`}>
                      {step.name}
                    </span>
                  </div>
                  {index < steps.length - 1 && (
                    <div className="w-12 h-px bg-gray-300 dark:bg-gray-600 mx-4" />
                  )}
                </div>
              )
            })}
          </div>
        </div>

        {/* Step Content */}
        <div className="min-h-[600px]">
          {renderStepContent()}
        </div>

        {/* Navigation */}
        <div className="flex items-center justify-between glass-panel p-4 rounded-2xl">
          <Button
            variant="outline"
            onClick={handleBack}
            className="glass-input"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            {currentStep === 1 ? 'Back to Resumes' : 'Previous'}
          </Button>

          {currentStep < steps.length && currentStep !== 3 && (
            <Button
              onClick={handleNext}
              disabled={currentStep === 1 && !selectedTemplate}
              className="glass-card bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white border-0"
            >
              Next
              <ArrowRight className="w-4 h-4 ml-2" />
            </Button>
          )}
        </div>
      </div>
    </DashboardLayout>
  )
}

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-6">
            <div className="text-center">
              <h2 className="text-2xl font-bold mb-2">Choose Your Template</h2>
              <p className="text-muted-foreground">
                Select a professional template that matches your style and industry
              </p>
            </div>
            <TemplateGallery
              onSelectTemplate={handleTemplateSelect}
              showSelectButton={true}
            />
          </div>
        )

      case 2:
        return (
          <div className="max-w-2xl mx-auto space-y-6">
            <div className="text-center">
              <h2 className="text-2xl font-bold mb-2">Resume Details</h2>
              <p className="text-muted-foreground">
                Give your resume a name and description to help you organize it
              </p>
            </div>

            {selectedTemplate && (
              <Card className="glass-card">
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <CheckCircle className="w-5 h-5 text-green-600" />
                    <span>Selected Template</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center space-x-4">
                    <div className="w-16 h-20 bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-800 dark:to-gray-900 rounded border">
                      <div className="p-2 space-y-1">
                        <div className="h-1 bg-gray-300 dark:bg-gray-600 rounded w-3/4"></div>
                        <div className="h-1 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
                        <div className="space-y-0.5 mt-2">
                          <div className="h-0.5 bg-gray-200 dark:bg-gray-700 rounded w-full"></div>
                          <div className="h-0.5 bg-gray-200 dark:bg-gray-700 rounded w-4/5"></div>
                        </div>
                      </div>
                    </div>
                    <div>
                      <h3 className="font-semibold">{selectedTemplate.name}</h3>
                      <p className="text-sm text-muted-foreground">{selectedTemplate.description}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            <Card className="glass-card">
              <CardContent className="p-6 space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="title">Resume Title *</Label>
                  <Input
                    id="title"
                    placeholder="e.g., Software Engineer Resume, Marketing Manager CV"
                    value={resumeData.title}
                    onChange={(e) => setResumeData(prev => ({ ...prev, title: e.target.value }))}
                    className="glass-input"
                  />
                  <p className="text-xs text-muted-foreground">
                    Choose a descriptive name to help you identify this resume later
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">Description (Optional)</Label>
                  <Textarea
                    id="description"
                    placeholder="e.g., Tailored for senior software engineering positions at tech companies"
                    value={resumeData.description}
                    onChange={(e) => setResumeData(prev => ({ ...prev, description: e.target.value }))}
                    className="glass-input"
                    rows={3}
                  />
                  <p className="text-xs text-muted-foreground">
                    Add notes about the target role or company for this resume
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        )

      case 3:
        return (
          <div className="max-w-2xl mx-auto text-center space-y-6">
            <div>
              <h2 className="text-2xl font-bold mb-2">Ready to Create!</h2>
              <p className="text-muted-foreground">
                Review your selections and create your new resume
              </p>
            </div>

            <Card className="glass-card">
              <CardContent className="p-6 space-y-4">
                <div className="flex items-center justify-between">
                  <span className="font-medium">Resume Title:</span>
                  <span>{resumeData.title}</span>
                </div>
                {resumeData.description && (
                  <div className="flex items-start justify-between">
                    <span className="font-medium">Description:</span>
                    <span className="text-right max-w-xs">{resumeData.description}</span>
                  </div>
                )}
                <div className="flex items-center justify-between">
                  <span className="font-medium">Template:</span>
                  <span>{selectedTemplate?.name}</span>
                </div>
              </CardContent>
            </Card>

            <Button
              size="lg"
              onClick={handleCreateResume}
              disabled={isCreating || !resumeData.title}
              className="w-full glass-card bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white border-0"
            >
              {isCreating ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                  Creating Resume...
                </>
              ) : (
                <>
                  <Sparkles className="w-4 h-4 mr-2" />
                  Create My Resume
                </>
              )}
            </Button>
          </div>
        )

      default:
        return null
    }
  }
