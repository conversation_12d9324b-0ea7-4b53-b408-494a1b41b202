"""
Base Job Scraper
Implements FR-5.2: Job Market Data Ingestion Service
"""

import asyncio
import logging
from abc import ABC, abstractmethod
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
import requests
from bs4 import BeautifulSoup
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import time
import random

@dataclass
class JobPosting:
    """Structured job posting data"""
    title: str
    company: str
    description: str
    requirements: Optional[str] = None
    skills: Optional[List[str]] = None
    experience_level: Optional[str] = None
    location: Optional[str] = None
    salary_min: Optional[int] = None
    salary_max: Optional[int] = None
    job_type: Optional[str] = None
    industry: Optional[str] = None
    source: str = ""
    external_id: Optional[str] = None
    external_url: Optional[str] = None
    posted_date: Optional[datetime] = None
    expires_date: Optional[datetime] = None

class BaseScraper(ABC):
    """Base class for job site scrapers"""
    
    def __init__(self, source_name: str, base_url: str, rate_limit: float = 1.0):
        self.source_name = source_name
        self.base_url = base_url
        self.rate_limit = rate_limit  # Seconds between requests
        self.session = requests.Session()
        self.logger = logging.getLogger(f"scraper.{source_name}")
        
        # Setup session headers
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        })

    def setup_selenium_driver(self, headless: bool = True) -> webdriver.Chrome:
        """Setup Selenium Chrome driver"""
        options = Options()
        if headless:
            options.add_argument('--headless')
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-gpu')
        options.add_argument('--window-size=1920,1080')
        options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36')
        
        return webdriver.Chrome(options=options)

    def respect_rate_limit(self):
        """Add delay between requests"""
        time.sleep(self.rate_limit + random.uniform(0, 0.5))

    def extract_salary_range(self, salary_text: str) -> tuple[Optional[int], Optional[int]]:
        """Extract salary range from text"""
        if not salary_text:
            return None, None
            
        # Remove common prefixes/suffixes
        salary_text = salary_text.lower().replace('$', '').replace(',', '').replace('k', '000')
        
        # Look for ranges like "80000-120000" or "80-120"
        import re
        range_pattern = r'(\d+)(?:000)?\s*[-–to]\s*(\d+)(?:000)?'
        match = re.search(range_pattern, salary_text)
        
        if match:
            min_sal = int(match.group(1))
            max_sal = int(match.group(2))
            
            # Handle abbreviated numbers (80-120 -> 80000-120000)
            if min_sal < 1000:
                min_sal *= 1000
            if max_sal < 1000:
                max_sal *= 1000
                
            return min_sal, max_sal
        
        # Look for single values
        single_pattern = r'(\d+)(?:000)?'
        match = re.search(single_pattern, salary_text)
        if match:
            salary = int(match.group(1))
            if salary < 1000:
                salary *= 1000
            return salary, salary
            
        return None, None

    def extract_experience_level(self, text: str) -> Optional[str]:
        """Extract experience level from job text"""
        if not text:
            return None
            
        text_lower = text.lower()
        
        # Senior level indicators
        if any(keyword in text_lower for keyword in ['senior', 'sr.', 'lead', 'principal', 'staff', '5+ years', '7+ years', '10+ years']):
            return 'SENIOR'
        
        # Mid level indicators
        if any(keyword in text_lower for keyword in ['mid', 'intermediate', '3+ years', '4+ years', '2-5 years']):
            return 'MID'
        
        # Entry level indicators
        if any(keyword in text_lower for keyword in ['entry', 'junior', 'jr.', 'graduate', 'new grad', '0-2 years', 'internship']):
            return 'ENTRY'
        
        # Executive level indicators
        if any(keyword in text_lower for keyword in ['director', 'vp', 'vice president', 'chief', 'head of', 'executive']):
            return 'EXECUTIVE'
            
        return None

    def extract_job_type(self, text: str) -> Optional[str]:
        """Extract job type from text"""
        if not text:
            return None
            
        text_lower = text.lower()
        
        if 'remote' in text_lower:
            return 'REMOTE'
        elif any(keyword in text_lower for keyword in ['contract', 'contractor', 'freelance']):
            return 'CONTRACT'
        elif any(keyword in text_lower for keyword in ['part-time', 'part time']):
            return 'PART_TIME'
        elif any(keyword in text_lower for keyword in ['full-time', 'full time']):
            return 'FULL_TIME'
            
        return 'FULL_TIME'  # Default assumption

    def extract_skills_from_text(self, text: str) -> List[str]:
        """Extract technical skills from job description"""
        if not text:
            return []
            
        # Common technical skills patterns
        skill_patterns = [
            # Programming languages
            r'\b(JavaScript|TypeScript|Python|Java|C\+\+|C#|PHP|Ruby|Go|Rust|Swift|Kotlin|Scala|R|MATLAB)\b',
            # Frameworks and libraries
            r'\b(React|Vue|Angular|Node\.js|Express|Django|Flask|Spring|Laravel|Rails|ASP\.NET)\b',
            # Databases
            r'\b(SQL|MySQL|PostgreSQL|MongoDB|Redis|Elasticsearch|Oracle|SQLite|Cassandra)\b',
            # Cloud and DevOps
            r'\b(AWS|Azure|GCP|Docker|Kubernetes|Jenkins|Git|GitHub|GitLab|Terraform|Ansible)\b',
            # Web technologies
            r'\b(HTML|CSS|SASS|SCSS|Tailwind|Bootstrap|jQuery|REST|GraphQL|API)\b',
            # Data and AI
            r'\b(Machine Learning|Deep Learning|TensorFlow|PyTorch|Pandas|NumPy|Tableau|Power BI)\b',
            # Other tools
            r'\b(Jira|Confluence|Slack|Figma|Adobe|Photoshop|Sketch|Linux|Windows|macOS)\b'
        ]
        
        skills = set()
        for pattern in skill_patterns:
            import re
            matches = re.findall(pattern, text, re.IGNORECASE)
            skills.update(matches)
            
        return list(skills)

    def clean_text(self, text: str) -> str:
        """Clean and normalize text"""
        if not text:
            return ""
            
        # Remove extra whitespace and normalize
        text = ' '.join(text.split())
        
        # Remove HTML entities
        import html
        text = html.unescape(text)
        
        return text.strip()

    def parse_date(self, date_str: str) -> Optional[datetime]:
        """Parse various date formats"""
        if not date_str:
            return None
            
        date_str = date_str.strip().lower()
        
        # Handle relative dates
        if 'today' in date_str or 'just posted' in date_str:
            return datetime.now()
        elif 'yesterday' in date_str:
            return datetime.now() - timedelta(days=1)
        elif 'days ago' in date_str:
            import re
            match = re.search(r'(\d+)\s*days?\s*ago', date_str)
            if match:
                days = int(match.group(1))
                return datetime.now() - timedelta(days=days)
        elif 'weeks ago' in date_str:
            import re
            match = re.search(r'(\d+)\s*weeks?\s*ago', date_str)
            if match:
                weeks = int(match.group(1))
                return datetime.now() - timedelta(weeks=weeks)
        elif 'months ago' in date_str:
            import re
            match = re.search(r'(\d+)\s*months?\s*ago', date_str)
            if match:
                months = int(match.group(1))
                return datetime.now() - timedelta(days=months * 30)
        
        # Try to parse absolute dates
        from dateutil import parser
        try:
            return parser.parse(date_str)
        except:
            return None

    @abstractmethod
    async def scrape_jobs(self, search_terms: List[str], locations: List[str], max_pages: int = 5) -> List[JobPosting]:
        """Scrape jobs from the source"""
        pass

    @abstractmethod
    def build_search_url(self, search_term: str, location: str, page: int = 1) -> str:
        """Build search URL for the source"""
        pass

    async def scrape_with_retry(self, url: str, max_retries: int = 3) -> Optional[BeautifulSoup]:
        """Scrape URL with retry logic"""
        for attempt in range(max_retries):
            try:
                self.respect_rate_limit()
                response = self.session.get(url, timeout=30)
                response.raise_for_status()
                
                soup = BeautifulSoup(response.content, 'html.parser')
                return soup
                
            except Exception as e:
                self.logger.warning(f"Attempt {attempt + 1} failed for {url}: {e}")
                if attempt == max_retries - 1:
                    self.logger.error(f"Failed to scrape {url} after {max_retries} attempts")
                    return None
                
                # Exponential backoff
                await asyncio.sleep(2 ** attempt)
        
        return None

    def validate_job_posting(self, job: JobPosting) -> bool:
        """Validate job posting data"""
        # Required fields
        if not job.title or not job.company or not job.description:
            return False
            
        # Reasonable length checks
        if len(job.title) > 200 or len(job.company) > 200:
            return False
            
        if len(job.description) < 50:  # Too short to be useful
            return False
            
        # Salary validation
        if job.salary_min and job.salary_max:
            if job.salary_min > job.salary_max:
                job.salary_min, job.salary_max = job.salary_max, job.salary_min
            
            # Reasonable salary ranges
            if job.salary_min < 20000 or job.salary_max > 1000000:
                job.salary_min = job.salary_max = None
        
        return True

    async def process_job_batch(self, jobs: List[JobPosting]) -> List[JobPosting]:
        """Process and validate a batch of jobs"""
        valid_jobs = []
        
        for job in jobs:
            if self.validate_job_posting(job):
                # Set source
                job.source = self.source_name
                
                # Extract additional data if missing
                if not job.skills:
                    job.skills = self.extract_skills_from_text(f"{job.title} {job.description} {job.requirements or ''}")
                
                if not job.experience_level:
                    job.experience_level = self.extract_experience_level(f"{job.title} {job.description}")
                
                if not job.job_type:
                    job.job_type = self.extract_job_type(f"{job.title} {job.description}")
                
                valid_jobs.append(job)
            else:
                self.logger.debug(f"Invalid job posting filtered out: {job.title} at {job.company}")
        
        self.logger.info(f"Processed {len(jobs)} jobs, {len(valid_jobs)} valid")
        return valid_jobs
