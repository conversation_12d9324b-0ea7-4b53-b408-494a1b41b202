'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { 
  FileText, 
  Sparkles, 
  RefreshCw, 
  Copy, 
  Download, 
  Edit,
  Wand2,
  Target,
  User,
  Building
} from 'lucide-react'

interface CoverLetterData {
  applicantName: string
  applicantEmail: string
  applicantPhone: string
  companyName: string
  hiringManagerName: string
  position: string
  jobDescription: string
  experience: string
  skills: string
  achievements: string
  tone: 'professional' | 'creative' | 'technical' | 'executive'
  length: 'short' | 'medium' | 'long'
}

interface CoverLetterGeneratorProps {
  resumeData?: any
  onSave?: (coverLetter: string) => void
}

export function CoverLetterGenerator({ resumeData, onSave }: CoverLetterGeneratorProps) {
  const [formData, setFormData] = useState<CoverLetterData>({
    applicantName: resumeData?.personalInfo?.fullName || '',
    applicantEmail: resumeData?.personalInfo?.email || '',
    applicantPhone: resumeData?.personalInfo?.phone || '',
    companyName: '',
    hiringManagerName: '',
    position: '',
    jobDescription: '',
    experience: '',
    skills: '',
    achievements: '',
    tone: 'professional',
    length: 'medium'
  })

  const [generatedLetter, setGeneratedLetter] = useState('')
  const [isGenerating, setIsGenerating] = useState(false)
  const [isEditing, setIsEditing] = useState(false)

  const generateCoverLetter = async () => {
    setIsGenerating(true)
    try {
      // Simulate AI generation - replace with actual API call
      await new Promise(resolve => setTimeout(resolve, 3000))

      const mockCoverLetter = `Dear ${formData.hiringManagerName || 'Hiring Manager'},

I am writing to express my strong interest in the ${formData.position} position at ${formData.companyName}. With my background in ${formData.experience} and proven expertise in ${formData.skills}, I am confident that I would be a valuable addition to your team.

In my previous roles, I have successfully ${formData.achievements}. These experiences have equipped me with the technical skills and problem-solving abilities that directly align with the requirements outlined in your job description.

What particularly excites me about this opportunity at ${formData.companyName} is the chance to contribute to innovative projects while working with a team that values excellence and continuous learning. I am eager to bring my passion for technology and my commitment to delivering high-quality results to your organization.

I would welcome the opportunity to discuss how my background and enthusiasm can contribute to ${formData.companyName}'s continued success. Thank you for considering my application. I look forward to hearing from you soon.

Sincerely,
${formData.applicantName}
${formData.applicantEmail}
${formData.applicantPhone}`

      setGeneratedLetter(mockCoverLetter)
    } catch (error) {
      console.error('Error generating cover letter:', error)
    } finally {
      setIsGenerating(false)
    }
  }

  const copyToClipboard = () => {
    navigator.clipboard.writeText(generatedLetter)
  }

  const downloadAsText = () => {
    const blob = new Blob([generatedLetter], { type: 'text/plain' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `${formData.companyName}_${formData.position}_cover_letter.txt`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  const handleSave = () => {
    if (onSave) {
      onSave(generatedLetter)
    }
  }

  const updateFormData = (field: keyof CoverLetterData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const isFormValid = formData.companyName && formData.position && formData.jobDescription

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card className="glass-card">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <FileText className="w-5 h-5 text-blue-600" />
            <span>AI Cover Letter Generator</span>
          </CardTitle>
          <CardDescription>
            Create personalized, professional cover letters tailored to specific job applications
          </CardDescription>
        </CardHeader>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Input Form */}
        <div className="space-y-6">
          {/* Personal Information */}
          <Card className="glass-card">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <User className="w-4 h-4 text-green-600" />
                <span>Personal Information</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="applicantName">Full Name *</Label>
                  <Input
                    id="applicantName"
                    value={formData.applicantName}
                    onChange={(e) => updateFormData('applicantName', e.target.value)}
                    className="glass-input"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="applicantEmail">Email *</Label>
                  <Input
                    id="applicantEmail"
                    type="email"
                    value={formData.applicantEmail}
                    onChange={(e) => updateFormData('applicantEmail', e.target.value)}
                    className="glass-input"
                  />
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="applicantPhone">Phone</Label>
                <Input
                  id="applicantPhone"
                  value={formData.applicantPhone}
                  onChange={(e) => updateFormData('applicantPhone', e.target.value)}
                  className="glass-input"
                />
              </div>
            </CardContent>
          </Card>

          {/* Job Information */}
          <Card className="glass-card">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Building className="w-4 h-4 text-blue-600" />
                <span>Job Information</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="companyName">Company Name *</Label>
                  <Input
                    id="companyName"
                    value={formData.companyName}
                    onChange={(e) => updateFormData('companyName', e.target.value)}
                    className="glass-input"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="position">Position *</Label>
                  <Input
                    id="position"
                    value={formData.position}
                    onChange={(e) => updateFormData('position', e.target.value)}
                    className="glass-input"
                  />
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="hiringManagerName">Hiring Manager Name</Label>
                <Input
                  id="hiringManagerName"
                  value={formData.hiringManagerName}
                  onChange={(e) => updateFormData('hiringManagerName', e.target.value)}
                  placeholder="Leave blank if unknown"
                  className="glass-input"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="jobDescription">Job Description *</Label>
                <Textarea
                  id="jobDescription"
                  value={formData.jobDescription}
                  onChange={(e) => updateFormData('jobDescription', e.target.value)}
                  placeholder="Paste the job description here..."
                  className="glass-input min-h-[120px]"
                />
              </div>
            </CardContent>
          </Card>

          {/* Experience & Skills */}
          <Card className="glass-card">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Target className="w-4 h-4 text-purple-600" />
                <span>Your Background</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="experience">Relevant Experience</Label>
                <Textarea
                  id="experience"
                  value={formData.experience}
                  onChange={(e) => updateFormData('experience', e.target.value)}
                  placeholder="Describe your relevant work experience..."
                  className="glass-input"
                  rows={3}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="skills">Key Skills</Label>
                <Textarea
                  id="skills"
                  value={formData.skills}
                  onChange={(e) => updateFormData('skills', e.target.value)}
                  placeholder="List your key skills relevant to this position..."
                  className="glass-input"
                  rows={3}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="achievements">Notable Achievements</Label>
                <Textarea
                  id="achievements"
                  value={formData.achievements}
                  onChange={(e) => updateFormData('achievements', e.target.value)}
                  placeholder="Highlight your key achievements and accomplishments..."
                  className="glass-input"
                  rows={3}
                />
              </div>
            </CardContent>
          </Card>

          {/* Generation Settings */}
          <Card className="glass-card">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Wand2 className="w-4 h-4 text-yellow-600" />
                <span>Generation Settings</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="tone">Tone</Label>
                  <Select value={formData.tone} onValueChange={(value: any) => updateFormData('tone', value)}>
                    <SelectTrigger className="glass-input">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent className="glass-card">
                      <SelectItem value="professional">Professional</SelectItem>
                      <SelectItem value="creative">Creative</SelectItem>
                      <SelectItem value="technical">Technical</SelectItem>
                      <SelectItem value="executive">Executive</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="length">Length</Label>
                  <Select value={formData.length} onValueChange={(value: any) => updateFormData('length', value)}>
                    <SelectTrigger className="glass-input">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent className="glass-card">
                      <SelectItem value="short">Short (3 paragraphs)</SelectItem>
                      <SelectItem value="medium">Medium (4-5 paragraphs)</SelectItem>
                      <SelectItem value="long">Long (6+ paragraphs)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <Button 
                onClick={generateCoverLetter}
                disabled={isGenerating || !isFormValid}
                className="w-full glass-card bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white border-0"
                size="lg"
              >
                {isGenerating ? (
                  <>
                    <RefreshCw className="w-5 h-5 mr-2 animate-spin" />
                    Generating Cover Letter...
                  </>
                ) : (
                  <>
                    <Sparkles className="w-5 h-5 mr-2" />
                    Generate Cover Letter
                  </>
                )}
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Generated Cover Letter */}
        <div className="space-y-6">
          <Card className="glass-card">
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <FileText className="w-4 h-4 text-green-600" />
                  <span>Generated Cover Letter</span>
                </div>
                {generatedLetter && (
                  <div className="flex items-center space-x-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => setIsEditing(!isEditing)}
                      className="glass-input"
                    >
                      <Edit className="w-3 h-3 mr-1" />
                      {isEditing ? 'Preview' : 'Edit'}
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={copyToClipboard}
                      className="glass-input"
                    >
                      <Copy className="w-3 h-3 mr-1" />
                      Copy
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={downloadAsText}
                      className="glass-input"
                    >
                      <Download className="w-3 h-3 mr-1" />
                      Download
                    </Button>
                  </div>
                )}
              </CardTitle>
            </CardHeader>
            <CardContent>
              {!generatedLetter ? (
                <div className="text-center py-12">
                  <FileText className="w-16 h-16 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">No Cover Letter Generated</h3>
                  <p className="text-muted-foreground">
                    Fill in the form and click "Generate Cover Letter" to create your personalized cover letter
                  </p>
                </div>
              ) : (
                <div className="space-y-4">
                  {isEditing ? (
                    <Textarea
                      value={generatedLetter}
                      onChange={(e) => setGeneratedLetter(e.target.value)}
                      className="glass-input min-h-[500px] font-mono text-sm"
                    />
                  ) : (
                    <div className="glass-input p-4 rounded-lg min-h-[500px] whitespace-pre-wrap font-serif text-sm leading-relaxed">
                      {generatedLetter}
                    </div>
                  )}

                  <div className="flex items-center justify-between pt-4 border-t border-border">
                    <div className="text-sm text-muted-foreground">
                      {generatedLetter.split(' ').length} words • {generatedLetter.split('\n\n').length} paragraphs
                    </div>
                    <Button
                      onClick={handleSave}
                      className="glass-card bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white border-0"
                    >
                      Save Cover Letter
                    </Button>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
