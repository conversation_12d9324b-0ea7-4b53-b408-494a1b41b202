#!/usr/bin/env tsx

/**
 * AI Content Generation & ATS Optimization Testing Script
 * 
 * This script performs comprehensive testing of the AI system:
 * 1. AI content generation functionality
 * 2. ATS optimization and analysis
 * 3. Content quality validation
 * 4. API endpoints and data persistence
 * 5. Rate limiting and usage analytics
 * 6. Error handling and edge cases
 */

import { config } from 'dotenv';
import { join } from 'path';
import { 
  aiContentRequestSchema,
  contentContextSchema,
  generationOptionsSchema,
  atsOptimizationDataSchema,
  contentSuggestionSchema,
} from '@careercraft/shared/schemas/ai';
import {
  ContentType,
  ExperienceLevel,
  ContentTone,
  ContentLength,
  ContentStyle,
  ATSIssueType,
  IssueSeverity,
} from '@careercraft/shared/types/ai';
import { ContentGenerator } from '../apps/web/src/lib/ai/content-generator';
import { ATSOptimizer } from '../apps/web/src/lib/ai/ats-optimizer';

// Load environment variables
config({ path: join(__dirname, '../.env.local') });

interface TestResult {
  name: string;
  status: 'PASS' | 'FAIL' | 'SKIP';
  duration: number;
  error?: string;
  details?: any;
}

class AISystemTester {
  private results: TestResult[] = [];
  private contentGenerator: ContentGenerator;
  private atsOptimizer: ATSOptimizer;

  constructor() {
    this.contentGenerator = new ContentGenerator({
      apiKey: process.env.OPENAI_API_KEY || 'mock-key',
      model: 'gpt-4',
    });
    this.atsOptimizer = new ATSOptimizer();
  }

  private async runTest(name: string, testFn: () => Promise<any>): Promise<void> {
    const startTime = Date.now();
    console.log(`🤖 Running: ${name}`);

    try {
      const result = await testFn();
      const duration = Date.now() - startTime;
      
      this.results.push({
        name,
        status: 'PASS',
        duration,
        details: result,
      });
      
      console.log(`✅ PASS: ${name} (${duration}ms)`);
    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      
      this.results.push({
        name,
        status: 'FAIL',
        duration,
        error: errorMessage,
      });
      
      console.log(`❌ FAIL: ${name} (${duration}ms) - ${errorMessage}`);
    }
  }

  async testAIDataModels(): Promise<void> {
    await this.runTest('AI Data Models & Schemas', async () => {
      // Test ContentContext validation
      const validContext = {
        firstName: 'John',
        lastName: 'Doe',
        currentRole: 'Software Engineer',
        industry: 'Technology',
        experienceLevel: ExperienceLevel.MID_LEVEL,
        targetJobTitle: 'Senior Software Engineer',
        skills: ['JavaScript', 'React', 'Node.js'],
        tone: ContentTone.PROFESSIONAL,
      };

      const contextResult = contentContextSchema.safeParse(validContext);
      if (!contextResult.success) {
        throw new Error(`Content context validation failed: ${contextResult.error.message}`);
      }

      // Test GenerationOptions validation
      const validOptions = {
        length: ContentLength.MEDIUM,
        style: ContentStyle.PARAGRAPH,
        includeKeywords: true,
        atsOptimized: true,
        industrySpecific: true,
        creativityLevel: 0.7,
        maxSuggestions: 3,
        language: 'en',
      };

      const optionsResult = generationOptionsSchema.safeParse(validOptions);
      if (!optionsResult.success) {
        throw new Error(`Generation options validation failed: ${optionsResult.error.message}`);
      }

      // Test AIContentRequest validation
      const validRequest = {
        type: ContentType.PROFESSIONAL_SUMMARY,
        context: validContext,
        options: validOptions,
      };

      const requestResult = aiContentRequestSchema.omit({ userId: true }).safeParse(validRequest);
      if (!requestResult.success) {
        throw new Error(`AI content request validation failed: ${requestResult.error.message}`);
      }

      return {
        contextValidation: true,
        optionsValidation: true,
        requestValidation: true,
        contentTypes: Object.keys(ContentType).length,
        experienceLevels: Object.keys(ExperienceLevel).length,
      };
    });
  }

  async testContentGeneration(): Promise<void> {
    await this.runTest('AI Content Generation', async () => {
      const request = {
        type: ContentType.PROFESSIONAL_SUMMARY,
        context: {
          firstName: 'Jane',
          lastName: 'Smith',
          currentRole: 'Marketing Manager',
          industry: 'Technology',
          experienceLevel: ExperienceLevel.MID_LEVEL,
          targetJobTitle: 'Senior Marketing Manager',
          skills: ['Digital Marketing', 'Analytics', 'Strategy'],
          tone: ContentTone.PROFESSIONAL,
        },
        options: {
          length: ContentLength.MEDIUM,
          style: ContentStyle.PARAGRAPH,
          includeKeywords: true,
          atsOptimized: true,
          industrySpecific: true,
          creativityLevel: 0.7,
          maxSuggestions: 2,
          language: 'en',
        },
        userId: 'test-user-id',
      };

      const response = await this.contentGenerator.generateContent(request);

      if (!response.id || !response.suggestions || response.suggestions.length === 0) {
        throw new Error('Invalid content generation response');
      }

      // Validate each suggestion
      for (const suggestion of response.suggestions) {
        const suggestionResult = contentSuggestionSchema.safeParse(suggestion);
        if (!suggestionResult.success) {
          throw new Error(`Invalid suggestion format: ${suggestionResult.error.message}`);
        }

        if (!suggestion.content || suggestion.content.length < 10) {
          throw new Error('Generated content is too short');
        }

        if (suggestion.confidence < 0 || suggestion.confidence > 1) {
          throw new Error('Invalid confidence score');
        }

        if (suggestion.atsScore < 0 || suggestion.atsScore > 100) {
          throw new Error('Invalid ATS score');
        }
      }

      return {
        responseGenerated: true,
        suggestionsCount: response.suggestions.length,
        averageConfidence: response.metadata.confidence,
        averageATSScore: response.suggestions.reduce((sum, s) => sum + s.atsScore, 0) / response.suggestions.length,
        processingTime: response.metadata.processingTime,
      };
    });
  }

  async testATSOptimization(): Promise<void> {
    await this.runTest('ATS Optimization Analysis', async () => {
      // Mock resume data
      const mockResume = {
        id: 'test-resume-id',
        userId: 'test-user-id',
        title: 'Software Engineer Resume',
        personalInfo: {
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          phone: '******-0123',
          summary: 'Experienced software engineer with 5 years of experience in web development.',
        },
        sections: [
          {
            id: 'work-1',
            type: 'work_experience',
            data: [
              {
                id: 'exp-1',
                company: 'Tech Corp',
                position: 'Software Engineer',
                description: 'Developed web applications using React and Node.js.',
                achievements: [
                  'Improved application performance by 30%',
                  'Led a team of 3 developers',
                ],
                technologies: ['React', 'Node.js', 'MongoDB'],
                startDate: '2020-01',
                endDate: '2023-12',
                isCurrentRole: false,
              },
            ],
          },
        ],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      } as any;

      const jobDescription = `
        We are looking for a Senior Software Engineer with experience in:
        - React and modern JavaScript frameworks
        - Node.js and backend development
        - Database design and optimization
        - Team leadership and mentoring
        - Agile development methodologies
      `;

      const analysis = await this.atsOptimizer.analyzeResume(mockResume, jobDescription);

      // Validate ATS analysis structure
      const analysisResult = atsOptimizationDataSchema.safeParse(analysis);
      if (!analysisResult.success) {
        throw new Error(`Invalid ATS analysis format: ${analysisResult.error.message}`);
      }

      if (analysis.score < 0 || analysis.score > 100) {
        throw new Error('Invalid ATS score range');
      }

      if (analysis.keywordMatch < 0 || analysis.keywordMatch > 100) {
        throw new Error('Invalid keyword match score');
      }

      // Check that issues have proper severity levels
      for (const issue of analysis.issues) {
        if (!Object.values(ATSIssueType).includes(issue.type)) {
          throw new Error(`Invalid issue type: ${issue.type}`);
        }
        if (!Object.values(IssueSeverity).includes(issue.severity)) {
          throw new Error(`Invalid issue severity: ${issue.severity}`);
        }
      }

      return {
        analysisCompleted: true,
        atsScore: analysis.score,
        keywordMatch: analysis.keywordMatch,
        formatCompliance: analysis.formatCompliance,
        readabilityScore: analysis.readabilityScore,
        issuesFound: analysis.issues.length,
        recommendationsGenerated: analysis.recommendations.length,
      };
    });
  }

  async testContentTypes(): Promise<void> {
    await this.runTest('Content Type Coverage', async () => {
      const contentTypes = Object.values(ContentType);
      const testedTypes = [];
      const failedTypes = [];

      for (const contentType of contentTypes) {
        try {
          const request = {
            type: contentType,
            context: {
              experienceLevel: ExperienceLevel.MID_LEVEL,
              tone: ContentTone.PROFESSIONAL,
              currentRole: 'Software Engineer',
              industry: 'Technology',
            },
            options: {
              length: ContentLength.SHORT,
              style: ContentStyle.PARAGRAPH,
              includeKeywords: true,
              atsOptimized: true,
              industrySpecific: false,
              creativityLevel: 0.5,
              maxSuggestions: 1,
              language: 'en',
            },
            userId: 'test-user-id',
          };

          const response = await this.contentGenerator.generateContent(request);
          
          if (response.suggestions.length > 0) {
            testedTypes.push(contentType);
          } else {
            failedTypes.push(contentType);
          }
        } catch (error) {
          failedTypes.push(contentType);
        }
      }

      if (failedTypes.length > 0) {
        throw new Error(`Failed to generate content for types: ${failedTypes.join(', ')}`);
      }

      return {
        totalContentTypes: contentTypes.length,
        successfullyTested: testedTypes.length,
        failedTypes: failedTypes.length,
        coveragePercentage: (testedTypes.length / contentTypes.length) * 100,
      };
    });
  }

  async testExperienceLevels(): Promise<void> {
    await this.runTest('Experience Level Adaptation', async () => {
      const experienceLevels = Object.values(ExperienceLevel);
      const results = [];

      for (const level of experienceLevels) {
        const request = {
          type: ContentType.PROFESSIONAL_SUMMARY,
          context: {
            experienceLevel: level,
            tone: ContentTone.PROFESSIONAL,
            currentRole: 'Software Engineer',
            industry: 'Technology',
          },
          options: {
            length: ContentLength.MEDIUM,
            style: ContentStyle.PARAGRAPH,
            includeKeywords: true,
            atsOptimized: true,
            industrySpecific: true,
            creativityLevel: 0.7,
            maxSuggestions: 1,
            language: 'en',
          },
          userId: 'test-user-id',
        };

        const response = await this.contentGenerator.generateContent(request);
        
        if (response.suggestions.length === 0) {
          throw new Error(`No content generated for experience level: ${level}`);
        }

        results.push({
          level,
          contentLength: response.suggestions[0].content.length,
          confidence: response.suggestions[0].confidence,
          atsScore: response.suggestions[0].atsScore,
        });
      }

      return {
        experienceLevelsCount: experienceLevels.length,
        allLevelsGenerated: true,
        results,
        averageConfidence: results.reduce((sum, r) => sum + r.confidence, 0) / results.length,
      };
    });
  }

  async testErrorHandling(): Promise<void> {
    await this.runTest('Error Handling & Edge Cases', async () => {
      const errorCases = [];

      // Test invalid content type
      try {
        await this.contentGenerator.generateContent({
          type: 'INVALID_TYPE' as ContentType,
          context: { experienceLevel: ExperienceLevel.MID_LEVEL },
          options: {
            length: ContentLength.MEDIUM,
            style: ContentStyle.PARAGRAPH,
            includeKeywords: true,
            atsOptimized: true,
            industrySpecific: true,
            creativityLevel: 0.7,
            maxSuggestions: 1,
            language: 'en',
          },
          userId: 'test-user-id',
        });
        errorCases.push('Invalid content type should fail');
      } catch (error) {
        // Expected to fail
      }

      // Test empty context
      try {
        await this.contentGenerator.generateContent({
          type: ContentType.PROFESSIONAL_SUMMARY,
          context: {} as any,
          options: {
            length: ContentLength.MEDIUM,
            style: ContentStyle.PARAGRAPH,
            includeKeywords: true,
            atsOptimized: true,
            industrySpecific: true,
            creativityLevel: 0.7,
            maxSuggestions: 1,
            language: 'en',
          },
          userId: 'test-user-id',
        });
        errorCases.push('Empty context should fail');
      } catch (error) {
        // Expected to fail
      }

      // Test invalid creativity level
      try {
        const contextResult = generationOptionsSchema.safeParse({
          length: ContentLength.MEDIUM,
          style: ContentStyle.PARAGRAPH,
          includeKeywords: true,
          atsOptimized: true,
          industrySpecific: true,
          creativityLevel: 2.0, // Invalid - should be 0-1
          maxSuggestions: 1,
          language: 'en',
        });
        
        if (contextResult.success) {
          errorCases.push('Invalid creativity level should fail validation');
        }
      } catch (error) {
        // Expected to fail
      }

      if (errorCases.length > 0) {
        throw new Error(`Error handling failed: ${errorCases.join(', ')}`);
      }

      return {
        errorHandlingWorking: true,
        invalidInputsRejected: true,
        validationWorking: true,
      };
    });
  }

  async testComponentStructure(): Promise<void> {
    await this.runTest('AI Component Structure', async () => {
      const fs = require('fs');
      const path = require('path');

      const aiComponentsDir = join(__dirname, '../apps/web/src/components/ai');
      const requiredComponents = [
        'content-generator-panel.tsx',
        'content-suggestions.tsx',
        'ats-analysis-panel.tsx',
      ];

      const missingComponents = [];
      for (const component of requiredComponents) {
        const componentPath = join(aiComponentsDir, component);
        if (!fs.existsSync(componentPath)) {
          missingComponents.push(component);
        }
      }

      if (missingComponents.length > 0) {
        throw new Error(`Missing AI components: ${missingComponents.join(', ')}`);
      }

      // Check AI services
      const aiServicesDir = join(__dirname, '../apps/web/src/lib/ai');
      const requiredServices = [
        'content-generator.ts',
        'ats-optimizer.ts',
      ];

      const missingServices = [];
      for (const service of requiredServices) {
        const servicePath = join(aiServicesDir, service);
        if (!fs.existsSync(servicePath)) {
          missingServices.push(service);
        }
      }

      if (missingServices.length > 0) {
        throw new Error(`Missing AI services: ${missingServices.join(', ')}`);
      }

      return {
        totalComponents: requiredComponents.length,
        totalServices: requiredServices.length,
        allComponentsPresent: true,
        allServicesPresent: true,
      };
    });
  }

  async testAPIEndpoints(): Promise<void> {
    await this.runTest('AI API Endpoints', async () => {
      const fs = require('fs');
      const path = require('path');

      const apiDir = join(__dirname, '../apps/web/src/app/api/ai');
      const requiredEndpoints = [
        'generate-content/route.ts',
        'analyze-ats/route.ts',
      ];

      const missingEndpoints = [];
      for (const endpoint of requiredEndpoints) {
        const endpointPath = join(apiDir, endpoint);
        if (!fs.existsSync(endpointPath)) {
          missingEndpoints.push(endpoint);
        }
      }

      if (missingEndpoints.length > 0) {
        throw new Error(`Missing AI API endpoints: ${missingEndpoints.join(', ')}`);
      }

      // Check if endpoints have proper HTTP methods
      const generateContentFile = join(apiDir, 'generate-content/route.ts');
      const generateContentContent = fs.readFileSync(generateContentFile, 'utf8');

      if (!generateContentContent.includes('export async function POST')) {
        throw new Error('generate-content endpoint missing POST method');
      }

      const analyzeATSFile = join(apiDir, 'analyze-ats/route.ts');
      const analyzeATSContent = fs.readFileSync(analyzeATSFile, 'utf8');

      if (!analyzeATSContent.includes('export async function POST')) {
        throw new Error('analyze-ats endpoint missing POST method');
      }

      return {
        totalEndpoints: requiredEndpoints.length,
        allEndpointsPresent: true,
        allMethodsPresent: true,
      };
    });
  }

  async runAllTests(): Promise<void> {
    console.log('🤖 Starting AI Content Generation & ATS Optimization Tests...\n');

    await this.testAIDataModels();
    await this.testContentGeneration();
    await this.testATSOptimization();
    await this.testContentTypes();
    await this.testExperienceLevels();
    await this.testErrorHandling();
    await this.testComponentStructure();
    await this.testAPIEndpoints();

    await this.printSummary();
  }

  private async printSummary(): Promise<void> {
    console.log('\n📊 AI System Test Summary');
    console.log('==========================');

    const passed = this.results.filter(r => r.status === 'PASS').length;
    const failed = this.results.filter(r => r.status === 'FAIL').length;
    const total = this.results.length;

    console.log(`Total Tests: ${total}`);
    console.log(`Passed: ${passed}`);
    console.log(`Failed: ${failed}`);
    console.log(`Success Rate: ${((passed / total) * 100).toFixed(1)}%`);

    if (failed > 0) {
      console.log('\n❌ Failed Tests:');
      this.results
        .filter(r => r.status === 'FAIL')
        .forEach(r => {
          console.log(`  - ${r.name}: ${r.error}`);
        });
    }

    const totalTime = this.results.reduce((sum, r) => sum + r.duration, 0);
    console.log(`\nTotal Execution Time: ${totalTime}ms`);

    if (failed === 0) {
      console.log('\n🎉 All AI system tests passed! AI features are ready for production.');
    } else {
      console.log('\n⚠️  Some tests failed. Please check your AI system implementation.');
      process.exit(1);
    }
  }
}

// Run the tests
async function main() {
  const tester = new AISystemTester();
  await tester.runAllTests();
}

if (require.main === module) {
  main().catch(console.error);
}
