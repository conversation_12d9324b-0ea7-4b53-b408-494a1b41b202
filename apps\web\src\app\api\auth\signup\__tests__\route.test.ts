import { describe, it, expect, beforeAll, afterAll, beforeEach, jest } from '@jest/globals';
import { POST, GET } from '../route';
import { connectDB, disconnectDB, cleanupDatabase, prisma } from '@careercraft/database';
import { NextRequest } from 'next/server';

// Mock the rate limiter
jest.mock('@/lib/rate-limit', () => ({
  rateLimit: () => ({
    check: jest.fn().mockResolvedValue({
      success: true,
      limit: 5,
      remaining: 4,
      reset: Date.now() + 60000,
    }),
  }),
}));

// Mock bcrypt
jest.mock('bcryptjs', () => ({
  hash: jest.fn().mockResolvedValue('hashedPassword123'),
  compare: jest.fn().mockResolvedValue(true),
}));

describe('/api/auth/signup', () => {
  beforeAll(async () => {
    await connectDB();
  });

  afterAll(async () => {
    await cleanupDatabase();
    await disconnectDB();
  });

  beforeEach(async () => {
    await cleanupDatabase();
    jest.clearAllMocks();
  });

  describe('POST /api/auth/signup', () => {
    const createRequest = (body: any, ip?: string) => {
      return {
        json: async () => body,
        ip: ip || '127.0.0.1',
      } as NextRequest;
    };

    it('should create user successfully with valid data', async () => {
      const validUserData = {
        name: 'Test User',
        email: '<EMAIL>',
        password: 'Password123',
      };

      const request = createRequest(validUserData);
      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(201);
      expect(data.success).toBe(true);
      expect(data.message).toBe('Account created successfully');
      expect(data.user).toBeDefined();
      expect(data.user.email).toBe(validUserData.email);
      expect(data.user.name).toBe(validUserData.name);
      expect(data.user.id).toBeDefined();

      // Verify user was created in database
      const dbUser = await prisma.user.findUnique({
        where: { email: validUserData.email },
        include: {
          accounts: true,
          profiles: true,
        },
      });

      expect(dbUser).toBeDefined();
      expect(dbUser?.accounts).toHaveLength(1);
      expect(dbUser?.profiles).toHaveLength(1);
    });

    it('should return validation errors for invalid data', async () => {
      const invalidUserData = {
        name: 'T', // Too short
        email: 'invalid-email', // Invalid format
        password: '123', // Too short
      };

      const request = createRequest(invalidUserData);
      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe('Validation failed');
      expect(data.details).toBeDefined();
      expect(Array.isArray(data.details)).toBe(true);
      expect(data.details.length).toBeGreaterThan(0);
    });

    it('should reject duplicate email', async () => {
      const userData = {
        name: 'First User',
        email: '<EMAIL>',
        password: 'Password123',
      };

      // Create first user
      const request1 = createRequest(userData);
      const response1 = await POST(request1);
      expect(response1.status).toBe(201);

      // Try to create second user with same email
      const duplicateUserData = {
        name: 'Second User',
        email: '<EMAIL>',
        password: 'Password456',
      };

      const request2 = createRequest(duplicateUserData);
      const response2 = await POST(request2);
      const data2 = await response2.json();

      expect(response2.status).toBe(409);
      expect(data2.error).toBe('An account with this email already exists');
    });

    it('should handle rate limiting', async () => {
      // Mock rate limiter to return failure
      const { rateLimit } = require('@/lib/rate-limit');
      const mockRateLimit = rateLimit();
      mockRateLimit.check.mockResolvedValueOnce({
        success: false,
        limit: 5,
        remaining: 0,
        reset: Date.now() + 60000,
      });

      const userData = {
        name: 'Rate Limited User',
        email: '<EMAIL>',
        password: 'Password123',
      };

      const request = createRequest(userData);
      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(429);
      expect(data.error).toContain('Too many signup attempts');
      expect(data.retryAfter).toBeDefined();
    });

    it('should include rate limit headers in response', async () => {
      const userData = {
        name: 'Header Test User',
        email: '<EMAIL>',
        password: 'Password123',
      };

      const request = createRequest(userData);
      const response = await POST(request);

      expect(response.headers.get('X-RateLimit-Limit')).toBe('5');
      expect(response.headers.get('X-RateLimit-Remaining')).toBe('4');
      expect(response.headers.get('X-RateLimit-Reset')).toBeDefined();
    });

    it('should handle missing required fields', async () => {
      const incompleteData = {
        name: 'Test User',
        // Missing email and password
      };

      const request = createRequest(incompleteData);
      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe('Validation failed');
      expect(data.details).toBeDefined();
    });

    it('should handle malformed JSON', async () => {
      const request = {
        json: async () => {
          throw new Error('Invalid JSON');
        },
        ip: '127.0.0.1',
      } as NextRequest;

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.error).toBe('Failed to create account. Please try again.');
    });

    it('should handle database errors gracefully', async () => {
      // Mock createUser to throw a database error
      const originalCreateUser = require('@/lib/auth').createUser;
      const mockCreateUser = jest.fn().mockRejectedValue(new Error('Database connection failed'));
      
      // Replace the function temporarily
      require('@/lib/auth').createUser = mockCreateUser;

      const userData = {
        name: 'DB Error User',
        email: '<EMAIL>',
        password: 'Password123',
      };

      const request = createRequest(userData);
      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.error).toBe('Failed to create account. Please try again.');

      // Restore original function
      require('@/lib/auth').createUser = originalCreateUser;
    });

    it('should validate email format strictly', async () => {
      const invalidEmails = [
        'plainaddress',
        '@missingdomain.com',
        'missing@.com',
        'missing@domain',
        'spaces @domain.com',
        'special!@domain.com',
      ];

      for (const email of invalidEmails) {
        const userData = {
          name: 'Test User',
          email,
          password: 'Password123',
        };

        const request = createRequest(userData);
        const response = await POST(request);
        const data = await response.json();

        expect(response.status).toBe(400);
        expect(data.error).toBe('Validation failed');
      }
    });

    it('should validate password requirements', async () => {
      const weakPasswords = [
        '123456', // Too short
        'password', // No uppercase or numbers
        'PASSWORD', // No lowercase or numbers
        'Password', // No numbers
        '12345678', // No letters
      ];

      for (const password of weakPasswords) {
        const userData = {
          name: 'Test User',
          email: '<EMAIL>',
          password,
        };

        const request = createRequest(userData);
        const response = await POST(request);
        const data = await response.json();

        expect(response.status).toBe(400);
        expect(data.error).toBe('Validation failed');
      }
    });

    it('should accept strong passwords', async () => {
      const strongPasswords = [
        'Password123',
        'MyStr0ngP@ssw0rd',
        'C0mpl3xP@ssw0rd!',
        'Secure123Password',
      ];

      for (let i = 0; i < strongPasswords.length; i++) {
        const userData = {
          name: 'Test User',
          email: `strong${i}@example.com`,
          password: strongPasswords[i],
        };

        const request = createRequest(userData);
        const response = await POST(request);

        expect(response.status).toBe(201);
      }
    });

    it('should handle different IP addresses for rate limiting', async () => {
      const userData = {
        name: 'IP Test User',
        email: '<EMAIL>',
        password: 'Password123',
      };

      // Test with different IP addresses
      const ips = ['***********', '********', '**********'];

      for (let i = 0; i < ips.length; i++) {
        const userDataWithUniqueEmail = {
          ...userData,
          email: `iptest${i}@example.com`,
        };

        const request = createRequest(userDataWithUniqueEmail, ips[i]);
        const response = await POST(request);

        expect(response.status).toBe(201);
      }
    });
  });

  describe('GET /api/auth/signup', () => {
    it('should return method not allowed for GET requests', async () => {
      const response = await GET();
      const data = await response.json();

      expect(response.status).toBe(405);
      expect(data.error).toBe('Method not allowed');
    });
  });

  describe('Security Headers', () => {
    it('should include security headers in responses', async () => {
      const userData = {
        name: 'Security Test User',
        email: '<EMAIL>',
        password: 'Password123',
      };

      const request = createRequest(userData);
      const response = await POST(request);

      // Check for rate limiting headers
      expect(response.headers.get('X-RateLimit-Limit')).toBeDefined();
      expect(response.headers.get('X-RateLimit-Remaining')).toBeDefined();
      expect(response.headers.get('X-RateLimit-Reset')).toBeDefined();
    });
  });

  describe('Response Format', () => {
    it('should return consistent response format for success', async () => {
      const userData = {
        name: 'Format Test User',
        email: '<EMAIL>',
        password: 'Password123',
      };

      const request = createRequest(userData);
      const response = await POST(request);
      const data = await response.json();

      expect(data).toHaveProperty('success');
      expect(data).toHaveProperty('message');
      expect(data).toHaveProperty('user');
      expect(data.user).toHaveProperty('id');
      expect(data.user).toHaveProperty('email');
      expect(data.user).toHaveProperty('name');
      expect(data.user).not.toHaveProperty('password');
    });

    it('should return consistent response format for errors', async () => {
      const invalidData = {
        name: 'T',
        email: 'invalid',
        password: '123',
      };

      const request = createRequest(invalidData);
      const response = await POST(request);
      const data = await response.json();

      expect(data).toHaveProperty('error');
      expect(data).toHaveProperty('details');
      expect(Array.isArray(data.details)).toBe(true);
    });
  });
});
