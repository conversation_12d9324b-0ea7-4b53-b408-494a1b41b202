#!/usr/bin/env node

/**
 * Chrome Extension Packaging Script
 * 
 * Creates a Chrome Web Store ready package
 */

const fs = require('fs')
const path = require('path')
const archiver = require('archiver')

console.log('📦 Packaging Chrome Extension...')

const sourceDir = path.join(__dirname, '../dist/chrome')
const outputDir = path.join(__dirname, '../packages')
const outputFile = path.join(outputDir, 'careercraft-autofill-chrome-v1.0.0.zip')

// Ensure output directory exists
if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir, { recursive: true })
}

// Create zip package
const output = fs.createWriteStream(outputFile)
const archive = archiver('zip', { zlib: { level: 9 } })

output.on('close', () => {
  const sizeKB = (archive.pointer() / 1024).toFixed(2)
  console.log(`✅ Chrome package created: ${sizeKB} KB`)
  console.log(`📁 Location: ${outputFile}`)
  
  // Validate package
  validateChromePackage()
})

archive.on('error', (err) => {
  console.error('❌ Packaging failed:', err)
  process.exit(1)
})

archive.pipe(output)

// Add all files from dist/chrome
archive.directory(sourceDir, false)
archive.finalize()

function validateChromePackage() {
  console.log('\n🔍 Validating Chrome package...')
  
  // Check required files
  const requiredFiles = [
    'manifest.json',
    'background/background.js',
    'content/content.js',
    'popup/popup.html',
    'popup/popup.js',
    'icons/icon-16.png',
    'icons/icon-48.png',
    'icons/icon-128.png'
  ]
  
  let allValid = true
  
  requiredFiles.forEach(file => {
    const filePath = path.join(sourceDir, file)
    if (fs.existsSync(filePath)) {
      console.log(`✅ ${file}`)
    } else {
      console.log(`❌ ${file} - MISSING`)
      allValid = false
    }
  })
  
  // Check manifest
  const manifestPath = path.join(sourceDir, 'manifest.json')
  if (fs.existsSync(manifestPath)) {
    const manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf8'))
    
    console.log('\n📋 Manifest Validation:')
    console.log(`✅ Version: ${manifest.version}`)
    console.log(`✅ Manifest Version: ${manifest.manifest_version}`)
    console.log(`✅ Name: ${manifest.name}`)
    console.log(`✅ Permissions: ${manifest.permissions?.length || 0}`)
    
    if (manifest.manifest_version !== 3) {
      console.log('⚠️  Warning: Chrome Web Store prefers Manifest V3')
    }
  }
  
  if (allValid) {
    console.log('\n🎉 Chrome package validation successful!')
    console.log('\n📋 Next Steps:')
    console.log('1. Go to Chrome Web Store Developer Dashboard')
    console.log('2. Click "Add new item"')
    console.log('3. Upload the package file')
    console.log('4. Fill in store listing details')
    console.log('5. Submit for review')
  } else {
    console.log('\n❌ Package validation failed!')
    process.exit(1)
  }
}
