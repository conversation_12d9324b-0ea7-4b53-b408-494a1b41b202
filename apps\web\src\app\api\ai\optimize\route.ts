import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { aiService } from '@/lib/ai/openai'
import { z } from 'zod'

const optimizeRequestSchema = z.object({
  content: z.string().min(1, 'Content is required'),
  jobDescription: z.string().optional(),
  targetRole: z.string().optional(),
  industry: z.string().optional(),
  experienceLevel: z.enum(['entry', 'mid', 'senior', 'executive']).optional(),
})

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Parse and validate request body
    const body = await request.json()
    const validatedData = optimizeRequestSchema.parse(body)

    // Check if user has AI credits or subscription
    // This would typically check a database for user's AI usage limits
    const hasAIAccess = await checkUserAIAccess(session.user.id)
    if (!hasAIAccess) {
      return NextResponse.json(
        { error: 'AI optimization requires a premium subscription' },
        { status: 403 }
      )
    }

    // Call AI service to optimize content
    const optimizedContent = await aiService.optimizeResumeContent({
      content: validatedData.content,
      jobDescription: validatedData.jobDescription,
      targetRole: validatedData.targetRole,
      industry: validatedData.industry,
      experienceLevel: validatedData.experienceLevel,
    })

    // Log AI usage for billing/analytics
    await logAIUsage(session.user.id, 'optimize', {
      inputLength: validatedData.content.length,
      outputLength: optimizedContent.length,
    })

    return NextResponse.json({
      optimizedContent,
      originalLength: validatedData.content.length,
      optimizedLength: optimizedContent.length,
    })

  } catch (error) {
    console.error('Error in AI optimization:', error)

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 }
      )
    }

    if (error instanceof Error) {
      // Handle specific AI service errors
      if (error.message.includes('API key')) {
        return NextResponse.json(
          { error: 'AI service configuration error' },
          { status: 500 }
        )
      }

      if (error.message.includes('rate limit')) {
        return NextResponse.json(
          { error: 'AI service rate limit exceeded. Please try again later.' },
          { status: 429 }
        )
      }
    }

    return NextResponse.json(
      { error: 'Failed to optimize content' },
      { status: 500 }
    )
  }
}

async function checkUserAIAccess(userId: string): Promise<boolean> {
  // Mock implementation - replace with actual database check
  // This would check user's subscription status, AI credits, etc.
  return true
}

async function logAIUsage(
  userId: string, 
  operation: string, 
  metadata: Record<string, any>
): Promise<void> {
  // Mock implementation - replace with actual logging
  console.log('AI Usage:', { userId, operation, metadata, timestamp: new Date() })
}
