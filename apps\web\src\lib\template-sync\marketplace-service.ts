/**
 * Template Marketplace Service
 * 
 * Handles template marketplace operations, discovery, and transactions
 */

import { prisma } from '@/lib/db'
import { z } from 'zod'

// Marketplace schemas
export const TemplateSearchSchema = z.object({
  query: z.string().optional(),
  category: z.string().optional(),
  tags: z.array(z.string()).optional(),
  priceRange: z.object({
    min: z.number().min(0).optional(),
    max: z.number().min(0).optional()
  }).optional(),
  difficulty: z.enum(['beginner', 'intermediate', 'advanced']).optional(),
  sortBy: z.enum(['popular', 'newest', 'rating', 'price_low', 'price_high']).default('popular'),
  limit: z.number().min(1).max(100).default(20),
  offset: z.number().min(0).default(0)
})

export const PublishTemplateSchema = z.object({
  templateId: z.string(),
  price: z.number().min(0),
  isFeatured: z.boolean().default(false)
})

export const TemplateReviewSchema = z.object({
  templateId: z.string(),
  rating: z.number().min(1).max(5),
  reviewText: z.string().optional(),
  isVerifiedPurchase: z.boolean().default(false)
})

export const TemplateCollectionSchema = z.object({
  name: z.string().min(1).max(100),
  description: z.string().optional(),
  templateIds: z.array(z.string()),
  isPublic: z.boolean().default(false)
})

export interface MarketplaceTemplate {
  id: string
  name: string
  description?: string
  category?: string
  tags: string[]
  preview?: string
  thumbnail?: string
  difficulty?: string
  price: number
  rating: number
  reviewCount: number
  downloadCount: number
  isFeatured: boolean
  seller: {
    id: string
    name?: string
    email: string
  }
  createdAt: Date
  updatedAt: Date
}

export interface TemplateAnalytics {
  views: number
  downloads: number
  purchases: number
  revenue: number
  rating: number
  reviewCount: number
  conversionRate: number
  popularDevices: { device: string; count: number }[]
  popularCountries: { country: string; count: number }[]
  recentActivity: { date: Date; action: string; count: number }[]
}

export class TemplateMarketplaceService {
  /**
   * Search templates in marketplace
   */
  async searchTemplates(criteria: z.infer<typeof TemplateSearchSchema>): Promise<{
    templates: MarketplaceTemplate[]
    total: number
    hasMore: boolean
  }> {
    try {
      const validatedCriteria = TemplateSearchSchema.parse(criteria)
      
      const where: any = {
        marketplaceListing: {
          status: 'approved'
        },
        isActive: true
      }

      // Apply search filters
      if (validatedCriteria.query) {
        where.OR = [
          { name: { contains: validatedCriteria.query, mode: 'insensitive' } },
          { description: { contains: validatedCriteria.query, mode: 'insensitive' } }
        ]
      }

      if (validatedCriteria.category) {
        where.category = validatedCriteria.category
      }

      if (validatedCriteria.tags && validatedCriteria.tags.length > 0) {
        where.tags = {
          contains: validatedCriteria.tags.join('|')
        }
      }

      if (validatedCriteria.difficulty) {
        where.difficulty = validatedCriteria.difficulty
      }

      if (validatedCriteria.priceRange) {
        where.marketplaceListing = {
          ...where.marketplaceListing,
          price: {
            ...(validatedCriteria.priceRange.min !== undefined && { gte: validatedCriteria.priceRange.min }),
            ...(validatedCriteria.priceRange.max !== undefined && { lte: validatedCriteria.priceRange.max })
          }
        }
      }

      // Apply sorting
      let orderBy: any = {}
      switch (validatedCriteria.sortBy) {
        case 'popular':
          orderBy = { usageCount: 'desc' }
          break
        case 'newest':
          orderBy = { createdAt: 'desc' }
          break
        case 'rating':
          orderBy = { rating: 'desc' }
          break
        case 'price_low':
          orderBy = { marketplaceListing: { price: 'asc' } }
          break
        case 'price_high':
          orderBy = { marketplaceListing: { price: 'desc' } }
          break
        default:
          orderBy = { usageCount: 'desc' }
      }

      const [templates, total] = await Promise.all([
        prisma.template.findMany({
          where,
          include: {
            marketplaceListing: true,
            _count: {
              select: {
                reviews: true
              }
            }
          },
          orderBy,
          take: validatedCriteria.limit,
          skip: validatedCriteria.offset
        }),
        prisma.template.count({ where })
      ])

      const marketplaceTemplates: MarketplaceTemplate[] = await Promise.all(
        templates.map(async (template) => {
          const seller = await prisma.user.findUnique({
            where: { id: template.createdBy || '' },
            select: { id: true, name: true, email: true }
          })

          return {
            id: template.id,
            name: template.name,
            description: template.description,
            category: template.category,
            tags: template.tags ? JSON.parse(template.tags) : [],
            preview: template.preview,
            thumbnail: template.thumbnail,
            difficulty: template.difficulty,
            price: template.marketplaceListing?.price || 0,
            rating: template.rating,
            reviewCount: template._count.reviews,
            downloadCount: template.marketplaceListing?.downloadCount || 0,
            isFeatured: template.marketplaceListing?.isFeatured || false,
            seller: seller || { id: '', name: 'Unknown', email: '' },
            createdAt: template.createdAt,
            updatedAt: template.updatedAt
          }
        })
      )

      return {
        templates: marketplaceTemplates,
        total,
        hasMore: validatedCriteria.offset + validatedCriteria.limit < total
      }
    } catch (error) {
      console.error('Error searching templates:', error)
      throw new Error('Failed to search templates')
    }
  }

  /**
   * Get featured templates
   */
  async getFeaturedTemplates(limit: number = 10): Promise<MarketplaceTemplate[]> {
    try {
      const templates = await prisma.template.findMany({
        where: {
          marketplaceListing: {
            status: 'approved',
            isFeatured: true
          },
          isActive: true
        },
        include: {
          marketplaceListing: true,
          _count: {
            select: {
              reviews: true
            }
          }
        },
        orderBy: {
          usageCount: 'desc'
        },
        take: limit
      })

      return Promise.all(
        templates.map(async (template) => {
          const seller = await prisma.user.findUnique({
            where: { id: template.createdBy || '' },
            select: { id: true, name: true, email: true }
          })

          return {
            id: template.id,
            name: template.name,
            description: template.description,
            category: template.category,
            tags: template.tags ? JSON.parse(template.tags) : [],
            preview: template.preview,
            thumbnail: template.thumbnail,
            difficulty: template.difficulty,
            price: template.marketplaceListing?.price || 0,
            rating: template.rating,
            reviewCount: template._count.reviews,
            downloadCount: template.marketplaceListing?.downloadCount || 0,
            isFeatured: template.marketplaceListing?.isFeatured || false,
            seller: seller || { id: '', name: 'Unknown', email: '' },
            createdAt: template.createdAt,
            updatedAt: template.updatedAt
          }
        })
      )
    } catch (error) {
      console.error('Error getting featured templates:', error)
      throw new Error('Failed to get featured templates')
    }
  }

  /**
   * Publish template to marketplace
   */
  async publishTemplate(
    publishData: z.infer<typeof PublishTemplateSchema>,
    sellerId: string
  ): Promise<string> {
    try {
      const validatedData = PublishTemplateSchema.parse(publishData)

      // Check if template exists and user owns it
      const template = await prisma.template.findUnique({
        where: { id: validatedData.templateId },
        include: { marketplaceListing: true }
      })

      if (!template) {
        throw new Error('Template not found')
      }

      if (template.createdBy !== sellerId) {
        throw new Error('You can only publish your own templates')
      }

      if (template.marketplaceListing) {
        throw new Error('Template is already published')
      }

      // Create marketplace listing
      const listing = await prisma.templateMarketplace.create({
        data: {
          templateId: validatedData.templateId,
          sellerId,
          price: validatedData.price,
          isFeatured: validatedData.isFeatured,
          status: 'pending' // Requires approval
        }
      })

      return listing.id
    } catch (error) {
      console.error('Error publishing template:', error)
      throw error
    }
  }

  /**
   * Purchase template
   */
  async purchaseTemplate(templateId: string, userId: string): Promise<{
    success: boolean
    purchaseId?: string
    templateData?: any
  }> {
    try {
      const template = await prisma.template.findUnique({
        where: { id: templateId },
        include: {
          marketplaceListing: true
        }
      })

      if (!template || !template.marketplaceListing) {
        throw new Error('Template not found or not available for purchase')
      }

      if (template.marketplaceListing.status !== 'approved') {
        throw new Error('Template is not approved for sale')
      }

      // Check if user already purchased this template
      const existingPurchase = await prisma.templatePurchase.findUnique({
        where: {
          templateMarketplaceId_userId: {
            templateMarketplaceId: template.marketplaceListing.id,
            userId
          }
        }
      })

      if (existingPurchase) {
        // Return template data for existing purchase
        return {
          success: true,
          purchaseId: existingPurchase.id,
          templateData: {
            id: template.id,
            name: template.name,
            description: template.description,
            config: template.config ? JSON.parse(template.config) : null,
            category: template.category,
            tags: template.tags ? JSON.parse(template.tags) : []
          }
        }
      }

      // Create purchase record
      const purchase = await prisma.templatePurchase.create({
        data: {
          templateMarketplaceId: template.marketplaceListing.id,
          userId,
          price: template.marketplaceListing.price,
          paymentMethod: 'free', // Can be extended for actual payments
          paymentId: `free_${Date.now()}`
        }
      })

      // Update download count
      await prisma.templateMarketplace.update({
        where: { id: template.marketplaceListing.id },
        data: {
          downloadCount: {
            increment: 1
          }
        }
      })

      // Track usage analytics
      await this.trackTemplateUsage(templateId, userId, 'download')

      return {
        success: true,
        purchaseId: purchase.id,
        templateData: {
          id: template.id,
          name: template.name,
          description: template.description,
          config: template.config ? JSON.parse(template.config) : null,
          category: template.category,
          tags: template.tags ? JSON.parse(template.tags) : []
        }
      }
    } catch (error) {
      console.error('Error purchasing template:', error)
      throw error
    }
  }

  /**
   * Add template review
   */
  async addReview(
    reviewData: z.infer<typeof TemplateReviewSchema>,
    userId: string
  ): Promise<void> {
    try {
      const validatedData = TemplateReviewSchema.parse(reviewData)

      // Check if user purchased the template
      const purchase = await prisma.templatePurchase.findFirst({
        where: {
          userId,
          templateMarketplace: {
            templateId: validatedData.templateId
          }
        }
      })

      const isVerifiedPurchase = !!purchase

      // Create or update review
      await prisma.templateReview.upsert({
        where: {
          templateId_userId: {
            templateId: validatedData.templateId,
            userId
          }
        },
        update: {
          rating: validatedData.rating,
          reviewText: validatedData.reviewText,
          isVerifiedPurchase,
          updatedAt: new Date()
        },
        create: {
          templateId: validatedData.templateId,
          userId,
          rating: validatedData.rating,
          reviewText: validatedData.reviewText,
          isVerifiedPurchase
        }
      })

      // Update template rating
      await this.updateTemplateRating(validatedData.templateId)
    } catch (error) {
      console.error('Error adding review:', error)
      throw error
    }
  }

  /**
   * Get template reviews
   */
  async getTemplateReviews(templateId: string, limit: number = 20, offset: number = 0): Promise<any[]> {
    try {
      const reviews = await prisma.templateReview.findMany({
        where: { templateId },
        include: {
          user: {
            select: {
              id: true,
              name: true
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        },
        take: limit,
        skip: offset
      })

      return reviews.map(review => ({
        id: review.id,
        rating: review.rating,
        reviewText: review.reviewText,
        isVerifiedPurchase: review.isVerifiedPurchase,
        user: review.user,
        createdAt: review.createdAt,
        updatedAt: review.updatedAt
      }))
    } catch (error) {
      console.error('Error getting template reviews:', error)
      throw error
    }
  }

  /**
   * Track template usage analytics
   */
  async trackTemplateUsage(
    templateId: string,
    userId: string | null,
    actionType: string,
    metadata?: any
  ): Promise<void> {
    try {
      await prisma.templateUsageAnalytics.create({
        data: {
          templateId,
          userId,
          actionType,
          deviceType: metadata?.deviceType,
          userAgent: metadata?.userAgent,
          ipAddress: metadata?.ipAddress,
          metadata: metadata ? JSON.stringify(metadata) : null
        }
      })
    } catch (error) {
      console.error('Error tracking template usage:', error)
      // Don't throw error for analytics tracking
    }
  }

  /**
   * Get template analytics
   */
  async getTemplateAnalytics(templateId: string, sellerId: string): Promise<TemplateAnalytics> {
    try {
      // Verify ownership
      const template = await prisma.template.findUnique({
        where: { id: templateId }
      })

      if (!template || template.createdBy !== sellerId) {
        throw new Error('Template not found or access denied')
      }

      const [
        analytics,
        purchases,
        reviews,
        marketplace
      ] = await Promise.all([
        prisma.templateUsageAnalytics.findMany({
          where: { templateId }
        }),
        prisma.templatePurchase.count({
          where: {
            templateMarketplace: {
              templateId
            }
          }
        }),
        prisma.templateReview.findMany({
          where: { templateId }
        }),
        prisma.templateMarketplace.findUnique({
          where: { templateId }
        })
      ])

      const views = analytics.filter(a => a.actionType === 'view').length
      const downloads = analytics.filter(a => a.actionType === 'download').length
      const revenue = marketplace ? purchases * marketplace.price : 0
      const conversionRate = views > 0 ? (downloads / views) * 100 : 0

      const deviceCounts = analytics.reduce((acc, a) => {
        if (a.deviceType) {
          acc[a.deviceType] = (acc[a.deviceType] || 0) + 1
        }
        return acc
      }, {} as Record<string, number>)

      const popularDevices = Object.entries(deviceCounts)
        .map(([device, count]) => ({ device, count }))
        .sort((a, b) => b.count - a.count)
        .slice(0, 5)

      return {
        views,
        downloads,
        purchases,
        revenue,
        rating: template.rating,
        reviewCount: reviews.length,
        conversionRate,
        popularDevices,
        popularCountries: [], // Can be implemented with IP geolocation
        recentActivity: [] // Can be implemented with time-based analytics
      }
    } catch (error) {
      console.error('Error getting template analytics:', error)
      throw error
    }
  }

  /**
   * Update template rating based on reviews
   */
  private async updateTemplateRating(templateId: string): Promise<void> {
    try {
      const reviews = await prisma.templateReview.findMany({
        where: { templateId },
        select: { rating: true }
      })

      if (reviews.length === 0) {
        return
      }

      const averageRating = reviews.reduce((sum, review) => sum + review.rating, 0) / reviews.length

      await prisma.template.update({
        where: { id: templateId },
        data: {
          rating: Math.round(averageRating * 100) / 100, // Round to 2 decimal places
          reviewCount: reviews.length
        }
      })
    } catch (error) {
      console.error('Error updating template rating:', error)
    }
  }

  /**
   * Create template collection
   */
  async createCollection(
    collectionData: z.infer<typeof TemplateCollectionSchema>,
    userId: string
  ): Promise<string> {
    try {
      const validatedData = TemplateCollectionSchema.parse(collectionData)

      const collection = await prisma.templateCollection.create({
        data: {
          name: validatedData.name,
          description: validatedData.description,
          createdBy: userId,
          isPublic: validatedData.isPublic
        }
      })

      // Add templates to collection
      if (validatedData.templateIds.length > 0) {
        await prisma.templateCollectionItem.createMany({
          data: validatedData.templateIds.map((templateId, index) => ({
            collectionId: collection.id,
            templateId,
            orderIndex: index
          }))
        })
      }

      return collection.id
    } catch (error) {
      console.error('Error creating collection:', error)
      throw error
    }
  }

  /**
   * Get user's template collections
   */
  async getUserCollections(userId: string): Promise<any[]> {
    try {
      const collections = await prisma.templateCollection.findMany({
        where: {
          OR: [
            { createdBy: userId },
            { isPublic: true }
          ]
        },
        include: {
          items: {
            include: {
              template: {
                select: {
                  id: true,
                  name: true,
                  preview: true,
                  thumbnail: true
                }
              }
            },
            orderBy: {
              orderIndex: 'asc'
            }
          },
          creator: {
            select: {
              id: true,
              name: true
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        }
      })

      return collections.map(collection => ({
        id: collection.id,
        name: collection.name,
        description: collection.description,
        isPublic: collection.isPublic,
        isOfficial: collection.isOfficial,
        creator: collection.creator,
        templates: collection.items.map(item => item.template),
        createdAt: collection.createdAt,
        updatedAt: collection.updatedAt
      }))
    } catch (error) {
      console.error('Error getting user collections:', error)
      throw error
    }
  }
}

export const templateMarketplaceService = new TemplateMarketplaceService()
