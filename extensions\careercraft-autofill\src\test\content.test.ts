/**
 * Content Script Tests
 * 
 * Comprehensive test suite for the content script
 * covering form detection, field mapping, and autofill functionality.
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
// Note: JSDOM would be imported in actual implementation
// import { JSDOM } from 'jsdom'

// Mock browser APIs
vi.mock('webextension-polyfill', () => ({
  default: {
    runtime: {
      sendMessage: vi.fn(),
      onMessage: {
        addListener: vi.fn()
      }
    }
  }
}))

// Mock dependencies
vi.mock('../lib/form-detection/form-detector', () => ({
  FormDetector: vi.fn().mockImplementation(() => ({
    detectForms: vi.fn(),
    analyzeForm: vi.fn()
  }))
}))

vi.mock('../lib/field-mapping/field-mapper', () => ({
  FieldMapper: vi.fn().mockImplementation(() => ({
    mapField: vi.fn()
  }))
}))

vi.mock('../lib/autofill/autofill-engine', () => ({
  AutofillEngine: vi.fn().mockImplementation(() => ({
    fillForm: vi.fn()
  }))
}))

vi.mock('../lib/ui/ui-overlay', () => ({
  UIOverlay: vi.fn().mockImplementation(() => ({
    showFormDetected: vi.fn(),
    showSuccess: vi.fn(),
    showError: vi.fn(),
    show: vi.fn(),
    hide: vi.fn(),
    destroy: vi.fn()
  }))
}))

vi.mock('../lib/site-adapters/site-adapter', () => ({
  SiteAdapter: vi.fn().mockImplementation(() => ({
    configure: vi.fn(),
    extractMetadata: vi.fn(),
    determineFieldType: vi.fn(),
    classifyForm: vi.fn(),
    extractJobDescription: vi.fn()
  }))
}))

describe('ContentScript', () => {
  let ContentScript: any
  let contentScript: any
  let dom: JSDOM
  let document: Document
  let window: Window

  beforeEach(async () => {
    // Setup DOM environment
    dom = new JSDOM(`
      <!DOCTYPE html>
      <html>
        <head><title>Test Page</title></head>
        <body>
          <form id="job-application-form">
            <input type="text" name="firstName" placeholder="First Name" required />
            <input type="text" name="lastName" placeholder="Last Name" required />
            <input type="email" name="email" placeholder="Email Address" required />
            <input type="tel" name="phone" placeholder="Phone Number" />
            <textarea name="coverLetter" placeholder="Cover Letter"></textarea>
            <input type="file" name="resume" accept=".pdf,.doc,.docx" />
            <button type="submit">Submit Application</button>
          </form>
        </body>
      </html>
    `, { url: 'https://www.linkedin.com/jobs/view/123456' })

    document = dom.window.document
    window = dom.window as unknown as Window

    // Setup global objects
    global.document = document
    global.window = window
    global.Node = window.Node
    global.Element = window.Element
    global.HTMLElement = window.HTMLElement
    global.HTMLFormElement = window.HTMLFormElement
    global.HTMLInputElement = window.HTMLInputElement
    global.HTMLTextAreaElement = window.HTMLTextAreaElement
    global.HTMLSelectElement = window.HTMLSelectElement
    global.MutationObserver = vi.fn().mockImplementation(() => ({
      observe: vi.fn(),
      disconnect: vi.fn()
    }))

    vi.clearAllMocks()

    // Import ContentScript
    const module = await import('../content/content')
    ContentScript = module.ContentScript
    contentScript = new ContentScript()
  })

  afterEach(() => {
    vi.clearAllMocks()
    contentScript?.destroy()
  })

  describe('Initialization', () => {
    it('should initialize successfully', () => {
      expect(contentScript).toBeDefined()
      expect(contentScript.formDetector).toBeDefined()
      expect(contentScript.fieldMapper).toBeDefined()
      expect(contentScript.autofillEngine).toBeDefined()
      expect(contentScript.uiOverlay).toBeDefined()
      expect(contentScript.siteAdapter).toBeDefined()
    })

    it('should setup site adapter for current hostname', async () => {
      expect(contentScript.siteAdapter.configure).toHaveBeenCalledWith('www.linkedin.com')
    })

    it('should setup message listeners', () => {
      expect(contentScript.setupMessageListeners).toBeDefined()
    })

    it('should setup DOM observer', () => {
      expect(global.MutationObserver).toHaveBeenCalled()
    })
  })

  describe('Form Detection', () => {
    it('should detect forms on page load', async () => {
      const mockForms = [document.getElementById('job-application-form') as HTMLFormElement]
      const mockDetectedForm = {
        element: mockForms[0],
        fields: [
          { element: document.querySelector('input[name="firstName"]'), type: 'text', label: 'First Name' }
        ],
        confidence: 0.95,
        type: 'job-application',
        metadata: {
          url: 'https://www.linkedin.com/jobs/view/123456',
          title: 'Test Page',
          source: 'www.linkedin.com'
        }
      }

      contentScript.formDetector.detectForms.mockResolvedValue([mockForms[0]])
      contentScript.analyzeForm = vi.fn().mockResolvedValue(mockDetectedForm)

      const result = await contentScript.startFormDetection()

      expect(result.success).toBe(true)
      expect(contentScript.detectedForms).toHaveLength(1)
      expect(contentScript.uiOverlay.showFormDetected).toHaveBeenCalled()
    })

    it('should ignore forms with low confidence', async () => {
      const mockForms = [document.getElementById('job-application-form') as HTMLFormElement]
      const mockDetectedForm = {
        element: mockForms[0],
        confidence: 0.5, // Low confidence
        type: 'other'
      }

      contentScript.formDetector.detectForms.mockResolvedValue([mockForms[0]])
      contentScript.analyzeForm = vi.fn().mockResolvedValue(mockDetectedForm)

      const result = await contentScript.startFormDetection()

      expect(contentScript.detectedForms).toHaveLength(0)
    })

    it('should handle form detection errors gracefully', async () => {
      contentScript.formDetector.detectForms.mockRejectedValue(new Error('Detection failed'))

      const result = await contentScript.startFormDetection()

      expect(result.success).toBe(false)
      expect(result.error).toBe('Detection failed')
    })
  })

  describe('Form Analysis', () => {
    it('should analyze form metadata correctly', () => {
      const form = document.getElementById('job-application-form') as HTMLFormElement
      const metadata = contentScript.extractFormMetadata(form)

      expect(metadata.url).toBe('https://www.linkedin.com/jobs/view/123456')
      expect(metadata.title).toBe('Test Page')
      expect(metadata.source).toBe('www.linkedin.com')
    })

    it('should analyze form fields correctly', async () => {
      const form = document.getElementById('job-application-form') as HTMLFormElement
      const mockFieldMapping = {
        mappedTo: 'firstName',
        confidence: 0.9,
        dataType: 'string'
      }

      contentScript.fieldMapper.mapField.mockResolvedValue(mockFieldMapping)

      const fields = await contentScript.analyzeFormFields(form)

      expect(fields).toHaveLength(6) // 6 form fields
      expect(contentScript.fieldMapper.mapField).toHaveBeenCalledTimes(6)
    })

    it('should extract field labels correctly', () => {
      const input = document.querySelector('input[name="firstName"]') as HTMLInputElement
      const label = contentScript.extractFieldLabel(input)

      expect(label).toBe('First Name') // From placeholder
    })

    it('should determine field types correctly', () => {
      const emailInput = document.querySelector('input[name="email"]') as HTMLInputElement
      const phoneInput = document.querySelector('input[name="phone"]') as HTMLInputElement
      const fileInput = document.querySelector('input[name="resume"]') as HTMLInputElement
      const textarea = document.querySelector('textarea[name="coverLetter"]') as HTMLTextAreaElement

      expect(contentScript.determineFieldType(emailInput, 'Email', 'Email Address')).toBe('email')
      expect(contentScript.determineFieldType(phoneInput, 'Phone', 'Phone Number')).toBe('tel')
      expect(contentScript.determineFieldType(fileInput, 'Resume', '')).toBe('file')
      expect(contentScript.determineFieldType(textarea, 'Cover Letter', '')).toBe('textarea')
    })
  })

  describe('Form Classification', () => {
    it('should classify job application forms correctly', () => {
      const form = document.getElementById('job-application-form') as HTMLFormElement
      const fields = [
        { mappedTo: 'firstName', confidence: 0.9 },
        { mappedTo: 'email', confidence: 0.9 },
        { mappedTo: 'resume', confidence: 0.95 }
      ]
      const metadata = {
        url: 'https://www.linkedin.com/jobs/view/123456',
        title: 'Software Engineer - Apply Now',
        source: 'www.linkedin.com'
      }

      const { type, confidence } = contentScript.classifyForm(form, fields, metadata)

      expect(type).toBe('job-application')
      expect(confidence).toBeGreaterThan(0.7)
    })

    it('should classify non-job forms correctly', () => {
      // Create a simple contact form
      document.body.innerHTML = `
        <form id="contact-form">
          <input type="text" name="name" placeholder="Name" />
          <input type="email" name="email" placeholder="Email" />
          <textarea name="message" placeholder="Message"></textarea>
        </form>
      `

      const form = document.getElementById('contact-form') as HTMLFormElement
      const fields = [
        { mappedTo: 'fullName', confidence: 0.8 },
        { mappedTo: 'email', confidence: 0.9 }
      ]
      const metadata = {
        url: 'https://www.example.com/contact',
        title: 'Contact Us',
        source: 'www.example.com'
      }

      const { type, confidence } = contentScript.classifyForm(form, fields, metadata)

      expect(type).toBe('contact')
      expect(confidence).toBeLessThan(0.7)
    })
  })

  describe('Message Handling', () => {
    it('should handle START_FORM_DETECTION messages', async () => {
      const message = { type: 'START_FORM_DETECTION', data: { url: window.location.href } }
      
      contentScript.startFormDetection = vi.fn().mockResolvedValue({ success: true })
      
      const response = await contentScript.handleMessage(message, {}, vi.fn())
      
      expect(response.success).toBe(true)
      expect(contentScript.startFormDetection).toHaveBeenCalled()
    })

    it('should handle QUICK_FILL messages', async () => {
      contentScript.detectedForms = [{ /* mock form */ }]
      contentScript.fillForm = vi.fn().mockResolvedValue({ success: true })
      
      const message = { type: 'QUICK_FILL' }
      
      const response = await contentScript.handleMessage(message, {}, vi.fn())
      
      expect(contentScript.fillForm).toHaveBeenCalledWith({ formIndex: 0, customization: true })
    })

    it('should handle FILL_FORM messages', async () => {
      const message = { 
        type: 'FILL_FORM', 
        data: { formIndex: 0, customization: false } 
      }
      
      contentScript.fillForm = vi.fn().mockResolvedValue({ success: true })
      
      const response = await contentScript.handleMessage(message, {}, vi.fn())
      
      expect(contentScript.fillForm).toHaveBeenCalledWith(message.data)
    })

    it('should handle GET_DETECTED_FORMS messages', async () => {
      contentScript.detectedForms = [{ id: 'form-1' }, { id: 'form-2' }]
      
      const message = { type: 'GET_DETECTED_FORMS' }
      
      const response = await contentScript.handleMessage(message, {}, vi.fn())
      
      expect(response.success).toBe(true)
      expect(response.data).toHaveLength(2)
    })

    it('should handle unknown message types', async () => {
      const message = { type: 'UNKNOWN_TYPE' }
      
      const response = await contentScript.handleMessage(message, {}, vi.fn())
      
      expect(response.success).toBe(false)
      expect(response.error).toBe('Unknown message type')
    })
  })

  describe('Autofill Operations', () => {
    it('should fill forms successfully', async () => {
      const mockForm = {
        element: document.getElementById('job-application-form'),
        fields: [
          { mappedTo: 'firstName', element: document.querySelector('input[name="firstName"]') }
        ]
      }
      
      contentScript.detectedForms = [mockForm]
      
      // Mock browser API response
      const browser = await import('webextension-polyfill')
      browser.default.runtime.sendMessage.mockResolvedValue({
        success: true,
        data: { firstName: 'John', email: '<EMAIL>' }
      })
      
      contentScript.autofillEngine.fillForm.mockResolvedValue({ success: true })
      
      const result = await contentScript.fillForm({ formIndex: 0, customization: true })
      
      expect(result.success).toBe(true)
      expect(contentScript.autofillEngine.fillForm).toHaveBeenCalled()
      expect(contentScript.uiOverlay.showSuccess).toHaveBeenCalled()
    })

    it('should handle autofill errors gracefully', async () => {
      const mockForm = { fields: [] }
      contentScript.detectedForms = [mockForm]
      
      const browser = await import('webextension-polyfill')
      browser.default.runtime.sendMessage.mockResolvedValue({
        success: false,
        error: 'Authentication required'
      })
      
      const result = await contentScript.fillForm({ formIndex: 0 })
      
      expect(result.success).toBe(false)
      expect(contentScript.uiOverlay.showError).toHaveBeenCalled()
    })

    it('should handle invalid form index', async () => {
      contentScript.detectedForms = []
      
      const result = await contentScript.fillForm({ formIndex: 0 })
      
      expect(result.success).toBe(false)
      expect(result.error).toBe('Invalid form index')
    })
  })

  describe('Job Description Extraction', () => {
    it('should extract job description using site adapter', () => {
      const mockJobDescription = 'We are looking for a skilled software engineer...'
      contentScript.siteAdapter.extractJobDescription.mockReturnValue(mockJobDescription)
      
      const description = contentScript.extractJobDescription()
      
      expect(description).toBe(mockJobDescription)
      expect(contentScript.siteAdapter.extractJobDescription).toHaveBeenCalledWith(document)
    })

    it('should return empty string when no description found', () => {
      contentScript.siteAdapter.extractJobDescription.mockReturnValue('')
      
      const description = contentScript.extractJobDescription()
      
      expect(description).toBe('')
    })
  })

  describe('Dynamic Content Handling', () => {
    it('should setup DOM observer for dynamic content', () => {
      expect(global.MutationObserver).toHaveBeenCalled()
      
      const observerInstance = (global.MutationObserver as any).mock.instances[0]
      expect(observerInstance.observe).toHaveBeenCalledWith(document.body, {
        childList: true,
        subtree: true
      })
    })

    it('should re-detect forms when new content is added', () => {
      const observerCallback = (global.MutationObserver as any).mock.calls[0][0]
      
      // Mock mutations that add new forms
      const mutations = [{
        type: 'childList',
        addedNodes: [{
          nodeType: Node.ELEMENT_NODE,
          tagName: 'FORM'
        }]
      }]
      
      contentScript.startFormDetection = vi.fn()
      
      // Simulate mutation observer callback
      observerCallback(mutations)
      
      // Should debounce and call startFormDetection
      setTimeout(() => {
        expect(contentScript.startFormDetection).toHaveBeenCalled()
      }, 1100) // After debounce delay
    })
  })

  describe('UI Overlay Integration', () => {
    it('should show overlay when forms are detected', async () => {
      const mockDetectedForms = [{ confidence: 0.9, type: 'job-application' }]
      contentScript.detectedForms = mockDetectedForms
      
      await contentScript.startFormDetection()
      
      expect(contentScript.uiOverlay.showFormDetected).toHaveBeenCalledWith(mockDetectedForms)
    })

    it('should toggle overlay visibility', () => {
      const result1 = contentScript.toggleOverlay(true)
      expect(contentScript.uiOverlay.show).toHaveBeenCalled()
      expect(result1.success).toBe(true)
      
      const result2 = contentScript.toggleOverlay(false)
      expect(contentScript.uiOverlay.hide).toHaveBeenCalled()
      expect(result2.success).toBe(true)
    })
  })

  describe('Settings Management', () => {
    it('should update settings correctly', () => {
      const newSettings = { formDetectionEnabled: false }
      
      const result = contentScript.updateSettings(newSettings)
      
      expect(contentScript.isEnabled).toBe(false)
      expect(result.success).toBe(true)
    })
  })

  describe('Cleanup', () => {
    it('should cleanup resources on destroy', () => {
      contentScript.destroy()
      
      const observerInstance = (global.MutationObserver as any).mock.instances[0]
      expect(observerInstance.disconnect).toHaveBeenCalled()
      expect(contentScript.uiOverlay.destroy).toHaveBeenCalled()
    })
  })

  describe('Error Handling', () => {
    it('should handle form analysis errors gracefully', async () => {
      contentScript.formDetector.detectForms.mockRejectedValue(new Error('Analysis failed'))
      
      const result = await contentScript.startFormDetection()
      
      expect(result.success).toBe(false)
      expect(result.error).toBe('Analysis failed')
    })

    it('should handle field mapping errors gracefully', async () => {
      const form = document.getElementById('job-application-form') as HTMLFormElement
      contentScript.fieldMapper.mapField.mockRejectedValue(new Error('Mapping failed'))
      
      const fields = await contentScript.analyzeFormFields(form)
      
      // Should continue processing other fields even if one fails
      expect(fields).toBeDefined()
    })
  })
})
