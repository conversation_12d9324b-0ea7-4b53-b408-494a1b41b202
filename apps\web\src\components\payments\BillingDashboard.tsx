/**
 * Billing Dashboard Component
 * 
 * Displays subscription status, payment history, and billing management
 */

'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { 
  CreditCard, 
  Calendar, 
  Download, 
  ExternalLink, 
  AlertCircle,
  CheckCircle,
  Clock,
  XCircle
} from 'lucide-react'
import { toast } from 'sonner'
import { format } from 'date-fns'

interface Subscription {
  id: string
  plan: {
    id: string
    name: string
    priceMonthly: number
    priceYearly?: number
  }
  status: string
  currentPeriodStart?: Date
  currentPeriodEnd?: Date
  cancelAtPeriodEnd: boolean
  canceledAt?: Date
  trialStart?: Date
  trialEnd?: Date
}

interface Payment {
  id: string
  amount: number
  currency: string
  status: string
  paymentMethod?: string
  description?: string
  createdAt: Date
}

export function BillingDashboard() {
  const { data: session } = useSession()
  const [subscription, setSubscription] = useState<Subscription | null>(null)
  const [payments, setPayments] = useState<Payment[]>([])
  const [loading, setLoading] = useState(true)
  const [actionLoading, setActionLoading] = useState<string | null>(null)

  useEffect(() => {
    if (session?.user) {
      loadBillingData()
    }
  }, [session])

  const loadBillingData = async () => {
    try {
      const [subscriptionResponse, paymentsResponse] = await Promise.all([
        fetch('/api/payments?action=subscription'),
        fetch('/api/payments?action=payment-history&limit=10')
      ])

      if (subscriptionResponse.ok) {
        const subscriptionData = await subscriptionResponse.json()
        setSubscription(subscriptionData.subscription)
      }

      if (paymentsResponse.ok) {
        const paymentsData = await paymentsResponse.json()
        setPayments(paymentsData.payments || [])
      }
    } catch (error) {
      console.error('Error loading billing data:', error)
      toast.error('Failed to load billing information')
    } finally {
      setLoading(false)
    }
  }

  const handleManageBilling = async () => {
    setActionLoading('billing-portal')
    try {
      const response = await fetch('/api/payments?action=billing-portal')
      if (!response.ok) throw new Error('Failed to create billing portal session')

      const data = await response.json()
      if (data.url) {
        window.open(data.url, '_blank')
      }
    } catch (error) {
      console.error('Error opening billing portal:', error)
      toast.error('Failed to open billing portal')
    } finally {
      setActionLoading(null)
    }
  }

  const handleCancelSubscription = async () => {
    if (!subscription) return

    setActionLoading('cancel')
    try {
      const response = await fetch('/api/payments', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'cancel-subscription',
          cancelAtPeriodEnd: true
        })
      })

      if (!response.ok) throw new Error('Failed to cancel subscription')

      const data = await response.json()
      setSubscription(data.subscription)
      toast.success('Subscription will be canceled at the end of the current period')
    } catch (error) {
      console.error('Error canceling subscription:', error)
      toast.error('Failed to cancel subscription')
    } finally {
      setActionLoading(null)
    }
  }

  const handleReactivateSubscription = async () => {
    if (!subscription) return

    setActionLoading('reactivate')
    try {
      const response = await fetch('/api/payments', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'reactivate-subscription'
        })
      })

      if (!response.ok) throw new Error('Failed to reactivate subscription')

      const data = await response.json()
      setSubscription(data.subscription)
      toast.success('Subscription reactivated successfully')
    } catch (error) {
      console.error('Error reactivating subscription:', error)
      toast.error('Failed to reactivate subscription')
    } finally {
      setActionLoading(null)
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status.toLowerCase()) {
      case 'active':
        return <Badge className="bg-green-100 text-green-800"><CheckCircle className="h-3 w-3 mr-1" />Active</Badge>
      case 'trialing':
        return <Badge className="bg-blue-100 text-blue-800"><Clock className="h-3 w-3 mr-1" />Trial</Badge>
      case 'canceled':
        return <Badge className="bg-red-100 text-red-800"><XCircle className="h-3 w-3 mr-1" />Canceled</Badge>
      case 'past_due':
        return <Badge className="bg-yellow-100 text-yellow-800"><AlertCircle className="h-3 w-3 mr-1" />Past Due</Badge>
      default:
        return <Badge variant="secondary">{status}</Badge>
    }
  }

  const getPaymentStatusBadge = (status: string) => {
    switch (status.toLowerCase()) {
      case 'succeeded':
        return <Badge className="bg-green-100 text-green-800">Paid</Badge>
      case 'failed':
        return <Badge className="bg-red-100 text-red-800">Failed</Badge>
      case 'pending':
        return <Badge className="bg-yellow-100 text-yellow-800">Pending</Badge>
      default:
        return <Badge variant="secondary">{status}</Badge>
    }
  }

  const isTrialActive = () => {
    if (!subscription?.trialEnd) return false
    return new Date() < new Date(subscription.trialEnd)
  }

  const getTrialDaysRemaining = () => {
    if (!subscription?.trialEnd) return 0
    const now = new Date()
    const trialEnd = new Date(subscription.trialEnd)
    const diffTime = trialEnd.getTime() - now.getTime()
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    return Math.max(0, diffDays)
  }

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="space-y-6">
          <Card className="animate-pulse">
            <CardHeader>
              <div className="h-6 bg-gray-200 rounded w-48"></div>
              <div className="h-4 bg-gray-200 rounded w-64"></div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="h-4 bg-gray-200 rounded w-32"></div>
                <div className="h-4 bg-gray-200 rounded w-48"></div>
                <div className="h-10 bg-gray-200 rounded w-40"></div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Billing & Subscription</h1>
        <p className="text-gray-600">Manage your subscription and billing information</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Current Subscription */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CreditCard className="h-5 w-5" />
                Current Subscription
              </CardTitle>
              <CardDescription>
                Your current plan and billing information
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {subscription ? (
                <>
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="text-lg font-semibold">{subscription.plan.name} Plan</h3>
                      <p className="text-gray-600">
                        ${subscription.plan.priceMonthly}/month
                      </p>
                    </div>
                    {getStatusBadge(subscription.status)}
                  </div>

                  {isTrialActive() && (
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                      <div className="flex items-center gap-2 text-blue-800">
                        <Clock className="h-4 w-4" />
                        <span className="font-medium">Free Trial Active</span>
                      </div>
                      <p className="text-blue-700 text-sm mt-1">
                        {getTrialDaysRemaining()} days remaining in your trial
                      </p>
                    </div>
                  )}

                  {subscription.cancelAtPeriodEnd && (
                    <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                      <div className="flex items-center gap-2 text-yellow-800">
                        <AlertCircle className="h-4 w-4" />
                        <span className="font-medium">Subscription Ending</span>
                      </div>
                      <p className="text-yellow-700 text-sm mt-1">
                        Your subscription will end on{' '}
                        {subscription.currentPeriodEnd && 
                          format(new Date(subscription.currentPeriodEnd), 'MMMM d, yyyy')}
                      </p>
                    </div>
                  )}

                  <div className="grid grid-cols-2 gap-4 text-sm">
                    {subscription.currentPeriodStart && (
                      <div>
                        <span className="text-gray-500">Current period started:</span>
                        <p className="font-medium">
                          {format(new Date(subscription.currentPeriodStart), 'MMM d, yyyy')}
                        </p>
                      </div>
                    )}
                    {subscription.currentPeriodEnd && (
                      <div>
                        <span className="text-gray-500">Next billing date:</span>
                        <p className="font-medium">
                          {format(new Date(subscription.currentPeriodEnd), 'MMM d, yyyy')}
                        </p>
                      </div>
                    )}
                  </div>

                  <Separator />

                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      onClick={handleManageBilling}
                      disabled={actionLoading === 'billing-portal'}
                      className="flex items-center gap-2"
                    >
                      <ExternalLink className="h-4 w-4" />
                      {actionLoading === 'billing-portal' ? 'Loading...' : 'Manage Billing'}
                    </Button>

                    {subscription.cancelAtPeriodEnd ? (
                      <Button
                        onClick={handleReactivateSubscription}
                        disabled={actionLoading === 'reactivate'}
                      >
                        {actionLoading === 'reactivate' ? 'Processing...' : 'Reactivate'}
                      </Button>
                    ) : (
                      <Button
                        variant="destructive"
                        onClick={handleCancelSubscription}
                        disabled={actionLoading === 'cancel'}
                      >
                        {actionLoading === 'cancel' ? 'Processing...' : 'Cancel Subscription'}
                      </Button>
                    )}
                  </div>
                </>
              ) : (
                <div className="text-center py-8">
                  <p className="text-gray-600 mb-4">No active subscription</p>
                  <Button onClick={() => window.location.href = '/pricing'}>
                    View Plans
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions */}
        <div>
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button 
                variant="outline" 
                className="w-full justify-start"
                onClick={() => window.location.href = '/pricing'}
              >
                <Calendar className="h-4 w-4 mr-2" />
                Change Plan
              </Button>
              <Button 
                variant="outline" 
                className="w-full justify-start"
                onClick={handleManageBilling}
                disabled={!subscription}
              >
                <CreditCard className="h-4 w-4 mr-2" />
                Update Payment Method
              </Button>
              <Button 
                variant="outline" 
                className="w-full justify-start"
                onClick={handleManageBilling}
                disabled={!subscription}
              >
                <Download className="h-4 w-4 mr-2" />
                Download Invoices
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Payment History */}
        <div className="lg:col-span-3">
          <Card>
            <CardHeader>
              <CardTitle>Payment History</CardTitle>
              <CardDescription>
                Your recent payments and invoices
              </CardDescription>
            </CardHeader>
            <CardContent>
              {payments.length > 0 ? (
                <div className="space-y-4">
                  {payments.map((payment) => (
                    <div key={payment.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center gap-4">
                        <div className="p-2 bg-gray-100 rounded-lg">
                          <CreditCard className="h-4 w-4" />
                        </div>
                        <div>
                          <p className="font-medium">{payment.description || 'Subscription Payment'}</p>
                          <p className="text-sm text-gray-600">
                            {format(new Date(payment.createdAt), 'MMM d, yyyy')} • {payment.paymentMethod}
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-medium">
                          ${payment.amount} {payment.currency.toUpperCase()}
                        </p>
                        {getPaymentStatusBadge(payment.status)}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-gray-600">
                  No payment history available
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
