'use client';

import { useSession, signIn, signOut } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { toast } from 'sonner';

export interface AuthUser {
  id: string;
  email: string;
  name: string;
  image?: string;
  emailVerified?: Date;
}

export interface UseAuthReturn {
  user: AuthUser | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  signIn: (provider?: string, options?: any) => Promise<void>;
  signOut: () => Promise<void>;
  signUp: (data: SignUpData) => Promise<void>;
  signInWithCredentials: (data: SignInData) => Promise<void>;
}

export interface SignUpData {
  name: string;
  email: string;
  password: string;
}

export interface SignInData {
  email: string;
  password: string;
}

export function useAuth(): UseAuthReturn {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);

  const user = session?.user as AuthUser | null;
  const isAuthenticated = !!user;
  const isSessionLoading = status === 'loading';

  const handleSignIn = async (provider?: string, options?: any) => {
    try {
      setIsLoading(true);
      
      const result = await signIn(provider, {
        redirect: false,
        ...options,
      });

      if (result?.error) {
        toast.error('Sign in failed. Please try again.');
        throw new Error(result.error);
      }

      if (result?.ok) {
        toast.success('Signed in successfully!');
        router.push(options?.callbackUrl || '/dashboard');
      }
    } catch (error) {
      console.error('Sign in error:', error);
      toast.error('Sign in failed. Please try again.');
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const handleSignOut = async () => {
    try {
      setIsLoading(true);
      
      await signOut({
        redirect: false,
      });

      toast.success('Signed out successfully!');
      router.push('/');
    } catch (error) {
      console.error('Sign out error:', error);
      toast.error('Sign out failed. Please try again.');
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const handleSignUp = async (data: SignUpData) => {
    try {
      setIsLoading(true);

      const response = await fetch('/api/auth/signup', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      const result = await response.json();

      if (!response.ok) {
        if (response.status === 429) {
          toast.error(`Too many attempts. Please try again in ${result.retryAfter} seconds.`);
        } else if (response.status === 409) {
          toast.error('An account with this email already exists.');
        } else if (response.status === 400 && result.details) {
          const errorMessages = result.details.map((detail: any) => detail.message).join(', ');
          toast.error(`Validation error: ${errorMessages}`);
        } else {
          toast.error(result.error || 'Failed to create account.');
        }
        throw new Error(result.error);
      }

      toast.success('Account created successfully! Please sign in.');
      
      // Automatically sign in after successful signup
      await handleSignInWithCredentials({
        email: data.email,
        password: data.password,
      });
    } catch (error) {
      console.error('Sign up error:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const handleSignInWithCredentials = async (data: SignInData) => {
    try {
      setIsLoading(true);

      const result = await signIn('credentials', {
        email: data.email,
        password: data.password,
        redirect: false,
      });

      if (result?.error) {
        if (result.error === 'CredentialsSignin') {
          toast.error('Invalid email or password.');
        } else {
          toast.error('Sign in failed. Please try again.');
        }
        throw new Error(result.error);
      }

      if (result?.ok) {
        toast.success('Signed in successfully!');
        router.push('/dashboard');
      }
    } catch (error) {
      console.error('Credentials sign in error:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  return {
    user,
    isLoading: isLoading || isSessionLoading,
    isAuthenticated,
    signIn: handleSignIn,
    signOut: handleSignOut,
    signUp: handleSignUp,
    signInWithCredentials: handleSignInWithCredentials,
  };
}

// Higher-order component for protecting routes
export function withAuth<P extends object>(
  Component: React.ComponentType<P>
): React.ComponentType<P> {
  return function AuthenticatedComponent(props: P) {
    const { isAuthenticated, isLoading } = useAuth();
    const router = useRouter();

    if (isLoading) {
      return (
        <div className="flex items-center justify-center min-h-screen">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
        </div>
      );
    }

    if (!isAuthenticated) {
      router.push('/auth/signin');
      return null;
    }

    return <Component {...props} />;
  };
}
