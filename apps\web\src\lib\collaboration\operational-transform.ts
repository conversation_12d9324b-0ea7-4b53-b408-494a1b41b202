/**
 * Operational Transform (OT) Implementation
 * 
 * Handles conflict resolution for concurrent editing operations
 * in real-time collaborative resume editing.
 */

import { z } from 'zod'

// Operation types for different kinds of changes
export const OperationSchema = z.object({
  type: z.enum(['retain', 'insert', 'delete', 'replace']),
  length: z.number().optional(),
  text: z.string().optional(),
  attributes: z.record(z.any()).optional(),
  path: z.array(z.string()).optional()
})

export type Operation = z.infer<typeof OperationSchema>

// Document state representation
export interface DocumentState {
  content: any
  version: number
  operations: Operation[]
}

// Transform result
export interface TransformResult {
  operation1: Operation
  operation2: Operation
}

export class OperationalTransform {
  /**
   * Transform two concurrent operations against each other
   * This is the core of OT - ensuring convergence when operations are applied in different orders
   */
  static transform(op1: Operation, op2: Operation, priority: 'left' | 'right' = 'left'): TransformResult {
    // Handle different operation type combinations
    if (op1.type === 'retain' && op2.type === 'retain') {
      return this.transformRetainRetain(op1, op2)
    }
    
    if (op1.type === 'retain' && op2.type === 'insert') {
      return this.transformRetainInsert(op1, op2)
    }
    
    if (op1.type === 'insert' && op2.type === 'retain') {
      const result = this.transformRetainInsert(op2, op1)
      return { operation1: result.operation2, operation2: result.operation1 }
    }
    
    if (op1.type === 'insert' && op2.type === 'insert') {
      return this.transformInsertInsert(op1, op2, priority)
    }
    
    if (op1.type === 'delete' && op2.type === 'delete') {
      return this.transformDeleteDelete(op1, op2)
    }
    
    if (op1.type === 'delete' && op2.type === 'insert') {
      return this.transformDeleteInsert(op1, op2)
    }
    
    if (op1.type === 'insert' && op2.type === 'delete') {
      const result = this.transformDeleteInsert(op2, op1)
      return { operation1: result.operation2, operation2: result.operation1 }
    }
    
    if (op1.type === 'replace' || op2.type === 'replace') {
      return this.transformReplace(op1, op2)
    }

    // Default case - return operations unchanged
    return { operation1: op1, operation2: op2 }
  }

  private static transformRetainRetain(op1: Operation, op2: Operation): TransformResult {
    const len1 = op1.length || 0
    const len2 = op2.length || 0
    
    if (len1 === len2) {
      return { operation1: op1, operation2: op2 }
    }
    
    if (len1 < len2) {
      return {
        operation1: op1,
        operation2: { ...op2, length: len2 - len1 }
      }
    }
    
    return {
      operation1: { ...op1, length: len1 - len2 },
      operation2: op2
    }
  }

  private static transformRetainInsert(retain: Operation, insert: Operation): TransformResult {
    const retainLength = retain.length || 0
    const insertLength = insert.text?.length || 0
    
    return {
      operation1: { ...retain, length: retainLength + insertLength },
      operation2: insert
    }
  }

  private static transformInsertInsert(op1: Operation, op2: Operation, priority: 'left' | 'right'): TransformResult {
    // When two inserts happen at the same position, use priority to determine order
    if (priority === 'left') {
      return {
        operation1: op1,
        operation2: { ...op2, length: (op2.length || 0) + (op1.text?.length || 0) }
      }
    } else {
      return {
        operation1: { ...op1, length: (op1.length || 0) + (op2.text?.length || 0) },
        operation2: op2
      }
    }
  }

  private static transformDeleteDelete(op1: Operation, op2: Operation): TransformResult {
    const len1 = op1.length || 0
    const len2 = op2.length || 0
    
    if (len1 === len2) {
      // Both delete the same content - result in no-ops
      return {
        operation1: { type: 'retain', length: 0 },
        operation2: { type: 'retain', length: 0 }
      }
    }
    
    if (len1 < len2) {
      return {
        operation1: { type: 'retain', length: 0 },
        operation2: { ...op2, length: len2 - len1 }
      }
    }
    
    return {
      operation1: { ...op1, length: len1 - len2 },
      operation2: { type: 'retain', length: 0 }
    }
  }

  private static transformDeleteInsert(deleteOp: Operation, insertOp: Operation): TransformResult {
    // Insert happens at the position where delete starts
    return {
      operation1: deleteOp,
      operation2: insertOp
    }
  }

  private static transformReplace(op1: Operation, op2: Operation): TransformResult {
    // Replace operations need special handling based on paths
    if (op1.path && op2.path) {
      const path1 = op1.path.join('.')
      const path2 = op2.path.join('.')
      
      if (path1 === path2) {
        // Same path - later operation wins
        return {
          operation1: { type: 'retain', length: 0 },
          operation2: op2
        }
      }
    }
    
    return { operation1: op1, operation2: op2 }
  }

  /**
   * Apply an operation to a document
   */
  static apply(document: any, operation: Operation): any {
    switch (operation.type) {
      case 'retain':
        return document // No change
        
      case 'insert':
        return this.applyInsert(document, operation)
        
      case 'delete':
        return this.applyDelete(document, operation)
        
      case 'replace':
        return this.applyReplace(document, operation)
        
      default:
        return document
    }
  }

  private static applyInsert(document: any, operation: Operation): any {
    if (!operation.path || !operation.text) return document
    
    const result = JSON.parse(JSON.stringify(document))
    const path = operation.path
    
    // Navigate to the target location
    let current = result
    for (let i = 0; i < path.length - 1; i++) {
      if (!current[path[i]]) current[path[i]] = {}
      current = current[path[i]]
    }
    
    const lastKey = path[path.length - 1]
    
    if (Array.isArray(current[lastKey])) {
      // Insert into array
      const index = parseInt(lastKey) || 0
      current[lastKey].splice(index, 0, operation.text)
    } else if (typeof current[lastKey] === 'string') {
      // Insert into string
      const position = operation.length || 0
      const str = current[lastKey]
      current[lastKey] = str.slice(0, position) + operation.text + str.slice(position)
    } else {
      // Set new value
      current[lastKey] = operation.text
    }
    
    return result
  }

  private static applyDelete(document: any, operation: Operation): any {
    if (!operation.path) return document
    
    const result = JSON.parse(JSON.stringify(document))
    const path = operation.path
    
    // Navigate to the target location
    let current = result
    for (let i = 0; i < path.length - 1; i++) {
      if (!current[path[i]]) return document // Path doesn't exist
      current = current[path[i]]
    }
    
    const lastKey = path[path.length - 1]
    const deleteLength = operation.length || 1
    
    if (Array.isArray(current[lastKey])) {
      // Delete from array
      const index = parseInt(lastKey) || 0
      current[lastKey].splice(index, deleteLength)
    } else if (typeof current[lastKey] === 'string') {
      // Delete from string
      const str = current[lastKey]
      const startPos = operation.length || 0
      current[lastKey] = str.slice(0, startPos) + str.slice(startPos + deleteLength)
    } else {
      // Delete property
      delete current[lastKey]
    }
    
    return result
  }

  private static applyReplace(document: any, operation: Operation): any {
    if (!operation.path || operation.text === undefined) return document
    
    const result = JSON.parse(JSON.stringify(document))
    const path = operation.path
    
    // Navigate to the target location
    let current = result
    for (let i = 0; i < path.length - 1; i++) {
      if (!current[path[i]]) current[path[i]] = {}
      current = current[path[i]]
    }
    
    const lastKey = path[path.length - 1]
    current[lastKey] = operation.text
    
    return result
  }

  /**
   * Compose multiple operations into a single operation
   */
  static compose(operations: Operation[]): Operation {
    if (operations.length === 0) {
      return { type: 'retain', length: 0 }
    }
    
    if (operations.length === 1) {
      return operations[0]
    }
    
    // Simplified composition - in practice, this would be more complex
    let result = operations[0]
    
    for (let i = 1; i < operations.length; i++) {
      const op = operations[i]
      
      // Combine operations of the same type
      if (result.type === op.type && result.path?.join('.') === op.path?.join('.')) {
        switch (result.type) {
          case 'insert':
            result.text = (result.text || '') + (op.text || '')
            break
          case 'delete':
            result.length = (result.length || 0) + (op.length || 0)
            break
          case 'retain':
            result.length = (result.length || 0) + (op.length || 0)
            break
        }
      } else {
        // For different types, create a compound operation
        // This is simplified - real implementation would be more sophisticated
        result = op
      }
    }
    
    return result
  }

  /**
   * Create an operation from a change in document state
   */
  static createOperation(oldDoc: any, newDoc: any, path: string[] = []): Operation[] {
    const operations: Operation[] = []
    
    if (oldDoc === newDoc) {
      return operations
    }
    
    if (typeof oldDoc !== typeof newDoc) {
      // Type change - replace
      operations.push({
        type: 'replace',
        path: [...path],
        text: newDoc
      })
      return operations
    }
    
    if (typeof oldDoc === 'string' && typeof newDoc === 'string') {
      // String diff
      const stringOps = this.createStringOperations(oldDoc, newDoc, path)
      operations.push(...stringOps)
    } else if (Array.isArray(oldDoc) && Array.isArray(newDoc)) {
      // Array diff
      const arrayOps = this.createArrayOperations(oldDoc, newDoc, path)
      operations.push(...arrayOps)
    } else if (typeof oldDoc === 'object' && typeof newDoc === 'object') {
      // Object diff
      const objectOps = this.createObjectOperations(oldDoc, newDoc, path)
      operations.push(...objectOps)
    }
    
    return operations
  }

  private static createStringOperations(oldStr: string, newStr: string, path: string[]): Operation[] {
    // Simple string diff - in practice, you'd use a more sophisticated algorithm
    if (oldStr === newStr) return []
    
    return [{
      type: 'replace',
      path: [...path],
      text: newStr
    }]
  }

  private static createArrayOperations(oldArr: any[], newArr: any[], path: string[]): Operation[] {
    const operations: Operation[] = []
    
    // Simple array diff - detect additions and deletions
    if (newArr.length > oldArr.length) {
      // Items added
      for (let i = oldArr.length; i < newArr.length; i++) {
        operations.push({
          type: 'insert',
          path: [...path, i.toString()],
          text: newArr[i]
        })
      }
    } else if (newArr.length < oldArr.length) {
      // Items removed
      operations.push({
        type: 'delete',
        path: [...path],
        length: oldArr.length - newArr.length
      })
    }
    
    // Check for modifications in existing items
    const minLength = Math.min(oldArr.length, newArr.length)
    for (let i = 0; i < minLength; i++) {
      if (oldArr[i] !== newArr[i]) {
        const subOps = this.createOperation(oldArr[i], newArr[i], [...path, i.toString()])
        operations.push(...subOps)
      }
    }
    
    return operations
  }

  private static createObjectOperations(oldObj: any, newObj: any, path: string[]): Operation[] {
    const operations: Operation[] = []
    
    // Check for added/modified properties
    for (const key in newObj) {
      if (!(key in oldObj)) {
        // New property
        operations.push({
          type: 'insert',
          path: [...path, key],
          text: newObj[key]
        })
      } else if (oldObj[key] !== newObj[key]) {
        // Modified property
        const subOps = this.createOperation(oldObj[key], newObj[key], [...path, key])
        operations.push(...subOps)
      }
    }
    
    // Check for deleted properties
    for (const key in oldObj) {
      if (!(key in newObj)) {
        operations.push({
          type: 'delete',
          path: [...path, key]
        })
      }
    }
    
    return operations
  }
}

export default OperationalTransform
