# 🚀 Simple Manual Setup - Copy & Paste Commands

## **If PowerShell Scripts Keep Failing, Use This Manual Approach**

---

## **📋 Prerequisites (Install These First)**

### **1. Install Node.js**
- Go to: https://nodejs.org/
- Download and install LTS version
- Verify: Open Command Prompt and run `node --version`

### **2. Install PostgreSQL**
- Go to: https://www.postgresql.org/download/windows/
- Download and install (remember the password you set)
- Verify: Open Command Prompt and run `psql --version`

### **3. Install Git**
- Go to: https://git-scm.com/download/win
- Download and install
- Verify: Open Command Prompt and run `git --version`

---

## **🏗️ Manual Project Setup**

### **Step 1: Create Project**
Open **Command Prompt** (not PowerShell) and run:

```bash
npx create-next-app@latest careercraft-local --typescript --tailwind --eslint --app --src-dir --import-alias "@/*"
```

### **Step 2: Navigate to Project**
```bash
cd careercraft-local
```

### **Step 3: Install Basic Dependencies**
```bash
npm install @prisma/client prisma next-auth stripe openai lucide-react axios
```

### **Step 4: Install Dev Dependencies**
```bash
npm install -D @types/node @types/react @types/react-dom
```

---

## **🗄️ Database Setup**

### **Step 1: Create Database**
Open **Command Prompt** and run:

```bash
# Connect to PostgreSQL (enter the password you set during installation)
psql -U postgres

# In PostgreSQL prompt, run these commands:
CREATE USER careercraft_user WITH PASSWORD 'local_password';
CREATE DATABASE careercraft_local OWNER careercraft_user;
GRANT ALL PRIVILEGES ON DATABASE careercraft_local TO careercraft_user;
\q
```

### **Step 2: Create Environment File**
In your `careercraft-local` folder, create a file named `.env.local` with this content:

```bash
# Database
DATABASE_URL="postgresql://careercraft_user:local_password@localhost:5432/careercraft_local"

# NextAuth
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-local-secret-key"

# API Keys (add your own later)
OPENAI_API_KEY="sk-your-openai-key"
STRIPE_SECRET_KEY="sk_test_your-stripe-key"

# App Settings
NODE_ENV="development"
NEXT_PUBLIC_APP_URL="http://localhost:3000"
```

### **Step 3: Create Prisma Schema**
Create a folder named `prisma` and inside it create a file named `schema.prisma`:

```prisma
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  name      String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("users")
}
```

### **Step 4: Setup Prisma**
```bash
npx prisma generate
npx prisma db push
```

---

## **🚀 Start the Application**

### **Step 1: Start Development Server**
```bash
npm run dev
```

### **Step 2: Test the Application**
- Open your browser
- Go to: **http://localhost:3000**
- You should see the Next.js welcome page

---

## **✅ Verification**

### **Check if everything works:**

1. **Application loads**: http://localhost:3000 ✅
2. **Database connection**: Run `npx prisma studio` (opens http://localhost:5555) ✅
3. **No errors in terminal**: Check for any red error messages ✅

---

## **🔧 Troubleshooting**

### **If PostgreSQL won't start:**
- Open Windows Services (services.msc)
- Find "postgresql-x64-14" (or similar)
- Right-click → Start

### **If port 3000 is busy:**
```bash
# Kill process using port 3000
netstat -ano | findstr :3000
taskkill /PID <PID_NUMBER> /F
```

### **If database connection fails:**
```bash
# Test database connection
psql -h localhost -p 5432 -U careercraft_user -d careercraft_local
```

### **If npm install fails:**
```bash
# Clear cache and try again
npm cache clean --force
npm install
```

---

## **📝 Next Steps After Setup**

### **1. Add API Keys (Optional)**
Edit `.env.local` and add your real API keys:
- **OpenAI**: Get from https://platform.openai.com/api-keys
- **Stripe**: Get from https://dashboard.stripe.com/test/apikeys

### **2. Test Basic Features**
- Visit http://localhost:3000
- Check database with `npx prisma studio`
- Run tests with `npm test`

### **3. Start Building Features**
Once the basic setup works, we can start adding:
- Epic 8.0: Payment system
- Epic 1.0: Career intelligence
- Epic 6.0: Browser extension
- And all other features

---

## **🎯 Success Criteria**

You'll know the setup worked when:
- ✅ `npm run dev` starts without errors
- ✅ http://localhost:3000 loads the Next.js page
- ✅ `npx prisma studio` opens the database admin
- ✅ No red error messages in the terminal

---

## **🆘 If You're Still Stuck**

### **Minimal Test Setup:**
If everything else fails, just run:

```bash
npx create-next-app@latest test-app --typescript
cd test-app
npm run dev
```

If this works, we know Node.js is fine and we can build from there.

---

## **📞 Getting Help**

If you encounter any errors:
1. **Copy the exact error message**
2. **Note which step failed**
3. **Check the troubleshooting section above**
4. **Let me know what happened**

**This manual approach should work even if all the PowerShell scripts fail!** 🚀
