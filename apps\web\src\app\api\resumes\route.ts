import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@careercraft/database';
import { resumeSchema, createResumeSchema } from '@careercraft/shared/schemas/resume';
import { Resume, ResumeSectionType } from '@careercraft/shared/types/resume';
import { nanoid } from 'nanoid';

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search') || '';

    const skip = (page - 1) * limit;

    const where = {
      userId: session.user.id,
      ...(search && {
        OR: [
          { title: { contains: search, mode: 'insensitive' as const } },
          { personalInfo: { path: ['firstName'], string_contains: search } },
          { personalInfo: { path: ['lastName'], string_contains: search } },
        ],
      }),
    };

    const [resumes, total] = await Promise.all([
      prisma.resume.findMany({
        where,
        skip,
        take: limit,
        orderBy: { updatedAt: 'desc' },
        include: {
          template: true,
        },
      }),
      prisma.resume.count({ where }),
    ]);

    return NextResponse.json({
      resumes,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    });
  } catch (error) {
    console.error('Error fetching resumes:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    
    // Validate the request body
    const validationResult = createResumeSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          error: 'Validation failed',
          details: validationResult.error.errors,
        },
        { status: 400 }
      );
    }

    const resumeData = validationResult.data;

    // Create the resume in the database
    const resume = await prisma.resume.create({
      data: {
        id: nanoid(),
        userId: session.user.id,
        title: resumeData.title,
        templateId: resumeData.templateId,
        personalInfo: resumeData.personalInfo,
        sections: resumeData.sections,
        settings: resumeData.settings,
        metadata: {
          ...resumeData.metadata,
          lastEditedBy: session.user.id,
        },
      },
      include: {
        template: true,
      },
    });

    return NextResponse.json(resume, { status: 201 });
  } catch (error) {
    console.error('Error creating resume:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    
    // Validate the request body
    const validationResult = resumeSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          error: 'Validation failed',
          details: validationResult.error.errors,
        },
        { status: 400 }
      );
    }

    const resumeData = validationResult.data;

    // Check if the resume exists and belongs to the user
    const existingResume = await prisma.resume.findFirst({
      where: {
        id: resumeData.id,
        userId: session.user.id,
      },
    });

    if (!existingResume) {
      return NextResponse.json(
        { error: 'Resume not found' },
        { status: 404 }
      );
    }

    // Update the resume
    const updatedResume = await prisma.resume.update({
      where: { id: resumeData.id },
      data: {
        title: resumeData.title,
        templateId: resumeData.templateId,
        personalInfo: resumeData.personalInfo,
        sections: resumeData.sections,
        settings: resumeData.settings,
        metadata: {
          ...resumeData.metadata,
          lastEditedBy: session.user.id,
          version: existingResume.metadata.version + 1,
        },
        updatedAt: new Date(),
      },
      include: {
        template: true,
      },
    });

    return NextResponse.json(updatedResume);
  } catch (error) {
    console.error('Error updating resume:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
