import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@careercraft/database';
import { exportOptionsSchema } from '@careercraft/shared/schemas/template';
import { nanoid } from 'nanoid';

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { resumeId, templateId, options } = body;

    // Validate export options
    const validationResult = exportOptionsSchema.safeParse(options);
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          error: 'Invalid export options',
          details: validationResult.error.errors,
        },
        { status: 400 }
      );
    }

    // Verify resume ownership
    const resume = await prisma.resume.findFirst({
      where: {
        id: resumeId,
        userId: session.user.id,
      },
    });

    if (!resume) {
      return NextResponse.json(
        { error: 'Resume not found' },
        { status: 404 }
      );
    }

    // Verify template exists
    const template = await prisma.template.findUnique({
      where: { id: templateId },
    });

    if (!template) {
      return NextResponse.json(
        { error: 'Template not found' },
        { status: 404 }
      );
    }

    // Create export job
    const exportJob = await prisma.exportJob.create({
      data: {
        id: nanoid(),
        userId: session.user.id,
        resumeId,
        templateId,
        options: validationResult.data,
        status: 'pending',
        progress: 0,
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours
      },
    });

    // Start export process (in a real implementation, this would be queued)
    processExportJob(exportJob.id);

    return NextResponse.json(exportJob, { status: 201 });
  } catch (error) {
    console.error('Error creating export job:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Mock export processing function
async function processExportJob(jobId: string) {
  try {
    // Update job status to processing
    await prisma.exportJob.update({
      where: { id: jobId },
      data: {
        status: 'processing',
        progress: 10,
      },
    });

    // Simulate export processing time
    await new Promise(resolve => setTimeout(resolve, 3000));

    // Update progress
    await prisma.exportJob.update({
      where: { id: jobId },
      data: {
        progress: 50,
      },
    });

    // Simulate more processing
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Complete the job with a mock download URL
    const downloadUrl = `/api/export/${jobId}/download`;
    
    await prisma.exportJob.update({
      where: { id: jobId },
      data: {
        status: 'completed',
        progress: 100,
        downloadUrl,
        completedAt: new Date(),
      },
    });
  } catch (error) {
    console.error('Export processing error:', error);
    
    // Mark job as failed
    await prisma.exportJob.update({
      where: { id: jobId },
      data: {
        status: 'failed',
        error: 'Export processing failed',
      },
    });
  }
}
