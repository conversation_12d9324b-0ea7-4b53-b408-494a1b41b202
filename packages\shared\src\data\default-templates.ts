/**
 * Default Resume Templates
 * 
 * This file contains the default templates that come with CareerCraft.
 */

import { Template, TemplateCategory } from '../types/template';
import { nanoid } from 'nanoid';

export const defaultTemplates: Omit<Template, 'id' | 'createdAt' | 'updatedAt' | 'createdBy'>[] = [
  {
    name: 'Modern Professional',
    description: 'A clean, modern template perfect for tech professionals and creative roles.',
    category: TemplateCategory.MODERN,
    tags: ['tech', 'creative', 'modern', 'clean'],
    previewImage: '/templates/modern-professional-preview.jpg',
    thumbnailImage: '/templates/modern-professional-thumb.jpg',
    isPremium: false,
    isPopular: true,
    isNew: false,
    difficulty: 'beginner',
    style: {
      fontFamily: 'Inter, sans-serif',
      fontSize: {
        heading1: 32,
        heading2: 20,
        heading3: 16,
        body: 14,
        small: 12,
      },
      fontWeight: {
        normal: 400,
        medium: 500,
        semibold: 600,
        bold: 700,
      },
      lineHeight: {
        tight: 1.2,
        normal: 1.5,
        relaxed: 1.7,
      },
      colors: {
        primary: '#2563eb',
        secondary: '#64748b',
        accent: '#0ea5e9',
        text: '#1e293b',
        textLight: '#64748b',
        background: '#ffffff',
        border: '#e2e8f0',
        divider: '#cbd5e1',
      },
      spacing: {
        xs: 4,
        sm: 8,
        md: 16,
        lg: 24,
        xl: 32,
        xxl: 48,
      },
      layout: {
        pageMargin: {
          top: 40,
          right: 40,
          bottom: 40,
          left: 40,
        },
        sectionSpacing: 32,
        itemSpacing: 16,
        columnGap: 24,
      },
      components: {
        header: {
          alignment: 'left',
          showDivider: true,
        },
        section: {
          titleStyle: 'underline',
          titleCase: 'normal',
          spacing: 'normal',
        },
        list: {
          bulletStyle: 'bullet',
          indentation: 16,
        },
        contact: {
          layout: 'horizontal',
          showIcons: true,
          separator: '•',
        },
      },
    },
    layout: {
      type: 'single-column',
      columns: [
        {
          id: 'main',
          width: 100,
          sections: ['personal_info', 'summary', 'work_experience', 'education', 'skills'],
          order: 0,
        },
      ],
      header: {
        height: 120,
        sections: ['personal_info'],
        style: 'minimal',
      },
    },
    supportedSections: [
      'personal_info',
      'summary',
      'work_experience',
      'education',
      'skills',
      'projects',
      'certifications',
      'languages',
    ],
    requiredSections: ['personal_info', 'work_experience'],
    customizable: {
      colors: true,
      fonts: true,
      layout: false,
      spacing: true,
    },
    usageCount: 15420,
    rating: 4.8,
    reviewCount: 1247,
    isOfficial: true,
  },
  {
    name: 'Classic Executive',
    description: 'A traditional, professional template ideal for executive and senior-level positions.',
    category: TemplateCategory.CLASSIC,
    tags: ['executive', 'traditional', 'professional', 'senior'],
    previewImage: '/templates/classic-executive-preview.jpg',
    thumbnailImage: '/templates/classic-executive-thumb.jpg',
    isPremium: false,
    isPopular: true,
    isNew: false,
    difficulty: 'beginner',
    style: {
      fontFamily: 'Times New Roman, serif',
      fontSize: {
        heading1: 28,
        heading2: 18,
        heading3: 16,
        body: 12,
        small: 10,
      },
      fontWeight: {
        normal: 400,
        medium: 500,
        semibold: 600,
        bold: 700,
      },
      lineHeight: {
        tight: 1.2,
        normal: 1.4,
        relaxed: 1.6,
      },
      colors: {
        primary: '#1f2937',
        secondary: '#6b7280',
        accent: '#374151',
        text: '#111827',
        textLight: '#6b7280',
        background: '#ffffff',
        border: '#d1d5db',
        divider: '#9ca3af',
      },
      spacing: {
        xs: 4,
        sm: 8,
        md: 12,
        lg: 20,
        xl: 28,
        xxl: 40,
      },
      layout: {
        pageMargin: {
          top: 50,
          right: 50,
          bottom: 50,
          left: 50,
        },
        sectionSpacing: 24,
        itemSpacing: 12,
        columnGap: 20,
      },
      components: {
        header: {
          alignment: 'center',
          showDivider: true,
        },
        section: {
          titleStyle: 'border',
          titleCase: 'uppercase',
          spacing: 'compact',
        },
        list: {
          bulletStyle: 'dash',
          indentation: 20,
        },
        contact: {
          layout: 'horizontal',
          showIcons: false,
          separator: '|',
        },
      },
    },
    layout: {
      type: 'single-column',
      columns: [
        {
          id: 'main',
          width: 100,
          sections: ['personal_info', 'summary', 'work_experience', 'education', 'skills'],
          order: 0,
        },
      ],
      header: {
        height: 100,
        sections: ['personal_info'],
        style: 'prominent',
      },
    },
    supportedSections: [
      'personal_info',
      'summary',
      'work_experience',
      'education',
      'skills',
      'certifications',
      'awards',
      'publications',
    ],
    requiredSections: ['personal_info', 'work_experience', 'education'],
    customizable: {
      colors: false,
      fonts: false,
      layout: false,
      spacing: true,
    },
    usageCount: 8930,
    rating: 4.6,
    reviewCount: 743,
    isOfficial: true,
  },
  {
    name: 'Minimal Clean',
    description: 'A minimalist design that focuses on content with plenty of white space.',
    category: TemplateCategory.MINIMAL,
    tags: ['minimal', 'clean', 'simple', 'white-space'],
    previewImage: '/templates/minimal-clean-preview.jpg',
    thumbnailImage: '/templates/minimal-clean-thumb.jpg',
    isPremium: false,
    isPopular: false,
    isNew: true,
    difficulty: 'beginner',
    style: {
      fontFamily: 'Helvetica, Arial, sans-serif',
      fontSize: {
        heading1: 30,
        heading2: 18,
        heading3: 14,
        body: 12,
        small: 10,
      },
      fontWeight: {
        normal: 300,
        medium: 400,
        semibold: 500,
        bold: 600,
      },
      lineHeight: {
        tight: 1.3,
        normal: 1.6,
        relaxed: 1.8,
      },
      colors: {
        primary: '#000000',
        secondary: '#666666',
        accent: '#333333',
        text: '#000000',
        textLight: '#666666',
        background: '#ffffff',
        border: '#eeeeee',
        divider: '#dddddd',
      },
      spacing: {
        xs: 6,
        sm: 12,
        md: 20,
        lg: 32,
        xl: 48,
        xxl: 64,
      },
      layout: {
        pageMargin: {
          top: 60,
          right: 60,
          bottom: 60,
          left: 60,
        },
        sectionSpacing: 48,
        itemSpacing: 20,
        columnGap: 32,
      },
      components: {
        header: {
          alignment: 'left',
          showDivider: false,
        },
        section: {
          titleStyle: 'plain',
          titleCase: 'normal',
          spacing: 'spacious',
        },
        list: {
          bulletStyle: 'none',
          indentation: 0,
        },
        contact: {
          layout: 'vertical',
          showIcons: false,
          separator: '',
        },
      },
    },
    layout: {
      type: 'single-column',
      columns: [
        {
          id: 'main',
          width: 100,
          sections: ['personal_info', 'work_experience', 'education', 'skills'],
          order: 0,
        },
      ],
      header: {
        height: 80,
        sections: ['personal_info'],
        style: 'minimal',
      },
    },
    supportedSections: [
      'personal_info',
      'work_experience',
      'education',
      'skills',
      'projects',
    ],
    requiredSections: ['personal_info', 'work_experience'],
    customizable: {
      colors: true,
      fonts: true,
      layout: true,
      spacing: true,
    },
    usageCount: 3240,
    rating: 4.7,
    reviewCount: 189,
    isOfficial: true,
  },
  {
    name: 'Creative Portfolio',
    description: 'A bold, creative template perfect for designers, artists, and creative professionals.',
    category: TemplateCategory.CREATIVE,
    tags: ['creative', 'design', 'portfolio', 'artistic', 'bold'],
    previewImage: '/templates/creative-portfolio-preview.jpg',
    thumbnailImage: '/templates/creative-portfolio-thumb.jpg',
    isPremium: true,
    isPopular: false,
    isNew: true,
    difficulty: 'intermediate',
    style: {
      fontFamily: 'Montserrat, sans-serif',
      fontSize: {
        heading1: 36,
        heading2: 22,
        heading3: 18,
        body: 14,
        small: 12,
      },
      fontWeight: {
        normal: 400,
        medium: 500,
        semibold: 600,
        bold: 800,
      },
      lineHeight: {
        tight: 1.1,
        normal: 1.4,
        relaxed: 1.6,
      },
      colors: {
        primary: '#8b5cf6',
        secondary: '#a78bfa',
        accent: '#c084fc',
        text: '#1f2937',
        textLight: '#6b7280',
        background: '#ffffff',
        border: '#e5e7eb',
        divider: '#d1d5db',
      },
      spacing: {
        xs: 4,
        sm: 8,
        md: 16,
        lg: 24,
        xl: 32,
        xxl: 48,
      },
      layout: {
        pageMargin: {
          top: 40,
          right: 40,
          bottom: 40,
          left: 40,
        },
        sectionSpacing: 32,
        itemSpacing: 16,
        columnGap: 24,
      },
      components: {
        header: {
          alignment: 'center',
          showDivider: false,
          backgroundColor: '#f8fafc',
        },
        section: {
          titleStyle: 'background',
          titleCase: 'uppercase',
          spacing: 'normal',
        },
        list: {
          bulletStyle: 'arrow',
          indentation: 16,
        },
        contact: {
          layout: 'grid',
          showIcons: true,
          separator: '',
        },
      },
    },
    layout: {
      type: 'two-column',
      columns: [
        {
          id: 'sidebar',
          width: 35,
          sections: ['personal_info', 'skills', 'languages'],
          order: 0,
        },
        {
          id: 'main',
          width: 65,
          sections: ['summary', 'work_experience', 'projects', 'education'],
          order: 1,
        },
      ],
      header: {
        height: 140,
        sections: ['personal_info'],
        style: 'creative',
      },
    },
    supportedSections: [
      'personal_info',
      'summary',
      'work_experience',
      'education',
      'skills',
      'projects',
      'languages',
      'awards',
    ],
    requiredSections: ['personal_info', 'work_experience', 'projects'],
    customizable: {
      colors: true,
      fonts: true,
      layout: true,
      spacing: true,
    },
    usageCount: 1820,
    rating: 4.9,
    reviewCount: 94,
    isOfficial: true,
  },
];

// Helper function to generate templates with IDs and timestamps
export function generateDefaultTemplates(): Template[] {
  const now = new Date().toISOString();
  
  return defaultTemplates.map(template => ({
    ...template,
    id: nanoid(),
    createdAt: now,
    updatedAt: now,
    createdBy: 'system',
  }));
}
