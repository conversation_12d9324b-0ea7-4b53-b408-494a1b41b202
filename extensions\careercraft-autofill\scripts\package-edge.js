#!/usr/bin/env node

/**
 * Edge Extension Packaging Script
 * 
 * Creates a Microsoft Edge Add-ons ready package
 */

const fs = require('fs')
const path = require('path')
const archiver = require('archiver')

console.log('🔷 Packaging Edge Extension...')

const sourceDir = path.join(__dirname, '../dist/edge')
const outputDir = path.join(__dirname, '../packages')
const outputFile = path.join(outputDir, 'careercraft-autofill-edge-v1.0.0.zip')

// Ensure output directory exists
if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir, { recursive: true })
}

// Create zip package
const output = fs.createWriteStream(outputFile)
const archive = archiver('zip', { zlib: { level: 9 } })

output.on('close', () => {
  const sizeKB = (archive.pointer() / 1024).toFixed(2)
  console.log(`✅ Edge package created: ${sizeKB} KB`)
  console.log(`📁 Location: ${outputFile}`)
  
  // Validate package
  validateEdgePackage()
})

archive.on('error', (err) => {
  console.error('❌ Packaging failed:', err)
  process.exit(1)
})

archive.pipe(output)

// Add all files from dist/edge
archive.directory(sourceDir, false)
archive.finalize()

function validateEdgePackage() {
  console.log('\n🔍 Validating Edge package...')
  
  // Check required files
  const requiredFiles = [
    'manifest.json',
    'background/background.js',
    'content/content.js',
    'popup/popup.html',
    'popup/popup.js',
    'icons/icon-16.png',
    'icons/icon-48.png',
    'icons/icon-128.png'
  ]
  
  let allValid = true
  
  requiredFiles.forEach(file => {
    const filePath = path.join(sourceDir, file)
    if (fs.existsSync(filePath)) {
      console.log(`✅ ${file}`)
    } else {
      console.log(`❌ ${file} - MISSING`)
      allValid = false
    }
  })
  
  // Check manifest
  const manifestPath = path.join(sourceDir, 'manifest.json')
  if (fs.existsSync(manifestPath)) {
    const manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf8'))
    
    console.log('\n📋 Manifest Validation:')
    console.log(`✅ Version: ${manifest.version}`)
    console.log(`✅ Manifest Version: ${manifest.manifest_version}`)
    console.log(`✅ Name: ${manifest.name}`)
    console.log(`✅ Permissions: ${manifest.permissions?.length || 0}`)
    
    if (manifest.manifest_version !== 3) {
      console.log('⚠️  Warning: Edge Add-ons prefers Manifest V3')
    }
  }
  
  if (allValid) {
    console.log('\n🎉 Edge package validation successful!')
    console.log('\n📋 Next Steps:')
    console.log('1. Go to Microsoft Partner Center')
    console.log('2. Navigate to Edge Add-ons section')
    console.log('3. Click "Create new extension"')
    console.log('4. Upload the package file')
    console.log('5. Fill in store listing details')
    console.log('6. Submit for certification')
  } else {
    console.log('\n❌ Package validation failed!')
    process.exit(1)
  }
}
