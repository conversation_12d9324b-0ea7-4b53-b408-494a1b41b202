'use client';

import { useState } from 'react';

export default function CareerCraftApp() {
  const [activeTab, setActiveTab] = useState('builder');
  const [resumeData, setResumeData] = useState({
    personalInfo: {
      name: '<PERSON>',
      email: '<EMAIL>',
      phone: '+****************',
      location: 'New York, NY',
      title: 'Software Engineer'
    },
    summary: 'Experienced software engineer with 5+ years of expertise in full-stack development.',
    experience: [
      {
        id: 1,
        company: 'TechCorp Inc.',
        position: 'Senior Software Engineer',
        duration: '2022 - Present',
        description: 'Led development of scalable web applications using React and Node.js'
      }
    ],
    education: [
      {
        id: 1,
        school: 'University of Technology',
        degree: 'Bachelor of Science in Computer Science',
        year: '2019'
      }
    ],
    skills: ['React', 'Node.js', 'TypeScript', 'Python', 'AWS']
  });

  const [aiContent, setAiContent] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);

  const generateAIContent = async (type: string) => {
    setIsGenerating(true);
    // Simulate AI generation
    setTimeout(() => {
      const content = {
        summary: 'Dynamic and results-driven software engineer with 5+ years of experience in developing scalable web applications. Proven track record of leading cross-functional teams and delivering high-quality solutions using modern technologies including React, Node.js, and cloud platforms.',
        experience: 'Spearheaded the development of a microservices architecture that improved system performance by 40% and reduced deployment time by 60%. Collaborated with product managers and designers to deliver user-centric features that increased customer satisfaction by 25%.',
        skills: 'Proficient in modern JavaScript frameworks (React, Vue.js), backend technologies (Node.js, Python, Java), cloud platforms (AWS, Azure), and DevOps practices (Docker, Kubernetes, CI/CD pipelines).'
      };
      setAiContent(content[type as keyof typeof content] || 'Generated content will appear here...');
      setIsGenerating(false);
    }, 2000);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <h1 className="text-2xl font-bold text-gray-900">CareerCraft - Functional Demo</h1>
            <nav className="flex space-x-8">
              <button 
                onClick={() => setActiveTab('builder')}
                className={`px-3 py-2 rounded-md ${activeTab === 'builder' ? 'bg-blue-100 text-blue-600' : 'text-gray-500 hover:text-gray-900'}`}
              >
                Resume Builder
              </button>
              <button 
                onClick={() => setActiveTab('ai')}
                className={`px-3 py-2 rounded-md ${activeTab === 'ai' ? 'bg-blue-100 text-blue-600' : 'text-gray-500 hover:text-gray-900'}`}
              >
                AI Assistant
              </button>
              <button 
                onClick={() => setActiveTab('templates')}
                className={`px-3 py-2 rounded-md ${activeTab === 'templates' ? 'bg-blue-100 text-blue-600' : 'text-gray-500 hover:text-gray-900'}`}
              >
                Templates
              </button>
            </nav>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Resume Builder Tab */}
        {activeTab === 'builder' && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Editor Panel */}
            <div className="bg-white rounded-lg shadow-lg p-6">
              <h2 className="text-xl font-semibold mb-6">Resume Editor</h2>
              
              {/* Personal Information */}
              <div className="mb-6">
                <h3 className="text-lg font-medium mb-3">Personal Information</h3>
                <div className="space-y-3">
                  <input
                    type="text"
                    value={resumeData.personalInfo.name}
                    onChange={(e) => setResumeData({
                      ...resumeData,
                      personalInfo: { ...resumeData.personalInfo, name: e.target.value }
                    })}
                    className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Full Name"
                  />
                  <input
                    type="email"
                    value={resumeData.personalInfo.email}
                    onChange={(e) => setResumeData({
                      ...resumeData,
                      personalInfo: { ...resumeData.personalInfo, email: e.target.value }
                    })}
                    className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Email"
                  />
                  <input
                    type="text"
                    value={resumeData.personalInfo.title}
                    onChange={(e) => setResumeData({
                      ...resumeData,
                      personalInfo: { ...resumeData.personalInfo, title: e.target.value }
                    })}
                    className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Job Title"
                  />
                </div>
              </div>

              {/* Summary */}
              <div className="mb-6">
                <h3 className="text-lg font-medium mb-3">Professional Summary</h3>
                <textarea
                  value={resumeData.summary}
                  onChange={(e) => setResumeData({ ...resumeData, summary: e.target.value })}
                  className="w-full p-2 border border-gray-300 rounded-md h-24 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Write a brief professional summary..."
                />
              </div>

              {/* Skills */}
              <div className="mb-6">
                <h3 className="text-lg font-medium mb-3">Skills</h3>
                <div className="flex flex-wrap gap-2">
                  {resumeData.skills.map((skill, index) => (
                    <span key={index} className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm">
                      {skill}
                    </span>
                  ))}
                </div>
                <button className="mt-2 text-blue-600 hover:text-blue-800 text-sm">+ Add Skill</button>
              </div>

              {/* Action Buttons */}
              <div className="flex space-x-3">
                <button className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors">
                  Save Resume
                </button>
                <button className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition-colors">
                  Export PDF
                </button>
                <button className="border border-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-50 transition-colors">
                  Preview
                </button>
              </div>
            </div>

            {/* Preview Panel */}
            <div className="bg-white rounded-lg shadow-lg p-6">
              <h2 className="text-xl font-semibold mb-6">Live Preview</h2>
              <div className="border border-gray-200 rounded-lg p-6 bg-white" style={{ minHeight: '600px' }}>
                {/* Resume Preview */}
                <div className="text-center border-b pb-4 mb-4">
                  <h1 className="text-2xl font-bold text-gray-900">{resumeData.personalInfo.name}</h1>
                  <p className="text-lg text-gray-600">{resumeData.personalInfo.title}</p>
                  <p className="text-sm text-gray-500">{resumeData.personalInfo.email} | {resumeData.personalInfo.phone}</p>
                  <p className="text-sm text-gray-500">{resumeData.personalInfo.location}</p>
                </div>

                <div className="mb-4">
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">PROFESSIONAL SUMMARY</h3>
                  <p className="text-sm text-gray-700">{resumeData.summary}</p>
                </div>

                <div className="mb-4">
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">EXPERIENCE</h3>
                  {resumeData.experience.map((exp) => (
                    <div key={exp.id} className="mb-3">
                      <div className="flex justify-between">
                        <h4 className="font-medium text-gray-900">{exp.position}</h4>
                        <span className="text-sm text-gray-500">{exp.duration}</span>
                      </div>
                      <p className="text-sm text-gray-600">{exp.company}</p>
                      <p className="text-sm text-gray-700 mt-1">{exp.description}</p>
                    </div>
                  ))}
                </div>

                <div className="mb-4">
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">EDUCATION</h3>
                  {resumeData.education.map((edu) => (
                    <div key={edu.id} className="mb-2">
                      <div className="flex justify-between">
                        <h4 className="font-medium text-gray-900">{edu.degree}</h4>
                        <span className="text-sm text-gray-500">{edu.year}</span>
                      </div>
                      <p className="text-sm text-gray-600">{edu.school}</p>
                    </div>
                  ))}
                </div>

                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">SKILLS</h3>
                  <div className="flex flex-wrap gap-2">
                    {resumeData.skills.map((skill, index) => (
                      <span key={index} className="bg-gray-100 text-gray-800 px-2 py-1 rounded text-sm">
                        {skill}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* AI Assistant Tab */}
        {activeTab === 'ai' && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <div className="bg-white rounded-lg shadow-lg p-6">
              <h2 className="text-xl font-semibold mb-6">AI Content Generator</h2>
              
              <div className="space-y-4">
                <div>
                  <h3 className="text-lg font-medium mb-3">Generate Professional Content</h3>
                  <div className="space-y-3">
                    <button
                      onClick={() => generateAIContent('summary')}
                      disabled={isGenerating}
                      className="w-full bg-purple-600 text-white px-4 py-3 rounded-md hover:bg-purple-700 transition-colors disabled:opacity-50"
                    >
                      {isGenerating ? 'Generating...' : 'Generate Professional Summary'}
                    </button>
                    <button
                      onClick={() => generateAIContent('experience')}
                      disabled={isGenerating}
                      className="w-full bg-purple-600 text-white px-4 py-3 rounded-md hover:bg-purple-700 transition-colors disabled:opacity-50"
                    >
                      {isGenerating ? 'Generating...' : 'Generate Experience Description'}
                    </button>
                    <button
                      onClick={() => generateAIContent('skills')}
                      disabled={isGenerating}
                      className="w-full bg-purple-600 text-white px-4 py-3 rounded-md hover:bg-purple-700 transition-colors disabled:opacity-50"
                    >
                      {isGenerating ? 'Generating...' : 'Generate Skills Section'}
                    </button>
                  </div>
                </div>

                <div className="border-t pt-4">
                  <h3 className="text-lg font-medium mb-3">ATS Optimization</h3>
                  <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <span className="font-medium">ATS Compatibility Score</span>
                      <span className="text-green-600 font-bold">87/100</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div className="bg-green-600 h-2 rounded-full" style={{ width: '87%' }}></div>
                    </div>
                    <p className="text-sm text-gray-600 mt-2">Your resume is well-optimized for ATS systems!</p>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-lg p-6">
              <h2 className="text-xl font-semibold mb-6">Generated Content</h2>
              <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 min-h-96">
                {aiContent ? (
                  <div>
                    <p className="text-gray-700 leading-relaxed">{aiContent}</p>
                    <div className="mt-4 flex space-x-2">
                      <button className="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700">
                        Use This Content
                      </button>
                      <button className="border border-gray-300 text-gray-700 px-3 py-1 rounded text-sm hover:bg-gray-50">
                        Regenerate
                      </button>
                    </div>
                  </div>
                ) : (
                  <p className="text-gray-500 italic">Click a button above to generate AI-powered content for your resume.</p>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Templates Tab */}
        {activeTab === 'templates' && (
          <div>
            <h2 className="text-2xl font-semibold mb-6">Professional Templates</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {[
                { name: 'Modern Professional', color: 'blue', description: 'Clean and contemporary design' },
                { name: 'Classic Executive', color: 'gray', description: 'Traditional and elegant layout' },
                { name: 'Creative Portfolio', color: 'green', description: 'Perfect for creative professionals' },
                { name: 'Tech Specialist', color: 'purple', description: 'Optimized for tech roles' },
                { name: 'Minimalist', color: 'indigo', description: 'Simple and clean design' },
                { name: 'Corporate', color: 'red', description: 'Professional corporate style' }
              ].map((template, index) => (
                <div key={index} className="border rounded-lg overflow-hidden hover:shadow-lg transition-shadow cursor-pointer">
                  <div className={`h-48 bg-gradient-to-br from-${template.color}-500 to-${template.color}-600 p-4 text-white text-xs`}>
                    <div className="bg-white/20 rounded p-2 mb-2">
                      <div className="h-2 bg-white/60 rounded mb-1"></div>
                      <div className="h-1 bg-white/40 rounded"></div>
                    </div>
                    <div className="space-y-1">
                      <div className="h-1 bg-white/60 rounded"></div>
                      <div className="h-1 bg-white/40 rounded w-3/4"></div>
                    </div>
                  </div>
                  <div className="p-4">
                    <h4 className="font-semibold">{template.name}</h4>
                    <p className="text-sm text-gray-600">{template.description}</p>
                    <button className="mt-2 bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700">
                      Use Template
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
