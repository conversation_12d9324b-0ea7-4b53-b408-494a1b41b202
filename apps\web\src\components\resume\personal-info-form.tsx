'use client';

import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { personalInfoSchema, type PersonalInfoInput } from '@careercraft/shared/schemas/resume';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Icons } from '@/components/ui/icons';
import { cn } from '@/lib/utils';

interface PersonalInfoFormProps {
  initialData?: Partial<PersonalInfoInput>;
  onSubmit: (data: PersonalInfoInput) => void;
  onCancel?: () => void;
  isLoading?: boolean;
  className?: string;
}

export function PersonalInfoForm({
  initialData,
  onSubmit,
  onCancel,
  isLoading = false,
  className,
}: PersonalInfoFormProps) {
  const {
    register,
    handleSubmit,
    formState: { errors, isDirty, isValid },
    watch,
  } = useForm<PersonalInfoInput>({
    resolver: zodResolver(personalInfoSchema),
    defaultValues: {
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      location: '',
      website: '',
      linkedin: '',
      github: '',
      portfolio: '',
      summary: '',
      ...initialData,
    },
    mode: 'onChange',
  });

  const watchedData = watch();

  const handleFormSubmit = (data: PersonalInfoInput) => {
    onSubmit(data);
  };

  return (
    <Card className={cn('w-full', className)}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Icons.user className="h-5 w-5" />
          Personal Information
        </CardTitle>
        <CardDescription>
          Add your contact information and professional summary
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
          {/* Name Fields */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="firstName">
                First Name <span className="text-destructive">*</span>
              </Label>
              <Input
                id="firstName"
                {...register('firstName')}
                placeholder="John"
                className={cn(errors.firstName && 'border-destructive')}
              />
              {errors.firstName && (
                <p className="text-sm text-destructive">{errors.firstName.message}</p>
              )}
            </div>
            <div className="space-y-2">
              <Label htmlFor="lastName">
                Last Name <span className="text-destructive">*</span>
              </Label>
              <Input
                id="lastName"
                {...register('lastName')}
                placeholder="Doe"
                className={cn(errors.lastName && 'border-destructive')}
              />
              {errors.lastName && (
                <p className="text-sm text-destructive">{errors.lastName.message}</p>
              )}
            </div>
          </div>

          {/* Contact Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="email">
                Email <span className="text-destructive">*</span>
              </Label>
              <Input
                id="email"
                type="email"
                {...register('email')}
                placeholder="<EMAIL>"
                className={cn(errors.email && 'border-destructive')}
              />
              {errors.email && (
                <p className="text-sm text-destructive">{errors.email.message}</p>
              )}
            </div>
            <div className="space-y-2">
              <Label htmlFor="phone">
                Phone <span className="text-destructive">*</span>
              </Label>
              <Input
                id="phone"
                type="tel"
                {...register('phone')}
                placeholder="+****************"
                className={cn(errors.phone && 'border-destructive')}
              />
              {errors.phone && (
                <p className="text-sm text-destructive">{errors.phone.message}</p>
              )}
            </div>
          </div>

          {/* Location */}
          <div className="space-y-2">
            <Label htmlFor="location">
              Location <span className="text-destructive">*</span>
            </Label>
            <Input
              id="location"
              {...register('location')}
              placeholder="San Francisco, CA"
              className={cn(errors.location && 'border-destructive')}
            />
            {errors.location && (
              <p className="text-sm text-destructive">{errors.location.message}</p>
            )}
          </div>

          {/* Optional Links */}
          <div className="space-y-4">
            <h4 className="text-sm font-medium text-muted-foreground">
              Optional Links
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="website">Website</Label>
                <Input
                  id="website"
                  type="url"
                  {...register('website')}
                  placeholder="https://johndoe.com"
                  className={cn(errors.website && 'border-destructive')}
                />
                {errors.website && (
                  <p className="text-sm text-destructive">{errors.website.message}</p>
                )}
              </div>
              <div className="space-y-2">
                <Label htmlFor="linkedin">LinkedIn</Label>
                <Input
                  id="linkedin"
                  type="url"
                  {...register('linkedin')}
                  placeholder="https://linkedin.com/in/johndoe"
                  className={cn(errors.linkedin && 'border-destructive')}
                />
                {errors.linkedin && (
                  <p className="text-sm text-destructive">{errors.linkedin.message}</p>
                )}
              </div>
              <div className="space-y-2">
                <Label htmlFor="github">GitHub</Label>
                <Input
                  id="github"
                  type="url"
                  {...register('github')}
                  placeholder="https://github.com/johndoe"
                  className={cn(errors.github && 'border-destructive')}
                />
                {errors.github && (
                  <p className="text-sm text-destructive">{errors.github.message}</p>
                )}
              </div>
              <div className="space-y-2">
                <Label htmlFor="portfolio">Portfolio</Label>
                <Input
                  id="portfolio"
                  type="url"
                  {...register('portfolio')}
                  placeholder="https://portfolio.johndoe.com"
                  className={cn(errors.portfolio && 'border-destructive')}
                />
                {errors.portfolio && (
                  <p className="text-sm text-destructive">{errors.portfolio.message}</p>
                )}
              </div>
            </div>
          </div>

          {/* Professional Summary */}
          <div className="space-y-2">
            <Label htmlFor="summary">Professional Summary</Label>
            <Textarea
              id="summary"
              {...register('summary')}
              placeholder="Write a brief professional summary highlighting your key skills and experience..."
              rows={4}
              className={cn(errors.summary && 'border-destructive')}
            />
            {errors.summary && (
              <p className="text-sm text-destructive">{errors.summary.message}</p>
            )}
            <p className="text-xs text-muted-foreground">
              {watchedData.summary?.length || 0}/500 characters
            </p>
          </div>

          {/* Form Actions */}
          <div className="flex flex-col sm:flex-row gap-3 pt-4">
            <Button
              type="submit"
              disabled={!isDirty || !isValid || isLoading}
              className="flex-1 sm:flex-none"
            >
              {isLoading && <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />}
              Save Personal Information
            </Button>
            {onCancel && (
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={isLoading}
                className="flex-1 sm:flex-none"
              >
                Cancel
              </Button>
            )}
          </div>

          {/* Form Status */}
          {isDirty && (
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <Icons.alertCircle className="h-4 w-4" />
              You have unsaved changes
            </div>
          )}
        </form>
      </CardContent>
    </Card>
  );
}
