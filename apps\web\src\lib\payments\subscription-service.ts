/**
 * Subscription Management Service
 * 
 * Handles subscription lifecycle, plan management, and billing operations
 */

import { prisma } from '@/lib/db'
import { stripeService } from './stripe-service'
import { z } from 'zod'
import type Stripe from 'stripe'

// Subscription schemas
export const CreateSubscriptionSchema = z.object({
  userId: z.string(),
  planId: z.string(),
  paymentMethodId: z.string().optional(),
  couponCode: z.string().optional(),
  trialDays: z.number().optional()
})

export const UpdateSubscriptionSchema = z.object({
  planId: z.string().optional(),
  cancelAtPeriodEnd: z.boolean().optional()
})

export interface SubscriptionPlan {
  id: string
  name: string
  description?: string
  priceMonthly: number
  priceYearly?: number
  stripePriceIdMonthly?: string
  stripePriceIdYearly?: string
  features: FeatureAccess
  maxResumes: number
  maxTemplates: number
  maxCollaborators: number
  aiSuggestionsLimit: number
  isActive: boolean
  sortOrder: number
}

export interface FeatureAccess {
  // Resume Management
  maxResumes: number | 'unlimited'
  maxTemplates: number | 'unlimited'
  customTemplates: boolean
  templateMarketplaceSelling: boolean
  
  // AI Features
  aiSuggestionsLimit: number | 'unlimited'
  advancedAI: boolean
  jobMatching: boolean
  interviewPrep: boolean
  
  // Collaboration
  maxCollaborators: number | 'unlimited'
  realTimeEditing: boolean
  versionControl: boolean
  commentSystem: boolean
  
  // Export & Sharing
  pdfExport: boolean
  customBranding: boolean
  publicProfiles: boolean
  linkedinIntegration: boolean
  
  // Analytics & Insights
  basicAnalytics: boolean
  advancedAnalytics: boolean
  marketInsights: boolean
  
  // Support & Services
  supportLevel: 'standard' | 'priority' | 'dedicated'
  apiAccess: boolean
  ssoIntegration: boolean
}

export interface UserSubscriptionDetails {
  id: string
  userId: string
  plan: SubscriptionPlan
  status: string
  currentPeriodStart?: Date
  currentPeriodEnd?: Date
  cancelAtPeriodEnd: boolean
  canceledAt?: Date
  trialStart?: Date
  trialEnd?: Date
  stripeCustomerId?: string
  stripeSubscriptionId?: string
}

export class SubscriptionService {
  /**
   * Get all available subscription plans
   */
  async getPlans(): Promise<SubscriptionPlan[]> {
    try {
      const plans = await prisma.subscriptionPlan.findMany({
        where: { isActive: true },
        orderBy: { sortOrder: 'asc' }
      })

      return plans.map(plan => ({
        ...plan,
        features: JSON.parse(plan.features)
      }))
    } catch (error) {
      console.error('Error fetching subscription plans:', error)
      throw new Error('Failed to fetch subscription plans')
    }
  }

  /**
   * Get a specific subscription plan
   */
  async getPlan(planId: string): Promise<SubscriptionPlan | null> {
    try {
      const plan = await prisma.subscriptionPlan.findUnique({
        where: { id: planId }
      })

      if (!plan) return null

      return {
        ...plan,
        features: JSON.parse(plan.features)
      }
    } catch (error) {
      console.error('Error fetching subscription plan:', error)
      throw new Error('Failed to fetch subscription plan')
    }
  }

  /**
   * Get user's current subscription
   */
  async getUserSubscription(userId: string): Promise<UserSubscriptionDetails | null> {
    try {
      const subscription = await prisma.userSubscription.findUnique({
        where: { userId },
        include: { plan: true }
      })

      if (!subscription) return null

      return {
        ...subscription,
        plan: {
          ...subscription.plan,
          features: JSON.parse(subscription.plan.features)
        }
      }
    } catch (error) {
      console.error('Error fetching user subscription:', error)
      throw new Error('Failed to fetch user subscription')
    }
  }

  /**
   * Create a new subscription
   */
  async createSubscription(
    data: z.infer<typeof CreateSubscriptionSchema>
  ): Promise<{ subscription: UserSubscriptionDetails; clientSecret?: string }> {
    try {
      const validatedData = CreateSubscriptionSchema.parse(data)

      // Get user and plan details
      const [user, plan] = await Promise.all([
        prisma.user.findUnique({ where: { id: validatedData.userId } }),
        prisma.subscriptionPlan.findUnique({ where: { id: validatedData.planId } })
      ])

      if (!user) throw new Error('User not found')
      if (!plan) throw new Error('Subscription plan not found')

      // Check if user already has a subscription
      const existingSubscription = await prisma.userSubscription.findUnique({
        where: { userId: validatedData.userId }
      })

      if (existingSubscription) {
        throw new Error('User already has an active subscription')
      }

      // Create or get Stripe customer
      let stripeCustomerId = ''
      const existingCustomer = await prisma.userSubscription.findFirst({
        where: { 
          user: { email: user.email },
          stripeCustomerId: { not: null }
        }
      })

      if (existingCustomer?.stripeCustomerId) {
        stripeCustomerId = existingCustomer.stripeCustomerId
      } else {
        const customer = await stripeService.createCustomer({
          email: user.email,
          name: user.name || undefined,
          metadata: { userId: user.id }
        })
        stripeCustomerId = customer.id
      }

      // Apply coupon if provided
      let couponId: string | undefined
      if (validatedData.couponCode) {
        const coupon = await prisma.coupon.findUnique({
          where: { 
            code: validatedData.couponCode,
            isActive: true
          }
        })

        if (coupon && this.isCouponValid(coupon)) {
          couponId = coupon.stripeCouponId || undefined
        }
      }

      // Create Stripe subscription
      const stripeSubscription = await stripeService.createSubscription({
        customerId: stripeCustomerId,
        priceId: plan.stripePriceIdMonthly!,
        trialDays: validatedData.trialDays,
        couponId,
        metadata: {
          userId: user.id,
          planId: plan.id
        }
      })

      // Create database subscription record
      const subscription = await prisma.userSubscription.create({
        data: {
          userId: validatedData.userId,
          planId: validatedData.planId,
          stripeCustomerId,
          stripeSubscriptionId: stripeSubscription.id,
          status: stripeSubscription.status,
          currentPeriodStart: new Date(stripeSubscription.current_period_start * 1000),
          currentPeriodEnd: new Date(stripeSubscription.current_period_end * 1000),
          trialStart: stripeSubscription.trial_start 
            ? new Date(stripeSubscription.trial_start * 1000) 
            : null,
          trialEnd: stripeSubscription.trial_end 
            ? new Date(stripeSubscription.trial_end * 1000) 
            : null
        },
        include: { plan: true }
      })

      // Update coupon usage if applied
      if (validatedData.couponCode && couponId) {
        await this.trackCouponUsage(validatedData.couponCode, validatedData.userId, subscription.id)
      }

      // Get client secret for payment confirmation
      let clientSecret: string | undefined
      if (stripeSubscription.latest_invoice) {
        const invoice = stripeSubscription.latest_invoice as Stripe.Invoice
        if (invoice.payment_intent) {
          const paymentIntent = invoice.payment_intent as Stripe.PaymentIntent
          clientSecret = paymentIntent.client_secret || undefined
        }
      }

      return {
        subscription: {
          ...subscription,
          plan: {
            ...subscription.plan,
            features: JSON.parse(subscription.plan.features)
          }
        },
        clientSecret
      }
    } catch (error) {
      console.error('Error creating subscription:', error)
      throw error
    }
  }

  /**
   * Update an existing subscription
   */
  async updateSubscription(
    userId: string,
    updates: z.infer<typeof UpdateSubscriptionSchema>
  ): Promise<UserSubscriptionDetails> {
    try {
      const validatedUpdates = UpdateSubscriptionSchema.parse(updates)

      const subscription = await prisma.userSubscription.findUnique({
        where: { userId },
        include: { plan: true }
      })

      if (!subscription) {
        throw new Error('Subscription not found')
      }

      let updatedSubscription = subscription

      // Handle plan change
      if (validatedUpdates.planId && validatedUpdates.planId !== subscription.planId) {
        const newPlan = await prisma.subscriptionPlan.findUnique({
          where: { id: validatedUpdates.planId }
        })

        if (!newPlan) {
          throw new Error('New subscription plan not found')
        }

        // Update Stripe subscription
        if (subscription.stripeSubscriptionId) {
          await stripeService.updateSubscription(subscription.stripeSubscriptionId, {
            items: [{
              id: subscription.stripeSubscriptionId,
              price: newPlan.stripePriceIdMonthly!
            }],
            proration_behavior: 'create_prorations'
          })
        }

        // Update database record
        updatedSubscription = await prisma.userSubscription.update({
          where: { userId },
          data: { planId: validatedUpdates.planId },
          include: { plan: true }
        })
      }

      // Handle cancellation
      if (validatedUpdates.cancelAtPeriodEnd !== undefined) {
        if (subscription.stripeSubscriptionId) {
          await stripeService.cancelSubscription(
            subscription.stripeSubscriptionId,
            validatedUpdates.cancelAtPeriodEnd
          )
        }

        updatedSubscription = await prisma.userSubscription.update({
          where: { userId },
          data: { 
            cancelAtPeriodEnd: validatedUpdates.cancelAtPeriodEnd,
            canceledAt: validatedUpdates.cancelAtPeriodEnd ? new Date() : null
          },
          include: { plan: true }
        })
      }

      return {
        ...updatedSubscription,
        plan: {
          ...updatedSubscription.plan,
          features: JSON.parse(updatedSubscription.plan.features)
        }
      }
    } catch (error) {
      console.error('Error updating subscription:', error)
      throw error
    }
  }

  /**
   * Cancel a subscription
   */
  async cancelSubscription(
    userId: string,
    cancelAtPeriodEnd: boolean = true
  ): Promise<UserSubscriptionDetails> {
    try {
      return await this.updateSubscription(userId, { cancelAtPeriodEnd })
    } catch (error) {
      console.error('Error canceling subscription:', error)
      throw error
    }
  }

  /**
   * Reactivate a canceled subscription
   */
  async reactivateSubscription(userId: string): Promise<UserSubscriptionDetails> {
    try {
      const subscription = await prisma.userSubscription.findUnique({
        where: { userId },
        include: { plan: true }
      })

      if (!subscription) {
        throw new Error('Subscription not found')
      }

      if (!subscription.cancelAtPeriodEnd) {
        throw new Error('Subscription is not scheduled for cancellation')
      }

      // Reactivate in Stripe
      if (subscription.stripeSubscriptionId) {
        await stripeService.updateSubscription(subscription.stripeSubscriptionId, {
          cancel_at_period_end: false
        })
      }

      // Update database record
      const updatedSubscription = await prisma.userSubscription.update({
        where: { userId },
        data: { 
          cancelAtPeriodEnd: false,
          canceledAt: null
        },
        include: { plan: true }
      })

      return {
        ...updatedSubscription,
        plan: {
          ...updatedSubscription.plan,
          features: JSON.parse(updatedSubscription.plan.features)
        }
      }
    } catch (error) {
      console.error('Error reactivating subscription:', error)
      throw error
    }
  }

  /**
   * Get user's payment history
   */
  async getPaymentHistory(userId: string, limit: number = 10): Promise<any[]> {
    try {
      const payments = await prisma.payment.findMany({
        where: { userId },
        orderBy: { createdAt: 'desc' },
        take: limit,
        include: { subscription: { include: { plan: true } } }
      })

      return payments.map(payment => ({
        ...payment,
        metadata: payment.metadata ? JSON.parse(payment.metadata) : null
      }))
    } catch (error) {
      console.error('Error fetching payment history:', error)
      throw new Error('Failed to fetch payment history')
    }
  }

  /**
   * Create billing portal session
   */
  async createBillingPortalSession(
    userId: string,
    returnUrl: string
  ): Promise<string> {
    try {
      const subscription = await prisma.userSubscription.findUnique({
        where: { userId }
      })

      if (!subscription?.stripeCustomerId) {
        throw new Error('No billing information found')
      }

      const session = await stripeService.createBillingPortalSession(
        subscription.stripeCustomerId,
        returnUrl
      )

      return session.url
    } catch (error) {
      console.error('Error creating billing portal session:', error)
      throw new Error('Failed to create billing portal session')
    }
  }

  /**
   * Validate coupon
   */
  private isCouponValid(coupon: any): boolean {
    const now = new Date()
    
    // Check if coupon is active
    if (!coupon.isActive) return false
    
    // Check validity period
    if (coupon.validUntil && now > coupon.validUntil) return false
    if (now < coupon.validFrom) return false
    
    // Check redemption limits
    if (coupon.maxRedemptions && coupon.currentRedemptions >= coupon.maxRedemptions) {
      return false
    }
    
    return true
  }

  /**
   * Track coupon usage
   */
  private async trackCouponUsage(
    couponCode: string,
    userId: string,
    subscriptionId: string
  ): Promise<void> {
    try {
      const coupon = await prisma.coupon.findUnique({
        where: { code: couponCode }
      })

      if (!coupon) return

      // Create usage record
      await prisma.couponUsage.create({
        data: {
          couponId: coupon.id,
          userId,
          subscriptionId
        }
      })

      // Update redemption count
      await prisma.coupon.update({
        where: { id: coupon.id },
        data: {
          currentRedemptions: {
            increment: 1
          }
        }
      })
    } catch (error) {
      console.error('Error tracking coupon usage:', error)
      // Don't throw error as this is not critical
    }
  }

  /**
   * Get subscription analytics
   */
  async getSubscriptionAnalytics(): Promise<{
    totalSubscriptions: number
    activeSubscriptions: number
    trialSubscriptions: number
    canceledSubscriptions: number
    monthlyRevenue: number
    yearlyRevenue: number
  }> {
    try {
      const [
        totalSubscriptions,
        activeSubscriptions,
        trialSubscriptions,
        canceledSubscriptions,
        monthlyPayments,
        yearlyPayments
      ] = await Promise.all([
        prisma.userSubscription.count(),
        prisma.userSubscription.count({ where: { status: 'active' } }),
        prisma.userSubscription.count({ where: { status: 'trialing' } }),
        prisma.userSubscription.count({ where: { status: 'canceled' } }),
        prisma.payment.aggregate({
          where: {
            status: 'succeeded',
            createdAt: {
              gte: new Date(new Date().getFullYear(), new Date().getMonth(), 1)
            }
          },
          _sum: { amount: true }
        }),
        prisma.payment.aggregate({
          where: {
            status: 'succeeded',
            createdAt: {
              gte: new Date(new Date().getFullYear(), 0, 1)
            }
          },
          _sum: { amount: true }
        })
      ])

      return {
        totalSubscriptions,
        activeSubscriptions,
        trialSubscriptions,
        canceledSubscriptions,
        monthlyRevenue: monthlyPayments._sum.amount || 0,
        yearlyRevenue: yearlyPayments._sum.amount || 0
      }
    } catch (error) {
      console.error('Error fetching subscription analytics:', error)
      throw new Error('Failed to fetch subscription analytics')
    }
  }
}

// Export singleton instance
export const subscriptionService = new SubscriptionService()
