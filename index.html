<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CareerCraft - AI-Powered Resume Builder | Professional Resume Templates</title>
    <meta name="description" content="Create professional resumes with AI assistance, ATS optimization, cover letters, and comprehensive career tools. 50+ templates, LinkedIn optimization, and interview preparation.">
    <meta name="keywords" content="resume builder, AI resume, ATS optimization, cover letter, career tools, job search, professional templates, LinkedIn optimization">
    <meta name="author" content="CareerCraft">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="CareerCraft - AI-Powered Resume Builder">
    <meta property="og:description" content="Create professional resumes with AI assistance and land your dream job">
    <meta property="og:image" content="https://careercraft.onlinejobsearchhelp.com/assets/og-image.jpg">
    <meta property="og:url" content="https://careercraft.onlinejobsearchhelp.com">
    <meta property="og:type" content="website">
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="CareerCraft - AI-Powered Resume Builder">
    <meta name="twitter:description" content="Create professional resumes with AI assistance and land your dream job">
    <meta name="twitter:image" content="https://careercraft.onlinejobsearchhelp.com/assets/twitter-card.jpg">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
    
    <!-- Canonical URL -->
    <link rel="canonical" href="https://careercraft.onlinejobsearchhelp.com">
    
    <!-- Preload Critical Resources -->
    <link rel="preload" href="https://cdn.tailwindcss.com" as="style">
    <link rel="preload" href="https://unpkg.com/lucide@latest/dist/umd/lucide.js" as="script">
    
    <!-- Stylesheets -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    
    <!-- Custom Styles -->
    <style>
        /* CSS Variables for Dark/Light Mode */
        :root {
            --bg-primary: #ffffff;
            --bg-secondary: #f8fafc;
            --bg-tertiary: #f1f5f9;
            --text-primary: #1f2937;
            --text-secondary: #6b7280;
            --text-tertiary: #9ca3af;
            --border-color: #e5e7eb;
            --glass-bg: rgba(255, 255, 255, 0.25);
            --glass-border: rgba(255, 255, 255, 0.18);
            --glass-shadow: rgba(31, 38, 135, 0.37);
            --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }

        [data-theme="dark"] {
            --bg-primary: #0f172a;
            --bg-secondary: #1e293b;
            --bg-tertiary: #334155;
            --text-primary: #f8fafc;
            --text-secondary: #cbd5e1;
            --text-tertiary: #94a3b8;
            --border-color: #475569;
            --glass-bg: rgba(15, 23, 42, 0.25);
            --glass-border: rgba(248, 250, 252, 0.18);
            --glass-shadow: rgba(0, 0, 0, 0.37);
            --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --gradient-secondary: linear-gradient(135deg, #a855f7 0%, #3b82f6 100%);
        }

        /* Glassmorphism Base Styles */
        .glass-card {
            background: var(--glass-bg);
            backdrop-filter: blur(16px);
            -webkit-backdrop-filter: blur(16px);
            border-radius: 16px;
            border: 1px solid var(--glass-border);
            box-shadow: 0 8px 32px var(--glass-shadow);
            transition: all 0.3s ease;
        }

        .glass-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 16px 48px var(--glass-shadow);
            border-color: rgba(255, 255, 255, 0.3);
        }

        .glass-input {
            background: var(--glass-bg);
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
            border: 1px solid var(--glass-border);
            border-radius: 12px;
            transition: all 0.3s ease;
        }

        .glass-input:focus {
            border-color: rgba(102, 126, 234, 0.5);
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            outline: none;
        }

        .glass-panel {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 20px;
            box-shadow: 0 12px 40px var(--glass-shadow);
        }

        /* Feature Cards with Glassmorphism */
        .feature-card {
            background: var(--glass-bg);
            backdrop-filter: blur(16px);
            -webkit-backdrop-filter: blur(16px);
            border: 1px solid var(--glass-border);
            border-radius: 16px;
            box-shadow: 0 8px 32px var(--glass-shadow);
            transition: all 0.3s ease;
            color: var(--text-primary);
        }

        .feature-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 16px 48px var(--glass-shadow);
            border-color: rgba(102, 126, 234, 0.3);
        }

        /* Pricing Cards with Enhanced Glassmorphism */
        .pricing-card {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 20px;
            box-shadow: 0 12px 40px var(--glass-shadow);
            transition: all 0.3s ease;
            color: var(--text-primary);
        }

        .pricing-card:hover {
            transform: scale(1.05) translateY(-4px);
            box-shadow: 0 20px 60px var(--glass-shadow);
        }

        .pricing-card.featured {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.2), rgba(118, 75, 162, 0.2));
            border: 2px solid rgba(102, 126, 234, 0.4);
        }

        /* Demo Section with Glassmorphism */
        .demo-section {
            min-height: 400px;
            background: var(--glass-bg);
            backdrop-filter: blur(12px);
            -webkit-backdrop-filter: blur(12px);
            border: 1px solid var(--glass-border);
            border-radius: 16px;
            transition: all 0.3s ease;
        }

        .demo-section:hover {
            transform: scale(1.02);
            box-shadow: 0 12px 40px var(--glass-shadow);
        }

        /* Dark Mode Toggle Button */
        .theme-toggle {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            background: var(--glass-bg);
            backdrop-filter: blur(16px);
            -webkit-backdrop-filter: blur(16px);
            border: 1px solid var(--glass-border);
            border-radius: 50px;
            padding: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 16px var(--glass-shadow);
        }

        .theme-toggle:hover {
            transform: scale(1.1);
            box-shadow: 0 8px 24px var(--glass-shadow);
        }

        /* Background Patterns */
        body {
            background: var(--bg-primary);
            color: var(--text-primary);
            transition: all 0.3s ease;
        }

        .gradient-bg {
            background: var(--gradient-primary);
            position: relative;
            overflow: hidden;
        }

        .gradient-bg::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
            opacity: 0.3;
        }

        /* Animations */
        .hero-animation {
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }

        .fade-in {
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.6s ease;
        }

        .fade-in.visible {
            opacity: 1;
            transform: translateY(0);
        }

        /* Loading spinner with glassmorphism */
        .spinner {
            border: 4px solid var(--glass-bg);
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 2s linear infinite;
            backdrop-filter: blur(8px);
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Responsive Glassmorphism */
        @media (max-width: 768px) {
            .glass-card, .glass-panel {
                backdrop-filter: blur(8px);
                -webkit-backdrop-filter: blur(8px);
            }
        }

        /* Custom Scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: var(--bg-secondary);
        }

        ::-webkit-scrollbar-thumb {
            background: var(--glass-bg);
            border-radius: 4px;
            backdrop-filter: blur(8px);
        }

        ::-webkit-scrollbar-thumb:hover {
            background: rgba(102, 126, 234, 0.3);
        }
    </style>
    
    <!-- Google Analytics -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=GA_MEASUREMENT_ID"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', 'GA_MEASUREMENT_ID');
    </script>
    
    <!-- Schema.org Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebApplication",
        "name": "CareerCraft",
        "description": "AI-powered resume builder with professional templates and career tools",
        "url": "https://careercraft.onlinejobsearchhelp.com",
        "applicationCategory": "BusinessApplication",
        "operatingSystem": "Web Browser",
        "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD",
            "availability": "https://schema.org/InStock"
        },
        "creator": {
            "@type": "Organization",
            "name": "CareerCraft",
            "url": "https://careercraft.onlinejobsearchhelp.com"
        }
    }
    </script>
</head>
<body>
    <!-- Dark/Light Mode Toggle -->
    <button id="theme-toggle" class="theme-toggle" aria-label="Toggle dark mode">
        <i data-lucide="sun" class="w-6 h-6 text-yellow-500 dark-icon hidden"></i>
        <i data-lucide="moon" class="w-6 h-6 text-blue-600 light-icon"></i>
    </button>

    <!-- Loading Screen -->
    <div id="loading-screen" class="fixed inset-0 z-50 flex items-center justify-center glass-panel">
        <div class="text-center">
            <div class="spinner mx-auto mb-4"></div>
            <p class="text-gray-600">Loading CareerCraft...</p>
        </div>
    </div>

    <!-- Header -->
    <header class="glass-panel sticky top-0 z-40 border-0 border-b border-opacity-20">
        <nav class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <h1 class="text-2xl font-bold" style="color: var(--text-primary)">
                            <span class="text-blue-600">Career</span>Craft
                        </h1>
                    </div>
                </div>
                <div class="hidden md:block">
                    <div class="ml-10 flex items-baseline space-x-4">
                        <a href="#features" class="glass-input px-3 py-2 text-sm font-medium transition-colors" style="color: var(--text-secondary)">Features</a>
                        <a href="#demo" class="glass-input px-3 py-2 text-sm font-medium transition-colors" style="color: var(--text-secondary)">Demo</a>
                        <a href="#pricing" class="glass-input px-3 py-2 text-sm font-medium transition-colors" style="color: var(--text-secondary)">Pricing</a>
                        <a href="https://onlinejobsearchhelp.com" class="glass-input px-3 py-2 text-sm font-medium transition-colors" style="color: var(--text-secondary)">Blog</a>
                        <button id="get-started-btn" class="glass-card bg-gradient-to-r from-blue-600 to-purple-600 text-white px-4 py-2 text-sm font-medium hover:from-blue-700 hover:to-purple-700 transition-all">
                            Get Started Free
                        </button>
                    </div>
                </div>
                <!-- Mobile menu button -->
                <div class="md:hidden">
                    <button id="mobile-menu-btn" class="glass-input p-2" style="color: var(--text-secondary)">
                        <i data-lucide="menu" class="w-6 h-6"></i>
                    </button>
                </div>
            </div>
        </nav>

        <!-- Mobile menu -->
        <div id="mobile-menu" class="md:hidden hidden glass-panel border-t border-opacity-20">
            <div class="px-2 pt-2 pb-3 space-y-1">
                <a href="#features" class="block px-3 py-2 glass-input" style="color: var(--text-secondary)">Features</a>
                <a href="#demo" class="block px-3 py-2 glass-input" style="color: var(--text-secondary)">Demo</a>
                <a href="#pricing" class="block px-3 py-2 glass-input" style="color: var(--text-secondary)">Pricing</a>
                <a href="https://onlinejobsearchhelp.com" class="block px-3 py-2 glass-input" style="color: var(--text-secondary)">Blog</a>
                <button class="block w-full text-left px-3 py-2 glass-card bg-gradient-to-r from-blue-600 to-purple-600 text-white">Get Started Free</button>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="gradient-bg text-white py-20 relative overflow-hidden">
        <div class="absolute inset-0 bg-black opacity-10"></div>
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center relative z-10">
            <div class="hero-animation">
                <h1 class="text-4xl md:text-6xl font-bold mb-6 fade-in">
                    Your Complete AI-Powered Career Platform
                </h1>
                <p class="text-xl md:text-2xl mb-8 text-blue-100 fade-in">
                    Build resumes, track applications, get career insights, and land your dream job with our comprehensive AI-powered platform
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center fade-in">
                    <button id="hero-start-btn" class="bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors shadow-lg">
                        <i data-lucide="play" class="w-5 h-5 inline mr-2"></i>
                        Start Building Free
                    </button>
                    <button id="hero-demo-btn" class="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-colors">
                        <i data-lucide="video" class="w-5 h-5 inline mr-2"></i>
                        Watch Demo
                    </button>
                </div>
            </div>
            
            <!-- Stats -->
            <div class="grid grid-cols-2 md:grid-cols-4 gap-8 mt-16 fade-in">
                <div class="text-center">
                    <div class="text-3xl font-bold">50K+</div>
                    <div class="text-blue-200">Resumes Created</div>
                </div>
                <div class="text-center">
                    <div class="text-3xl font-bold">95%</div>
                    <div class="text-blue-200">ATS Success Rate</div>
                </div>
                <div class="text-center">
                    <div class="text-3xl font-bold">50+</div>
                    <div class="text-blue-200">Professional Templates</div>
                </div>
                <div class="text-center">
                    <div class="text-3xl font-bold">24/7</div>
                    <div class="text-blue-200">AI Assistance</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Trust Indicators -->
    <section class="py-12 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-8">
                <p class="text-gray-600">Trusted by professionals at</p>
            </div>
            <div class="grid grid-cols-2 md:grid-cols-5 gap-8 items-center opacity-60">
                <div class="text-center text-2xl font-bold text-gray-400">Google</div>
                <div class="text-center text-2xl font-bold text-gray-400">Microsoft</div>
                <div class="text-center text-2xl font-bold text-gray-400">Amazon</div>
                <div class="text-center text-2xl font-bold text-gray-400">Apple</div>
                <div class="text-center text-2xl font-bold text-gray-400">Meta</div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="py-20">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16 fade-in">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                    Complete AI-Powered Career Ecosystem
                </h2>
                <p class="text-xl text-gray-600">
                    From resume building to career intelligence - everything you need to accelerate your career success
                </p>
            </div>

            <!-- Core Features Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- AI Resume Builder -->
                <div class="feature-card p-6 fade-in">
                    <div class="w-12 h-12 glass-card bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center mb-4">
                        <i data-lucide="file-text" class="w-6 h-6 text-white"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-2" style="color: var(--text-primary)">AI Resume Builder</h3>
                    <p class="mb-4" style="color: var(--text-secondary)">
                        Intelligent drag-and-drop builder with real-time AI suggestions and optimization.
                    </p>
                    <ul class="text-sm space-y-1" style="color: var(--text-tertiary)">
                        <li>✓ Real-time preview</li>
                        <li>✓ AI content suggestions</li>
                        <li>✓ Smart formatting</li>
                        <li>✓ Auto-save functionality</li>
                    </ul>
                </div>

                <!-- AI Content Generation -->
                <div class="feature-card p-6 fade-in">
                    <div class="w-12 h-12 glass-card bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg flex items-center justify-center mb-4">
                        <i data-lucide="sparkles" class="w-6 h-6 text-white"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-2" style="color: var(--text-primary)">AI Content Generation</h3>
                    <p class="mb-4" style="color: var(--text-secondary)">
                        Generate professional content tailored to your experience and target role.
                    </p>
                    <ul class="text-sm space-y-1" style="color: var(--text-tertiary)">
                        <li>✓ Professional summaries</li>
                        <li>✓ Achievement bullets</li>
                        <li>✓ Job descriptions</li>
                        <li>✓ Skills optimization</li>
                    </ul>
                </div>

                <!-- ATS Optimization -->
                <div class="feature-card p-6 fade-in">
                    <div class="w-12 h-12 glass-card bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center mb-4">
                        <i data-lucide="shield-check" class="w-6 h-6 text-white"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-2" style="color: var(--text-primary)">ATS Optimization</h3>
                    <p class="mb-4" style="color: var(--text-secondary)">
                        Ensure your resume passes Applicant Tracking Systems with 95%+ success rate.
                    </p>
                    <ul class="text-sm space-y-1" style="color: var(--text-tertiary)">
                        <li>✓ ATS compatibility score</li>
                        <li>✓ Keyword optimization</li>
                        <li>✓ Format validation</li>
                        <li>✓ Industry keywords</li>
                    </ul>
                </div>

                <!-- Professional Templates -->
                <div class="feature-card bg-white p-6 rounded-xl shadow-lg fade-in">
                    <div class="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center mb-4">
                        <i data-lucide="layout-template" class="w-6 h-6 text-indigo-600"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-2">50+ Professional Templates</h3>
                    <p class="text-gray-600 mb-4">
                        Industry-specific templates designed by HR professionals and recruiters.
                    </p>
                    <ul class="text-sm text-gray-500 space-y-1">
                        <li>✓ 50+ unique designs</li>
                        <li>✓ Industry-specific layouts</li>
                        <li>✓ Modern & classic styles</li>
                        <li>✓ Fully customizable</li>
                    </ul>
                </div>

                <!-- Cover Letter Builder -->
                <div class="feature-card bg-white p-6 rounded-xl shadow-lg fade-in">
                    <div class="w-12 h-12 bg-cyan-100 rounded-lg flex items-center justify-center mb-4">
                        <i data-lucide="mail" class="w-6 h-6 text-cyan-600"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-2">AI Cover Letter Builder</h3>
                    <p class="text-gray-600 mb-4">
                        Create compelling cover letters that perfectly match your resume and target job.
                    </p>
                    <ul class="text-sm text-gray-500 space-y-1">
                        <li>✓ AI-powered writing</li>
                        <li>✓ Job-specific content</li>
                        <li>✓ Company research integration</li>
                        <li>✓ Matching templates</li>
                    </ul>
                </div>

                <!-- LinkedIn Optimization -->
                <div class="feature-card bg-white p-6 rounded-xl shadow-lg fade-in">
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                        <i data-lucide="linkedin" class="w-6 h-6 text-blue-600"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-2">LinkedIn Profile Optimizer</h3>
                    <p class="text-gray-600 mb-4">
                        Optimize your LinkedIn profile with AI-generated content and SEO keywords.
                    </p>
                    <ul class="text-sm text-gray-500 space-y-1">
                        <li>✓ Profile optimization</li>
                        <li>✓ Headline generation</li>
                        <li>✓ Summary enhancement</li>
                        <li>✓ Skills recommendations</li>
                    </ul>
                </div>

                <!-- Smart Job Matching -->
                <div class="feature-card bg-white p-6 rounded-xl shadow-lg fade-in">
                    <div class="w-12 h-12 bg-emerald-100 rounded-lg flex items-center justify-center mb-4">
                        <i data-lucide="target" class="w-6 h-6 text-emerald-600"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-2">Smart Job Matching</h3>
                    <p class="text-gray-600 mb-4">
                        Find perfect job matches and get tailored resume suggestions for each application.
                    </p>
                    <ul class="text-sm text-gray-500 space-y-1">
                        <li>✓ Job compatibility scoring</li>
                        <li>✓ Skill gap analysis</li>
                        <li>✓ Resume customization</li>
                        <li>✓ Application tracking</li>
                    </ul>
                </div>

                <!-- Interview Preparation -->
                <div class="feature-card bg-white p-6 rounded-xl shadow-lg fade-in">
                    <div class="w-12 h-12 bg-violet-100 rounded-lg flex items-center justify-center mb-4">
                        <i data-lucide="mic" class="w-6 h-6 text-violet-600"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-2">AI Interview Coach</h3>
                    <p class="text-gray-600 mb-4">
                        Practice interviews with AI and get personalized feedback to improve performance.
                    </p>
                    <ul class="text-sm text-gray-500 space-y-1">
                        <li>✓ Mock interview sessions</li>
                        <li>✓ 1000+ question database</li>
                        <li>✓ Answer analysis & scoring</li>
                        <li>✓ Video practice mode</li>
                    </ul>
                </div>

                <!-- Career Intelligence Engine -->
                <div class="feature-card bg-white p-6 rounded-xl shadow-lg fade-in">
                    <div class="w-12 h-12 bg-amber-100 rounded-lg flex items-center justify-center mb-4">
                        <i data-lucide="trending-up" class="w-6 h-6 text-amber-600"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-2">Career Intelligence Engine</h3>
                    <p class="text-gray-600 mb-4">
                        Advanced AI-powered career insights with predictive analytics and market intelligence.
                    </p>
                    <ul class="text-sm text-gray-500 space-y-1">
                        <li>✓ Profile vectorization & analysis</li>
                        <li>✓ Real-time market data collection</li>
                        <li>✓ Predictive career forecasting</li>
                        <li>✓ Skill gap analysis & recommendations</li>
                    </ul>
                </div>

                <!-- Browser Extension -->
                <div class="feature-card bg-white p-6 rounded-xl shadow-lg fade-in">
                    <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mb-4">
                        <i data-lucide="chrome" class="w-6 h-6 text-orange-600"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-2">Intelligent Autofill Extension</h3>
                    <p class="text-gray-600 mb-4">
                        One-click job applications with intelligent form detection and auto-population.
                    </p>
                    <ul class="text-sm text-gray-500 space-y-1">
                        <li>✓ Cross-browser support (Chrome, Firefox, Edge)</li>
                        <li>✓ Smart form detection & autofill</li>
                        <li>✓ Application tracking & analytics</li>
                        <li>✓ Job board integrations</li>
                    </ul>
                </div>

                <!-- Collaborative Platform -->
                <div class="feature-card bg-white p-6 rounded-xl shadow-lg fade-in">
                    <div class="w-12 h-12 bg-pink-100 rounded-lg flex items-center justify-center mb-4">
                        <i data-lucide="users" class="w-6 h-6 text-pink-600"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-2">Collaborative Review Platform</h3>
                    <p class="text-gray-600 mb-4">
                        Real-time collaboration with peers, mentors, and career experts.
                    </p>
                    <ul class="text-sm text-gray-500 space-y-1">
                        <li>✓ Real-time collaborative editing</li>
                        <li>✓ Comment & annotation system</li>
                        <li>✓ Version control & history</li>
                        <li>✓ Expert feedback marketplace</li>
                    </ul>
                </div>

                <!-- Template Marketplace -->
                <div class="feature-card bg-white p-6 rounded-xl shadow-lg fade-in">
                    <div class="w-12 h-12 bg-teal-100 rounded-lg flex items-center justify-center mb-4">
                        <i data-lucide="store" class="w-6 h-6 text-teal-600"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-2">Template Marketplace</h3>
                    <p class="text-gray-600 mb-4">
                        Create, sell, and discover premium templates from the community.
                    </p>
                    <ul class="text-sm text-gray-500 space-y-1">
                        <li>✓ Custom template builder</li>
                        <li>✓ Community marketplace</li>
                        <li>✓ Template selling platform</li>
                        <li>✓ Revenue sharing program</li>
                    </ul>
                </div>

                <!-- Integration Ecosystem -->
                <div class="feature-card bg-white p-6 rounded-xl shadow-lg fade-in">
                    <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-4">
                        <i data-lucide="link" class="w-6 h-6 text-purple-600"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-2">Integration Ecosystem</h3>
                    <p class="text-gray-600 mb-4">
                        Seamless integrations with job boards, ATS systems, and professional networks.
                    </p>
                    <ul class="text-sm text-gray-500 space-y-1">
                        <li>✓ LinkedIn auto-import & sync</li>
                        <li>✓ GitHub portfolio integration</li>
                        <li>✓ Major job board connections</li>
                        <li>✓ ATS system compatibility</li>
                    </ul>
                </div>

                <!-- Mobile Application -->
                <div class="feature-card bg-white p-6 rounded-xl shadow-lg fade-in">
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-4">
                        <i data-lucide="smartphone" class="w-6 h-6 text-green-600"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-2">Mobile Application</h3>
                    <p class="text-gray-600 mb-4">
                        Native iOS and Android apps with offline capabilities and push notifications.
                    </p>
                    <ul class="text-sm text-gray-500 space-y-1">
                        <li>✓ Native iOS & Android apps</li>
                        <li>✓ Offline resume editing</li>
                        <li>✓ Push notifications</li>
                        <li>✓ Mobile-optimized features</li>
                    </ul>
                </div>
            </div>
        </div>
    </section>

    <!-- Pricing Section -->
    <section id="pricing" class="py-20 bg-gray-100">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16 fade-in">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                    Choose Your Plan
                </h2>
                <p class="text-xl text-gray-600">
                    Start free, upgrade when you need more features
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <!-- Free Plan -->
                <div class="pricing-card p-8 fade-in">
                    <div class="text-center">
                        <h3 class="text-2xl font-bold mb-2" style="color: var(--text-primary)">Free</h3>
                        <div class="text-4xl font-bold mb-4" style="color: var(--text-primary)">$0<span class="text-lg" style="color: var(--text-tertiary)">/month</span></div>
                        <p class="mb-6" style="color: var(--text-secondary)">Perfect for getting started</p>
                        <button class="w-full glass-card bg-gradient-to-r from-gray-500 to-gray-600 text-white py-3 font-semibold hover:from-gray-600 hover:to-gray-700 transition-all">
                            Get Started Free
                        </button>
                    </div>
                    <ul class="mt-8 space-y-3">
                        <li class="flex items-center"><i data-lucide="check" class="w-5 h-5 text-green-500 mr-3"></i><span style="color: var(--text-secondary)">3 Resume templates</span></li>
                        <li class="flex items-center"><i data-lucide="check" class="w-5 h-5 text-green-500 mr-3"></i><span style="color: var(--text-secondary)">Basic AI content generation</span></li>
                        <li class="flex items-center"><i data-lucide="check" class="w-5 h-5 text-green-500 mr-3"></i><span style="color: var(--text-secondary)">PDF export</span></li>
                        <li class="flex items-center"><i data-lucide="check" class="w-5 h-5 text-green-500 mr-3"></i><span style="color: var(--text-secondary)">ATS compatibility check</span></li>
                        <li class="flex items-center"><i data-lucide="check" class="w-5 h-5 text-green-500 mr-3"></i><span style="color: var(--text-secondary)">Basic career insights</span></li>
                        <li class="flex items-center"><i data-lucide="check" class="w-5 h-5 text-green-500 mr-3"></i><span style="color: var(--text-secondary)">Browser extension (basic)</span></li>
                    </ul>
                </div>

                <!-- Pro Plan -->
                <div class="pricing-card featured p-8 transform scale-105 fade-in">
                    <div class="text-center">
                        <div class="glass-card bg-gradient-to-r from-yellow-400 to-orange-400 text-gray-900 px-3 py-1 rounded-full text-sm font-semibold mb-4 inline-block">
                            Most Popular
                        </div>
                        <h3 class="text-2xl font-bold mb-2 text-white">Pro</h3>
                        <div class="text-4xl font-bold mb-4 text-white">$19<span class="text-lg text-blue-200">/month</span></div>
                        <p class="text-blue-100 mb-6">For serious job seekers</p>
                        <button class="w-full glass-card bg-gradient-to-r from-white to-gray-100 text-blue-600 py-3 font-semibold hover:from-gray-100 hover:to-gray-200 transition-all">
                            Start Pro Trial
                        </button>
                    </div>
                    <ul class="mt-8 space-y-3">
                        <li class="flex items-center"><i data-lucide="check" class="w-5 h-5 text-green-400 mr-3"></i><span class="text-white">50+ Premium templates + marketplace</span></li>
                        <li class="flex items-center"><i data-lucide="check" class="w-5 h-5 text-green-400 mr-3"></i><span class="text-white">Advanced AI content generation</span></li>
                        <li class="flex items-center"><i data-lucide="check" class="w-5 h-5 text-green-400 mr-3"></i><span class="text-white">Cover letter & portfolio builder</span></li>
                        <li class="flex items-center"><i data-lucide="check" class="w-5 h-5 text-green-400 mr-3"></i><span class="text-white">LinkedIn optimization & auto-import</span></li>
                        <li class="flex items-center"><i data-lucide="check" class="w-5 h-5 text-green-400 mr-3"></i><span class="text-white">AI interview coach & preparation</span></li>
                        <li class="flex items-center"><i data-lucide="check" class="w-5 h-5 text-green-400 mr-3"></i><span class="text-white">Career intelligence engine</span></li>
                        <li class="flex items-center"><i data-lucide="check" class="w-5 h-5 text-green-400 mr-3"></i><span class="text-white">Browser extension (full features)</span></li>
                        <li class="flex items-center"><i data-lucide="check" class="w-5 h-5 text-green-400 mr-3"></i><span class="text-white">Mobile app access</span></li>
                        <li class="flex items-center"><i data-lucide="check" class="w-5 h-5 text-green-400 mr-3"></i><span class="text-white">Job board integrations</span></li>
                    </ul>
                </div>

                <!-- Enterprise Plan -->
                <div class="pricing-card p-8 fade-in">
                    <div class="text-center">
                        <h3 class="text-2xl font-bold mb-2" style="color: var(--text-primary)">Enterprise</h3>
                        <div class="text-4xl font-bold mb-4" style="color: var(--text-primary)">$49<span class="text-lg" style="color: var(--text-tertiary)">/month</span></div>
                        <p class="mb-6" style="color: var(--text-secondary)">For teams and organizations</p>
                        <button class="w-full glass-card bg-gradient-to-r from-blue-600 to-purple-600 text-white py-3 font-semibold hover:from-blue-700 hover:to-purple-700 transition-all">
                            Contact Sales
                        </button>
                    </div>
                    <ul class="mt-8 space-y-3">
                        <li class="flex items-center"><i data-lucide="check" class="w-5 h-5 text-green-500 mr-3"></i><span style="color: var(--text-secondary)">Everything in Pro</span></li>
                        <li class="flex items-center"><i data-lucide="check" class="w-5 h-5 text-green-500 mr-3"></i><span style="color: var(--text-secondary)">Real-time team collaboration</span></li>
                        <li class="flex items-center"><i data-lucide="check" class="w-5 h-5 text-green-500 mr-3"></i><span style="color: var(--text-secondary)">Expert feedback marketplace</span></li>
                        <li class="flex items-center"><i data-lucide="check" class="w-5 h-5 text-green-500 mr-3"></i><span style="color: var(--text-secondary)">Custom branding & templates</span></li>
                        <li class="flex items-center"><i data-lucide="check" class="w-5 h-5 text-green-500 mr-3"></i><span style="color: var(--text-secondary)">API access & custom integrations</span></li>
                        <li class="flex items-center"><i data-lucide="check" class="w-5 h-5 text-green-500 mr-3"></i><span style="color: var(--text-secondary)">Advanced analytics & reporting</span></li>
                        <li class="flex items-center"><i data-lucide="check" class="w-5 h-5 text-green-500 mr-3"></i><span style="color: var(--text-secondary)">Priority support & training</span></li>
                        <li class="flex items-center"><i data-lucide="check" class="w-5 h-5 text-green-500 mr-3"></i><span style="color: var(--text-secondary)">White-label solutions</span></li>
                    </ul>
                </div>
            </div>
        </div>
    </section>

    <!-- Demo Section -->
    <section id="demo" class="py-20">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16 fade-in">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                    See CareerCraft in Action
                </h2>
                <p class="text-xl text-gray-600">
                    Experience the power of AI-driven resume building
                </p>
            </div>

            <div class="glass-panel p-8 fade-in">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <!-- Demo Interface -->
                    <div class="demo-section p-6">
                        <h3 class="text-xl font-semibold mb-4" style="color: var(--text-primary)">Interactive Resume Builder</h3>
                        <div class="space-y-4">
                            <div class="glass-card p-4 border-l-4 border-blue-500">
                                <h4 class="font-semibold" style="color: var(--text-primary)">Professional Summary</h4>
                                <p class="text-sm mt-1" style="color: var(--text-secondary)">AI-generated content based on your experience</p>
                                <div class="mt-2 text-xs text-blue-600 glass-input px-2 py-1 inline-block">✨ Enhanced by AI</div>
                            </div>
                            <div class="glass-card p-4 border-l-4 border-green-500">
                                <h4 class="font-semibold" style="color: var(--text-primary)">Work Experience</h4>
                                <p class="text-sm mt-1" style="color: var(--text-secondary)">Optimized bullet points with impact metrics</p>
                                <div class="mt-2 text-xs text-green-600 glass-input px-2 py-1 inline-block">✓ ATS Optimized</div>
                            </div>
                            <div class="glass-card p-4 border-l-4 border-purple-500">
                                <h4 class="font-semibold" style="color: var(--text-primary)">Skills & Keywords</h4>
                                <p class="text-sm mt-1" style="color: var(--text-secondary)">Industry-relevant skills automatically suggested</p>
                                <div class="mt-2 text-xs text-purple-600 glass-input px-2 py-1 inline-block">🎯 Job-Matched</div>
                            </div>
                        </div>
                    </div>

                    <!-- Features Demo -->
                    <div class="demo-section p-6">
                        <h3 class="text-xl font-semibold mb-4" style="color: var(--text-primary)">Key Features Demo</h3>
                        <div class="space-y-6">
                            <div class="flex items-start space-x-3">
                                <div class="w-8 h-8 glass-card bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center">
                                    <i data-lucide="zap" class="w-4 h-4 text-white"></i>
                                </div>
                                <div>
                                    <h4 class="font-semibold" style="color: var(--text-primary)">AI Content Generation</h4>
                                    <p class="text-sm" style="color: var(--text-secondary)">Generate professional content in seconds</p>
                                </div>
                            </div>
                            <div class="flex items-start space-x-3">
                                <div class="w-8 h-8 glass-card bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center">
                                    <i data-lucide="shield" class="w-4 h-4 text-white"></i>
                                </div>
                                <div>
                                    <h4 class="font-semibold" style="color: var(--text-primary)">ATS Optimization</h4>
                                    <p class="text-sm" style="color: var(--text-secondary)">95%+ ATS compatibility rate</p>
                                </div>
                            </div>
                            <div class="flex items-start space-x-3">
                                <div class="w-8 h-8 glass-card bg-gradient-to-br from-purple-500 to-purple-600 rounded-full flex items-center justify-center">
                                    <i data-lucide="eye" class="w-4 h-4 text-white"></i>
                                </div>
                                <div>
                                    <h4 class="font-semibold" style="color: var(--text-primary)">Real-time Preview</h4>
                                    <p class="text-sm" style="color: var(--text-secondary)">See changes instantly as you build</p>
                                </div>
                            </div>
                            <div class="flex items-start space-x-3">
                                <div class="w-8 h-8 glass-card bg-gradient-to-br from-orange-500 to-orange-600 rounded-full flex items-center justify-center">
                                    <i data-lucide="download" class="w-4 h-4 text-white"></i>
                                </div>
                                <div>
                                    <h4 class="font-semibold" style="color: var(--text-primary)">Multiple Formats</h4>
                                    <p class="text-sm" style="color: var(--text-secondary)">Export as PDF, DOCX, or HTML</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="py-20 gradient-bg text-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <div class="fade-in">
                <h2 class="text-3xl md:text-4xl font-bold mb-4">
                    Ready to Build Your Perfect Resume?
                </h2>
                <p class="text-xl text-blue-100 mb-8">
                    Join thousands of professionals who landed their dream jobs with CareerCraft
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <button class="bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors shadow-lg">
                        Start Building Free
                    </button>
                    <button class="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-colors">
                        View Templates
                    </button>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div>
                    <h3 class="text-xl font-bold mb-4">
                        <span class="text-blue-400">Career</span>Craft
                    </h3>
                    <p class="text-gray-400 mb-4">
                        AI-powered resume platform helping professionals land their dream jobs.
                    </p>
                    <div class="flex space-x-4">
                        <a href="#" class="text-gray-400 hover:text-white transition-colors">
                            <i data-lucide="twitter" class="w-5 h-5"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white transition-colors">
                            <i data-lucide="linkedin" class="w-5 h-5"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white transition-colors">
                            <i data-lucide="facebook" class="w-5 h-5"></i>
                        </a>
                    </div>
                </div>
                <div>
                    <h4 class="font-semibold mb-4">Product</h4>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="#" class="hover:text-white transition-colors">AI Resume Builder</a></li>
                        <li><a href="#" class="hover:text-white transition-colors">Cover Letters & Portfolios</a></li>
                        <li><a href="#" class="hover:text-white transition-colors">Career Intelligence Engine</a></li>
                        <li><a href="#" class="hover:text-white transition-colors">Browser Extension</a></li>
                        <li><a href="#" class="hover:text-white transition-colors">Mobile Apps</a></li>
                        <li><a href="#" class="hover:text-white transition-colors">Template Marketplace</a></li>
                        <li><a href="#" class="hover:text-white transition-colors">Collaboration Platform</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-semibold mb-4">Resources</h4>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="https://onlinejobsearchhelp.com" class="hover:text-white transition-colors">Career Guide</a></li>
                        <li><a href="https://onlinejobsearchhelp.com" class="hover:text-white transition-colors">Resume Examples</a></li>
                        <li><a href="https://onlinejobsearchhelp.com" class="hover:text-white transition-colors">Job Search Tips</a></li>
                        <li><a href="https://onlinejobsearchhelp.com" class="hover:text-white transition-colors">Blog</a></li>
                        <li><a href="#" class="hover:text-white transition-colors">Templates</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-semibold mb-4">Support</h4>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="#" class="hover:text-white transition-colors">Help Center</a></li>
                        <li><a href="#" class="hover:text-white transition-colors">Contact Us</a></li>
                        <li><a href="#" class="hover:text-white transition-colors">Privacy Policy</a></li>
                        <li><a href="#" class="hover:text-white transition-colors">Terms of Service</a></li>
                        <li><a href="#" class="hover:text-white transition-colors">API Documentation</a></li>
                    </ul>
                </div>
            </div>
            <div class="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
                <p>&copy; 2024 CareerCraft. All rights reserved. | Hosted at <a href="https://careercraft.onlinejobsearchhelp.com" class="text-blue-400 hover:text-blue-300">careercraft.onlinejobsearchhelp.com</a></p>
                <p class="mt-2 text-sm">Part of the <a href="https://onlinejobsearchhelp.com" class="text-blue-400 hover:text-blue-300">OnlineJobSearchHelp.com</a> network</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script>
        // Initialize Lucide icons
        lucide.createIcons();

        // Dark Mode Toggle Functionality
        function initThemeToggle() {
            const themeToggle = document.getElementById('theme-toggle');
            const lightIcon = document.querySelector('.light-icon');
            const darkIcon = document.querySelector('.dark-icon');

            // Check for saved theme preference or default to light mode
            const currentTheme = localStorage.getItem('theme') || 'light';
            document.documentElement.setAttribute('data-theme', currentTheme);

            // Update icon visibility
            if (currentTheme === 'dark') {
                lightIcon.classList.add('hidden');
                darkIcon.classList.remove('hidden');
            } else {
                lightIcon.classList.remove('hidden');
                darkIcon.classList.add('hidden');
            }

            // Theme toggle event listener
            themeToggle.addEventListener('click', function() {
                const currentTheme = document.documentElement.getAttribute('data-theme');
                const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

                // Update theme
                document.documentElement.setAttribute('data-theme', newTheme);
                localStorage.setItem('theme', newTheme);

                // Update icons with animation
                if (newTheme === 'dark') {
                    lightIcon.classList.add('hidden');
                    darkIcon.classList.remove('hidden');
                } else {
                    lightIcon.classList.remove('hidden');
                    darkIcon.classList.add('hidden');
                }

                // Add a subtle animation to the toggle button
                themeToggle.style.transform = 'scale(0.9)';
                setTimeout(() => {
                    themeToggle.style.transform = 'scale(1)';
                }, 150);

                // Track theme change
                gtag('event', 'theme_change', {
                    'event_category': 'engagement',
                    'event_label': newTheme
                });
            });
        }

        // Loading screen
        window.addEventListener('load', function() {
            const loadingScreen = document.getElementById('loading-screen');
            setTimeout(() => {
                loadingScreen.style.opacity = '0';
                setTimeout(() => {
                    loadingScreen.style.display = 'none';
                }, 300);
            }, 1000);

            // Initialize theme toggle after loading
            initThemeToggle();
        });

        // Mobile menu toggle
        const mobileMenuBtn = document.getElementById('mobile-menu-btn');
        const mobileMenu = document.getElementById('mobile-menu');

        mobileMenuBtn.addEventListener('click', function() {
            mobileMenu.classList.toggle('hidden');
        });

        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
                // Close mobile menu if open
                mobileMenu.classList.add('hidden');
            });
        });

        // Fade in animation on scroll
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('visible');
                }
            });
        }, observerOptions);

        // Observe all fade-in elements
        document.querySelectorAll('.fade-in').forEach(el => {
            observer.observe(el);
        });

        // Interactive demo functionality
        document.addEventListener('DOMContentLoaded', function() {
            const demoElements = document.querySelectorAll('.demo-section');
            demoElements.forEach(element => {
                element.addEventListener('mouseenter', function() {
                    this.style.transform = 'scale(1.02)';
                    this.style.transition = 'transform 0.3s ease';
                });
                element.addEventListener('mouseleave', function() {
                    this.style.transform = 'scale(1)';
                });
            });

            // Feature card hover effects
            const featureCards = document.querySelectorAll('.feature-card');
            featureCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-8px)';
                });
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(-4px)';
                });
            });

            // Pricing card interactions
            const pricingCards = document.querySelectorAll('.pricing-card');
            pricingCards.forEach(card => {
                card.addEventListener('click', function() {
                    // Add click animation
                    this.style.transform = 'scale(0.98)';
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 150);
                });
            });
        });

        // Button click handlers
        document.getElementById('get-started-btn').addEventListener('click', function() {
            // Track button click
            gtag('event', 'click', {
                'event_category': 'engagement',
                'event_label': 'header_get_started'
            });

            // Scroll to pricing or redirect to app
            document.getElementById('pricing').scrollIntoView({ behavior: 'smooth' });
        });

        document.getElementById('hero-start-btn').addEventListener('click', function() {
            gtag('event', 'click', {
                'event_category': 'engagement',
                'event_label': 'hero_start_building'
            });

            // Redirect to app or show modal
            alert('Welcome to CareerCraft! The full application is coming soon. For now, explore our features below.');
        });

        document.getElementById('hero-demo-btn').addEventListener('click', function() {
            gtag('event', 'click', {
                'event_category': 'engagement',
                'event_label': 'hero_watch_demo'
            });

            document.getElementById('demo').scrollIntoView({ behavior: 'smooth' });
        });

        // Performance optimization
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', function() {
                navigator.serviceWorker.register('/sw.js').then(function(registration) {
                    console.log('ServiceWorker registration successful');
                }, function(err) {
                    console.log('ServiceWorker registration failed');
                });
            });
        }

        // Error handling
        window.addEventListener('error', function(e) {
            console.error('JavaScript error:', e.error);
            // You could send this to an error tracking service
        });
    </script>

    <!-- Schema.org for better SEO -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "SoftwareApplication",
        "name": "CareerCraft",
        "description": "AI-powered resume builder with professional templates, ATS optimization, and comprehensive career tools",
        "url": "https://careercraft.onlinejobsearchhelp.com",
        "applicationCategory": "BusinessApplication",
        "operatingSystem": "Web Browser",
        "offers": [
            {
                "@type": "Offer",
                "name": "Free Plan",
                "price": "0",
                "priceCurrency": "USD"
            },
            {
                "@type": "Offer",
                "name": "Pro Plan",
                "price": "19",
                "priceCurrency": "USD"
            }
        ],
        "aggregateRating": {
            "@type": "AggregateRating",
            "ratingValue": "4.8",
            "ratingCount": "1250"
        },
        "creator": {
            "@type": "Organization",
            "name": "CareerCraft",
            "url": "https://careercraft.onlinejobsearchhelp.com"
        }
    }
    </script>
</body>
</html>
