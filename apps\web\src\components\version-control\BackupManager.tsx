'use client'

import { useState, useEffect } from 'react'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import { 
  Archive, 
  Download, 
  Plus, 
  RotateCcw, 
  Calendar,
  Clock,
  AlertTriangle,
  CheckCircle,
  Trash2
} from 'lucide-react'
import { formatDistanceToNow, format } from 'date-fns'
import { toast } from 'sonner'

interface ResumeBackup {
  id: string
  resumeId: string
  backupName?: string
  backupType: string
  createdBy: string
  createdAt: Date
  expiresAt?: Date
  metadata?: any
  creator: {
    id: string
    name: string | null
    image: string | null
  }
}

interface BackupManagerProps {
  resumeId: string
  className?: string
}

export function BackupManager({ resumeId, className }: BackupManagerProps) {
  const [backups, setBackups] = useState<ResumeBackup[]>([])
  const [loading, setLoading] = useState(true)
  const [createDialog, setCreateDialog] = useState(false)
  const [restoreDialog, setRestoreDialog] = useState<ResumeBackup | null>(null)
  const [isCreating, setIsCreating] = useState(false)
  const [isRestoring, setIsRestoring] = useState(false)
  
  // Create backup form state
  const [backupName, setBackupName] = useState('')
  const [backupType, setBackupType] = useState<'manual' | 'auto' | 'scheduled'>('manual')
  const [expiresAt, setExpiresAt] = useState('')

  useEffect(() => {
    loadBackups()
  }, [resumeId])

  const loadBackups = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/version-control/backups?action=list&resumeId=${resumeId}`)
      
      if (!response.ok) {
        throw new Error('Failed to load backups')
      }

      const data = await response.json()
      setBackups(data.backups.map((b: any) => ({
        ...b,
        createdAt: new Date(b.createdAt),
        expiresAt: b.expiresAt ? new Date(b.expiresAt) : undefined
      })))
    } catch (error) {
      console.error('Error loading backups:', error)
      toast.error('Failed to load backups')
    } finally {
      setLoading(false)
    }
  }

  const handleCreateBackup = async () => {
    if (!backupName.trim()) {
      toast.error('Please enter a backup name')
      return
    }

    try {
      setIsCreating(true)
      const response = await fetch('/api/version-control/backups', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'create',
          resumeId,
          backupName: backupName.trim(),
          backupType,
          expiresAt: expiresAt ? new Date(expiresAt).toISOString() : undefined,
          metadata: {
            userInitiated: true,
            description: `Manual backup created by user`
          }
        })
      })

      if (!response.ok) {
        throw new Error('Failed to create backup')
      }

      toast.success('Backup created successfully')
      setCreateDialog(false)
      setBackupName('')
      setExpiresAt('')
      await loadBackups()
    } catch (error) {
      console.error('Error creating backup:', error)
      toast.error('Failed to create backup')
    } finally {
      setIsCreating(false)
    }
  }

  const handleRestoreBackup = async (backup: ResumeBackup) => {
    try {
      setIsRestoring(true)
      const response = await fetch('/api/version-control/backups', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'restore',
          backupId: backup.id
        })
      })

      if (!response.ok) {
        throw new Error('Failed to restore backup')
      }

      toast.success('Backup restored successfully')
      setRestoreDialog(null)
    } catch (error) {
      console.error('Error restoring backup:', error)
      toast.error('Failed to restore backup')
    } finally {
      setIsRestoring(false)
    }
  }

  const getBackupTypeColor = (backupType: string) => {
    switch (backupType) {
      case 'manual': return 'bg-blue-100 text-blue-800'
      case 'auto': return 'bg-green-100 text-green-800'
      case 'scheduled': return 'bg-purple-100 text-purple-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getBackupTypeIcon = (backupType: string) => {
    switch (backupType) {
      case 'manual': return <Archive className="w-3 h-3" />
      case 'auto': return <CheckCircle className="w-3 h-3" />
      case 'scheduled': return <Calendar className="w-3 h-3" />
      default: return <Archive className="w-3 h-3" />
    }
  }

  const isExpired = (backup: ResumeBackup) => {
    return backup.expiresAt && backup.expiresAt < new Date()
  }

  const isExpiringSoon = (backup: ResumeBackup) => {
    if (!backup.expiresAt) return false
    const threeDaysFromNow = new Date()
    threeDaysFromNow.setDate(threeDaysFromNow.getDate() + 3)
    return backup.expiresAt < threeDaysFromNow && backup.expiresAt > new Date()
  }

  if (loading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Archive className="w-5 h-5" />
            <span>Backup Manager</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="animate-pulse">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-gray-200 rounded-full" />
                  <div className="flex-1">
                    <div className="h-4 bg-gray-200 rounded w-3/4 mb-2" />
                    <div className="h-3 bg-gray-200 rounded w-1/2" />
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <>
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Archive className="w-5 h-5" />
              <span>Backup Manager</span>
              <Badge variant="secondary">{backups.length}</Badge>
            </div>
            <Dialog open={createDialog} onOpenChange={setCreateDialog}>
              <DialogTrigger asChild>
                <Button size="sm" className="flex items-center space-x-1">
                  <Plus className="w-4 h-4" />
                  <span>Create Backup</span>
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Create New Backup</DialogTitle>
                  <DialogDescription>
                    Create a backup of your current resume state
                  </DialogDescription>
                </DialogHeader>
                
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="backup-name">Backup Name</Label>
                    <Input
                      id="backup-name"
                      placeholder="Enter backup name..."
                      value={backupName}
                      onChange={(e) => setBackupName(e.target.value)}
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="backup-type">Backup Type</Label>
                    <Select value={backupType} onValueChange={(value: any) => setBackupType(value)}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="manual">Manual</SelectItem>
                        <SelectItem value="auto">Automatic</SelectItem>
                        <SelectItem value="scheduled">Scheduled</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div>
                    <Label htmlFor="expires-at">Expiration Date (Optional)</Label>
                    <Input
                      id="expires-at"
                      type="datetime-local"
                      value={expiresAt}
                      onChange={(e) => setExpiresAt(e.target.value)}
                    />
                  </div>
                </div>
                
                <DialogFooter>
                  <Button
                    variant="outline"
                    onClick={() => setCreateDialog(false)}
                    disabled={isCreating}
                  >
                    Cancel
                  </Button>
                  <Button onClick={handleCreateBackup} disabled={isCreating}>
                    {isCreating ? (
                      <>
                        <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                        Creating...
                      </>
                    ) : (
                      <>
                        <Archive className="w-4 h-4 mr-2" />
                        Create Backup
                      </>
                    )}
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ScrollArea className="h-96">
            <div className="space-y-3">
              {backups.map((backup) => {
                const expired = isExpired(backup)
                const expiringSoon = isExpiringSoon(backup)

                return (
                  <div
                    key={backup.id}
                    className={`p-3 rounded-lg border transition-colors ${
                      expired 
                        ? 'border-red-200 bg-red-50' 
                        : expiringSoon
                        ? 'border-orange-200 bg-orange-50'
                        : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                    }`}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex items-start space-x-3 flex-1">
                        <Avatar className="w-8 h-8">
                          <AvatarImage src={backup.creator.image || undefined} />
                          <AvatarFallback>
                            {backup.creator.name?.charAt(0) || 'U'}
                          </AvatarFallback>
                        </Avatar>
                        
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center space-x-2 mb-1">
                            <span className="font-medium text-sm">
                              {backup.backupName || 'Unnamed Backup'}
                            </span>
                            <Badge 
                              variant="secondary" 
                              className={`text-xs ${getBackupTypeColor(backup.backupType)}`}
                            >
                              <div className="flex items-center space-x-1">
                                {getBackupTypeIcon(backup.backupType)}
                                <span className="capitalize">{backup.backupType}</span>
                              </div>
                            </Badge>
                            {expired && (
                              <Badge variant="destructive" className="text-xs">
                                Expired
                              </Badge>
                            )}
                            {expiringSoon && (
                              <Badge variant="outline" className="text-xs border-orange-300 text-orange-700">
                                Expiring Soon
                              </Badge>
                            )}
                          </div>
                          
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                              <span>{backup.creator.name}</span>
                              <span>•</span>
                              <span>{formatDistanceToNow(backup.createdAt, { addSuffix: true })}</span>
                              {backup.expiresAt && (
                                <>
                                  <span>•</span>
                                  <span>
                                    Expires {formatDistanceToNow(backup.expiresAt, { addSuffix: true })}
                                  </span>
                                </>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex items-center space-x-1 ml-2">
                        {!expired && (
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => setRestoreDialog(backup)}
                                >
                                  <RotateCcw className="w-4 h-4" />
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>Restore from backup</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        )}
                      </div>
                    </div>
                  </div>
                )
              })}
              
              {backups.length === 0 && (
                <div className="text-center py-8">
                  <Archive className="w-12 h-12 mx-auto text-gray-400 mb-4" />
                  <p className="text-gray-500">No backups available</p>
                  <p className="text-sm text-gray-400">
                    Create your first backup to protect your resume data
                  </p>
                </div>
              )}
            </div>
          </ScrollArea>
        </CardContent>
      </Card>

      {/* Restore Dialog */}
      <Dialog open={!!restoreDialog} onOpenChange={() => setRestoreDialog(null)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle className="flex items-center space-x-2">
              <AlertTriangle className="w-5 h-5 text-orange-500" />
              <span>Confirm Restore</span>
            </DialogTitle>
            <DialogDescription>
              Are you sure you want to restore from this backup? This will replace your current resume with the backup data.
            </DialogDescription>
          </DialogHeader>
          
          {restoreDialog && (
            <div className="space-y-3">
              <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                <div className="text-sm">
                  <div className="font-medium mb-1">
                    {restoreDialog.backupName || 'Unnamed Backup'}
                  </div>
                  <div className="text-gray-600">
                    Created {format(restoreDialog.createdAt, 'PPpp')} by {restoreDialog.creator.name}
                  </div>
                  {restoreDialog.expiresAt && (
                    <div className="text-gray-600">
                      Expires {format(restoreDialog.expiresAt, 'PPpp')}
                    </div>
                  )}
                </div>
              </div>
              
              <div className="p-3 bg-orange-50 border border-orange-200 rounded-lg">
                <div className="flex items-start space-x-2">
                  <AlertTriangle className="w-4 h-4 text-orange-600 mt-0.5" />
                  <div className="text-sm text-orange-800">
                    <div className="font-medium mb-1">Warning</div>
                    <div>
                      This action will overwrite your current resume. Consider creating a backup of your current state before proceeding.
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
          
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setRestoreDialog(null)}
              disabled={isRestoring}
            >
              Cancel
            </Button>
            <Button
              onClick={() => restoreDialog && handleRestoreBackup(restoreDialog)}
              disabled={isRestoring}
              className="bg-orange-600 hover:bg-orange-700"
            >
              {isRestoring ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                  Restoring...
                </>
              ) : (
                <>
                  <RotateCcw className="w-4 h-4 mr-2" />
                  Confirm Restore
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
}
