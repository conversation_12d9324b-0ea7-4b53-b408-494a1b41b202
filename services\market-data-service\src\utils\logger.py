"""
Logging Configuration for Market Data Service
Provides structured logging with multiple output formats
"""

import logging
import logging.handlers
import sys
import os
from datetime import datetime
from typing import Optional
import json

def setup_logger(name: str, level: Optional[str] = None) -> logging.Logger:
    """Setup structured logger with file and console handlers"""
    
    # Get log level from environment or parameter
    log_level = level or os.getenv('LOG_LEVEL', 'INFO')
    numeric_level = getattr(logging, log_level.upper(), logging.INFO)
    
    # Create logger
    logger = logging.getLogger(name)
    logger.setLevel(numeric_level)
    
    # Avoid duplicate handlers
    if logger.handlers:
        return logger
    
    # Create formatters
    console_formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    file_formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # Console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(numeric_level)
    console_handler.setFormatter(console_formatter)
    logger.addHandler(console_handler)
    
    # File handler (if log directory exists or can be created)
    log_dir = os.getenv('LOG_DIR', 'logs')
    if create_log_directory(log_dir):
        log_file = os.path.join(log_dir, f'{name}.log')
        
        # Rotating file handler (10MB max, keep 5 files)
        file_handler = logging.handlers.RotatingFileHandler(
            log_file,
            maxBytes=10 * 1024 * 1024,  # 10MB
            backupCount=5
        )
        file_handler.setLevel(numeric_level)
        file_handler.setFormatter(file_formatter)
        logger.addHandler(file_handler)
    
    # JSON handler for structured logging (if enabled)
    if os.getenv('ENABLE_JSON_LOGGING', 'false').lower() == 'true':
        json_handler = JSONHandler()
        json_handler.setLevel(numeric_level)
        logger.addHandler(json_handler)
    
    return logger

def create_log_directory(log_dir: str) -> bool:
    """Create log directory if it doesn't exist"""
    try:
        os.makedirs(log_dir, exist_ok=True)
        return True
    except (OSError, PermissionError):
        return False

class JSONHandler(logging.Handler):
    """Custom handler for JSON-formatted logs"""
    
    def __init__(self):
        super().__init__()
        self.log_file = os.path.join(
            os.getenv('LOG_DIR', 'logs'),
            'market_data_service.json'
        )
    
    def emit(self, record):
        """Emit a log record in JSON format"""
        try:
            log_entry = {
                'timestamp': datetime.fromtimestamp(record.created).isoformat(),
                'level': record.levelname,
                'logger': record.name,
                'message': record.getMessage(),
                'module': record.module,
                'function': record.funcName,
                'line': record.lineno,
                'process_id': os.getpid(),
                'thread_id': record.thread
            }
            
            # Add exception info if present
            if record.exc_info:
                log_entry['exception'] = self.format(record)
            
            # Add extra fields if present
            if hasattr(record, 'extra_fields'):
                log_entry.update(record.extra_fields)
            
            # Write to file
            with open(self.log_file, 'a') as f:
                f.write(json.dumps(log_entry) + '\n')
                
        except Exception:
            self.handleError(record)

class StructuredLogger:
    """Wrapper for structured logging with additional context"""
    
    def __init__(self, logger: logging.Logger):
        self.logger = logger
        self.context = {}
    
    def add_context(self, **kwargs):
        """Add context fields to all log messages"""
        self.context.update(kwargs)
    
    def clear_context(self):
        """Clear all context fields"""
        self.context.clear()
    
    def _log_with_context(self, level: int, message: str, **kwargs):
        """Log message with context"""
        extra_fields = {**self.context, **kwargs}
        
        # Create log record with extra fields
        record = self.logger.makeRecord(
            self.logger.name,
            level,
            '',  # pathname
            0,   # lineno
            message,
            (),  # args
            None,  # exc_info
            extra={'extra_fields': extra_fields}
        )
        
        self.logger.handle(record)
    
    def debug(self, message: str, **kwargs):
        """Log debug message with context"""
        self._log_with_context(logging.DEBUG, message, **kwargs)
    
    def info(self, message: str, **kwargs):
        """Log info message with context"""
        self._log_with_context(logging.INFO, message, **kwargs)
    
    def warning(self, message: str, **kwargs):
        """Log warning message with context"""
        self._log_with_context(logging.WARNING, message, **kwargs)
    
    def error(self, message: str, **kwargs):
        """Log error message with context"""
        self._log_with_context(logging.ERROR, message, **kwargs)
    
    def critical(self, message: str, **kwargs):
        """Log critical message with context"""
        self._log_with_context(logging.CRITICAL, message, **kwargs)

def get_structured_logger(name: str) -> StructuredLogger:
    """Get a structured logger instance"""
    base_logger = setup_logger(name)
    return StructuredLogger(base_logger)

# Performance logging utilities
class PerformanceLogger:
    """Logger for performance metrics and timing"""
    
    def __init__(self, logger: logging.Logger):
        self.logger = logger
        self.timers = {}
    
    def start_timer(self, operation: str):
        """Start timing an operation"""
        self.timers[operation] = datetime.now()
    
    def end_timer(self, operation: str, **context):
        """End timing and log duration"""
        if operation in self.timers:
            start_time = self.timers.pop(operation)
            duration = (datetime.now() - start_time).total_seconds()
            
            self.logger.info(
                f"Operation '{operation}' completed in {duration:.2f}s",
                extra={
                    'extra_fields': {
                        'operation': operation,
                        'duration_seconds': duration,
                        **context
                    }
                }
            )
        else:
            self.logger.warning(f"Timer for operation '{operation}' not found")
    
    def log_metrics(self, operation: str, metrics: dict):
        """Log performance metrics"""
        self.logger.info(
            f"Metrics for '{operation}': {metrics}",
            extra={
                'extra_fields': {
                    'operation': operation,
                    'metrics': metrics
                }
            }
        )

# Error tracking utilities
class ErrorTracker:
    """Track and log errors with context"""
    
    def __init__(self, logger: logging.Logger):
        self.logger = logger
        self.error_counts = {}
    
    def log_error(self, error: Exception, context: dict = None):
        """Log error with full context"""
        error_type = type(error).__name__
        error_message = str(error)
        
        # Track error frequency
        self.error_counts[error_type] = self.error_counts.get(error_type, 0) + 1
        
        self.logger.error(
            f"{error_type}: {error_message}",
            exc_info=True,
            extra={
                'extra_fields': {
                    'error_type': error_type,
                    'error_count': self.error_counts[error_type],
                    'context': context or {}
                }
            }
        )
    
    def get_error_summary(self) -> dict:
        """Get summary of tracked errors"""
        return dict(self.error_counts)

# Scraping-specific logging utilities
def log_scraping_start(logger: logging.Logger, scraper: str, search_terms: list, locations: list):
    """Log start of scraping operation"""
    logger.info(
        f"Starting {scraper} scraping",
        extra={
            'extra_fields': {
                'scraper': scraper,
                'search_terms': search_terms,
                'locations': locations,
                'operation': 'scraping_start'
            }
        }
    )

def log_scraping_result(logger: logging.Logger, scraper: str, jobs_found: int, duration: float, errors: list = None):
    """Log result of scraping operation"""
    logger.info(
        f"{scraper} scraping completed: {jobs_found} jobs in {duration:.2f}s",
        extra={
            'extra_fields': {
                'scraper': scraper,
                'jobs_found': jobs_found,
                'duration_seconds': duration,
                'errors': errors or [],
                'operation': 'scraping_complete'
            }
        }
    )

def log_job_processing(logger: logging.Logger, job_title: str, company: str, processing_steps: dict):
    """Log job processing details"""
    logger.debug(
        f"Processing job: {job_title} at {company}",
        extra={
            'extra_fields': {
                'job_title': job_title,
                'company': company,
                'processing_steps': processing_steps,
                'operation': 'job_processing'
            }
        }
    )
