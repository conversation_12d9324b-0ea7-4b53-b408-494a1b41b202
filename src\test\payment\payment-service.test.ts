/**
 * Payment Service Unit Tests
 * 
 * Comprehensive test suite for payment processing,
 * subscription management, and billing operations.
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { PaymentService } from '../../services/payment/payment-service'
import { SubscriptionService } from '../../services/payment/subscription-service'
import { UsageService } from '../../services/payment/usage-service'
import { StripeService } from '../../services/external/stripe-service'
import { DatabaseService } from '../../services/database/database-service'

// Mock external dependencies
vi.mock('../../services/external/stripe-service')
vi.mock('../../services/database/database-service')
vi.mock('../../services/notification/email-service')

describe('PaymentService', () => {
  let paymentService: PaymentService
  let mockStripeService: any
  let mockDatabaseService: any

  beforeEach(() => {
    mockStripeService = {
      createCustomer: vi.fn(),
      createSubscription: vi.fn(),
      updateSubscription: vi.fn(),
      cancelSubscription: vi.fn(),
      createPaymentIntent: vi.fn(),
      confirmPaymentIntent: vi.fn(),
      retrievePaymentIntent: vi.fn()
    }

    mockDatabaseService = {
      subscriptions: {
        create: vi.fn(),
        findById: vi.fn(),
        update: vi.fn(),
        delete: vi.fn()
      },
      payments: {
        create: vi.fn(),
        findBySubscriptionId: vi.fn()
      },
      users: {
        findById: vi.fn(),
        update: vi.fn()
      }
    }

    paymentService = new PaymentService(mockStripeService, mockDatabaseService)
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('createSubscription', () => {
    it('should create subscription successfully', async () => {
      const userId = 'user-123'
      const planId = 'premium-monthly'
      const paymentMethodId = 'pm_card_visa'

      const mockUser = {
        id: userId,
        email: '<EMAIL>',
        name: 'Test User'
      }

      const mockPlan = {
        id: planId,
        name: 'Premium Monthly',
        stripePriceId: 'price_premium_monthly',
        priceCents: 999,
        currency: 'usd',
        billingInterval: 'monthly'
      }

      const mockStripeCustomer = {
        id: 'cus_stripe_123',
        email: '<EMAIL>'
      }

      const mockStripeSubscription = {
        id: 'sub_stripe_123',
        status: 'active',
        current_period_start: 1640995200,
        current_period_end: 1643673600,
        latest_invoice: {
          payment_intent: {
            status: 'succeeded'
          }
        }
      }

      mockDatabaseService.users.findById.mockResolvedValue(mockUser)
      mockDatabaseService.subscriptions.findByPlanId = vi.fn().mockResolvedValue(mockPlan)
      mockStripeService.createCustomer.mockResolvedValue(mockStripeCustomer)
      mockStripeService.createSubscription.mockResolvedValue(mockStripeSubscription)
      mockDatabaseService.subscriptions.create.mockResolvedValue({
        id: 'sub-123',
        userId,
        planId,
        stripeSubscriptionId: 'sub_stripe_123',
        status: 'active'
      })

      const result = await paymentService.createSubscription({
        userId,
        planId,
        paymentMethodId
      })

      expect(result.success).toBe(true)
      expect(result.subscription).toBeDefined()
      expect(result.subscription.status).toBe('active')
      expect(mockStripeService.createCustomer).toHaveBeenCalledWith({
        email: mockUser.email,
        name: mockUser.name,
        payment_method: paymentMethodId
      })
      expect(mockStripeService.createSubscription).toHaveBeenCalledWith({
        customer: mockStripeCustomer.id,
        items: [{ price: mockPlan.stripePriceId }],
        payment_behavior: 'default_incomplete',
        expand: ['latest_invoice.payment_intent']
      })
    })

    it('should handle payment failure during subscription creation', async () => {
      const userId = 'user-123'
      const planId = 'premium-monthly'
      const paymentMethodId = 'pm_card_declined'

      mockDatabaseService.users.findById.mockResolvedValue({
        id: userId,
        email: '<EMAIL>'
      })
      mockDatabaseService.subscriptions.findByPlanId = vi.fn().mockResolvedValue({
        stripePriceId: 'price_premium_monthly'
      })
      mockStripeService.createCustomer.mockResolvedValue({ id: 'cus_123' })
      mockStripeService.createSubscription.mockRejectedValue(
        new Error('Your card was declined.')
      )

      const result = await paymentService.createSubscription({
        userId,
        planId,
        paymentMethodId
      })

      expect(result.success).toBe(false)
      expect(result.error).toContain('card was declined')
      expect(mockDatabaseService.subscriptions.create).not.toHaveBeenCalled()
    })

    it('should handle trial period correctly', async () => {
      const userId = 'user-123'
      const planId = 'premium-monthly'
      const paymentMethodId = 'pm_card_visa'
      const trialDays = 14

      mockDatabaseService.users.findById.mockResolvedValue({
        id: userId,
        email: '<EMAIL>'
      })
      mockDatabaseService.subscriptions.findByPlanId = vi.fn().mockResolvedValue({
        stripePriceId: 'price_premium_monthly'
      })
      mockStripeService.createCustomer.mockResolvedValue({ id: 'cus_123' })
      mockStripeService.createSubscription.mockResolvedValue({
        id: 'sub_123',
        status: 'trialing',
        trial_end: Math.floor(Date.now() / 1000) + (trialDays * 24 * 60 * 60)
      })

      const result = await paymentService.createSubscription({
        userId,
        planId,
        paymentMethodId,
        trialDays
      })

      expect(result.success).toBe(true)
      expect(mockStripeService.createSubscription).toHaveBeenCalledWith(
        expect.objectContaining({
          trial_period_days: trialDays
        })
      )
    })
  })

  describe('updateSubscription', () => {
    it('should upgrade subscription plan successfully', async () => {
      const subscriptionId = 'sub-123'
      const newPlanId = 'enterprise-monthly'

      const mockSubscription = {
        id: subscriptionId,
        userId: 'user-123',
        planId: 'premium-monthly',
        stripeSubscriptionId: 'sub_stripe_123',
        status: 'active'
      }

      const mockNewPlan = {
        id: newPlanId,
        stripePriceId: 'price_enterprise_monthly',
        priceCents: 2999
      }

      const mockUpdatedStripeSubscription = {
        id: 'sub_stripe_123',
        status: 'active',
        items: {
          data: [{ price: { id: 'price_enterprise_monthly' } }]
        }
      }

      mockDatabaseService.subscriptions.findById.mockResolvedValue(mockSubscription)
      mockDatabaseService.subscriptions.findByPlanId = vi.fn().mockResolvedValue(mockNewPlan)
      mockStripeService.updateSubscription.mockResolvedValue(mockUpdatedStripeSubscription)
      mockDatabaseService.subscriptions.update.mockResolvedValue({
        ...mockSubscription,
        planId: newPlanId
      })

      const result = await paymentService.updateSubscription(subscriptionId, {
        planId: newPlanId
      })

      expect(result.success).toBe(true)
      expect(result.subscription.planId).toBe(newPlanId)
      expect(mockStripeService.updateSubscription).toHaveBeenCalledWith(
        'sub_stripe_123',
        expect.objectContaining({
          items: expect.arrayContaining([
            expect.objectContaining({
              price: 'price_enterprise_monthly'
            })
          ]),
          proration_behavior: 'create_prorations'
        })
      )
    })

    it('should handle downgrade with proration', async () => {
      const subscriptionId = 'sub-123'
      const newPlanId = 'basic-monthly'

      mockDatabaseService.subscriptions.findById.mockResolvedValue({
        id: subscriptionId,
        planId: 'premium-monthly',
        stripeSubscriptionId: 'sub_stripe_123'
      })
      mockDatabaseService.subscriptions.findByPlanId = vi.fn().mockResolvedValue({
        stripePriceId: 'price_basic_monthly'
      })
      mockStripeService.updateSubscription.mockResolvedValue({
        id: 'sub_stripe_123',
        status: 'active'
      })

      const result = await paymentService.updateSubscription(subscriptionId, {
        planId: newPlanId,
        prorationBehavior: 'create_prorations'
      })

      expect(result.success).toBe(true)
      expect(mockStripeService.updateSubscription).toHaveBeenCalledWith(
        'sub_stripe_123',
        expect.objectContaining({
          proration_behavior: 'create_prorations'
        })
      )
    })
  })

  describe('cancelSubscription', () => {
    it('should cancel subscription immediately', async () => {
      const subscriptionId = 'sub-123'

      const mockSubscription = {
        id: subscriptionId,
        stripeSubscriptionId: 'sub_stripe_123',
        status: 'active'
      }

      mockDatabaseService.subscriptions.findById.mockResolvedValue(mockSubscription)
      mockStripeService.cancelSubscription.mockResolvedValue({
        id: 'sub_stripe_123',
        status: 'canceled',
        canceled_at: Math.floor(Date.now() / 1000)
      })
      mockDatabaseService.subscriptions.update.mockResolvedValue({
        ...mockSubscription,
        status: 'canceled'
      })

      const result = await paymentService.cancelSubscription(subscriptionId, {
        immediate: true
      })

      expect(result.success).toBe(true)
      expect(result.subscription.status).toBe('canceled')
      expect(mockStripeService.cancelSubscription).toHaveBeenCalledWith(
        'sub_stripe_123',
        { cancel_at_period_end: false }
      )
    })

    it('should schedule cancellation at period end', async () => {
      const subscriptionId = 'sub-123'

      mockDatabaseService.subscriptions.findById.mockResolvedValue({
        id: subscriptionId,
        stripeSubscriptionId: 'sub_stripe_123',
        status: 'active'
      })
      mockStripeService.updateSubscription.mockResolvedValue({
        id: 'sub_stripe_123',
        cancel_at_period_end: true
      })

      const result = await paymentService.cancelSubscription(subscriptionId, {
        immediate: false
      })

      expect(result.success).toBe(true)
      expect(mockStripeService.updateSubscription).toHaveBeenCalledWith(
        'sub_stripe_123',
        { cancel_at_period_end: true }
      )
    })
  })

  describe('processPayment', () => {
    it('should process one-time payment successfully', async () => {
      const paymentData = {
        amount: 1000,
        currency: 'usd',
        paymentMethodId: 'pm_card_visa',
        customerId: 'cus_123'
      }

      const mockPaymentIntent = {
        id: 'pi_123',
        status: 'succeeded',
        amount: 1000,
        currency: 'usd'
      }

      mockStripeService.createPaymentIntent.mockResolvedValue(mockPaymentIntent)
      mockDatabaseService.payments.create.mockResolvedValue({
        id: 'payment-123',
        stripePaymentIntentId: 'pi_123',
        amount: 1000,
        status: 'succeeded'
      })

      const result = await paymentService.processPayment(paymentData)

      expect(result.success).toBe(true)
      expect(result.payment.status).toBe('succeeded')
      expect(mockStripeService.createPaymentIntent).toHaveBeenCalledWith({
        amount: 1000,
        currency: 'usd',
        payment_method: 'pm_card_visa',
        customer: 'cus_123',
        confirm: true
      })
    })

    it('should handle payment failure', async () => {
      const paymentData = {
        amount: 1000,
        currency: 'usd',
        paymentMethodId: 'pm_card_declined'
      }

      mockStripeService.createPaymentIntent.mockRejectedValue(
        new Error('Your card was declined.')
      )

      const result = await paymentService.processPayment(paymentData)

      expect(result.success).toBe(false)
      expect(result.error).toContain('card was declined')
      expect(mockDatabaseService.payments.create).not.toHaveBeenCalled()
    })
  })

  describe('retryFailedPayment', () => {
    it('should retry failed payment successfully', async () => {
      const paymentId = 'payment-123'

      const mockPayment = {
        id: paymentId,
        stripePaymentIntentId: 'pi_123',
        status: 'failed',
        retryCount: 1
      }

      const mockUpdatedPaymentIntent = {
        id: 'pi_123',
        status: 'succeeded'
      }

      mockDatabaseService.payments.findById = vi.fn().mockResolvedValue(mockPayment)
      mockStripeService.confirmPaymentIntent.mockResolvedValue(mockUpdatedPaymentIntent)
      mockDatabaseService.payments.update.mockResolvedValue({
        ...mockPayment,
        status: 'succeeded'
      })

      const result = await paymentService.retryFailedPayment(paymentId)

      expect(result.success).toBe(true)
      expect(result.payment.status).toBe('succeeded')
      expect(mockStripeService.confirmPaymentIntent).toHaveBeenCalledWith('pi_123')
    })

    it('should handle max retry limit', async () => {
      const paymentId = 'payment-123'

      mockDatabaseService.payments.findById = vi.fn().mockResolvedValue({
        id: paymentId,
        retryCount: 5,
        maxRetries: 3
      })

      const result = await paymentService.retryFailedPayment(paymentId)

      expect(result.success).toBe(false)
      expect(result.error).toContain('Maximum retry attempts exceeded')
      expect(mockStripeService.confirmPaymentIntent).not.toHaveBeenCalled()
    })
  })

  describe('getPaymentHistory', () => {
    it('should retrieve payment history for user', async () => {
      const userId = 'user-123'
      const limit = 10
      const offset = 0

      const mockPayments = [
        {
          id: 'payment-1',
          amount: 999,
          currency: 'usd',
          status: 'succeeded',
          createdAt: new Date()
        },
        {
          id: 'payment-2',
          amount: 999,
          currency: 'usd',
          status: 'succeeded',
          createdAt: new Date()
        }
      ]

      mockDatabaseService.payments.findByUserId = vi.fn().mockResolvedValue({
        payments: mockPayments,
        total: 2,
        hasMore: false
      })

      const result = await paymentService.getPaymentHistory(userId, { limit, offset })

      expect(result.success).toBe(true)
      expect(result.payments).toHaveLength(2)
      expect(result.total).toBe(2)
      expect(result.hasMore).toBe(false)
    })
  })

  describe('calculateProration', () => {
    it('should calculate proration for plan upgrade', async () => {
      const subscriptionId = 'sub-123'
      const newPlanId = 'enterprise-monthly'

      const mockSubscription = {
        id: subscriptionId,
        planId: 'premium-monthly',
        currentPeriodStart: new Date('2024-01-01'),
        currentPeriodEnd: new Date('2024-02-01')
      }

      const mockCurrentPlan = {
        priceCents: 999
      }

      const mockNewPlan = {
        priceCents: 2999
      }

      mockDatabaseService.subscriptions.findById.mockResolvedValue(mockSubscription)
      mockDatabaseService.subscriptions.findByPlanId = vi.fn()
        .mockResolvedValueOnce(mockCurrentPlan)
        .mockResolvedValueOnce(mockNewPlan)

      const result = await paymentService.calculateProration(subscriptionId, newPlanId)

      expect(result.success).toBe(true)
      expect(result.proration).toBeDefined()
      expect(result.proration.amount).toBeGreaterThan(0)
      expect(result.proration.isUpgrade).toBe(true)
    })
  })
})
