'use client'

import { DashboardLayout } from '@/components/dashboard/DashboardLayout'
import { TemplateGallery } from '@/components/templates/TemplateGallery'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  Palette, 
  Star, 
  Crown, 
  TrendingUp,
  Users,
  Briefcase,
  Heart,
  Sparkles,
  Download,
  Eye
} from 'lucide-react'

export default function TemplatesPage() {
  const categories = [
    {
      id: 'modern',
      name: 'Modern',
      description: 'Contemporary designs for tech and creative professionals',
      icon: TrendingUp,
      color: 'from-blue-500 to-blue-600',
      count: 12
    },
    {
      id: 'classic',
      name: 'Classic',
      description: 'Traditional and elegant designs for executive positions',
      icon: Briefcase,
      color: 'from-gray-500 to-gray-600',
      count: 8
    },
    {
      id: 'creative',
      name: 'Creative',
      description: 'Bold and artistic designs for creative industries',
      icon: Palette,
      color: 'from-purple-500 to-purple-600',
      count: 15
    },
    {
      id: 'minimal',
      name: 'Minimal',
      description: 'Clean and simple designs that focus on content',
      icon: Heart,
      color: 'from-pink-500 to-pink-600',
      count: 6
    },
    {
      id: 'professional',
      name: 'Professional',
      description: 'Corporate-ready designs for business environments',
      icon: Users,
      color: 'from-green-500 to-green-600',
      count: 10
    }
  ]

  const stats = [
    {
      label: 'Total Templates',
      value: '50+',
      icon: Palette,
      color: 'text-blue-600'
    },
    {
      label: 'Premium Templates',
      value: '25+',
      icon: Crown,
      color: 'text-yellow-600'
    },
    {
      label: 'Popular This Month',
      value: '12',
      icon: Star,
      color: 'text-red-600'
    },
    {
      label: 'New This Week',
      value: '3',
      icon: Sparkles,
      color: 'text-purple-600'
    }
  ]

  return (
    <DashboardLayout currentPage="templates">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              Resume Templates
            </h1>
            <p className="text-muted-foreground mt-1">
              Choose from our collection of professional, ATS-optimized templates
            </p>
          </div>
          
          <div className="flex items-center space-x-2">
            <Button variant="outline" className="glass-input">
              <Download className="w-4 h-4 mr-2" />
              Download All
            </Button>
            <Button variant="outline" className="glass-input">
              <Eye className="w-4 h-4 mr-2" />
              Preview Mode
            </Button>
          </div>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
          {stats.map((stat) => {
            const Icon = stat.icon
            return (
              <Card key={stat.label} className="glass-card">
                <CardContent className="p-4">
                  <div className="flex items-center space-x-3">
                    <div className={`w-10 h-10 rounded-lg bg-gradient-to-br ${stat.color === 'text-blue-600' ? 'from-blue-500 to-blue-600' : stat.color === 'text-yellow-600' ? 'from-yellow-500 to-yellow-600' : stat.color === 'text-red-600' ? 'from-red-500 to-red-600' : 'from-purple-500 to-purple-600'} flex items-center justify-center`}>
                      <Icon className="w-5 h-5 text-white" />
                    </div>
                    <div>
                      <div className="text-2xl font-bold">{stat.value}</div>
                      <div className="text-sm text-muted-foreground">{stat.label}</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )
          })}
        </div>

        {/* Categories */}
        <div className="glass-panel p-6 rounded-2xl">
          <h2 className="text-xl font-semibold mb-4">Browse by Category</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-4">
            {categories.map((category) => {
              const Icon = category.icon
              return (
                <Card key={category.id} className="glass-card hover:scale-105 transition-transform duration-200 cursor-pointer group">
                  <CardContent className="p-4 text-center">
                    <div className={`w-12 h-12 mx-auto mb-3 rounded-lg bg-gradient-to-br ${category.color} flex items-center justify-center group-hover:scale-110 transition-transform`}>
                      <Icon className="w-6 h-6 text-white" />
                    </div>
                    <h3 className="font-semibold mb-1">{category.name}</h3>
                    <p className="text-xs text-muted-foreground mb-2 line-clamp-2">
                      {category.description}
                    </p>
                    <Badge variant="secondary" className="glass-input text-xs">
                      {category.count} templates
                    </Badge>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </div>

        {/* Featured Templates */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold">Featured Templates</h2>
            <Button variant="outline" className="glass-input">
              View All
            </Button>
          </div>
          
          <TemplateGallery />
        </div>

        {/* Tips Section */}
        <div className="glass-panel p-6 rounded-2xl">
          <h3 className="text-lg font-semibold mb-4 flex items-center">
            💡 Template Selection Tips
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="space-y-2">
              <h4 className="font-medium text-blue-600">Match Your Industry</h4>
              <p className="text-sm text-muted-foreground">
                Choose templates that align with your industry standards. Creative fields can use more colorful designs, while corporate roles benefit from classic layouts.
              </p>
            </div>
            <div className="space-y-2">
              <h4 className="font-medium text-green-600">Consider ATS Compatibility</h4>
              <p className="text-sm text-muted-foreground">
                All our templates are ATS-optimized, but simpler designs often perform better with applicant tracking systems.
              </p>
            </div>
            <div className="space-y-2">
              <h4 className="font-medium text-purple-600">Think About Your Experience</h4>
              <p className="text-sm text-muted-foreground">
                Entry-level candidates should focus on clean, simple designs, while experienced professionals can use more sophisticated layouts.
              </p>
            </div>
          </div>
        </div>

        {/* Premium Upgrade */}
        <div className="glass-panel p-6 rounded-2xl bg-gradient-to-r from-yellow-50 to-orange-50 dark:from-yellow-900/10 dark:to-orange-900/10 border-yellow-200 dark:border-yellow-800">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="w-12 h-12 bg-gradient-to-br from-yellow-500 to-orange-500 rounded-lg flex items-center justify-center">
                <Crown className="w-6 h-6 text-white" />
              </div>
              <div>
                <h3 className="text-lg font-semibold">Unlock Premium Templates</h3>
                <p className="text-sm text-muted-foreground">
                  Get access to 25+ exclusive premium templates designed by professionals
                </p>
              </div>
            </div>
            <Button className="glass-card bg-gradient-to-r from-yellow-500 to-orange-500 hover:from-yellow-600 hover:to-orange-600 text-white border-0">
              <Crown className="w-4 h-4 mr-2" />
              Upgrade Now
            </Button>
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}
