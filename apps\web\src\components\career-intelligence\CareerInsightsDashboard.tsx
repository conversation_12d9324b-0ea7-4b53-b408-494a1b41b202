/**
 * Career Insights Dashboard
 * 
 * Main dashboard component for displaying career intelligence insights
 * Implements FR-5.5: Insights Dashboard UI
 */

'use client'

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  TrendingUp, 
  DollarSign, 
  Target, 
  Users, 
  MapPin, 
  Briefcase,
  Star,
  AlertCircle,
  RefreshCw,
  BarChart3,
  PieChart,
  LineChart
} from 'lucide-react'
import { useToast } from '@/hooks/use-toast'

interface CareerInsights {
  salaryEstimate: {
    min: number
    max: number
    confidence: number
    currency: string
  }
  marketFit: {
    score: number
    description: string
    strengths: string[]
    improvements: string[]
  }
  skillGaps: {
    missing: string[]
    recommended: string[]
    trending: string[]
  }
  careerPaths: {
    current: string
    nextSteps: Array<{
      role: string
      probability: number
      requirements: string[]
      timeframe: string
    }>
    alternatives: Array<{
      role: string
      transferableSkills: string[]
      additionalSkills: string[]
    }>
  }
  marketData: {
    totalJobs: number
    averageSalary: number
    topCompanies: string[]
    hotSkills: string[]
    locations: Array<{
      city: string
      jobCount: number
      averageSalary: number
    }>
  }
}

interface CareerInsightsDashboardProps {
  resumeId: string
  onUpgradeRequired?: () => void
}

export function CareerInsightsDashboard({ resumeId, onUpgradeRequired }: CareerInsightsDashboardProps) {
  const [insights, setInsights] = useState<CareerInsights | null>(null)
  const [loading, setLoading] = useState(true)
  const [refreshing, setRefreshing] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const { toast } = useToast()

  useEffect(() => {
    loadCareerInsights()
  }, [resumeId])

  const loadCareerInsights = async () => {
    try {
      setLoading(true)
      setError(null)

      const response = await fetch(`/api/career-intelligence?resumeId=${resumeId}`)
      const data = await response.json()

      if (!response.ok) {
        if (response.status === 403 && data.upgradeRequired) {
          onUpgradeRequired?.()
          return
        }
        throw new Error(data.message || 'Failed to load career insights')
      }

      setInsights(data.data)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load career insights'
      setError(errorMessage)
      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }

  const refreshInsights = async () => {
    try {
      setRefreshing(true)
      
      const response = await fetch('/api/career-intelligence', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ resumeId })
      })

      const data = await response.json()

      if (!response.ok) {
        if (response.status === 403 && data.upgradeRequired) {
          onUpgradeRequired?.()
          return
        }
        throw new Error(data.message || 'Failed to refresh insights')
      }

      setInsights(data.data)
      toast({
        title: 'Success',
        description: 'Career insights refreshed successfully'
      })
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to refresh insights'
      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive'
      })
    } finally {
      setRefreshing(false)
    }
  }

  const formatSalary = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount)
  }

  const getMarketFitColor = (score: number) => {
    if (score >= 0.8) return 'text-green-600'
    if (score >= 0.6) return 'text-yellow-600'
    return 'text-red-600'
  }

  const getMarketFitBadgeVariant = (score: number) => {
    if (score >= 0.8) return 'default'
    if (score >= 0.6) return 'secondary'
    return 'destructive'
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold">Career Insights</h2>
          <Button disabled>
            <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
            Loading...
          </Button>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(6)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader>
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </CardHeader>
              <CardContent>
                <div className="h-8 bg-gray-200 rounded w-full"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  if (error || !insights) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold">Career Insights</h2>
          <Button onClick={loadCareerInsights}>
            <RefreshCw className="w-4 h-4 mr-2" />
            Retry
          </Button>
        </div>
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {error || 'Failed to load career insights. Please try again.'}
          </AlertDescription>
        </Alert>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Career Insights</h2>
          <p className="text-muted-foreground">AI-powered analysis of your career potential</p>
        </div>
        <Button onClick={refreshInsights} disabled={refreshing}>
          <RefreshCw className={`w-4 h-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      {/* Key Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* Salary Estimate */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Salary Estimate</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatSalary(insights.salaryEstimate.min)} - {formatSalary(insights.salaryEstimate.max)}
            </div>
            <p className="text-xs text-muted-foreground">
              {Math.round(insights.salaryEstimate.confidence * 100)}% confidence
            </p>
          </CardContent>
        </Card>

        {/* Market Fit */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Market Fit</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${getMarketFitColor(insights.marketFit.score)}`}>
              {Math.round(insights.marketFit.score * 100)}%
            </div>
            <p className="text-xs text-muted-foreground">
              {insights.marketFit.description}
            </p>
          </CardContent>
        </Card>

        {/* Available Jobs */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Available Jobs</CardTitle>
            <Briefcase className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {insights.marketData.totalJobs.toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">
              Active job postings
            </p>
          </CardContent>
        </Card>

        {/* Current Role */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Current Role</CardTitle>
            <Star className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-lg font-bold truncate">
              {insights.careerPaths.current}
            </div>
            <p className="text-xs text-muted-foreground">
              Primary position
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Analysis Tabs */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="skills">Skills</TabsTrigger>
          <TabsTrigger value="career">Career Paths</TabsTrigger>
          <TabsTrigger value="market">Market Data</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Market Fit Analysis */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Target className="h-5 w-5" />
                  Market Fit Analysis
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span>Overall Score</span>
                    <Badge variant={getMarketFitBadgeVariant(insights.marketFit.score)}>
                      {Math.round(insights.marketFit.score * 100)}%
                    </Badge>
                  </div>
                  <Progress value={insights.marketFit.score * 100} className="h-2" />
                </div>
                
                <div className="space-y-3">
                  <div>
                    <h4 className="font-medium text-green-600 mb-2">Strengths</h4>
                    <ul className="space-y-1">
                      {insights.marketFit.strengths.map((strength, index) => (
                        <li key={index} className="text-sm flex items-center gap-2">
                          <div className="w-1.5 h-1.5 bg-green-500 rounded-full" />
                          {strength}
                        </li>
                      ))}
                    </ul>
                  </div>
                  
                  <div>
                    <h4 className="font-medium text-orange-600 mb-2">Areas for Improvement</h4>
                    <ul className="space-y-1">
                      {insights.marketFit.improvements.map((improvement, index) => (
                        <li key={index} className="text-sm flex items-center gap-2">
                          <div className="w-1.5 h-1.5 bg-orange-500 rounded-full" />
                          {improvement}
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Salary Breakdown */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <DollarSign className="h-5 w-5" />
                  Salary Analysis
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Minimum</span>
                    <span className="font-medium">{formatSalary(insights.salaryEstimate.min)}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Maximum</span>
                    <span className="font-medium">{formatSalary(insights.salaryEstimate.max)}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Market Average</span>
                    <span className="font-medium">{formatSalary(insights.marketData.averageSalary)}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Confidence Level</span>
                    <Badge variant="outline">
                      {Math.round(insights.salaryEstimate.confidence * 100)}%
                    </Badge>
                  </div>
                </div>
                
                <div className="pt-4 border-t">
                  <p className="text-sm text-muted-foreground">
                    Based on {insights.marketData.totalJobs.toLocaleString()} job postings and market data analysis.
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Skills Tab */}
        <TabsContent value="skills" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Skill Gaps */}
            <Card>
              <CardHeader>
                <CardTitle>Skill Gaps</CardTitle>
                <CardDescription>
                  Skills that could improve your market competitiveness
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {insights.skillGaps.missing.length > 0 ? (
                  <div>
                    <h4 className="font-medium mb-2">Missing Skills</h4>
                    <div className="flex flex-wrap gap-2">
                      {insights.skillGaps.missing.map((skill, index) => (
                        <Badge key={index} variant="destructive">
                          {skill}
                        </Badge>
                      ))}
                    </div>
                  </div>
                ) : (
                  <p className="text-sm text-muted-foreground">
                    Great! You have most of the trending skills for your role.
                  </p>
                )}
                
                <div>
                  <h4 className="font-medium mb-2">Recommended Skills</h4>
                  <div className="flex flex-wrap gap-2">
                    {insights.skillGaps.recommended.map((skill, index) => (
                      <Badge key={index} variant="secondary">
                        {skill}
                      </Badge>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Trending Skills */}
            <Card>
              <CardHeader>
                <CardTitle>Trending Skills</CardTitle>
                <CardDescription>
                  Most in-demand skills in your field
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-2">
                  {insights.skillGaps.trending.map((skill, index) => (
                    <Badge key={index} variant="outline">
                      {skill}
                    </Badge>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Career Paths Tab */}
        <TabsContent value="career" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Next Steps */}
            <Card>
              <CardHeader>
                <CardTitle>Career Progression</CardTitle>
                <CardDescription>
                  Recommended next steps in your career
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {insights.careerPaths.nextSteps.map((step, index) => (
                  <div key={index} className="border rounded-lg p-4 space-y-2">
                    <div className="flex justify-between items-start">
                      <h4 className="font-medium">{step.role}</h4>
                      <Badge variant="outline">
                        {Math.round(step.probability * 100)}% match
                      </Badge>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Timeline: {step.timeframe}
                    </p>
                    <div>
                      <p className="text-sm font-medium mb-1">Requirements:</p>
                      <ul className="text-sm space-y-1">
                        {step.requirements.map((req, reqIndex) => (
                          <li key={reqIndex} className="flex items-center gap-2">
                            <div className="w-1 h-1 bg-gray-400 rounded-full" />
                            {req}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>

            {/* Alternative Paths */}
            <Card>
              <CardHeader>
                <CardTitle>Alternative Career Paths</CardTitle>
                <CardDescription>
                  Other roles that match your skills
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {insights.careerPaths.alternatives.map((alt, index) => (
                  <div key={index} className="border rounded-lg p-4 space-y-2">
                    <h4 className="font-medium">{alt.role}</h4>
                    <div>
                      <p className="text-sm font-medium mb-1">Transferable Skills:</p>
                      <div className="flex flex-wrap gap-1 mb-2">
                        {alt.transferableSkills.map((skill, skillIndex) => (
                          <Badge key={skillIndex} variant="secondary" className="text-xs">
                            {skill}
                          </Badge>
                        ))}
                      </div>
                    </div>
                    <div>
                      <p className="text-sm font-medium mb-1">Skills to Develop:</p>
                      <div className="flex flex-wrap gap-1">
                        {alt.additionalSkills.map((skill, skillIndex) => (
                          <Badge key={skillIndex} variant="outline" className="text-xs">
                            {skill}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Market Data Tab */}
        <TabsContent value="market" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Top Companies */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  Top Hiring Companies
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {insights.marketData.topCompanies.map((company, index) => (
                    <div key={index} className="flex items-center justify-between p-2 rounded border">
                      <span className="font-medium">{company}</span>
                      <Badge variant="outline">#{index + 1}</Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Top Locations */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MapPin className="h-5 w-5" />
                  Top Job Markets
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {insights.marketData.locations.map((location, index) => (
                    <div key={index} className="space-y-1">
                      <div className="flex justify-between items-center">
                        <span className="font-medium">{location.city}</span>
                        <span className="text-sm text-muted-foreground">
                          {formatSalary(location.averageSalary)}
                        </span>
                      </div>
                      <div className="flex justify-between items-center text-sm">
                        <span className="text-muted-foreground">
                          {location.jobCount.toLocaleString()} jobs
                        </span>
                        <Progress 
                          value={(location.jobCount / Math.max(...insights.marketData.locations.map(l => l.jobCount))) * 100} 
                          className="w-20 h-2"
                        />
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
