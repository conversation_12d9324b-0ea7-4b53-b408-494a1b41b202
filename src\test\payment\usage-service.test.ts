/**
 * Usage Service Unit Tests
 * 
 * Comprehensive test suite for usage tracking,
 * limit enforcement, and billing calculations.
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { UsageService } from '../../services/payment/usage-service'
import { SubscriptionService } from '../../services/payment/subscription-service'
import { NotificationService } from '../../services/notification/notification-service'

vi.mock('../../services/payment/subscription-service')
vi.mock('../../services/notification/notification-service')
vi.mock('../../services/database/database-service')

describe('UsageService', () => {
  let usageService: UsageService
  let mockSubscriptionService: any
  let mockNotificationService: any
  let mockDatabaseService: any

  beforeEach(() => {
    mockSubscriptionService = {
      getUserSubscription: vi.fn(),
      getPlanLimits: vi.fn()
    }

    mockNotificationService = {
      sendUsageWarning: vi.fn(),
      sendLimitReachedNotification: vi.fn(),
      sendOverageNotification: vi.fn()
    }

    mockDatabaseService = {
      usage: {
        create: vi.fn(),
        findByUserId: vi.fn(),
        aggregateByPeriod: vi.fn(),
        update: vi.fn()
      },
      subscriptions: {
        findByUserId: vi.fn()
      }
    }

    usageService = new UsageService(
      mockSubscriptionService,
      mockNotificationService,
      mockDatabaseService
    )
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('recordUsage', () => {
    it('should record usage within limits', async () => {
      const userId = 'user-123'
      const featureType = 'autofill'
      const usageCount = 1

      const mockSubscription = {
        id: 'sub-123',
        planId: 'premium-monthly',
        currentPeriodStart: new Date('2024-01-01'),
        currentPeriodEnd: new Date('2024-02-01')
      }

      const mockPlanLimits = {
        applicationsPerMonth: -1, // unlimited
        aiCustomizationsPerMonth: 100
      }

      const mockCurrentUsage = {
        autofillCount: 25,
        aiCustomizationCount: 5
      }

      mockSubscriptionService.getUserSubscription.mockResolvedValue(mockSubscription)
      mockSubscriptionService.getPlanLimits.mockResolvedValue(mockPlanLimits)
      mockDatabaseService.usage.aggregateByPeriod.mockResolvedValue(mockCurrentUsage)
      mockDatabaseService.usage.create.mockResolvedValue({
        id: 'usage-123',
        userId,
        featureType,
        usageCount,
        recordedAt: new Date()
      })

      const result = await usageService.recordUsage(userId, featureType, usageCount)

      expect(result.success).toBe(true)
      expect(result.allowed).toBe(true)
      expect(result.usage.featureType).toBe(featureType)
      expect(mockDatabaseService.usage.create).toHaveBeenCalledWith({
        userId,
        subscriptionId: mockSubscription.id,
        featureType,
        usageCount,
        billingPeriodStart: mockSubscription.currentPeriodStart,
        billingPeriodEnd: mockSubscription.currentPeriodEnd,
        recordedAt: expect.any(Date)
      })
    })

    it('should enforce hard limits', async () => {
      const userId = 'user-123'
      const featureType = 'ai_customization'

      const mockSubscription = {
        planId: 'basic-monthly'
      }

      const mockPlanLimits = {
        applicationsPerMonth: 10,
        aiCustomizationsPerMonth: 0 // not allowed on basic plan
      }

      const mockCurrentUsage = {
        aiCustomizationCount: 0
      }

      mockSubscriptionService.getUserSubscription.mockResolvedValue(mockSubscription)
      mockSubscriptionService.getPlanLimits.mockResolvedValue(mockPlanLimits)
      mockDatabaseService.usage.aggregateByPeriod.mockResolvedValue(mockCurrentUsage)

      const result = await usageService.recordUsage(userId, featureType, 1)

      expect(result.success).toBe(false)
      expect(result.allowed).toBe(false)
      expect(result.reason).toBe('FEATURE_NOT_AVAILABLE')
      expect(mockDatabaseService.usage.create).not.toHaveBeenCalled()
    })

    it('should enforce usage limits', async () => {
      const userId = 'user-123'
      const featureType = 'autofill'

      const mockSubscription = {
        planId: 'free'
      }

      const mockPlanLimits = {
        applicationsPerMonth: 10
      }

      const mockCurrentUsage = {
        autofillCount: 10 // already at limit
      }

      mockSubscriptionService.getUserSubscription.mockResolvedValue(mockSubscription)
      mockSubscriptionService.getPlanLimits.mockResolvedValue(mockPlanLimits)
      mockDatabaseService.usage.aggregateByPeriod.mockResolvedValue(mockCurrentUsage)

      const result = await usageService.recordUsage(userId, featureType, 1)

      expect(result.success).toBe(false)
      expect(result.allowed).toBe(false)
      expect(result.reason).toBe('LIMIT_EXCEEDED')
      expect(result.currentUsage).toBe(10)
      expect(result.limit).toBe(10)
      expect(mockNotificationService.sendLimitReachedNotification).toHaveBeenCalledWith(
        userId,
        featureType,
        10
      )
    })

    it('should send warning at 80% usage', async () => {
      const userId = 'user-123'
      const featureType = 'autofill'

      const mockSubscription = {
        planId: 'free'
      }

      const mockPlanLimits = {
        applicationsPerMonth: 10
      }

      const mockCurrentUsage = {
        autofillCount: 7 // 70% usage, will be 80% after this record
      }

      mockSubscriptionService.getUserSubscription.mockResolvedValue(mockSubscription)
      mockSubscriptionService.getPlanLimits.mockResolvedValue(mockPlanLimits)
      mockDatabaseService.usage.aggregateByPeriod.mockResolvedValue(mockCurrentUsage)
      mockDatabaseService.usage.create.mockResolvedValue({})

      const result = await usageService.recordUsage(userId, featureType, 1)

      expect(result.success).toBe(true)
      expect(result.allowed).toBe(true)
      expect(result.warningTriggered).toBe(true)
      expect(mockNotificationService.sendUsageWarning).toHaveBeenCalledWith(
        userId,
        featureType,
        8,
        10
      )
    })
  })

  describe('checkLimits', () => {
    it('should return current usage and limits', async () => {
      const userId = 'user-123'

      const mockSubscription = {
        planId: 'premium-monthly'
      }

      const mockPlanLimits = {
        applicationsPerMonth: -1,
        aiCustomizationsPerMonth: 100
      }

      const mockCurrentUsage = {
        autofillCount: 45,
        aiCustomizationCount: 12
      }

      mockSubscriptionService.getUserSubscription.mockResolvedValue(mockSubscription)
      mockSubscriptionService.getPlanLimits.mockResolvedValue(mockPlanLimits)
      mockDatabaseService.usage.aggregateByPeriod.mockResolvedValue(mockCurrentUsage)

      const result = await usageService.checkLimits(userId)

      expect(result.limits).toEqual(mockPlanLimits)
      expect(result.usage).toEqual(mockCurrentUsage)
      expect(result.percentageUsed.autofill).toBe(0) // unlimited
      expect(result.percentageUsed.aiCustomization).toBe(12) // 12/100 = 12%
    })

    it('should handle user without subscription', async () => {
      const userId = 'user-456'

      mockSubscriptionService.getUserSubscription.mockResolvedValue(null)

      const result = await usageService.checkLimits(userId)

      expect(result.limits).toEqual({
        applicationsPerMonth: 10, // free tier limits
        aiCustomizationsPerMonth: 0
      })
      expect(result.planType).toBe('free')
    })
  })

  describe('getCurrentUsage', () => {
    it('should return usage for current billing period', async () => {
      const userId = 'user-123'

      const mockSubscription = {
        currentPeriodStart: new Date('2024-01-01'),
        currentPeriodEnd: new Date('2024-02-01')
      }

      const mockUsageData = [
        {
          featureType: 'autofill',
          totalUsage: 25,
          lastUsed: new Date('2024-01-15')
        },
        {
          featureType: 'ai_customization',
          totalUsage: 8,
          lastUsed: new Date('2024-01-20')
        }
      ]

      mockSubscriptionService.getUserSubscription.mockResolvedValue(mockSubscription)
      mockDatabaseService.usage.aggregateByPeriod.mockResolvedValue(mockUsageData)

      const result = await usageService.getCurrentUsage(userId)

      expect(result.autofill).toBe(25)
      expect(result.aiCustomization).toBe(8)
      expect(result.billingPeriod.start).toEqual(mockSubscription.currentPeriodStart)
      expect(result.billingPeriod.end).toEqual(mockSubscription.currentPeriodEnd)
    })
  })

  describe('getUsageHistory', () => {
    it('should return usage history with pagination', async () => {
      const userId = 'user-123'
      const options = {
        limit: 10,
        offset: 0,
        featureType: 'autofill'
      }

      const mockUsageHistory = [
        {
          id: 'usage-1',
          featureType: 'autofill',
          usageCount: 1,
          recordedAt: new Date('2024-01-15'),
          metadata: { jobTitle: 'Software Engineer' }
        },
        {
          id: 'usage-2',
          featureType: 'autofill',
          usageCount: 1,
          recordedAt: new Date('2024-01-14'),
          metadata: { jobTitle: 'Product Manager' }
        }
      ]

      mockDatabaseService.usage.findByUserId.mockResolvedValue({
        usage: mockUsageHistory,
        total: 25,
        hasMore: true
      })

      const result = await usageService.getUsageHistory(userId, options)

      expect(result.usage).toHaveLength(2)
      expect(result.total).toBe(25)
      expect(result.hasMore).toBe(true)
      expect(mockDatabaseService.usage.findByUserId).toHaveBeenCalledWith(
        userId,
        expect.objectContaining(options)
      )
    })
  })

  describe('resetUsage', () => {
    it('should reset usage for new billing period', async () => {
      const userId = 'user-123'
      const newPeriodStart = new Date('2024-02-01')

      mockDatabaseService.usage.update.mockResolvedValue({ affectedRows: 5 })

      const result = await usageService.resetUsage(userId, newPeriodStart)

      expect(result.success).toBe(true)
      expect(result.resetCount).toBe(5)
      expect(mockDatabaseService.usage.update).toHaveBeenCalledWith(
        { userId, billingPeriodStart: { $lt: newPeriodStart } },
        { archived: true, archivedAt: expect.any(Date) }
      )
    })
  })

  describe('calculateOverage', () => {
    it('should calculate overage charges', async () => {
      const userId = 'user-123'

      const mockSubscription = {
        planId: 'premium-monthly'
      }

      const mockPlanLimits = {
        applicationsPerMonth: 100,
        overageRates: {
          autofill: 0.10 // $0.10 per additional application
        }
      }

      const mockCurrentUsage = {
        autofillCount: 125 // 25 over limit
      }

      mockSubscriptionService.getUserSubscription.mockResolvedValue(mockSubscription)
      mockSubscriptionService.getPlanLimits.mockResolvedValue(mockPlanLimits)
      mockDatabaseService.usage.aggregateByPeriod.mockResolvedValue(mockCurrentUsage)

      const result = await usageService.calculateOverage(userId)

      expect(result.hasOverage).toBe(true)
      expect(result.overageAmount).toBe(2.50) // 25 * $0.10
      expect(result.overageDetails.autofill.count).toBe(25)
      expect(result.overageDetails.autofill.rate).toBe(0.10)
    })

    it('should return no overage for unlimited plans', async () => {
      const userId = 'user-123'

      const mockSubscription = {
        planId: 'enterprise-monthly'
      }

      const mockPlanLimits = {
        applicationsPerMonth: -1 // unlimited
      }

      mockSubscriptionService.getUserSubscription.mockResolvedValue(mockSubscription)
      mockSubscriptionService.getPlanLimits.mockResolvedValue(mockPlanLimits)

      const result = await usageService.calculateOverage(userId)

      expect(result.hasOverage).toBe(false)
      expect(result.overageAmount).toBe(0)
    })
  })

  describe('getUsageAnalytics', () => {
    it('should return usage analytics and trends', async () => {
      const userId = 'user-123'
      const dateRange = {
        start: new Date('2024-01-01'),
        end: new Date('2024-01-31')
      }

      const mockAnalytics = {
        totalUsage: 45,
        averageDailyUsage: 1.5,
        peakUsageDays: ['2024-01-15', '2024-01-22'],
        featureBreakdown: {
          autofill: 35,
          aiCustomization: 10
        },
        trends: {
          weekOverWeek: 15, // 15% increase
          monthOverMonth: -5 // 5% decrease
        }
      }

      mockDatabaseService.usage.getAnalytics = vi.fn().mockResolvedValue(mockAnalytics)

      const result = await usageService.getUsageAnalytics(userId, dateRange)

      expect(result.totalUsage).toBe(45)
      expect(result.trends.weekOverWeek).toBe(15)
      expect(result.featureBreakdown.autofill).toBe(35)
    })
  })

  describe('exportUsageData', () => {
    it('should export usage data in specified format', async () => {
      const userId = 'user-123'
      const format = 'csv'
      const dateRange = {
        start: new Date('2024-01-01'),
        end: new Date('2024-01-31')
      }

      const mockUsageData = [
        {
          date: '2024-01-15',
          featureType: 'autofill',
          count: 3,
          metadata: { jobTitles: ['Engineer', 'Manager', 'Designer'] }
        }
      ]

      mockDatabaseService.usage.findByDateRange = vi.fn().mockResolvedValue(mockUsageData)

      const result = await usageService.exportUsageData(userId, format, dateRange)

      expect(result.success).toBe(true)
      expect(result.format).toBe('csv')
      expect(result.data).toContain('date,featureType,count')
      expect(result.filename).toMatch(/usage-export-.*\.csv/)
    })
  })

  describe('scheduleUsageReports', () => {
    it('should generate and send monthly usage reports', async () => {
      const mockUsers = [
        { id: 'user-1', email: '<EMAIL>' },
        { id: 'user-2', email: '<EMAIL>' }
      ]

      mockDatabaseService.users = {
        findActiveSubscribers: vi.fn().mockResolvedValue(mockUsers)
      }

      mockDatabaseService.usage.getMonthlyReport = vi.fn().mockResolvedValue({
        totalUsage: 50,
        topFeatures: ['autofill', 'ai_customization']
      })

      const result = await usageService.generateMonthlyReports()

      expect(result.reportsGenerated).toBe(2)
      expect(mockNotificationService.sendUsageReport).toHaveBeenCalledTimes(2)
    })
  })
})
