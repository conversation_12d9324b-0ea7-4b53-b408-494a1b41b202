"""
Company Website Job Scraper
Implements job scraping from company career pages
Part of FR-5.2: Job Market Data Ingestion Service
"""

import asyncio
import logging
from typing import List, Optional, Dict
from urllib.parse import urljoin, urlparse
import re
from datetime import datetime

from .base_scraper import BaseScraper, JobPosting

class CompanyScraper(BaseScraper):
    """Company website job scraper for direct career pages"""
    
    def __init__(self):
        super().__init__(
            source_name="company_sites",
            base_url="",  # Will be set per company
            rate_limit=2.0  # Be extra respectful to company sites
        )
        
        # List of major tech companies and their career page patterns
        self.company_configs = {
            'google': {
                'base_url': 'https://careers.google.com',
                'search_path': '/jobs/results/',
                'selectors': {
                    'job_cards': '.gc-card',
                    'title': '.gc-card__title',
                    'location': '.gc-card__location',
                    'description': '.gc-card__description'
                }
            },
            'microsoft': {
                'base_url': 'https://careers.microsoft.com',
                'search_path': '/us/en/search-results',
                'selectors': {
                    'job_cards': '.job-item',
                    'title': '.job-title',
                    'location': '.job-location',
                    'description': '.job-description'
                }
            },
            'amazon': {
                'base_url': 'https://www.amazon.jobs',
                'search_path': '/en/search',
                'selectors': {
                    'job_cards': '.job-tile',
                    'title': '.job-title',
                    'location': '.location-and-id',
                    'description': '.job-description'
                }
            },
            'meta': {
                'base_url': 'https://www.metacareers.com',
                'search_path': '/jobs/',
                'selectors': {
                    'job_cards': '[data-testid="job-card"]',
                    'title': '[data-testid="job-title"]',
                    'location': '[data-testid="job-location"]',
                    'description': '[data-testid="job-description"]'
                }
            },
            'apple': {
                'base_url': 'https://jobs.apple.com',
                'search_path': '/en-us/search',
                'selectors': {
                    'job_cards': '.table--advanced-search tbody tr',
                    'title': '.table-col-1 a',
                    'location': '.table-col-2',
                    'description': '.table-col-1'
                }
            },
            'netflix': {
                'base_url': 'https://jobs.netflix.com',
                'search_path': '/search',
                'selectors': {
                    'job_cards': '.job-card',
                    'title': '.job-title',
                    'location': '.job-location',
                    'description': '.job-summary'
                }
            },
            'salesforce': {
                'base_url': 'https://salesforce.wd1.myworkdayjobs.com',
                'search_path': '/External_Career_Site',
                'selectors': {
                    'job_cards': '[data-automation-id="jobPostingItem"]',
                    'title': '[data-automation-id="jobPostingTitle"]',
                    'location': '[data-automation-id="jobPostingLocation"]',
                    'description': '[data-automation-id="jobPostingDescription"]'
                }
            },
            'uber': {
                'base_url': 'https://www.uber.com',
                'search_path': '/careers/list/',
                'selectors': {
                    'job_cards': '.job-card',
                    'title': '.job-title',
                    'location': '.job-location',
                    'description': '.job-description'
                }
            }
        }

    async def scrape_jobs(self, search_terms: List[str], locations: List[str], max_pages: int = 3) -> List[JobPosting]:
        """Scrape jobs from company career pages"""
        all_jobs = []
        
        for company_name, config in self.company_configs.items():
            self.logger.info(f"Scraping {company_name} career page")
            
            try:
                # Update base URL for this company
                self.base_url = config['base_url']
                
                jobs = await self._scrape_company_jobs(company_name, config, search_terms, locations)
                all_jobs.extend(jobs)
                
                self.logger.info(f"Found {len(jobs)} jobs from {company_name}")
                
                # Add delay between companies
                await asyncio.sleep(3)
                
            except Exception as e:
                self.logger.error(f"Error scraping {company_name}: {e}")
                continue
        
        # Process and validate jobs
        return await self.process_job_batch(all_jobs)

    async def _scrape_company_jobs(self, company_name: str, config: Dict, search_terms: List[str], locations: List[str]) -> List[JobPosting]:
        """Scrape jobs from a specific company"""
        jobs = []
        
        # Try different search approaches
        for search_term in search_terms[:3]:  # Limit to top 3 search terms
            try:
                search_url = self._build_company_search_url(config, search_term, locations[0] if locations else '')
                
                soup = await self.scrape_with_retry(search_url)
                if not soup:
                    continue
                
                company_jobs = self._parse_company_jobs(soup, config, company_name)
                jobs.extend(company_jobs)
                
                # Add delay between searches
                await asyncio.sleep(2)
                
            except Exception as e:
                self.logger.debug(f"Error searching {company_name} for {search_term}: {e}")
                continue
        
        return jobs

    def _build_company_search_url(self, config: Dict, search_term: str = '', location: str = '') -> str:
        """Build search URL for company career page"""
        base_url = config['base_url']
        search_path = config['search_path']
        
        # Different companies have different URL patterns
        if 'google' in base_url:
            return f"{base_url}{search_path}?q={search_term}&location={location}"
        elif 'microsoft' in base_url:
            return f"{base_url}{search_path}?keywords={search_term}&location={location}"
        elif 'amazon' in base_url:
            return f"{base_url}{search_path}?base_query={search_term}&loc_query={location}"
        elif 'meta' in base_url:
            return f"{base_url}{search_path}?q={search_term}&location={location}"
        elif 'apple' in base_url:
            return f"{base_url}{search_path}?search={search_term}&location={location}"
        elif 'workday' in base_url:  # Salesforce uses Workday
            return f"{base_url}{search_path}?q={search_term}"
        else:
            # Generic approach
            return f"{base_url}{search_path}?q={search_term}&location={location}"

    def _parse_company_jobs(self, soup, config: Dict, company_name: str) -> List[JobPosting]:
        """Parse job listings from company career page"""
        jobs = []
        selectors = config['selectors']
        
        # Find job cards using company-specific selectors
        job_cards = soup.select(selectors['job_cards'])
        
        for card in job_cards:
            try:
                job = self._parse_company_job_card(card, selectors, company_name)
                if job:
                    jobs.append(job)
            except Exception as e:
                self.logger.debug(f"Error parsing job card from {company_name}: {e}")
                continue
        
        return jobs

    def _parse_company_job_card(self, card, selectors: Dict, company_name: str) -> Optional[JobPosting]:
        """Parse individual job card from company site"""
        try:
            # Extract title
            title_elem = card.select_one(selectors['title'])
            if not title_elem:
                return None
            
            title = self.clean_text(title_elem.get_text())
            
            # Extract location
            location_elem = card.select_one(selectors['location'])
            location = self.clean_text(location_elem.get_text()) if location_elem else 'Not specified'
            
            # Extract description/summary
            desc_elem = card.select_one(selectors['description'])
            description = self.clean_text(desc_elem.get_text()) if desc_elem else f"Job posting for {title} at {company_name}"
            
            # Extract job URL
            external_url = None
            link_elem = card.find('a', href=True)
            if not link_elem and title_elem.name == 'a':
                link_elem = title_elem
            
            if link_elem:
                href = link_elem['href']
                if href.startswith('http'):
                    external_url = href
                elif href.startswith('/'):
                    external_url = urljoin(self.base_url, href)
            
            # Extract job ID from URL or data attributes
            external_id = None
            if external_url:
                # Try to extract ID from URL
                id_match = re.search(r'/(\d+)/?$', external_url)
                if id_match:
                    external_id = id_match.group(1)
                else:
                    # Use URL hash as ID
                    external_id = str(hash(external_url))[-8:]
            
            # Check for data attributes that might contain ID
            for attr in ['data-job-id', 'data-id', 'id']:
                if card.get(attr):
                    external_id = card[attr]
                    break
            
            # Extract additional information
            job_type = self.extract_job_type(f"{title} {description}")
            experience_level = self.extract_experience_level(f"{title} {description}")
            skills = self.extract_skills_from_text(f"{title} {description}")
            
            # Extract posted date if available
            posted_date = None
            date_elem = card.find(string=re.compile(r'posted|ago|days?', re.I))
            if date_elem:
                posted_date = self.parse_date(date_elem)
            
            # Extract salary if mentioned
            salary_text = f"{title} {description}"
            salary_min, salary_max = self.extract_salary_range(salary_text)
            
            return JobPosting(
                title=title,
                company=company_name.title(),
                description=description,
                location=location,
                salary_min=salary_min,
                salary_max=salary_max,
                job_type=job_type,
                experience_level=experience_level,
                skills=skills,
                source=self.source_name,
                external_id=external_id,
                external_url=external_url,
                posted_date=posted_date or datetime.now()
            )
            
        except Exception as e:
            self.logger.debug(f"Error parsing company job card: {e}")
            return None

    async def fetch_company_job_details(self, job_url: str, company_name: str) -> Optional[dict]:
        """Fetch detailed job information from company job page"""
        try:
            soup = await self.scrape_with_retry(job_url)
            if not soup:
                return None
            
            # Generic selectors for job details
            details = {}
            
            # Try to find full job description
            desc_selectors = [
                '.job-description',
                '.job-content',
                '.description',
                '[data-testid="job-description"]',
                '.jobdescription',
                '.job-details'
            ]
            
            description = ""
            for selector in desc_selectors:
                desc_elem = soup.select_one(selector)
                if desc_elem:
                    description = self.clean_text(desc_elem.get_text())
                    break
            
            if description:
                details['description'] = description
                details['skills'] = self.extract_skills_from_text(description)
            
            # Try to find requirements
            req_selectors = [
                '.requirements',
                '.qualifications',
                '.job-requirements',
                '[data-testid="requirements"]'
            ]
            
            for selector in req_selectors:
                req_elem = soup.select_one(selector)
                if req_elem:
                    details['requirements'] = self.clean_text(req_elem.get_text())
                    break
            
            # Try to find benefits
            benefit_selectors = [
                '.benefits',
                '.perks',
                '.compensation',
                '[data-testid="benefits"]'
            ]
            
            for selector in benefit_selectors:
                benefit_elem = soup.select_one(selector)
                if benefit_elem:
                    details['benefits'] = self.clean_text(benefit_elem.get_text())
                    break
            
            # Extract team/department information
            team_elem = soup.find(string=re.compile(r'team|department|division', re.I))
            if team_elem:
                team_parent = team_elem.find_parent()
                if team_parent:
                    details['team'] = self.clean_text(team_parent.get_text())
            
            return details
            
        except Exception as e:
            self.logger.error(f"Error fetching company job details from {job_url}: {e}")
            return None

    async def enrich_job_data(self, jobs: List[JobPosting]) -> List[JobPosting]:
        """Enrich job data by fetching detailed information"""
        enriched_jobs = []
        
        for i, job in enumerate(jobs):
            try:
                if job.external_url and i < 3:  # Limit detailed fetching to first 3 jobs per company
                    details = await self.fetch_company_job_details(job.external_url, job.company)
                    if details:
                        job.description = details.get('description', job.description)
                        job.requirements = details.get('requirements')
                        job.skills = details.get('skills', job.skills)
                        
                        # Add company-specific metadata
                        if 'benefits' in details:
                            job.industry = f"{job.company} - {details.get('team', 'General')}"
                
                enriched_jobs.append(job)
                
                # Rate limiting for detail fetching
                if job.external_url:
                    await asyncio.sleep(3)  # Be extra respectful to company sites
                    
            except Exception as e:
                self.logger.error(f"Error enriching company job data: {e}")
                enriched_jobs.append(job)  # Add original job even if enrichment fails
        
        return enriched_jobs

    def build_search_url(self, search_term: str, location: str, page: int = 1) -> str:
        """Build search URL - implemented for base class compatibility"""
        # This is handled by _build_company_search_url for specific companies
        return f"{self.base_url}/jobs?q={search_term}&location={location}&page={page}"

    def _extract_company_metadata(self, soup, company_name: str) -> dict:
        """Extract company-specific metadata"""
        metadata = {
            'company': company_name,
            'source_type': 'company_direct'
        }
        
        # Try to extract company size
        size_elem = soup.find(string=re.compile(r'employees|people|size', re.I))
        if size_elem:
            size_parent = size_elem.find_parent()
            if size_parent:
                size_text = self.clean_text(size_parent.get_text())
                metadata['company_size'] = size_text
        
        # Try to extract company industry
        industry_elem = soup.find(string=re.compile(r'industry|sector', re.I))
        if industry_elem:
            industry_parent = industry_elem.find_parent()
            if industry_parent:
                industry_text = self.clean_text(industry_parent.get_text())
                metadata['industry'] = industry_text
        
        return metadata
