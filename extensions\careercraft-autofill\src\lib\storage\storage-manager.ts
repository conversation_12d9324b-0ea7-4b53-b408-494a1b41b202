/**
 * Storage Manager
 * 
 * Manages local storage operations with encryption,
 * caching, and data synchronization capabilities.
 */

import browser from 'webextension-polyfill'

export interface StorageOptions {
  encrypt?: boolean
  ttl?: number // Time to live in milliseconds
  sync?: boolean // Use sync storage instead of local
}

export interface StorageItem {
  value: any
  timestamp: number
  ttl?: number
  encrypted?: boolean
}

export class StorageManager {
  private cache: Map<string, StorageItem> = new Map()
  private encryptionKey: string | null = null

  constructor() {
    this.initializeEncryption()
  }

  /**
   * Store data with optional encryption and TTL
   */
  async set(key: string, value: any, options: StorageOptions = {}): Promise<void> {
    const item: StorageItem = {
      value: options.encrypt ? await this.encrypt(value) : value,
      timestamp: Date.now(),
      ttl: options.ttl,
      encrypted: options.encrypt
    }

    // Store in cache
    this.cache.set(key, item)

    // Store in browser storage
    const storage = options.sync ? browser.storage.sync : browser.storage.local
    await storage.set({ [key]: item })
  }

  /**
   * Store multiple items
   */
  async setMultiple(items: { [key: string]: any }, options: StorageOptions = {}): Promise<void> {
    const storageItems: { [key: string]: StorageItem } = {}

    for (const [key, value] of Object.entries(items)) {
      const item: StorageItem = {
        value: options.encrypt ? await this.encrypt(value) : value,
        timestamp: Date.now(),
        ttl: options.ttl,
        encrypted: options.encrypt
      }

      this.cache.set(key, item)
      storageItems[key] = item
    }

    const storage = options.sync ? browser.storage.sync : browser.storage.local
    await storage.set(storageItems)
  }

  /**
   * Retrieve data with automatic decryption and TTL checking
   */
  async get(key: string): Promise<any> {
    // Check cache first
    const cachedItem = this.cache.get(key)
    if (cachedItem && this.isItemValid(cachedItem)) {
      return cachedItem.encrypted ? await this.decrypt(cachedItem.value) : cachedItem.value
    }

    // Retrieve from browser storage
    try {
      const result = await browser.storage.local.get(key)
      const item = result[key] as StorageItem

      if (!item) {
        return null
      }

      // Check TTL
      if (!this.isItemValid(item)) {
        await this.remove(key)
        return null
      }

      // Update cache
      this.cache.set(key, item)

      // Decrypt if necessary
      return item.encrypted ? await this.decrypt(item.value) : item.value
    } catch (error) {
      console.error('Storage get error:', error)
      return null
    }
  }

  /**
   * Retrieve multiple items
   */
  async getMultiple(keys: string[]): Promise<{ [key: string]: any }> {
    const result: { [key: string]: any } = {}

    try {
      const storageResult = await browser.storage.local.get(keys)

      for (const key of keys) {
        const item = storageResult[key] as StorageItem

        if (item && this.isItemValid(item)) {
          this.cache.set(key, item)
          result[key] = item.encrypted ? await this.decrypt(item.value) : item.value
        } else if (item && !this.isItemValid(item)) {
          // Remove expired item
          await this.remove(key)
        }
      }
    } catch (error) {
      console.error('Storage getMultiple error:', error)
    }

    return result
  }

  /**
   * Remove item from storage
   */
  async remove(key: string): Promise<void> {
    this.cache.delete(key)
    await browser.storage.local.remove(key)
  }

  /**
   * Remove multiple items
   */
  async removeMultiple(keys: string[]): Promise<void> {
    keys.forEach(key => this.cache.delete(key))
    await browser.storage.local.remove(keys)
  }

  /**
   * Clear all storage
   */
  async clear(): Promise<void> {
    this.cache.clear()
    await browser.storage.local.clear()
  }

  /**
   * Get all stored keys
   */
  async getAllKeys(): Promise<string[]> {
    const result = await browser.storage.local.get()
    return Object.keys(result)
  }

  /**
   * Get storage usage information
   */
  async getUsage(): Promise<{
    bytesInUse: number
    itemCount: number
    quota: number
  }> {
    try {
      const bytesInUse = await browser.storage.local.getBytesInUse()
      const allItems = await browser.storage.local.get()
      const itemCount = Object.keys(allItems).length

      return {
        bytesInUse,
        itemCount,
        quota: browser.storage.local.QUOTA_BYTES || 5242880 // 5MB default
      }
    } catch (error) {
      console.error('Storage usage error:', error)
      return { bytesInUse: 0, itemCount: 0, quota: 5242880 }
    }
  }

  /**
   * Clean up expired items
   */
  async cleanup(): Promise<number> {
    const allItems = await browser.storage.local.get()
    const expiredKeys: string[] = []

    for (const [key, item] of Object.entries(allItems)) {
      const storageItem = item as StorageItem
      if (storageItem && !this.isItemValid(storageItem)) {
        expiredKeys.push(key)
      }
    }

    if (expiredKeys.length > 0) {
      await this.removeMultiple(expiredKeys)
    }

    return expiredKeys.length
  }

  /**
   * Export all data for backup
   */
  async exportData(): Promise<{ [key: string]: any }> {
    const allItems = await browser.storage.local.get()
    const exportData: { [key: string]: any } = {}

    for (const [key, item] of Object.entries(allItems)) {
      const storageItem = item as StorageItem
      if (storageItem && this.isItemValid(storageItem)) {
        exportData[key] = storageItem.encrypted ? 
          await this.decrypt(storageItem.value) : 
          storageItem.value
      }
    }

    return exportData
  }

  /**
   * Import data from backup
   */
  async importData(data: { [key: string]: any }, options: StorageOptions = {}): Promise<void> {
    await this.setMultiple(data, options)
  }

  /**
   * Watch for storage changes
   */
  onChanged(callback: (changes: { [key: string]: { oldValue?: any; newValue?: any } }) => void): void {
    browser.storage.onChanged.addListener((changes, areaName) => {
      if (areaName === 'local') {
        const processedChanges: { [key: string]: { oldValue?: any; newValue?: any } } = {}

        for (const [key, change] of Object.entries(changes)) {
          processedChanges[key] = {
            oldValue: change.oldValue ? 
              (change.oldValue as StorageItem).value : 
              undefined,
            newValue: change.newValue ? 
              (change.newValue as StorageItem).value : 
              undefined
          }
        }

        callback(processedChanges)
      }
    })
  }

  /**
   * Check if storage item is valid (not expired)
   */
  private isItemValid(item: StorageItem): boolean {
    if (!item.ttl) {
      return true
    }

    return Date.now() - item.timestamp < item.ttl
  }

  /**
   * Initialize encryption key
   */
  private async initializeEncryption(): Promise<void> {
    try {
      // Try to get existing key
      const result = await browser.storage.local.get('_encryptionKey')
      
      if (result._encryptionKey) {
        this.encryptionKey = result._encryptionKey
      } else {
        // Generate new key
        this.encryptionKey = this.generateEncryptionKey()
        await browser.storage.local.set({ _encryptionKey: this.encryptionKey })
      }
    } catch (error) {
      console.error('Encryption initialization error:', error)
      this.encryptionKey = this.generateEncryptionKey()
    }
  }

  /**
   * Generate encryption key
   */
  private generateEncryptionKey(): string {
    const array = new Uint8Array(32)
    crypto.getRandomValues(array)
    return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('')
  }

  /**
   * Encrypt data
   */
  private async encrypt(data: any): Promise<string> {
    if (!this.encryptionKey) {
      throw new Error('Encryption key not available')
    }

    try {
      const jsonString = JSON.stringify(data)
      const encoder = new TextEncoder()
      const dataBuffer = encoder.encode(jsonString)

      // Simple XOR encryption (for demonstration - use proper encryption in production)
      const keyBuffer = encoder.encode(this.encryptionKey)
      const encrypted = new Uint8Array(dataBuffer.length)

      for (let i = 0; i < dataBuffer.length; i++) {
        encrypted[i] = dataBuffer[i] ^ keyBuffer[i % keyBuffer.length]
      }

      return btoa(String.fromCharCode(...encrypted))
    } catch (error) {
      console.error('Encryption error:', error)
      throw new Error('Failed to encrypt data')
    }
  }

  /**
   * Decrypt data
   */
  private async decrypt(encryptedData: string): Promise<any> {
    if (!this.encryptionKey) {
      throw new Error('Encryption key not available')
    }

    try {
      const encrypted = new Uint8Array(
        atob(encryptedData).split('').map(char => char.charCodeAt(0))
      )

      const encoder = new TextEncoder()
      const keyBuffer = encoder.encode(this.encryptionKey)
      const decrypted = new Uint8Array(encrypted.length)

      for (let i = 0; i < encrypted.length; i++) {
        decrypted[i] = encrypted[i] ^ keyBuffer[i % keyBuffer.length]
      }

      const decoder = new TextDecoder()
      const jsonString = decoder.decode(decrypted)
      return JSON.parse(jsonString)
    } catch (error) {
      console.error('Decryption error:', error)
      throw new Error('Failed to decrypt data')
    }
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): {
    size: number
    hitRate: number
    items: string[]
  } {
    return {
      size: this.cache.size,
      hitRate: 0, // Would need to track hits/misses for accurate calculation
      items: Array.from(this.cache.keys())
    }
  }

  /**
   * Clear cache
   */
  clearCache(): void {
    this.cache.clear()
  }

  /**
   * Preload frequently used items into cache
   */
  async preloadCache(keys: string[]): Promise<void> {
    await this.getMultiple(keys)
  }

  /**
   * Set cache size limit
   */
  setCacheLimit(limit: number): void {
    if (this.cache.size > limit) {
      const keysToRemove = Array.from(this.cache.keys()).slice(0, this.cache.size - limit)
      keysToRemove.forEach(key => this.cache.delete(key))
    }
  }
}
