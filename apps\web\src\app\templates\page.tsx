'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { MainLayout } from '@/components/layout/main-layout';
import { TemplateSelector } from '@/components/templates/template-selector';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Icons } from '@/components/ui/icons';
import { Template } from '@careercraft/shared/types/template';
import { toast } from 'sonner';

export default function TemplatesPage() {
  const router = useRouter();
  const [selectedTemplate, setSelectedTemplate] = useState<Template | null>(null);

  const handleTemplateSelect = (template: Template) => {
    setSelectedTemplate(template);
  };

  const handleUseTemplate = () => {
    if (selectedTemplate) {
      router.push(`/dashboard/resumes/new?template=${selectedTemplate.id}`);
    } else {
      toast.error('Please select a template first');
    }
  };

  const handlePreviewTemplate = () => {
    if (selectedTemplate) {
      router.push(`/templates/${selectedTemplate.id}/preview`);
    } else {
      toast.error('Please select a template first');
    }
  };

  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold mb-4">
            Professional Resume Templates
          </h1>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Choose from our collection of professionally designed resume templates. 
            Each template is optimized for ATS systems and designed to help you stand out.
          </p>
        </div>

        {/* Features */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
          <Card>
            <CardHeader className="text-center">
              <Icons.checkCircle className="h-12 w-12 mx-auto text-primary mb-4" />
              <CardTitle>ATS Optimized</CardTitle>
              <CardDescription>
                All templates are designed to pass applicant tracking systems
              </CardDescription>
            </CardHeader>
          </Card>
          
          <Card>
            <CardHeader className="text-center">
              <Icons.palette className="h-12 w-12 mx-auto text-primary mb-4" />
              <CardTitle>Fully Customizable</CardTitle>
              <CardDescription>
                Customize colors, fonts, and layout to match your personal style
              </CardDescription>
            </CardHeader>
          </Card>
          
          <Card>
            <CardHeader className="text-center">
              <Icons.download className="h-12 w-12 mx-auto text-primary mb-4" />
              <CardTitle>Multiple Formats</CardTitle>
              <CardDescription>
                Export your resume as PDF, Word document, or HTML
              </CardDescription>
            </CardHeader>
          </Card>
        </div>

        {/* Template Selector */}
        <div className="mb-8">
          <TemplateSelector
            onTemplateSelect={handleTemplateSelect}
            selectedTemplateId={selectedTemplate?.id}
          />
        </div>

        {/* Selected Template Actions */}
        {selectedTemplate && (
          <Card className="mb-8">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Icons.star className="h-5 w-5 text-primary" />
                Selected Template: {selectedTemplate.name}
              </CardTitle>
              <CardDescription>
                {selectedTemplate.description}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col sm:flex-row gap-4">
                <Button onClick={handleUseTemplate} size="lg" className="flex-1 sm:flex-none">
                  <Icons.plus className="mr-2 h-4 w-4" />
                  Use This Template
                </Button>
                <Button 
                  variant="outline" 
                  onClick={handlePreviewTemplate} 
                  size="lg"
                  className="flex-1 sm:flex-none"
                >
                  <Icons.eye className="mr-2 h-4 w-4" />
                  Preview Template
                </Button>
              </div>
              
              <div className="mt-4 flex flex-wrap gap-2">
                {selectedTemplate.tags.map((tag) => (
                  <span
                    key={tag}
                    className="px-2 py-1 bg-secondary text-secondary-foreground rounded-md text-sm"
                  >
                    {tag}
                  </span>
                ))}
              </div>
              
              <div className="mt-4 grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <span className="font-medium">Category:</span>
                  <p className="text-muted-foreground capitalize">
                    {selectedTemplate.category.replace('_', ' ')}
                  </p>
                </div>
                <div>
                  <span className="font-medium">Difficulty:</span>
                  <p className="text-muted-foreground capitalize">
                    {selectedTemplate.difficulty}
                  </p>
                </div>
                <div>
                  <span className="font-medium">Rating:</span>
                  <p className="text-muted-foreground">
                    {selectedTemplate.rating.toFixed(1)} / 5.0
                  </p>
                </div>
                <div>
                  <span className="font-medium">Uses:</span>
                  <p className="text-muted-foreground">
                    {selectedTemplate.usageCount.toLocaleString()}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Call to Action */}
        <div className="text-center py-12">
          <h2 className="text-2xl font-bold mb-4">
            Ready to Create Your Professional Resume?
          </h2>
          <p className="text-muted-foreground mb-6">
            {selectedTemplate 
              ? `Start building your resume with the ${selectedTemplate.name} template`
              : 'Select a template above to get started'
            }
          </p>
          <Button 
            size="lg" 
            onClick={selectedTemplate ? handleUseTemplate : undefined}
            disabled={!selectedTemplate}
          >
            {selectedTemplate ? 'Create Resume' : 'Select a Template First'}
          </Button>
        </div>
      </div>
    </MainLayout>
  );
}
