/**
 * AI Components Unit Tests
 * 
 * Tests for AI-powered React components including:
 * - AIAssistant component
 * - Content generation panels
 * - ATS analysis displays
 * - User interactions and state management
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { AIAssistant } from '@/components/ai/AIAssistant'
import { AIAnalytics } from '@/components/ai/AIAnalytics'
import { CoverLetterGenerator } from '@/components/ai/CoverLetterGenerator'
import { KeywordOptimizer } from '@/components/ai/KeywordOptimizer'

// Mock fetch for API calls
global.fetch = vi.fn()

// Mock next/navigation
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: vi.fn(),
    refresh: vi.fn()
  })
}))

// Mock framer-motion to avoid animation issues in tests
vi.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
    button: ({ children, ...props }: any) => <button {...props}>{children}</button>,
    span: ({ children, ...props }: any) => <span {...props}>{children}</span>
  },
  AnimatePresence: ({ children }: any) => children
}))

describe('AI Components', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('AIAssistant', () => {
    const mockResumeContent = {
      personalInfo: {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>'
      },
      summary: 'Software engineer with 5 years experience',
      experience: [
        {
          company: 'TechCorp',
          position: 'Software Engineer',
          description: 'Developed web applications'
        }
      ]
    }

    const mockProps = {
      resumeContent: mockResumeContent,
      onContentUpdate: vi.fn(),
      onSuggestionApply: vi.fn()
    }

    it('should render AI assistant interface', () => {
      render(<AIAssistant {...mockProps} />)
      
      expect(screen.getByText('AI Assistant')).toBeInTheDocument()
      expect(screen.getByText('ATS Score')).toBeInTheDocument()
      expect(screen.getByText('Content Generation')).toBeInTheDocument()
    })

    it('should display ATS score analysis', async () => {
      render(<AIAssistant {...mockProps} />)
      
      // Wait for mock analysis to complete
      await waitFor(() => {
        expect(screen.getByText(/78/)).toBeInTheDocument() // Mock score
      }, { timeout: 3000 })
      
      expect(screen.getByText('Keywords')).toBeInTheDocument()
      expect(screen.getByText('Formatting')).toBeInTheDocument()
      expect(screen.getByText('Content')).toBeInTheDocument()
    })

    it('should show improvement suggestions', async () => {
      render(<AIAssistant {...mockProps} />)
      
      await waitFor(() => {
        expect(screen.getByText(/Add more industry-specific keywords/)).toBeInTheDocument()
      }, { timeout: 3000 })
      
      expect(screen.getByText(/Quantify achievements with numbers/)).toBeInTheDocument()
      expect(screen.getByText(/Use stronger action verbs/)).toBeInTheDocument()
    })

    it('should handle content generation', async () => {
      const user = userEvent.setup()
      
      // Mock successful API response
      ;(global.fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          content: 'Generated professional summary content',
          type: 'summary',
          tone: 'professional'
        })
      })

      render(<AIAssistant {...mockProps} />)
      
      // Select generation type
      const summaryButton = screen.getByText('Professional Summary')
      await user.click(summaryButton)
      
      // Add context
      const contextInput = screen.getByPlaceholderText(/Describe your role/i)
      await user.type(contextInput, 'Senior Software Engineer at TechCorp')
      
      // Generate content
      const generateButton = screen.getByText('Generate Content')
      await user.click(generateButton)
      
      await waitFor(() => {
        expect(screen.getByText(/Generated professional summary content/)).toBeInTheDocument()
      })
    })

    it('should handle API errors gracefully', async () => {
      const user = userEvent.setup()
      
      // Mock API error
      ;(global.fetch as any).mockRejectedValueOnce(new Error('API Error'))

      render(<AIAssistant {...mockProps} />)
      
      const generateButton = screen.getByText('Generate Content')
      await user.click(generateButton)
      
      await waitFor(() => {
        expect(screen.getByText(/Failed to generate content/)).toBeInTheDocument()
      })
    })

    it('should apply suggestions to resume', async () => {
      const user = userEvent.setup()
      
      render(<AIAssistant {...mockProps} />)
      
      // Wait for suggestions to load
      await waitFor(() => {
        expect(screen.getByText(/Add more industry-specific keywords/)).toBeInTheDocument()
      })
      
      // Apply a suggestion
      const applyButton = screen.getAllByText('Apply')[0]
      await user.click(applyButton)
      
      expect(mockProps.onSuggestionApply).toHaveBeenCalled()
    })

    it('should handle job description input for ATS analysis', async () => {
      const user = userEvent.setup()
      
      render(<AIAssistant {...mockProps} />)
      
      const jobDescInput = screen.getByPlaceholderText(/Paste the job description/i)
      await user.type(jobDescInput, 'Looking for a React developer with Node.js experience')
      
      const analyzeButton = screen.getByText('Analyze ATS Compatibility')
      await user.click(analyzeButton)
      
      // Should trigger re-analysis with job description
      expect(jobDescInput).toHaveValue('Looking for a React developer with Node.js experience')
    })
  })

  describe('AIAnalytics', () => {
    it('should render analytics dashboard', () => {
      render(<AIAnalytics />)
      
      expect(screen.getByText('AI Usage Analytics')).toBeInTheDocument()
      expect(screen.getByText('Total Optimizations')).toBeInTheDocument()
      expect(screen.getByText('Average ATS Score')).toBeInTheDocument()
    })

    it('should display usage statistics', async () => {
      render(<AIAnalytics />)
      
      await waitFor(() => {
        expect(screen.getByText('47')).toBeInTheDocument() // Mock total optimizations
        expect(screen.getByText('82')).toBeInTheDocument() // Mock average ATS score
      })
    })

    it('should show improvement metrics', async () => {
      render(<AIAnalytics />)
      
      await waitFor(() => {
        expect(screen.getByText(/34%/)).toBeInTheDocument() // Mock improvement rate
        expect(screen.getByText(/12.5 hours/)).toBeInTheDocument() // Mock time saved
      })
    })
  })

  describe('CoverLetterGenerator', () => {
    const mockProps = {
      resumeData: {
        personalInfo: { firstName: 'John', lastName: 'Doe' },
        experience: []
      },
      onGenerate: vi.fn()
    }

    it('should render cover letter generator', () => {
      render(<CoverLetterGenerator {...mockProps} />)
      
      expect(screen.getByText('AI Cover Letter Generator')).toBeInTheDocument()
      expect(screen.getByPlaceholderText(/Company name/i)).toBeInTheDocument()
      expect(screen.getByPlaceholderText(/Position title/i)).toBeInTheDocument()
    })

    it('should generate cover letter with job details', async () => {
      const user = userEvent.setup()
      
      // Mock API response
      ;(global.fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          content: 'Dear Hiring Manager,\n\nI am writing to express my interest...',
          type: 'cover_letter'
        })
      })

      render(<CoverLetterGenerator {...mockProps} />)
      
      // Fill in job details
      await user.type(screen.getByPlaceholderText(/Company name/i), 'TechCorp')
      await user.type(screen.getByPlaceholderText(/Position title/i), 'Senior Developer')
      await user.type(screen.getByPlaceholderText(/Job description/i), 'Looking for a React developer')
      
      // Generate cover letter
      const generateButton = screen.getByText('Generate Cover Letter')
      await user.click(generateButton)
      
      await waitFor(() => {
        expect(screen.getByText(/Dear Hiring Manager/)).toBeInTheDocument()
      })
    })

    it('should validate required fields', async () => {
      const user = userEvent.setup()
      
      render(<CoverLetterGenerator {...mockProps} />)
      
      // Try to generate without filling required fields
      const generateButton = screen.getByText('Generate Cover Letter')
      await user.click(generateButton)
      
      expect(screen.getByText(/Company name is required/)).toBeInTheDocument()
    })
  })

  describe('KeywordOptimizer', () => {
    const mockProps = {
      content: 'Software engineer with JavaScript experience',
      jobDescription: 'Looking for React developer with Node.js skills',
      onOptimize: vi.fn()
    }

    it('should render keyword optimizer', () => {
      render(<KeywordOptimizer {...mockProps} />)
      
      expect(screen.getByText('Keyword Optimization')).toBeInTheDocument()
      expect(screen.getByText('Missing Keywords')).toBeInTheDocument()
    })

    it('should analyze keyword density', async () => {
      render(<KeywordOptimizer {...mockProps} />)
      
      await waitFor(() => {
        expect(screen.getByText(/javascript/i)).toBeInTheDocument()
        expect(screen.getByText(/react/i)).toBeInTheDocument()
      })
    })

    it('should suggest keyword improvements', async () => {
      render(<KeywordOptimizer {...mockProps} />)
      
      await waitFor(() => {
        expect(screen.getByText(/Add "React" to improve relevance/)).toBeInTheDocument()
        expect(screen.getByText(/Include "Node.js" for better matching/)).toBeInTheDocument()
      })
    })

    it('should optimize content with keywords', async () => {
      const user = userEvent.setup()
      
      // Mock API response
      ;(global.fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          optimizedContent: 'Software engineer with JavaScript, React, and Node.js experience',
          score: 85
        })
      })

      render(<KeywordOptimizer {...mockProps} />)
      
      const optimizeButton = screen.getByText('Optimize Content')
      await user.click(optimizeButton)
      
      await waitFor(() => {
        expect(mockProps.onOptimize).toHaveBeenCalledWith(
          expect.stringContaining('React')
        )
      })
    })
  })

  describe('Loading States', () => {
    it('should show loading indicators during AI operations', async () => {
      // Mock slow API response
      ;(global.fetch as any).mockImplementationOnce(() => 
        new Promise(resolve => setTimeout(() => resolve({
          ok: true,
          json: async () => ({ content: 'Generated content' })
        }), 1000))
      )

      const user = userEvent.setup()
      
      render(<AIAssistant resumeContent={{}} onContentUpdate={vi.fn()} onSuggestionApply={vi.fn()} />)
      
      const generateButton = screen.getByText('Generate Content')
      await user.click(generateButton)
      
      expect(screen.getByText(/Generating/i)).toBeInTheDocument()
    })
  })

  describe('Accessibility', () => {
    it('should have proper ARIA labels', () => {
      render(<AIAssistant resumeContent={{}} onContentUpdate={vi.fn()} onSuggestionApply={vi.fn()} />)
      
      expect(screen.getByRole('button', { name: /Generate Content/i })).toBeInTheDocument()
      expect(screen.getByRole('textbox', { name: /Job Description/i })).toBeInTheDocument()
    })

    it('should support keyboard navigation', async () => {
      const user = userEvent.setup()
      
      render(<AIAssistant resumeContent={{}} onContentUpdate={vi.fn()} onSuggestionApply={vi.fn()} />)
      
      // Tab through interactive elements
      await user.tab()
      expect(screen.getByRole('textbox')).toHaveFocus()
      
      await user.tab()
      expect(screen.getByRole('button')).toHaveFocus()
    })
  })
})
