import { NextAuthOptions } from 'next-auth';
import { PrismaAdapter } from '@next-auth/prisma-adapter';
import GoogleProvider from 'next-auth/providers/google';
import GitHubProvider from 'next-auth/providers/github';
import Cred<PERSON>sProvider from 'next-auth/providers/credentials';
import { prisma } from '@careercraft/database';
import bcrypt from 'bcryptjs';
import { z } from 'zod';

// Validation schemas
const loginSchema = z.object({
  email: z.string().email('Invalid email address'),
  password: z.string().min(8, 'Password must be at least 8 characters'),
});

const signupSchema = z.object({
  email: z.string().email('Invalid email address'),
  password: z.string().min(8, 'Password must be at least 8 characters'),
  name: z.string().min(2, 'Name must be at least 2 characters'),
});

export const authOptions: NextAuthOptions = {
  adapter: PrismaAdapter(prisma),
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
      profile(profile) {
        return {
          id: profile.sub,
          name: profile.name,
          email: profile.email,
          image: profile.picture,
          emailVerified: profile.email_verified ? new Date() : null,
        };
      },
    }),
    GitHubProvider({
      clientId: process.env.GITHUB_CLIENT_ID!,
      clientSecret: process.env.GITHUB_CLIENT_SECRET!,
      profile(profile) {
        return {
          id: profile.id.toString(),
          name: profile.name || profile.login,
          email: profile.email,
          image: profile.avatar_url,
        };
      },
    }),
    CredentialsProvider({
      name: 'credentials',
      credentials: {
        email: { label: 'Email', type: 'email' },
        password: { label: 'Password', type: 'password' },
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          throw new Error('Email and password are required');
        }

        try {
          // Validate input
          const { email, password } = loginSchema.parse(credentials);

          // Find user in database
          const user = await prisma.user.findUnique({
            where: { email },
            include: {
              accounts: true,
            },
          });

          if (!user) {
            throw new Error('No user found with this email');
          }

          // Check if user has a password (not OAuth-only)
          const account = user.accounts.find(acc => acc.provider === 'credentials');
          if (!account) {
            throw new Error('Please sign in with your OAuth provider');
          }

          // For credentials provider, we store hashed password in account.refresh_token
          // This is a secure way to handle passwords with NextAuth + Prisma adapter
          const hashedPassword = account.refresh_token;
          if (!hashedPassword) {
            throw new Error('Invalid credentials');
          }

          // Verify password
          const isValidPassword = await bcrypt.compare(password, hashedPassword);
          if (!isValidPassword) {
            throw new Error('Invalid credentials');
          }

          return {
            id: user.id,
            email: user.email,
            name: user.name,
            image: user.image,
            emailVerified: user.emailVerified,
          };
        } catch (error) {
          console.error('Authentication error:', error);
          throw new Error('Authentication failed');
        }
      },
    }),
  ],
  session: {
    strategy: 'jwt',
    maxAge: 30 * 24 * 60 * 60, // 30 days
    updateAge: 24 * 60 * 60, // 24 hours
  },
  jwt: {
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  pages: {
    signIn: '/auth/signin',
    signUp: '/auth/signup',
    error: '/auth/error',
    verifyRequest: '/auth/verify-request',
  },
  callbacks: {
    async jwt({ token, user, account }) {
      // Initial sign in
      if (account && user) {
        token.accessToken = account.access_token;
        token.provider = account.provider;
      }

      // Add user ID to token
      if (user) {
        token.id = user.id;
      }

      return token;
    },
    async session({ session, token }) {
      // Send properties to the client
      if (token) {
        session.user.id = token.id as string;
        session.accessToken = token.accessToken as string;
        session.provider = token.provider as string;
      }

      return session;
    },
    async signIn({ user, account, profile }) {
      // Allow OAuth sign-ins
      if (account?.provider === 'google' || account?.provider === 'github') {
        return true;
      }

      // Allow credentials sign-ins
      if (account?.provider === 'credentials') {
        return true;
      }

      return false;
    },
    async redirect({ url, baseUrl }) {
      // Allows relative callback URLs
      if (url.startsWith('/')) return `${baseUrl}${url}`;
      
      // Allows callback URLs on the same origin
      if (new URL(url).origin === baseUrl) return url;
      
      return baseUrl;
    },
  },
  events: {
    async signIn({ user, account, profile, isNewUser }) {
      console.log(`User signed in: ${user.email} via ${account?.provider}`);
      
      // Create user profile for new users
      if (isNewUser && user.id) {
        try {
          await prisma.userProfile.create({
            data: {
              userId: user.id,
              firstName: user.name?.split(' ')[0] || '',
              lastName: user.name?.split(' ').slice(1).join(' ') || '',
            },
          });
        } catch (error) {
          console.error('Error creating user profile:', error);
        }
      }
    },
    async signOut({ token }) {
      console.log(`User signed out: ${token?.email}`);
    },
  },
  debug: process.env.NODE_ENV === 'development',
};

// Helper functions for authentication
export async function hashPassword(password: string): Promise<string> {
  const saltRounds = 12;
  return bcrypt.hash(password, saltRounds);
}

export async function verifyPassword(password: string, hashedPassword: string): Promise<boolean> {
  return bcrypt.compare(password, hashedPassword);
}

export async function createUser(data: z.infer<typeof signupSchema>) {
  try {
    // Validate input
    const validatedData = signupSchema.parse(data);

    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email: validatedData.email },
    });

    if (existingUser) {
      throw new Error('User with this email already exists');
    }

    // Hash password
    const hashedPassword = await hashPassword(validatedData.password);

    // Create user and credentials account in a transaction
    const result = await prisma.$transaction(async (tx) => {
      // Create user
      const user = await tx.user.create({
        data: {
          email: validatedData.email,
          name: validatedData.name,
        },
      });

      // Create credentials account (store hashed password in refresh_token field)
      await tx.account.create({
        data: {
          userId: user.id,
          type: 'credentials',
          provider: 'credentials',
          providerAccountId: user.id,
          refresh_token: hashedPassword, // Store hashed password here
        },
      });

      // Create user profile
      await tx.userProfile.create({
        data: {
          userId: user.id,
          firstName: validatedData.name.split(' ')[0] || '',
          lastName: validatedData.name.split(' ').slice(1).join(' ') || '',
        },
      });

      return user;
    });

    return result;
  } catch (error) {
    console.error('Error creating user:', error);
    throw error;
  }
}

// Export validation schemas
export { loginSchema, signupSchema };
