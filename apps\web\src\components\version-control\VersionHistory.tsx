'use client'

import { useState, useEffect } from 'react'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Separator } from '@/components/ui/separator'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import { 
  History, 
  RotateCcw, 
  GitCompare, 
  Clock, 
  User,
  FileText,
  AlertTriangle,
  CheckCircle,
  Eye
} from 'lucide-react'
import { formatDistanceToNow } from 'date-fns'
import { toast } from 'sonner'

interface ResumeVersion {
  id: string
  resumeId: string
  versionNumber: number
  versionName?: string
  changeSummary?: string
  changeType: string
  createdBy: string
  createdAt: Date
  metadata?: any
  creator: {
    id: string
    name: string | null
    image: string | null
  }
}

interface VersionDiff {
  operations: Array<{
    type: 'add' | 'remove' | 'modify' | 'move'
    path: string[]
    oldValue?: any
    newValue?: any
  }>
  summary: {
    additions: number
    deletions: number
    modifications: number
    moves: number
  }
  metadata: {
    fromVersion: number
    toVersion: number
    complexity: 'low' | 'medium' | 'high'
  }
}

interface VersionHistoryProps {
  resumeId: string
  className?: string
  onVersionSelect?: (version: ResumeVersion) => void
}

export function VersionHistory({ resumeId, className, onVersionSelect }: VersionHistoryProps) {
  const [versions, setVersions] = useState<ResumeVersion[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedVersions, setSelectedVersions] = useState<[ResumeVersion | null, ResumeVersion | null]>([null, null])
  const [rollbackDialog, setRollbackDialog] = useState<ResumeVersion | null>(null)
  const [compareDialog, setCompareDialog] = useState(false)
  const [previewDiff, setPreviewDiff] = useState<VersionDiff | null>(null)
  const [isRollingBack, setIsRollingBack] = useState(false)

  useEffect(() => {
    loadVersions()
  }, [resumeId])

  const loadVersions = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/version-control/versions?action=list&resumeId=${resumeId}`)
      
      if (!response.ok) {
        throw new Error('Failed to load versions')
      }

      const data = await response.json()
      setVersions(data.versions.map((v: any) => ({
        ...v,
        createdAt: new Date(v.createdAt)
      })))
    } catch (error) {
      console.error('Error loading versions:', error)
      toast.error('Failed to load version history')
    } finally {
      setLoading(false)
    }
  }

  const handleVersionClick = (version: ResumeVersion) => {
    if (selectedVersions[0] === null) {
      setSelectedVersions([version, null])
    } else if (selectedVersions[1] === null && selectedVersions[0]?.id !== version.id) {
      setSelectedVersions([selectedVersions[0], version])
    } else {
      setSelectedVersions([version, null])
    }
    
    onVersionSelect?.(version)
  }

  const handleCompareVersions = async () => {
    if (!selectedVersions[0] || !selectedVersions[1]) {
      toast.error('Please select two versions to compare')
      return
    }

    try {
      const response = await fetch('/api/version-control/versions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'compare',
          resumeId,
          fromVersion: selectedVersions[0].versionNumber,
          toVersion: selectedVersions[1].versionNumber
        })
      })

      if (!response.ok) {
        throw new Error('Failed to compare versions')
      }

      const data = await response.json()
      setPreviewDiff(data.diff)
      setCompareDialog(true)
    } catch (error) {
      console.error('Error comparing versions:', error)
      toast.error('Failed to compare versions')
    }
  }

  const handleRollbackClick = async (version: ResumeVersion) => {
    try {
      const response = await fetch('/api/version-control/versions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'preview-rollback',
          resumeId,
          versionId: version.id
        })
      })

      if (!response.ok) {
        throw new Error('Failed to preview rollback')
      }

      const data = await response.json()
      setPreviewDiff(data.diff)
      setRollbackDialog(version)
    } catch (error) {
      console.error('Error previewing rollback:', error)
      toast.error('Failed to preview rollback')
    }
  }

  const confirmRollback = async () => {
    if (!rollbackDialog) return

    try {
      setIsRollingBack(true)
      const response = await fetch('/api/version-control/versions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'rollback',
          resumeId,
          versionId: rollbackDialog.id,
          options: {
            createBackup: true,
            backupName: `Pre-rollback backup ${new Date().toISOString()}`
          }
        })
      })

      if (!response.ok) {
        throw new Error('Rollback failed')
      }

      toast.success('Successfully rolled back to selected version')
      setRollbackDialog(null)
      setPreviewDiff(null)
      await loadVersions()
    } catch (error) {
      console.error('Error during rollback:', error)
      toast.error('Rollback failed')
    } finally {
      setIsRollingBack(false)
    }
  }

  const getChangeTypeColor = (changeType: string) => {
    switch (changeType) {
      case 'auto': return 'bg-blue-100 text-blue-800'
      case 'manual': return 'bg-green-100 text-green-800'
      case 'rollback': return 'bg-orange-100 text-orange-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getChangeTypeIcon = (changeType: string) => {
    switch (changeType) {
      case 'auto': return <Clock className="w-3 h-3" />
      case 'manual': return <User className="w-3 h-3" />
      case 'rollback': return <RotateCcw className="w-3 h-3" />
      default: return <FileText className="w-3 h-3" />
    }
  }

  const renderDiffSummary = (diff: VersionDiff) => {
    const { summary } = diff
    const total = summary.additions + summary.deletions + summary.modifications + summary.moves

    if (total === 0) {
      return <span className="text-sm text-muted-foreground">No changes</span>
    }

    return (
      <div className="flex items-center space-x-2 text-sm">
        {summary.additions > 0 && (
          <span className="text-green-600">+{summary.additions}</span>
        )}
        {summary.deletions > 0 && (
          <span className="text-red-600">-{summary.deletions}</span>
        )}
        {summary.modifications > 0 && (
          <span className="text-blue-600">~{summary.modifications}</span>
        )}
        {summary.moves > 0 && (
          <span className="text-purple-600">↔{summary.moves}</span>
        )}
      </div>
    )
  }

  if (loading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <History className="w-5 h-5" />
            <span>Version History</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="animate-pulse">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-gray-200 rounded-full" />
                  <div className="flex-1">
                    <div className="h-4 bg-gray-200 rounded w-3/4 mb-2" />
                    <div className="h-3 bg-gray-200 rounded w-1/2" />
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <>
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <History className="w-5 h-5" />
              <span>Version History</span>
              <Badge variant="secondary">{versions.length}</Badge>
            </div>
            <div className="flex items-center space-x-2">
              {selectedVersions[0] && selectedVersions[1] && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleCompareVersions}
                  className="flex items-center space-x-1"
                >
                  <GitCompare className="w-4 h-4" />
                  <span>Compare</span>
                </Button>
              )}
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ScrollArea className="h-96">
            <div className="space-y-3">
              {versions.map((version, index) => {
                const isSelected = selectedVersions.some(v => v?.id === version.id)
                const isLatest = index === 0

                return (
                  <div
                    key={version.id}
                    className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                      isSelected 
                        ? 'border-blue-500 bg-blue-50' 
                        : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                    }`}
                    onClick={() => handleVersionClick(version)}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex items-start space-x-3 flex-1">
                        <Avatar className="w-8 h-8">
                          <AvatarImage src={version.creator.image || undefined} />
                          <AvatarFallback>
                            {version.creator.name?.charAt(0) || 'U'}
                          </AvatarFallback>
                        </Avatar>
                        
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center space-x-2 mb-1">
                            <span className="font-medium text-sm">
                              Version {version.versionNumber}
                            </span>
                            {isLatest && (
                              <Badge variant="default" className="text-xs">
                                Current
                              </Badge>
                            )}
                            <Badge 
                              variant="secondary" 
                              className={`text-xs ${getChangeTypeColor(version.changeType)}`}
                            >
                              <div className="flex items-center space-x-1">
                                {getChangeTypeIcon(version.changeType)}
                                <span className="capitalize">{version.changeType}</span>
                              </div>
                            </Badge>
                          </div>
                          
                          {version.versionName && (
                            <p className="text-sm font-medium text-gray-900 mb-1">
                              {version.versionName}
                            </p>
                          )}
                          
                          {version.changeSummary && (
                            <p className="text-sm text-gray-600 mb-2">
                              {version.changeSummary}
                            </p>
                          )}
                          
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                              <span>{version.creator.name}</span>
                              <span>•</span>
                              <span>{formatDistanceToNow(version.createdAt, { addSuffix: true })}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex items-center space-x-1 ml-2">
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={(e) => {
                                  e.stopPropagation()
                                  onVersionSelect?.(version)
                                }}
                              >
                                <Eye className="w-4 h-4" />
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Preview version</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                        
                        {!isLatest && (
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={(e) => {
                                    e.stopPropagation()
                                    handleRollbackClick(version)
                                  }}
                                >
                                  <RotateCcw className="w-4 h-4" />
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>Rollback to this version</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        )}
                      </div>
                    </div>
                  </div>
                )
              })}
              
              {versions.length === 0 && (
                <div className="text-center py-8">
                  <History className="w-12 h-12 mx-auto text-gray-400 mb-4" />
                  <p className="text-gray-500">No version history available</p>
                  <p className="text-sm text-gray-400">
                    Versions will appear here as you make changes to your resume
                  </p>
                </div>
              )}
            </div>
          </ScrollArea>
        </CardContent>
      </Card>

      {/* Compare Dialog */}
      <Dialog open={compareDialog} onOpenChange={setCompareDialog}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>Version Comparison</DialogTitle>
            <DialogDescription>
              Comparing version {selectedVersions[0]?.versionNumber} with version {selectedVersions[1]?.versionNumber}
            </DialogDescription>
          </DialogHeader>
          
          {previewDiff && (
            <div className="space-y-4">
              <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div className="text-sm">
                  <span className="font-medium">Changes Summary:</span>
                </div>
                {renderDiffSummary(previewDiff)}
              </div>
              
              <ScrollArea className="h-96 border rounded-lg p-4">
                <div className="space-y-2">
                  {previewDiff.operations.map((op, index) => (
                    <div key={index} className="text-sm font-mono">
                      <span className={`inline-block w-8 ${
                        op.type === 'add' ? 'text-green-600' :
                        op.type === 'remove' ? 'text-red-600' :
                        op.type === 'modify' ? 'text-blue-600' :
                        'text-purple-600'
                      }`}>
                        {op.type === 'add' ? '+' :
                         op.type === 'remove' ? '-' :
                         op.type === 'modify' ? '~' : '↔'}
                      </span>
                      <span className="text-gray-600">{op.path.join('.')}</span>
                      {op.newValue && (
                        <span className="ml-2 text-gray-800">
                          → {typeof op.newValue === 'string' ? op.newValue : JSON.stringify(op.newValue)}
                        </span>
                      )}
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Rollback Dialog */}
      <Dialog open={!!rollbackDialog} onOpenChange={() => setRollbackDialog(null)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle className="flex items-center space-x-2">
              <AlertTriangle className="w-5 h-5 text-orange-500" />
              <span>Confirm Rollback</span>
            </DialogTitle>
            <DialogDescription>
              Are you sure you want to rollback to version {rollbackDialog?.versionNumber}?
              This will create a backup of your current version before applying the rollback.
            </DialogDescription>
          </DialogHeader>
          
          {previewDiff && (
            <div className="space-y-3">
              <div className="p-3 bg-orange-50 border border-orange-200 rounded-lg">
                <div className="flex items-center space-x-2 text-sm">
                  <span className="font-medium">Changes that will be applied:</span>
                  {renderDiffSummary(previewDiff)}
                </div>
              </div>
            </div>
          )}
          
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setRollbackDialog(null)}
              disabled={isRollingBack}
            >
              Cancel
            </Button>
            <Button
              onClick={confirmRollback}
              disabled={isRollingBack}
              className="bg-orange-600 hover:bg-orange-700"
            >
              {isRollingBack ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                  Rolling back...
                </>
              ) : (
                <>
                  <RotateCcw className="w-4 h-4 mr-2" />
                  Confirm Rollback
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
}
