import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Icons } from '@/components/ui/icons';

export function Hero() {
  return (
    <section className="py-12 md:py-24 lg:py-32">
      <div className="container px-4 md:px-6">
        <div className="flex flex-col items-center space-y-4 text-center">
          <div className="space-y-2">
            <h1 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl lg:text-6xl/none">
              Build Your Perfect Resume with{' '}
              <span className="text-primary">AI Assistance</span>
            </h1>
            <p className="mx-auto max-w-[700px] text-gray-500 md:text-xl dark:text-gray-400">
              Create professional, ATS-optimized resumes and cover letters in minutes. 
              Our AI-powered platform helps you stand out in today's competitive job market.
            </p>
          </div>
          <div className="space-x-4">
            <Button asChild size="lg">
              <Link href="/auth/signup">
                Get Started Free
                <Icons.chevronRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
            <Button variant="outline" size="lg" asChild>
              <Link href="/templates">
                View Templates
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
}
