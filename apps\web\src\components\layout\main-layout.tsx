'use client';

import { ReactNode } from 'react';
import { Header } from './header';
import { Footer } from './footer';
import { cn } from '@/lib/utils';

interface MainLayoutProps {
  children: ReactNode;
  className?: string;
  showHeader?: boolean;
  showFooter?: boolean;
  containerized?: boolean;
}

export function MainLayout({
  children,
  className,
  showHeader = true,
  showFooter = true,
  containerized = true,
}: MainLayoutProps) {
  return (
    <div className="min-h-screen flex flex-col">
      {showHeader && <Header />}
      
      <main className={cn('flex-1', className)}>
        {containerized ? (
          <div className="container py-6">
            {children}
          </div>
        ) : (
          children
        )}
      </main>
      
      {showFooter && <Footer />}
    </div>
  );
}
