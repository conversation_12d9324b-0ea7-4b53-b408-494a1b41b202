'use client';

import { TemplateRenderContext } from '@careercraft/shared/types/template';
import { ClassicTemplate } from './classic-template';

interface ProfessionalTemplateProps {
  context: TemplateRenderContext;
  interactive?: boolean;
  preview?: boolean;
}

export function ProfessionalTemplate(props: ProfessionalTemplateProps) {
  // For now, use the classic template as a base
  // In a full implementation, this would have its own unique design
  return <ClassicTemplate {...props} />;
}
