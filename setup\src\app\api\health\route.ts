import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET() {
  try {
    // Test database connection
    await prisma.$queryRaw`SELECT 1`
    
    // Test Redis connection (if configured)
    let redisStatus = 'not_configured'
    if (process.env.REDIS_URL) {
      try {
        // Add Redis health check here if needed
        redisStatus = 'connected'
      } catch {
        redisStatus = 'error'
      }
    }

    return NextResponse.json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      services: {
        database: 'connected',
        redis: redisStatus,
        openai: process.env.OPENAI_API_KEY ? 'configured' : 'not_configured',
        stripe: process.env.STRIPE_SECRET_KEY ? 'configured' : 'not_configured',
      },
      version: '1.0.0',
    })
  } catch (error) {
    return NextResponse.json(
      {
        status: 'unhealthy',
        error: 'Database connection failed',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    )
  }
}
