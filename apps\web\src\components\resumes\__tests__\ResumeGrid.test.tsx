import { describe, it, expect, vi, beforeEach } from 'vitest'
import { screen, fireEvent, waitFor } from '@testing-library/react'
import { ResumeGrid } from '../ResumeGrid'
import { render, mockResumes } from '@/test/utils'

// Mock the useRouter hook
const mockPush = vi.fn()
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: mockPush,
  }),
}))

describe('ResumeGrid', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders resume cards with correct information', async () => {
    render(<ResumeGrid />)

    await waitFor(() => {
      // Check for resume titles
      expect(screen.getByText('Software Engineer Resume')).toBeInTheDocument()
      expect(screen.getByText('Product Manager Resume')).toBeInTheDocument()
      expect(screen.getByText('Data Scientist Resume')).toBeInTheDocument()

      // Check for descriptions
      expect(screen.getByText('Full-stack developer position at tech companies')).toBeInTheDocument()
      expect(screen.getByText('Product management roles in SaaS companies')).toBeInTheDocument()

      // Check for template names
      expect(screen.getByText('Modern Professional')).toBeInTheDocument()
      expect(screen.getByText('Classic Professional')).toBeInTheDocument()
    })
  })

  it('displays correct status badges', async () => {
    render(<ResumeGrid />)

    await waitFor(() => {
      expect(screen.getByText('PUBLISHED')).toBeInTheDocument()
      expect(screen.getByText('DRAFT')).toBeInTheDocument()
      expect(screen.getByText('ARCHIVED')).toBeInTheDocument()
    })
  })

  it('shows loading state initially', () => {
    render(<ResumeGrid />)

    // Should show loading skeletons
    const loadingElements = document.querySelectorAll('.animate-pulse')
    expect(loadingElements.length).toBeGreaterThan(0)
  })

  it('filters resumes by search query', async () => {
    render(<ResumeGrid searchQuery="Software" />)

    await waitFor(() => {
      // Should show only the Software Engineer resume
      expect(screen.getByText('Software Engineer Resume')).toBeInTheDocument()
      expect(screen.queryByText('Product Manager Resume')).not.toBeInTheDocument()
    })
  })

  it('filters resumes by status', async () => {
    render(<ResumeGrid statusFilter="draft" />)

    await waitFor(() => {
      // Should show only draft resumes
      expect(screen.getByText('Product Manager Resume')).toBeInTheDocument()
      expect(screen.queryByText('Software Engineer Resume')).not.toBeInTheDocument()
    })
  })

  it('shows empty state when no resumes found', async () => {
    render(<ResumeGrid searchQuery="nonexistent" />)

    await waitFor(() => {
      expect(screen.getByText('No resumes found')).toBeInTheDocument()
      expect(screen.getByText('Try adjusting your search or filters')).toBeInTheDocument()
      expect(screen.getByRole('button', { name: /create resume/i })).toBeInTheDocument()
    })
  })

  it('navigates to resume editor when edit button is clicked', async () => {
    render(<ResumeGrid />)

    await waitFor(() => {
      const editButtons = screen.getAllByText('Edit')
      fireEvent.click(editButtons[0])
    })

    expect(mockPush).toHaveBeenCalledWith('/dashboard/resumes/1/edit')
  })

  it('navigates to resume view when view button is clicked', async () => {
    render(<ResumeGrid />)

    await waitFor(() => {
      const viewButtons = screen.getAllByText('View')
      fireEvent.click(viewButtons[0])
    })

    expect(mockPush).toHaveBeenCalledWith('/dashboard/resumes/1')
  })

  it('opens dropdown menu when more options button is clicked', async () => {
    render(<ResumeGrid />)

    await waitFor(() => {
      // Find the first more options button (three dots)
      const moreButtons = screen.getAllByRole('button')
      const moreOptionsButton = moreButtons.find(button => 
        button.getAttribute('class')?.includes('opacity-0')
      )
      
      if (moreOptionsButton) {
        fireEvent.click(moreOptionsButton)
        
        // Should show dropdown menu items
        expect(screen.getByText('Download')).toBeInTheDocument()
        expect(screen.getByText('Duplicate')).toBeInTheDocument()
        expect(screen.getByText('Delete')).toBeInTheDocument()
      }
    })
  })

  it('displays resume metadata correctly', async () => {
    render(<ResumeGrid />)

    await waitFor(() => {
      // Check for creation and update times (mocked)
      const timeElements = screen.getAllByText(/ago/)
      expect(timeElements.length).toBeGreaterThan(0)

      // Check for public indicator
      expect(screen.getByText('Public')).toBeInTheDocument()
    })
  })

  it('applies correct status colors', async () => {
    render(<ResumeGrid />)

    await waitFor(() => {
      const publishedBadge = screen.getByText('PUBLISHED')
      expect(publishedBadge).toHaveClass('bg-green-100', 'text-green-800')

      const draftBadge = screen.getByText('DRAFT')
      expect(draftBadge).toHaveClass('bg-yellow-100', 'text-yellow-800')

      const archivedBadge = screen.getByText('ARCHIVED')
      expect(archivedBadge).toHaveClass('bg-gray-100', 'text-gray-800')
    })
  })

  it('has hover effects on resume cards', async () => {
    render(<ResumeGrid />)

    await waitFor(() => {
      const resumeCards = document.querySelectorAll('.glass-card')
      resumeCards.forEach(card => {
        expect(card).toHaveClass('hover:scale-105')
      })
    })
  })

  it('shows resume preview placeholder', async () => {
    render(<ResumeGrid />)

    await waitFor(() => {
      // Check for preview placeholder elements
      const previewElements = document.querySelectorAll('.bg-gray-300, .bg-gray-200')
      expect(previewElements.length).toBeGreaterThan(0)
    })
  })

  it('handles download action', async () => {
    const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {})
    
    render(<ResumeGrid />)

    await waitFor(() => {
      // Open dropdown menu
      const moreButtons = screen.getAllByRole('button')
      const moreOptionsButton = moreButtons.find(button => 
        button.getAttribute('class')?.includes('opacity-0')
      )
      
      if (moreOptionsButton) {
        fireEvent.click(moreOptionsButton)
        
        const downloadButton = screen.getByText('Download')
        fireEvent.click(downloadButton)
        
        expect(consoleSpy).toHaveBeenCalledWith('Downloading resume:', '1')
      }
    })

    consoleSpy.mockRestore()
  })

  it('handles duplicate action', async () => {
    const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {})
    
    render(<ResumeGrid />)

    await waitFor(() => {
      // Open dropdown menu and click duplicate
      const moreButtons = screen.getAllByRole('button')
      const moreOptionsButton = moreButtons.find(button => 
        button.getAttribute('class')?.includes('opacity-0')
      )
      
      if (moreOptionsButton) {
        fireEvent.click(moreOptionsButton)
        
        const duplicateButton = screen.getByText('Duplicate')
        fireEvent.click(duplicateButton)
        
        expect(consoleSpy).toHaveBeenCalledWith('Duplicating resume:', '1')
      }
    })

    consoleSpy.mockRestore()
  })

  it('handles delete action', async () => {
    const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {})
    
    render(<ResumeGrid />)

    await waitFor(() => {
      // Open dropdown menu and click delete
      const moreButtons = screen.getAllByRole('button')
      const moreOptionsButton = moreButtons.find(button => 
        button.getAttribute('class')?.includes('opacity-0')
      )
      
      if (moreOptionsButton) {
        fireEvent.click(moreOptionsButton)
        
        const deleteButton = screen.getByText('Delete')
        fireEvent.click(deleteButton)
        
        expect(consoleSpy).toHaveBeenCalledWith('Deleting resume:', '1')
      }
    })

    consoleSpy.mockRestore()
  })

  it('applies glassmorphism styling', async () => {
    render(<ResumeGrid />)

    await waitFor(() => {
      const glassCards = document.querySelectorAll('.glass-card')
      expect(glassCards.length).toBeGreaterThan(0)
    })
  })

  it('is responsive on different screen sizes', async () => {
    render(<ResumeGrid />)

    await waitFor(() => {
      // Check for responsive grid classes
      const gridContainer = document.querySelector('.grid')
      expect(gridContainer).toHaveClass('grid-cols-1', 'md:grid-cols-2', 'lg:grid-cols-3')
    })
  })

  it('navigates to create resume from empty state', async () => {
    render(<ResumeGrid searchQuery="nonexistent" />)

    await waitFor(() => {
      const createButton = screen.getByRole('button', { name: /create resume/i })
      fireEvent.click(createButton)
    })

    expect(mockPush).toHaveBeenCalledWith('/dashboard/resumes/new')
  })
})
