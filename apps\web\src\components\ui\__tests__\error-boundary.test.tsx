import { describe, it, expect, jest, beforeEach } from '@jest/globals';
import { render, screen, fireEvent } from '@testing-library/react';
import {
  ErrorBoundary,
  DefaultErrorFallback,
  AsyncErrorBoundary,
  use<PERSON>rrorHand<PERSON>,
  withErrorBoundary,
  ErrorPage,
} from '../error-boundary';

// Mock console.error to avoid noise in tests
const originalConsoleError = console.error;
beforeEach(() => {
  console.error = jest.fn();
});

afterEach(() => {
  console.error = originalConsoleError;
});

// Component that throws an error
function ThrowError({ shouldThrow = false }: { shouldThrow?: boolean }) {
  if (shouldThrow) {
    throw new Error('Test error');
  }
  return <div>No error</div>;
}

describe('ErrorBoundary', () => {
  it('should render children when there is no error', () => {
    render(
      <ErrorBoundary>
        <div>Test content</div>
      </ErrorBoundary>
    );

    expect(screen.getByText('Test content')).toBeInTheDocument();
  });

  it('should render default error fallback when error occurs', () => {
    render(
      <ErrorBoundary>
        <ThrowError shouldThrow={true} />
      </ErrorBoundary>
    );

    expect(screen.getByText('Something went wrong')).toBeInTheDocument();
    expect(screen.getByText('An unexpected error occurred. Please try again.')).toBeInTheDocument();
  });

  it('should render custom fallback when provided', () => {
    const customFallback = <div>Custom error message</div>;

    render(
      <ErrorBoundary fallback={customFallback}>
        <ThrowError shouldThrow={true} />
      </ErrorBoundary>
    );

    expect(screen.getByText('Custom error message')).toBeInTheDocument();
  });

  it('should call onError callback when error occurs', () => {
    const onError = jest.fn();

    render(
      <ErrorBoundary onError={onError}>
        <ThrowError shouldThrow={true} />
      </ErrorBoundary>
    );

    expect(onError).toHaveBeenCalledWith(
      expect.any(Error),
      expect.objectContaining({
        componentStack: expect.any(String),
      })
    );
  });

  it('should reset error state when reset is called', () => {
    const { rerender } = render(
      <ErrorBoundary>
        <ThrowError shouldThrow={true} />
      </ErrorBoundary>
    );

    // Error should be displayed
    expect(screen.getByText('Something went wrong')).toBeInTheDocument();

    // Click try again button
    const tryAgainButton = screen.getByText('Try Again');
    fireEvent.click(tryAgainButton);

    // Re-render with no error
    rerender(
      <ErrorBoundary>
        <ThrowError shouldThrow={false} />
      </ErrorBoundary>
    );

    expect(screen.getByText('No error')).toBeInTheDocument();
  });
});

describe('DefaultErrorFallback', () => {
  it('should render with default props', () => {
    render(<DefaultErrorFallback />);

    expect(screen.getByText('Something went wrong')).toBeInTheDocument();
    expect(screen.getByText('An unexpected error occurred. Please try again.')).toBeInTheDocument();
  });

  it('should render with custom title and description', () => {
    render(
      <DefaultErrorFallback
        title="Custom Error Title"
        description="Custom error description"
      />
    );

    expect(screen.getByText('Custom Error Title')).toBeInTheDocument();
    expect(screen.getByText('Custom error description')).toBeInTheDocument();
  });

  it('should show error details in development', () => {
    const originalEnv = process.env.NODE_ENV;
    process.env.NODE_ENV = 'development';

    const error = new Error('Test error message');
    error.stack = 'Error stack trace';

    render(<DefaultErrorFallback error={error} showDetails={true} />);

    expect(screen.getByText('Error Details')).toBeInTheDocument();
    expect(screen.getByText('Test error message')).toBeInTheDocument();

    process.env.NODE_ENV = originalEnv;
  });

  it('should hide error details in production', () => {
    const originalEnv = process.env.NODE_ENV;
    process.env.NODE_ENV = 'production';

    const error = new Error('Test error message');

    render(<DefaultErrorFallback error={error} />);

    expect(screen.queryByText('Error Details')).not.toBeInTheDocument();

    process.env.NODE_ENV = originalEnv;
  });

  it('should call reset function when try again is clicked', () => {
    const reset = jest.fn();

    render(<DefaultErrorFallback reset={reset} />);

    const tryAgainButton = screen.getByText('Try Again');
    fireEvent.click(tryAgainButton);

    expect(reset).toHaveBeenCalled();
  });
});

describe('AsyncErrorBoundary', () => {
  it('should render children when there is no error', () => {
    render(
      <AsyncErrorBoundary>
        <div>Async content</div>
      </AsyncErrorBoundary>
    );

    expect(screen.getByText('Async content')).toBeInTheDocument();
  });

  it('should handle unhandled promise rejections', () => {
    render(
      <AsyncErrorBoundary>
        <div>Async content</div>
      </AsyncErrorBoundary>
    );

    // Simulate unhandled promise rejection
    const event = new Event('unhandledrejection') as any;
    event.reason = 'Async error';
    window.dispatchEvent(event);

    expect(screen.getByText('Something went wrong')).toBeInTheDocument();
  });

  it('should render custom fallback for async errors', () => {
    const customFallback = (error: Error, retry: () => void) => (
      <div>
        <span>Custom async error: {error.message}</span>
        <button onClick={retry}>Retry</button>
      </div>
    );

    render(
      <AsyncErrorBoundary fallback={customFallback}>
        <div>Async content</div>
      </AsyncErrorBoundary>
    );

    // Simulate unhandled promise rejection
    const event = new Event('unhandledrejection') as any;
    event.reason = 'Async error message';
    window.dispatchEvent(event);

    expect(screen.getByText('Custom async error: Async error message')).toBeInTheDocument();
    expect(screen.getByText('Retry')).toBeInTheDocument();
  });
});

describe('useErrorHandler', () => {
  function TestComponent() {
    const { error, handleError, resetError } = useErrorHandler();

    if (error) {
      return (
        <div>
          <span>Error: {error.message}</span>
          <button onClick={resetError}>Reset</button>
        </div>
      );
    }

    return (
      <button onClick={() => handleError(new Error('Test error'))}>
        Trigger Error
      </button>
    );
  }

  it('should handle errors and provide reset functionality', () => {
    render(<TestComponent />);

    // Initially no error
    expect(screen.getByText('Trigger Error')).toBeInTheDocument();

    // Trigger error
    fireEvent.click(screen.getByText('Trigger Error'));

    // Error should be displayed
    expect(screen.getByText('Error: Test error')).toBeInTheDocument();

    // Reset error
    fireEvent.click(screen.getByText('Reset'));

    // Should be back to initial state
    expect(screen.getByText('Trigger Error')).toBeInTheDocument();
  });
});

describe('withErrorBoundary', () => {
  function TestComponent({ shouldThrow = false }: { shouldThrow?: boolean }) {
    if (shouldThrow) {
      throw new Error('HOC test error');
    }
    return <div>HOC content</div>;
  }

  it('should wrap component with error boundary', () => {
    const WrappedComponent = withErrorBoundary(TestComponent);

    render(<WrappedComponent shouldThrow={false} />);

    expect(screen.getByText('HOC content')).toBeInTheDocument();
  });

  it('should catch errors in wrapped component', () => {
    const WrappedComponent = withErrorBoundary(TestComponent);

    render(<WrappedComponent shouldThrow={true} />);

    expect(screen.getByText('Something went wrong')).toBeInTheDocument();
  });

  it('should use custom error fallback', () => {
    const customFallback = <div>Custom HOC error</div>;
    const WrappedComponent = withErrorBoundary(TestComponent, customFallback);

    render(<WrappedComponent shouldThrow={true} />);

    expect(screen.getByText('Custom HOC error')).toBeInTheDocument();
  });
});

describe('ErrorPage', () => {
  it('should render 404 error page', () => {
    render(<ErrorPage statusCode={404} />);

    expect(screen.getByText('404')).toBeInTheDocument();
    expect(screen.getByText('Page Not Found')).toBeInTheDocument();
    expect(screen.getByText('The page you are looking for does not exist.')).toBeInTheDocument();
  });

  it('should render 500 error page', () => {
    render(<ErrorPage statusCode={500} />);

    expect(screen.getByText('500')).toBeInTheDocument();
    expect(screen.getByText('Internal Server Error')).toBeInTheDocument();
    expect(screen.getByText('An internal server error occurred.')).toBeInTheDocument();
  });

  it('should render custom title and description', () => {
    render(
      <ErrorPage
        statusCode={403}
        title="Custom Forbidden"
        description="Custom forbidden message"
      />
    );

    expect(screen.getByText('403')).toBeInTheDocument();
    expect(screen.getByText('Custom Forbidden')).toBeInTheDocument();
    expect(screen.getByText('Custom forbidden message')).toBeInTheDocument();
  });

  it('should show error details in development', () => {
    const originalEnv = process.env.NODE_ENV;
    process.env.NODE_ENV = 'development';

    const error = new Error('Development error');

    render(<ErrorPage error={error} />);

    expect(screen.getByText('Development Error Details')).toBeInTheDocument();

    process.env.NODE_ENV = originalEnv;
  });

  it('should hide home button when showHomeButton is false', () => {
    render(<ErrorPage showHomeButton={false} />);

    expect(screen.getByText('Go Back')).toBeInTheDocument();
    expect(screen.queryByText('Go Home')).not.toBeInTheDocument();
  });

  it('should show both navigation buttons by default', () => {
    render(<ErrorPage />);

    expect(screen.getByText('Go Back')).toBeInTheDocument();
    expect(screen.getByText('Go Home')).toBeInTheDocument();
  });
});

describe('Accessibility', () => {
  it('should have proper ARIA attributes', () => {
    render(<DefaultErrorFallback />);

    const alert = screen.getByRole('alert');
    expect(alert).toBeInTheDocument();
  });

  it('should have proper button roles', () => {
    render(<DefaultErrorFallback reset={() => {}} />);

    const button = screen.getByRole('button', { name: 'Try Again' });
    expect(button).toBeInTheDocument();
  });

  it('should have proper heading structure in ErrorPage', () => {
    render(<ErrorPage statusCode={404} />);

    const statusHeading = screen.getByRole('heading', { level: 1 });
    const titleHeading = screen.getByRole('heading', { level: 2 });

    expect(statusHeading).toHaveTextContent('404');
    expect(titleHeading).toHaveTextContent('Page Not Found');
  });
});
