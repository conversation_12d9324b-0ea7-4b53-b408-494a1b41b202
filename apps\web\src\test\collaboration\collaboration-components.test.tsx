/**
 * Collaboration Components Unit Tests
 * 
 * Tests for collaboration React components
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { SessionProvider } from 'next-auth/react'
import { CollaborationProvider, useCollaboration } from '@/components/collaboration/CollaborationProvider'
import { UserPresence } from '@/components/collaboration/UserPresence'

// Mock next-auth
vi.mock('next-auth/react', () => ({
  SessionProvider: ({ children }: any) => children,
  useSession: vi.fn()
}))

// Mock collaboration store
vi.mock('@/lib/collaboration/store', () => ({
  useCollaborationStore: vi.fn()
}))

// Mock sonner for toast notifications
vi.mock('sonner', () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn(),
    info: vi.fn()
  }
}))

// Mock fetch for API calls
global.fetch = vi.fn()

describe('Collaboration Components', () => {
  const mockSession = {
    user: {
      id: 'test-user-id',
      name: 'Test User',
      email: '<EMAIL>'
    },
    accessToken: 'mock-access-token'
  }

  const mockCollaborationStore = {
    sessionId: null,
    isConnected: false,
    isConnecting: false,
    connectionError: null,
    activeUsers: [],
    userPermissions: [],
    connect: vi.fn(),
    disconnect: vi.fn(),
    reset: vi.fn()
  }

  beforeEach(() => {
    vi.clearAllMocks()
    
    const { useSession } = require('next-auth/react')
    useSession.mockReturnValue({ data: mockSession })
    
    const { useCollaborationStore } = require('@/lib/collaboration/store')
    useCollaborationStore.mockReturnValue(mockCollaborationStore)
    
    ;(global.fetch as any).mockResolvedValue({
      ok: true,
      json: () => Promise.resolve({})
    })
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('CollaborationProvider', () => {
    const TestComponent = () => {
      const collaboration = useCollaboration()
      return (
        <div>
          <div data-testid="is-enabled">{collaboration.isEnabled.toString()}</div>
          <div data-testid="is-connected">{collaboration.isConnected.toString()}</div>
          <div data-testid="session-id">{collaboration.sessionId || 'null'}</div>
          <button 
            data-testid="start-collaboration" 
            onClick={() => collaboration.startCollaboration('resume-123')}
          >
            Start Collaboration
          </button>
          <button 
            data-testid="join-collaboration" 
            onClick={() => collaboration.joinCollaboration('token-123')}
          >
            Join Collaboration
          </button>
          <button 
            data-testid="stop-collaboration" 
            onClick={collaboration.stopCollaboration}
          >
            Stop Collaboration
          </button>
        </div>
      )
    }

    it('should provide collaboration context', () => {
      render(
        <SessionProvider session={mockSession}>
          <CollaborationProvider>
            <TestComponent />
          </CollaborationProvider>
        </SessionProvider>
      )

      expect(screen.getByTestId('is-enabled')).toHaveTextContent('true')
      expect(screen.getByTestId('is-connected')).toHaveTextContent('false')
      expect(screen.getByTestId('session-id')).toHaveTextContent('null')
    })

    it('should disable collaboration when not authenticated', () => {
      const { useSession } = require('next-auth/react')
      useSession.mockReturnValue({ data: null })

      render(
        <SessionProvider session={null}>
          <CollaborationProvider>
            <TestComponent />
          </CollaborationProvider>
        </SessionProvider>
      )

      expect(screen.getByTestId('is-enabled')).toHaveTextContent('false')
    })

    it('should start collaboration session', async () => {
      const user = userEvent.setup()
      
      ;(global.fetch as any).mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({
          session: {
            id: 'session-123',
            sessionToken: 'token-123'
          }
        })
      })

      render(
        <SessionProvider session={mockSession}>
          <CollaborationProvider>
            <TestComponent />
          </CollaborationProvider>
        </SessionProvider>
      )

      const startButton = screen.getByTestId('start-collaboration')
      await user.click(startButton)

      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalledWith('/api/collaboration/session', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            resumeId: 'resume-123',
            expiresIn: 24 * 60 * 60 * 1000
          })
        })
      })

      expect(mockCollaborationStore.connect).toHaveBeenCalledWith('session-123', 'mock-access-token')
    })

    it('should handle start collaboration errors', async () => {
      const user = userEvent.setup()
      const { toast } = require('sonner')
      
      ;(global.fetch as any).mockResolvedValueOnce({
        ok: false,
        json: () => Promise.resolve({ error: 'Resume not found' })
      })

      render(
        <SessionProvider session={mockSession}>
          <CollaborationProvider>
            <TestComponent />
          </CollaborationProvider>
        </SessionProvider>
      )

      const startButton = screen.getByTestId('start-collaboration')
      await user.click(startButton)

      await waitFor(() => {
        expect(toast.error).toHaveBeenCalledWith('Resume not found')
      })
    })

    it('should join collaboration session', async () => {
      const user = userEvent.setup()
      
      ;(global.fetch as any).mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({
          session: {
            id: 'session-123',
            sessionToken: 'token-123'
          }
        })
      })

      render(
        <SessionProvider session={mockSession}>
          <CollaborationProvider>
            <TestComponent />
          </CollaborationProvider>
        </SessionProvider>
      )

      const joinButton = screen.getByTestId('join-collaboration')
      await user.click(joinButton)

      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalledWith('/api/collaboration/session?sessionToken=token-123')
      })

      expect(mockCollaborationStore.connect).toHaveBeenCalled()
    })

    it('should stop collaboration', async () => {
      const user = userEvent.setup()
      const { toast } = require('sonner')

      render(
        <SessionProvider session={mockSession}>
          <CollaborationProvider>
            <TestComponent />
          </CollaborationProvider>
        </SessionProvider>
      )

      const stopButton = screen.getByTestId('stop-collaboration')
      await user.click(stopButton)

      expect(mockCollaborationStore.disconnect).toHaveBeenCalled()
      expect(mockCollaborationStore.reset).toHaveBeenCalled()
      expect(toast.info).toHaveBeenCalledWith('Left collaboration session')
    })

    it('should auto-connect when enabled', async () => {
      const { useCollaborationStore } = require('@/lib/collaboration/store')
      useCollaborationStore.mockReturnValue({
        ...mockCollaborationStore,
        sessionId: 'session-123'
      })

      render(
        <SessionProvider session={mockSession}>
          <CollaborationProvider autoConnect={true}>
            <TestComponent />
          </CollaborationProvider>
        </SessionProvider>
      )

      // Auto-connect should not trigger without sessionToken
      expect(mockCollaborationStore.connect).not.toHaveBeenCalled()
    })

    it('should show connection status notifications', () => {
      const { toast } = require('sonner')
      const { useCollaborationStore } = require('@/lib/collaboration/store')
      
      // Test connected state
      useCollaborationStore.mockReturnValue({
        ...mockCollaborationStore,
        isConnected: true
      })

      const { rerender } = render(
        <SessionProvider session={mockSession}>
          <CollaborationProvider>
            <TestComponent />
          </CollaborationProvider>
        </SessionProvider>
      )

      expect(toast.success).toHaveBeenCalledWith('Connected to collaboration session')

      // Test error state
      useCollaborationStore.mockReturnValue({
        ...mockCollaborationStore,
        connectionError: 'Connection failed'
      })

      rerender(
        <SessionProvider session={mockSession}>
          <CollaborationProvider>
            <TestComponent />
          </CollaborationProvider>
        </SessionProvider>
      )

      expect(toast.error).toHaveBeenCalledWith('Collaboration error: Connection failed')
    })
  })

  describe('UserPresence', () => {
    const mockActiveUsers = [
      {
        userId: 'user-1',
        userName: 'John Doe',
        userAvatar: 'avatar1.jpg',
        status: 'active',
        lastSeen: Date.now(),
        cursor: {
          sectionPath: 'personalInfo',
          position: 10
        }
      },
      {
        userId: 'user-2',
        userName: 'Jane Smith',
        userAvatar: 'avatar2.jpg',
        status: 'idle',
        lastSeen: Date.now() - 60000
      }
    ]

    beforeEach(() => {
      const { useCollaborationStore } = require('@/lib/collaboration/store')
      useCollaborationStore.mockReturnValue({
        ...mockCollaborationStore,
        isConnected: true,
        activeUsers: mockActiveUsers,
        userPermissions: [
          { userId: 'user-1', permissionLevel: 'admin' },
          { userId: 'user-2', permissionLevel: 'edit' }
        ]
      })
    })

    it('should display active users count', () => {
      render(
        <SessionProvider session={mockSession}>
          <CollaborationProvider>
            <UserPresence />
          </CollaborationProvider>
        </SessionProvider>
      )

      expect(screen.getByText('2')).toBeInTheDocument()
      expect(screen.getByText('Connected')).toBeInTheDocument()
    })

    it('should show user avatars', () => {
      render(
        <SessionProvider session={mockSession}>
          <CollaborationProvider>
            <UserPresence />
          </CollaborationProvider>
        </SessionProvider>
      )

      // Check for user avatars (by alt text)
      expect(screen.getByAltText('John Doe')).toBeInTheDocument()
      expect(screen.getByAltText('Jane Smith')).toBeInTheDocument()
    })

    it('should open user details popover', async () => {
      const user = userEvent.setup()

      render(
        <SessionProvider session={mockSession}>
          <CollaborationProvider>
            <UserPresence />
          </CollaborationProvider>
        </SessionProvider>
      )

      const usersButton = screen.getByRole('button')
      await user.click(usersButton)

      await waitFor(() => {
        expect(screen.getByText('Active Collaborators (2)')).toBeInTheDocument()
        expect(screen.getByText('John Doe')).toBeInTheDocument()
        expect(screen.getByText('Jane Smith')).toBeInTheDocument()
      })
    })

    it('should display user status and permissions', async () => {
      const user = userEvent.setup()

      render(
        <SessionProvider session={mockSession}>
          <CollaborationProvider>
            <UserPresence />
          </CollaborationProvider>
        </SessionProvider>
      )

      const usersButton = screen.getByRole('button')
      await user.click(usersButton)

      await waitFor(() => {
        expect(screen.getByText('active')).toBeInTheDocument()
        expect(screen.getByText('idle')).toBeInTheDocument()
      })
    })

    it('should show cursor information', async () => {
      const user = userEvent.setup()

      render(
        <SessionProvider session={mockSession}>
          <CollaborationProvider>
            <UserPresence />
          </CollaborationProvider>
        </SessionProvider>
      )

      const usersButton = screen.getByRole('button')
      await user.click(usersButton)

      await waitFor(() => {
        expect(screen.getByText('Editing: personalInfo')).toBeInTheDocument()
      })
    })

    it('should not render when not connected', () => {
      const { useCollaborationStore } = require('@/lib/collaboration/store')
      useCollaborationStore.mockReturnValue({
        ...mockCollaborationStore,
        isConnected: false,
        isConnecting: false
      })

      const { container } = render(
        <SessionProvider session={mockSession}>
          <CollaborationProvider>
            <UserPresence />
          </CollaborationProvider>
        </SessionProvider>
      )

      expect(container.firstChild).toBeNull()
    })

    it('should show connecting status', () => {
      const { useCollaborationStore } = require('@/lib/collaboration/store')
      useCollaborationStore.mockReturnValue({
        ...mockCollaborationStore,
        isConnecting: true,
        activeUsers: []
      })

      render(
        <SessionProvider session={mockSession}>
          <CollaborationProvider>
            <UserPresence />
          </CollaborationProvider>
        </SessionProvider>
      )

      expect(screen.getByText('Connecting...')).toBeInTheDocument()
    })

    it('should handle empty user list', async () => {
      const user = userEvent.setup()
      const { useCollaborationStore } = require('@/lib/collaboration/store')
      useCollaborationStore.mockReturnValue({
        ...mockCollaborationStore,
        isConnected: true,
        activeUsers: []
      })

      render(
        <SessionProvider session={mockSession}>
          <CollaborationProvider>
            <UserPresence />
          </CollaborationProvider>
        </SessionProvider>
      )

      // Should still show connection status but no users button
      expect(screen.getByText('Connected')).toBeInTheDocument()
      expect(screen.queryByRole('button')).toBeNull()
    })

    it('should show overflow indicator for many users', () => {
      const manyUsers = Array.from({ length: 5 }, (_, i) => ({
        userId: `user-${i}`,
        userName: `User ${i}`,
        status: 'active',
        lastSeen: Date.now()
      }))

      const { useCollaborationStore } = require('@/lib/collaboration/store')
      useCollaborationStore.mockReturnValue({
        ...mockCollaborationStore,
        isConnected: true,
        activeUsers: manyUsers,
        userPermissions: []
      })

      render(
        <SessionProvider session={mockSession}>
          <CollaborationProvider>
            <UserPresence />
          </CollaborationProvider>
        </SessionProvider>
      )

      expect(screen.getByText('5')).toBeInTheDocument()
      expect(screen.getByText('+2')).toBeInTheDocument() // Shows +2 for users beyond first 3
    })
  })

  describe('Error Handling', () => {
    it('should handle collaboration context errors', () => {
      const TestComponentWithoutProvider = () => {
        try {
          useCollaboration()
          return <div>Should not render</div>
        } catch (error) {
          return <div>Error caught</div>
        }
      }

      expect(() => {
        render(<TestComponentWithoutProvider />)
      }).toThrow('useCollaboration must be used within a CollaborationProvider')
    })

    it('should handle network errors gracefully', async () => {
      const user = userEvent.setup()
      const { toast } = require('sonner')
      
      ;(global.fetch as any).mockRejectedValueOnce(new Error('Network error'))

      const TestComponent = () => {
        const collaboration = useCollaboration()
        return (
          <button 
            data-testid="start-collaboration" 
            onClick={() => collaboration.startCollaboration('resume-123')}
          >
            Start Collaboration
          </button>
        )
      }

      render(
        <SessionProvider session={mockSession}>
          <CollaborationProvider>
            <TestComponent />
          </CollaborationProvider>
        </SessionProvider>
      )

      const startButton = screen.getByTestId('start-collaboration')
      await user.click(startButton)

      await waitFor(() => {
        expect(toast.error).toHaveBeenCalledWith('Failed to start collaboration')
      })
    })
  })
})
