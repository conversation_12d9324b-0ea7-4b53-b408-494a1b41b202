/**
 * Job Matching Components Unit Tests
 * 
 * Tests for job matching React components
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { JobSearch } from '@/components/job-matching/JobSearch'
import { JobRecommendations } from '@/components/job-matching/JobRecommendations'
import { ApplicationTracker } from '@/components/job-matching/ApplicationTracker'

// Mock sonner for toast notifications
vi.mock('sonner', () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn(),
    info: vi.fn()
  }
}))

// Mock date-fns
vi.mock('date-fns', () => ({
  formatDistanceToNow: vi.fn(() => '2 hours ago'),
  format: vi.fn(() => '2024-01-15 10:30 AM')
}))

// Mock fetch for API calls
global.fetch = vi.fn()

describe('Job Matching Components', () => {
  const mockJobs = [
    {
      id: 'job-1',
      title: 'Software Developer',
      company: 'Tech Corp',
      description: 'Develop amazing software applications using modern technologies',
      location: 'San Francisco, CA',
      salaryMin: 80000,
      salaryMax: 120000,
      employmentType: 'full-time',
      remoteType: 'hybrid',
      experienceLevel: 'mid',
      skills: ['JavaScript', 'React', 'Node.js'],
      postedDate: new Date('2024-01-15T10:30:00Z'),
      sourceUrl: 'https://example.com/job/1',
      isActive: true
    },
    {
      id: 'job-2',
      title: 'Senior Frontend Developer',
      company: 'Startup Inc',
      description: 'Build user interfaces for our cutting-edge platform',
      location: 'Remote',
      salaryMin: 100000,
      salaryMax: 150000,
      employmentType: 'full-time',
      remoteType: 'remote',
      experienceLevel: 'senior',
      skills: ['React', 'TypeScript', 'CSS'],
      postedDate: new Date('2024-01-14T09:00:00Z'),
      sourceUrl: 'https://example.com/job/2',
      isActive: true
    }
  ]

  const mockRecommendations = [
    {
      id: 'rec-1',
      jobPostingId: 'job-1',
      matchScore: 85,
      reasoning: { skillMatch: true, locationMatch: true },
      isViewed: false,
      isSaved: false,
      isDismissed: false,
      recommendedAt: new Date('2024-01-15T08:00:00Z'),
      jobPosting: mockJobs[0]
    },
    {
      id: 'rec-2',
      jobPostingId: 'job-2',
      matchScore: 92,
      reasoning: { skillMatch: true, experienceMatch: true },
      isViewed: true,
      isSaved: true,
      isDismissed: false,
      recommendedAt: new Date('2024-01-14T12:00:00Z'),
      jobPosting: mockJobs[1]
    }
  ]

  const mockApplications = [
    {
      id: 'app-1',
      userId: 'user-1',
      jobPostingId: 'job-1',
      status: 'applied',
      appliedDate: new Date('2024-01-15T10:00:00Z'),
      lastUpdated: new Date('2024-01-15T10:00:00Z'),
      notes: 'Excited about this opportunity',
      jobPosting: mockJobs[0]
    },
    {
      id: 'app-2',
      userId: 'user-1',
      jobPostingId: 'job-2',
      status: 'interview',
      appliedDate: new Date('2024-01-14T14:00:00Z'),
      lastUpdated: new Date('2024-01-16T09:00:00Z'),
      salaryOffered: 130000,
      jobPosting: mockJobs[1]
    }
  ]

  beforeEach(() => {
    vi.clearAllMocks()
    
    ;(global.fetch as any).mockResolvedValue({
      ok: true,
      json: () => Promise.resolve({})
    })
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('JobSearch', () => {
    it('should render job search interface', async () => {
      ;(global.fetch as any).mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({ jobs: mockJobs })
      })

      render(<JobSearch />)

      expect(screen.getByText('Job Search')).toBeInTheDocument()
      expect(screen.getByPlaceholderText('Search jobs, companies, or keywords...')).toBeInTheDocument()
      expect(screen.getByPlaceholderText('Location')).toBeInTheDocument()
      expect(screen.getByText('Search')).toBeInTheDocument()
      expect(screen.getByText('Filters')).toBeInTheDocument()

      await waitFor(() => {
        expect(screen.getByText('Software Developer')).toBeInTheDocument()
        expect(screen.getByText('Senior Frontend Developer')).toBeInTheDocument()
      })
    })

    it('should perform job search with criteria', async () => {
      const user = userEvent.setup()
      
      ;(global.fetch as any).mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({ jobs: mockJobs })
      })

      render(<JobSearch />)

      // Enter search criteria
      const searchInput = screen.getByPlaceholderText('Search jobs, companies, or keywords...')
      await user.type(searchInput, 'developer')

      const locationInput = screen.getByPlaceholderText('Location')
      await user.type(locationInput, 'San Francisco')

      // Click search
      const searchButton = screen.getByText('Search')
      await user.click(searchButton)

      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalledWith(
          expect.stringContaining('/api/job-matching/jobs?action=search&query=developer&location=San Francisco')
        )
      })
    })

    it('should show and hide filters', async () => {
      const user = userEvent.setup()
      
      ;(global.fetch as any).mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({ jobs: [] })
      })

      render(<JobSearch />)

      // Filters should be hidden initially
      expect(screen.queryByText('Work Type')).not.toBeInTheDocument()

      // Click filters button
      const filtersButton = screen.getByText('Filters')
      await user.click(filtersButton)

      // Filters should now be visible
      expect(screen.getByText('Work Type')).toBeInTheDocument()
      expect(screen.getByText('Employment Type')).toBeInTheDocument()
      expect(screen.getByText('Experience Level')).toBeInTheDocument()
    })

    it('should handle job selection', async () => {
      const onJobSelect = vi.fn()
      
      ;(global.fetch as any).mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({ jobs: mockJobs })
      })

      render(<JobSearch onJobSelect={onJobSelect} />)

      await waitFor(() => {
        expect(screen.getByText('Software Developer')).toBeInTheDocument()
      })

      const jobTitle = screen.getByText('Software Developer')
      fireEvent.click(jobTitle)

      expect(onJobSelect).toHaveBeenCalledWith(mockJobs[0])
    })

    it('should handle job application', async () => {
      const user = userEvent.setup()
      const { toast } = require('sonner')
      
      ;(global.fetch as any)
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ jobs: mockJobs })
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ success: true })
        })

      render(<JobSearch />)

      await waitFor(() => {
        expect(screen.getByText('Software Developer')).toBeInTheDocument()
      })

      const applyButton = screen.getAllByText('Apply Now')[0]
      await user.click(applyButton)

      await waitFor(() => {
        expect(toast.success).toHaveBeenCalledWith('Application submitted successfully')
      })
    })

    it('should show empty state when no jobs found', async () => {
      ;(global.fetch as any).mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({ jobs: [] })
      })

      render(<JobSearch />)

      await waitFor(() => {
        expect(screen.getByText('No jobs found')).toBeInTheDocument()
        expect(screen.getByText('Try adjusting your search criteria or filters to find more opportunities.')).toBeInTheDocument()
      })
    })

    it('should handle API errors', async () => {
      const { toast } = require('sonner')
      
      ;(global.fetch as any).mockResolvedValueOnce({
        ok: false,
        status: 500
      })

      render(<JobSearch />)

      await waitFor(() => {
        expect(toast.error).toHaveBeenCalledWith('Failed to search jobs')
      })
    })
  })

  describe('JobRecommendations', () => {
    it('should render job recommendations', async () => {
      ;(global.fetch as any).mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({ recommendations: mockRecommendations })
      })

      render(<JobRecommendations />)

      await waitFor(() => {
        expect(screen.getByText('AI Job Recommendations')).toBeInTheDocument()
        expect(screen.getByText('Software Developer')).toBeInTheDocument()
        expect(screen.getByText('Senior Frontend Developer')).toBeInTheDocument()
        expect(screen.getByText('85%')).toBeInTheDocument()
        expect(screen.getByText('92%')).toBeInTheDocument()
      })
    })

    it('should show new recommendation badge', async () => {
      ;(global.fetch as any).mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({ recommendations: mockRecommendations })
      })

      render(<JobRecommendations />)

      await waitFor(() => {
        expect(screen.getByText('New')).toBeInTheDocument()
      })
    })

    it('should handle recommendation actions', async () => {
      const user = userEvent.setup()
      const { toast } = require('sonner')
      
      ;(global.fetch as any)
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ recommendations: mockRecommendations })
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ success: true })
        })

      render(<JobRecommendations />)

      await waitFor(() => {
        expect(screen.getByText('Software Developer')).toBeInTheDocument()
      })

      // Find and click save button
      const saveButtons = screen.getAllByRole('button')
      const saveButton = saveButtons.find(button => 
        button.querySelector('svg') && button.getAttribute('class')?.includes('text-gray-500')
      )
      
      if (saveButton) {
        await user.click(saveButton)

        await waitFor(() => {
          expect(toast.success).toHaveBeenCalledWith('Recommendation saved successfully')
        })
      }
    })

    it('should refresh recommendations', async () => {
      const user = userEvent.setup()
      const { toast } = require('sonner')
      
      ;(global.fetch as any)
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ recommendations: mockRecommendations })
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ recommendations: mockRecommendations })
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ recommendations: mockRecommendations })
        })

      render(<JobRecommendations />)

      await waitFor(() => {
        expect(screen.getByText('Refresh')).toBeInTheDocument()
      })

      const refreshButton = screen.getByText('Refresh')
      await user.click(refreshButton)

      await waitFor(() => {
        expect(toast.success).toHaveBeenCalledWith('Recommendations refreshed')
      })
    })

    it('should show empty state when no recommendations', async () => {
      ;(global.fetch as any).mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({ recommendations: [] })
      })

      render(<JobRecommendations />)

      await waitFor(() => {
        expect(screen.getByText('No recommendations yet')).toBeInTheDocument()
        expect(screen.getByText('Complete your profile and set job preferences to get personalized recommendations.')).toBeInTheDocument()
      })
    })

    it('should handle job selection from recommendations', async () => {
      const onJobSelect = vi.fn()
      
      ;(global.fetch as any).mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({ recommendations: mockRecommendations })
      })

      render(<JobRecommendations onJobSelect={onJobSelect} />)

      await waitFor(() => {
        expect(screen.getByText('Software Developer')).toBeInTheDocument()
      })

      const jobTitle = screen.getByText('Software Developer')
      fireEvent.click(jobTitle)

      expect(onJobSelect).toHaveBeenCalledWith(mockJobs[0])
    })
  })

  describe('ApplicationTracker', () => {
    it('should render application tracker with stats', async () => {
      const mockStats = {
        total: 10,
        applied: 5,
        screening: 2,
        interview: 2,
        offer: 1,
        rejected: 0,
        withdrawn: 0,
        responseRate: 50,
        averageResponseTime: 7
      }

      ;(global.fetch as any)
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ applications: mockApplications })
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ stats: mockStats })
        })

      render(<ApplicationTracker />)

      await waitFor(() => {
        expect(screen.getByText('Job Applications')).toBeInTheDocument()
        expect(screen.getByText('Total Applications')).toBeInTheDocument()
        expect(screen.getByText('Response Rate')).toBeInTheDocument()
        expect(screen.getByText('Interviews')).toBeInTheDocument()
        expect(screen.getByText('Offers')).toBeInTheDocument()
        expect(screen.getByText('10')).toBeInTheDocument()
        expect(screen.getByText('50%')).toBeInTheDocument()
      })
    })

    it('should display applications list', async () => {
      ;(global.fetch as any)
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ applications: mockApplications })
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ stats: {} })
        })

      render(<ApplicationTracker />)

      await waitFor(() => {
        expect(screen.getByText('Software Developer')).toBeInTheDocument()
        expect(screen.getByText('Senior Frontend Developer')).toBeInTheDocument()
        expect(screen.getByText('Tech Corp')).toBeInTheDocument()
        expect(screen.getByText('Startup Inc')).toBeInTheDocument()
      })
    })

    it('should filter applications by status', async () => {
      const user = userEvent.setup()
      
      ;(global.fetch as any)
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ applications: mockApplications })
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ stats: {} })
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ applications: [mockApplications[1]] })
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ stats: {} })
        })

      render(<ApplicationTracker />)

      await waitFor(() => {
        expect(screen.getByText('All Status')).toBeInTheDocument()
      })

      // Change filter to interview status
      const statusFilter = screen.getByDisplayValue('All Status')
      await user.click(statusFilter)
      
      const interviewOption = screen.getByText('Interview')
      await user.click(interviewOption)

      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalledWith(
          expect.stringContaining('status=interview')
        )
      })
    })

    it('should update application status', async () => {
      const user = userEvent.setup()
      const { toast } = require('sonner')
      
      ;(global.fetch as any)
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ applications: mockApplications })
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ stats: {} })
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ success: true })
        })

      render(<ApplicationTracker />)

      await waitFor(() => {
        expect(screen.getByText('Software Developer')).toBeInTheDocument()
      })

      // Find status dropdown for first application
      const statusDropdowns = screen.getAllByDisplayValue('applied')
      await user.click(statusDropdowns[0])

      const interviewOption = screen.getByText('interview')
      await user.click(interviewOption)

      await waitFor(() => {
        expect(toast.success).toHaveBeenCalledWith('Application status updated')
      })
    })

    it('should add notes to application', async () => {
      const user = userEvent.setup()
      const { toast } = require('sonner')
      
      ;(global.fetch as any)
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ applications: mockApplications })
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ stats: {} })
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ success: true })
        })

      render(<ApplicationTracker />)

      await waitFor(() => {
        expect(screen.getByText('Software Developer')).toBeInTheDocument()
      })

      // Click add note button
      const addNoteButtons = screen.getAllByText('Add Note')
      await user.click(addNoteButtons[0])

      await waitFor(() => {
        expect(screen.getByText('Add Note')).toBeInTheDocument()
      })

      // Enter note text
      const noteTextarea = screen.getByPlaceholderText('Enter your note...')
      await user.type(noteTextarea, 'Follow up next week')

      // Submit note
      const submitButton = screen.getByRole('button', { name: 'Add Note' })
      await user.click(submitButton)

      await waitFor(() => {
        expect(toast.success).toHaveBeenCalledWith('Note added successfully')
      })
    })

    it('should show empty state when no applications', async () => {
      ;(global.fetch as any)
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ applications: [] })
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ stats: {} })
        })

      render(<ApplicationTracker />)

      await waitFor(() => {
        expect(screen.getByText('No applications found')).toBeInTheDocument()
        expect(screen.getByText("You haven't applied to any jobs yet. Start by searching for opportunities!")).toBeInTheDocument()
      })
    })

    it('should handle API errors', async () => {
      const { toast } = require('sonner')
      
      ;(global.fetch as any).mockResolvedValueOnce({
        ok: false,
        status: 500
      })

      render(<ApplicationTracker />)

      await waitFor(() => {
        expect(toast.error).toHaveBeenCalledWith('Failed to load applications')
      })
    })
  })

  describe('Integration', () => {
    it('should handle network errors gracefully', async () => {
      const { toast } = require('sonner')
      
      ;(global.fetch as any).mockRejectedValue(new Error('Network error'))

      render(<JobSearch />)

      await waitFor(() => {
        expect(toast.error).toHaveBeenCalledWith('Failed to search jobs')
      })
    })

    it('should handle malformed API responses', async () => {
      const { toast } = require('sonner')
      
      ;(global.fetch as any).mockResolvedValueOnce({
        ok: true,
        json: () => Promise.reject(new Error('Invalid JSON'))
      })

      render(<JobRecommendations />)

      await waitFor(() => {
        expect(toast.error).toHaveBeenCalledWith('Failed to load job recommendations')
      })
    })

    it('should show loading states correctly', () => {
      ;(global.fetch as any).mockImplementation(() => new Promise(() => {})) // Never resolves

      render(<JobSearch />)

      // Should show loading skeletons
      expect(document.querySelectorAll('.animate-pulse')).toHaveLength(5)
    })
  })
})
