/**
 * Collaboration Comments API
 * 
 * Handles collaboration comments and discussions
 */

import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { collaborationService, CreateCommentSchema } from '@/lib/collaboration/service'
import { z } from 'zod'

// Request schemas
const ResolveCommentSchema = z.object({
  commentId: z.string()
})

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { sessionId, sectionPath, content, parentId } = CreateCommentSchema.parse(body)

    // Create comment
    const comment = await collaborationService.createComment({
      sessionId,
      userId: session.user.id,
      sectionPath,
      content,
      parentId
    })

    return NextResponse.json({
      success: true,
      comment,
      message: 'Comment created successfully'
    })
  } catch (error) {
    console.error('Comment creation error:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const sessionId = searchParams.get('sessionId')
    const sectionPath = searchParams.get('sectionPath')

    if (!sessionId) {
      return NextResponse.json(
        { error: 'Session ID required' },
        { status: 400 }
      )
    }

    // Check if user has permission to view comments
    const permission = await collaborationService.getUserPermission(sessionId, session.user.id)
    if (!permission) {
      return NextResponse.json(
        { error: 'Access denied' },
        { status: 403 }
      )
    }

    // Get comments
    const comments = await collaborationService.getComments(sessionId, sectionPath || undefined)

    return NextResponse.json({ comments })
  } catch (error) {
    console.error('Comments get error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const action = body.action

    if (action === 'resolve') {
      const { commentId } = ResolveCommentSchema.parse(body)
      
      await collaborationService.resolveComment(commentId, session.user.id)

      return NextResponse.json({
        success: true,
        message: 'Comment resolved successfully'
      })
    }

    return NextResponse.json(
      { error: 'Invalid action' },
      { status: 400 }
    )
  } catch (error) {
    console.error('Comment update error:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal server error' },
      { status: 500 }
    )
  }
}
