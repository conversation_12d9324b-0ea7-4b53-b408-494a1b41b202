/**
 * CareerCraft Browser Extension - Content Script
 * 
 * Handles form detection, field analysis, and autofill functionality
 * on job application pages.
 */

import browser from 'webextension-polyfill'
import { FormDetector } from '../lib/form-detection/form-detector'
import { FieldMapper } from '../lib/field-mapping/field-mapper'
import { AutofillEngine } from '../lib/autofill/autofill-engine'
import { UIOverlay } from '../lib/ui/ui-overlay'
import { SiteAdapter } from '../lib/site-adapters/site-adapter'

interface DetectedForm {
  element: HTMLFormElement
  fields: FormField[]
  confidence: number
  type: 'job-application' | 'profile' | 'other'
  metadata: FormMetadata
}

interface FormField {
  element: HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
  type: string
  label: string
  placeholder: string
  required: boolean
  confidence: number
  mappedTo?: string
}

interface FormMetadata {
  url: string
  title: string
  company?: string
  jobTitle?: string
  source: string
}

class ContentScript {
  private formDetector: FormDetector
  private fieldMapper: FieldMapper
  private autofillEngine: AutofillEngine
  private uiOverlay: UIOverlay
  private siteAdapter: SiteAdapter
  
  private detectedForms: DetectedForm[] = []
  private isEnabled: boolean = true
  private isProcessing: boolean = false
  private observer: MutationObserver | null = null

  constructor() {
    this.formDetector = new FormDetector()
    this.fieldMapper = new FieldMapper()
    this.autofillEngine = new AutofillEngine()
    this.uiOverlay = new UIOverlay()
    this.siteAdapter = new SiteAdapter()

    this.initialize()
  }

  private async initialize() {
    console.log('🎯 CareerCraft Content Script: Initializing...')

    try {
      // Check if extension is enabled
      const state = await this.getExtensionState()
      this.isEnabled = state.formDetectionEnabled

      if (!this.isEnabled) {
        console.log('📴 CareerCraft: Form detection disabled')
        return
      }

      // Set up site-specific adapter
      await this.setupSiteAdapter()

      // Set up message listeners
      this.setupMessageListeners()

      // Start form detection
      await this.startFormDetection()

      // Set up DOM observer for dynamic content
      this.setupDOMObserver()

      console.log('✅ CareerCraft Content Script: Initialized successfully')
    } catch (error) {
      console.error('❌ CareerCraft Content Script: Initialization failed:', error)
    }
  }

  private async getExtensionState() {
    try {
      const response = await browser.runtime.sendMessage({
        type: 'GET_STATE'
      })
      return response.data
    } catch (error) {
      console.error('Failed to get extension state:', error)
      return { formDetectionEnabled: false }
    }
  }

  private async setupSiteAdapter() {
    const hostname = window.location.hostname
    this.siteAdapter.configure(hostname)
  }

  private setupMessageListeners() {
    browser.runtime.onMessage.addListener((message, sender, sendResponse) => {
      return this.handleMessage(message, sender, sendResponse)
    })
  }

  private async handleMessage(message: any, sender: any, sendResponse: any) {
    const { type, data } = message

    switch (type) {
      case 'START_FORM_DETECTION':
        return await this.startFormDetection()

      case 'QUICK_FILL':
        return await this.quickFill()

      case 'FILL_FORM':
        return await this.fillForm(data)

      case 'GET_DETECTED_FORMS':
        return { success: true, data: this.detectedForms }

      case 'TOGGLE_OVERLAY':
        return this.toggleOverlay(data.visible)

      case 'UPDATE_SETTINGS':
        return this.updateSettings(data)

      default:
        return { success: false, error: 'Unknown message type' }
    }
  }

  private async startFormDetection() {
    if (this.isProcessing) {
      return { success: false, error: 'Already processing' }
    }

    this.isProcessing = true

    try {
      console.log('🔍 CareerCraft: Starting form detection...')

      // Clear previous detections
      this.detectedForms = []

      // Detect forms on the page
      const forms = await this.formDetector.detectForms(document)

      for (const form of forms) {
        const detectedForm = await this.analyzeForm(form)
        if (detectedForm && detectedForm.confidence > 0.7) {
          this.detectedForms.push(detectedForm)
        }
      }

      console.log(`📋 CareerCraft: Detected ${this.detectedForms.length} relevant forms`)

      // Notify background script
      if (this.detectedForms.length > 0) {
        await this.notifyFormDetected()
        
        // Show UI overlay
        this.uiOverlay.showFormDetected(this.detectedForms)
      }

      return { success: true, data: { formsDetected: this.detectedForms.length } }
    } catch (error) {
      console.error('Form detection failed:', error)
      return { success: false, error: error.message }
    } finally {
      this.isProcessing = false
    }
  }

  private async analyzeForm(formElement: HTMLFormElement): Promise<DetectedForm | null> {
    try {
      // Get form metadata
      const metadata = this.extractFormMetadata(formElement)

      // Detect and analyze form fields
      const fields = await this.analyzeFormFields(formElement)

      // Determine form type and confidence
      const { type, confidence } = this.classifyForm(formElement, fields, metadata)

      if (confidence < 0.5) {
        return null // Not confident enough this is a job application form
      }

      return {
        element: formElement,
        fields,
        confidence,
        type,
        metadata
      }
    } catch (error) {
      console.error('Form analysis failed:', error)
      return null
    }
  }

  private extractFormMetadata(formElement: HTMLFormElement): FormMetadata {
    const url = window.location.href
    const title = document.title
    const hostname = window.location.hostname

    // Use site adapter to extract specific metadata
    const siteMetadata = this.siteAdapter.extractMetadata(document)

    return {
      url,
      title,
      company: siteMetadata.company,
      jobTitle: siteMetadata.jobTitle,
      source: hostname
    }
  }

  private async analyzeFormFields(formElement: HTMLFormElement): Promise<FormField[]> {
    const fields: FormField[] = []
    
    // Get all input, textarea, and select elements
    const fieldElements = formElement.querySelectorAll(
      'input:not([type="hidden"]):not([type="submit"]):not([type="button"]), textarea, select'
    )

    for (const element of fieldElements) {
      const field = await this.analyzeField(element as HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement)
      if (field) {
        fields.push(field)
      }
    }

    return fields
  }

  private async analyzeField(element: HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement): Promise<FormField | null> {
    try {
      // Extract field information
      const label = this.extractFieldLabel(element)
      const placeholder = element.getAttribute('placeholder') || ''
      const type = this.determineFieldType(element, label, placeholder)
      const required = element.hasAttribute('required') || element.getAttribute('aria-required') === 'true'

      // Map field to profile data
      const { mappedTo, confidence } = await this.fieldMapper.mapField({
        element,
        label,
        placeholder,
        type
      })

      return {
        element,
        type,
        label,
        placeholder,
        required,
        confidence,
        mappedTo
      }
    } catch (error) {
      console.error('Field analysis failed:', error)
      return null
    }
  }

  private extractFieldLabel(element: HTMLElement): string {
    // Try multiple methods to find the field label
    const id = element.id
    const name = element.getAttribute('name')

    // Look for associated label element
    if (id) {
      const label = document.querySelector(`label[for="${id}"]`)
      if (label) {
        return label.textContent?.trim() || ''
      }
    }

    // Look for parent label
    const parentLabel = element.closest('label')
    if (parentLabel) {
      return parentLabel.textContent?.replace(element.textContent || '', '').trim() || ''
    }

    // Look for nearby text
    const nearbyText = this.findNearbyText(element)
    if (nearbyText) {
      return nearbyText
    }

    // Fallback to name or id
    return name || id || ''
  }

  private findNearbyText(element: HTMLElement): string {
    // Look for text in previous siblings
    let sibling = element.previousElementSibling
    while (sibling) {
      const text = sibling.textContent?.trim()
      if (text && text.length > 0 && text.length < 100) {
        return text
      }
      sibling = sibling.previousElementSibling
    }

    // Look for text in parent elements
    let parent = element.parentElement
    while (parent && parent !== document.body) {
      const text = parent.textContent?.trim()
      if (text && text.length > 0 && text.length < 100) {
        // Make sure it's not the entire form text
        if (text !== parent.closest('form')?.textContent?.trim()) {
          return text
        }
      }
      parent = parent.parentElement
    }

    return ''
  }

  private determineFieldType(
    element: HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement,
    label: string,
    placeholder: string
  ): string {
    // Use site adapter for site-specific field type detection
    const siteType = this.siteAdapter.determineFieldType(element, label, placeholder)
    if (siteType) {
      return siteType
    }

    // Generic field type detection
    if (element.tagName === 'SELECT') {
      return 'select'
    }

    if (element.tagName === 'TEXTAREA') {
      return 'textarea'
    }

    const inputElement = element as HTMLInputElement
    const inputType = inputElement.type.toLowerCase()

    // Map HTML input types to semantic types
    const typeMap: { [key: string]: string } = {
      'email': 'email',
      'tel': 'phone',
      'url': 'url',
      'date': 'date',
      'file': 'file',
      'checkbox': 'checkbox',
      'radio': 'radio'
    }

    return typeMap[inputType] || 'text'
  }

  private classifyForm(
    formElement: HTMLFormElement,
    fields: FormField[],
    metadata: FormMetadata
  ): { type: 'job-application' | 'profile' | 'other', confidence: number } {
    // Use site adapter for site-specific classification
    const siteClassification = this.siteAdapter.classifyForm(formElement, fields, metadata)
    if (siteClassification) {
      return siteClassification
    }

    // Generic form classification
    let score = 0
    const totalFields = fields.length

    if (totalFields === 0) {
      return { type: 'other', confidence: 0 }
    }

    // Check for job application indicators
    const jobApplicationKeywords = [
      'resume', 'cv', 'cover letter', 'application', 'apply',
      'experience', 'education', 'skills', 'employment',
      'position', 'job', 'career', 'salary', 'availability'
    ]

    const formText = formElement.textContent?.toLowerCase() || ''
    const urlText = metadata.url.toLowerCase()
    const titleText = metadata.title.toLowerCase()

    // Score based on keywords in form content
    jobApplicationKeywords.forEach(keyword => {
      if (formText.includes(keyword)) score += 0.1
      if (urlText.includes(keyword)) score += 0.15
      if (titleText.includes(keyword)) score += 0.1
    })

    // Score based on field types
    const fieldTypeScores: { [key: string]: number } = {
      'firstName': 0.05,
      'lastName': 0.05,
      'email': 0.1,
      'phone': 0.1,
      'resume': 0.3,
      'coverLetter': 0.2,
      'experience': 0.15,
      'education': 0.1,
      'skills': 0.1
    }

    fields.forEach(field => {
      if (field.mappedTo && fieldTypeScores[field.mappedTo]) {
        score += fieldTypeScores[field.mappedTo]
      }
    })

    // Normalize score
    const confidence = Math.min(score, 1.0)

    // Determine type
    let type: 'job-application' | 'profile' | 'other' = 'other'
    if (confidence > 0.7) {
      type = 'job-application'
    } else if (confidence > 0.4) {
      type = 'profile'
    }

    return { type, confidence }
  }

  private async notifyFormDetected() {
    const primaryForm = this.detectedForms[0]
    
    await browser.runtime.sendMessage({
      type: 'FORM_DETECTED',
      data: {
        url: window.location.href,
        formType: primaryForm.type,
        fieldCount: primaryForm.fields.length,
        confidence: primaryForm.confidence,
        company: primaryForm.metadata.company,
        jobTitle: primaryForm.metadata.jobTitle
      }
    })
  }

  private async quickFill() {
    if (this.detectedForms.length === 0) {
      return { success: false, error: 'No forms detected' }
    }

    const primaryForm = this.detectedForms[0]
    return await this.fillForm({ formIndex: 0, customization: true })
  }

  private async fillForm(data: { formIndex: number, customization?: boolean }) {
    if (data.formIndex >= this.detectedForms.length) {
      return { success: false, error: 'Invalid form index' }
    }

    const form = this.detectedForms[data.formIndex]

    try {
      // Request autofill data from background script
      const response = await browser.runtime.sendMessage({
        type: 'REQUEST_AUTOFILL',
        data: {
          formFields: form.fields.map(f => ({
            type: f.type,
            label: f.label,
            mappedTo: f.mappedTo,
            required: f.required
          })),
          jobDescription: this.extractJobDescription(),
          customization: data.customization
        }
      })

      if (!response.success) {
        throw new Error(response.error)
      }

      // Fill the form with received data
      await this.autofillEngine.fillForm(form, response.data)

      // Show success notification
      this.uiOverlay.showSuccess('Form filled successfully!')

      return { success: true }
    } catch (error) {
      console.error('Form filling failed:', error)
      this.uiOverlay.showError('Failed to fill form: ' + error.message)
      return { success: false, error: error.message }
    }
  }

  private extractJobDescription(): string {
    // Use site adapter to extract job description
    return this.siteAdapter.extractJobDescription(document) || ''
  }

  private toggleOverlay(visible: boolean) {
    if (visible) {
      this.uiOverlay.show()
    } else {
      this.uiOverlay.hide()
    }
    return { success: true }
  }

  private updateSettings(settings: any) {
    this.isEnabled = settings.formDetectionEnabled
    return { success: true }
  }

  private setupDOMObserver() {
    // Watch for dynamic content changes
    this.observer = new MutationObserver((mutations) => {
      let shouldRedetect = false

      mutations.forEach((mutation) => {
        if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
          // Check if new forms were added
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              const element = node as Element
              if (element.tagName === 'FORM' || element.querySelector('form')) {
                shouldRedetect = true
              }
            }
          })
        }
      })

      if (shouldRedetect) {
        // Debounce re-detection
        setTimeout(() => {
          this.startFormDetection()
        }, 1000)
      }
    })

    this.observer.observe(document.body, {
      childList: true,
      subtree: true
    })
  }

  public destroy() {
    if (this.observer) {
      this.observer.disconnect()
    }
    this.uiOverlay.destroy()
  }
}

// Initialize content script
const contentScript = new ContentScript()

// Clean up on page unload
window.addEventListener('beforeunload', () => {
  contentScript.destroy()
})

// Export for testing
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { ContentScript }
}
