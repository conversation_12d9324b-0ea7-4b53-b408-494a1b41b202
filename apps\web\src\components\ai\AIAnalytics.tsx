'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { 
  BarChart3, 
  TrendingUp, 
  Target, 
  Zap, 
  Brain, 
  Award,
  Users,
  Clock,
  CheckCircle,
  AlertTriangle,
  Sparkles,
  RefreshCw
} from 'lucide-react'

interface AIUsageStats {
  totalOptimizations: number
  totalGenerations: number
  totalAnalyses: number
  averageATSScore: number
  improvementRate: number
  timesSaved: number
  creditsUsed: number
  creditsRemaining: number
}

interface ResumePerformance {
  resumeId: string
  resumeTitle: string
  atsScore: number
  keywordMatch: number
  lastOptimized: Date
  improvements: number
  status: 'excellent' | 'good' | 'needs_work'
}

interface AIInsight {
  id: string
  type: 'improvement' | 'warning' | 'success'
  title: string
  description: string
  action?: string
  priority: 'high' | 'medium' | 'low'
}

export function AIAnalytics() {
  const [stats, setStats] = useState<AIUsageStats | null>(null)
  const [resumePerformance, setResumePerformance] = useState<ResumePerformance[]>([])
  const [insights, setInsights] = useState<AIInsight[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [chartData, setChartData] = useState<any[]>([])

  useEffect(() => {
    loadAnalytics()
  }, [])

  const loadAnalytics = async () => {
    setIsLoading(true)
    try {
      // Simulate API call - replace with actual data fetching
      await new Promise(resolve => setTimeout(resolve, 1500))

      // Mock data
      const mockStats: AIUsageStats = {
        totalOptimizations: 47,
        totalGenerations: 23,
        totalAnalyses: 31,
        averageATSScore: 82,
        improvementRate: 34,
        timesSaved: 12.5,
        creditsUsed: 156,
        creditsRemaining: 344
      }

      const mockPerformance: ResumePerformance[] = [
        {
          resumeId: '1',
          resumeTitle: 'Software Engineer Resume',
          atsScore: 87,
          keywordMatch: 92,
          lastOptimized: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
          improvements: 5,
          status: 'excellent'
        },
        {
          resumeId: '2',
          resumeTitle: 'Product Manager Resume',
          atsScore: 74,
          keywordMatch: 68,
          lastOptimized: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
          improvements: 3,
          status: 'good'
        },
        {
          resumeId: '3',
          resumeTitle: 'Data Scientist Resume',
          atsScore: 61,
          keywordMatch: 55,
          lastOptimized: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000),
          improvements: 1,
          status: 'needs_work'
        }
      ]

      const mockInsights: AIInsight[] = [
        {
          id: '1',
          type: 'improvement',
          title: 'Keyword Optimization Opportunity',
          description: 'Your Data Scientist resume is missing 8 key technical skills mentioned in recent job postings.',
          action: 'Optimize Keywords',
          priority: 'high'
        },
        {
          id: '2',
          type: 'success',
          title: 'Great ATS Performance',
          description: 'Your Software Engineer resume has achieved an excellent ATS score of 87%.',
          priority: 'low'
        },
        {
          id: '3',
          type: 'warning',
          title: 'Resume Needs Update',
          description: 'Your Product Manager resume hasn\'t been optimized in 5 days. Consider refreshing it with recent trends.',
          action: 'Update Resume',
          priority: 'medium'
        }
      ]

      setStats(mockStats)
      setResumePerformance(mockPerformance)
      setInsights(mockInsights)

      // Set chart data for analytics visualization
      setChartData([
        { name: 'Optimizations', value: mockStats.totalOptimizations },
        { name: 'Generations', value: mockStats.totalGenerations },
        { name: 'Analyses', value: mockStats.totalAnalyses }
      ])
    } catch (error) {
      console.error('Error loading analytics:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const getStatusColor = (status: ResumePerformance['status']) => {
    switch (status) {
      case 'excellent': return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
      case 'good': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400'
      case 'needs_work': return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
    }
  }

  const getInsightIcon = (type: AIInsight['type']) => {
    switch (type) {
      case 'improvement': return <TrendingUp className="w-4 h-4 text-blue-600" />
      case 'warning': return <AlertTriangle className="w-4 h-4 text-yellow-600" />
      case 'success': return <CheckCircle className="w-4 h-4 text-green-600" />
    }
  }

  const getPriorityColor = (priority: AIInsight['priority']) => {
    switch (priority) {
      case 'high': return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
      case 'medium': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400'
      case 'low': return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400'
    }
  }

  if (isLoading) {
    return (
      <div className="space-y-6">
        {[...Array(4)].map((_, i) => (
          <Card key={i} className="glass-card animate-pulse">
            <CardContent className="p-6">
              <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-1/2 mb-4"></div>
              <div className="h-20 bg-gray-200 rounded"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card className="glass-card">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Brain className="w-5 h-5 text-purple-600" />
            <span>AI Analytics Dashboard</span>
          </CardTitle>
          <CardDescription>
            Track your AI usage, resume performance, and optimization insights
          </CardDescription>
        </CardHeader>
      </Card>

      {/* Usage Statistics - Key Metrics Dashboard */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card className="glass-card">
            <CardContent className="p-4">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 rounded-lg bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center">
                  <Zap className="w-5 h-5 text-white" />
                </div>
                <div>
                  <div className="text-2xl font-bold">{stats.totalOptimizations}</div>
                  <div className="text-sm text-muted-foreground">Optimizations</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="glass-card">
            <CardContent className="p-4">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 rounded-lg bg-gradient-to-br from-green-500 to-green-600 flex items-center justify-center">
                  <Sparkles className="w-5 h-5 text-white" />
                </div>
                <div>
                  <div className="text-2xl font-bold">{stats.totalGenerations}</div>
                  <div className="text-sm text-muted-foreground">Generations</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="glass-card">
            <CardContent className="p-4">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 rounded-lg bg-gradient-to-br from-purple-500 to-purple-600 flex items-center justify-center">
                  <BarChart3 className="w-5 h-5 text-white" />
                </div>
                <div>
                  <div className="text-2xl font-bold">{stats.averageATSScore}%</div>
                  <div className="text-sm text-muted-foreground">Avg ATS Score</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="glass-card">
            <CardContent className="p-4">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 rounded-lg bg-gradient-to-br from-yellow-500 to-yellow-600 flex items-center justify-center">
                  <Clock className="w-5 h-5 text-white" />
                </div>
                <div>
                  <div className="text-2xl font-bold">{stats.timesSaved}h</div>
                  <div className="text-sm text-muted-foreground">Time Saved</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* AI Credits */}
      {stats && (
        <Card className="glass-card">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Award className="w-5 h-5 text-yellow-600" />
              <span>AI Credits</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Credits Used</span>
                <span className="text-lg font-bold">{stats.creditsUsed} / {stats.creditsUsed + stats.creditsRemaining}</span>
              </div>
              <Progress 
                value={(stats.creditsUsed / (stats.creditsUsed + stats.creditsRemaining)) * 100} 
                className="h-2"
              />
              <div className="flex items-center justify-between text-sm text-muted-foreground">
                <span>{stats.creditsRemaining} credits remaining</span>
                <span>{Math.round((stats.creditsUsed / (stats.creditsUsed + stats.creditsRemaining)) * 100)}% used</span>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Resume Performance Chart & Metrics */}
      <Card className="glass-card">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Target className="w-5 h-5 text-blue-600" />
            <span>Resume Performance Chart</span>
          </CardTitle>
          <CardDescription>
            ATS scores, metrics, and optimization status for your resumes
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {resumePerformance.map((resume) => (
              <div key={resume.resumeId} className="glass-input p-4 rounded-lg">
                <div className="flex items-center justify-between mb-3">
                  <div>
                    <h4 className="font-medium">{resume.resumeTitle}</h4>
                    <p className="text-sm text-muted-foreground">
                      Last optimized {resume.lastOptimized.toLocaleDateString()}
                    </p>
                  </div>
                  <Badge className={getStatusColor(resume.status)}>
                    {resume.status.replace('_', ' ')}
                  </Badge>
                </div>

                {/* Performance Chart Grid */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <div className="text-sm font-medium mb-1">ATS Score</div>
                    <div className="flex items-center space-x-2">
                      <Progress value={resume.atsScore} className="flex-1 h-2" />
                      <span className="text-sm font-bold">{resume.atsScore}%</span>
                    </div>
                  </div>

                  <div>
                    <div className="text-sm font-medium mb-1">Keyword Match</div>
                    <div className="flex items-center space-x-2">
                      <Progress value={resume.keywordMatch} className="flex-1 h-2" />
                      <span className="text-sm font-bold">{resume.keywordMatch}%</span>
                    </div>
                  </div>

                  <div>
                    <div className="text-sm font-medium mb-1">Improvements</div>
                    <div className="text-sm font-bold">{resume.improvements} optimizations</div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* AI Insights */}
      <Card className="glass-card">
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Brain className="w-5 h-5 text-purple-600" />
              <span>AI Insights</span>
            </div>
            <Button
              size="sm"
              variant="outline"
              onClick={loadAnalytics}
              className="glass-input"
            >
              <RefreshCw className="w-3 h-3 mr-1" />
              Refresh
            </Button>
          </CardTitle>
          <CardDescription>
            Personalized recommendations to improve your resume performance
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {insights.map((insight) => (
              <div key={insight.id} className="glass-input p-4 rounded-lg">
                <div className="flex items-start justify-between mb-2">
                  <div className="flex items-center space-x-2">
                    {getInsightIcon(insight.type)}
                    <h4 className="font-medium">{insight.title}</h4>
                    <Badge className={getPriorityColor(insight.priority)}>
                      {insight.priority}
                    </Badge>
                  </div>
                </div>
                
                <p className="text-sm text-muted-foreground mb-3">
                  {insight.description}
                </p>

                {insight.action && (
                  <Button
                    size="sm"
                    className="glass-card bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white border-0"
                  >
                    {insight.action}
                  </Button>
                )}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
