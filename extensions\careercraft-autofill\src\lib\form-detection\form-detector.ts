/**
 * Form Detection Engine
 * 
 * Intelligently detects and analyzes forms on web pages
 * to identify job application forms and their characteristics.
 */

export interface DetectedForm {
  element: HTMLFormElement
  confidence: number
  type: 'job-application' | 'profile' | 'contact' | 'other'
  characteristics: FormCharacteristics
}

export interface FormCharacteristics {
  fieldCount: number
  hasFileUpload: boolean
  hasTextArea: boolean
  hasRequiredFields: boolean
  hasPersonalInfo: boolean
  hasWorkExperience: boolean
  hasEducation: boolean
  complexity: 'simple' | 'medium' | 'complex'
}

export class FormDetector {
  private jobApplicationKeywords = [
    'resume', 'cv', 'cover letter', 'application', 'apply',
    'experience', 'education', 'skills', 'employment',
    'position', 'job', 'career', 'salary', 'availability',
    'work history', 'qualifications', 'references'
  ]

  private personalInfoKeywords = [
    'first name', 'last name', 'full name', 'email',
    'phone', 'address', 'city', 'state', 'zip',
    'country', 'linkedin', 'portfolio', 'website'
  ]

  private workExperienceKeywords = [
    'company', 'employer', 'job title', 'position',
    'start date', 'end date', 'current', 'responsibilities',
    'achievements', 'years of experience'
  ]

  private educationKeywords = [
    'school', 'university', 'college', 'degree',
    'major', 'gpa', 'graduation', 'education',
    'certification', 'course', 'training'
  ]

  /**
   * Detect all forms on the page and analyze them
   */
  async detectForms(document: Document): Promise<DetectedForm[]> {
    const forms = Array.from(document.querySelectorAll('form'))
    const detectedForms: DetectedForm[] = []

    for (const form of forms) {
      const detection = await this.analyzeForm(form)
      if (detection) {
        detectedForms.push(detection)
      }
    }

    // Sort by confidence score
    return detectedForms.sort((a, b) => b.confidence - a.confidence)
  }

  /**
   * Analyze a single form element
   */
  private async analyzeForm(form: HTMLFormElement): Promise<DetectedForm | null> {
    try {
      // Skip forms that are clearly not job applications
      if (this.shouldSkipForm(form)) {
        return null
      }

      const characteristics = this.analyzeFormCharacteristics(form)
      const { type, confidence } = this.classifyForm(form, characteristics)

      // Only return forms with reasonable confidence
      if (confidence < 0.3) {
        return null
      }

      return {
        element: form,
        confidence,
        type,
        characteristics
      }
    } catch (error) {
      console.error('Error analyzing form:', error)
      return null
    }
  }

  /**
   * Check if form should be skipped entirely
   */
  private shouldSkipForm(form: HTMLFormElement): boolean {
    // Skip hidden forms
    if (form.style.display === 'none' || form.hidden) {
      return true
    }

    // Skip very small forms (likely search or login)
    const inputs = form.querySelectorAll('input, textarea, select')
    if (inputs.length < 3) {
      return true
    }

    // Skip forms with only hidden inputs
    const visibleInputs = Array.from(inputs).filter(input => {
      const element = input as HTMLElement
      return element.offsetWidth > 0 && element.offsetHeight > 0
    })
    if (visibleInputs.length < 2) {
      return true
    }

    // Skip search forms
    const formText = form.textContent?.toLowerCase() || ''
    if (formText.includes('search') && formText.length < 100) {
      return true
    }

    // Skip newsletter/subscription forms
    const subscriptionKeywords = ['newsletter', 'subscribe', 'unsubscribe', 'email updates']
    if (subscriptionKeywords.some(keyword => formText.includes(keyword))) {
      return true
    }

    return false
  }

  /**
   * Analyze form characteristics
   */
  private analyzeFormCharacteristics(form: HTMLFormElement): FormCharacteristics {
    const inputs = Array.from(form.querySelectorAll('input, textarea, select'))
    const formText = form.textContent?.toLowerCase() || ''

    const characteristics: FormCharacteristics = {
      fieldCount: inputs.length,
      hasFileUpload: inputs.some(input => 
        input.getAttribute('type') === 'file'
      ),
      hasTextArea: form.querySelector('textarea') !== null,
      hasRequiredFields: inputs.some(input => 
        input.hasAttribute('required') || 
        input.getAttribute('aria-required') === 'true'
      ),
      hasPersonalInfo: this.hasKeywords(formText, this.personalInfoKeywords),
      hasWorkExperience: this.hasKeywords(formText, this.workExperienceKeywords),
      hasEducation: this.hasKeywords(formText, this.educationKeywords),
      complexity: this.determineComplexity(inputs.length, formText)
    }

    return characteristics
  }

  /**
   * Check if text contains any of the given keywords
   */
  private hasKeywords(text: string, keywords: string[]): boolean {
    return keywords.some(keyword => text.includes(keyword.toLowerCase()))
  }

  /**
   * Determine form complexity
   */
  private determineComplexity(fieldCount: number, formText: string): 'simple' | 'medium' | 'complex' {
    if (fieldCount < 5) {
      return 'simple'
    } else if (fieldCount < 15) {
      return 'medium'
    } else {
      return 'complex'
    }
  }

  /**
   * Classify form type and calculate confidence
   */
  private classifyForm(
    form: HTMLFormElement, 
    characteristics: FormCharacteristics
  ): { type: DetectedForm['type'], confidence: number } {
    let confidence = 0
    let type: DetectedForm['type'] = 'other'

    const formText = form.textContent?.toLowerCase() || ''
    const formAction = form.action?.toLowerCase() || ''
    const formId = form.id?.toLowerCase() || ''
    const formClass = form.className?.toLowerCase() || ''

    // Check for job application indicators
    let jobApplicationScore = 0

    // URL and action indicators
    if (formAction.includes('apply') || formAction.includes('application')) {
      jobApplicationScore += 0.3
    }

    // Form attributes indicators
    if (formId.includes('application') || formId.includes('apply')) {
      jobApplicationScore += 0.2
    }
    if (formClass.includes('application') || formClass.includes('apply')) {
      jobApplicationScore += 0.2
    }

    // Content-based indicators
    const jobKeywordMatches = this.jobApplicationKeywords.filter(keyword => 
      formText.includes(keyword)
    ).length
    jobApplicationScore += Math.min(jobKeywordMatches * 0.1, 0.4)

    // Characteristic-based indicators
    if (characteristics.hasFileUpload) {
      jobApplicationScore += 0.2 // Resume/CV upload
    }
    if (characteristics.hasPersonalInfo) {
      jobApplicationScore += 0.1
    }
    if (characteristics.hasWorkExperience) {
      jobApplicationScore += 0.2
    }
    if (characteristics.hasEducation) {
      jobApplicationScore += 0.15
    }
    if (characteristics.complexity === 'complex') {
      jobApplicationScore += 0.1
    }

    // Page context indicators
    const pageTitle = document.title.toLowerCase()
    const pageUrl = window.location.href.toLowerCase()

    if (pageTitle.includes('apply') || pageTitle.includes('application') || pageTitle.includes('career')) {
      jobApplicationScore += 0.2
    }
    if (pageUrl.includes('apply') || pageUrl.includes('application') || pageUrl.includes('career') || pageUrl.includes('job')) {
      jobApplicationScore += 0.2
    }

    // Determine type and confidence
    if (jobApplicationScore > 0.6) {
      type = 'job-application'
      confidence = Math.min(jobApplicationScore, 0.95)
    } else if (jobApplicationScore > 0.4) {
      type = 'profile'
      confidence = jobApplicationScore * 0.8
    } else if (characteristics.hasPersonalInfo && characteristics.fieldCount > 5) {
      type = 'contact'
      confidence = 0.4
    } else {
      type = 'other'
      confidence = 0.2
    }

    return { type, confidence }
  }

  /**
   * Get form submission method and action
   */
  getFormSubmissionInfo(form: HTMLFormElement): {
    method: string
    action: string
    enctype: string
  } {
    return {
      method: form.method || 'GET',
      action: form.action || window.location.href,
      enctype: form.enctype || 'application/x-www-form-urlencoded'
    }
  }

  /**
   * Check if form is likely to be a multi-step form
   */
  isMultiStepForm(form: HTMLFormElement): boolean {
    const formText = form.textContent?.toLowerCase() || ''
    
    // Look for step indicators
    const stepIndicators = [
      'step 1', 'step 2', 'step 3',
      'page 1', 'page 2', 'page 3',
      'next', 'continue', 'previous',
      'progress', 'wizard'
    ]

    return stepIndicators.some(indicator => formText.includes(indicator))
  }

  /**
   * Get form validation requirements
   */
  getValidationRequirements(form: HTMLFormElement): {
    requiredFields: number
    hasClientValidation: boolean
    hasServerValidation: boolean
  } {
    const inputs = Array.from(form.querySelectorAll('input, textarea, select'))
    const requiredFields = inputs.filter(input => 
      input.hasAttribute('required') || 
      input.getAttribute('aria-required') === 'true'
    ).length

    const hasClientValidation = inputs.some(input => 
      input.hasAttribute('pattern') || 
      input.hasAttribute('min') || 
      input.hasAttribute('max') ||
      input.hasAttribute('minlength') ||
      input.hasAttribute('maxlength')
    )

    // Check for validation scripts
    const scripts = Array.from(document.querySelectorAll('script'))
    const hasServerValidation = scripts.some(script => 
      script.textContent?.includes('validation') ||
      script.textContent?.includes('validate')
    )

    return {
      requiredFields,
      hasClientValidation,
      hasServerValidation
    }
  }
}
