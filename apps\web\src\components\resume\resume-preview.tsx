'use client';

import { Resume, ResumeSectionType, WorkExperience, Education } from '@careercraft/shared/types/resume';
import { Card, CardContent } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Icons } from '@/components/ui/icons';
import { cn } from '@/lib/utils';

interface ResumePreviewProps {
  resume: Resume;
  className?: string;
}

export function ResumePreview({ resume, className }: ResumePreviewProps) {
  const { personalInfo, sections } = resume;

  const getWorkExperience = () => {
    const workSection = sections.find(s => s.type === ResumeSectionType.WORK_EXPERIENCE);
    return (workSection?.data as WorkExperience[]) || [];
  };

  const getEducation = () => {
    const educationSection = sections.find(s => s.type === ResumeSectionType.EDUCATION);
    return (educationSection?.data as Education[]) || [];
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return '';
    const date = new Date(dateString + '-01'); // Add day for month input
    return date.toLocaleDateString('en-US', { year: 'numeric', month: 'short' });
  };

  const formatDateRange = (startDate: string, endDate?: string, isCurrent?: boolean) => {
    const start = formatDate(startDate);
    if (isCurrent) return `${start} - Present`;
    if (!endDate) return start;
    return `${start} - ${formatDate(endDate)}`;
  };

  return (
    <Card className={cn('w-full max-w-4xl mx-auto bg-white text-black', className)}>
      <CardContent className="p-8 space-y-6">
        {/* Header */}
        <div className="text-center space-y-2">
          <h1 className="text-3xl font-bold text-gray-900">
            {personalInfo.firstName} {personalInfo.lastName}
          </h1>
          
          <div className="flex flex-wrap justify-center gap-4 text-sm text-gray-600">
            {personalInfo.email && (
              <div className="flex items-center gap-1">
                <Icons.mail className="h-4 w-4" />
                {personalInfo.email}
              </div>
            )}
            {personalInfo.phone && (
              <div className="flex items-center gap-1">
                <Icons.phone className="h-4 w-4" />
                {personalInfo.phone}
              </div>
            )}
            {personalInfo.location && (
              <div className="flex items-center gap-1">
                <Icons.mapPin className="h-4 w-4" />
                {personalInfo.location}
              </div>
            )}
          </div>

          {/* Links */}
          {(personalInfo.website || personalInfo.linkedin || personalInfo.github || personalInfo.portfolio) && (
            <div className="flex flex-wrap justify-center gap-4 text-sm text-blue-600">
              {personalInfo.website && (
                <a href={personalInfo.website} className="hover:underline flex items-center gap-1">
                  <Icons.externalLink className="h-4 w-4" />
                  Website
                </a>
              )}
              {personalInfo.linkedin && (
                <a href={personalInfo.linkedin} className="hover:underline flex items-center gap-1">
                  <Icons.linkedin className="h-4 w-4" />
                  LinkedIn
                </a>
              )}
              {personalInfo.github && (
                <a href={personalInfo.github} className="hover:underline flex items-center gap-1">
                  <Icons.gitHub className="h-4 w-4" />
                  GitHub
                </a>
              )}
              {personalInfo.portfolio && (
                <a href={personalInfo.portfolio} className="hover:underline flex items-center gap-1">
                  <Icons.externalLink className="h-4 w-4" />
                  Portfolio
                </a>
              )}
            </div>
          )}
        </div>

        {/* Professional Summary */}
        {personalInfo.summary && (
          <>
            <Separator />
            <div>
              <h2 className="text-xl font-semibold text-gray-900 mb-3">Professional Summary</h2>
              <p className="text-gray-700 leading-relaxed">{personalInfo.summary}</p>
            </div>
          </>
        )}

        {/* Work Experience */}
        {getWorkExperience().length > 0 && (
          <>
            <Separator />
            <div>
              <h2 className="text-xl font-semibold text-gray-900 mb-4">Work Experience</h2>
              <div className="space-y-6">
                {getWorkExperience().map((experience) => (
                  <div key={experience.id} className="space-y-2">
                    <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start">
                      <div>
                        <h3 className="text-lg font-medium text-gray-900">{experience.position}</h3>
                        <p className="text-gray-700 font-medium">{experience.company}</p>
                      </div>
                      <div className="text-sm text-gray-600 sm:text-right">
                        <p>{formatDateRange(experience.startDate, experience.endDate, experience.isCurrentRole)}</p>
                        <p>{experience.location}</p>
                      </div>
                    </div>
                    
                    <p className="text-gray-700 text-sm leading-relaxed">{experience.description}</p>
                    
                    {experience.achievements.length > 0 && (
                      <ul className="list-disc list-inside space-y-1 text-sm text-gray-700 ml-4">
                        {experience.achievements.map((achievement, index) => (
                          <li key={index}>{achievement}</li>
                        ))}
                      </ul>
                    )}
                    
                    {experience.technologies && experience.technologies.length > 0 && (
                      <div className="flex flex-wrap gap-2 mt-2">
                        {experience.technologies.map((tech, index) => (
                          <span
                            key={index}
                            className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded"
                          >
                            {tech}
                          </span>
                        ))}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </>
        )}

        {/* Education */}
        {getEducation().length > 0 && (
          <>
            <Separator />
            <div>
              <h2 className="text-xl font-semibold text-gray-900 mb-4">Education</h2>
              <div className="space-y-4">
                {getEducation().map((education) => (
                  <div key={education.id} className="space-y-1">
                    <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start">
                      <div>
                        <h3 className="text-lg font-medium text-gray-900">{education.degree}</h3>
                        <p className="text-gray-700">{education.field}</p>
                        <p className="text-gray-600">{education.institution}</p>
                      </div>
                      <div className="text-sm text-gray-600 sm:text-right">
                        <p>{formatDateRange(education.startDate, education.endDate, education.isCurrentlyEnrolled)}</p>
                        <p>{education.location}</p>
                      </div>
                    </div>
                    
                    {education.gpa && (
                      <p className="text-sm text-gray-600">GPA: {education.gpa}</p>
                    )}
                    
                    {education.honors && education.honors.length > 0 && (
                      <div className="text-sm text-gray-700">
                        <span className="font-medium">Honors: </span>
                        {education.honors.join(', ')}
                      </div>
                    )}
                    
                    {education.relevantCoursework && education.relevantCoursework.length > 0 && (
                      <div className="text-sm text-gray-700">
                        <span className="font-medium">Relevant Coursework: </span>
                        {education.relevantCoursework.join(', ')}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </>
        )}

        {/* Empty State */}
        {getWorkExperience().length === 0 && getEducation().length === 0 && !personalInfo.summary && (
          <div className="text-center py-12 text-gray-500">
            <Icons.fileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <h3 className="text-lg font-medium mb-2">Start Building Your Resume</h3>
            <p className="text-sm">
              Add your personal information, work experience, and education to see your resume preview.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
