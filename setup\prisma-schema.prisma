// CareerCraft Complete Database Schema
// Includes all Epic 1-8 features for local testing

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// ============================================================================
// CORE USER MANAGEMENT
// ============================================================================

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map("accounts")
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

model User {
  id                String   @id @default(cuid())
  name              String?
  email             String   @unique
  emailVerified     DateTime?
  image             String?
  stripeCustomerId  String?  @unique
  onboardingComplete Boolean @default(false)
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  // Relations
  accounts          Account[]
  sessions          Session[]
  subscriptions     UserSubscription[]
  payments          Payment[]
  invoices          Invoice[]
  usageRecords      UsageRecord[]
  resumes           Resume[]
  coverLetters      CoverLetter[]
  portfolios        Portfolio[]
  careerProfiles    CareerProfile[]
  jobApplications   JobApplication[]
  interviewSessions InterviewSession[]
  collaborations    Collaboration[]
  templates         Template[]
  reviews           Review[]

  @@map("users")
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
  @@map("verification_tokens")
}

// ============================================================================
// EPIC 8.0: STRIPE PAYMENT INTEGRATION & SAAS MONETIZATION
// ============================================================================

model SubscriptionPlan {
  id              String  @id @default(cuid())
  name            String
  description     String?
  stripePriceId   String  @unique
  priceCents      Int
  currency        String  @default("USD")
  billingInterval String  // monthly, yearly
  features        Json    // Array of feature names
  limits          Json    // Usage limits object
  isActive        Boolean @default(true)
  sortOrder       Int     @default(0)
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relations
  subscriptions   UserSubscription[]

  @@map("subscription_plans")
}

model UserSubscription {
  id                    String   @id @default(cuid())
  userId                String
  planId                String
  stripeSubscriptionId  String   @unique
  stripeCustomerId      String
  status                String   // active, canceled, past_due, trialing, etc.
  currentPeriodStart    DateTime
  currentPeriodEnd      DateTime
  cancelAtPeriodEnd     Boolean  @default(false)
  canceledAt            DateTime?
  trialStart            DateTime?
  trialEnd              DateTime?
  metadata              Json?
  createdAt             DateTime @default(now())
  updatedAt             DateTime @updatedAt

  // Relations
  user                  User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  plan                  SubscriptionPlan @relation(fields: [planId], references: [id])
  payments              Payment[]
  invoices              Invoice[]
  usageRecords          UsageRecord[]

  @@map("user_subscriptions")
}

model Payment {
  id                      String   @id @default(cuid())
  userId                  String
  subscriptionId          String?
  stripePaymentIntentId   String   @unique
  amountCents             Int
  currency                String   @default("USD")
  status                  String   // succeeded, failed, pending, canceled
  paymentMethodType       String?
  description             String?
  metadata                Json?
  failureReason           String?
  refundedAt              DateTime?
  refundAmount            Int?
  createdAt               DateTime @default(now())

  // Relations
  user                    User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  subscription            UserSubscription? @relation(fields: [subscriptionId], references: [id])

  @@map("payments")
}

model Invoice {
  id                String   @id @default(cuid())
  userId            String
  subscriptionId    String?
  stripeInvoiceId   String   @unique
  amountCents       Int
  currency          String   @default("USD")
  status            String   // paid, open, void, uncollectible
  invoicePdfUrl     String?
  hostedInvoiceUrl  String?
  dueDate           DateTime?
  paidAt            DateTime?
  metadata          Json?
  createdAt         DateTime @default(now())

  // Relations
  user              User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  subscription      UserSubscription? @relation(fields: [subscriptionId], references: [id])

  @@map("invoices")
}

model UsageRecord {
  id                    String   @id @default(cuid())
  userId                String
  subscriptionId        String?
  featureType           String   // autofill, ai_customization, resume_generation, etc.
  usageCount            Int      @default(1)
  metadata              Json?    // Additional context about the usage
  recordedAt            DateTime @default(now())
  billingPeriodStart    DateTime
  billingPeriodEnd      DateTime

  // Relations
  user                  User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  subscription          UserSubscription? @relation(fields: [subscriptionId], references: [id])

  @@index([userId, featureType, billingPeriodStart])
  @@map("usage_records")
}

// ============================================================================
// CORE RESUME & CAREER FEATURES
// ============================================================================

model Resume {
  id          String   @id @default(cuid())
  userId      String
  title       String
  content     Json     // Complete resume data structure
  templateId  String
  isPublic    Boolean  @default(false)
  isActive    Boolean  @default(true)
  version     Int      @default(1)
  metadata    Json?    // ATS scores, analytics, etc.
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  template    Template @relation(fields: [templateId], references: [id])
  applications JobApplication[]
  collaborations Collaboration[]

  @@map("resumes")
}

model CoverLetter {
  id          String   @id @default(cuid())
  userId      String
  title       String
  content     String   @db.Text
  jobTitle    String?
  company     String?
  isTemplate  Boolean  @default(false)
  metadata    Json?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  applications JobApplication[]

  @@map("cover_letters")
}

model Portfolio {
  id          String   @id @default(cuid())
  userId      String
  title       String
  description String?
  content     Json     // Portfolio structure and projects
  isPublic    Boolean  @default(false)
  customUrl   String?  @unique
  metadata    Json?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("portfolios")
}

// ============================================================================
// EPIC 3.0: ADVANCED TEMPLATE SYSTEM
// ============================================================================

model Template {
  id          String   @id @default(cuid())
  name        String
  description String?
  category    String   // resume, cover_letter, portfolio
  type        String   // free, premium, custom
  authorId    String?
  content     Json     // Template structure and styling
  preview     String?  // Preview image URL
  tags        String[] // Searchable tags
  price       Int?     // Price in cents (for marketplace)
  downloads   Int      @default(0)
  rating      Float?
  isActive    Boolean  @default(true)
  isApproved  Boolean  @default(false)
  metadata    Json?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  author      User?    @relation(fields: [authorId], references: [id])
  resumes     Resume[]
  reviews     Review[]

  @@map("templates")
}

model Review {
  id         String   @id @default(cuid())
  userId     String
  templateId String
  rating     Int      // 1-5 stars
  comment    String?
  isPublic   Boolean  @default(true)
  createdAt  DateTime @default(now())

  // Relations
  user       User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  template   Template @relation(fields: [templateId], references: [id], onDelete: Cascade)

  @@unique([userId, templateId])
  @@map("reviews")
}

// ============================================================================
// EPIC 1.0: CAREER INTELLIGENCE ENGINE
// ============================================================================

model CareerProfile {
  id                String   @id @default(cuid())
  userId            String
  currentRole       String?
  targetRole        String?
  experience        Json     // Skills, experience, education
  preferences       Json     // Location, salary, remote, etc.
  profileVector     Float[]  // AI-generated profile embedding
  lastAnalyzed      DateTime?
  analysisResults   Json?    // Career insights and recommendations
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  // Relations
  user              User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("career_profiles")
}

model JobApplication {
  id              String   @id @default(cuid())
  userId          String
  resumeId        String?
  coverLetterId   String?
  jobTitle        String
  company         String
  jobUrl          String?
  status          String   // applied, interviewing, rejected, offered, accepted
  appliedAt       DateTime
  source          String?  // linkedin, indeed, company_website, etc.
  notes           String?
  metadata        Json?    // Salary, location, job description, etc.
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relations
  user            User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  resume          Resume?  @relation(fields: [resumeId], references: [id])
  coverLetter     CoverLetter? @relation(fields: [coverLetterId], references: [id])
  interviews      InterviewSession[]

  @@map("job_applications")
}

model InterviewSession {
  id              String   @id @default(cuid())
  userId          String
  applicationId   String?
  type            String   // mock, real, phone, video, in_person
  questions       Json     // Array of questions and answers
  feedback        Json?    // AI-generated feedback
  score           Float?   // Overall performance score
  duration        Int?     // Duration in minutes
  scheduledAt     DateTime?
  completedAt     DateTime?
  metadata        Json?
  createdAt       DateTime @default(now())

  // Relations
  user            User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  application     JobApplication? @relation(fields: [applicationId], references: [id])

  @@map("interview_sessions")
}

// ============================================================================
// EPIC 2.0: COLLABORATIVE REVIEW & FEEDBACK PORTAL
// ============================================================================

model Collaboration {
  id          String   @id @default(cuid())
  resumeId    String
  ownerId     String   // User who owns the resume
  type        String   // review, edit, comment
  permissions Json     // What collaborators can do
  isActive    Boolean  @default(true)
  expiresAt   DateTime?
  metadata    Json?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  resume      Resume   @relation(fields: [resumeId], references: [id], onDelete: Cascade)
  owner       User     @relation(fields: [ownerId], references: [id], onDelete: Cascade)
  participants CollaborationParticipant[]
  comments    Comment[]

  @@map("collaborations")
}

model CollaborationParticipant {
  id              String   @id @default(cuid())
  collaborationId String
  userId          String?  // Null for anonymous participants
  email           String?  // For inviting non-users
  role            String   // viewer, commenter, editor
  status          String   // pending, accepted, declined
  invitedAt       DateTime @default(now())
  joinedAt        DateTime?

  // Relations
  collaboration   Collaboration @relation(fields: [collaborationId], references: [id], onDelete: Cascade)
  user            User?    @relation(fields: [userId], references: [id])

  @@unique([collaborationId, userId])
  @@unique([collaborationId, email])
  @@map("collaboration_participants")
}

model Comment {
  id              String   @id @default(cuid())
  collaborationId String
  userId          String?
  content         String   @db.Text
  position        Json?    // Where in the document the comment is
  isResolved      Boolean  @default(false)
  parentId        String?  // For threaded comments
  metadata        Json?
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relations
  collaboration   Collaboration @relation(fields: [collaborationId], references: [id], onDelete: Cascade)
  user            User?    @relation(fields: [userId], references: [id])
  parent          Comment? @relation("CommentThread", fields: [parentId], references: [id])
  replies         Comment[] @relation("CommentThread")

  @@map("comments")
}

// ============================================================================
// SYSTEM TABLES
// ============================================================================

model WebhookEvent {
  id          String   @id @default(cuid())
  provider    String   // stripe, github, etc.
  eventType   String
  eventId     String   @unique
  data        Json
  processed   Boolean  @default(false)
  error       String?
  createdAt   DateTime @default(now())
  processedAt DateTime?

  @@index([provider, eventType])
  @@map("webhook_events")
}

model AuditLog {
  id        String   @id @default(cuid())
  userId    String?
  action    String   // create, update, delete, login, etc.
  resource  String   // resume, payment, subscription, etc.
  resourceId String?
  metadata  Json?
  ipAddress String?
  userAgent String?
  createdAt DateTime @default(now())

  @@index([userId, action])
  @@index([resource, resourceId])
  @@map("audit_logs")
}
