import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { generateContentSchema } from '@careercraft/shared/schemas/ai';
import { ContentGenerator } from '@/lib/ai/content-generator';
import { prisma } from '@careercraft/database';

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    
    // Validate the request body
    const validationResult = generateContentSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          error: 'Validation failed',
          details: validationResult.error.errors,
        },
        { status: 400 }
      );
    }

    const requestData = {
      ...validationResult.data,
      userId: session.user.id,
    };

    // Check user's AI usage limits
    const today = new Date().toISOString().split('T')[0];
    const usageToday = await prisma.aIUsageAnalytics.findFirst({
      where: {
        userId: session.user.id,
        date: today,
      },
    });

    // Simple rate limiting - 50 requests per day for free users
    if (usageToday && usageToday.requestCount >= 50) {
      return NextResponse.json(
        { error: 'Daily AI usage limit exceeded. Please upgrade your plan.' },
        { status: 429 }
      );
    }

    // Initialize content generator
    const generator = new ContentGenerator({
      apiKey: process.env.OPENAI_API_KEY || 'mock-key',
      model: 'gpt-4',
    });

    // Generate content
    const result = await generator.generateContent(requestData);

    // Track usage
    await prisma.aIUsageAnalytics.upsert({
      where: {
        userId_date: {
          userId: session.user.id,
          date: today,
        },
      },
      update: {
        requestCount: {
          increment: 1,
        },
        tokensUsed: {
          increment: result.metadata.tokensUsed,
        },
        contentTypes: {
          // This would need to be handled differently in a real implementation
          // as Prisma doesn't support JSON field updates like this
        },
      },
      create: {
        userId: session.user.id,
        period: 'day',
        date: today,
        requestCount: 1,
        tokensUsed: result.metadata.tokensUsed,
        contentTypes: {
          [requestData.type]: 1,
        },
        averageConfidence: result.metadata.confidence,
        successRate: 1.0,
        cost: 0.01, // Mock cost
      },
    });

    // Store the generated content for future reference
    await prisma.aIContentResponse.create({
      data: {
        id: result.id,
        userId: session.user.id,
        requestId: result.requestId,
        type: requestData.type,
        context: requestData.context,
        options: requestData.options,
        suggestions: result.suggestions,
        metadata: result.metadata,
      },
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error('Content generation error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
