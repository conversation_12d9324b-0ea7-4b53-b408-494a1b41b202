# Test Environment Configuration
NODE_ENV=test

# Test Database - Use a separate test database
DATABASE_URL="postgresql://username:password@localhost:5432/careercraft_v2_test"

# NextAuth.js (for integration tests)
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="test-secret-key-for-testing-only"

# Test OAuth Providers (mock values)
GOOGLE_CLIENT_ID="test-google-client-id"
GOOGLE_CLIENT_SECRET="test-google-client-secret"
GITHUB_CLIENT_ID="test-github-client-id"
GITHUB_CLIENT_SECRET="test-github-client-secret"

# Test OpenAI API (use a test key or mock)
OPENAI_API_KEY="test-openai-api-key"

# Test Email Configuration
SMTP_HOST="localhost"
SMTP_PORT="1025"
SMTP_USER="test"
SMTP_PASSWORD="test"
FROM_EMAIL="<EMAIL>"

# Test File Storage
AWS_ACCESS_KEY_ID="test-access-key"
AWS_SECRET_ACCESS_KEY="test-secret-key"
AWS_REGION="us-east-1"
AWS_S3_BUCKET="careercraft-test-uploads"

# Test Redis (optional)
REDIS_URL="redis://localhost:6379/1"

# Test App Configuration
NEXT_PUBLIC_APP_URL="http://localhost:3000"
NEXT_PUBLIC_APP_NAME="CareerCraft Test"

# Feature Flags for Testing
NEXT_PUBLIC_ENABLE_ANALYTICS="false"
NEXT_PUBLIC_ENABLE_CHAT_SUPPORT="false"

# Rate Limiting (relaxed for tests)
RATE_LIMIT_REQUESTS_PER_MINUTE="1000"

# Test Service URLs
AI_SERVICE_URL="http://localhost:3001"
PDF_SERVICE_URL="http://localhost:3002"
