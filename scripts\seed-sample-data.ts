#!/usr/bin/env tsx

/**
 * Sample Data Seeding Script
 * 
 * This script creates realistic sample data for local testing
 */

import { PrismaClient } from '@careercraft/database';
import { hash } from 'bcryptjs';
import { generateDefaultTemplates } from '@careercraft/shared/data/default-templates';

const prisma = new PrismaClient();

async function seedSampleData() {
  console.log('🌱 Seeding CareerCraft with sample data...');

  try {
    // Create demo users
    console.log('👥 Creating demo users...');
    
    const demoUser = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        email: '<EMAIL>',
        name: 'Demo User',
        emailVerified: new Date(),
      },
    });

    const johnDoe = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        email: '<EMAIL>',
        name: '<PERSON>',
        emailVerified: new Date(),
      },
    });

    const janeSmith = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        email: '<EMAIL>',
        name: '<PERSON>',
        emailVerified: new Date(),
      },
    });

    // Create user profiles
    console.log('📝 Creating user profiles...');
    
    await prisma.userProfile.upsert({
      where: { userId: johnDoe.id },
      update: {},
      create: {
        userId: johnDoe.id,
        firstName: 'John',
        lastName: 'Doe',
        phone: '******-0123',
        location: 'San Francisco, CA',
        website: 'https://johndoe.dev',
        linkedinUrl: 'https://linkedin.com/in/johndoe',
        githubUrl: 'https://github.com/johndoe',
        bio: 'Passionate software engineer with 5+ years of experience building scalable web applications.',
      },
    });

    await prisma.userProfile.upsert({
      where: { userId: janeSmith.id },
      update: {},
      create: {
        userId: janeSmith.id,
        firstName: 'Jane',
        lastName: 'Smith',
        phone: '******-0456',
        location: 'New York, NY',
        website: 'https://janesmith.design',
        linkedinUrl: 'https://linkedin.com/in/janesmith',
        bio: 'Creative marketing professional specializing in digital campaigns and brand strategy.',
      },
    });

    // Create default templates
    console.log('🎨 Creating default templates...');
    
    const templates = generateDefaultTemplates();
    for (const template of templates) {
      await prisma.template.upsert({
        where: { id: template.id },
        update: {},
        create: {
          id: template.id,
          name: template.name,
          description: template.description,
          category: template.category,
          isPremium: template.isPremium,
          isActive: true,
          config: {
            style: template.style,
            layout: template.layout,
            supportedSections: template.supportedSections,
            requiredSections: template.requiredSections,
            customizable: template.customizable,
          },
          preview: template.previewImage,
        },
      });
    }

    // Create sample resumes
    console.log('📄 Creating sample resumes...');
    
    const johnResume = await prisma.resume.upsert({
      where: { id: 'john-software-engineer-resume' },
      update: {},
      create: {
        id: 'john-software-engineer-resume',
        userId: johnDoe.id,
        title: 'Software Engineer Resume',
        description: 'My professional software engineering resume',
        templateId: templates[0].id, // Modern Professional template
        status: 'PUBLISHED',
        isPublic: true,
        publicUrl: 'john-doe-software-engineer',
      },
    });

    const janeResume = await prisma.resume.upsert({
      where: { id: 'jane-marketing-manager-resume' },
      update: {},
      create: {
        id: 'jane-marketing-manager-resume',
        userId: janeSmith.id,
        title: 'Marketing Manager Resume',
        description: 'My marketing and brand strategy resume',
        templateId: templates[1].id, // Classic Executive template
        status: 'DRAFT',
        isPublic: false,
      },
    });

    // Create work experiences
    console.log('💼 Creating work experiences...');
    
    await prisma.experience.createMany({
      data: [
        {
          resumeId: johnResume.id,
          company: 'TechCorp Inc.',
          position: 'Senior Software Engineer',
          location: 'San Francisco, CA',
          startDate: new Date('2021-03-01'),
          endDate: new Date('2023-12-31'),
          isCurrent: false,
          description: 'Led development of scalable web applications using React, Node.js, and AWS. Managed a team of 3 junior developers and implemented CI/CD pipelines.',
          achievements: [
            'Improved application performance by 40% through code optimization',
            'Led migration to microservices architecture, reducing deployment time by 60%',
            'Mentored 3 junior developers and established code review processes',
            'Implemented automated testing that reduced bugs by 35%',
          ],
          displayOrder: 0,
        },
        {
          resumeId: johnResume.id,
          company: 'StartupXYZ',
          position: 'Full Stack Developer',
          location: 'San Francisco, CA',
          startDate: new Date('2019-06-01'),
          endDate: new Date('2021-02-28'),
          isCurrent: false,
          description: 'Developed and maintained full-stack web applications using modern JavaScript frameworks and cloud technologies.',
          achievements: [
            'Built responsive web applications serving 10,000+ daily users',
            'Integrated third-party APIs and payment processing systems',
            'Collaborated with design team to implement pixel-perfect UIs',
          ],
          displayOrder: 1,
        },
        {
          resumeId: janeResume.id,
          company: 'Marketing Pro Agency',
          position: 'Marketing Manager',
          location: 'New York, NY',
          startDate: new Date('2020-01-01'),
          endDate: new Date('2023-12-31'),
          isCurrent: false,
          description: 'Managed digital marketing campaigns for B2B and B2C clients, focusing on brand strategy and customer acquisition.',
          achievements: [
            'Increased client ROI by 150% through targeted digital campaigns',
            'Managed $500K annual marketing budget across 15+ clients',
            'Led rebranding project that increased brand recognition by 80%',
            'Developed content strategy that grew social media following by 300%',
          ],
          displayOrder: 0,
        },
      ],
      skipDuplicates: true,
    });

    // Create education records
    console.log('🎓 Creating education records...');
    
    await prisma.education.createMany({
      data: [
        {
          resumeId: johnResume.id,
          institution: 'University of California, Berkeley',
          degree: 'Bachelor of Science',
          field: 'Computer Science',
          location: 'Berkeley, CA',
          startDate: new Date('2015-09-01'),
          endDate: new Date('2019-05-31'),
          gpa: '3.8',
          description: 'Relevant coursework: Data Structures, Algorithms, Software Engineering, Database Systems',
          displayOrder: 0,
        },
        {
          resumeId: janeResume.id,
          institution: 'New York University',
          degree: 'Master of Business Administration',
          field: 'Marketing',
          location: 'New York, NY',
          startDate: new Date('2017-09-01'),
          endDate: new Date('2019-05-31'),
          gpa: '3.9',
          description: 'Concentration in Digital Marketing and Brand Management',
          displayOrder: 0,
        },
      ],
      skipDuplicates: true,
    });

    // Create skills
    console.log('⚡ Creating skills...');
    
    await prisma.skill.createMany({
      data: [
        // John's technical skills
        { resumeId: johnResume.id, name: 'JavaScript', category: 'Programming Languages', level: 'EXPERT', displayOrder: 0 },
        { resumeId: johnResume.id, name: 'TypeScript', category: 'Programming Languages', level: 'EXPERT', displayOrder: 1 },
        { resumeId: johnResume.id, name: 'Python', category: 'Programming Languages', level: 'ADVANCED', displayOrder: 2 },
        { resumeId: johnResume.id, name: 'React', category: 'Frontend Frameworks', level: 'EXPERT', displayOrder: 3 },
        { resumeId: johnResume.id, name: 'Node.js', category: 'Backend Technologies', level: 'EXPERT', displayOrder: 4 },
        { resumeId: johnResume.id, name: 'AWS', category: 'Cloud Platforms', level: 'ADVANCED', displayOrder: 5 },
        { resumeId: johnResume.id, name: 'Docker', category: 'DevOps', level: 'ADVANCED', displayOrder: 6 },
        { resumeId: johnResume.id, name: 'PostgreSQL', category: 'Databases', level: 'ADVANCED', displayOrder: 7 },
        
        // Jane's marketing skills
        { resumeId: janeResume.id, name: 'Digital Marketing', category: 'Marketing', level: 'EXPERT', displayOrder: 0 },
        { resumeId: janeResume.id, name: 'Brand Strategy', category: 'Marketing', level: 'EXPERT', displayOrder: 1 },
        { resumeId: janeResume.id, name: 'Google Analytics', category: 'Analytics', level: 'ADVANCED', displayOrder: 2 },
        { resumeId: janeResume.id, name: 'Social Media Marketing', category: 'Marketing', level: 'EXPERT', displayOrder: 3 },
        { resumeId: janeResume.id, name: 'Content Strategy', category: 'Marketing', level: 'ADVANCED', displayOrder: 4 },
        { resumeId: janeResume.id, name: 'SEO/SEM', category: 'Marketing', level: 'ADVANCED', displayOrder: 5 },
      ],
      skipDuplicates: true,
    });

    // Create projects
    console.log('🚀 Creating projects...');
    
    await prisma.project.createMany({
      data: [
        {
          resumeId: johnResume.id,
          name: 'E-commerce Platform',
          description: 'Built a full-stack e-commerce platform with React, Node.js, and PostgreSQL. Features include user authentication, payment processing, and admin dashboard.',
          url: 'https://ecommerce-demo.johndoe.dev',
          githubUrl: 'https://github.com/johndoe/ecommerce-platform',
          technologies: ['React', 'Node.js', 'PostgreSQL', 'Stripe API', 'AWS'],
          startDate: new Date('2023-01-01'),
          endDate: new Date('2023-06-30'),
          displayOrder: 0,
        },
        {
          resumeId: johnResume.id,
          name: 'Task Management App',
          description: 'Developed a collaborative task management application with real-time updates using Socket.io and React.',
          url: 'https://taskapp.johndoe.dev',
          githubUrl: 'https://github.com/johndoe/task-manager',
          technologies: ['React', 'Socket.io', 'MongoDB', 'Express.js'],
          startDate: new Date('2022-08-01'),
          endDate: new Date('2022-12-31'),
          displayOrder: 1,
        },
      ],
      skipDuplicates: true,
    });

    // Create certifications
    console.log('🏆 Creating certifications...');
    
    await prisma.certification.createMany({
      data: [
        {
          resumeId: johnResume.id,
          name: 'AWS Certified Solutions Architect',
          issuer: 'Amazon Web Services',
          issueDate: new Date('2023-03-15'),
          expiryDate: new Date('2026-03-15'),
          credentialId: 'AWS-CSA-123456',
          url: 'https://aws.amazon.com/certification/',
          displayOrder: 0,
        },
        {
          resumeId: janeResume.id,
          name: 'Google Analytics Certified',
          issuer: 'Google',
          issueDate: new Date('2023-01-10'),
          expiryDate: new Date('2024-01-10'),
          credentialId: 'GA-CERT-789012',
          url: 'https://skillshop.exceedlms.com/student/catalog',
          displayOrder: 0,
        },
      ],
      skipDuplicates: true,
    });

    // Create sample AI usage analytics
    console.log('🤖 Creating AI usage analytics...');
    
    const today = new Date().toISOString().split('T')[0];
    await prisma.aIUsageAnalytics.upsert({
      where: {
        userId_date: {
          userId: johnDoe.id,
          date: today,
        },
      },
      update: {},
      create: {
        userId: johnDoe.id,
        period: 'day',
        date: today,
        requestCount: 5,
        tokensUsed: 1250,
        contentTypes: {
          'professional_summary': 2,
          'work_experience_description': 2,
          'achievement_bullet': 1,
        },
        averageConfidence: 0.87,
        successRate: 1.0,
        cost: 0.05,
      },
    });

    console.log('✅ Sample data seeding completed successfully!');
    console.log('');
    console.log('🎉 You can now test with the following accounts:');
    console.log('');
    console.log('📧 Demo User:');
    console.log('   Email: <EMAIL>');
    console.log('   (No password needed for demo)');
    console.log('');
    console.log('📧 John Doe (Software Engineer):');
    console.log('   Email: <EMAIL>');
    console.log('   Resume: Complete software engineering resume');
    console.log('');
    console.log('📧 Jane Smith (Marketing Manager):');
    console.log('   Email: <EMAIL>');
    console.log('   Resume: Marketing professional resume');
    console.log('');
    console.log('🎨 Templates: 4 professional templates available');
    console.log('🤖 AI Features: Sample usage data created');
    console.log('');

  } catch (error) {
    console.error('❌ Error seeding sample data:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the seeding
if (require.main === module) {
  seedSampleData().catch((error) => {
    console.error(error);
    process.exit(1);
  });
}

export { seedSampleData };
