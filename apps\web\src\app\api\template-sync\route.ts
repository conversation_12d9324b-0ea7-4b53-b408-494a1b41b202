/**
 * Template Sync API Routes
 * 
 * Handles template synchronization, versioning, and conflict resolution
 */

import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { templateSyncService, ConflictResolutionSchema, TemplateVersionSchema } from '@/lib/template-sync/service'
import { cloudStorageService } from '@/lib/template-sync/cloud-storage'
import { z } from 'zod'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const action = searchParams.get('action')

    if (action === 'status') {
      // Get sync status for user
      const status = await templateSyncService.getSyncStatus(session.user.id)
      
      return NextResponse.json({
        success: true,
        status
      })
    }

    if (action === 'conflicts') {
      // Get unresolved conflicts
      const conflicts = await templateSyncService.detectConflicts(session.user.id)
      
      return NextResponse.json({
        success: true,
        conflicts
      })
    }

    if (action === 'history') {
      // Get version history for a template
      const templateId = searchParams.get('templateId')
      
      if (!templateId) {
        return NextResponse.json(
          { error: 'Template ID required' },
          { status: 400 }
        )
      }

      const history = await templateSyncService.getVersionHistory(templateId)
      
      return NextResponse.json({
        success: true,
        history
      })
    }

    return NextResponse.json(
      { error: 'Invalid action' },
      { status: 400 }
    )
  } catch (error) {
    console.error('Template sync GET error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const action = body.action

    if (action === 'sync') {
      // Sync all user templates
      const result = await templateSyncService.syncUserTemplates(session.user.id)
      
      return NextResponse.json({
        success: true,
        result
      })
    }

    if (action === 'sync-single') {
      // Sync a single template
      const { templateId } = body
      
      if (!templateId) {
        return NextResponse.json(
          { error: 'Template ID required' },
          { status: 400 }
        )
      }

      await templateSyncService.syncSingleTemplate(templateId, session.user.id)
      
      return NextResponse.json({
        success: true,
        message: 'Template synced successfully'
      })
    }

    if (action === 'create-version') {
      // Create a new template version
      const versionData = TemplateVersionSchema.parse(body)
      
      const versionId = await templateSyncService.createTemplateVersion(
        versionData.templateId,
        versionData,
        session.user.id
      )
      
      return NextResponse.json({
        success: true,
        versionId,
        message: 'Version created successfully'
      })
    }

    if (action === 'resolve-conflict') {
      // Resolve a sync conflict
      const resolutionData = ConflictResolutionSchema.parse(body)
      
      await templateSyncService.resolveConflict(resolutionData.conflictId, resolutionData)
      
      return NextResponse.json({
        success: true,
        message: 'Conflict resolved successfully'
      })
    }

    if (action === 'rollback') {
      // Rollback to a specific version
      const { templateId, versionId } = body
      
      if (!templateId || !versionId) {
        return NextResponse.json(
          { error: 'Template ID and version ID required' },
          { status: 400 }
        )
      }

      await templateSyncService.rollbackToVersion(templateId, versionId, session.user.id)
      
      return NextResponse.json({
        success: true,
        message: 'Template rolled back successfully'
      })
    }

    if (action === 'upload-cloud') {
      // Upload template to cloud storage
      const { templateId, templateData } = body
      
      if (!templateId || !templateData) {
        return NextResponse.json(
          { error: 'Template ID and data required' },
          { status: 400 }
        )
      }

      const result = await cloudStorageService.syncTemplate(
        templateId,
        session.user.id,
        templateData
      )
      
      return NextResponse.json({
        success: result.success,
        url: result.url,
        error: result.error
      })
    }

    if (action === 'download-cloud') {
      // Download template from cloud storage
      const { url } = body
      
      if (!url) {
        return NextResponse.json(
          { error: 'Cloud URL required' },
          { status: 400 }
        )
      }

      const templateData = await cloudStorageService.downloadTemplate(url)
      
      return NextResponse.json({
        success: true,
        templateData
      })
    }

    if (action === 'batch-sync') {
      // Batch sync multiple templates
      const { templates } = body
      
      if (!Array.isArray(templates)) {
        return NextResponse.json(
          { error: 'Templates array required' },
          { status: 400 }
        )
      }

      const results = await cloudStorageService.batchSyncTemplates(
        templates.map(t => ({
          ...t,
          userId: session.user.id
        }))
      )
      
      return NextResponse.json({
        success: true,
        results
      })
    }

    return NextResponse.json(
      { error: 'Invalid action' },
      { status: 400 }
    )
  } catch (error) {
    console.error('Template sync POST error:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const action = body.action

    if (action === 'update-sync-settings') {
      // Update user's sync settings
      const { settings } = body
      
      // This would update user's sync preferences in the database
      // For now, we'll just return success
      return NextResponse.json({
        success: true,
        message: 'Sync settings updated successfully'
      })
    }

    if (action === 'force-sync') {
      // Force sync all templates (ignore conflicts)
      const result = await templateSyncService.syncUserTemplates(session.user.id)
      
      return NextResponse.json({
        success: true,
        result,
        message: 'Force sync completed'
      })
    }

    if (action === 'clear-conflicts') {
      // Clear all resolved conflicts
      const { templateId } = body
      
      // This would mark all conflicts as resolved for the template
      // Implementation would go here
      
      return NextResponse.json({
        success: true,
        message: 'Conflicts cleared successfully'
      })
    }

    return NextResponse.json(
      { error: 'Invalid action' },
      { status: 400 }
    )
  } catch (error) {
    console.error('Template sync PUT error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const action = searchParams.get('action')
    const templateId = searchParams.get('templateId')
    const versionId = searchParams.get('versionId')

    if (action === 'delete-version') {
      // Delete a specific template version
      if (!templateId || !versionId) {
        return NextResponse.json(
          { error: 'Template ID and version ID required' },
          { status: 400 }
        )
      }

      // Implementation would delete the version from database
      // For now, we'll just return success
      return NextResponse.json({
        success: true,
        message: 'Version deleted successfully'
      })
    }

    if (action === 'delete-cloud') {
      // Delete template from cloud storage
      const url = searchParams.get('url')
      
      if (!url) {
        return NextResponse.json(
          { error: 'Cloud URL required' },
          { status: 400 }
        )
      }

      await cloudStorageService.deleteTemplate(url)
      
      return NextResponse.json({
        success: true,
        message: 'Template deleted from cloud storage'
      })
    }

    if (action === 'clear-sync-data') {
      // Clear all sync data for user
      if (!templateId) {
        return NextResponse.json(
          { error: 'Template ID required' },
          { status: 400 }
        )
      }

      // Implementation would clear sync data from database
      // For now, we'll just return success
      return NextResponse.json({
        success: true,
        message: 'Sync data cleared successfully'
      })
    }

    return NextResponse.json(
      { error: 'Invalid action' },
      { status: 400 }
    )
  } catch (error) {
    console.error('Template sync DELETE error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
