#!/bin/bash
# CareerCraft Complete Local Testing Setup

echo "🚀 CareerCraft Complete Local Testing Setup"
echo "==========================================="
echo ""

# Check if we're in the right directory
if [ ! -f "setup-local-testing.sh" ]; then
    echo "❌ Please run this script from the CareerCraft root directory"
    exit 1
fi

# Step 1: Install Prerequisites
echo "📦 Step 1: Installing Prerequisites"
echo "-----------------------------------"

# Detect OS and run appropriate installation script
if [[ "$OSTYPE" == "msys" || "$OSTYPE" == "win32" ]]; then
    echo "🪟 Detected Windows - Please run setup/install-prerequisites.ps1 as Administrator first"
    echo "   Then come back and run this script"
    exit 1
else
    chmod +x setup/install-prerequisites.sh
    ./setup/install-prerequisites.sh
fi

echo ""
echo "✅ Prerequisites installation complete!"
echo ""

# Step 2: Create Project
echo "📁 Step 2: Creating Project Structure"
echo "-------------------------------------"

chmod +x setup/create-project.sh
./setup/create-project.sh

# Navigate to project directory
cd careercraft-local

echo ""
echo "✅ Project structure created!"
echo ""

# Step 3: Copy Setup Files
echo "📋 Step 3: Setting Up Configuration Files"
echo "-----------------------------------------"

# Copy all setup files to the project
cp -r ../setup/src/* src/ 2>/dev/null || true
cp ../setup/jest.config.js .
cp ../setup/jest.setup.js .
cp ../setup/playwright.config.ts .
cp -r ../setup/tests .

echo "✅ Configuration files copied!"
echo ""

# Step 4: Database Setup
echo "🗄️  Step 4: Setting Up Database"
echo "-------------------------------"

chmod +x ../setup/setup-database.sh
../setup/setup-database.sh

echo ""
echo "✅ Database setup complete!"
echo ""

# Step 5: Install Dependencies
echo "📦 Step 5: Installing Project Dependencies"
echo "-----------------------------------------"

npm install

echo ""
echo "✅ Dependencies installed!"
echo ""

# Step 6: Run Initial Tests
echo "🧪 Step 6: Running Initial Tests"
echo "--------------------------------"

echo "🔧 Type checking..."
npm run type-check

echo "🧪 Running unit tests..."
npm run test -- --passWithNoTests

echo "🎭 Installing Playwright browsers..."
npx playwright install

echo ""
echo "✅ Initial tests complete!"
echo ""

# Step 7: Start Development Server
echo "🚀 Step 7: Starting Development Server"
echo "--------------------------------------"

echo "📝 Creating startup script..."
cat > start-dev.sh << 'EOF'
#!/bin/bash
echo "🚀 Starting CareerCraft Local Development"
echo "========================================"

# Check if all services are running
echo "🔍 Checking services..."

# Check PostgreSQL
if ! pg_isready -h localhost -p 5432 >/dev/null 2>&1; then
    echo "❌ PostgreSQL is not running. Starting..."
    if [[ "$OSTYPE" == "darwin"* ]]; then
        brew services start postgresql@14
    elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
        sudo systemctl start postgresql
    fi
fi

# Check Redis (optional)
if command -v redis-cli &> /dev/null; then
    if ! redis-cli ping >/dev/null 2>&1; then
        echo "⚠️  Redis is not running. Starting..."
        if [[ "$OSTYPE" == "darwin"* ]]; then
            brew services start redis
        elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
            sudo systemctl start redis-server
        fi
    fi
fi

echo "✅ Services are running!"
echo ""

# Start the development server
echo "🚀 Starting Next.js development server..."
echo "📱 App will be available at: http://localhost:3000"
echo "🗄️  Database admin at: http://localhost:5555 (run 'npm run db:studio' in another terminal)"
echo ""
echo "🔧 Useful commands:"
echo "   npm run test          - Run unit tests"
echo "   npm run test:e2e      - Run E2E tests"
echo "   npm run db:studio     - Open database admin"
echo "   npm run stripe:listen - Listen for Stripe webhooks"
echo ""

npm run dev
EOF

chmod +x start-dev.sh

echo ""
echo "🎉 SETUP COMPLETE!"
echo "=================="
echo ""
echo "📊 Setup Summary:"
echo "   ✅ Prerequisites installed"
echo "   ✅ Project structure created"
echo "   ✅ Database configured and seeded"
echo "   ✅ Dependencies installed"
echo "   ✅ Tests configured"
echo "   ✅ Development environment ready"
echo ""
echo "🔧 Next Steps:"
echo "   1. Configure your API keys in .env.local:"
echo "      - GOOGLE_CLIENT_ID and GOOGLE_CLIENT_SECRET"
echo "      - GITHUB_ID and GITHUB_SECRET"
echo "      - OPENAI_API_KEY"
echo "      - STRIPE_PUBLISHABLE_KEY and STRIPE_SECRET_KEY"
echo ""
echo "   2. Start the development server:"
echo "      ./start-dev.sh"
echo ""
echo "   3. Visit http://localhost:3000 to see your app!"
echo ""
echo "📚 Testing Commands:"
echo "   npm run test           - Unit tests"
echo "   npm run test:e2e       - End-to-end tests"
echo "   npm run test:coverage  - Coverage report"
echo "   npm run db:studio      - Database admin UI"
echo ""
echo "🎯 Epic Testing Checklist:"
echo "   □ Epic 8.0: Payment system (Stripe integration)"
echo "   □ Epic 1.0: Career intelligence (OpenAI integration)"
echo "   □ Epic 6.0: Browser extension (autofill testing)"
echo "   □ Epic 2.0: Collaboration features"
echo "   □ Epic 3.0: Template marketplace"
echo "   □ Epic 4.0: External integrations"
echo "   □ Epic 5.0: Mobile responsiveness"
echo "   □ Epic 7.0: Advanced AI features"
echo ""
echo "🚀 Happy coding! Your CareerCraft local testing environment is ready!"
