/**
 * Version Control API - Backups
 * 
 * Handles resume backup and restore operations
 */

import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { versionControlService, CreateBackupSchema } from '@/lib/version-control/service'
import { z } from 'zod'

// Request schemas
const RestoreBackupSchema = z.object({
  backupId: z.string()
})

const GetBackupsSchema = z.object({
  resumeId: z.string(),
  limit: z.coerce.number().optional().default(20)
})

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const action = body.action

    if (action === 'create') {
      const { resumeId, backupName, backupType, expiresAt, metadata } = CreateBackupSchema.parse(body)
      
      const backup = await versionControlService.createBackup({
        resumeId,
        userId: session.user.id,
        backupName,
        backupType,
        expiresAt: expiresAt ? new Date(expiresAt) : undefined,
        metadata
      })

      return NextResponse.json({
        success: true,
        backup,
        message: 'Backup created successfully'
      })
    }

    if (action === 'restore') {
      const { backupId } = RestoreBackupSchema.parse(body)
      
      const success = await versionControlService.restoreFromBackup(
        backupId,
        session.user.id
      )

      if (!success) {
        return NextResponse.json(
          { error: 'Restore failed' },
          { status: 500 }
        )
      }

      return NextResponse.json({
        success: true,
        message: 'Backup restored successfully'
      })
    }

    return NextResponse.json(
      { error: 'Invalid action' },
      { status: 400 }
    )
  } catch (error) {
    console.error('Backup operation error:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const action = searchParams.get('action')

    if (action === 'list') {
      const resumeId = searchParams.get('resumeId')
      const limit = parseInt(searchParams.get('limit') || '20')

      if (!resumeId) {
        return NextResponse.json(
          { error: 'Resume ID required' },
          { status: 400 }
        )
      }

      const backups = await versionControlService.getBackups(
        resumeId,
        session.user.id,
        limit
      )

      return NextResponse.json({ backups })
    }

    return NextResponse.json(
      { error: 'Invalid action' },
      { status: 400 }
    )
  } catch (error) {
    console.error('Backup get error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const action = searchParams.get('action')

    if (action === 'cleanup-expired') {
      const deletedCount = await versionControlService.cleanupExpiredBackups()

      return NextResponse.json({
        success: true,
        deletedCount,
        message: `Cleaned up ${deletedCount} expired backups`
      })
    }

    return NextResponse.json(
      { error: 'Invalid action' },
      { status: 400 }
    )
  } catch (error) {
    console.error('Backup cleanup error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
