/**
 * Template Sync API Unit Tests
 * 
 * Tests for template sync and marketplace API endpoints
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { NextRequest } from 'next/server'
import { GET as SyncGET, POST as SyncPOST, PUT as SyncPUT, DELETE as SyncDELETE } from '@/app/api/template-sync/route'
import { GET as MarketplaceGET, POST as MarketplacePOST, PUT as MarketplacePUT, DELETE as MarketplaceDELETE } from '@/app/api/template-marketplace/route'

// Mock next-auth
const mockSession = {
  user: {
    id: 'user-1',
    name: '<PERSON>',
    email: '<EMAIL>'
  }
}

vi.mock('next-auth', () => ({
  getServerSession: vi.fn()
}))

// Mock template sync service
const mockTemplateSyncService = {
  getSyncStatus: vi.fn(),
  detectConflicts: vi.fn(),
  getVersionHistory: vi.fn(),
  syncUserTemplates: vi.fn(),
  syncSingleTemplate: vi.fn(),
  createTemplateVersion: vi.fn(),
  resolveConflict: vi.fn(),
  rollbackToVersion: vi.fn()
}

vi.mock('@/lib/template-sync/service', () => ({
  templateSyncService: mockTemplateSyncService
}))

// Mock marketplace service
const mockMarketplaceService = {
  searchTemplates: vi.fn(),
  getFeaturedTemplates: vi.fn(),
  publishTemplate: vi.fn(),
  purchaseTemplate: vi.fn(),
  addReview: vi.fn(),
  getTemplateReviews: vi.fn(),
  createCollection: vi.fn(),
  getUserCollections: vi.fn(),
  trackTemplateUsage: vi.fn(),
  getTemplateAnalytics: vi.fn()
}

vi.mock('@/lib/template-sync/marketplace-service', () => ({
  templateMarketplaceService: mockMarketplaceService
}))

// Mock cloud storage service
const mockCloudStorageService = {
  syncTemplate: vi.fn(),
  downloadTemplate: vi.fn(),
  deleteTemplate: vi.fn(),
  batchSyncTemplates: vi.fn()
}

vi.mock('@/lib/template-sync/cloud-storage', () => ({
  cloudStorageService: mockCloudStorageService
}))

describe('Template Sync API', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    const { getServerSession } = require('next-auth')
    getServerSession.mockResolvedValue(mockSession)
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('Template Sync Routes', () => {
    describe('GET /api/template-sync', () => {
      it('should get sync status', async () => {
        const mockStatus = {
          totalTemplates: 5,
          syncedTemplates: 4,
          pendingSync: 1,
          syncErrors: 0,
          conflicts: 0,
          lastSyncTime: new Date()
        }

        mockTemplateSyncService.getSyncStatus.mockResolvedValue(mockStatus)

        const request = new NextRequest('http://localhost/api/template-sync?action=status')

        const response = await SyncGET(request)
        const data = await response.json()

        expect(response.status).toBe(200)
        expect(data.success).toBe(true)
        expect(data.status).toEqual(mockStatus)
        expect(mockTemplateSyncService.getSyncStatus).toHaveBeenCalledWith('user-1')
      })

      it('should get conflicts', async () => {
        const mockConflicts = [
          {
            id: 'conflict-1',
            templateId: 'template-1',
            conflictType: 'concurrent_edit',
            localVersion: { name: 'Local' },
            remoteVersion: { name: 'Remote' },
            timestamp: new Date()
          }
        ]

        mockTemplateSyncService.detectConflicts.mockResolvedValue(mockConflicts)

        const request = new NextRequest('http://localhost/api/template-sync?action=conflicts')

        const response = await SyncGET(request)
        const data = await response.json()

        expect(response.status).toBe(200)
        expect(data.success).toBe(true)
        expect(data.conflicts).toEqual(mockConflicts)
      })

      it('should get version history', async () => {
        const mockHistory = [
          {
            id: 'version-1',
            versionNumber: 1,
            versionName: 'Version 1',
            createdAt: new Date()
          }
        ]

        mockTemplateSyncService.getVersionHistory.mockResolvedValue(mockHistory)

        const request = new NextRequest('http://localhost/api/template-sync?action=history&templateId=template-1')

        const response = await SyncGET(request)
        const data = await response.json()

        expect(response.status).toBe(200)
        expect(data.success).toBe(true)
        expect(data.history).toEqual(mockHistory)
        expect(mockTemplateSyncService.getVersionHistory).toHaveBeenCalledWith('template-1')
      })

      it('should return 400 for missing templateId in history request', async () => {
        const request = new NextRequest('http://localhost/api/template-sync?action=history')

        const response = await SyncGET(request)

        expect(response.status).toBe(400)
      })

      it('should return 401 for unauthenticated requests', async () => {
        const { getServerSession } = require('next-auth')
        getServerSession.mockResolvedValue(null)

        const request = new NextRequest('http://localhost/api/template-sync?action=status')

        const response = await SyncGET(request)

        expect(response.status).toBe(401)
      })
    })

    describe('POST /api/template-sync', () => {
      it('should sync all templates', async () => {
        const mockResult = {
          success: true,
          syncedTemplates: 3,
          conflicts: [],
          errors: []
        }

        mockTemplateSyncService.syncUserTemplates.mockResolvedValue(mockResult)

        const request = new NextRequest('http://localhost/api/template-sync', {
          method: 'POST',
          body: JSON.stringify({ action: 'sync' })
        })

        const response = await SyncPOST(request)
        const data = await response.json()

        expect(response.status).toBe(200)
        expect(data.success).toBe(true)
        expect(data.result).toEqual(mockResult)
        expect(mockTemplateSyncService.syncUserTemplates).toHaveBeenCalledWith('user-1')
      })

      it('should sync single template', async () => {
        mockTemplateSyncService.syncSingleTemplate.mockResolvedValue(undefined)

        const request = new NextRequest('http://localhost/api/template-sync', {
          method: 'POST',
          body: JSON.stringify({
            action: 'sync-single',
            templateId: 'template-1'
          })
        })

        const response = await SyncPOST(request)
        const data = await response.json()

        expect(response.status).toBe(200)
        expect(data.success).toBe(true)
        expect(mockTemplateSyncService.syncSingleTemplate).toHaveBeenCalledWith('template-1', 'user-1')
      })

      it('should create template version', async () => {
        mockTemplateSyncService.createTemplateVersion.mockResolvedValue('version-1')

        const request = new NextRequest('http://localhost/api/template-sync', {
          method: 'POST',
          body: JSON.stringify({
            action: 'create-version',
            templateId: 'template-1',
            templateData: { name: 'Test Template' },
            changesSummary: 'Updated layout'
          })
        })

        const response = await SyncPOST(request)
        const data = await response.json()

        expect(response.status).toBe(200)
        expect(data.success).toBe(true)
        expect(data.versionId).toBe('version-1')
      })

      it('should resolve conflict', async () => {
        mockTemplateSyncService.resolveConflict.mockResolvedValue(undefined)

        const request = new NextRequest('http://localhost/api/template-sync', {
          method: 'POST',
          body: JSON.stringify({
            action: 'resolve-conflict',
            conflictId: 'conflict-1',
            resolution: 'prefer_local'
          })
        })

        const response = await SyncPOST(request)
        const data = await response.json()

        expect(response.status).toBe(200)
        expect(data.success).toBe(true)
        expect(mockTemplateSyncService.resolveConflict).toHaveBeenCalledWith('conflict-1', {
          conflictId: 'conflict-1',
          resolution: 'prefer_local'
        })
      })

      it('should rollback template', async () => {
        mockTemplateSyncService.rollbackToVersion.mockResolvedValue(undefined)

        const request = new NextRequest('http://localhost/api/template-sync', {
          method: 'POST',
          body: JSON.stringify({
            action: 'rollback',
            templateId: 'template-1',
            versionId: 'version-1'
          })
        })

        const response = await SyncPOST(request)
        const data = await response.json()

        expect(response.status).toBe(200)
        expect(data.success).toBe(true)
        expect(mockTemplateSyncService.rollbackToVersion).toHaveBeenCalledWith('template-1', 'version-1', 'user-1')
      })

      it('should upload to cloud storage', async () => {
        mockCloudStorageService.syncTemplate.mockResolvedValue({
          success: true,
          url: 'cloud://template-1'
        })

        const request = new NextRequest('http://localhost/api/template-sync', {
          method: 'POST',
          body: JSON.stringify({
            action: 'upload-cloud',
            templateId: 'template-1',
            templateData: { name: 'Test Template' }
          })
        })

        const response = await SyncPOST(request)
        const data = await response.json()

        expect(response.status).toBe(200)
        expect(data.success).toBe(true)
        expect(data.url).toBe('cloud://template-1')
      })

      it('should download from cloud storage', async () => {
        const mockTemplateData = { name: 'Test Template' }
        mockCloudStorageService.downloadTemplate.mockResolvedValue(mockTemplateData)

        const request = new NextRequest('http://localhost/api/template-sync', {
          method: 'POST',
          body: JSON.stringify({
            action: 'download-cloud',
            url: 'cloud://template-1'
          })
        })

        const response = await SyncPOST(request)
        const data = await response.json()

        expect(response.status).toBe(200)
        expect(data.success).toBe(true)
        expect(data.templateData).toEqual(mockTemplateData)
      })

      it('should handle batch sync', async () => {
        const mockResults = [
          { templateId: 'template-1', success: true, url: 'cloud://template-1' },
          { templateId: 'template-2', success: false, error: 'Upload failed' }
        ]

        mockCloudStorageService.batchSyncTemplates.mockResolvedValue(mockResults)

        const request = new NextRequest('http://localhost/api/template-sync', {
          method: 'POST',
          body: JSON.stringify({
            action: 'batch-sync',
            templates: [
              { templateId: 'template-1', templateData: {} },
              { templateId: 'template-2', templateData: {} }
            ]
          })
        })

        const response = await SyncPOST(request)
        const data = await response.json()

        expect(response.status).toBe(200)
        expect(data.success).toBe(true)
        expect(data.results).toEqual(mockResults)
      })

      it('should return 400 for invalid action', async () => {
        const request = new NextRequest('http://localhost/api/template-sync', {
          method: 'POST',
          body: JSON.stringify({ action: 'invalid-action' })
        })

        const response = await SyncPOST(request)

        expect(response.status).toBe(400)
      })

      it('should handle validation errors', async () => {
        const request = new NextRequest('http://localhost/api/template-sync', {
          method: 'POST',
          body: JSON.stringify({
            action: 'create-version',
            // Missing required fields
          })
        })

        const response = await SyncPOST(request)

        expect(response.status).toBe(400)
      })
    })

    describe('DELETE /api/template-sync', () => {
      it('should delete template from cloud storage', async () => {
        mockCloudStorageService.deleteTemplate.mockResolvedValue(undefined)

        const request = new NextRequest('http://localhost/api/template-sync?action=delete-cloud&url=cloud://template-1')

        const response = await SyncDELETE(request)
        const data = await response.json()

        expect(response.status).toBe(200)
        expect(data.success).toBe(true)
        expect(mockCloudStorageService.deleteTemplate).toHaveBeenCalledWith('cloud://template-1')
      })

      it('should return 400 for missing URL', async () => {
        const request = new NextRequest('http://localhost/api/template-sync?action=delete-cloud')

        const response = await SyncDELETE(request)

        expect(response.status).toBe(400)
      })
    })
  })

  describe('Template Marketplace Routes', () => {
    describe('GET /api/template-marketplace', () => {
      it('should search templates', async () => {
        const mockResult = {
          templates: [
            {
              id: 'template-1',
              name: 'Professional Resume',
              price: 0,
              rating: 4.5
            }
          ],
          total: 1,
          hasMore: false
        }

        mockMarketplaceService.searchTemplates.mockResolvedValue(mockResult)

        const request = new NextRequest('http://localhost/api/template-marketplace?action=search&query=professional&limit=20')

        const response = await MarketplaceGET(request)
        const data = await response.json()

        expect(response.status).toBe(200)
        expect(data.success).toBe(true)
        expect(data.templates).toEqual(mockResult.templates)
        expect(data.total).toBe(1)
        expect(data.hasMore).toBe(false)
      })

      it('should get featured templates', async () => {
        const mockTemplates = [
          {
            id: 'featured-1',
            name: 'Featured Template',
            isFeatured: true
          }
        ]

        mockMarketplaceService.getFeaturedTemplates.mockResolvedValue(mockTemplates)

        const request = new NextRequest('http://localhost/api/template-marketplace?action=featured&limit=5')

        const response = await MarketplaceGET(request)
        const data = await response.json()

        expect(response.status).toBe(200)
        expect(data.success).toBe(true)
        expect(data.templates).toEqual(mockTemplates)
        expect(mockMarketplaceService.getFeaturedTemplates).toHaveBeenCalledWith(5)
      })

      it('should get template categories', async () => {
        const request = new NextRequest('http://localhost/api/template-marketplace?action=categories')

        const response = await MarketplaceGET(request)
        const data = await response.json()

        expect(response.status).toBe(200)
        expect(data.success).toBe(true)
        expect(data.categories).toBeDefined()
        expect(Array.isArray(data.categories)).toBe(true)
      })

      it('should get template reviews', async () => {
        const mockReviews = [
          {
            id: 'review-1',
            rating: 5,
            reviewText: 'Great template!',
            user: { name: 'John Doe' }
          }
        ]

        mockMarketplaceService.getTemplateReviews.mockResolvedValue(mockReviews)

        const request = new NextRequest('http://localhost/api/template-marketplace?action=reviews&templateId=template-1&limit=10')

        const response = await MarketplaceGET(request)
        const data = await response.json()

        expect(response.status).toBe(200)
        expect(data.success).toBe(true)
        expect(data.reviews).toEqual(mockReviews)
        expect(mockMarketplaceService.getTemplateReviews).toHaveBeenCalledWith('template-1', 10, 0)
      })

      it('should get template analytics for authenticated user', async () => {
        const mockAnalytics = {
          views: 100,
          downloads: 50,
          revenue: 250,
          rating: 4.5
        }

        mockMarketplaceService.getTemplateAnalytics.mockResolvedValue(mockAnalytics)

        const request = new NextRequest('http://localhost/api/template-marketplace?action=analytics&templateId=template-1')

        const response = await MarketplaceGET(request)
        const data = await response.json()

        expect(response.status).toBe(200)
        expect(data.success).toBe(true)
        expect(data.analytics).toEqual(mockAnalytics)
        expect(mockMarketplaceService.getTemplateAnalytics).toHaveBeenCalledWith('template-1', 'user-1')
      })

      it('should return 401 for analytics without authentication', async () => {
        const { getServerSession } = require('next-auth')
        getServerSession.mockResolvedValue(null)

        const request = new NextRequest('http://localhost/api/template-marketplace?action=analytics&templateId=template-1')

        const response = await MarketplaceGET(request)

        expect(response.status).toBe(401)
      })
    })

    describe('POST /api/template-marketplace', () => {
      it('should publish template', async () => {
        mockMarketplaceService.publishTemplate.mockResolvedValue('listing-1')

        const request = new NextRequest('http://localhost/api/template-marketplace', {
          method: 'POST',
          body: JSON.stringify({
            action: 'publish',
            templateId: 'template-1',
            price: 9.99,
            isFeatured: false
          })
        })

        const response = await MarketplacePOST(request)
        const data = await response.json()

        expect(response.status).toBe(200)
        expect(data.success).toBe(true)
        expect(data.listingId).toBe('listing-1')
        expect(mockMarketplaceService.publishTemplate).toHaveBeenCalledWith({
          templateId: 'template-1',
          price: 9.99,
          isFeatured: false
        }, 'user-1')
      })

      it('should purchase template', async () => {
        const mockResult = {
          success: true,
          purchaseId: 'purchase-1',
          templateData: { name: 'Purchased Template' }
        }

        mockMarketplaceService.purchaseTemplate.mockResolvedValue(mockResult)

        const request = new NextRequest('http://localhost/api/template-marketplace', {
          method: 'POST',
          body: JSON.stringify({
            action: 'purchase',
            templateId: 'template-1'
          })
        })

        const response = await MarketplacePOST(request)
        const data = await response.json()

        expect(response.status).toBe(200)
        expect(data.success).toBe(true)
        expect(data.purchaseId).toBe('purchase-1')
        expect(data.templateData).toEqual(mockResult.templateData)
      })

      it('should add template review', async () => {
        mockMarketplaceService.addReview.mockResolvedValue(undefined)

        const request = new NextRequest('http://localhost/api/template-marketplace', {
          method: 'POST',
          body: JSON.stringify({
            action: 'review',
            templateId: 'template-1',
            rating: 5,
            reviewText: 'Excellent template!'
          })
        })

        const response = await MarketplacePOST(request)
        const data = await response.json()

        expect(response.status).toBe(200)
        expect(data.success).toBe(true)
        expect(mockMarketplaceService.addReview).toHaveBeenCalledWith({
          templateId: 'template-1',
          rating: 5,
          reviewText: 'Excellent template!'
        }, 'user-1')
      })

      it('should create template collection', async () => {
        mockMarketplaceService.createCollection.mockResolvedValue('collection-1')

        const request = new NextRequest('http://localhost/api/template-marketplace', {
          method: 'POST',
          body: JSON.stringify({
            action: 'create-collection',
            name: 'Professional Templates',
            description: 'Collection of professional templates',
            templateIds: ['template-1', 'template-2'],
            isPublic: true
          })
        })

        const response = await MarketplacePOST(request)
        const data = await response.json()

        expect(response.status).toBe(200)
        expect(data.success).toBe(true)
        expect(data.collectionId).toBe('collection-1')
      })

      it('should track template usage', async () => {
        mockMarketplaceService.trackTemplateUsage.mockResolvedValue(undefined)

        const request = new NextRequest('http://localhost/api/template-marketplace', {
          method: 'POST',
          body: JSON.stringify({
            action: 'track-usage',
            templateId: 'template-1',
            actionType: 'view',
            metadata: { deviceType: 'desktop' }
          })
        })

        const response = await MarketplacePOST(request)
        const data = await response.json()

        expect(response.status).toBe(200)
        expect(data.success).toBe(true)
        expect(mockMarketplaceService.trackTemplateUsage).toHaveBeenCalledWith(
          'template-1',
          'user-1',
          'view',
          { deviceType: 'desktop' }
        )
      })

      it('should return 401 for unauthenticated requests', async () => {
        const { getServerSession } = require('next-auth')
        getServerSession.mockResolvedValue(null)

        const request = new NextRequest('http://localhost/api/template-marketplace', {
          method: 'POST',
          body: JSON.stringify({ action: 'publish' })
        })

        const response = await MarketplacePOST(request)

        expect(response.status).toBe(401)
      })

      it('should handle validation errors', async () => {
        const request = new NextRequest('http://localhost/api/template-marketplace', {
          method: 'POST',
          body: JSON.stringify({
            action: 'publish',
            // Missing required fields
          })
        })

        const response = await MarketplacePOST(request)

        expect(response.status).toBe(400)
      })
    })
  })

  describe('Error Handling', () => {
    it('should handle service errors', async () => {
      mockTemplateSyncService.getSyncStatus.mockRejectedValue(new Error('Service error'))

      const request = new NextRequest('http://localhost/api/template-sync?action=status')

      const response = await SyncGET(request)

      expect(response.status).toBe(500)
    })

    it('should handle marketplace service errors', async () => {
      mockMarketplaceService.searchTemplates.mockRejectedValue(new Error('Search failed'))

      const request = new NextRequest('http://localhost/api/template-marketplace?action=search')

      const response = await MarketplaceGET(request)

      expect(response.status).toBe(500)
    })
  })
})
