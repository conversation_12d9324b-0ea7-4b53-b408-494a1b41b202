import { describe, it, expect, vi, beforeEach } from 'vitest'
import { GET, POST } from '../route'
import { NextRequest } from 'next/server'

// Mock the auth function
vi.mock('@/lib/auth', () => ({
  getServerSession: vi.fn(),
}))

// Mock the database
vi.mock('@database/client', () => ({
  prisma: {
    resume: {
      findMany: vi.fn(),
      create: vi.fn(),
      findUnique: vi.fn(),
      update: vi.fn(),
      delete: vi.fn(),
    },
    user: {
      findUnique: vi.fn(),
    },
  },
}))

const mockSession = {
  user: {
    id: 'test-user-id',
    email: '<EMAIL>',
    name: 'Test User',
  },
}

const mockResumes = [
  {
    id: '1',
    title: 'Software Engineer Resume',
    description: 'Full-stack developer position',
    status: 'PUBLISHED',
    templateId: 'modern-1',
    isPublic: true,
    publicUrl: 'https://example.com/resume/1',
    userId: 'test-user-id',
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-15'),
    personalInfo: '{"name":"<PERSON>e","email":"<EMAIL>"}',
    sections: '{"experience":[],"education":[],"skills":[]}',
    settings: '{"theme":"modern","colors":["#3B82F6"]}',
    metadata: '{"version":"1.0"}',
  },
]

describe('/api/resumes', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('GET /api/resumes', () => {
    it('returns user resumes when authenticated', async () => {
      const { getServerSession } = await import('@/lib/auth')
      const { prisma } = await import('@database/client')

      vi.mocked(getServerSession).mockResolvedValue(mockSession)
      vi.mocked(prisma.resume.findMany).mockResolvedValue(mockResumes)

      const request = new NextRequest('http://localhost:3000/api/resumes')
      const response = await GET(request)

      expect(response.status).toBe(200)
      
      const data = await response.json()
      expect(data.resumes).toHaveLength(1)
      expect(data.resumes[0].title).toBe('Software Engineer Resume')

      expect(prisma.resume.findMany).toHaveBeenCalledWith({
        where: { userId: 'test-user-id' },
        orderBy: { updatedAt: 'desc' },
        include: {
          template: {
            select: {
              id: true,
              name: true,
              category: true,
            },
          },
        },
      })
    })

    it('returns 401 when not authenticated', async () => {
      const { getServerSession } = await import('@/lib/auth')
      
      vi.mocked(getServerSession).mockResolvedValue(null)

      const request = new NextRequest('http://localhost:3000/api/resumes')
      const response = await GET(request)

      expect(response.status).toBe(401)
      
      const data = await response.json()
      expect(data.error).toBe('Unauthorized')
    })

    it('handles database errors gracefully', async () => {
      const { getServerSession } = await import('@/lib/auth')
      const { prisma } = await import('@database/client')

      vi.mocked(getServerSession).mockResolvedValue(mockSession)
      vi.mocked(prisma.resume.findMany).mockRejectedValue(new Error('Database error'))

      const request = new NextRequest('http://localhost:3000/api/resumes')
      const response = await GET(request)

      expect(response.status).toBe(500)
      
      const data = await response.json()
      expect(data.error).toBe('Internal server error')
    })

    it('filters resumes by status when provided', async () => {
      const { getServerSession } = await import('@/lib/auth')
      const { prisma } = await import('@database/client')

      vi.mocked(getServerSession).mockResolvedValue(mockSession)
      vi.mocked(prisma.resume.findMany).mockResolvedValue(mockResumes)

      const request = new NextRequest('http://localhost:3000/api/resumes?status=PUBLISHED')
      const response = await GET(request)

      expect(response.status).toBe(200)
      
      expect(prisma.resume.findMany).toHaveBeenCalledWith({
        where: { 
          userId: 'test-user-id',
          status: 'PUBLISHED'
        },
        orderBy: { updatedAt: 'desc' },
        include: {
          template: {
            select: {
              id: true,
              name: true,
              category: true,
            },
          },
        },
      })
    })

    it('limits results when limit parameter is provided', async () => {
      const { getServerSession } = await import('@/lib/auth')
      const { prisma } = await import('@database/client')

      vi.mocked(getServerSession).mockResolvedValue(mockSession)
      vi.mocked(prisma.resume.findMany).mockResolvedValue(mockResumes)

      const request = new NextRequest('http://localhost:3000/api/resumes?limit=5')
      const response = await GET(request)

      expect(response.status).toBe(200)
      
      expect(prisma.resume.findMany).toHaveBeenCalledWith({
        where: { userId: 'test-user-id' },
        orderBy: { updatedAt: 'desc' },
        take: 5,
        include: {
          template: {
            select: {
              id: true,
              name: true,
              category: true,
            },
          },
        },
      })
    })
  })

  describe('POST /api/resumes', () => {
    const validResumeData = {
      title: 'New Resume',
      description: 'Test resume description',
      templateId: 'modern-1',
    }

    it('creates a new resume when authenticated', async () => {
      const { getServerSession } = await import('@/lib/auth')
      const { prisma } = await import('@database/client')

      vi.mocked(getServerSession).mockResolvedValue(mockSession)
      vi.mocked(prisma.resume.create).mockResolvedValue({
        ...mockResumes[0],
        ...validResumeData,
        id: 'new-resume-id',
      })

      const request = new NextRequest('http://localhost:3000/api/resumes', {
        method: 'POST',
        body: JSON.stringify(validResumeData),
        headers: {
          'Content-Type': 'application/json',
        },
      })

      const response = await POST(request)

      expect(response.status).toBe(201)
      
      const data = await response.json()
      expect(data.resume.title).toBe('New Resume')
      expect(data.resume.id).toBe('new-resume-id')

      expect(prisma.resume.create).toHaveBeenCalledWith({
        data: {
          title: 'New Resume',
          description: 'Test resume description',
          templateId: 'modern-1',
          userId: 'test-user-id',
          status: 'DRAFT',
          isPublic: false,
        },
        include: {
          template: {
            select: {
              id: true,
              name: true,
              category: true,
            },
          },
        },
      })
    })

    it('returns 401 when not authenticated', async () => {
      const { getServerSession } = await import('@/lib/auth')
      
      vi.mocked(getServerSession).mockResolvedValue(null)

      const request = new NextRequest('http://localhost:3000/api/resumes', {
        method: 'POST',
        body: JSON.stringify(validResumeData),
        headers: {
          'Content-Type': 'application/json',
        },
      })

      const response = await POST(request)

      expect(response.status).toBe(401)
      
      const data = await response.json()
      expect(data.error).toBe('Unauthorized')
    })

    it('validates required fields', async () => {
      const { getServerSession } = await import('@/lib/auth')
      
      vi.mocked(getServerSession).mockResolvedValue(mockSession)

      const invalidData = {
        description: 'Missing title',
        templateId: 'modern-1',
      }

      const request = new NextRequest('http://localhost:3000/api/resumes', {
        method: 'POST',
        body: JSON.stringify(invalidData),
        headers: {
          'Content-Type': 'application/json',
        },
      })

      const response = await POST(request)

      expect(response.status).toBe(400)
      
      const data = await response.json()
      expect(data.error).toBe('Title is required')
    })

    it('handles invalid JSON gracefully', async () => {
      const { getServerSession } = await import('@/lib/auth')
      
      vi.mocked(getServerSession).mockResolvedValue(mockSession)

      const request = new NextRequest('http://localhost:3000/api/resumes', {
        method: 'POST',
        body: 'invalid json',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      const response = await POST(request)

      expect(response.status).toBe(400)
      
      const data = await response.json()
      expect(data.error).toBe('Invalid JSON')
    })

    it('handles database creation errors', async () => {
      const { getServerSession } = await import('@/lib/auth')
      const { prisma } = await import('@database/client')

      vi.mocked(getServerSession).mockResolvedValue(mockSession)
      vi.mocked(prisma.resume.create).mockRejectedValue(new Error('Database error'))

      const request = new NextRequest('http://localhost:3000/api/resumes', {
        method: 'POST',
        body: JSON.stringify(validResumeData),
        headers: {
          'Content-Type': 'application/json',
        },
      })

      const response = await POST(request)

      expect(response.status).toBe(500)
      
      const data = await response.json()
      expect(data.error).toBe('Failed to create resume')
    })

    it('validates title length', async () => {
      const { getServerSession } = await import('@/lib/auth')
      
      vi.mocked(getServerSession).mockResolvedValue(mockSession)

      const longTitleData = {
        title: 'a'.repeat(256), // Too long
        templateId: 'modern-1',
      }

      const request = new NextRequest('http://localhost:3000/api/resumes', {
        method: 'POST',
        body: JSON.stringify(longTitleData),
        headers: {
          'Content-Type': 'application/json',
        },
      })

      const response = await POST(request)

      expect(response.status).toBe(400)
      
      const data = await response.json()
      expect(data.error).toBe('Title must be less than 255 characters')
    })

    it('sets default values correctly', async () => {
      const { getServerSession } = await import('@/lib/auth')
      const { prisma } = await import('@database/client')

      vi.mocked(getServerSession).mockResolvedValue(mockSession)
      vi.mocked(prisma.resume.create).mockResolvedValue({
        ...mockResumes[0],
        title: 'Minimal Resume',
        id: 'new-resume-id',
      })

      const minimalData = {
        title: 'Minimal Resume',
      }

      const request = new NextRequest('http://localhost:3000/api/resumes', {
        method: 'POST',
        body: JSON.stringify(minimalData),
        headers: {
          'Content-Type': 'application/json',
        },
      })

      const response = await POST(request)

      expect(response.status).toBe(201)
      
      expect(prisma.resume.create).toHaveBeenCalledWith({
        data: {
          title: 'Minimal Resume',
          userId: 'test-user-id',
          status: 'DRAFT',
          isPublic: false,
        },
        include: {
          template: {
            select: {
              id: true,
              name: true,
              category: true,
            },
          },
        },
      })
    })
  })
})
