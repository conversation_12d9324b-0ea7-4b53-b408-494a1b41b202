/**
 * Feature Access API Routes
 * 
 * Handles feature access control, usage tracking, and premium feature gates
 */

import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { featureGate, FEATURES, type FeatureName } from '@/lib/payments/feature-gate'
import { z } from 'zod'

// Request schemas
const CheckAccessSchema = z.object({
  feature: z.string()
})

const TrackUsageSchema = z.object({
  feature: z.string(),
  amount: z.number().positive().default(1),
  metadata: z.record(z.string()).optional()
})

const GetUsageSchema = z.object({
  feature: z.string().optional(),
  days: z.number().positive().default(30)
})

/**
 * GET /api/features
 * Get feature access and usage information
 */
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const action = searchParams.get('action')

    switch (action) {
      case 'check-access':
        const feature = searchParams.get('feature') as FeatureName
        if (!feature) {
          return NextResponse.json({ error: 'Feature parameter required' }, { status: 400 })
        }

        const accessResult = await featureGate.checkAccess(session.user.id, feature)
        return NextResponse.json({ success: true, access: accessResult })

      case 'check-usage':
        const usageFeature = searchParams.get('feature') as FeatureName
        if (!usageFeature) {
          return NextResponse.json({ error: 'Feature parameter required' }, { status: 400 })
        }

        const usageResult = await featureGate.checkUsageLimit(session.user.id, usageFeature)
        return NextResponse.json({ success: true, usage: usageResult })

      case 'can-use':
        const canUseFeature = searchParams.get('feature') as FeatureName
        if (!canUseFeature) {
          return NextResponse.json({ error: 'Feature parameter required' }, { status: 400 })
        }

        const canUseResult = await featureGate.canUseFeature(session.user.id, canUseFeature)
        return NextResponse.json({ success: true, canUse: canUseResult })

      case 'remaining-usage':
        const remainingFeature = searchParams.get('feature') as FeatureName
        if (!remainingFeature) {
          return NextResponse.json({ error: 'Feature parameter required' }, { status: 400 })
        }

        const remaining = await featureGate.getRemainingUsage(session.user.id, remainingFeature)
        return NextResponse.json({ success: true, remaining })

      case 'usage-analytics':
        const days = parseInt(searchParams.get('days') || '30')
        const analytics = await featureGate.getUserUsageAnalytics(session.user.id, days)
        return NextResponse.json({ success: true, analytics })

      case 'all-features':
        // Get access status for all features
        const allFeatures = Object.values(FEATURES)
        const featureAccess = await Promise.all(
          allFeatures.map(async (feature) => {
            const [access, usage] = await Promise.all([
              featureGate.checkAccess(session.user.id, feature),
              featureGate.checkUsageLimit(session.user.id, feature)
            ])
            return {
              feature,
              access,
              usage
            }
          })
        )

        return NextResponse.json({ success: true, features: featureAccess })

      case 'feature-list':
        // Return list of all available features
        const featureList = Object.entries(FEATURES).map(([key, value]) => ({
          key,
          value,
          name: key.toLowerCase().replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
        }))

        return NextResponse.json({ success: true, features: featureList })

      default:
        return NextResponse.json({ error: 'Invalid action' }, { status: 400 })
    }
  } catch (error) {
    console.error('Feature GET error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

/**
 * POST /api/features
 * Track feature usage and perform feature-related actions
 */
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { action } = body

    switch (action) {
      case 'track-usage':
        const usageData = TrackUsageSchema.parse(body)
        
        // Check if user can use the feature before tracking
        const canUse = await featureGate.canUseFeature(session.user.id, usageData.feature as FeatureName)
        if (!canUse.allowed) {
          return NextResponse.json({ 
            success: false, 
            error: canUse.reason,
            upgradeRequired: canUse.upgradeRequired,
            usageExceeded: canUse.usageExceeded
          }, { status: 403 })
        }

        await featureGate.trackUsage({
          userId: session.user.id,
          feature: usageData.feature,
          amount: usageData.amount,
          metadata: usageData.metadata
        })

        return NextResponse.json({ success: true, message: 'Usage tracked successfully' })

      case 'check-and-track':
        const checkTrackData = TrackUsageSchema.parse(body)
        
        // Check access and usage in one call
        const result = await featureGate.canUseFeature(session.user.id, checkTrackData.feature as FeatureName)
        
        if (result.allowed) {
          // Track usage if allowed
          await featureGate.trackUsage({
            userId: session.user.id,
            feature: checkTrackData.feature,
            amount: checkTrackData.amount,
            metadata: checkTrackData.metadata
          })
        }

        return NextResponse.json({ 
          success: result.allowed, 
          result,
          tracked: result.allowed
        })

      case 'bulk-check':
        const { features } = body
        if (!Array.isArray(features)) {
          return NextResponse.json({ error: 'Features must be an array' }, { status: 400 })
        }

        const bulkResults = await Promise.all(
          features.map(async (feature: string) => {
            const canUse = await featureGate.canUseFeature(session.user.id, feature as FeatureName)
            return {
              feature,
              ...canUse
            }
          })
        )

        return NextResponse.json({ success: true, results: bulkResults })

      default:
        return NextResponse.json({ error: 'Invalid action' }, { status: 400 })
    }
  } catch (error) {
    console.error('Feature POST error:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal server error' },
      { status: 500 }
    )
  }
}

/**
 * PUT /api/features
 * Update feature-related settings
 */
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { action } = body

    switch (action) {
      case 'reset-usage':
        const { feature } = body
        if (!feature) {
          return NextResponse.json({ error: 'Feature parameter required' }, { status: 400 })
        }

        // This would typically be an admin-only operation
        // For now, we'll just return success without actually resetting
        return NextResponse.json({ 
          success: true, 
          message: 'Usage reset functionality not implemented' 
        })

      default:
        return NextResponse.json({ error: 'Invalid action' }, { status: 400 })
    }
  } catch (error) {
    console.error('Feature PUT error:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal server error' },
      { status: 500 }
    )
  }
}
