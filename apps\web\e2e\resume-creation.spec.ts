import { test, expect } from '@playwright/test'

test.describe('Resume Creation Flow', () => {
  test.beforeEach(async ({ page }) => {
    // Mock authentication
    await page.route('**/api/auth/session', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          user: {
            id: 'test-user-id',
            name: 'Test User',
            email: '<EMAIL>',
            image: 'https://example.com/avatar.jpg',
          },
          expires: '2024-12-31',
        }),
      })
    })

    // Mock resumes API
    await page.route('**/api/resumes', async route => {
      if (route.request().method() === 'GET') {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            resumes: [
              {
                id: '1',
                title: 'Software Engineer Resume',
                description: 'Full-stack developer position',
                status: 'PUBLISHED',
                templateId: 'modern-1',
                templateName: 'Modern Professional',
                isPublic: true,
                createdAt: '2024-01-01T00:00:00Z',
                updatedAt: '2024-01-15T10:00:00Z',
              },
            ],
          }),
        })
      } else if (route.request().method() === 'POST') {
        await route.fulfill({
          status: 201,
          contentType: 'application/json',
          body: JSON.stringify({
            resume: {
              id: 'new-resume-id',
              title: 'New Resume',
              status: 'DRAFT',
              templateId: 'modern-1',
            },
          }),
        })
      }
    })

    // Navigate to dashboard
    await page.goto('/dashboard')
  })

  test('should display dashboard with user information', async ({ page }) => {
    // Check if user is welcomed
    await expect(page.getByText('Welcome back, Test!')).toBeVisible()
    
    // Check if navigation is present
    await expect(page.getByText('Dashboard')).toBeVisible()
    await expect(page.getByText('My Resumes')).toBeVisible()
    await expect(page.getByText('Templates')).toBeVisible()
    
    // Check if create resume button is present
    await expect(page.getByRole('button', { name: /create resume/i })).toBeVisible()
  })

  test('should navigate to resumes page and display resume grid', async ({ page }) => {
    // Navigate to resumes page
    await page.getByRole('button', { name: /my resumes/i }).click()
    
    // Check if we're on the resumes page
    await expect(page.getByText('My Resumes')).toBeVisible()
    await expect(page.getByText('Manage and organize your professional resumes')).toBeVisible()
    
    // Check if resume cards are displayed
    await expect(page.getByText('Software Engineer Resume')).toBeVisible()
    await expect(page.getByText('Full-stack developer position')).toBeVisible()
    await expect(page.getByText('PUBLISHED')).toBeVisible()
  })

  test('should complete the full resume creation flow', async ({ page }) => {
    // Start creating a new resume
    await page.getByRole('button', { name: /create resume/i }).first().click()
    
    // Should be on the new resume page
    await expect(page.getByText('Create New Resume')).toBeVisible()
    await expect(page.getByText('Choose Your Template')).toBeVisible()
    
    // Step 1: Select a template
    await expect(page.getByText('Select a professional template')).toBeVisible()
    
    // Mock template selection (since we're not implementing the full template gallery in this test)
    await page.evaluate(() => {
      // Simulate template selection
      const event = new CustomEvent('templateSelected', {
        detail: {
          id: 'modern-1',
          name: 'Modern Professional',
          description: 'Clean and contemporary design',
          category: 'modern',
          isPremium: false,
        }
      })
      window.dispatchEvent(event)
    })
    
    // Proceed to step 2
    const nextButton = page.getByRole('button', { name: /next/i })
    await nextButton.click()
    
    // Step 2: Fill in resume details
    await expect(page.getByText('Resume Details')).toBeVisible()
    
    const titleInput = page.getByLabel('Resume Title *')
    await titleInput.fill('My Test Resume')
    
    const descriptionInput = page.getByLabel('Description (Optional)')
    await descriptionInput.fill('This is a test resume for automation testing')
    
    // Proceed to step 3
    await page.getByRole('button', { name: /next/i }).click()
    
    // Step 3: Review and create
    await expect(page.getByText('Ready to Create!')).toBeVisible()
    await expect(page.getByText('My Test Resume')).toBeVisible()
    await expect(page.getByText('This is a test resume for automation testing')).toBeVisible()
    
    // Create the resume
    await page.getByRole('button', { name: /create my resume/i }).click()
    
    // Should show loading state
    await expect(page.getByText('Creating Resume...')).toBeVisible()
    
    // Should navigate to editor (mocked)
    await page.waitForURL('**/resumes/new-resume-id/edit')
  })

  test('should handle template gallery search and filtering', async ({ page }) => {
    // Navigate to templates page
    await page.getByRole('button', { name: /templates/i }).click()
    
    // Should be on templates page
    await expect(page.getByText('Resume Templates')).toBeVisible()
    await expect(page.getByText('Choose from our collection')).toBeVisible()
    
    // Test search functionality
    const searchInput = page.getByPlaceholder('Search templates...')
    await searchInput.fill('Modern')
    
    // Test category filtering
    const categoryFilter = page.getByRole('combobox').first()
    await categoryFilter.click()
    await page.getByText('Modern').click()
    
    // Should show filtered results
    await expect(page.getByText('Modern')).toBeVisible()
  })

  test('should handle resume actions from grid', async ({ page }) => {
    // Navigate to resumes page
    await page.getByRole('button', { name: /my resumes/i }).click()
    
    // Wait for resumes to load
    await expect(page.getByText('Software Engineer Resume')).toBeVisible()
    
    // Test view action
    await page.getByRole('button', { name: /view/i }).first().click()
    await page.waitForURL('**/resumes/1')
    
    // Go back to resumes
    await page.goBack()
    
    // Test edit action
    await page.getByRole('button', { name: /edit/i }).first().click()
    await page.waitForURL('**/resumes/1/edit')
  })

  test('should handle responsive design on mobile', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 })
    
    // Check if mobile menu button is visible
    await expect(page.getByRole('button').first()).toBeVisible()
    
    // Open mobile menu
    await page.getByRole('button').first().click()
    
    // Check if navigation items are visible in mobile menu
    await expect(page.getByText('Dashboard')).toBeVisible()
    await expect(page.getByText('My Resumes')).toBeVisible()
  })

  test('should handle dark mode toggle', async ({ page }) => {
    // Find theme toggle button
    const themeToggle = page.getByRole('button').filter({ hasText: /theme/i }).first()
    
    // Toggle to dark mode
    await themeToggle.click()
    
    // Check if dark mode is applied (this would depend on your implementation)
    await expect(page.locator('html')).toHaveAttribute('data-theme', 'dark')
    
    // Toggle back to light mode
    await themeToggle.click()
    await expect(page.locator('html')).toHaveAttribute('data-theme', 'light')
  })

  test('should handle error states gracefully', async ({ page }) => {
    // Mock API error
    await page.route('**/api/resumes', async route => {
      await route.fulfill({
        status: 500,
        contentType: 'application/json',
        body: JSON.stringify({ error: 'Internal server error' }),
      })
    })
    
    // Navigate to resumes page
    await page.getByRole('button', { name: /my resumes/i }).click()
    
    // Should handle error gracefully (this depends on your error handling implementation)
    // For now, we'll just check that the page doesn't crash
    await expect(page.getByText('My Resumes')).toBeVisible()
  })

  test('should validate form inputs', async ({ page }) => {
    // Start creating a new resume
    await page.getByRole('button', { name: /create resume/i }).first().click()
    
    // Try to proceed without selecting template
    const nextButton = page.getByRole('button', { name: /next/i })
    await expect(nextButton).toBeDisabled()
    
    // Mock template selection and proceed
    await page.evaluate(() => {
      const event = new CustomEvent('templateSelected', {
        detail: { id: 'modern-1', name: 'Modern Professional' }
      })
      window.dispatchEvent(event)
    })
    
    await nextButton.click()
    
    // Go to final step without filling required fields
    await page.getByRole('button', { name: /next/i }).click()
    
    // Create button should be disabled without title
    const createButton = page.getByRole('button', { name: /create my resume/i })
    await expect(createButton).toBeDisabled()
  })

  test('should handle search and filtering in resume grid', async ({ page }) => {
    // Navigate to resumes page
    await page.getByRole('button', { name: /my resumes/i }).click()
    
    // Test search functionality
    const searchInput = page.getByPlaceholder('Search resumes...')
    await searchInput.fill('Software')
    
    // Should filter results
    await expect(page.getByText('Software Engineer Resume')).toBeVisible()
    
    // Test status filtering
    const statusFilter = page.getByRole('combobox').first()
    await statusFilter.click()
    await page.getByText('Published').click()
    
    // Should show only published resumes
    await expect(page.getByText('PUBLISHED')).toBeVisible()
  })
})
