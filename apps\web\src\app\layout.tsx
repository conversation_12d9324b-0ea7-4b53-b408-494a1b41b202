import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import { Providers } from '@/components/providers';
import { Toaster } from '@/components/ui/toaster';
import { cn } from '@/lib/utils';
import './globals.css';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: {
    default: 'CareerCraft - AI-Powered Resume Builder',
    template: '%s | CareerCraft',
  },
  description: 'Create professional, ATS-optimized resumes with AI assistance. Build your career with intelligent resume and cover letter generation.',
  keywords: ['resume builder', 'AI resume', 'cover letter', 'career', 'job search', 'ATS optimization'],
  authors: [{ name: 'CareerCraft Team' }],
  creator: 'CareerC<PERSON>',
  publisher: 'CareerCraft',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL(process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'),
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: process.env.NEXT_PUBLIC_APP_URL,
    title: 'CareerCraft - AI-Powered Resume Builder',
    description: 'Create professional, ATS-optimized resumes with AI assistance.',
    siteName: 'CareerCraft',
    images: [
      {
        url: '/og-image.png',
        width: 1200,
        height: 630,
        alt: 'CareerCraft - AI-Powered Resume Builder',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'CareerCraft - AI-Powered Resume Builder',
    description: 'Create professional, ATS-optimized resumes with AI assistance.',
    images: ['/og-image.png'],
    creator: '@careercraft',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: process.env.GOOGLE_SITE_VERIFICATION,
  },
};

interface RootLayoutProps {
  children: React.ReactNode;
}

export default function RootLayout({ children }: RootLayoutProps) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={cn(
        'min-h-screen bg-background font-sans antialiased',
        inter.className
      )}>
        <Providers>
          <div className="relative flex min-h-screen flex-col">
            <div className="flex-1">{children}</div>
          </div>
          <Toaster />
        </Providers>
      </body>
    </html>
  );
}
