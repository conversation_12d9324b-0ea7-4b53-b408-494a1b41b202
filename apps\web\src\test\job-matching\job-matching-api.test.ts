/**
 * Job Matching API Unit Tests
 * 
 * Tests for job matching API endpoints
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { NextRequest } from 'next/server'
import { GET as JobsGET, POST as JobsPOST, PUT as JobsPUT } from '@/app/api/job-matching/jobs/route'
import { GET as ApplicationsGET, POST as ApplicationsPOST, PUT as ApplicationsPUT, DELETE as ApplicationsDELETE } from '@/app/api/job-matching/applications/route'
import { GET as PreferencesGET, POST as PreferencesPOST } from '@/app/api/job-matching/preferences/route'

// Mock next-auth
const mockSession = {
  user: {
    id: 'user-1',
    name: '<PERSON>',
    email: '<EMAIL>'
  }
}

vi.mock('next-auth', () => ({
  getServerSession: vi.fn()
}))

// Mock job matching service
const mockJobMatchingService = {
  searchJobs: vi.fn(),
  getRecommendations: vi.fn(),
  calculateMatchScore: vi.fn(),
  generateRecommendations: vi.fn(),
  markRecommendationViewed: vi.fn(),
  updateRecommendationStatus: vi.fn(),
  applyToJob: vi.fn(),
  updateApplicationStatus: vi.fn(),
  getApplications: vi.fn(),
  updateJobPreferences: vi.fn()
}

vi.mock('@/lib/job-matching/service', () => ({
  jobMatchingService: mockJobMatchingService
}))

// Mock AI engine
const mockAIEngine = {
  analyzeJobPosting: vi.fn(),
  analyzeJobCompatibility: vi.fn(),
  generateInterviewQuestions: vi.fn(),
  extractSkillsFromResume: vi.fn(),
  predictCareerPath: vi.fn()
}

vi.mock('@/lib/job-matching/ai-engine', () => ({
  aiRecommendationEngine: mockAIEngine
}))

// Mock prisma
const mockPrisma = {
  userJobPreferences: {
    findUnique: vi.fn(),
    upsert: vi.fn()
  }
}

vi.mock('@/lib/db', () => ({
  prisma: mockPrisma
}))

describe('Job Matching API', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    const { getServerSession } = require('next-auth')
    getServerSession.mockResolvedValue(mockSession)
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('Jobs API', () => {
    describe('GET /api/job-matching/jobs', () => {
      it('should search jobs with criteria', async () => {
        const mockJobs = [
          {
            id: 'job-1',
            title: 'Software Developer',
            company: 'Tech Corp',
            description: 'Great opportunity',
            location: 'San Francisco',
            salaryMin: 80000,
            salaryMax: 120000
          }
        ]

        mockJobMatchingService.searchJobs.mockResolvedValue(mockJobs)

        const request = new NextRequest('http://localhost/api/job-matching/jobs?action=search&query=developer&location=San Francisco&limit=10')

        const response = await JobsGET(request)
        const data = await response.json()

        expect(response.status).toBe(200)
        expect(data.success).toBe(true)
        expect(data.jobs).toEqual(mockJobs)
        expect(mockJobMatchingService.searchJobs).toHaveBeenCalledWith({
          query: 'developer',
          location: 'San Francisco',
          remoteType: undefined,
          employmentType: undefined,
          experienceLevel: undefined,
          salaryMin: undefined,
          salaryMax: undefined,
          skills: undefined,
          companies: undefined,
          limit: 10,
          offset: 0
        })
      })

      it('should get job recommendations', async () => {
        const mockRecommendations = [
          {
            id: 'rec-1',
            jobPostingId: 'job-1',
            matchScore: 85,
            jobPosting: {
              id: 'job-1',
              title: 'Software Developer',
              company: 'Tech Corp'
            }
          }
        ]

        mockJobMatchingService.getRecommendations.mockResolvedValue(mockRecommendations)

        const request = new NextRequest('http://localhost/api/job-matching/jobs?action=recommendations&limit=5')

        const response = await JobsGET(request)
        const data = await response.json()

        expect(response.status).toBe(200)
        expect(data.success).toBe(true)
        expect(data.recommendations).toEqual(mockRecommendations)
        expect(mockJobMatchingService.getRecommendations).toHaveBeenCalledWith('user-1', 5)
      })

      it('should refresh recommendations', async () => {
        mockJobMatchingService.generateRecommendations.mockResolvedValue(3)
        mockJobMatchingService.getRecommendations.mockResolvedValue([])

        const request = new NextRequest('http://localhost/api/job-matching/jobs?action=recommendations&refresh=true')

        const response = await JobsGET(request)

        expect(response.status).toBe(200)
        expect(mockJobMatchingService.generateRecommendations).toHaveBeenCalledWith('user-1')
        expect(mockJobMatchingService.getRecommendations).toHaveBeenCalledWith('user-1', 10)
      })

      it('should return 401 for unauthenticated requests', async () => {
        const { getServerSession } = require('next-auth')
        getServerSession.mockResolvedValue(null)

        const request = new NextRequest('http://localhost/api/job-matching/jobs?action=search')

        const response = await JobsGET(request)

        expect(response.status).toBe(401)
      })
    })

    describe('POST /api/job-matching/jobs', () => {
      it('should analyze job posting', async () => {
        const mockJobs = [
          {
            id: 'job-1',
            title: 'Software Developer',
            company: 'Tech Corp',
            description: 'Great job',
            requirements: 'React, JavaScript',
            skills: ['React', 'JavaScript']
          }
        ]

        const mockAnalysis = {
          requiredSkills: ['React', 'JavaScript'],
          experienceLevel: 'mid',
          responsibilities: ['Build applications'],
          qualifications: ['Bachelor degree']
        }

        mockJobMatchingService.searchJobs.mockResolvedValue(mockJobs)
        mockAIEngine.analyzeJobPosting.mockResolvedValue(mockAnalysis)

        const request = new NextRequest('http://localhost/api/job-matching/jobs', {
          method: 'POST',
          body: JSON.stringify({
            action: 'analyze',
            jobId: 'job-1'
          })
        })

        const response = await JobsPOST(request)
        const data = await response.json()

        expect(response.status).toBe(200)
        expect(data.success).toBe(true)
        expect(data.job).toEqual(mockJobs[0])
        expect(data.analysis).toEqual(mockAnalysis)
      })

      it('should generate interview questions', async () => {
        const mockJobs = [
          {
            id: 'job-1',
            title: 'Software Developer',
            company: 'Tech Corp',
            description: 'Great job',
            skills: ['React', 'JavaScript']
          }
        ]

        const mockQuestions = [
          'Tell me about your React experience',
          'How do you handle state management?',
          'Describe a challenging project'
        ]

        mockJobMatchingService.searchJobs.mockResolvedValue(mockJobs)
        mockAIEngine.generateInterviewQuestions.mockResolvedValue(mockQuestions)

        const request = new NextRequest('http://localhost/api/job-matching/jobs', {
          method: 'POST',
          body: JSON.stringify({
            action: 'generate-questions',
            jobId: 'job-1'
          })
        })

        const response = await JobsPOST(request)
        const data = await response.json()

        expect(response.status).toBe(200)
        expect(data.success).toBe(true)
        expect(data.questions).toEqual(mockQuestions)
      })

      it('should calculate match score', async () => {
        mockJobMatchingService.calculateMatchScore.mockResolvedValue(75)

        const request = new NextRequest('http://localhost/api/job-matching/jobs', {
          method: 'POST',
          body: JSON.stringify({
            action: 'calculate-match',
            jobId: 'job-1'
          })
        })

        const response = await JobsPOST(request)
        const data = await response.json()

        expect(response.status).toBe(200)
        expect(data.success).toBe(true)
        expect(data.matchScore).toBe(75)
        expect(mockJobMatchingService.calculateMatchScore).toHaveBeenCalledWith('user-1', 'job-1')
      })

      it('should return 404 for non-existent job', async () => {
        mockJobMatchingService.searchJobs.mockResolvedValue([])

        const request = new NextRequest('http://localhost/api/job-matching/jobs', {
          method: 'POST',
          body: JSON.stringify({
            action: 'analyze',
            jobId: 'non-existent'
          })
        })

        const response = await JobsPOST(request)

        expect(response.status).toBe(404)
      })
    })

    describe('PUT /api/job-matching/jobs', () => {
      it('should update recommendation status', async () => {
        mockJobMatchingService.updateRecommendationStatus.mockResolvedValue(true)

        const request = new NextRequest('http://localhost/api/job-matching/jobs', {
          method: 'PUT',
          body: JSON.stringify({
            action: 'update-recommendation',
            recommendationId: 'rec-1',
            status: 'save'
          })
        })

        const response = await JobsPUT(request)
        const data = await response.json()

        expect(response.status).toBe(200)
        expect(data.success).toBe(true)
        expect(mockJobMatchingService.updateRecommendationStatus).toHaveBeenCalledWith('rec-1', 'user-1', 'save')
      })

      it('should mark recommendation as viewed', async () => {
        mockJobMatchingService.markRecommendationViewed.mockResolvedValue(true)

        const request = new NextRequest('http://localhost/api/job-matching/jobs', {
          method: 'PUT',
          body: JSON.stringify({
            action: 'mark-viewed',
            recommendationId: 'rec-1'
          })
        })

        const response = await JobsPUT(request)
        const data = await response.json()

        expect(response.status).toBe(200)
        expect(data.success).toBe(true)
        expect(mockJobMatchingService.markRecommendationViewed).toHaveBeenCalledWith('rec-1', 'user-1')
      })

      it('should return 404 for non-existent recommendation', async () => {
        mockJobMatchingService.updateRecommendationStatus.mockResolvedValue(false)

        const request = new NextRequest('http://localhost/api/job-matching/jobs', {
          method: 'PUT',
          body: JSON.stringify({
            action: 'update-recommendation',
            recommendationId: 'non-existent',
            status: 'save'
          })
        })

        const response = await JobsPUT(request)

        expect(response.status).toBe(404)
      })
    })
  })

  describe('Applications API', () => {
    describe('POST /api/job-matching/applications', () => {
      it('should create job application', async () => {
        const mockApplication = {
          id: 'app-1',
          userId: 'user-1',
          jobPostingId: 'job-1',
          status: 'applied',
          appliedDate: new Date(),
          jobPosting: {
            id: 'job-1',
            title: 'Software Developer',
            company: 'Tech Corp'
          }
        }

        mockJobMatchingService.applyToJob.mockResolvedValue(mockApplication)

        const request = new NextRequest('http://localhost/api/job-matching/applications', {
          method: 'POST',
          body: JSON.stringify({
            action: 'apply',
            jobPostingId: 'job-1',
            resumeId: 'resume-1',
            notes: 'Excited about this opportunity'
          })
        })

        const response = await ApplicationsPOST(request)
        const data = await response.json()

        expect(response.status).toBe(200)
        expect(data.success).toBe(true)
        expect(data.application).toEqual(mockApplication)
        expect(mockJobMatchingService.applyToJob).toHaveBeenCalledWith({
          userId: 'user-1',
          jobPostingId: 'job-1',
          resumeId: 'resume-1',
          coverLetterId: undefined,
          notes: 'Excited about this opportunity',
          customFields: undefined
        })
      })

      it('should handle bulk applications', async () => {
        const mockApplications = [
          { id: 'app-1', jobPostingId: 'job-1' },
          { id: 'app-2', jobPostingId: 'job-2' }
        ]

        mockJobMatchingService.applyToJob
          .mockResolvedValueOnce(mockApplications[0])
          .mockResolvedValueOnce(mockApplications[1])

        const request = new NextRequest('http://localhost/api/job-matching/applications', {
          method: 'POST',
          body: JSON.stringify({
            action: 'bulk-apply',
            jobIds: ['job-1', 'job-2'],
            resumeId: 'resume-1'
          })
        })

        const response = await ApplicationsPOST(request)
        const data = await response.json()

        expect(response.status).toBe(200)
        expect(data.success).toBe(true)
        expect(data.applications).toHaveLength(2)
        expect(data.errors).toHaveLength(0)
      })

      it('should handle application errors in bulk apply', async () => {
        mockJobMatchingService.applyToJob
          .mockResolvedValueOnce({ id: 'app-1' })
          .mockRejectedValueOnce(new Error('Already applied'))

        const request = new NextRequest('http://localhost/api/job-matching/applications', {
          method: 'POST',
          body: JSON.stringify({
            action: 'bulk-apply',
            jobIds: ['job-1', 'job-2']
          })
        })

        const response = await ApplicationsPOST(request)
        const data = await response.json()

        expect(response.status).toBe(200)
        expect(data.applications).toHaveLength(1)
        expect(data.errors).toHaveLength(1)
        expect(data.errors[0].jobId).toBe('job-2')
      })
    })

    describe('GET /api/job-matching/applications', () => {
      it('should list user applications', async () => {
        const mockApplications = [
          {
            id: 'app-1',
            userId: 'user-1',
            status: 'applied',
            appliedDate: new Date(),
            jobPosting: {
              title: 'Software Developer',
              company: 'Tech Corp'
            }
          }
        ]

        mockJobMatchingService.getApplications.mockResolvedValue(mockApplications)

        const request = new NextRequest('http://localhost/api/job-matching/applications?action=list&status=applied&limit=10')

        const response = await ApplicationsGET(request)
        const data = await response.json()

        expect(response.status).toBe(200)
        expect(data.success).toBe(true)
        expect(data.applications).toEqual(mockApplications)
        expect(mockJobMatchingService.getApplications).toHaveBeenCalledWith('user-1', {
          status: 'applied',
          limit: 10
        })
      })

      it('should get application statistics', async () => {
        const mockStats = {
          total: 10,
          applied: 5,
          screening: 2,
          interview: 2,
          offer: 1,
          rejected: 0,
          withdrawn: 0,
          responseRate: 50,
          averageResponseTime: 7
        }

        mockJobMatchingService.getApplications.mockResolvedValue([
          { status: 'applied' },
          { status: 'applied' },
          { status: 'screening' },
          { status: 'interview' },
          { status: 'offer' }
        ])

        const request = new NextRequest('http://localhost/api/job-matching/applications?action=stats')

        const response = await ApplicationsGET(request)
        const data = await response.json()

        expect(response.status).toBe(200)
        expect(data.success).toBe(true)
        expect(data.stats).toBeDefined()
        expect(data.stats.total).toBe(5)
        expect(data.stats.applied).toBe(2)
      })
    })

    describe('PUT /api/job-matching/applications', () => {
      it('should update application status', async () => {
        mockJobMatchingService.updateApplicationStatus.mockResolvedValue(true)

        const request = new NextRequest('http://localhost/api/job-matching/applications', {
          method: 'PUT',
          body: JSON.stringify({
            action: 'update-status',
            applicationId: 'app-1',
            status: 'interview'
          })
        })

        const response = await ApplicationsPUT(request)
        const data = await response.json()

        expect(response.status).toBe(200)
        expect(data.success).toBe(true)
        expect(mockJobMatchingService.updateApplicationStatus).toHaveBeenCalledWith('app-1', 'interview', 'user-1')
      })

      it('should return 404 for non-existent application', async () => {
        mockJobMatchingService.updateApplicationStatus.mockResolvedValue(false)

        const request = new NextRequest('http://localhost/api/job-matching/applications', {
          method: 'PUT',
          body: JSON.stringify({
            action: 'update-status',
            applicationId: 'non-existent',
            status: 'interview'
          })
        })

        const response = await ApplicationsPUT(request)

        expect(response.status).toBe(404)
      })
    })

    describe('DELETE /api/job-matching/applications', () => {
      it('should withdraw application', async () => {
        mockJobMatchingService.updateApplicationStatus.mockResolvedValue(true)

        const request = new NextRequest('http://localhost/api/job-matching/applications?applicationId=app-1')

        const response = await ApplicationsDELETE(request)
        const data = await response.json()

        expect(response.status).toBe(200)
        expect(data.success).toBe(true)
        expect(mockJobMatchingService.updateApplicationStatus).toHaveBeenCalledWith('app-1', 'withdrawn', 'user-1')
      })
    })
  })

  describe('Preferences API', () => {
    describe('GET /api/job-matching/preferences', () => {
      it('should get user job preferences', async () => {
        const mockPreferences = {
          id: 'pref-1',
          userId: 'user-1',
          preferredTitles: JSON.stringify(['Software Developer']),
          preferredLocations: JSON.stringify(['San Francisco']),
          salaryMin: 80000,
          salaryMax: 120000
        }

        mockPrisma.userJobPreferences.findUnique.mockResolvedValue(mockPreferences)

        const request = new NextRequest('http://localhost/api/job-matching/preferences?action=get')

        const response = await PreferencesGET(request)
        const data = await response.json()

        expect(response.status).toBe(200)
        expect(data.success).toBe(true)
        expect(data.preferences.preferredTitles).toEqual(['Software Developer'])
        expect(data.preferences.preferredLocations).toEqual(['San Francisco'])
      })

      it('should return null for non-existent preferences', async () => {
        mockPrisma.userJobPreferences.findUnique.mockResolvedValue(null)

        const request = new NextRequest('http://localhost/api/job-matching/preferences')

        const response = await PreferencesGET(request)
        const data = await response.json()

        expect(response.status).toBe(200)
        expect(data.success).toBe(true)
        expect(data.preferences).toBeNull()
      })

      it('should get career path predictions', async () => {
        const mockCareerPath = {
          nextRoles: [
            {
              title: 'Senior Software Developer',
              probability: 0.8,
              timeframe: '1-2 years',
              requiredSkills: ['leadership'],
              salaryRange: { min: 100000, max: 140000 }
            }
          ],
          skillGaps: [],
          marketTrends: []
        }

        mockAIEngine.predictCareerPath.mockResolvedValue(mockCareerPath)

        const request = new NextRequest('http://localhost/api/job-matching/preferences?action=career-path')

        const response = await PreferencesGET(request)
        const data = await response.json()

        expect(response.status).toBe(200)
        expect(data.success).toBe(true)
        expect(data.careerPath).toEqual(mockCareerPath)
      })
    })

    describe('POST /api/job-matching/preferences', () => {
      it('should update job preferences', async () => {
        mockJobMatchingService.updateJobPreferences.mockResolvedValue(true)

        const request = new NextRequest('http://localhost/api/job-matching/preferences', {
          method: 'POST',
          body: JSON.stringify({
            preferredTitles: ['Software Developer', 'Frontend Developer'],
            preferredLocations: ['San Francisco', 'Remote'],
            salaryMin: 90000,
            salaryMax: 130000
          })
        })

        const response = await PreferencesPOST(request)
        const data = await response.json()

        expect(response.status).toBe(200)
        expect(data.success).toBe(true)
        expect(mockJobMatchingService.updateJobPreferences).toHaveBeenCalledWith({
          userId: 'user-1',
          preferredTitles: ['Software Developer', 'Frontend Developer'],
          preferredLocations: ['San Francisco', 'Remote'],
          salaryMin: 90000,
          salaryMax: 130000
        })
      })

      it('should analyze resume', async () => {
        const mockAnalysis = {
          skills: [
            { name: 'JavaScript', category: 'programming', confidence: 0.9 }
          ],
          experience: [],
          education: []
        }

        mockAIEngine.extractSkillsFromResume.mockResolvedValue(mockAnalysis)

        const request = new NextRequest('http://localhost/api/job-matching/preferences', {
          method: 'POST',
          body: JSON.stringify({
            action: 'analyze-resume',
            resumeId: 'resume-1'
          })
        })

        const response = await PreferencesPOST(request)
        const data = await response.json()

        expect(response.status).toBe(200)
        expect(data.success).toBe(true)
        expect(data.analysis).toEqual(mockAnalysis)
      })
    })
  })

  describe('Error Handling', () => {
    it('should handle service errors', async () => {
      mockJobMatchingService.searchJobs.mockRejectedValue(new Error('Service error'))

      const request = new NextRequest('http://localhost/api/job-matching/jobs?action=search')

      const response = await JobsGET(request)

      expect(response.status).toBe(500)
    })

    it('should handle authentication errors', async () => {
      const { getServerSession } = require('next-auth')
      getServerSession.mockResolvedValue(null)

      const request = new NextRequest('http://localhost/api/job-matching/jobs?action=search')

      const response = await JobsGET(request)
      const data = await response.json()

      expect(response.status).toBe(401)
      expect(data.error).toBe('Unauthorized')
    })

    it('should handle validation errors', async () => {
      const request = new NextRequest('http://localhost/api/job-matching/applications', {
        method: 'PUT',
        body: JSON.stringify({
          action: 'update-status',
          applicationId: 'app-1',
          status: 'invalid-status'
        })
      })

      const response = await ApplicationsPUT(request)

      expect(response.status).toBe(400)
    })
  })
})
