/**
 * Version Control API - Versions
 * 
 * Handles resume version management operations
 */

import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { versionControlService, CreateVersionSchema, RollbackOptionsSchema } from '@/lib/version-control/service'
import { z } from 'zod'

// Request schemas
const GetVersionsSchema = z.object({
  resumeId: z.string(),
  limit: z.coerce.number().optional().default(50)
})

const CompareVersionsSchema = z.object({
  resumeId: z.string(),
  fromVersion: z.coerce.number(),
  toVersion: z.coerce.number()
})

const RollbackSchema = z.object({
  resumeId: z.string(),
  versionId: z.string(),
  options: RollbackOptionsSchema.optional()
})

const PreviewRollbackSchema = z.object({
  resumeId: z.string(),
  versionId: z.string()
})

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const action = body.action

    if (action === 'create') {
      const { resumeId, versionName, changeType, changeSummary, metadata } = CreateVersionSchema.parse(body)
      
      const version = await versionControlService.createVersion({
        resumeId,
        userId: session.user.id,
        versionName,
        changeType,
        changeSummary,
        metadata
      })

      return NextResponse.json({
        success: true,
        version,
        message: 'Version created successfully'
      })
    }

    if (action === 'rollback') {
      const { resumeId, versionId, options } = RollbackSchema.parse(body)
      
      const success = await versionControlService.rollbackToVersion(
        resumeId,
        versionId,
        session.user.id,
        options
      )

      if (!success) {
        return NextResponse.json(
          { error: 'Rollback failed' },
          { status: 500 }
        )
      }

      return NextResponse.json({
        success: true,
        message: 'Rollback completed successfully'
      })
    }

    if (action === 'preview-rollback') {
      const { resumeId, versionId } = PreviewRollbackSchema.parse(body)
      
      const diff = await versionControlService.previewRollback(
        resumeId,
        versionId,
        session.user.id
      )

      return NextResponse.json({
        success: true,
        diff
      })
    }

    if (action === 'compare') {
      const { resumeId, fromVersion, toVersion } = CompareVersionsSchema.parse(body)
      
      const diff = await versionControlService.compareVersions(
        resumeId,
        fromVersion,
        toVersion,
        session.user.id
      )

      return NextResponse.json({
        success: true,
        diff
      })
    }

    return NextResponse.json(
      { error: 'Invalid action' },
      { status: 400 }
    )
  } catch (error) {
    console.error('Version control error:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const action = searchParams.get('action')
    const versionId = searchParams.get('versionId')

    if (action === 'list') {
      const resumeId = searchParams.get('resumeId')
      const limit = parseInt(searchParams.get('limit') || '50')

      if (!resumeId) {
        return NextResponse.json(
          { error: 'Resume ID required' },
          { status: 400 }
        )
      }

      const versions = await versionControlService.getVersions(
        resumeId,
        session.user.id,
        limit
      )

      return NextResponse.json({ versions })
    }

    if (action === 'get' && versionId) {
      const version = await versionControlService.getVersion(
        versionId,
        session.user.id
      )

      if (!version) {
        return NextResponse.json(
          { error: 'Version not found' },
          { status: 404 }
        )
      }

      return NextResponse.json({ version })
    }

    if (action === 'activities') {
      const resumeId = searchParams.get('resumeId')
      const limit = parseInt(searchParams.get('limit') || '50')

      if (!resumeId) {
        return NextResponse.json(
          { error: 'Resume ID required' },
          { status: 400 }
        )
      }

      const activities = await versionControlService.getVersionActivities(
        resumeId,
        session.user.id,
        limit
      )

      return NextResponse.json({ activities })
    }

    return NextResponse.json(
      { error: 'Invalid action or missing parameters' },
      { status: 400 }
    )
  } catch (error) {
    console.error('Version control get error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const action = searchParams.get('action')
    const resumeId = searchParams.get('resumeId')

    if (action === 'cleanup' && resumeId) {
      const keepCount = parseInt(searchParams.get('keepCount') || '50')
      
      const deletedCount = await versionControlService.cleanupOldVersions(
        resumeId,
        keepCount
      )

      return NextResponse.json({
        success: true,
        deletedCount,
        message: `Cleaned up ${deletedCount} old versions`
      })
    }

    return NextResponse.json(
      { error: 'Invalid action or missing parameters' },
      { status: 400 }
    )
  } catch (error) {
    console.error('Version control delete error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
