/**
 * Template System Types and Interfaces
 * 
 * This file contains all the TypeScript types and interfaces
 * for the resume template system.
 */

export interface TemplateStyle {
  // Typography
  fontFamily: string;
  fontSize: {
    heading1: number;
    heading2: number;
    heading3: number;
    body: number;
    small: number;
  };
  fontWeight: {
    normal: number;
    medium: number;
    semibold: number;
    bold: number;
  };
  lineHeight: {
    tight: number;
    normal: number;
    relaxed: number;
  };

  // Colors
  colors: {
    primary: string;
    secondary: string;
    accent: string;
    text: string;
    textLight: string;
    background: string;
    border: string;
    divider: string;
  };

  // Spacing
  spacing: {
    xs: number;
    sm: number;
    md: number;
    lg: number;
    xl: number;
    xxl: number;
  };

  // Layout
  layout: {
    pageMargin: {
      top: number;
      right: number;
      bottom: number;
      left: number;
    };
    sectionSpacing: number;
    itemSpacing: number;
    columnGap: number;
  };

  // Components
  components: {
    header: {
      alignment: 'left' | 'center' | 'right';
      showDivider: boolean;
      backgroundColor?: string;
    };
    section: {
      titleStyle: 'underline' | 'background' | 'border' | 'plain';
      titleCase: 'uppercase' | 'lowercase' | 'capitalize' | 'normal';
      spacing: 'compact' | 'normal' | 'spacious';
    };
    list: {
      bulletStyle: 'bullet' | 'dash' | 'arrow' | 'none';
      indentation: number;
    };
    contact: {
      layout: 'horizontal' | 'vertical' | 'grid';
      showIcons: boolean;
      separator: string;
    };
  };
}

export interface TemplateLayout {
  type: 'single-column' | 'two-column' | 'three-column' | 'sidebar';
  columns: TemplateColumn[];
  header: TemplateHeaderConfig;
  footer?: TemplateFooterConfig;
}

export interface TemplateColumn {
  id: string;
  width: number; // Percentage or fixed width
  sections: string[]; // Section IDs that belong to this column
  order: number;
}

export interface TemplateHeaderConfig {
  height: number;
  sections: string[];
  style: 'minimal' | 'prominent' | 'creative';
}

export interface TemplateFooterConfig {
  height: number;
  content: string;
  style: 'minimal' | 'detailed';
}

export interface Template {
  id: string;
  name: string;
  description: string;
  category: TemplateCategory;
  tags: string[];
  
  // Visual properties
  previewImage: string;
  thumbnailImage: string;
  
  // Metadata
  isPremium: boolean;
  isPopular: boolean;
  isNew: boolean;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  
  // Template configuration
  style: TemplateStyle;
  layout: TemplateLayout;
  supportedSections: string[];
  requiredSections: string[];
  
  // Customization options
  customizable: {
    colors: boolean;
    fonts: boolean;
    layout: boolean;
    spacing: boolean;
  };
  
  // Usage stats
  usageCount: number;
  rating: number;
  reviewCount: number;
  
  // Timestamps
  createdAt: string;
  updatedAt: string;
  
  // Creator info
  createdBy: string;
  isOfficial: boolean;
}

export enum TemplateCategory {
  MODERN = 'modern',
  CLASSIC = 'classic',
  CREATIVE = 'creative',
  MINIMAL = 'minimal',
  PROFESSIONAL = 'professional',
  ACADEMIC = 'academic',
  TECHNICAL = 'technical',
  EXECUTIVE = 'executive',
  ENTRY_LEVEL = 'entry_level',
  INDUSTRY_SPECIFIC = 'industry_specific',
}

export interface TemplateCustomization {
  templateId: string;
  userId: string;
  customizations: {
    style?: Partial<TemplateStyle>;
    layout?: Partial<TemplateLayout>;
    sectionOrder?: string[];
    hiddenSections?: string[];
  };
  name?: string; // Custom name for the customization
  isDefault: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface TemplatePreset {
  id: string;
  name: string;
  description: string;
  templateId: string;
  customizations: TemplateCustomization['customizations'];
  previewImage: string;
  isOfficial: boolean;
  usageCount: number;
}

// Font options
export interface FontOption {
  id: string;
  name: string;
  family: string;
  category: 'serif' | 'sans-serif' | 'monospace' | 'display';
  weights: number[];
  googleFont: boolean;
  previewText: string;
}

// Color scheme options
export interface ColorScheme {
  id: string;
  name: string;
  description: string;
  colors: TemplateStyle['colors'];
  category: 'professional' | 'creative' | 'modern' | 'classic';
  previewImage?: string;
}

// Export options
export interface ExportOptions {
  format: 'pdf' | 'docx' | 'html' | 'png' | 'jpg';
  quality: 'draft' | 'standard' | 'high' | 'print';
  pageSize: 'a4' | 'letter' | 'legal';
  orientation: 'portrait' | 'landscape';
  margins: {
    top: number;
    right: number;
    bottom: number;
    left: number;
  };
  includeMetadata: boolean;
  watermark?: {
    text: string;
    opacity: number;
    position: 'center' | 'corner';
  };
}

export interface ExportJob {
  id: string;
  userId: string;
  resumeId: string;
  templateId: string;
  options: ExportOptions;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  progress: number;
  downloadUrl?: string;
  error?: string;
  createdAt: string;
  completedAt?: string;
  expiresAt?: string;
}

// Template marketplace
export interface TemplateReview {
  id: string;
  templateId: string;
  userId: string;
  rating: number;
  comment: string;
  helpful: number;
  createdAt: string;
  updatedAt: string;
}

export interface TemplateCollection {
  id: string;
  name: string;
  description: string;
  templateIds: string[];
  category: string;
  isOfficial: boolean;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

// Template analytics
export interface TemplateAnalytics {
  templateId: string;
  period: 'day' | 'week' | 'month' | 'year';
  metrics: {
    views: number;
    uses: number;
    downloads: number;
    customizations: number;
    averageRating: number;
    conversionRate: number;
  };
  demographics: {
    industries: Record<string, number>;
    experienceLevels: Record<string, number>;
    locations: Record<string, number>;
  };
  date: string;
}

// Template builder (for creating custom templates)
export interface TemplateBuilder {
  id: string;
  name: string;
  description: string;
  baseTemplateId?: string;
  style: TemplateStyle;
  layout: TemplateLayout;
  status: 'draft' | 'published' | 'archived';
  version: number;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

// API response types
export interface TemplateListResponse {
  templates: Template[];
  total: number;
  page: number;
  limit: number;
  filters: {
    category?: TemplateCategory;
    isPremium?: boolean;
    tags?: string[];
  };
}

export interface TemplateResponse {
  template: Template;
  customizations?: TemplateCustomization[];
  analytics?: TemplateAnalytics;
}

// Utility types
export type TemplateFormData = Omit<Template, 'id' | 'createdAt' | 'updatedAt' | 'usageCount' | 'rating' | 'reviewCount'>;
export type TemplateUpdate = Partial<Pick<Template, 'name' | 'description' | 'tags' | 'style' | 'layout' | 'customizable'>>;
export type CustomizationFormData = Omit<TemplateCustomization, 'id' | 'userId' | 'createdAt' | 'updatedAt'>;

// Template validation types
export interface TemplateValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  suggestions: string[];
}

// Template rendering context
export interface TemplateRenderContext {
  template: Template;
  customization?: TemplateCustomization;
  resume: any; // Resume data
  options: {
    preview: boolean;
    export: boolean;
    interactive: boolean;
  };
}
