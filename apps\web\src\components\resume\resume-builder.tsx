'use client';

import { useState, useCallback, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { Resume, ResumeSectionType, WorkExperience, Education } from '@careercraft/shared/types/resume';
import { PersonalInfoInput, WorkExperienceInput } from '@careercraft/shared/schemas/resume';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { Icons } from '@/components/ui/icons';
import { LoadingSpinner } from '@/components/ui/loading';
import { PersonalInfoForm } from './personal-info-form';
import { WorkExperienceForm } from './work-experience-form';
import { ResumePreview } from './resume-preview';
import { ResumeSectionManager } from './resume-section-manager';
import { useResumeBuilder } from '@/hooks/use-resume-builder';
import { cn } from '@/lib/utils';

interface ResumeBuilderProps {
  resumeId?: string;
  templateId?: string;
  className?: string;
}

export function ResumeBuilder({ resumeId, templateId, className }: ResumeBuilderProps) {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('personal-info');
  const [isPreviewMode, setIsPreviewMode] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  const {
    resume,
    isLoading,
    error,
    updatePersonalInfo,
    addWorkExperience,
    updateWorkExperience,
    removeWorkExperience,
    addEducation,
    updateEducation,
    removeEducation,
    saveResume,
    loadResume,
  } = useResumeBuilder(resumeId, templateId);

  useEffect(() => {
    if (resumeId) {
      loadResume(resumeId);
    }
  }, [resumeId, loadResume]);

  const handlePersonalInfoSubmit = useCallback(async (data: PersonalInfoInput) => {
    try {
      setIsSaving(true);
      await updatePersonalInfo(data);
      toast.success('Personal information updated successfully');
      setActiveTab('work-experience');
    } catch (error) {
      toast.error('Failed to update personal information');
      console.error('Personal info update error:', error);
    } finally {
      setIsSaving(false);
    }
  }, [updatePersonalInfo]);

  const handleWorkExperienceSubmit = useCallback(async (data: WorkExperienceInput) => {
    try {
      setIsSaving(true);
      if (data.id && resume?.sections.find(s => s.type === ResumeSectionType.WORK_EXPERIENCE)?.data.some((exp: WorkExperience) => exp.id === data.id)) {
        await updateWorkExperience(data.id, data);
        toast.success('Work experience updated successfully');
      } else {
        await addWorkExperience(data);
        toast.success('Work experience added successfully');
      }
    } catch (error) {
      toast.error('Failed to save work experience');
      console.error('Work experience save error:', error);
    } finally {
      setIsSaving(false);
    }
  }, [addWorkExperience, updateWorkExperience, resume]);

  const handleSaveResume = useCallback(async () => {
    if (!resume) return;

    try {
      setIsSaving(true);
      const savedResume = await saveResume();
      toast.success('Resume saved successfully');
      
      if (!resumeId && savedResume?.id) {
        router.push(`/dashboard/resumes/${savedResume.id}/edit`);
      }
    } catch (error) {
      toast.error('Failed to save resume');
      console.error('Resume save error:', error);
    } finally {
      setIsSaving(false);
    }
  }, [resume, saveResume, resumeId, router]);

  const handlePreviewToggle = useCallback(() => {
    setIsPreviewMode(!isPreviewMode);
  }, [isPreviewMode]);

  const getCompletionPercentage = useCallback(() => {
    if (!resume) return 0;

    let completed = 0;
    let total = 0;

    // Personal info (required)
    total += 5;
    if (resume.personalInfo.firstName) completed++;
    if (resume.personalInfo.lastName) completed++;
    if (resume.personalInfo.email) completed++;
    if (resume.personalInfo.phone) completed++;
    if (resume.personalInfo.location) completed++;

    // Work experience (at least one)
    total += 1;
    const workSection = resume.sections.find(s => s.type === ResumeSectionType.WORK_EXPERIENCE);
    if (workSection?.data && workSection.data.length > 0) completed++;

    // Education (at least one)
    total += 1;
    const educationSection = resume.sections.find(s => s.type === ResumeSectionType.EDUCATION);
    if (educationSection?.data && educationSection.data.length > 0) completed++;

    // Skills
    total += 1;
    const skillsSection = resume.sections.find(s => s.type === ResumeSectionType.SKILLS);
    if (skillsSection?.data && skillsSection.data.length > 0) completed++;

    return Math.round((completed / total) * 100);
  }, [resume]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (error) {
    return (
      <Card className="max-w-md mx-auto">
        <CardHeader>
          <CardTitle className="text-destructive">Error Loading Resume</CardTitle>
          <CardDescription>{error}</CardDescription>
        </CardHeader>
        <CardContent>
          <Button onClick={() => window.location.reload()}>
            Try Again
          </Button>
        </CardContent>
      </Card>
    );
  }

  if (!resume) {
    return (
      <Card className="max-w-md mx-auto">
        <CardHeader>
          <CardTitle>Resume Not Found</CardTitle>
          <CardDescription>
            The resume you're looking for doesn't exist or you don't have permission to view it.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Button onClick={() => router.push('/dashboard/resumes')}>
            Back to Resumes
          </Button>
        </CardContent>
      </Card>
    );
  }

  const completionPercentage = getCompletionPercentage();

  return (
    <div className={cn('flex flex-col lg:flex-row gap-6 h-full', className)}>
      {/* Builder Panel */}
      <div className={cn(
        'flex-1 min-w-0',
        isPreviewMode && 'hidden lg:block lg:w-1/2'
      )}>
        <Card className="h-full">
          <CardHeader className="pb-4">
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>Resume Builder</CardTitle>
                <CardDescription>
                  Complete your resume step by step
                </CardDescription>
              </div>
              <div className="flex items-center gap-2">
                <div className="text-sm text-muted-foreground">
                  {completionPercentage}% complete
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handlePreviewToggle}
                  className="lg:hidden"
                >
                  {isPreviewMode ? 'Edit' : 'Preview'}
                </Button>
              </div>
            </div>
            
            {/* Progress Bar */}
            <div className="w-full bg-secondary rounded-full h-2">
              <div
                className="bg-primary h-2 rounded-full transition-all duration-300"
                style={{ width: `${completionPercentage}%` }}
              />
            </div>
          </CardHeader>
          
          <CardContent className="p-0">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full">
              <div className="px-6 pb-4">
                <TabsList className="grid w-full grid-cols-4">
                  <TabsTrigger value="personal-info" className="text-xs">
                    Personal
                  </TabsTrigger>
                  <TabsTrigger value="work-experience" className="text-xs">
                    Experience
                  </TabsTrigger>
                  <TabsTrigger value="education" className="text-xs">
                    Education
                  </TabsTrigger>
                  <TabsTrigger value="sections" className="text-xs">
                    Sections
                  </TabsTrigger>
                </TabsList>
              </div>

              <ScrollArea className="h-[calc(100vh-300px)]">
                <div className="px-6 pb-6">
                  <TabsContent value="personal-info" className="mt-0">
                    <PersonalInfoForm
                      initialData={resume.personalInfo}
                      onSubmit={handlePersonalInfoSubmit}
                      isLoading={isSaving}
                    />
                  </TabsContent>

                  <TabsContent value="work-experience" className="mt-0">
                    <WorkExperienceForm
                      onSubmit={handleWorkExperienceSubmit}
                      isLoading={isSaving}
                    />
                  </TabsContent>

                  <TabsContent value="education" className="mt-0">
                    <div className="text-center py-8">
                      <Icons.graduationCap className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                      <h3 className="text-lg font-medium mb-2">Education Section</h3>
                      <p className="text-muted-foreground mb-4">
                        Add your educational background
                      </p>
                      <Button variant="outline">
                        Coming Soon
                      </Button>
                    </div>
                  </TabsContent>

                  <TabsContent value="sections" className="mt-0">
                    <ResumeSectionManager
                      sections={resume.sections}
                      onSectionUpdate={() => {}}
                      isLoading={isSaving}
                    />
                  </TabsContent>
                </div>
              </ScrollArea>
            </Tabs>
          </CardContent>
        </Card>
      </div>

      {/* Preview Panel */}
      <div className={cn(
        'flex-1 min-w-0',
        !isPreviewMode && 'hidden lg:block lg:w-1/2'
      )}>
        <Card className="h-full">
          <CardHeader className="pb-4">
            <div className="flex items-center justify-between">
              <CardTitle>Live Preview</CardTitle>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleSaveResume}
                  disabled={isSaving}
                >
                  {isSaving && <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />}
                  Save
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handlePreviewToggle}
                  className="lg:hidden"
                >
                  Edit
                </Button>
              </div>
            </div>
          </CardHeader>
          
          <CardContent className="p-0">
            <ScrollArea className="h-[calc(100vh-200px)]">
              <div className="p-6">
                <ResumePreview resume={resume} />
              </div>
            </ScrollArea>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
