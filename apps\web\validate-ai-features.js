/**
 * AI Features Validation Script
 * 
 * Manual validation of AI features without running full test suite
 * This script checks the implementation and provides a comprehensive report
 */

const fs = require('fs')
const path = require('path')

class AIFeaturesValidator {
  constructor() {
    this.results = {
      files: { checked: 0, missing: 0, present: 0 },
      components: { checked: 0, missing: 0, present: 0 },
      apis: { checked: 0, missing: 0, present: 0 },
      tests: { checked: 0, missing: 0, present: 0 },
      config: { checked: 0, missing: 0, present: 0 }
    }
  }

  log(message, type = 'info') {
    const colors = {
      info: '\x1b[36m',    // Cyan
      success: '\x1b[32m', // Green
      error: '\x1b[31m',   // Red
      warning: '\x1b[33m', // Yellow
      reset: '\x1b[0m'     // Reset
    }
    
    console.log(`${colors[type]}${message}${colors.reset}`)
  }

  checkFileExists(filePath, description) {
    const fullPath = path.join(__dirname, filePath)
    const exists = fs.existsSync(fullPath)
    
    if (exists) {
      this.log(`✅ ${description}: Found`, 'success')
      return true
    } else {
      this.log(`❌ ${description}: Missing (${filePath})`, 'error')
      return false
    }
  }

  checkFileContent(filePath, searchTerms, description) {
    const fullPath = path.join(__dirname, filePath)
    
    if (!fs.existsSync(fullPath)) {
      this.log(`❌ ${description}: File not found`, 'error')
      return false
    }

    try {
      const content = fs.readFileSync(fullPath, 'utf8')
      const missingTerms = searchTerms.filter(term => !content.includes(term))
      
      if (missingTerms.length === 0) {
        this.log(`✅ ${description}: All required content found`, 'success')
        return true
      } else {
        this.log(`⚠️  ${description}: Missing content: ${missingTerms.join(', ')}`, 'warning')
        return false
      }
    } catch (error) {
      this.log(`❌ ${description}: Error reading file - ${error.message}`, 'error')
      return false
    }
  }

  validateAIServices() {
    this.log('\n🤖 Validating AI Services...', 'info')
    
    const aiServiceChecks = [
      {
        file: 'src/lib/ai/openai.ts',
        description: 'OpenAI Service Implementation',
        requiredContent: ['OpenAI', 'generateContent', 'analyzeATSCompatibility', 'optimizeResumeContent']
      },
      {
        file: 'src/app/api/ai/generate/route.ts',
        description: 'Content Generation API',
        requiredContent: ['POST', 'generateContent', 'NextRequest', 'NextResponse']
      },
      {
        file: 'src/app/api/ai/analyze/route.ts',
        description: 'ATS Analysis API',
        requiredContent: ['POST', 'analyzeATSCompatibility', 'NextRequest', 'NextResponse']
      },
      {
        file: 'src/app/api/ai/optimize/route.ts',
        description: 'Content Optimization API',
        requiredContent: ['POST', 'optimizeResumeContent', 'NextRequest', 'NextResponse']
      }
    ]

    let passed = 0
    let total = aiServiceChecks.length

    aiServiceChecks.forEach(check => {
      this.results.apis.checked++
      if (this.checkFileContent(check.file, check.requiredContent, check.description)) {
        passed++
        this.results.apis.present++
      } else {
        this.results.apis.missing++
      }
    })

    this.log(`📊 AI Services: ${passed}/${total} checks passed`, passed === total ? 'success' : 'warning')
    return passed === total
  }

  validateAIComponents() {
    this.log('\n⚛️  Validating AI Components...', 'info')
    
    const componentChecks = [
      {
        file: 'src/components/ai/AIAssistant.tsx',
        description: 'AI Assistant Component',
        requiredContent: ['AIAssistant', 'generateContent', 'analyzeATS', 'useState', 'useEffect']
      },
      {
        file: 'src/components/ai/AIAnalytics.tsx',
        description: 'AI Analytics Component',
        requiredContent: ['AIAnalytics', 'analytics', 'chart', 'metrics']
      },
      {
        file: 'src/components/ai/CoverLetterGenerator.tsx',
        description: 'Cover Letter Generator',
        requiredContent: ['CoverLetterGenerator', 'generateCoverLetter', 'jobDescription']
      },
      {
        file: 'src/components/ai/KeywordOptimizer.tsx',
        description: 'Keyword Optimizer',
        requiredContent: ['KeywordOptimizer', 'keywords', 'optimization', 'density']
      }
    ]

    let passed = 0
    let total = componentChecks.length

    componentChecks.forEach(check => {
      this.results.components.checked++
      if (this.checkFileContent(check.file, check.requiredContent, check.description)) {
        passed++
        this.results.components.present++
      } else {
        this.results.components.missing++
      }
    })

    this.log(`📊 AI Components: ${passed}/${total} checks passed`, passed === total ? 'success' : 'warning')
    return passed === total
  }

  validateTestFiles() {
    this.log('\n🧪 Validating Test Files...', 'info')
    
    const testChecks = [
      {
        file: 'src/test/ai/ai-services.test.ts',
        description: 'AI Services Unit Tests',
        requiredContent: ['describe', 'it', 'expect', 'AIService', 'generateContent']
      },
      {
        file: 'src/test/ai/ai-api.test.ts',
        description: 'AI API Integration Tests',
        requiredContent: ['describe', 'it', 'expect', 'NextRequest', 'POST']
      },
      {
        file: 'src/test/ai/ai-components.test.tsx',
        description: 'AI Component Tests',
        requiredContent: ['describe', 'it', 'expect', 'render', 'screen']
      },
      {
        file: 'e2e/ai-features.spec.ts',
        description: 'AI E2E Tests',
        requiredContent: ['test', 'expect', 'page', 'ai-assistant', 'generate']
      }
    ]

    let passed = 0
    let total = testChecks.length

    testChecks.forEach(check => {
      this.results.tests.checked++
      if (this.checkFileContent(check.file, check.requiredContent, check.description)) {
        passed++
        this.results.tests.present++
      } else {
        this.results.tests.missing++
      }
    })

    this.log(`📊 Test Files: ${passed}/${total} checks passed`, passed === total ? 'success' : 'warning')
    return passed === total
  }

  validateConfiguration() {
    this.log('\n⚙️  Validating Configuration...', 'info')
    
    const configChecks = [
      {
        file: 'package.json',
        description: 'Package.json AI Scripts',
        requiredContent: ['test:ai', 'test:ai:unit', 'test:ai:components', 'test:ai:e2e']
      },
      {
        file: '.env.example',
        description: 'Environment Variables',
        requiredContent: ['OPENAI_API_KEY', 'DATABASE_URL', 'NEXTAUTH_SECRET']
      },
      {
        file: 'vitest.config.ts',
        description: 'Vitest Configuration',
        requiredContent: ['vitest', 'defineConfig', 'test']
      },
      {
        file: 'playwright.config.ts',
        description: 'Playwright Configuration',
        requiredContent: ['playwright', 'defineConfig', 'testDir']
      }
    ]

    let passed = 0
    let total = configChecks.length

    configChecks.forEach(check => {
      this.results.config.checked++
      if (this.checkFileContent(check.file, check.requiredContent, check.description)) {
        passed++
        this.results.config.present++
      } else {
        this.results.config.missing++
      }
    })

    this.log(`📊 Configuration: ${passed}/${total} checks passed`, passed === total ? 'success' : 'warning')
    return passed === total
  }

  validateFileStructure() {
    this.log('\n📁 Validating File Structure...', 'info')
    
    const requiredFiles = [
      'src/lib/ai/openai.ts',
      'src/components/ai/AIAssistant.tsx',
      'src/components/ai/AIAnalytics.tsx',
      'src/components/ai/CoverLetterGenerator.tsx',
      'src/components/ai/KeywordOptimizer.tsx',
      'src/app/api/ai/generate/route.ts',
      'src/app/api/ai/analyze/route.ts',
      'src/app/api/ai/optimize/route.ts',
      'src/test/ai/ai-services.test.ts',
      'src/test/ai/ai-api.test.ts',
      'src/test/ai/ai-components.test.tsx',
      'e2e/ai-features.spec.ts',
      'scripts/test-ai-features.js'
    ]

    let present = 0
    let total = requiredFiles.length

    requiredFiles.forEach(file => {
      this.results.files.checked++
      if (this.checkFileExists(file, `File: ${file}`)) {
        present++
        this.results.files.present++
      } else {
        this.results.files.missing++
      }
    })

    this.log(`📊 File Structure: ${present}/${total} files present`, present === total ? 'success' : 'warning')
    return present === total
  }

  generateReport() {
    this.log('\n📊 AI Features Validation Report', 'info')
    this.log('=' .repeat(60), 'info')
    
    const categories = ['files', 'components', 'apis', 'tests', 'config']
    let totalChecked = 0
    let totalPresent = 0
    let totalMissing = 0

    categories.forEach(category => {
      const result = this.results[category]
      totalChecked += result.checked
      totalPresent += result.present
      totalMissing += result.missing

      const status = result.missing === 0 ? '✅' : '⚠️ '
      const categoryName = category.charAt(0).toUpperCase() + category.slice(1)
      
      this.log(
        `${status} ${categoryName}: ${result.present}/${result.checked} present`,
        result.missing === 0 ? 'success' : 'warning'
      )
    })

    this.log('=' .repeat(60), 'info')
    this.log(`📈 Overall: ${totalPresent}/${totalChecked} checks passed`, 
             totalMissing === 0 ? 'success' : 'warning')
    
    const completionRate = totalChecked > 0 ? (totalPresent / totalChecked * 100).toFixed(1) : 0
    this.log(`📊 Completion Rate: ${completionRate}%`, 
             completionRate >= 90 ? 'success' : completionRate >= 70 ? 'warning' : 'error')

    // Recommendations
    this.log('\n💡 Recommendations:', 'info')
    if (totalMissing === 0) {
      this.log('🎉 All AI features are properly implemented!', 'success')
      this.log('✨ Ready to proceed to next development phase', 'success')
    } else {
      this.log(`🔧 ${totalMissing} items need attention before proceeding`, 'warning')
      if (this.results.files.missing > 0) {
        this.log('📁 Create missing files and implement required functionality', 'warning')
      }
      if (this.results.tests.missing > 0) {
        this.log('🧪 Add comprehensive tests for AI features', 'warning')
      }
      if (this.results.config.missing > 0) {
        this.log('⚙️  Update configuration files for proper AI testing', 'warning')
      }
    }

    return totalMissing === 0
  }

  async validate() {
    this.log('🚀 Starting AI Features Validation...', 'info')
    this.log('📅 ' + new Date().toISOString(), 'info')
    
    const results = [
      this.validateFileStructure(),
      this.validateAIServices(),
      this.validateAIComponents(),
      this.validateTestFiles(),
      this.validateConfiguration()
    ]

    const allValid = this.generateReport()
    
    this.log('\n🏁 Validation Complete!', 'info')
    return allValid
  }
}

// Run validation if this script is executed directly
if (require.main === module) {
  const validator = new AIFeaturesValidator()
  validator.validate()
    .then(success => {
      process.exit(success ? 0 : 1)
    })
    .catch(error => {
      console.error('Validation failed:', error)
      process.exit(1)
    })
}

module.exports = AIFeaturesValidator
