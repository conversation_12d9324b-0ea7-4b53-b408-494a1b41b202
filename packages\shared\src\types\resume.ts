/**
 * Resume Data Types and Interfaces
 * 
 * This file contains all the TypeScript types and interfaces
 * for the resume builder system.
 */

export interface PersonalInfo {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  location: string;
  website?: string;
  linkedin?: string;
  github?: string;
  portfolio?: string;
  summary?: string;
}

export interface WorkExperience {
  id: string;
  company: string;
  position: string;
  location: string;
  startDate: string;
  endDate?: string;
  isCurrentRole: boolean;
  description: string;
  achievements: string[];
  technologies?: string[];
}

export interface Education {
  id: string;
  institution: string;
  degree: string;
  field: string;
  location: string;
  startDate: string;
  endDate?: string;
  isCurrentlyEnrolled: boolean;
  gpa?: string;
  honors?: string[];
  relevantCoursework?: string[];
}

export interface Project {
  id: string;
  name: string;
  description: string;
  technologies: string[];
  startDate: string;
  endDate?: string;
  isOngoing: boolean;
  url?: string;
  github?: string;
  highlights: string[];
}

export interface Skill {
  id: string;
  name: string;
  category: SkillCategory;
  level: SkillLevel;
  yearsOfExperience?: number;
}

export enum SkillCategory {
  TECHNICAL = 'technical',
  PROGRAMMING = 'programming',
  FRAMEWORK = 'framework',
  DATABASE = 'database',
  TOOL = 'tool',
  LANGUAGE = 'language',
  SOFT_SKILL = 'soft_skill',
  CERTIFICATION = 'certification',
}

export enum SkillLevel {
  BEGINNER = 'beginner',
  INTERMEDIATE = 'intermediate',
  ADVANCED = 'advanced',
  EXPERT = 'expert',
}

export interface Certification {
  id: string;
  name: string;
  issuer: string;
  issueDate: string;
  expirationDate?: string;
  credentialId?: string;
  url?: string;
}

export interface Language {
  id: string;
  name: string;
  proficiency: LanguageProficiency;
}

export enum LanguageProficiency {
  ELEMENTARY = 'elementary',
  LIMITED_WORKING = 'limited_working',
  PROFESSIONAL_WORKING = 'professional_working',
  FULL_PROFESSIONAL = 'full_professional',
  NATIVE = 'native',
}

export interface Award {
  id: string;
  title: string;
  issuer: string;
  date: string;
  description?: string;
}

export interface Publication {
  id: string;
  title: string;
  publisher: string;
  date: string;
  url?: string;
  description?: string;
}

export interface VolunteerExperience {
  id: string;
  organization: string;
  role: string;
  location: string;
  startDate: string;
  endDate?: string;
  isOngoing: boolean;
  description: string;
  achievements: string[];
}

export interface ResumeSection {
  id: string;
  type: ResumeSectionType;
  title: string;
  isVisible: boolean;
  order: number;
  data: any; // Will be typed based on section type
}

export enum ResumeSectionType {
  PERSONAL_INFO = 'personal_info',
  SUMMARY = 'summary',
  WORK_EXPERIENCE = 'work_experience',
  EDUCATION = 'education',
  PROJECTS = 'projects',
  SKILLS = 'skills',
  CERTIFICATIONS = 'certifications',
  LANGUAGES = 'languages',
  AWARDS = 'awards',
  PUBLICATIONS = 'publications',
  VOLUNTEER = 'volunteer',
  CUSTOM = 'custom',
}

export interface ResumeTemplate {
  id: string;
  name: string;
  description: string;
  category: TemplateCategory;
  previewImage: string;
  isPremium: boolean;
  sections: ResumeSectionType[];
  styling: TemplateStyle;
}

export enum TemplateCategory {
  MODERN = 'modern',
  CLASSIC = 'classic',
  CREATIVE = 'creative',
  MINIMAL = 'minimal',
  PROFESSIONAL = 'professional',
  ACADEMIC = 'academic',
  TECHNICAL = 'technical',
}

export interface TemplateStyle {
  fontFamily: string;
  fontSize: number;
  lineHeight: number;
  margins: {
    top: number;
    right: number;
    bottom: number;
    left: number;
  };
  colors: {
    primary: string;
    secondary: string;
    text: string;
    accent: string;
  };
  spacing: {
    sectionGap: number;
    itemGap: number;
  };
}

export interface Resume {
  id: string;
  userId: string;
  title: string;
  templateId: string;
  personalInfo: PersonalInfo;
  sections: ResumeSection[];
  settings: ResumeSettings;
  metadata: ResumeMetadata;
  createdAt: string;
  updatedAt: string;
}

export interface ResumeSettings {
  isPublic: boolean;
  allowComments: boolean;
  seoOptimized: boolean;
  atsOptimized: boolean;
  targetJobTitle?: string;
  targetCompany?: string;
  keywords: string[];
}

export interface ResumeMetadata {
  version: number;
  lastEditedBy: string;
  wordCount: number;
  pageCount: number;
  atsScore?: number;
  readabilityScore?: number;
  completionPercentage: number;
}

export interface ResumeAnalysis {
  atsScore: number;
  readabilityScore: number;
  keywordDensity: Record<string, number>;
  suggestions: AnalysisSuggestion[];
  strengths: string[];
  weaknesses: string[];
}

export interface AnalysisSuggestion {
  id: string;
  type: SuggestionType;
  severity: SuggestionSeverity;
  title: string;
  description: string;
  section?: ResumeSectionType;
  actionable: boolean;
}

export enum SuggestionType {
  CONTENT = 'content',
  FORMATTING = 'formatting',
  KEYWORD = 'keyword',
  LENGTH = 'length',
  GRAMMAR = 'grammar',
  ATS = 'ats',
}

export enum SuggestionSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}

// Form validation types
export interface ValidationError {
  field: string;
  message: string;
  code: string;
}

export interface FormState<T> {
  data: T;
  errors: ValidationError[];
  isValid: boolean;
  isDirty: boolean;
  isSubmitting: boolean;
}

// API response types
export interface ResumeListResponse {
  resumes: Resume[];
  total: number;
  page: number;
  limit: number;
}

export interface ResumeResponse {
  resume: Resume;
  analysis?: ResumeAnalysis;
}

// Export utility types
export type ResumeFormData = Omit<Resume, 'id' | 'userId' | 'createdAt' | 'updatedAt' | 'metadata'>;
export type PartialResume = Partial<Resume>;
export type ResumeUpdate = Partial<Pick<Resume, 'title' | 'personalInfo' | 'sections' | 'settings'>>;

// Section-specific data types
export type WorkExperienceData = WorkExperience[];
export type EducationData = Education[];
export type ProjectData = Project[];
export type SkillData = Skill[];
export type CertificationData = Certification[];
export type LanguageData = Language[];
export type AwardData = Award[];
export type PublicationData = Publication[];
export type VolunteerData = VolunteerExperience[];
