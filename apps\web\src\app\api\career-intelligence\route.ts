/**
 * Career Intelligence API Routes
 * 
 * Handles requests for career insights, market analysis, and job matching
 * Implements Epic 5.0: Career Intelligence Engine
 */

import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { CareerIntelligenceService } from '@/lib/career-intelligence/service'
import { FeatureGate } from '@/lib/payments/feature-gate'
import { z } from 'zod'

// Request validation schemas
const generateInsightsSchema = z.object({
  resumeId: z.string().min(1, 'Resume ID is required')
})

const marketAnalysisSchema = z.object({
  resumeId: z.string().min(1, 'Resume ID is required'),
  analysisType: z.enum(['SALARY_ESTIMATE', 'MARKET_FIT', 'SKILL_GAP', 'CAREER_PATH', 'COMPREHENSIVE']).optional().default('COMPREHENSIVE')
})

// Initialize services
const careerIntelligence = new CareerIntelligenceService()
const featureGate = new FeatureGate()

/**
 * GET /api/career-intelligence
 * Get career insights for a user's resume
 */
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const resumeId = searchParams.get('resumeId')

    if (!resumeId) {
      return NextResponse.json({ error: 'Resume ID is required' }, { status: 400 })
    }

    // Check feature access
    const hasAccess = await featureGate.checkFeatureAccess(session.user.id, 'CAREER_INTELLIGENCE')
    if (!hasAccess.allowed) {
      return NextResponse.json({
        error: 'Feature not available',
        message: hasAccess.reason,
        upgradeRequired: true
      }, { status: 403 })
    }

    // Generate career insights
    const insights = await careerIntelligence.generateCareerInsights(session.user.id, resumeId)

    // Track feature usage
    await featureGate.trackFeatureUsage(session.user.id, 'CAREER_INTELLIGENCE')

    return NextResponse.json({
      success: true,
      data: insights,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Career intelligence error:', error)
    return NextResponse.json({
      error: 'Failed to generate career insights',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

/**
 * POST /api/career-intelligence
 * Generate new career insights or refresh existing analysis
 */
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validation = generateInsightsSchema.safeParse(body)

    if (!validation.success) {
      return NextResponse.json({
        error: 'Invalid request data',
        details: validation.error.errors
      }, { status: 400 })
    }

    const { resumeId } = validation.data

    // Check feature access
    const hasAccess = await featureGate.checkFeatureAccess(session.user.id, 'CAREER_INTELLIGENCE')
    if (!hasAccess.allowed) {
      return NextResponse.json({
        error: 'Feature not available',
        message: hasAccess.reason,
        upgradeRequired: true
      }, { status: 403 })
    }

    // Generate fresh career insights
    const insights = await careerIntelligence.generateCareerInsights(session.user.id, resumeId)

    // Track feature usage
    await featureGate.trackFeatureUsage(session.user.id, 'CAREER_INTELLIGENCE')

    return NextResponse.json({
      success: true,
      data: insights,
      message: 'Career insights generated successfully',
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Career intelligence generation error:', error)
    return NextResponse.json({
      error: 'Failed to generate career insights',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

/**
 * PUT /api/career-intelligence
 * Update profile vector and regenerate insights
 */
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validation = generateInsightsSchema.safeParse(body)

    if (!validation.success) {
      return NextResponse.json({
        error: 'Invalid request data',
        details: validation.error.errors
      }, { status: 400 })
    }

    const { resumeId } = validation.data

    // Check feature access
    const hasAccess = await featureGate.checkFeatureAccess(session.user.id, 'CAREER_INTELLIGENCE')
    if (!hasAccess.allowed) {
      return NextResponse.json({
        error: 'Feature not available',
        message: hasAccess.reason,
        upgradeRequired: true
      }, { status: 403 })
    }

    // Update profile vector
    const profileVector = await careerIntelligence.createOrUpdateProfileVector(session.user.id, resumeId)

    // Generate updated insights
    const insights = await careerIntelligence.generateCareerInsights(session.user.id, resumeId)

    // Track feature usage
    await featureGate.trackFeatureUsage(session.user.id, 'CAREER_INTELLIGENCE')

    return NextResponse.json({
      success: true,
      data: {
        profileVector: {
          experienceLevel: profileVector.experienceLevel,
          primaryRole: profileVector.primaryRole,
          skillsCount: profileVector.skillsExtracted.length,
          industries: profileVector.industries,
          locations: profileVector.locations
        },
        insights
      },
      message: 'Profile updated and insights regenerated',
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Profile update error:', error)
    return NextResponse.json({
      error: 'Failed to update profile and insights',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
