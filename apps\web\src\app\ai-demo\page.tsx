'use client';

import { useState } from 'react';
import { MainLayout } from '@/components/layout/main-layout';
import { ContentGeneratorPanel } from '@/components/ai/content-generator-panel';
import { ATSAnalysisPanel } from '@/components/ai/ats-analysis-panel';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Icons } from '@/components/ui/icons';
import { ContentType } from '@careercraft/shared/types/ai';
import { Resume } from '@careercraft/shared/types/resume';
import { toast } from 'sonner';

export default function AIDemoPage() {
  const [generatedContent, setGeneratedContent] = useState<string>('');
  
  // Mock resume data for ATS analysis demo
  const mockResume: Resume = {
    id: 'demo-resume',
    userId: 'demo-user',
    title: 'Software Engineer Resume',
    description: 'Demo resume for AI testing',
    personalInfo: {
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      phone: '******-0123',
      location: 'San Francisco, CA',
      website: 'https://johndoe.dev',
      summary: 'Experienced software engineer with 5+ years of experience developing scalable web applications using modern technologies.',
      title: 'Senior Software Engineer',
      industry: 'Technology',
      skills: ['JavaScript', 'React', 'Node.js', 'Python', 'AWS'],
    },
    sections: [],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  };

  const handleContentGenerated = (content: string) => {
    setGeneratedContent(content);
    toast.success('Content generated successfully!');
  };

  const aiFeatures = [
    {
      icon: Icons.sparkles,
      title: 'AI Content Generation',
      description: 'Generate professional summaries, job descriptions, and achievement bullets',
      features: [
        'Professional summary optimization',
        'Work experience descriptions',
        'Achievement bullet points',
        'Skills recommendations',
        'Cover letter generation',
        'LinkedIn profile content',
      ],
    },
    {
      icon: Icons.shield,
      title: 'ATS Optimization',
      description: 'Analyze and optimize your resume for Applicant Tracking Systems',
      features: [
        'ATS compatibility scoring',
        'Keyword analysis and matching',
        'Format compliance checking',
        'Readability assessment',
        'Issue identification',
        'Improvement recommendations',
      ],
    },
    {
      icon: Icons.target,
      title: 'Smart Recommendations',
      description: 'Get personalized suggestions based on your industry and experience',
      features: [
        'Industry-specific keywords',
        'Experience level adaptation',
        'Role-targeted content',
        'Skill gap analysis',
        'Career progression advice',
        'Market trend insights',
      ],
    },
  ];

  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold mb-4">
            AI-Powered Resume Enhancement
          </h1>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Experience the future of resume building with our advanced AI technology. 
            Generate compelling content and optimize for ATS systems automatically.
          </p>
        </div>

        {/* Features Overview */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
          {aiFeatures.map((feature, index) => (
            <Card key={index}>
              <CardHeader className="text-center">
                <feature.icon className="h-12 w-12 mx-auto text-primary mb-4" />
                <CardTitle>{feature.title}</CardTitle>
                <CardDescription>{feature.description}</CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  {feature.features.map((item, itemIndex) => (
                    <li key={itemIndex} className="flex items-center gap-2 text-sm">
                      <Icons.check className="h-4 w-4 text-green-500 flex-shrink-0" />
                      {item}
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Interactive Demo */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Icons.play className="h-5 w-5" />
              Interactive AI Demo
            </CardTitle>
            <CardDescription>
              Try our AI-powered features with real examples
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="content-generation" className="w-full">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="content-generation">Content Generation</TabsTrigger>
                <TabsTrigger value="ats-analysis">ATS Analysis</TabsTrigger>
              </TabsList>

              <TabsContent value="content-generation" className="space-y-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div>
                    <h3 className="text-lg font-semibold mb-4">Generate AI Content</h3>
                    <ContentGeneratorPanel
                      contentType={ContentType.PROFESSIONAL_SUMMARY}
                      onContentGenerated={handleContentGenerated}
                      initialContext={{
                        firstName: 'John',
                        lastName: 'Doe',
                        currentRole: 'Software Engineer',
                        industry: 'Technology',
                        skills: ['JavaScript', 'React', 'Node.js', 'Python'],
                      }}
                    />
                  </div>
                  
                  <div>
                    <h3 className="text-lg font-semibold mb-4">Generated Content</h3>
                    <Card>
                      <CardContent className="p-6">
                        {generatedContent ? (
                          <div className="space-y-4">
                            <div className="flex items-center gap-2">
                              <Badge variant="secondary">AI Generated</Badge>
                              <Badge variant="outline">ATS Optimized</Badge>
                            </div>
                            <p className="text-sm leading-relaxed bg-muted p-4 rounded-lg">
                              {generatedContent}
                            </p>
                          </div>
                        ) : (
                          <div className="text-center py-8 text-muted-foreground">
                            <Icons.sparkles className="h-12 w-12 mx-auto mb-4 opacity-50" />
                            <p>Generated content will appear here</p>
                            <p className="text-xs mt-2">Use the AI generator to create professional content</p>
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="ats-analysis" className="space-y-6">
                <div>
                  <h3 className="text-lg font-semibold mb-4">ATS Compatibility Analysis</h3>
                  <ATSAnalysisPanel resume={mockResume} />
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>

        {/* Benefits */}
        <Card>
          <CardHeader>
            <CardTitle>Why Choose AI-Enhanced Resume Building?</CardTitle>
            <CardDescription>
              Discover the advantages of using AI for your career advancement
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div className="flex items-start gap-3">
                  <Icons.clock className="h-5 w-5 text-primary mt-0.5" />
                  <div>
                    <h4 className="font-medium">Save Time</h4>
                    <p className="text-sm text-muted-foreground">
                      Generate professional content in seconds instead of hours
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start gap-3">
                  <Icons.trendingUp className="h-5 w-5 text-primary mt-0.5" />
                  <div>
                    <h4 className="font-medium">Improve Success Rate</h4>
                    <p className="text-sm text-muted-foreground">
                      Optimize for ATS systems and increase interview callbacks
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start gap-3">
                  <Icons.users className="h-5 w-5 text-primary mt-0.5" />
                  <div>
                    <h4 className="font-medium">Expert Guidance</h4>
                    <p className="text-sm text-muted-foreground">
                      Benefit from AI trained on thousands of successful resumes
                    </p>
                  </div>
                </div>
              </div>
              
              <div className="space-y-4">
                <div className="flex items-start gap-3">
                  <Icons.globe className="h-5 w-5 text-primary mt-0.5" />
                  <div>
                    <h4 className="font-medium">Industry Specific</h4>
                    <p className="text-sm text-muted-foreground">
                      Tailored content for your specific industry and role
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start gap-3">
                  <Icons.shield className="h-5 w-5 text-primary mt-0.5" />
                  <div>
                    <h4 className="font-medium">ATS Compatible</h4>
                    <p className="text-sm text-muted-foreground">
                      Ensure your resume passes automated screening systems
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start gap-3">
                  <Icons.refresh className="h-5 w-5 text-primary mt-0.5" />
                  <div>
                    <h4 className="font-medium">Continuous Improvement</h4>
                    <p className="text-sm text-muted-foreground">
                      AI learns and improves with every interaction
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Call to Action */}
        <div className="text-center py-12">
          <h2 className="text-2xl font-bold mb-4">
            Ready to Transform Your Resume?
          </h2>
          <p className="text-muted-foreground mb-6">
            Join thousands of professionals who have enhanced their careers with AI-powered resumes
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="/dashboard/resumes/new"
              className="inline-flex items-center justify-center rounded-md bg-primary px-6 py-3 text-sm font-medium text-primary-foreground shadow transition-colors hover:bg-primary/90"
            >
              <Icons.plus className="mr-2 h-4 w-4" />
              Create AI-Enhanced Resume
            </a>
            <a
              href="/templates"
              className="inline-flex items-center justify-center rounded-md border border-input bg-background px-6 py-3 text-sm font-medium shadow-sm transition-colors hover:bg-accent hover:text-accent-foreground"
            >
              <Icons.eye className="mr-2 h-4 w-4" />
              Browse Templates
            </a>
          </div>
        </div>
      </div>
    </MainLayout>
  );
}
