#!/usr/bin/env tsx

/**
 * Database Testing Script
 * 
 * This script performs comprehensive testing of the database setup:
 * 1. Connection testing
 * 2. Schema validation
 * 3. CRUD operations
 * 4. Data integrity checks
 * 5. Performance benchmarks
 */

import { config } from 'dotenv';
import { join } from 'path';
import { 
  prisma, 
  connectDB, 
  disconnectDB, 
  healthCheck, 
  cleanupDatabase, 
  seedDatabase,
  withTransaction 
} from '../packages/database/src/index';

// Load environment variables
config({ path: join(__dirname, '../.env.local') });

interface TestResult {
  name: string;
  status: 'PASS' | 'FAIL' | 'SKIP';
  duration: number;
  error?: string;
  details?: any;
}

class DatabaseTester {
  private results: TestResult[] = [];

  private async runTest(name: string, testFn: () => Promise<any>): Promise<void> {
    const startTime = Date.now();
    console.log(`🧪 Running: ${name}`);

    try {
      const result = await testFn();
      const duration = Date.now() - startTime;
      
      this.results.push({
        name,
        status: 'PASS',
        duration,
        details: result,
      });
      
      console.log(`✅ PASS: ${name} (${duration}ms)`);
    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      
      this.results.push({
        name,
        status: 'FAIL',
        duration,
        error: errorMessage,
      });
      
      console.log(`❌ FAIL: ${name} (${duration}ms) - ${errorMessage}`);
    }
  }

  async testConnection(): Promise<void> {
    await this.runTest('Database Connection', async () => {
      await connectDB();
      return 'Connected successfully';
    });
  }

  async testHealthCheck(): Promise<void> {
    await this.runTest('Health Check', async () => {
      const health = await healthCheck();
      if (health.status !== 'healthy') {
        throw new Error(`Health check failed: ${health.error}`);
      }
      return health;
    });
  }

  async testSchemaValidation(): Promise<void> {
    await this.runTest('Schema Validation', async () => {
      // Test that all expected tables exist by querying them
      const tables = [
        'User',
        'UserProfile', 
        'Resume',
        'Experience',
        'Education',
        'Skill',
        'Project',
        'Certification',
        'Language',
        'CustomSection',
        'Template',
        'CoverLetter',
        'AIGeneration',
        'ResumeAnalytics'
      ];

      const results = [];
      for (const table of tables) {
        try {
          // Use Prisma's introspection to check table existence
          const count = await (prisma as any)[table.toLowerCase()].count();
          results.push({ table, exists: true, count });
        } catch (error) {
          results.push({ table, exists: false, error: error instanceof Error ? error.message : 'Unknown error' });
        }
      }

      return results;
    });
  }

  async testCRUDOperations(): Promise<void> {
    await this.runTest('CRUD Operations', async () => {
      // Clean up first
      await cleanupDatabase();

      // Create a user
      const user = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          name: 'Test User',
        },
      });

      // Create a resume with all sections
      const resume = await prisma.resume.create({
        data: {
          userId: user.id,
          title: 'Test Resume',
          description: 'A test resume',
          experiences: {
            create: {
              company: 'Test Company',
              position: 'Test Position',
              startDate: new Date('2023-01-01'),
              displayOrder: 0,
            },
          },
          educations: {
            create: {
              institution: 'Test University',
              degree: 'Test Degree',
              startDate: new Date('2019-09-01'),
              endDate: new Date('2023-05-31'),
              displayOrder: 0,
            },
          },
          skills: {
            create: [
              {
                name: 'JavaScript',
                category: 'Programming',
                level: 'ADVANCED',
                displayOrder: 0,
              },
              {
                name: 'TypeScript',
                category: 'Programming',
                level: 'INTERMEDIATE',
                displayOrder: 1,
              },
            ],
          },
        },
        include: {
          experiences: true,
          educations: true,
          skills: true,
        },
      });

      // Update the resume
      const updatedResume = await prisma.resume.update({
        where: { id: resume.id },
        data: { title: 'Updated Test Resume' },
      });

      // Read the resume
      const fetchedResume = await prisma.resume.findUnique({
        where: { id: resume.id },
        include: {
          experiences: true,
          educations: true,
          skills: true,
        },
      });

      // Delete the resume (cascade should delete related records)
      await prisma.resume.delete({
        where: { id: resume.id },
      });

      // Verify deletion
      const deletedResume = await prisma.resume.findUnique({
        where: { id: resume.id },
      });

      return {
        created: !!resume.id,
        updated: updatedResume.title === 'Updated Test Resume',
        fetched: !!fetchedResume,
        deleted: deletedResume === null,
        sectionsCreated: {
          experiences: resume.experiences.length === 1,
          educations: resume.educations.length === 1,
          skills: resume.skills.length === 2,
        },
      };
    });
  }

  async testTransactions(): Promise<void> {
    await this.runTest('Transaction Handling', async () => {
      await cleanupDatabase();

      const user = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          name: 'Transaction User',
        },
      });

      // Test successful transaction
      const successResult = await withTransaction(async (tx) => {
        const resume = await tx.resume.create({
          data: {
            userId: user.id,
            title: 'Transaction Resume',
          },
        });

        await tx.experience.create({
          data: {
            resumeId: resume.id,
            company: 'Transaction Corp',
            position: 'Developer',
            startDate: new Date(),
            displayOrder: 0,
          },
        });

        return resume;
      });

      // Test failed transaction (should rollback)
      let failedTransaction = false;
      try {
        await withTransaction(async (tx) => {
          await tx.resume.create({
            data: {
              userId: user.id,
              title: 'Failed Resume',
            },
          });

          throw new Error('Intentional failure');
        });
      } catch {
        failedTransaction = true;
      }

      // Verify only successful transaction data exists
      const resumes = await prisma.resume.findMany({
        where: { userId: user.id },
        include: { experiences: true },
      });

      return {
        successfulTransaction: !!successResult.id,
        failedTransactionRolledBack: failedTransaction,
        finalResumeCount: resumes.length,
        experienceCreated: resumes[0]?.experiences.length === 1,
      };
    });
  }

  async testSeedData(): Promise<void> {
    await this.runTest('Seed Data', async () => {
      await cleanupDatabase();
      await seedDatabase();

      const templates = await prisma.template.findMany();
      
      return {
        templatesCreated: templates.length,
        templates: templates.map(t => ({
          name: t.name,
          category: t.category,
          isPremium: t.isPremium,
          isActive: t.isActive,
        })),
      };
    });
  }

  async testPerformance(): Promise<void> {
    await this.runTest('Performance Benchmarks', async () => {
      await cleanupDatabase();

      // Create test data
      const user = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          name: 'Performance User',
        },
      });

      // Benchmark: Create multiple resumes
      const createStart = Date.now();
      const resumes = await Promise.all(
        Array.from({ length: 10 }, (_, i) =>
          prisma.resume.create({
            data: {
              userId: user.id,
              title: `Resume ${i + 1}`,
            },
          })
        )
      );
      const createTime = Date.now() - createStart;

      // Benchmark: Query with relations
      const queryStart = Date.now();
      const resumesWithRelations = await prisma.resume.findMany({
        where: { userId: user.id },
        include: {
          experiences: true,
          educations: true,
          skills: true,
          projects: true,
        },
      });
      const queryTime = Date.now() - queryStart;

      // Benchmark: Complex aggregation
      const aggStart = Date.now();
      const userStats = await prisma.user.findUnique({
        where: { id: user.id },
        include: {
          _count: {
            select: {
              resumes: true,
            },
          },
        },
      });
      const aggTime = Date.now() - aggStart;

      return {
        createMultipleResumes: {
          count: resumes.length,
          timeMs: createTime,
          avgTimePerResume: createTime / resumes.length,
        },
        queryWithRelations: {
          count: resumesWithRelations.length,
          timeMs: queryTime,
        },
        aggregationQuery: {
          timeMs: aggTime,
          userResumeCount: userStats?._count.resumes,
        },
      };
    });
  }

  async runAllTests(): Promise<void> {
    console.log('🚀 Starting Database Tests...\n');

    await this.testConnection();
    await this.testHealthCheck();
    await this.testSchemaValidation();
    await this.testCRUDOperations();
    await this.testTransactions();
    await this.testSeedData();
    await this.testPerformance();

    await this.cleanup();
    await this.printSummary();
  }

  private async cleanup(): Promise<void> {
    try {
      await cleanupDatabase();
      await disconnectDB();
      console.log('\n🧹 Cleanup completed');
    } catch (error) {
      console.log('\n⚠️  Cleanup failed:', error);
    }
  }

  private async printSummary(): Promise<void> {
    console.log('\n📊 Test Summary');
    console.log('================');

    const passed = this.results.filter(r => r.status === 'PASS').length;
    const failed = this.results.filter(r => r.status === 'FAIL').length;
    const total = this.results.length;

    console.log(`Total Tests: ${total}`);
    console.log(`Passed: ${passed}`);
    console.log(`Failed: ${failed}`);
    console.log(`Success Rate: ${((passed / total) * 100).toFixed(1)}%`);

    if (failed > 0) {
      console.log('\n❌ Failed Tests:');
      this.results
        .filter(r => r.status === 'FAIL')
        .forEach(r => {
          console.log(`  - ${r.name}: ${r.error}`);
        });
    }

    const totalTime = this.results.reduce((sum, r) => sum + r.duration, 0);
    console.log(`\nTotal Execution Time: ${totalTime}ms`);

    if (failed === 0) {
      console.log('\n🎉 All tests passed! Database is ready for development.');
    } else {
      console.log('\n⚠️  Some tests failed. Please check your database configuration.');
      process.exit(1);
    }
  }
}

// Run the tests
async function main() {
  const tester = new DatabaseTester();
  await tester.runAllTests();
}

if (require.main === module) {
  main().catch(console.error);
}
