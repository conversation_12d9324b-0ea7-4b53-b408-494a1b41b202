'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { AIAssistant } from '@/components/ai/AIAssistant'
import { 
  Save, 
  Download, 
  Eye, 
  Plus, 
  Trash2, 
  GripVertical,
  Sparkles,
  FileText,
  User,
  Briefcase,
  GraduationCap,
  Award,
  Settings
} from 'lucide-react'

interface PersonalInfo {
  fullName: string
  email: string
  phone: string
  location: string
  website?: string
  linkedin?: string
  github?: string
  summary: string
}

interface Experience {
  id: string
  company: string
  position: string
  location: string
  startDate: string
  endDate: string
  current: boolean
  description: string
  achievements: string[]
}

interface Education {
  id: string
  institution: string
  degree: string
  field: string
  startDate: string
  endDate: string
  gpa?: string
  achievements: string[]
}

interface Skill {
  id: string
  category: string
  skills: string[]
}

interface ResumeData {
  personalInfo: PersonalInfo
  experience: Experience[]
  education: Education[]
  skills: Skill[]
  achievements: string[]
  certifications: string[]
}

interface ResumeEditorProps {
  resumeId?: string
  templateId?: string
  initialData?: Partial<ResumeData>
}

export function ResumeEditor({ resumeId, templateId, initialData }: ResumeEditorProps) {
  const [resumeData, setResumeData] = useState<ResumeData>({
    personalInfo: {
      fullName: '',
      email: '',
      phone: '',
      location: '',
      website: '',
      linkedin: '',
      github: '',
      summary: ''
    },
    experience: [],
    education: [],
    skills: [],
    achievements: [],
    certifications: []
  })

  const [activeTab, setActiveTab] = useState('personal')
  const [isSaving, setIsSaving] = useState(false)
  const [showAIAssistant, setShowAIAssistant] = useState(false)
  const [lastSaved, setLastSaved] = useState<Date | null>(null)

  useEffect(() => {
    if (initialData) {
      setResumeData(prev => ({ ...prev, ...initialData }))
    }
    
    if (resumeId) {
      loadResumeData()
    }
  }, [resumeId, initialData])

  const loadResumeData = async () => {
    try {
      // Load resume data from API
      console.log('Loading resume data for:', resumeId)
    } catch (error) {
      console.error('Error loading resume data:', error)
    }
  }

  const saveResume = async () => {
    setIsSaving(true)
    try {
      // Save resume data to API
      console.log('Saving resume data:', resumeData)
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      setLastSaved(new Date())
    } catch (error) {
      console.error('Error saving resume:', error)
    } finally {
      setIsSaving(false)
    }
  }

  const addExperience = () => {
    const newExperience: Experience = {
      id: Date.now().toString(),
      company: '',
      position: '',
      location: '',
      startDate: '',
      endDate: '',
      current: false,
      description: '',
      achievements: []
    }
    setResumeData(prev => ({
      ...prev,
      experience: [...prev.experience, newExperience]
    }))
  }

  const updateExperience = (id: string, updates: Partial<Experience>) => {
    setResumeData(prev => ({
      ...prev,
      experience: prev.experience.map(exp => 
        exp.id === id ? { ...exp, ...updates } : exp
      )
    }))
  }

  const removeExperience = (id: string) => {
    setResumeData(prev => ({
      ...prev,
      experience: prev.experience.filter(exp => exp.id !== id)
    }))
  }

  const addEducation = () => {
    const newEducation: Education = {
      id: Date.now().toString(),
      institution: '',
      degree: '',
      field: '',
      startDate: '',
      endDate: '',
      gpa: '',
      achievements: []
    }
    setResumeData(prev => ({
      ...prev,
      education: [...prev.education, newEducation]
    }))
  }

  const updateEducation = (id: string, updates: Partial<Education>) => {
    setResumeData(prev => ({
      ...prev,
      education: prev.education.map(edu => 
        edu.id === id ? { ...edu, ...updates } : edu
      )
    }))
  }

  const removeEducation = (id: string) => {
    setResumeData(prev => ({
      ...prev,
      education: prev.education.filter(edu => edu.id !== id)
    }))
  }

  const addSkillCategory = () => {
    const newSkillCategory: Skill = {
      id: Date.now().toString(),
      category: '',
      skills: []
    }
    setResumeData(prev => ({
      ...prev,
      skills: [...prev.skills, newSkillCategory]
    }))
  }

  const updateSkillCategory = (id: string, updates: Partial<Skill>) => {
    setResumeData(prev => ({
      ...prev,
      skills: prev.skills.map(skill => 
        skill.id === id ? { ...skill, ...updates } : skill
      )
    }))
  }

  const removeSkillCategory = (id: string) => {
    setResumeData(prev => ({
      ...prev,
      skills: prev.skills.filter(skill => skill.id !== id)
    }))
  }

  const handleAIContentUpdate = (content: string) => {
    // Apply AI-generated content based on current tab
    switch (activeTab) {
      case 'personal':
        setResumeData(prev => ({
          ...prev,
          personalInfo: { ...prev.personalInfo, summary: content }
        }))
        break
      // Add other cases as needed
    }
  }

  const handleAISuggestionApply = (suggestion: string) => {
    // Apply AI suggestion to current context
    console.log('Applying AI suggestion:', suggestion)
  }

  const getResumeContent = () => {
    // Convert resume data to text for AI analysis
    return `
    ${resumeData.personalInfo.fullName}
    ${resumeData.personalInfo.summary}
    
    Experience:
    ${resumeData.experience.map(exp => `${exp.position} at ${exp.company}: ${exp.description}`).join('\n')}
    
    Education:
    ${resumeData.education.map(edu => `${edu.degree} in ${edu.field} from ${edu.institution}`).join('\n')}
    
    Skills:
    ${resumeData.skills.map(skill => `${skill.category}: ${skill.skills.join(', ')}`).join('\n')}
    `
  }

  return (
    <div className="flex h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      {/* Main Editor */}
      <div className={`flex-1 flex flex-col ${showAIAssistant ? 'mr-96' : ''} transition-all duration-300`}>
        {/* Header */}
        <div className="glass-panel p-4 border-b">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                Resume Editor
              </h1>
              <p className="text-sm text-muted-foreground">
                {lastSaved ? `Last saved ${lastSaved.toLocaleTimeString()}` : 'Unsaved changes'}
              </p>
            </div>
            
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                onClick={() => setShowAIAssistant(!showAIAssistant)}
                className={`glass-input ${showAIAssistant ? 'bg-purple-100 dark:bg-purple-900/20' : ''}`}
              >
                <Sparkles className="w-4 h-4 mr-2" />
                AI Assistant
              </Button>
              
              <Button variant="outline" className="glass-input">
                <Eye className="w-4 h-4 mr-2" />
                Preview
              </Button>
              
              <Button variant="outline" className="glass-input">
                <Download className="w-4 h-4 mr-2" />
                Export
              </Button>
              
              <Button 
                onClick={saveResume}
                disabled={isSaving}
                className="glass-card bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white border-0"
              >
                {isSaving ? (
                  <>
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="w-4 h-4 mr-2" />
                    Save
                  </>
                )}
              </Button>
            </div>
          </div>
        </div>

        {/* Editor Content */}
        <div className="flex-1 overflow-auto p-6">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
            <TabsList className="glass-card grid w-full grid-cols-5">
              <TabsTrigger value="personal" className="flex items-center space-x-2">
                <User className="w-4 h-4" />
                <span>Personal</span>
              </TabsTrigger>
              <TabsTrigger value="experience" className="flex items-center space-x-2">
                <Briefcase className="w-4 h-4" />
                <span>Experience</span>
              </TabsTrigger>
              <TabsTrigger value="education" className="flex items-center space-x-2">
                <GraduationCap className="w-4 h-4" />
                <span>Education</span>
              </TabsTrigger>
              <TabsTrigger value="skills" className="flex items-center space-x-2">
                <Award className="w-4 h-4" />
                <span>Skills</span>
              </TabsTrigger>
              <TabsTrigger value="settings" className="flex items-center space-x-2">
                <Settings className="w-4 h-4" />
                <span>Settings</span>
              </TabsTrigger>
            </TabsList>

            {/* Personal Information Tab */}
            <TabsContent value="personal" className="space-y-6">
              <Card className="glass-card">
                <CardHeader>
                  <CardTitle>Personal Information</CardTitle>
                  <CardDescription>
                    Your contact details and professional summary
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="fullName">Full Name *</Label>
                      <Input
                        id="fullName"
                        value={resumeData.personalInfo.fullName}
                        onChange={(e) => setResumeData(prev => ({
                          ...prev,
                          personalInfo: { ...prev.personalInfo, fullName: e.target.value }
                        }))}
                        className="glass-input"
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="email">Email *</Label>
                      <Input
                        id="email"
                        type="email"
                        value={resumeData.personalInfo.email}
                        onChange={(e) => setResumeData(prev => ({
                          ...prev,
                          personalInfo: { ...prev.personalInfo, email: e.target.value }
                        }))}
                        className="glass-input"
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="phone">Phone</Label>
                      <Input
                        id="phone"
                        value={resumeData.personalInfo.phone}
                        onChange={(e) => setResumeData(prev => ({
                          ...prev,
                          personalInfo: { ...prev.personalInfo, phone: e.target.value }
                        }))}
                        className="glass-input"
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="location">Location</Label>
                      <Input
                        id="location"
                        value={resumeData.personalInfo.location}
                        onChange={(e) => setResumeData(prev => ({
                          ...prev,
                          personalInfo: { ...prev.personalInfo, location: e.target.value }
                        }))}
                        className="glass-input"
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="linkedin">LinkedIn</Label>
                      <Input
                        id="linkedin"
                        value={resumeData.personalInfo.linkedin}
                        onChange={(e) => setResumeData(prev => ({
                          ...prev,
                          personalInfo: { ...prev.personalInfo, linkedin: e.target.value }
                        }))}
                        className="glass-input"
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="github">GitHub</Label>
                      <Input
                        id="github"
                        value={resumeData.personalInfo.github}
                        onChange={(e) => setResumeData(prev => ({
                          ...prev,
                          personalInfo: { ...prev.personalInfo, github: e.target.value }
                        }))}
                        className="glass-input"
                      />
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="summary">Professional Summary</Label>
                    <Textarea
                      id="summary"
                      value={resumeData.personalInfo.summary}
                      onChange={(e) => setResumeData(prev => ({
                        ...prev,
                        personalInfo: { ...prev.personalInfo, summary: e.target.value }
                      }))}
                      className="glass-input min-h-[120px]"
                      placeholder="Write a compelling professional summary that highlights your key strengths and career achievements..."
                    />
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Experience Tab */}
            <TabsContent value="experience" className="space-y-6">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-xl font-semibold">Work Experience</h2>
                  <p className="text-sm text-muted-foreground">
                    Add your professional experience in reverse chronological order
                  </p>
                </div>
                <Button onClick={addExperience} className="glass-card bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white border-0">
                  <Plus className="w-4 h-4 mr-2" />
                  Add Experience
                </Button>
              </div>

              {resumeData.experience.map((exp, index) => (
                <Card key={exp.id} className="glass-card">
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-lg">Experience #{index + 1}</CardTitle>
                      <div className="flex items-center space-x-2">
                        <Button size="sm" variant="ghost">
                          <GripVertical className="w-4 h-4" />
                        </Button>
                        <Button 
                          size="sm" 
                          variant="ghost" 
                          onClick={() => removeExperience(exp.id)}
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label>Company *</Label>
                        <Input
                          value={exp.company}
                          onChange={(e) => updateExperience(exp.id, { company: e.target.value })}
                          className="glass-input"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label>Position *</Label>
                        <Input
                          value={exp.position}
                          onChange={(e) => updateExperience(exp.id, { position: e.target.value })}
                          className="glass-input"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label>Location</Label>
                        <Input
                          value={exp.location}
                          onChange={(e) => updateExperience(exp.id, { location: e.target.value })}
                          className="glass-input"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label>Start Date</Label>
                        <Input
                          type="month"
                          value={exp.startDate}
                          onChange={(e) => updateExperience(exp.id, { startDate: e.target.value })}
                          className="glass-input"
                        />
                      </div>
                    </div>
                    
                    <div className="space-y-2">
                      <Label>Description</Label>
                      <Textarea
                        value={exp.description}
                        onChange={(e) => updateExperience(exp.id, { description: e.target.value })}
                        className="glass-input"
                        rows={4}
                        placeholder="Describe your role, responsibilities, and key achievements..."
                      />
                    </div>
                  </CardContent>
                </Card>
              ))}

              {resumeData.experience.length === 0 && (
                <Card className="glass-card">
                  <CardContent className="text-center py-12">
                    <Briefcase className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-semibold mb-2">No experience added yet</h3>
                    <p className="text-muted-foreground mb-4">
                      Add your work experience to showcase your professional background
                    </p>
                    <Button onClick={addExperience} className="glass-card bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white border-0">
                      <Plus className="w-4 h-4 mr-2" />
                      Add Your First Experience
                    </Button>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            {/* Add other tabs content here */}
            <TabsContent value="education">
              <div>Education content coming soon...</div>
            </TabsContent>
            
            <TabsContent value="skills">
              <div>Skills content coming soon...</div>
            </TabsContent>
            
            <TabsContent value="settings">
              <div>Settings content coming soon...</div>
            </TabsContent>
          </Tabs>
        </div>
      </div>

      {/* AI Assistant Sidebar */}
      {showAIAssistant && (
        <div className="w-96 glass-panel border-l overflow-auto">
          <div className="p-4">
            <AIAssistant
              resumeContent={getResumeContent()}
              onContentUpdate={handleAIContentUpdate}
              onSuggestionApply={handleAISuggestionApply}
            />
          </div>
        </div>
      )}
    </div>
  )
}
