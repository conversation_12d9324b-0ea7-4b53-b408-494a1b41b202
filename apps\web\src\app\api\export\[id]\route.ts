import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@careercraft/database';

interface RouteParams {
  params: {
    id: string;
  };
}

export async function GET(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const exportJob = await prisma.exportJob.findFirst({
      where: {
        id: params.id,
        userId: session.user.id,
      },
    });

    if (!exportJob) {
      return NextResponse.json(
        { error: 'Export job not found' },
        { status: 404 }
      );
    }

    // Check if job has expired
    if (exportJob.expiresAt && new Date() > exportJob.expiresAt) {
      await prisma.exportJob.update({
        where: { id: params.id },
        data: { status: 'failed', error: 'Export job expired' },
      });

      return NextResponse.json(
        { error: 'Export job expired' },
        { status: 410 }
      );
    }

    return NextResponse.json(exportJob);
  } catch (error) {
    console.error('Error fetching export job:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
