# CareerCraft Minimal Setup Script (Windows PowerShell)
# Run as Administrator

Write-Host "🚀 CareerCraft Minimal Setup" -ForegroundColor Green
Write-Host "============================" -ForegroundColor Green

# Check Administrator privileges
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "❌ Please run PowerShell as Administrator" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# Step 1: Install Chocolatey
Write-Host "📦 Installing Chocolatey..." -ForegroundColor Yellow
if (!(Get-Command choco -ErrorAction SilentlyContinue)) {
    Set-ExecutionPolicy Bypass -Scope Process -Force
    [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072
    iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
    Write-Host "✅ Chocolatey installed" -ForegroundColor Green
} else {
    Write-Host "✅ Chocolatey already installed" -ForegroundColor Green
}

# Step 2: Install Node.js
Write-Host "📦 Installing Node.js..." -ForegroundColor Yellow
choco install nodejs -y
Write-Host "✅ Node.js installed" -ForegroundColor Green

# Step 3: Install PostgreSQL
Write-Host "📦 Installing PostgreSQL..." -ForegroundColor Yellow
choco install postgresql --params '/Password:postgres' -y
Write-Host "✅ PostgreSQL installed" -ForegroundColor Green

# Step 4: Install Git
Write-Host "📦 Installing Git..." -ForegroundColor Yellow
choco install git -y
Write-Host "✅ Git installed" -ForegroundColor Green

# Refresh environment
Write-Host "🔄 Refreshing environment..." -ForegroundColor Yellow
refreshenv

# Step 5: Create project
$projectName = "careercraft-local"
Write-Host "📁 Creating project: $projectName" -ForegroundColor Yellow

if (Test-Path $projectName) {
    Write-Host "⚠️ Directory exists. Removing..." -ForegroundColor Yellow
    Remove-Item -Recurse -Force $projectName
}

# Create Next.js project
Write-Host "📦 Creating Next.js project (this may take a few minutes)..." -ForegroundColor Yellow
npx create-next-app@latest $projectName --typescript --tailwind --eslint --app --src-dir --import-alias "@/*"

# Navigate to project
Set-Location $projectName

# Step 6: Install basic dependencies
Write-Host "📦 Installing basic dependencies..." -ForegroundColor Yellow
npm install @prisma/client prisma
npm install next-auth
npm install stripe
npm install openai
npm install lucide-react
npm install axios
Write-Host "✅ Basic dependencies installed" -ForegroundColor Green

# Step 7: Create basic environment file
Write-Host "📝 Creating environment file..." -ForegroundColor Yellow
$envLines = @(
    "# Database",
    "DATABASE_URL=`"postgresql://careercraft_user:local_password@localhost:5432/careercraft_local`"",
    "",
    "# NextAuth",
    "NEXTAUTH_URL=`"http://localhost:3000`"",
    "NEXTAUTH_SECRET=`"your-local-secret-key`"",
    "",
    "# API Keys (Add your own)",
    "OPENAI_API_KEY=`"sk-your-openai-key`"",
    "STRIPE_SECRET_KEY=`"sk_test_your-stripe-key`"",
    "",
    "# App Settings",
    "NODE_ENV=`"development`"",
    "NEXT_PUBLIC_APP_URL=`"http://localhost:3000`""
)

$envLines | Out-File -FilePath ".env.local" -Encoding UTF8
Write-Host "✅ Environment file created" -ForegroundColor Green

# Step 8: Create basic Prisma schema
Write-Host "📋 Setting up Prisma..." -ForegroundColor Yellow
if (!(Test-Path "prisma")) {
    New-Item -ItemType Directory -Path "prisma"
}

$schemaLines = @(
    "generator client {",
    "  provider = `"prisma-client-js`"",
    "}",
    "",
    "datasource db {",
    "  provider = `"postgresql`"",
    "  url      = env(`"DATABASE_URL`")",
    "}",
    "",
    "model User {",
    "  id        String   @id @default(cuid())",
    "  email     String   @unique",
    "  name      String?",
    "  createdAt DateTime @default(now())",
    "  updatedAt DateTime @updatedAt",
    "",
    "  @@map(`"users`")",
    "}"
)

$schemaLines | Out-File -FilePath "prisma/schema.prisma" -Encoding UTF8
Write-Host "✅ Prisma schema created" -ForegroundColor Green

# Step 9: Start PostgreSQL service
Write-Host "🗄️ Starting PostgreSQL service..." -ForegroundColor Yellow
try {
    Start-Service postgresql-x64-14 -ErrorAction SilentlyContinue
    Write-Host "✅ PostgreSQL service started" -ForegroundColor Green
} catch {
    Write-Host "⚠️ Could not start PostgreSQL automatically" -ForegroundColor Yellow
    Write-Host "   Please start it manually from Windows Services" -ForegroundColor Yellow
}

# Step 10: Create database
Write-Host "👤 Creating database..." -ForegroundColor Yellow
try {
    & psql -U postgres -c "CREATE USER careercraft_user WITH PASSWORD 'local_password';" 2>$null
    & psql -U postgres -c "CREATE DATABASE careercraft_local OWNER careercraft_user;" 2>$null
    & psql -U postgres -c "GRANT ALL PRIVILEGES ON DATABASE careercraft_local TO careercraft_user;" 2>$null
    Write-Host "✅ Database created" -ForegroundColor Green
} catch {
    Write-Host "⚠️ Database creation may need manual setup" -ForegroundColor Yellow
}

# Step 11: Generate Prisma client
Write-Host "⚙️ Setting up Prisma client..." -ForegroundColor Yellow
npx prisma generate
npx prisma db push
Write-Host "✅ Prisma setup complete" -ForegroundColor Green

# Step 12: Create simple startup files
Write-Host "🚀 Creating startup files..." -ForegroundColor Yellow

# Create simple batch file
"npm run dev" | Out-File -FilePath "start.bat" -Encoding ASCII

# Create PowerShell startup script
$psScript = @(
    "Write-Host `"Starting CareerCraft Development Server`" -ForegroundColor Green",
    "Write-Host `"Visit: http://localhost:3000`" -ForegroundColor Yellow",
    "npm run dev"
)
$psScript | Out-File -FilePath "start.ps1" -Encoding UTF8

Write-Host "✅ Startup files created" -ForegroundColor Green

# Final summary
Write-Host ""
Write-Host "🎉 Minimal Setup Complete!" -ForegroundColor Green
Write-Host "===========================" -ForegroundColor Green
Write-Host ""
Write-Host "📁 Project Location: $PWD" -ForegroundColor Yellow
Write-Host ""
Write-Host "🚀 To start the application:" -ForegroundColor Yellow
Write-Host "   Option 1: start.bat" -ForegroundColor White
Write-Host "   Option 2: npm run dev" -ForegroundColor White
Write-Host ""
Write-Host "🌐 Then visit: http://localhost:3000" -ForegroundColor Yellow
Write-Host ""
Write-Host "📝 To add API keys, edit: .env.local" -ForegroundColor Yellow
Write-Host ""
Write-Host "✅ Setup completed successfully!" -ForegroundColor Green
Write-Host ""

Read-Host "Press Enter to exit"
