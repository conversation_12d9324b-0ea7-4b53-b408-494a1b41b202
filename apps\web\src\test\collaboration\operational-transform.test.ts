/**
 * Operational Transform Unit Tests
 * 
 * Tests for conflict resolution in collaborative editing
 */

import { describe, it, expect } from 'vitest'
import OperationalTransform, { Operation } from '@/lib/collaboration/operational-transform'

describe('OperationalTransform', () => {
  describe('Transform Operations', () => {
    it('should transform retain-retain operations', () => {
      const op1: Operation = { type: 'retain', length: 5 }
      const op2: Operation = { type: 'retain', length: 3 }

      const result = OperationalTransform.transform(op1, op2)

      expect(result.operation1).toEqual({ type: 'retain', length: 2 })
      expect(result.operation2).toEqual(op2)
    })

    it('should transform retain-insert operations', () => {
      const op1: Operation = { type: 'retain', length: 5 }
      const op2: Operation = { type: 'insert', text: 'hello' }

      const result = OperationalTransform.transform(op1, op2)

      expect(result.operation1).toEqual({ type: 'retain', length: 10 }) // 5 + 5 (length of 'hello')
      expect(result.operation2).toEqual(op2)
    })

    it('should transform insert-insert operations with left priority', () => {
      const op1: Operation = { type: 'insert', text: 'abc' }
      const op2: Operation = { type: 'insert', text: 'def' }

      const result = OperationalTransform.transform(op1, op2, 'left')

      expect(result.operation1).toEqual(op1)
      expect(result.operation2).toEqual({ type: 'insert', text: 'def', length: 3 })
    })

    it('should transform insert-insert operations with right priority', () => {
      const op1: Operation = { type: 'insert', text: 'abc' }
      const op2: Operation = { type: 'insert', text: 'def' }

      const result = OperationalTransform.transform(op1, op2, 'right')

      expect(result.operation1).toEqual({ type: 'insert', text: 'abc', length: 3 })
      expect(result.operation2).toEqual(op2)
    })

    it('should transform delete-delete operations', () => {
      const op1: Operation = { type: 'delete', length: 5 }
      const op2: Operation = { type: 'delete', length: 3 }

      const result = OperationalTransform.transform(op1, op2)

      expect(result.operation1).toEqual({ type: 'delete', length: 2 })
      expect(result.operation2).toEqual({ type: 'retain', length: 0 })
    })

    it('should transform delete-insert operations', () => {
      const op1: Operation = { type: 'delete', length: 3 }
      const op2: Operation = { type: 'insert', text: 'new' }

      const result = OperationalTransform.transform(op1, op2)

      expect(result.operation1).toEqual(op1)
      expect(result.operation2).toEqual(op2)
    })

    it('should transform replace operations on same path', () => {
      const op1: Operation = { 
        type: 'replace', 
        path: ['personalInfo', 'firstName'], 
        text: 'John' 
      }
      const op2: Operation = { 
        type: 'replace', 
        path: ['personalInfo', 'firstName'], 
        text: 'Jane' 
      }

      const result = OperationalTransform.transform(op1, op2)

      expect(result.operation1).toEqual({ type: 'retain', length: 0 })
      expect(result.operation2).toEqual(op2) // Later operation wins
    })

    it('should transform replace operations on different paths', () => {
      const op1: Operation = { 
        type: 'replace', 
        path: ['personalInfo', 'firstName'], 
        text: 'John' 
      }
      const op2: Operation = { 
        type: 'replace', 
        path: ['personalInfo', 'lastName'], 
        text: 'Doe' 
      }

      const result = OperationalTransform.transform(op1, op2)

      expect(result.operation1).toEqual(op1)
      expect(result.operation2).toEqual(op2)
    })
  })

  describe('Apply Operations', () => {
    it('should apply retain operation (no change)', () => {
      const document = { name: 'John', age: 30 }
      const operation: Operation = { type: 'retain', length: 0 }

      const result = OperationalTransform.apply(document, operation)

      expect(result).toEqual(document)
    })

    it('should apply insert operation to object', () => {
      const document = { personalInfo: {} }
      const operation: Operation = { 
        type: 'insert', 
        path: ['personalInfo', 'firstName'], 
        text: 'John' 
      }

      const result = OperationalTransform.apply(document, operation)

      expect(result.personalInfo.firstName).toBe('John')
    })

    it('should apply insert operation to array', () => {
      const document = { skills: ['JavaScript'] }
      const operation: Operation = { 
        type: 'insert', 
        path: ['skills', '1'], 
        text: 'React' 
      }

      const result = OperationalTransform.apply(document, operation)

      expect(result.skills).toEqual(['JavaScript', 'React'])
    })

    it('should apply insert operation to string', () => {
      const document = { description: 'Hello world' }
      const operation: Operation = { 
        type: 'insert', 
        path: ['description'], 
        text: ' beautiful',
        length: 5 // Insert at position 5
      }

      const result = OperationalTransform.apply(document, operation)

      expect(result.description).toBe('Hello beautiful world')
    })

    it('should apply delete operation from object', () => {
      const document = { 
        personalInfo: { 
          firstName: 'John', 
          lastName: 'Doe' 
        } 
      }
      const operation: Operation = { 
        type: 'delete', 
        path: ['personalInfo', 'lastName'] 
      }

      const result = OperationalTransform.apply(document, operation)

      expect(result.personalInfo.lastName).toBeUndefined()
      expect(result.personalInfo.firstName).toBe('John')
    })

    it('should apply delete operation from array', () => {
      const document = { skills: ['JavaScript', 'React', 'Node.js'] }
      const operation: Operation = { 
        type: 'delete', 
        path: ['skills', '1'], 
        length: 1 
      }

      const result = OperationalTransform.apply(document, operation)

      expect(result.skills).toEqual(['JavaScript', 'Node.js'])
    })

    it('should apply delete operation from string', () => {
      const document = { description: 'Hello beautiful world' }
      const operation: Operation = { 
        type: 'delete', 
        path: ['description'], 
        length: 10, // Start position
        length: 10  // Delete 10 characters starting from position 10
      }

      const result = OperationalTransform.apply(document, operation)

      // Note: This is a simplified test - real implementation would handle string positions better
      expect(result.description).toBeDefined()
    })

    it('should apply replace operation', () => {
      const document = { personalInfo: { firstName: 'John' } }
      const operation: Operation = { 
        type: 'replace', 
        path: ['personalInfo', 'firstName'], 
        text: 'Jane' 
      }

      const result = OperationalTransform.apply(document, operation)

      expect(result.personalInfo.firstName).toBe('Jane')
    })

    it('should handle missing paths gracefully', () => {
      const document = { personalInfo: {} }
      const operation: Operation = { 
        type: 'delete', 
        path: ['personalInfo', 'nonexistent'] 
      }

      const result = OperationalTransform.apply(document, operation)

      expect(result).toEqual(document) // Should not crash
    })
  })

  describe('Compose Operations', () => {
    it('should return retain for empty operations array', () => {
      const result = OperationalTransform.compose([])

      expect(result).toEqual({ type: 'retain', length: 0 })
    })

    it('should return single operation unchanged', () => {
      const operation: Operation = { type: 'insert', text: 'hello' }
      const result = OperationalTransform.compose([operation])

      expect(result).toEqual(operation)
    })

    it('should compose multiple insert operations', () => {
      const operations: Operation[] = [
        { type: 'insert', path: ['test'], text: 'hello' },
        { type: 'insert', path: ['test'], text: ' world' }
      ]

      const result = OperationalTransform.compose(operations)

      expect(result.type).toBe('insert')
      expect(result.text).toBe('hello world')
    })

    it('should compose multiple delete operations', () => {
      const operations: Operation[] = [
        { type: 'delete', path: ['test'], length: 3 },
        { type: 'delete', path: ['test'], length: 2 }
      ]

      const result = OperationalTransform.compose(operations)

      expect(result.type).toBe('delete')
      expect(result.length).toBe(5)
    })

    it('should compose multiple retain operations', () => {
      const operations: Operation[] = [
        { type: 'retain', length: 5 },
        { type: 'retain', length: 3 }
      ]

      const result = OperationalTransform.compose(operations)

      expect(result.type).toBe('retain')
      expect(result.length).toBe(8)
    })
  })

  describe('Create Operations', () => {
    it('should create no operations for identical documents', () => {
      const oldDoc = { name: 'John', age: 30 }
      const newDoc = { name: 'John', age: 30 }

      const operations = OperationalTransform.createOperation(oldDoc, newDoc)

      expect(operations).toEqual([])
    })

    it('should create replace operation for type change', () => {
      const oldDoc = { value: 'string' }
      const newDoc = { value: 123 }

      const operations = OperationalTransform.createOperation(oldDoc, newDoc)

      expect(operations).toHaveLength(1)
      expect(operations[0].type).toBe('replace')
      expect(operations[0].path).toEqual(['value'])
      expect(operations[0].text).toBe(123)
    })

    it('should create replace operation for string changes', () => {
      const oldDoc = { description: 'old text' }
      const newDoc = { description: 'new text' }

      const operations = OperationalTransform.createOperation(oldDoc, newDoc)

      expect(operations).toHaveLength(1)
      expect(operations[0].type).toBe('replace')
      expect(operations[0].path).toEqual(['description'])
      expect(operations[0].text).toBe('new text')
    })

    it('should create operations for array changes', () => {
      const oldDoc = { items: ['a', 'b'] }
      const newDoc = { items: ['a', 'b', 'c'] }

      const operations = OperationalTransform.createOperation(oldDoc, newDoc)

      expect(operations.length).toBeGreaterThan(0)
      expect(operations.some(op => op.type === 'insert')).toBe(true)
    })

    it('should create operations for object changes', () => {
      const oldDoc = { user: { name: 'John' } }
      const newDoc = { user: { name: 'John', age: 30 } }

      const operations = OperationalTransform.createOperation(oldDoc, newDoc)

      expect(operations.length).toBeGreaterThan(0)
      expect(operations.some(op => op.type === 'insert')).toBe(true)
    })

    it('should create delete operations for removed properties', () => {
      const oldDoc = { user: { name: 'John', age: 30 } }
      const newDoc = { user: { name: 'John' } }

      const operations = OperationalTransform.createOperation(oldDoc, newDoc)

      expect(operations.length).toBeGreaterThan(0)
      expect(operations.some(op => op.type === 'delete')).toBe(true)
    })
  })

  describe('Complex Scenarios', () => {
    it('should handle concurrent edits to different sections', () => {
      const op1: Operation = { 
        type: 'replace', 
        path: ['personalInfo', 'firstName'], 
        text: 'John' 
      }
      const op2: Operation = { 
        type: 'replace', 
        path: ['experience', '0', 'company'], 
        text: 'TechCorp' 
      }

      const result = OperationalTransform.transform(op1, op2)

      expect(result.operation1).toEqual(op1)
      expect(result.operation2).toEqual(op2)
    })

    it('should handle concurrent edits to same section', () => {
      const op1: Operation = { 
        type: 'insert', 
        path: ['skills'], 
        text: 'JavaScript' 
      }
      const op2: Operation = { 
        type: 'insert', 
        path: ['skills'], 
        text: 'React' 
      }

      const result = OperationalTransform.transform(op1, op2, 'left')

      expect(result.operation1).toEqual(op1)
      expect(result.operation2.type).toBe('insert')
    })

    it('should maintain document consistency after transforms', () => {
      const document = { text: 'Hello world' }
      
      const op1: Operation = { 
        type: 'replace', 
        path: ['text'], 
        text: 'Hello beautiful world' 
      }
      
      const result1 = OperationalTransform.apply(document, op1)
      expect(result1.text).toBe('Hello beautiful world')
      
      const op2: Operation = { 
        type: 'replace', 
        path: ['text'], 
        text: 'Goodbye beautiful world' 
      }
      
      const result2 = OperationalTransform.apply(result1, op2)
      expect(result2.text).toBe('Goodbye beautiful world')
    })
  })
})
