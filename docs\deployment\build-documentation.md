# CareerCraft Build & Deployment Documentation

## Overview
This document provides comprehensive build, deployment, and operational procedures for the CareerCraft Career Intelligence System.

## System Architecture

### Technology Stack
- **Frontend**: Next.js 14, React 18, TypeScript
- **Backend**: Next.js API Routes, Python FastAPI (Market Data Service)
- **Database**: PostgreSQL with Prisma ORM
- **Cache**: Redis
- **AI Services**: OpenAI GPT-4, text-embedding-ada-002
- **Authentication**: NextAuth.js
- **Styling**: Tailwind CSS, Radix UI
- **Testing**: Vitest, Testing Library
- **Deployment**: Vercel (Frontend), Docker (Backend Services)

### Service Architecture
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web App       │    │ Market Data     │    │   Database      │
│   (Next.js)     │◄──►│   Service       │◄──►│  (PostgreSQL)   │
│   Port: 3000    │    │   (Python)      │    │   Port: 5432    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│     Redis       │    │    OpenAI       │    │   File Storage  │
│   (Cache)       │    │     API         │    │   (Optional)    │
│   Port: 6379    │    │   (External)    │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## Prerequisites

### Development Environment
- **Node.js**: v18.17.0 or higher
- **Python**: v3.9 or higher
- **PostgreSQL**: v14 or higher
- **Redis**: v6 or higher
- **Git**: Latest version
- **Docker**: v20.10 or higher (optional)

### Required Accounts & API Keys
- **OpenAI API Key**: For AI-powered features
- **Database URL**: PostgreSQL connection string
- **Redis URL**: Redis connection string
- **NextAuth Secret**: For authentication
- **GitHub OAuth**: For social login (optional)
- **Google OAuth**: For social login (optional)

## Environment Configuration

### Environment Variables

#### Web Application (.env.local)
```bash
# Database
DATABASE_URL="postgresql://username:password@localhost:5432/careercraft"
DIRECT_URL="postgresql://username:password@localhost:5432/careercraft"

# Authentication
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-nextauth-secret-key"

# OAuth Providers (Optional)
GITHUB_CLIENT_ID="your-github-client-id"
GITHUB_CLIENT_SECRET="your-github-client-secret"
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"

# OpenAI
OPENAI_API_KEY="your-openai-api-key"

# Redis
REDIS_URL="redis://localhost:6379"

# Application
NODE_ENV="development"
NEXT_PUBLIC_APP_URL="http://localhost:3000"
```

#### Market Data Service (.env)
```bash
# Database
DATABASE_URL="postgresql://username:password@localhost:5432/careercraft"

# OpenAI
OPENAI_API_KEY="your-openai-api-key"
OPENAI_MODEL="gpt-4"
OPENAI_EMBEDDING_MODEL="text-embedding-ada-002"

# Redis
REDIS_URL="redis://localhost:6379"

# Service Configuration
ENABLE_AI_PROCESSING="true"
ENABLE_VECTORIZATION="true"
LOG_LEVEL="INFO"

# Scraping Configuration
MAX_CONCURRENT_SCRAPERS="3"
DEFAULT_MAX_PAGES="5"
DEFAULT_RATE_LIMIT="1.5"

# LinkedIn Scraper
LINKEDIN_SCRAPER_ENABLED="true"
LINKEDIN_MAX_PAGES="5"
LINKEDIN_RATE_LIMIT="2.0"

# Indeed Scraper
INDEED_SCRAPER_ENABLED="true"
INDEED_MAX_PAGES="5"
INDEED_RATE_LIMIT="1.5"

# Company Scraper
COMPANY_SCRAPER_ENABLED="true"
COMPANY_MAX_PAGES="3"
COMPANY_RATE_LIMIT="2.0"
```

## Build Process

### Web Application Build

#### Development Build
```bash
# Navigate to web app directory
cd apps/web

# Install dependencies
npm install

# Generate Prisma client
npx prisma generate

# Run database migrations
npx prisma db push

# Start development server
npm run dev
```

#### Production Build
```bash
# Install dependencies
npm ci

# Generate Prisma client
npx prisma generate

# Build application
npm run build

# Start production server
npm start
```

### Market Data Service Build

#### Development Setup
```bash
# Navigate to service directory
cd services/market-data-service

# Create virtual environment
python -m venv venv

# Activate virtual environment
# Windows:
venv\Scripts\activate
# macOS/Linux:
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt

# Run service
python src/main.py
```

#### Production Build
```bash
# Install production dependencies
pip install -r requirements.txt --no-dev

# Run with production settings
python src/main.py --env production
```

## Database Setup

### Initial Setup
```bash
# Install PostgreSQL (if not already installed)
# Create database
createdb careercraft

# Navigate to web app directory
cd apps/web

# Generate Prisma client
npx prisma generate

# Run migrations
npx prisma db push

# Seed database (optional)
npx prisma db seed
```

### Database Migrations
```bash
# Create new migration
npx prisma migrate dev --name migration_name

# Apply migrations to production
npx prisma migrate deploy

# Reset database (development only)
npx prisma migrate reset
```

### Database Schema
```sql
-- Key tables created by Prisma migrations
CREATE TABLE "User" (
  "id" TEXT PRIMARY KEY,
  "email" TEXT UNIQUE NOT NULL,
  "name" TEXT,
  "createdAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE "Resume" (
  "id" TEXT PRIMARY KEY,
  "userId" TEXT NOT NULL,
  "fileName" TEXT NOT NULL,
  "content" TEXT NOT NULL,
  "createdAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY ("userId") REFERENCES "User"("id")
);

CREATE TABLE "JobPosting" (
  "id" TEXT PRIMARY KEY,
  "title" TEXT NOT NULL,
  "company" TEXT NOT NULL,
  "description" TEXT NOT NULL,
  "location" TEXT,
  "salaryMin" INTEGER,
  "salaryMax" INTEGER,
  "skills" TEXT[],
  "experienceLevel" TEXT,
  "jobType" TEXT,
  "industry" TEXT,
  "source" TEXT NOT NULL,
  "externalId" TEXT,
  "externalUrl" TEXT,
  "isActive" BOOLEAN DEFAULT true,
  "scrapedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  "createdAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Additional tables for UserProfileVector, MarketAnalysis, JobMatch
```

## Testing

### Unit Tests
```bash
# Run all tests
npm run test

# Run tests with coverage
npm run test:coverage

# Run tests in watch mode
npm run test:watch

# Run specific test file
npm run test profile-vectorization.test.ts
```

### Integration Tests
```bash
# Run integration tests
npm run test:integration

# Run end-to-end tests
npm run test:e2e
```

### Python Service Tests
```bash
cd services/market-data-service

# Install test dependencies
pip install pytest pytest-asyncio pytest-cov

# Run tests
python -m pytest tests/ -v

# Run with coverage
python -m pytest tests/ --cov=src --cov-report=html
```

## Deployment

### Vercel Deployment (Web Application)

#### Automatic Deployment
```bash
# Connect GitHub repository to Vercel
# Automatic deployments on push to main branch

# Manual deployment
npx vercel --prod
```

#### Environment Variables (Vercel)
```bash
# Set in Vercel dashboard or CLI
vercel env add DATABASE_URL
vercel env add NEXTAUTH_SECRET
vercel env add OPENAI_API_KEY
vercel env add REDIS_URL
```

### Docker Deployment

#### Web Application Dockerfile
```dockerfile
FROM node:18-alpine AS base

# Install dependencies only when needed
FROM base AS deps
WORKDIR /app
COPY package.json package-lock.json ./
RUN npm ci

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .
RUN npm run build

# Production image
FROM base AS runner
WORKDIR /app
ENV NODE_ENV production
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/public ./public
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs
EXPOSE 3000
ENV PORT 3000
CMD ["node", "server.js"]
```

#### Market Data Service Dockerfile
```dockerfile
FROM python:3.9-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# Install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY src/ ./src/

# Create non-root user
RUN useradd --create-home --shell /bin/bash app
USER app

# Expose port
EXPOSE 8000

# Run application
CMD ["python", "src/main.py"]
```

#### Docker Compose
```yaml
version: '3.8'

services:
  web:
    build: ./apps/web
    ports:
      - "3000:3000"
    environment:
      - DATABASE_URL=**************************************/careercraft
      - REDIS_URL=redis://redis:6379
      - OPENAI_API_KEY=${OPENAI_API_KEY}
    depends_on:
      - db
      - redis

  market-data-service:
    build: ./services/market-data-service
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=**************************************/careercraft
      - REDIS_URL=redis://redis:6379
      - OPENAI_API_KEY=${OPENAI_API_KEY}
    depends_on:
      - db
      - redis

  db:
    image: postgres:14
    environment:
      - POSTGRES_DB=careercraft
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

  redis:
    image: redis:6-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

volumes:
  postgres_data:
  redis_data:
```

### Production Deployment Commands
```bash
# Build and deploy with Docker Compose
docker-compose up -d --build

# Scale services
docker-compose up -d --scale market-data-service=3

# View logs
docker-compose logs -f web
docker-compose logs -f market-data-service

# Stop services
docker-compose down
```

## Monitoring & Maintenance

### Health Checks
```bash
# Web application health
curl http://localhost:3000/api/health

# Market data service health
curl http://localhost:8000/health

# Database connection
npx prisma db ping
```

### Logging
- **Application Logs**: Structured JSON logging
- **Error Tracking**: Sentry integration (optional)
- **Performance Monitoring**: Built-in Next.js analytics
- **Database Logs**: PostgreSQL query logging

### Backup Procedures
```bash
# Database backup
pg_dump careercraft > backup_$(date +%Y%m%d_%H%M%S).sql

# Restore database
psql careercraft < backup_file.sql

# Redis backup
redis-cli BGSAVE
```

### Performance Optimization
- **Database Indexing**: Optimized queries with proper indexes
- **Caching**: Redis caching for frequently accessed data
- **CDN**: Static asset delivery optimization
- **Image Optimization**: Next.js automatic image optimization

## Security

### Security Measures
- **Authentication**: NextAuth.js with secure session management
- **Authorization**: Role-based access control
- **Data Encryption**: Encrypted data at rest and in transit
- **API Security**: Rate limiting and input validation
- **Environment Security**: Secure environment variable management

### Security Checklist
- ✅ Environment variables secured
- ✅ Database connections encrypted
- ✅ API endpoints protected
- ✅ User input sanitized
- ✅ HTTPS enforced in production
- ✅ Security headers configured
- ✅ Dependencies regularly updated

## Troubleshooting

### Common Issues
1. **Database Connection**: Check DATABASE_URL and network connectivity
2. **OpenAI API**: Verify API key and rate limits
3. **Redis Connection**: Ensure Redis service is running
4. **Build Failures**: Check Node.js version and dependencies
5. **Migration Issues**: Verify database schema and permissions

### Debug Commands
```bash
# Check application logs
npm run logs

# Database connection test
npx prisma db ping

# Redis connection test
redis-cli ping

# Environment variables check
npm run env:check
```

## Support & Documentation

### Additional Resources
- **API Documentation**: `/docs/api/`
- **Architecture Documentation**: `/docs/architecture/`
- **Testing Documentation**: `/docs/testing/`
- **User Guide**: `/docs/user-guide/`

### Contact Information
- **Development Team**: [<EMAIL>]
- **Technical Support**: [<EMAIL>]
- **Documentation**: [docs.careercraft.com]
