#!/bin/bash

# CareerCraft Local Development Setup Script
# This script sets up the complete development environment

set -e

echo "🚀 Setting up CareerCraft for local development..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    print_error "Please run this script from the careercraft-v2 root directory"
    exit 1
fi

# Check Node.js version
print_status "Checking Node.js version..."
NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    print_error "Node.js 18 or higher is required. Current version: $(node --version)"
    exit 1
fi
print_success "Node.js version: $(node --version)"

# Check if .env.local exists
if [ ! -f ".env.local" ]; then
    print_status "Creating .env.local file..."
    cat > .env.local << 'EOF'
# Database
DATABASE_URL="postgresql://postgres:password@localhost:5432/careercraft_dev"

# NextAuth.js
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-secret-key-change-this-in-production"

# OAuth Providers (Optional - for testing auth)
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"
GITHUB_ID="your-github-client-id"
GITHUB_SECRET="your-github-client-secret"

# AI Services (Optional - for testing AI features)
OPENAI_API_KEY="your-openai-api-key"

# Email (Optional - for testing email features)
SMTP_HOST="smtp.gmail.com"
SMTP_PORT="587"
SMTP_USER="<EMAIL>"
SMTP_PASS="your-app-password"

# File Storage (Optional - for testing file uploads)
AWS_ACCESS_KEY_ID="your-aws-access-key"
AWS_SECRET_ACCESS_KEY="your-aws-secret-key"
AWS_REGION="us-east-1"
AWS_S3_BUCKET="your-s3-bucket"

# Analytics (Optional)
GOOGLE_ANALYTICS_ID="your-ga-id"

# Feature Flags
ENABLE_AI_FEATURES="true"
ENABLE_PREMIUM_FEATURES="true"
ENABLE_ANALYTICS="false"
EOF
    print_success "Created .env.local file. Please update with your actual credentials."
    print_warning "The app will work with mock data even without real API keys."
else
    print_success ".env.local file already exists"
fi

# Install dependencies
print_status "Installing dependencies..."
npm install
print_success "Dependencies installed"

# Setup database (if PostgreSQL is available)
print_status "Checking database setup..."
if command -v psql &> /dev/null; then
    print_status "PostgreSQL found. Setting up database..."
    
    # Check if database exists
    if psql -lqt | cut -d \| -f 1 | grep -qw careercraft_dev; then
        print_success "Database 'careercraft_dev' already exists"
    else
        print_status "Creating database 'careercraft_dev'..."
        createdb careercraft_dev || print_warning "Could not create database. Please create manually."
    fi
    
    # Run migrations
    print_status "Running database migrations..."
    npm run db:push || print_warning "Database migration failed. Check your DATABASE_URL."
    
    # Seed database
    print_status "Seeding database with sample data..."
    npm run db:seed || print_warning "Database seeding failed."
    
    print_success "Database setup complete"
else
    print_warning "PostgreSQL not found. Using SQLite for development."
    # Update DATABASE_URL for SQLite
    sed -i.bak 's|DATABASE_URL="postgresql://.*"|DATABASE_URL="file:./dev.db"|' .env.local
    
    print_status "Running database migrations with SQLite..."
    npm run db:push
    npm run db:seed
    print_success "SQLite database setup complete"
fi

# Build the project
print_status "Building the project..."
npm run build:packages
print_success "Project built successfully"

# Run tests to verify everything works
print_status "Running comprehensive tests..."

echo ""
echo "🧪 Testing Database Layer..."
npm run test:db || print_warning "Database tests failed"

echo ""
echo "🔐 Testing Authentication..."
npm run test:auth || print_warning "Auth tests failed"

echo ""
echo "🎨 Testing UI Components..."
npm run test:ui || print_warning "UI tests failed"

echo ""
echo "📄 Testing Resume Builder..."
npm run test:resume || print_warning "Resume builder tests failed"

echo ""
echo "🎨 Testing Template System..."
npm run test:templates || print_warning "Template tests failed"

echo ""
echo "🤖 Testing AI Features..."
npm run test:ai || print_warning "AI tests failed"

print_success "Test suite completed"

# Create sample data script
print_status "Creating sample data for testing..."
cat > scripts/create-sample-data.ts << 'EOF'
#!/usr/bin/env tsx

import { PrismaClient } from '@careercraft/database';
import { hash } from 'bcryptjs';

const prisma = new PrismaClient();

async function createSampleData() {
  console.log('🌱 Creating sample data...');

  // Create sample user
  const hashedPassword = await hash('password123', 12);
  
  const user = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Demo User',
      accounts: {
        create: {
          type: 'credentials',
          provider: 'credentials',
          providerAccountId: 'demo-user',
        },
      },
    },
  });

  // Create sample resume
  const resume = await prisma.resume.upsert({
    where: { id: 'demo-resume-1' },
    update: {},
    create: {
      id: 'demo-resume-1',
      userId: user.id,
      title: 'Software Engineer Resume',
      description: 'My professional software engineering resume',
      status: 'DRAFT',
    },
  });

  console.log('✅ Sample data created successfully!');
  console.log('📧 Demo user email: <EMAIL>');
  console.log('🔑 Demo user password: password123');
}

createSampleData()
  .catch(console.error)
  .finally(() => prisma.$disconnect());
EOF

npm run tsx scripts/create-sample-data.ts || print_warning "Could not create sample data"

echo ""
echo "🎉 Setup complete! Here's what you can do next:"
echo ""
echo "1. Start the development server:"
echo "   npm run dev"
echo ""
echo "2. Open your browser and visit:"
echo "   http://localhost:3000"
echo ""
echo "3. Test the features:"
echo "   • Homepage: http://localhost:3000"
echo "   • Templates: http://localhost:3000/templates"
echo "   • AI Demo: http://localhost:3000/ai-demo"
echo "   • Dashboard: http://localhost:3000/dashboard"
echo ""
echo "4. Login with demo account:"
echo "   Email: <EMAIL>"
echo "   Password: password123"
echo ""
echo "5. Run individual tests:"
echo "   npm run test:db     # Database tests"
echo "   npm run test:auth   # Authentication tests"
echo "   npm run test:ui     # UI component tests"
echo "   npm run test:resume # Resume builder tests"
echo "   npm run test:templates # Template system tests"
echo "   npm run test:ai     # AI features tests"
echo ""
print_success "Happy coding! 🚀"
EOF
