/**
 * Market Intelligence API
 * Implements comprehensive market analysis endpoints
 * Part of Milestone 1.3: Market Analysis Engine
 */

import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { MarketAnalysisEngine } from '@/lib/market-analysis/market-analysis-engine'
import { logger } from '@/lib/logger'
import { rateLimit } from '@/lib/rate-limit'

const marketEngine = new MarketAnalysisEngine()

export async function GET(request: NextRequest) {
  try {
    // Rate limiting
    const rateLimitResult = await rateLimit(request)
    if (!rateLimitResult.success) {
      return NextResponse.json(
        { error: 'Rate limit exceeded' },
        { status: 429 }
      )
    }

    // Get session for user context
    const session = await getServerSession(authOptions)
    const userId = session?.user?.id

    // Parse query parameters
    const { searchParams } = new URL(request.url)
    const analysisType = searchParams.get('type') as 'REAL_TIME' | 'HISTORICAL' | 'PREDICTIVE' | 'COMPARATIVE' || 'REAL_TIME'
    const targetRole = searchParams.get('role')
    const targetLocation = searchParams.get('location')
    const targetSkills = searchParams.get('skills')?.split(',').filter(Boolean)
    const timeframe = searchParams.get('timeframe') || '30d'

    logger.info('Market intelligence request', {
      userId,
      analysisType,
      targetRole,
      targetLocation,
      targetSkills,
      timeframe
    })

    // Generate market analysis
    const analysis = await marketEngine.generateMarketAnalysis({
      userId,
      analysisType,
      targetRole,
      targetLocation,
      targetSkills,
      timeframe
    })

    return NextResponse.json({
      success: true,
      data: analysis,
      meta: {
        generatedAt: analysis.generatedAt,
        confidence: analysis.confidence,
        expiresAt: analysis.expiresAt
      }
    })

  } catch (error) {
    logger.error('Market intelligence API error', { error })
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to generate market analysis',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    // Rate limiting
    const rateLimitResult = await rateLimit(request)
    if (!rateLimitResult.success) {
      return NextResponse.json(
        { error: 'Rate limit exceeded' },
        { status: 429 }
      )
    }

    // Get session for user context
    const session = await getServerSession(authOptions)
    const userId = session?.user?.id

    if (!userId) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Parse request body
    const body = await request.json()
    const {
      analysisType = 'REAL_TIME',
      targetRole,
      targetLocation,
      targetSkills,
      timeframe = '30d',
      customFilters
    } = body

    // Validate input
    if (!['REAL_TIME', 'HISTORICAL', 'PREDICTIVE', 'COMPARATIVE'].includes(analysisType)) {
      return NextResponse.json(
        { error: 'Invalid analysis type' },
        { status: 400 }
      )
    }

    logger.info('Custom market analysis request', {
      userId,
      analysisType,
      targetRole,
      targetLocation,
      targetSkills,
      timeframe,
      customFilters
    })

    // Generate custom market analysis
    const analysis = await marketEngine.generateMarketAnalysis({
      userId,
      analysisType,
      targetRole,
      targetLocation,
      targetSkills,
      timeframe,
      ...customFilters
    })

    return NextResponse.json({
      success: true,
      data: analysis,
      meta: {
        generatedAt: analysis.generatedAt,
        confidence: analysis.confidence,
        expiresAt: analysis.expiresAt,
        customFilters: customFilters || null
      }
    })

  } catch (error) {
    logger.error('Custom market analysis API error', { error })
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to generate custom market analysis',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    // Rate limiting
    const rateLimitResult = await rateLimit(request)
    if (!rateLimitResult.success) {
      return NextResponse.json(
        { error: 'Rate limit exceeded' },
        { status: 429 }
      )
    }

    // Get session for user context
    const session = await getServerSession(authOptions)
    const userId = session?.user?.id

    if (!userId) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Parse request body
    const body = await request.json()
    const { analysisId, feedback, rating } = body

    if (!analysisId) {
      return NextResponse.json(
        { error: 'Analysis ID required' },
        { status: 400 }
      )
    }

    logger.info('Market analysis feedback', {
      userId,
      analysisId,
      rating,
      hasFeedback: !!feedback
    })

    // Store feedback (simplified implementation)
    // In a real implementation, this would update the analysis record
    // and potentially use the feedback to improve future analyses

    return NextResponse.json({
      success: true,
      message: 'Feedback recorded successfully'
    })

  } catch (error) {
    logger.error('Market analysis feedback API error', { error })
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to record feedback',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      },
      { status: 500 }
    )
  }
}
