/**
 * Version Control Features Validation Script
 * 
 * Validates version control and resume history implementation
 */

const fs = require('fs')
const path = require('path')

class VersionControlFeaturesValidator {
  constructor() {
    this.results = {
      files: { checked: 0, missing: 0, present: 0 },
      services: { checked: 0, missing: 0, present: 0 },
      apis: { checked: 0, missing: 0, present: 0 },
      components: { checked: 0, missing: 0, present: 0 },
      tests: { checked: 0, missing: 0, present: 0 },
      config: { checked: 0, missing: 0, present: 0 }
    }
  }

  log(message, type = 'info') {
    const colors = {
      info: '\x1b[36m',    // Cyan
      success: '\x1b[32m', // Green
      error: '\x1b[31m',   // Red
      warning: '\x1b[33m', // Yellow
      reset: '\x1b[0m'     // Reset
    }
    
    console.log(`${colors[type]}${message}${colors.reset}`)
  }

  checkFileExists(filePath, description) {
    const fullPath = path.join(__dirname, filePath)
    const exists = fs.existsSync(fullPath)
    
    if (exists) {
      this.log(`✅ ${description}: Found`, 'success')
      return true
    } else {
      this.log(`❌ ${description}: Missing (${filePath})`, 'error')
      return false
    }
  }

  checkFileContent(filePath, searchTerms, description) {
    const fullPath = path.join(__dirname, filePath)
    
    if (!fs.existsSync(fullPath)) {
      this.log(`❌ ${description}: File not found`, 'error')
      return false
    }

    try {
      const content = fs.readFileSync(fullPath, 'utf8')
      const missingTerms = searchTerms.filter(term => !content.includes(term))
      
      if (missingTerms.length === 0) {
        this.log(`✅ ${description}: All required content found`, 'success')
        return true
      } else {
        this.log(`⚠️  ${description}: Missing content: ${missingTerms.join(', ')}`, 'warning')
        return false
      }
    } catch (error) {
      this.log(`❌ ${description}: Error reading file - ${error.message}`, 'error')
      return false
    }
  }

  validateFileStructure() {
    this.log('\n📚 Validating Version Control File Structure...', 'info')
    
    const requiredFiles = [
      'src/lib/version-control/diff-engine.ts',
      'src/lib/version-control/service.ts',
      'src/app/api/version-control/versions/route.ts',
      'src/app/api/version-control/backups/route.ts',
      'src/components/version-control/VersionHistory.tsx',
      'src/components/version-control/BackupManager.tsx',
      'src/test/version-control/diff-engine.test.ts',
      'src/test/version-control/version-control-service.test.ts',
      'src/test/version-control/version-control-api.test.ts',
      'src/test/version-control/version-control-components.test.tsx',
      'docs/features/version-control-spec.md'
    ]

    let present = 0
    let total = requiredFiles.length

    requiredFiles.forEach(file => {
      this.results.files.checked++
      if (this.checkFileExists(file, `File: ${file}`)) {
        present++
        this.results.files.present++
      } else {
        this.results.files.missing++
      }
    })

    this.log(`📊 File Structure: ${present}/${total} files present`, present === total ? 'success' : 'warning')
    return present === total
  }

  validateVersionControlServices() {
    this.log('\n🔧 Validating Version Control Services...', 'info')
    
    const serviceChecks = [
      {
        file: 'src/lib/version-control/diff-engine.ts',
        description: 'Diff Engine Implementation',
        requiredContent: ['DiffEngine', 'calculateDiff', 'applyDiff', 'reverseDiff', 'mergeDiffs']
      },
      {
        file: 'src/lib/version-control/service.ts',
        description: 'Version Control Service Implementation',
        requiredContent: ['VersionControlService', 'createVersion', 'getVersions', 'compareVersions', 'rollbackToVersion', 'createBackup']
      }
    ]

    let passed = 0
    let total = serviceChecks.length

    serviceChecks.forEach(check => {
      this.results.services.checked++
      if (this.checkFileContent(check.file, check.requiredContent, check.description)) {
        passed++
        this.results.services.present++
      } else {
        this.results.services.missing++
      }
    })

    this.log(`📊 Version Control Services: ${passed}/${total} checks passed`, passed === total ? 'success' : 'warning')
    return passed === total
  }

  validateVersionControlAPIs() {
    this.log('\n🌐 Validating Version Control API Routes...', 'info')
    
    const apiChecks = [
      {
        file: 'src/app/api/version-control/versions/route.ts',
        description: 'Version Control Versions API',
        requiredContent: ['GET', 'POST', 'DELETE', 'createVersion', 'rollbackToVersion', 'compareVersions']
      },
      {
        file: 'src/app/api/version-control/backups/route.ts',
        description: 'Version Control Backups API',
        requiredContent: ['GET', 'POST', 'createBackup', 'restoreFromBackup', 'getBackups']
      }
    ]

    let passed = 0
    let total = apiChecks.length

    apiChecks.forEach(check => {
      this.results.apis.checked++
      if (this.checkFileContent(check.file, check.requiredContent, check.description)) {
        passed++
        this.results.apis.present++
      } else {
        this.results.apis.missing++
      }
    })

    this.log(`📊 Version Control APIs: ${passed}/${total} checks passed`, passed === total ? 'success' : 'warning')
    return passed === total
  }

  validateVersionControlComponents() {
    this.log('\n⚛️  Validating Version Control Components...', 'info')
    
    const componentChecks = [
      {
        file: 'src/components/version-control/VersionHistory.tsx',
        description: 'Version History Component',
        requiredContent: ['VersionHistory', 'handleVersionClick', 'handleCompareVersions', 'handleRollbackClick']
      },
      {
        file: 'src/components/version-control/BackupManager.tsx',
        description: 'Backup Manager Component',
        requiredContent: ['BackupManager', 'handleCreateBackup', 'handleRestoreBackup', 'loadBackups']
      }
    ]

    let passed = 0
    let total = componentChecks.length

    componentChecks.forEach(check => {
      this.results.components.checked++
      if (this.checkFileContent(check.file, check.requiredContent, check.description)) {
        passed++
        this.results.components.present++
      } else {
        this.results.components.missing++
      }
    })

    this.log(`📊 Version Control Components: ${passed}/${total} checks passed`, passed === total ? 'success' : 'warning')
    return passed === total
  }

  validateVersionControlTests() {
    this.log('\n🧪 Validating Version Control Test Files...', 'info')
    
    const testChecks = [
      {
        file: 'src/test/version-control/diff-engine.test.ts',
        description: 'Diff Engine Tests',
        requiredContent: ['describe', 'it', 'expect', 'DiffEngine', 'calculateDiff', 'applyDiff']
      },
      {
        file: 'src/test/version-control/version-control-service.test.ts',
        description: 'Version Control Service Tests',
        requiredContent: ['describe', 'it', 'expect', 'VersionControlService', 'createVersion', 'rollbackToVersion']
      },
      {
        file: 'src/test/version-control/version-control-api.test.ts',
        description: 'Version Control API Tests',
        requiredContent: ['describe', 'it', 'expect', 'NextRequest', 'version-control/versions']
      },
      {
        file: 'src/test/version-control/version-control-components.test.tsx',
        description: 'Version Control Component Tests',
        requiredContent: ['describe', 'it', 'expect', 'render', 'VersionHistory', 'BackupManager']
      }
    ]

    let passed = 0
    let total = testChecks.length

    testChecks.forEach(check => {
      this.results.tests.checked++
      if (this.checkFileContent(check.file, check.requiredContent, check.description)) {
        passed++
        this.results.tests.present++
      } else {
        this.results.tests.missing++
      }
    })

    this.log(`📊 Version Control Tests: ${passed}/${total} checks passed`, passed === total ? 'success' : 'warning')
    return passed === total
  }

  validateConfiguration() {
    this.log('\n⚙️  Validating Version Control Configuration...', 'info')
    
    const configChecks = [
      {
        file: 'package.json',
        description: 'Package.json Version Control Scripts',
        requiredContent: ['test:version-control', 'test:version-control:diff', 'test:version-control:service']
      },
      {
        file: 'packages/database/prisma/schema.prisma',
        description: 'Database Schema',
        requiredContent: ['ResumeVersion', 'VersionComparison', 'ResumeBackup', 'VersionActivity']
      }
    ]

    let passed = 0
    let total = configChecks.length

    configChecks.forEach(check => {
      this.results.config.checked++
      if (this.checkFileContent(check.file, check.requiredContent, check.description)) {
        passed++
        this.results.config.present++
      } else {
        this.results.config.missing++
      }
    })

    this.log(`📊 Version Control Configuration: ${passed}/${total} checks passed`, passed === total ? 'success' : 'warning')
    return passed === total
  }

  generateReport() {
    this.log('\n📊 Version Control Features Validation Report', 'info')
    this.log('=' .repeat(60), 'info')
    
    const categories = ['files', 'services', 'apis', 'components', 'tests', 'config']
    let totalChecked = 0
    let totalPresent = 0
    let totalMissing = 0

    categories.forEach(category => {
      const result = this.results[category]
      totalChecked += result.checked
      totalPresent += result.present
      totalMissing += result.missing

      const status = result.missing === 0 ? '✅' : '⚠️ '
      const categoryName = category.charAt(0).toUpperCase() + category.slice(1)
      
      this.log(
        `${status} ${categoryName}: ${result.present}/${result.checked} present`,
        result.missing === 0 ? 'success' : 'warning'
      )
    })

    this.log('=' .repeat(60), 'info')
    this.log(`📈 Overall: ${totalPresent}/${totalChecked} checks passed`, 
             totalMissing === 0 ? 'success' : 'warning')
    
    const completionRate = totalChecked > 0 ? (totalPresent / totalChecked * 100).toFixed(1) : 0
    this.log(`📊 Completion Rate: ${completionRate}%`, 
             completionRate >= 90 ? 'success' : completionRate >= 70 ? 'warning' : 'error')

    // Recommendations
    this.log('\n💡 Version Control Features Status:', 'info')
    if (totalMissing === 0) {
      this.log('🎉 All version control features are properly implemented!', 'success')
      this.log('✨ Ready for resume version tracking and history management', 'success')
    } else {
      this.log(`🔧 ${totalMissing} items need attention`, 'warning')
      if (this.results.files.missing > 0) {
        this.log('📁 Create missing files and implement required functionality', 'warning')
      }
      if (this.results.tests.missing > 0) {
        this.log('🧪 Add comprehensive tests for version control features', 'warning')
      }
      if (this.results.config.missing > 0) {
        this.log('⚙️  Update configuration files for version control', 'warning')
      }
    }

    return totalMissing === 0
  }

  async validate() {
    this.log('🚀 Starting Version Control Features Validation...', 'info')
    this.log('📅 ' + new Date().toISOString(), 'info')
    
    const results = [
      this.validateFileStructure(),
      this.validateVersionControlServices(),
      this.validateVersionControlAPIs(),
      this.validateVersionControlComponents(),
      this.validateVersionControlTests(),
      this.validateConfiguration()
    ]

    const allValid = this.generateReport()
    
    this.log('\n🏁 Version Control Validation Complete!', 'info')
    return allValid
  }
}

// Run validation if this script is executed directly
if (require.main === module) {
  const validator = new VersionControlFeaturesValidator()
  validator.validate()
    .then(success => {
      process.exit(success ? 0 : 1)
    })
    .catch(error => {
      console.error('Validation failed:', error)
      process.exit(1)
    })
}

module.exports = VersionControlFeaturesValidator
