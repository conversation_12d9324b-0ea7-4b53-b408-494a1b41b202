# CareerCraft Deployment Guide
## Hosting on careercraft.onlinejobsearchhelp.com

### Overview
This guide will help you deploy CareerCraft to your subdomain `careercraft.onlinejobsearchhelp.com` as part of your WordPress website `onlinejobsearchhelp.com`.

## Deployment Options

### Option 1: Static HTML Deployment (Recommended for Quick Start)

#### Files to Upload:
- `enhanced-demo.html` (rename to `index.html`)
- Any additional assets (CSS, JS, images)

#### Steps:
1. **Create Subdomain Directory**
   ```bash
   # On your server, create directory structure
   /public_html/careercraft/
   ```

2. **Upload Files**
   - Upload `enhanced-demo.html` as `index.html`
   - Ensure proper file permissions (644 for files, 755 for directories)

3. **Configure Subdomain**
   - In cPanel/hosting panel, create subdomain `careercraft`
   - Point it to `/public_html/careercraft/`

### Option 2: Next.js Application Deployment

#### Prerequisites:
- Node.js 18+ on server
- PM2 or similar process manager
- Reverse proxy (Nginx/Apache)

#### Steps:
1. **Prepare Application**
   ```bash
   cd careercraft-working
   npm run build
   npm run export  # For static export
   ```

2. **Upload Build Files**
   - Upload `out/` directory contents to `/public_html/careercraft/`

3. **Configure Server**
   - Set up reverse proxy if using server-side rendering
   - Configure environment variables

## DNS Configuration

### Subdomain Setup:
1. **A Record**: Point `careercraft.onlinejobsearchhelp.com` to your server IP
2. **CNAME**: Alternative - point to `onlinejobsearchhelp.com`

### SSL Certificate:
- Ensure SSL covers subdomain
- Use Let's Encrypt or your hosting provider's SSL

## WordPress Integration

### Option 1: Iframe Integration
Add to WordPress page/post:
```html
<iframe src="https://careercraft.onlinejobsearchhelp.com" 
        width="100%" 
        height="800px" 
        frameborder="0">
</iframe>
```

### Option 2: Direct Link
Create menu item or button linking to subdomain:
```html
<a href="https://careercraft.onlinejobsearchhelp.com" 
   class="btn btn-primary">
   Launch CareerCraft
</a>
```

## Server Requirements

### Minimum Requirements:
- **Storage**: 100MB
- **Bandwidth**: 1GB/month (for moderate traffic)
- **PHP**: 7.4+ (if using WordPress integration)
- **Database**: MySQL 5.7+ (for user data storage)

### Recommended:
- **Storage**: 500MB
- **Bandwidth**: 5GB/month
- **CDN**: CloudFlare or similar
- **Backup**: Daily automated backups

## Performance Optimization

### 1. Enable Compression
```apache
# .htaccess
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>
```

### 2. Browser Caching
```apache
# .htaccess
<IfModule mod_expires.c>
    ExpiresActive on
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
</IfModule>
```

### 3. CDN Integration
- Use CloudFlare for global content delivery
- Optimize images with WebP format
- Minify CSS and JavaScript

## Security Considerations

### 1. HTTPS Enforcement
```apache
# .htaccess
RewriteEngine On
RewriteCond %{HTTPS} off
RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
```

### 2. Security Headers
```apache
# .htaccess
Header always set X-Content-Type-Options nosniff
Header always set X-Frame-Options DENY
Header always set X-XSS-Protection "1; mode=block"
Header always set Strict-Transport-Security "max-age=63072000; includeSubDomains; preload"
```

## Monitoring & Analytics

### 1. Google Analytics
Add to `<head>` section:
```html
<!-- Google Analytics -->
<script async src="https://www.googletagmanager.com/gtag/js?id=GA_MEASUREMENT_ID"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'GA_MEASUREMENT_ID');
</script>
```

### 2. Uptime Monitoring
- Use UptimeRobot or similar service
- Monitor subdomain availability
- Set up alerts for downtime

## Backup Strategy

### 1. File Backup
```bash
# Daily backup script
#!/bin/bash
DATE=$(date +%Y%m%d)
tar -czf /backups/careercraft_$DATE.tar.gz /public_html/careercraft/
```

### 2. Database Backup (if applicable)
```bash
# MySQL backup
mysqldump -u username -p database_name > careercraft_backup_$DATE.sql
```

## Troubleshooting

### Common Issues:

1. **Subdomain not resolving**
   - Check DNS propagation (24-48 hours)
   - Verify A record configuration

2. **SSL certificate issues**
   - Ensure certificate covers subdomain
   - Check certificate chain

3. **File permission errors**
   - Set correct permissions: 644 for files, 755 for directories
   - Check ownership settings

4. **WordPress conflicts**
   - Ensure subdomain doesn't conflict with WordPress routes
   - Check .htaccess rules

## Next Steps

1. **Upload enhanced-demo.html as index.html**
2. **Configure subdomain in hosting panel**
3. **Test accessibility at careercraft.onlinejobsearchhelp.com**
4. **Set up SSL certificate**
5. **Add monitoring and analytics**
6. **Create WordPress integration links**

## Support

For technical issues:
- Check server error logs
- Contact hosting provider support
- Review WordPress compatibility

## Future Enhancements

1. **User Authentication**
   - Integrate with WordPress users
   - Add user dashboard

2. **Database Integration**
   - Store user resumes
   - Add sharing capabilities

3. **API Integration**
   - Connect with job boards
   - Add real-time job matching

4. **Advanced Features**
   - Payment processing for premium features
   - Team collaboration tools
   - Advanced analytics dashboard
