/**
 * Form Detector Tests
 * 
 * Comprehensive test suite for the form detection engine
 * covering form analysis, classification, and confidence scoring.
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { FormDetector } from '../lib/form-detection/form-detector'

describe('FormDetector', () => {
  let formDetector: FormDetector
  let mockDocument: Document
  let mockWindow: Window

  beforeEach(() => {
    formDetector = new FormDetector()
    
    // Setup mock DOM environment
    mockDocument = {
      querySelectorAll: vi.fn(),
      title: 'Software Engineer - Apply Now',
      body: { textContent: 'Apply for this position' }
    } as any

    mockWindow = {
      location: {
        href: 'https://www.linkedin.com/jobs/view/123456',
        hostname: 'www.linkedin.com'
      }
    } as any

    global.document = mockDocument
    global.window = mockWindow
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('Form Detection', () => {
    it('should detect job application forms correctly', async () => {
      const mockForm = createMockJobApplicationForm()
      mockDocument.querySelectorAll.mockReturnValue([mockForm])

      const detectedForms = await formDetector.detectForms(mockDocument)

      expect(detectedForms).toHaveLength(1)
      expect(detectedForms[0].type).toBe('job-application')
      expect(detectedForms[0].confidence).toBeGreaterThan(0.7)
    })

    it('should ignore search forms', async () => {
      const mockForm = createMockSearchForm()
      mockDocument.querySelectorAll.mockReturnValue([mockForm])

      const detectedForms = await formDetector.detectForms(mockDocument)

      expect(detectedForms).toHaveLength(0)
    })

    it('should ignore newsletter signup forms', async () => {
      const mockForm = createMockNewsletterForm()
      mockDocument.querySelectorAll.mockReturnValue([mockForm])

      const detectedForms = await formDetector.detectForms(mockDocument)

      expect(detectedForms).toHaveLength(0)
    })

    it('should ignore hidden forms', async () => {
      const mockForm = createMockJobApplicationForm()
      mockForm.style.display = 'none'
      mockDocument.querySelectorAll.mockReturnValue([mockForm])

      const detectedForms = await formDetector.detectForms(mockDocument)

      expect(detectedForms).toHaveLength(0)
    })

    it('should ignore forms with too few fields', async () => {
      const mockForm = createMockSimpleForm()
      mockDocument.querySelectorAll.mockReturnValue([mockForm])

      const detectedForms = await formDetector.detectForms(mockDocument)

      expect(detectedForms).toHaveLength(0)
    })

    it('should sort forms by confidence score', async () => {
      const lowConfidenceForm = createMockContactForm()
      const highConfidenceForm = createMockJobApplicationForm()
      
      mockDocument.querySelectorAll.mockReturnValue([lowConfidenceForm, highConfidenceForm])

      const detectedForms = await formDetector.detectForms(mockDocument)

      expect(detectedForms).toHaveLength(2)
      expect(detectedForms[0].confidence).toBeGreaterThan(detectedForms[1].confidence)
    })
  })

  describe('Form Characteristics Analysis', () => {
    it('should analyze form characteristics correctly', () => {
      const mockForm = createMockJobApplicationForm()
      const characteristics = formDetector.analyzeFormCharacteristics(mockForm)

      expect(characteristics.fieldCount).toBeGreaterThan(5)
      expect(characteristics.hasFileUpload).toBe(true)
      expect(characteristics.hasTextArea).toBe(true)
      expect(characteristics.hasRequiredFields).toBe(true)
      expect(characteristics.hasPersonalInfo).toBe(true)
      expect(characteristics.hasWorkExperience).toBe(true)
      expect(characteristics.complexity).toBe('complex')
    })

    it('should detect file upload fields', () => {
      const mockForm = createMockFormWithFileUpload()
      const characteristics = formDetector.analyzeFormCharacteristics(mockForm)

      expect(characteristics.hasFileUpload).toBe(true)
    })

    it('should detect required fields', () => {
      const mockForm = createMockFormWithRequiredFields()
      const characteristics = formDetector.analyzeFormCharacteristics(mockForm)

      expect(characteristics.hasRequiredFields).toBe(true)
    })

    it('should determine form complexity correctly', () => {
      const simpleForm = createMockSimpleForm()
      const complexForm = createMockJobApplicationForm()

      const simpleCharacteristics = formDetector.analyzeFormCharacteristics(simpleForm)
      const complexCharacteristics = formDetector.analyzeFormCharacteristics(complexForm)

      expect(simpleCharacteristics.complexity).toBe('simple')
      expect(complexCharacteristics.complexity).toBe('complex')
    })
  })

  describe('Form Classification', () => {
    it('should classify job application forms with high confidence', () => {
      const mockForm = createMockJobApplicationForm()
      const characteristics = {
        fieldCount: 10,
        hasFileUpload: true,
        hasTextArea: true,
        hasRequiredFields: true,
        hasPersonalInfo: true,
        hasWorkExperience: true,
        hasEducation: true,
        complexity: 'complex' as const
      }

      const { type, confidence } = formDetector.classifyForm(mockForm, characteristics)

      expect(type).toBe('job-application')
      expect(confidence).toBeGreaterThan(0.7)
    })

    it('should classify contact forms correctly', () => {
      const mockForm = createMockContactForm()
      const characteristics = {
        fieldCount: 4,
        hasFileUpload: false,
        hasTextArea: true,
        hasRequiredFields: true,
        hasPersonalInfo: true,
        hasWorkExperience: false,
        hasEducation: false,
        complexity: 'simple' as const
      }

      const { type, confidence } = formDetector.classifyForm(mockForm, characteristics)

      expect(type).toBe('contact')
      expect(confidence).toBeLessThan(0.7)
    })

    it('should consider URL context in classification', () => {
      mockWindow.location.href = 'https://www.linkedin.com/jobs/apply/123456'
      mockDocument.title = 'Apply for Software Engineer Position'

      const mockForm = createMockJobApplicationForm()
      const characteristics = {
        fieldCount: 8,
        hasFileUpload: true,
        hasTextArea: false,
        hasRequiredFields: true,
        hasPersonalInfo: true,
        hasWorkExperience: false,
        hasEducation: false,
        complexity: 'medium' as const
      }

      const { type, confidence } = formDetector.classifyForm(mockForm, characteristics)

      expect(type).toBe('job-application')
      expect(confidence).toBeGreaterThan(0.6)
    })

    it('should consider form content in classification', () => {
      const mockForm = createMockFormWithJobKeywords()
      const characteristics = {
        fieldCount: 6,
        hasFileUpload: false,
        hasTextArea: true,
        hasRequiredFields: true,
        hasPersonalInfo: true,
        hasWorkExperience: true,
        hasEducation: false,
        complexity: 'medium' as const
      }

      const { type, confidence } = formDetector.classifyForm(mockForm, characteristics)

      expect(type).toBe('job-application')
      expect(confidence).toBeGreaterThan(0.5)
    })
  })

  describe('Keyword Detection', () => {
    it('should detect job application keywords', () => {
      const text = 'Please upload your resume and cover letter for this position'
      const jobKeywords = ['resume', 'cover letter', 'position', 'experience']

      const hasKeywords = formDetector.hasKeywords(text, jobKeywords)

      expect(hasKeywords).toBe(true)
    })

    it('should detect personal information keywords', () => {
      const text = 'Enter your first name, last name, and email address'
      const personalKeywords = ['first name', 'last name', 'email']

      const hasKeywords = formDetector.hasKeywords(text, personalKeywords)

      expect(hasKeywords).toBe(true)
    })

    it('should return false when no keywords match', () => {
      const text = 'This is just some random text'
      const keywords = ['resume', 'application', 'job']

      const hasKeywords = formDetector.hasKeywords(text, keywords)

      expect(hasKeywords).toBe(false)
    })
  })

  describe('Form Validation Requirements', () => {
    it('should detect validation requirements correctly', () => {
      const mockForm = createMockFormWithValidation()
      const requirements = formDetector.getValidationRequirements(mockForm)

      expect(requirements.requiredFields).toBeGreaterThan(0)
      expect(requirements.hasClientValidation).toBe(true)
    })

    it('should detect required fields', () => {
      const mockForm = createMockFormWithRequiredFields()
      const requirements = formDetector.getValidationRequirements(mockForm)

      expect(requirements.requiredFields).toBeGreaterThan(0)
    })

    it('should detect client-side validation', () => {
      const mockForm = createMockFormWithClientValidation()
      const requirements = formDetector.getValidationRequirements(mockForm)

      expect(requirements.hasClientValidation).toBe(true)
    })
  })

  describe('Multi-step Form Detection', () => {
    it('should detect multi-step forms', () => {
      const mockForm = createMockMultiStepForm()
      const isMultiStep = formDetector.isMultiStepForm(mockForm)

      expect(isMultiStep).toBe(true)
    })

    it('should not detect single-step forms as multi-step', () => {
      const mockForm = createMockJobApplicationForm()
      const isMultiStep = formDetector.isMultiStepForm(mockForm)

      expect(isMultiStep).toBe(false)
    })
  })

  describe('Form Submission Information', () => {
    it('should extract form submission information', () => {
      const mockForm = createMockFormWithAction()
      const submissionInfo = formDetector.getFormSubmissionInfo(mockForm)

      expect(submissionInfo.method).toBe('POST')
      expect(submissionInfo.action).toContain('apply')
      expect(submissionInfo.enctype).toBeDefined()
    })

    it('should handle forms without explicit action', () => {
      const mockForm = createMockJobApplicationForm()
      const submissionInfo = formDetector.getFormSubmissionInfo(mockForm)

      expect(submissionInfo.action).toBe(window.location.href)
    })
  })

  describe('Error Handling', () => {
    it('should handle malformed forms gracefully', async () => {
      const malformedForm = {} as HTMLFormElement
      mockDocument.querySelectorAll.mockReturnValue([malformedForm])

      const detectedForms = await formDetector.detectForms(mockDocument)

      expect(detectedForms).toHaveLength(0)
    })

    it('should handle missing DOM elements gracefully', async () => {
      mockDocument.querySelectorAll.mockReturnValue([])

      const detectedForms = await formDetector.detectForms(mockDocument)

      expect(detectedForms).toHaveLength(0)
    })
  })

  // Helper functions to create mock forms
  function createMockJobApplicationForm(): HTMLFormElement {
    return {
      style: { display: 'block' },
      hidden: false,
      textContent: 'Apply for this position. Upload your resume and cover letter. Enter your work experience and education.',
      action: 'https://www.linkedin.com/jobs/apply/123456',
      method: 'POST',
      id: 'job-application-form',
      className: 'application-form',
      querySelectorAll: vi.fn().mockReturnValue([
        { type: 'text', name: 'firstName', required: true },
        { type: 'text', name: 'lastName', required: true },
        { type: 'email', name: 'email', required: true },
        { type: 'tel', name: 'phone' },
        { type: 'file', name: 'resume', required: true },
        { type: 'file', name: 'coverLetter' },
        { tagName: 'TEXTAREA', name: 'experience' },
        { tagName: 'TEXTAREA', name: 'education' },
        { tagName: 'SELECT', name: 'experienceLevel' },
        { type: 'text', name: 'linkedinProfile' }
      ]),
      querySelector: vi.fn()
    } as any
  }

  function createMockSearchForm(): HTMLFormElement {
    return {
      style: { display: 'block' },
      hidden: false,
      textContent: 'Search for jobs',
      action: '/search',
      method: 'GET',
      querySelectorAll: vi.fn().mockReturnValue([
        { type: 'text', name: 'query' },
        { type: 'submit', value: 'Search' }
      ])
    } as any
  }

  function createMockNewsletterForm(): HTMLFormElement {
    return {
      style: { display: 'block' },
      hidden: false,
      textContent: 'Subscribe to our newsletter for job updates',
      querySelectorAll: vi.fn().mockReturnValue([
        { type: 'email', name: 'email' },
        { type: 'submit', value: 'Subscribe' }
      ])
    } as any
  }

  function createMockContactForm(): HTMLFormElement {
    return {
      style: { display: 'block' },
      hidden: false,
      textContent: 'Contact us for more information',
      querySelectorAll: vi.fn().mockReturnValue([
        { type: 'text', name: 'name', required: true },
        { type: 'email', name: 'email', required: true },
        { tagName: 'TEXTAREA', name: 'message', required: true },
        { type: 'submit', value: 'Send' }
      ])
    } as any
  }

  function createMockSimpleForm(): HTMLFormElement {
    return {
      style: { display: 'block' },
      hidden: false,
      textContent: 'Simple form',
      querySelectorAll: vi.fn().mockReturnValue([
        { type: 'text', name: 'name' },
        { type: 'submit', value: 'Submit' }
      ])
    } as any
  }

  function createMockFormWithFileUpload(): HTMLFormElement {
    return {
      style: { display: 'block' },
      hidden: false,
      textContent: 'Upload your documents',
      querySelectorAll: vi.fn().mockReturnValue([
        { type: 'text', name: 'name' },
        { type: 'file', name: 'document' },
        { type: 'submit', value: 'Upload' }
      ])
    } as any
  }

  function createMockFormWithRequiredFields(): HTMLFormElement {
    return {
      style: { display: 'block' },
      hidden: false,
      textContent: 'Required information form',
      querySelectorAll: vi.fn().mockReturnValue([
        { type: 'text', name: 'name', required: true, hasAttribute: vi.fn(() => true) },
        { type: 'email', name: 'email', required: true, hasAttribute: vi.fn(() => true) },
        { type: 'submit', value: 'Submit' }
      ])
    } as any
  }

  function createMockFormWithJobKeywords(): HTMLFormElement {
    return {
      style: { display: 'block' },
      hidden: false,
      textContent: 'Please provide your work experience, education, and skills for this job application',
      querySelectorAll: vi.fn().mockReturnValue([
        { type: 'text', name: 'name' },
        { type: 'email', name: 'email' },
        { tagName: 'TEXTAREA', name: 'experience' },
        { tagName: 'TEXTAREA', name: 'education' },
        { type: 'text', name: 'skills' },
        { type: 'submit', value: 'Apply' }
      ])
    } as any
  }

  function createMockMultiStepForm(): HTMLFormElement {
    return {
      style: { display: 'block' },
      hidden: false,
      textContent: 'Step 1 of 3: Personal Information. Click Next to continue.',
      querySelectorAll: vi.fn().mockReturnValue([
        { type: 'text', name: 'firstName' },
        { type: 'text', name: 'lastName' },
        { type: 'button', value: 'Next' }
      ])
    } as any
  }

  function createMockFormWithAction(): HTMLFormElement {
    return {
      style: { display: 'block' },
      hidden: false,
      textContent: 'Application form',
      action: 'https://company.com/apply',
      method: 'POST',
      enctype: 'multipart/form-data',
      querySelectorAll: vi.fn().mockReturnValue([
        { type: 'text', name: 'name' },
        { type: 'file', name: 'resume' }
      ])
    } as any
  }

  function createMockFormWithValidation(): HTMLFormElement {
    return {
      style: { display: 'block' },
      hidden: false,
      textContent: 'Form with validation',
      querySelectorAll: vi.fn().mockReturnValue([
        { 
          type: 'text', 
          name: 'name', 
          required: true,
          pattern: '[A-Za-z]+',
          minLength: 2,
          maxLength: 50,
          hasAttribute: vi.fn((attr) => ['required', 'pattern', 'minlength', 'maxlength'].includes(attr))
        },
        { 
          type: 'email', 
          name: 'email', 
          required: true,
          hasAttribute: vi.fn((attr) => attr === 'required')
        }
      ])
    } as any
  }

  function createMockFormWithClientValidation(): HTMLFormElement {
    return {
      style: { display: 'block' },
      hidden: false,
      textContent: 'Form with client validation',
      querySelectorAll: vi.fn().mockReturnValue([
        { 
          type: 'text', 
          name: 'name',
          pattern: '[A-Za-z]+',
          hasAttribute: vi.fn((attr) => attr === 'pattern')
        }
      ])
    } as any
  }
})
