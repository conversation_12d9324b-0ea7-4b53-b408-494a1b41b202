'use client'

import { useState, useEffect, useRef } from 'react'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Separator } from '@/components/ui/separator'
import { Slider } from '@/components/ui/slider'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Textarea } from '@/components/ui/textarea'
import { 
  Palette, 
  Type, 
  Layout, 
  Image, 
  Save, 
  Upload, 
  Download, 
  Eye, 
  Undo, 
  <PERSON>o,
  <PERSON><PERSON>,
  Trash2,
  Plus,
  Move,
  RotateCcw,
  Layers,
  Grid,
  Smartphone,
  Tablet,
  Monitor,
  Printer
} from 'lucide-react'
import { toast } from 'sonner'

interface TemplateElement {
  id: string
  type: 'text' | 'image' | 'section' | 'divider'
  content: string
  styles: {
    fontSize?: number
    fontFamily?: string
    fontWeight?: string
    color?: string
    backgroundColor?: string
    padding?: number
    margin?: number
    borderRadius?: number
    textAlign?: 'left' | 'center' | 'right'
  }
  position: {
    x: number
    y: number
    width: number
    height: number
  }
}

interface TemplateConfig {
  id: string
  name: string
  description: string
  category: string
  tags: string[]
  elements: TemplateElement[]
  globalStyles: {
    fontFamily: string
    primaryColor: string
    secondaryColor: string
    backgroundColor: string
    pageMargin: number
  }
  layout: {
    columns: number
    spacing: number
    responsive: boolean
  }
}

interface CloudTemplateBuilderProps {
  className?: string
  templateId?: string
  onSave?: (template: TemplateConfig) => void
}

export function CloudTemplateBuilder({ className, templateId, onSave }: CloudTemplateBuilderProps) {
  const [template, setTemplate] = useState<TemplateConfig>({
    id: templateId || 'new-template',
    name: 'Untitled Template',
    description: '',
    category: 'professional',
    tags: [],
    elements: [],
    globalStyles: {
      fontFamily: 'Inter',
      primaryColor: '#1f2937',
      secondaryColor: '#6b7280',
      backgroundColor: '#ffffff',
      pageMargin: 20
    },
    layout: {
      columns: 1,
      spacing: 16,
      responsive: true
    }
  })

  const [selectedElement, setSelectedElement] = useState<string | null>(null)
  const [viewMode, setViewMode] = useState<'desktop' | 'tablet' | 'mobile' | 'print'>('desktop')
  const [showGrid, setShowGrid] = useState(true)
  const [zoom, setZoom] = useState(100)
  const [history, setHistory] = useState<TemplateConfig[]>([])
  const [historyIndex, setHistoryIndex] = useState(-1)
  const [saving, setSaving] = useState(false)

  const canvasRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (templateId) {
      loadTemplate(templateId)
    } else {
      // Initialize with default elements
      addDefaultElements()
    }
  }, [templateId])

  const loadTemplate = async (id: string) => {
    try {
      // This would load template from API
      // For now, we'll use mock data
      const mockTemplate: TemplateConfig = {
        id,
        name: 'Professional Resume',
        description: 'Clean and professional resume template',
        category: 'professional',
        tags: ['modern', 'clean', 'professional'],
        elements: [
          {
            id: 'header',
            type: 'section',
            content: 'Header Section',
            styles: {
              backgroundColor: '#f8fafc',
              padding: 20,
              textAlign: 'center'
            },
            position: { x: 0, y: 0, width: 100, height: 120 }
          },
          {
            id: 'name',
            type: 'text',
            content: 'Your Name',
            styles: {
              fontSize: 32,
              fontWeight: 'bold',
              color: '#1f2937',
              textAlign: 'center'
            },
            position: { x: 0, y: 20, width: 100, height: 40 }
          }
        ],
        globalStyles: {
          fontFamily: 'Inter',
          primaryColor: '#1f2937',
          secondaryColor: '#6b7280',
          backgroundColor: '#ffffff',
          pageMargin: 20
        },
        layout: {
          columns: 1,
          spacing: 16,
          responsive: true
        }
      }
      
      setTemplate(mockTemplate)
      addToHistory(mockTemplate)
    } catch (error) {
      console.error('Error loading template:', error)
      toast.error('Failed to load template')
    }
  }

  const addDefaultElements = () => {
    const defaultElements: TemplateElement[] = [
      {
        id: 'header-section',
        type: 'section',
        content: 'Header',
        styles: {
          backgroundColor: '#f8fafc',
          padding: 20
        },
        position: { x: 0, y: 0, width: 100, height: 100 }
      },
      {
        id: 'name-text',
        type: 'text',
        content: 'Your Name Here',
        styles: {
          fontSize: 28,
          fontWeight: 'bold',
          color: '#1f2937',
          textAlign: 'center'
        },
        position: { x: 0, y: 20, width: 100, height: 40 }
      }
    ]

    const updatedTemplate = { ...template, elements: defaultElements }
    setTemplate(updatedTemplate)
    addToHistory(updatedTemplate)
  }

  const addToHistory = (templateState: TemplateConfig) => {
    const newHistory = history.slice(0, historyIndex + 1)
    newHistory.push(templateState)
    setHistory(newHistory)
    setHistoryIndex(newHistory.length - 1)
  }

  const undo = () => {
    if (historyIndex > 0) {
      setHistoryIndex(historyIndex - 1)
      setTemplate(history[historyIndex - 1])
    }
  }

  const redo = () => {
    if (historyIndex < history.length - 1) {
      setHistoryIndex(historyIndex + 1)
      setTemplate(history[historyIndex + 1])
    }
  }

  const addElement = (type: TemplateElement['type']) => {
    const newElement: TemplateElement = {
      id: `element-${Date.now()}`,
      type,
      content: type === 'text' ? 'New Text' : type === 'section' ? 'New Section' : '',
      styles: {
        fontSize: 16,
        fontFamily: template.globalStyles.fontFamily,
        color: template.globalStyles.primaryColor,
        padding: 10
      },
      position: {
        x: 10,
        y: template.elements.length * 60 + 10,
        width: 80,
        height: 40
      }
    }

    const updatedTemplate = {
      ...template,
      elements: [...template.elements, newElement]
    }
    
    setTemplate(updatedTemplate)
    addToHistory(updatedTemplate)
    setSelectedElement(newElement.id)
  }

  const updateElement = (elementId: string, updates: Partial<TemplateElement>) => {
    const updatedElements = template.elements.map(element =>
      element.id === elementId ? { ...element, ...updates } : element
    )

    const updatedTemplate = { ...template, elements: updatedElements }
    setTemplate(updatedTemplate)
  }

  const deleteElement = (elementId: string) => {
    const updatedElements = template.elements.filter(element => element.id !== elementId)
    const updatedTemplate = { ...template, elements: updatedElements }
    
    setTemplate(updatedTemplate)
    addToHistory(updatedTemplate)
    setSelectedElement(null)
  }

  const duplicateElement = (elementId: string) => {
    const element = template.elements.find(el => el.id === elementId)
    if (!element) return

    const duplicatedElement: TemplateElement = {
      ...element,
      id: `element-${Date.now()}`,
      position: {
        ...element.position,
        y: element.position.y + 50
      }
    }

    const updatedTemplate = {
      ...template,
      elements: [...template.elements, duplicatedElement]
    }
    
    setTemplate(updatedTemplate)
    addToHistory(updatedTemplate)
  }

  const saveTemplate = async () => {
    try {
      setSaving(true)
      
      const response = await fetch('/api/template-sync', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'upload-cloud',
          templateId: template.id,
          templateData: template
        })
      })

      if (!response.ok) {
        throw new Error('Failed to save template')
      }

      toast.success('Template saved successfully!')
      onSave?.(template)
    } catch (error) {
      console.error('Error saving template:', error)
      toast.error('Failed to save template')
    } finally {
      setSaving(false)
    }
  }

  const exportTemplate = (format: 'json' | 'pdf' | 'html') => {
    switch (format) {
      case 'json':
        const dataStr = JSON.stringify(template, null, 2)
        const dataBlob = new Blob([dataStr], { type: 'application/json' })
        const url = URL.createObjectURL(dataBlob)
        const link = document.createElement('a')
        link.href = url
        link.download = `${template.name}.json`
        link.click()
        break
      case 'pdf':
        toast.info('PDF export would be implemented here')
        break
      case 'html':
        toast.info('HTML export would be implemented here')
        break
    }
  }

  const getViewportStyles = () => {
    switch (viewMode) {
      case 'mobile':
        return { width: '375px', height: '667px' }
      case 'tablet':
        return { width: '768px', height: '1024px' }
      case 'print':
        return { width: '8.5in', height: '11in' }
      default:
        return { width: '100%', height: 'auto' }
    }
  }

  const selectedElementData = selectedElement 
    ? template.elements.find(el => el.id === selectedElement)
    : null

  return (
    <div className={`flex h-screen ${className}`}>
      {/* Toolbar */}
      <div className="w-16 bg-gray-100 border-r flex flex-col items-center py-4 space-y-2">
        <Button
          variant="ghost"
          size="sm"
          onClick={undo}
          disabled={historyIndex <= 0}
          title="Undo"
        >
          <Undo className="w-4 h-4" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={redo}
          disabled={historyIndex >= history.length - 1}
          title="Redo"
        >
          <Redo className="w-4 h-4" />
        </Button>
        <Separator />
        <Button
          variant="ghost"
          size="sm"
          onClick={() => addElement('text')}
          title="Add Text"
        >
          <Type className="w-4 h-4" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => addElement('section')}
          title="Add Section"
        >
          <Layout className="w-4 h-4" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => addElement('image')}
          title="Add Image"
        >
          <Image className="w-4 h-4" />
        </Button>
        <Separator />
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setShowGrid(!showGrid)}
          title="Toggle Grid"
          className={showGrid ? 'bg-blue-100' : ''}
        >
          <Grid className="w-4 h-4" />
        </Button>
      </div>

      {/* Left Panel - Elements & Styles */}
      <div className="w-80 bg-white border-r overflow-y-auto">
        <Tabs defaultValue="elements" className="h-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="elements">Elements</TabsTrigger>
            <TabsTrigger value="styles">Styles</TabsTrigger>
            <TabsTrigger value="settings">Settings</TabsTrigger>
          </TabsList>

          <TabsContent value="elements" className="p-4 space-y-4">
            <div>
              <h3 className="font-medium mb-2">Template Elements</h3>
              <div className="space-y-2">
                {template.elements.map((element) => (
                  <div
                    key={element.id}
                    className={`p-2 border rounded cursor-pointer flex items-center justify-between ${
                      selectedElement === element.id ? 'border-blue-500 bg-blue-50' : 'border-gray-200'
                    }`}
                    onClick={() => setSelectedElement(element.id)}
                  >
                    <div className="flex items-center space-x-2">
                      {element.type === 'text' && <Type className="w-4 h-4" />}
                      {element.type === 'section' && <Layout className="w-4 h-4" />}
                      {element.type === 'image' && <Image className="w-4 h-4" />}
                      <span className="text-sm">{element.content.substring(0, 20)}...</span>
                    </div>
                    <div className="flex space-x-1">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation()
                          duplicateElement(element.id)
                        }}
                      >
                        <Copy className="w-3 h-3" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation()
                          deleteElement(element.id)
                        }}
                      >
                        <Trash2 className="w-3 h-3" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <Separator />

            <div>
              <h3 className="font-medium mb-2">Add Elements</h3>
              <div className="grid grid-cols-2 gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => addElement('text')}
                  className="flex items-center space-x-1"
                >
                  <Type className="w-4 h-4" />
                  <span>Text</span>
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => addElement('section')}
                  className="flex items-center space-x-1"
                >
                  <Layout className="w-4 h-4" />
                  <span>Section</span>
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => addElement('image')}
                  className="flex items-center space-x-1"
                >
                  <Image className="w-4 h-4" />
                  <span>Image</span>
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => addElement('divider')}
                  className="flex items-center space-x-1"
                >
                  <Separator className="w-4 h-4" />
                  <span>Divider</span>
                </Button>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="styles" className="p-4 space-y-4">
            {selectedElementData ? (
              <div>
                <h3 className="font-medium mb-2">Element Styles</h3>
                <div className="space-y-3">
                  <div>
                    <Label>Content</Label>
                    <Textarea
                      value={selectedElementData.content}
                      onChange={(e) => updateElement(selectedElement!, { content: e.target.value })}
                      className="mt-1"
                    />
                  </div>

                  <div>
                    <Label>Font Size</Label>
                    <Slider
                      value={[selectedElementData.styles.fontSize || 16]}
                      onValueChange={([value]) => 
                        updateElement(selectedElement!, { 
                          styles: { ...selectedElementData.styles, fontSize: value }
                        })
                      }
                      min={8}
                      max={72}
                      step={1}
                      className="mt-1"
                    />
                    <span className="text-xs text-gray-500">
                      {selectedElementData.styles.fontSize || 16}px
                    </span>
                  </div>

                  <div>
                    <Label>Font Weight</Label>
                    <Select
                      value={selectedElementData.styles.fontWeight || 'normal'}
                      onValueChange={(value) =>
                        updateElement(selectedElement!, {
                          styles: { ...selectedElementData.styles, fontWeight: value }
                        })
                      }
                    >
                      <SelectTrigger className="mt-1">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="normal">Normal</SelectItem>
                        <SelectItem value="bold">Bold</SelectItem>
                        <SelectItem value="lighter">Light</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label>Text Color</Label>
                    <Input
                      type="color"
                      value={selectedElementData.styles.color || '#000000'}
                      onChange={(e) =>
                        updateElement(selectedElement!, {
                          styles: { ...selectedElementData.styles, color: e.target.value }
                        })
                      }
                      className="mt-1 h-10"
                    />
                  </div>

                  <div>
                    <Label>Background Color</Label>
                    <Input
                      type="color"
                      value={selectedElementData.styles.backgroundColor || '#ffffff'}
                      onChange={(e) =>
                        updateElement(selectedElement!, {
                          styles: { ...selectedElementData.styles, backgroundColor: e.target.value }
                        })
                      }
                      className="mt-1 h-10"
                    />
                  </div>

                  <div>
                    <Label>Text Alignment</Label>
                    <Select
                      value={selectedElementData.styles.textAlign || 'left'}
                      onValueChange={(value: any) =>
                        updateElement(selectedElement!, {
                          styles: { ...selectedElementData.styles, textAlign: value }
                        })
                      }
                    >
                      <SelectTrigger className="mt-1">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="left">Left</SelectItem>
                        <SelectItem value="center">Center</SelectItem>
                        <SelectItem value="right">Right</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>
            ) : (
              <div className="text-center py-8">
                <Layers className="w-12 h-12 mx-auto text-gray-400 mb-2" />
                <p className="text-gray-500">Select an element to edit its styles</p>
              </div>
            )}
          </TabsContent>

          <TabsContent value="settings" className="p-4 space-y-4">
            <div>
              <h3 className="font-medium mb-2">Template Settings</h3>
              <div className="space-y-3">
                <div>
                  <Label>Template Name</Label>
                  <Input
                    value={template.name}
                    onChange={(e) => setTemplate({ ...template, name: e.target.value })}
                    className="mt-1"
                  />
                </div>

                <div>
                  <Label>Description</Label>
                  <Textarea
                    value={template.description}
                    onChange={(e) => setTemplate({ ...template, description: e.target.value })}
                    className="mt-1"
                  />
                </div>

                <div>
                  <Label>Category</Label>
                  <Select
                    value={template.category}
                    onValueChange={(value) => setTemplate({ ...template, category: value })}
                  >
                    <SelectTrigger className="mt-1">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="professional">Professional</SelectItem>
                      <SelectItem value="creative">Creative</SelectItem>
                      <SelectItem value="modern">Modern</SelectItem>
                      <SelectItem value="classic">Classic</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label>Primary Color</Label>
                  <Input
                    type="color"
                    value={template.globalStyles.primaryColor}
                    onChange={(e) =>
                      setTemplate({
                        ...template,
                        globalStyles: { ...template.globalStyles, primaryColor: e.target.value }
                      })
                    }
                    className="mt-1 h-10"
                  />
                </div>

                <div>
                  <Label>Font Family</Label>
                  <Select
                    value={template.globalStyles.fontFamily}
                    onValueChange={(value) =>
                      setTemplate({
                        ...template,
                        globalStyles: { ...template.globalStyles, fontFamily: value }
                      })
                    }
                  >
                    <SelectTrigger className="mt-1">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Inter">Inter</SelectItem>
                      <SelectItem value="Arial">Arial</SelectItem>
                      <SelectItem value="Times New Roman">Times New Roman</SelectItem>
                      <SelectItem value="Georgia">Georgia</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </div>

      {/* Main Canvas */}
      <div className="flex-1 flex flex-col">
        {/* Top Bar */}
        <div className="h-14 bg-white border-b flex items-center justify-between px-4">
          <div className="flex items-center space-x-4">
            <h2 className="font-medium">{template.name}</h2>
            <Badge variant="outline">{template.category}</Badge>
          </div>

          <div className="flex items-center space-x-2">
            {/* View Mode */}
            <div className="flex items-center space-x-1 border rounded p-1">
              <Button
                variant={viewMode === 'desktop' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('desktop')}
              >
                <Monitor className="w-4 h-4" />
              </Button>
              <Button
                variant={viewMode === 'tablet' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('tablet')}
              >
                <Tablet className="w-4 h-4" />
              </Button>
              <Button
                variant={viewMode === 'mobile' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('mobile')}
              >
                <Smartphone className="w-4 h-4" />
              </Button>
              <Button
                variant={viewMode === 'print' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('print')}
              >
                <Printer className="w-4 h-4" />
              </Button>
            </div>

            {/* Zoom */}
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setZoom(Math.max(25, zoom - 25))}
              >
                -
              </Button>
              <span className="text-sm w-12 text-center">{zoom}%</span>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setZoom(Math.min(200, zoom + 25))}
              >
                +
              </Button>
            </div>

            {/* Actions */}
            <Button variant="outline" size="sm">
              <Eye className="w-4 h-4 mr-1" />
              Preview
            </Button>

            <Dialog>
              <DialogTrigger asChild>
                <Button variant="outline" size="sm">
                  <Download className="w-4 h-4 mr-1" />
                  Export
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Export Template</DialogTitle>
                  <DialogDescription>
                    Choose the format to export your template
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <Button
                    className="w-full justify-start"
                    variant="outline"
                    onClick={() => exportTemplate('json')}
                  >
                    Export as JSON
                  </Button>
                  <Button
                    className="w-full justify-start"
                    variant="outline"
                    onClick={() => exportTemplate('pdf')}
                  >
                    Export as PDF
                  </Button>
                  <Button
                    className="w-full justify-start"
                    variant="outline"
                    onClick={() => exportTemplate('html')}
                  >
                    Export as HTML
                  </Button>
                </div>
              </DialogContent>
            </Dialog>

            <Button onClick={saveTemplate} disabled={saving}>
              <Save className="w-4 h-4 mr-1" />
              {saving ? 'Saving...' : 'Save'}
            </Button>
          </div>
        </div>

        {/* Canvas Area */}
        <div className="flex-1 bg-gray-100 overflow-auto p-8">
          <div
            ref={canvasRef}
            className="mx-auto bg-white shadow-lg relative"
            style={{
              ...getViewportStyles(),
              transform: `scale(${zoom / 100})`,
              transformOrigin: 'top center',
              minHeight: '600px'
            }}
          >
            {showGrid && (
              <div
                className="absolute inset-0 opacity-10"
                style={{
                  backgroundImage: 'radial-gradient(circle, #000 1px, transparent 1px)',
                  backgroundSize: '20px 20px'
                }}
              />
            )}

            {/* Render Template Elements */}
            {template.elements.map((element) => (
              <div
                key={element.id}
                className={`absolute cursor-pointer border-2 ${
                  selectedElement === element.id 
                    ? 'border-blue-500' 
                    : 'border-transparent hover:border-gray-300'
                }`}
                style={{
                  left: `${element.position.x}%`,
                  top: `${element.position.y}px`,
                  width: `${element.position.width}%`,
                  height: `${element.position.height}px`,
                  fontSize: element.styles.fontSize,
                  fontFamily: element.styles.fontFamily,
                  fontWeight: element.styles.fontWeight,
                  color: element.styles.color,
                  backgroundColor: element.styles.backgroundColor,
                  padding: element.styles.padding,
                  textAlign: element.styles.textAlign,
                  borderRadius: element.styles.borderRadius
                }}
                onClick={() => setSelectedElement(element.id)}
              >
                {element.type === 'text' && (
                  <div className="w-full h-full flex items-center">
                    {element.content}
                  </div>
                )}
                {element.type === 'section' && (
                  <div className="w-full h-full border border-dashed border-gray-300 flex items-center justify-center text-gray-500">
                    {element.content}
                  </div>
                )}
                {element.type === 'image' && (
                  <div className="w-full h-full border border-dashed border-gray-300 flex items-center justify-center text-gray-500">
                    <Image className="w-8 h-8" />
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}
