/**
 * Career Intelligence System Validation Script
 * 
 * Validates the implementation of Epic 5.0: Career Intelligence Engine
 * Tests core functionality without requiring full test environment
 */

const fs = require('fs')
const path = require('path')

console.log('🧠 CAREER INTELLIGENCE SYSTEM VALIDATION')
console.log('=' .repeat(50))

// Track validation results
const results = {
  passed: 0,
  failed: 0,
  total: 0,
  details: []
}

function validateFile(filePath, description) {
  results.total++
  
  if (fs.existsSync(filePath)) {
    const content = fs.readFileSync(filePath, 'utf8')
    const size = (content.length / 1024).toFixed(2)
    
    console.log(`✅ ${description}`)
    console.log(`   📁 ${filePath} (${size} KB)`)
    
    results.passed++
    results.details.push({ file: filePath, status: 'PASS', size: `${size} KB` })
    return content
  } else {
    console.log(`❌ ${description}`)
    console.log(`   📁 ${filePath} (NOT FOUND)`)
    
    results.failed++
    results.details.push({ file: filePath, status: 'FAIL', size: 'N/A' })
    return null
  }
}

function validateImplementation() {
  console.log('\n📊 VALIDATING CAREER INTELLIGENCE IMPLEMENTATION')
  console.log('-'.repeat(50))

  // 1. Database Schema Extensions
  console.log('\n🗄️  Database Schema:')
  const schema = validateFile(
    'packages/database/prisma/schema.prisma',
    'Prisma Schema with Career Intelligence Models'
  )
  
  if (schema) {
    const hasUserProfileVector = schema.includes('model UserProfileVector')
    const hasMarketAnalysis = schema.includes('model MarketAnalysis')
    const hasJobMatch = schema.includes('model JobMatch')
    
    console.log(`   📋 UserProfileVector Model: ${hasUserProfileVector ? '✅' : '❌'}`)
    console.log(`   📋 MarketAnalysis Model: ${hasMarketAnalysis ? '✅' : '❌'}`)
    console.log(`   📋 JobMatch Model: ${hasJobMatch ? '✅' : '❌'}`)
  }

  // 2. Core Services
  console.log('\n🔧 Core Services:')
  const vectorizer = validateFile(
    'apps/web/src/lib/career-intelligence/profile-vectorizer.ts',
    'Profile Vectorization Service (FR-5.1)'
  )
  
  const service = validateFile(
    'apps/web/src/lib/career-intelligence/service.ts',
    'Career Intelligence Service (Epic 5.0)'
  )

  // 3. Market Data Service
  console.log('\n🌐 Market Data Service:')
  validateFile(
    'services/market-data-service/requirements.txt',
    'Python Dependencies for Job Scraping'
  )
  
  validateFile(
    'services/market-data-service/src/scrapers/base_scraper.py',
    'Base Job Scraper (FR-5.2)'
  )
  
  validateFile(
    'services/market-data-service/src/scrapers/linkedin_scraper.py',
    'LinkedIn Job Scraper'
  )

  // 4. API Endpoints
  console.log('\n🌍 API Endpoints:')
  validateFile(
    'apps/web/src/app/api/career-intelligence/route.ts',
    'Career Intelligence API Routes'
  )
  
  validateFile(
    'apps/web/src/app/api/career-intelligence/market-analysis/route.ts',
    'Market Analysis API (FR-5.3, FR-5.4)'
  )

  // 5. UI Components
  console.log('\n🎨 User Interface:')
  const dashboard = validateFile(
    'apps/web/src/components/career-intelligence/CareerInsightsDashboard.tsx',
    'Career Insights Dashboard (FR-5.5)'
  )

  // 6. Testing Suite
  console.log('\n🧪 Testing Suite:')
  const tests = validateFile(
    'apps/web/src/test/career-intelligence/career-intelligence.test.ts',
    'Comprehensive Career Intelligence Tests'
  )

  // Validate test content
  if (tests) {
    const hasProfileVectorizerTests = tests.includes('describe(\'ProfileVectorizer\'')
    const hasServiceTests = tests.includes('describe(\'CareerIntelligenceService\'')
    const hasAPITests = tests.includes('describe(\'Career Intelligence API Integration\'')
    
    console.log(`   🧪 ProfileVectorizer Tests: ${hasProfileVectorizerTests ? '✅' : '❌'}`)
    console.log(`   🧪 Service Tests: ${hasServiceTests ? '✅' : '❌'}`)
    console.log(`   🧪 API Integration Tests: ${hasAPITests ? '✅' : '❌'}`)
  }
}

function validateFeatureRequirements() {
  console.log('\n📋 VALIDATING FEATURE REQUIREMENTS')
  console.log('-'.repeat(50))

  const requirements = [
    {
      id: 'FR-5.1',
      name: 'Profile Vectorization',
      files: ['apps/web/src/lib/career-intelligence/profile-vectorizer.ts'],
      description: 'Convert resume data into structured vectors'
    },
    {
      id: 'FR-5.2',
      name: 'Job Market Data Ingestion',
      files: [
        'services/market-data-service/src/scrapers/base_scraper.py',
        'services/market-data-service/src/scrapers/linkedin_scraper.py'
      ],
      description: 'Scrape and aggregate job postings from multiple sources'
    },
    {
      id: 'FR-5.3',
      name: 'Market-Fit & Salary Analysis',
      files: [
        'apps/web/src/lib/career-intelligence/service.ts',
        'apps/web/src/app/api/career-intelligence/market-analysis/route.ts'
      ],
      description: 'Salary estimation and market fit scoring'
    },
    {
      id: 'FR-5.4',
      name: 'Skill Gap & Opportunity Analysis',
      files: [
        'apps/web/src/lib/career-intelligence/service.ts',
        'apps/web/src/app/api/career-intelligence/market-analysis/route.ts'
      ],
      description: 'Identify skill gaps and career opportunities'
    },
    {
      id: 'FR-5.5',
      name: 'Insights Dashboard UI',
      files: ['apps/web/src/components/career-intelligence/CareerInsightsDashboard.tsx'],
      description: 'Interactive career insights dashboard'
    }
  ]

  requirements.forEach(req => {
    console.log(`\n${req.id}: ${req.name}`)
    console.log(`📝 ${req.description}`)
    
    const allFilesExist = req.files.every(file => fs.existsSync(file))
    console.log(`📁 Implementation: ${allFilesExist ? '✅ COMPLETE' : '❌ INCOMPLETE'}`)
    
    req.files.forEach(file => {
      const exists = fs.existsSync(file)
      console.log(`   ${exists ? '✅' : '❌'} ${file}`)
    })
  })
}

function validateCodeQuality() {
  console.log('\n🔍 CODE QUALITY ANALYSIS')
  console.log('-'.repeat(50))

  const codeFiles = [
    'apps/web/src/lib/career-intelligence/profile-vectorizer.ts',
    'apps/web/src/lib/career-intelligence/service.ts',
    'apps/web/src/components/career-intelligence/CareerInsightsDashboard.tsx'
  ]

  codeFiles.forEach(file => {
    if (fs.existsSync(file)) {
      const content = fs.readFileSync(file, 'utf8')
      const lines = content.split('\n').length
      const hasTypeScript = file.endsWith('.ts') || file.endsWith('.tsx')
      const hasDocumentation = content.includes('/**')
      const hasErrorHandling = content.includes('try') && content.includes('catch')
      const hasTypeDefinitions = content.includes('interface') || content.includes('type')
      
      console.log(`\n📄 ${path.basename(file)}:`)
      console.log(`   📏 Lines of Code: ${lines}`)
      console.log(`   🔷 TypeScript: ${hasTypeScript ? '✅' : '❌'}`)
      console.log(`   📚 Documentation: ${hasDocumentation ? '✅' : '❌'}`)
      console.log(`   🛡️  Error Handling: ${hasErrorHandling ? '✅' : '❌'}`)
      console.log(`   🏷️  Type Definitions: ${hasTypeDefinitions ? '✅' : '❌'}`)
    }
  })
}

function generateReport() {
  console.log('\n📊 VALIDATION SUMMARY')
  console.log('='.repeat(50))
  
  const successRate = ((results.passed / results.total) * 100).toFixed(1)
  
  console.log(`📈 Overall Success Rate: ${successRate}%`)
  console.log(`✅ Passed: ${results.passed}/${results.total}`)
  console.log(`❌ Failed: ${results.failed}/${results.total}`)
  
  if (results.failed > 0) {
    console.log('\n❌ FAILED VALIDATIONS:')
    results.details
      .filter(detail => detail.status === 'FAIL')
      .forEach(detail => {
        console.log(`   📁 ${detail.file}`)
      })
  }
  
  console.log('\n🎯 MILESTONE 1.1 STATUS:')
  if (successRate >= 90) {
    console.log('🎉 MILESTONE 1.1: PROFILE VECTORIZATION SYSTEM - ✅ COMPLETE')
    console.log('✨ Ready to proceed to Milestone 1.2: Job Market Data Service')
  } else if (successRate >= 70) {
    console.log('⚠️  MILESTONE 1.1: PROFILE VECTORIZATION SYSTEM - 🔄 PARTIAL')
    console.log('🔧 Minor fixes needed before proceeding')
  } else {
    console.log('❌ MILESTONE 1.1: PROFILE VECTORIZATION SYSTEM - ❌ INCOMPLETE')
    console.log('🚨 Major implementation required')
  }
  
  console.log('\n🚀 NEXT STEPS:')
  console.log('1. 🧪 Run comprehensive tests: npm run test:career-intelligence')
  console.log('2. 🗄️  Apply database migrations: npx prisma db push')
  console.log('3. 🔧 Set up environment variables for OpenAI API')
  console.log('4. 📊 Proceed to Milestone 1.2: Job Market Data Service')
  
  return successRate >= 90
}

// Run validation
console.log('Starting Career Intelligence System validation...\n')

try {
  validateImplementation()
  validateFeatureRequirements()
  validateCodeQuality()
  const success = generateReport()
  
  process.exit(success ? 0 : 1)
} catch (error) {
  console.error('\n💥 VALIDATION ERROR:', error.message)
  process.exit(1)
}
