# Epic 8.0: Stripe Payment Integration - Build & Deployment Documentation

## Overview
This document provides comprehensive build and deployment procedures for the Stripe payment integration and SaaS monetization features in CareerCraft.

## Build System Architecture

### Technology Stack
```
┌─────────────────────────────────────────────────────────────────┐
│                    Build & Deployment Stack                     │
├─────────────────────────────────────────────────────────────────┤
│  Build Tools                                                    │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │  • Node.js 18+ (Runtime environment)                       │ │
│  │  • npm/yarn (Package management)                           │ │
│  │  • TypeScript (Type-safe development)                      │ │
│  │  • Webpack/Vite (Module bundling)                          │ │
│  │  • Babel (JavaScript transpilation)                        │ │
│  └─────────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────┤
│  Infrastructure as Code                                         │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │  • Terraform (Infrastructure provisioning)                 │ │
│  │  • AWS CDK (Cloud infrastructure)                          │ │
│  │  • Docker (Containerization)                               │ │
│  │  • Kubernetes (Container orchestration)                    │ │
│  └─────────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────┤
│  CI/CD Pipeline                                                 │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │  • GitHub Actions (Continuous integration)                 │ │
│  │  • AWS CodePipeline (Deployment pipeline)                  │ │
│  │  • AWS CodeBuild (Build service)                           │ │
│  │  • AWS CodeDeploy (Deployment service)                     │ │
│  └─────────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────┤
│  Monitoring & Observability                                     │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │  • AWS CloudWatch (Metrics and logs)                       │ │
│  │  • Datadog (Application monitoring)                        │ │
│  │  • Sentry (Error tracking)                                 │ │
│  │  • New Relic (Performance monitoring)                      │ │
│  └─────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

## Environment Configuration

### Environment Variables
```bash
# Development Environment
NODE_ENV=development
PORT=3000
DATABASE_URL=postgresql://localhost:5432/careercraft_dev
REDIS_URL=redis://localhost:6379
STRIPE_PUBLISHABLE_KEY=pk_test_...
STRIPE_SECRET_KEY=sk_test_...
STRIPE_WEBHOOK_SECRET=whsec_...
JWT_SECRET=dev_jwt_secret
ENCRYPTION_KEY=dev_encryption_key
SENDGRID_API_KEY=SG.dev...
MIXPANEL_TOKEN=dev_mixpanel_token

# Staging Environment
NODE_ENV=staging
PORT=3000
DATABASE_URL=postgresql://staging-db.amazonaws.com:5432/careercraft_staging
REDIS_URL=redis://staging-redis.amazonaws.com:6379
STRIPE_PUBLISHABLE_KEY=pk_test_...
STRIPE_SECRET_KEY=sk_test_...
STRIPE_WEBHOOK_SECRET=whsec_...
JWT_SECRET=staging_jwt_secret
ENCRYPTION_KEY=staging_encryption_key
SENDGRID_API_KEY=SG.staging...
MIXPANEL_TOKEN=staging_mixpanel_token

# Production Environment
NODE_ENV=production
PORT=3000
DATABASE_URL=postgresql://prod-db.amazonaws.com:5432/careercraft_prod
REDIS_URL=redis://prod-redis.amazonaws.com:6379
STRIPE_PUBLISHABLE_KEY=pk_live_...
STRIPE_SECRET_KEY=sk_live_...
STRIPE_WEBHOOK_SECRET=whsec_...
JWT_SECRET=${AWS_SECRETS_MANAGER_JWT_SECRET}
ENCRYPTION_KEY=${AWS_SECRETS_MANAGER_ENCRYPTION_KEY}
SENDGRID_API_KEY=${AWS_SECRETS_MANAGER_SENDGRID_KEY}
MIXPANEL_TOKEN=${AWS_SECRETS_MANAGER_MIXPANEL_TOKEN}
```

### Database Configuration
```javascript
// Database configuration for different environments
module.exports = {
  development: {
    client: 'postgresql',
    connection: {
      host: 'localhost',
      port: 5432,
      database: 'careercraft_dev',
      user: 'postgres',
      password: 'postgres'
    },
    migrations: {
      directory: './migrations'
    },
    seeds: {
      directory: './seeds'
    }
  },
  
  staging: {
    client: 'postgresql',
    connection: {
      host: process.env.DB_HOST,
      port: process.env.DB_PORT,
      database: process.env.DB_NAME,
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      ssl: { rejectUnauthorized: false }
    },
    pool: {
      min: 2,
      max: 10
    },
    migrations: {
      directory: './migrations'
    }
  },
  
  production: {
    client: 'postgresql',
    connection: {
      host: process.env.DB_HOST,
      port: process.env.DB_PORT,
      database: process.env.DB_NAME,
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      ssl: { rejectUnauthorized: false }
    },
    pool: {
      min: 5,
      max: 50
    },
    migrations: {
      directory: './migrations'
    }
  }
}
```

## Build Process

### Package.json Scripts
```json
{
  "scripts": {
    "dev": "nodemon --exec ts-node src/server.ts",
    "build": "npm run clean && npm run compile && npm run copy-assets",
    "build:staging": "NODE_ENV=staging npm run build",
    "build:production": "NODE_ENV=production npm run build",
    "clean": "rimraf dist",
    "compile": "tsc --project tsconfig.build.json",
    "copy-assets": "copyfiles -u 1 src/**/*.{json,sql,html} dist/",
    "start": "node dist/server.js",
    "test": "jest",
    "test:coverage": "jest --coverage",
    "test:e2e": "playwright test",
    "lint": "eslint src/**/*.ts",
    "lint:fix": "eslint src/**/*.ts --fix",
    "migrate": "knex migrate:latest",
    "migrate:rollback": "knex migrate:rollback",
    "seed": "knex seed:run",
    "docker:build": "docker build -t careercraft-api .",
    "docker:run": "docker run -p 3000:3000 careercraft-api",
    "deploy:staging": "npm run build:staging && npm run deploy:aws:staging",
    "deploy:production": "npm run build:production && npm run deploy:aws:production"
  }
}
```

### TypeScript Configuration
```json
{
  "compilerOptions": {
    "target": "ES2020",
    "module": "commonjs",
    "lib": ["ES2020"],
    "outDir": "./dist",
    "rootDir": "./src",
    "strict": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "resolveJsonModule": true,
    "declaration": true,
    "declarationMap": true,
    "sourceMap": true,
    "removeComments": true,
    "noImplicitAny": true,
    "strictNullChecks": true,
    "strictFunctionTypes": true,
    "noImplicitReturns": true,
    "noFallthroughCasesInSwitch": true,
    "noUncheckedIndexedAccess": true,
    "noImplicitOverride": true,
    "exactOptionalPropertyTypes": true,
    "baseUrl": "./src",
    "paths": {
      "@/*": ["*"],
      "@/services/*": ["services/*"],
      "@/models/*": ["models/*"],
      "@/utils/*": ["utils/*"]
    }
  },
  "include": ["src/**/*"],
  "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts"]
}
```

### Webpack Configuration
```javascript
const path = require('path')
const nodeExternals = require('webpack-node-externals')

module.exports = {
  target: 'node',
  mode: process.env.NODE_ENV === 'production' ? 'production' : 'development',
  entry: './src/server.ts',
  output: {
    path: path.resolve(__dirname, 'dist'),
    filename: 'server.js'
  },
  externals: [nodeExternals()],
  module: {
    rules: [
      {
        test: /\.ts$/,
        use: 'ts-loader',
        exclude: /node_modules/
      }
    ]
  },
  resolve: {
    extensions: ['.ts', '.js'],
    alias: {
      '@': path.resolve(__dirname, 'src')
    }
  },
  optimization: {
    minimize: process.env.NODE_ENV === 'production'
  }
}
```

## Docker Configuration

### Dockerfile
```dockerfile
# Multi-stage build for production optimization
FROM node:18-alpine AS builder

WORKDIR /app

# Copy package files
COPY package*.json ./
COPY tsconfig*.json ./

# Install dependencies
RUN npm ci --only=production && npm cache clean --force

# Copy source code
COPY src/ ./src/

# Build application
RUN npm run build

# Production stage
FROM node:18-alpine AS production

WORKDIR /app

# Create non-root user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S careercraft -u 1001

# Copy built application
COPY --from=builder --chown=careercraft:nodejs /app/dist ./dist
COPY --from=builder --chown=careercraft:nodejs /app/node_modules ./node_modules
COPY --from=builder --chown=careercraft:nodejs /app/package*.json ./

# Set user
USER careercraft

# Expose port
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000/health || exit 1

# Start application
CMD ["node", "dist/server.js"]
```

### Docker Compose (Development)
```yaml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - DATABASE_URL=**************************************/careercraft_dev
      - REDIS_URL=redis://redis:6379
    depends_on:
      - db
      - redis
    volumes:
      - ./src:/app/src
      - ./package*.json:/app/
    command: npm run dev

  db:
    image: postgres:14-alpine
    environment:
      - POSTGRES_DB=careercraft_dev
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  stripe-cli:
    image: stripe/stripe-cli:latest
    environment:
      - STRIPE_API_KEY=${STRIPE_SECRET_KEY}
    command: listen --forward-to app:3000/api/webhooks/stripe
    depends_on:
      - app

volumes:
  postgres_data:
  redis_data:
```

## CI/CD Pipeline

### GitHub Actions Workflow
```yaml
name: Payment Integration CI/CD

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

env:
  NODE_VERSION: '18'
  AWS_REGION: 'us-east-1'

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:14
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: careercraft_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run linting
        run: npm run lint

      - name: Run type checking
        run: npm run type-check

      - name: Run unit tests
        run: npm run test:coverage
        env:
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/careercraft_test
          REDIS_URL: redis://localhost:6379
          STRIPE_SECRET_KEY: ${{ secrets.STRIPE_TEST_SECRET_KEY }}

      - name: Run integration tests
        run: npm run test:integration
        env:
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/careercraft_test
          REDIS_URL: redis://localhost:6379
          STRIPE_SECRET_KEY: ${{ secrets.STRIPE_TEST_SECRET_KEY }}

      - name: Upload coverage reports
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage/lcov.info

  security:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Run security audit
        run: npm audit --audit-level high

      - name: Run Snyk security scan
        uses: snyk/actions/node@master
        env:
          SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}

  build:
    needs: [test, security]
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Build application
        run: npm run build:production

      - name: Build Docker image
        run: |
          docker build -t careercraft-api:${{ github.sha }} .
          docker tag careercraft-api:${{ github.sha }} careercraft-api:latest

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1

      - name: Push to ECR
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          ECR_REPOSITORY: careercraft-api
        run: |
          docker tag careercraft-api:${{ github.sha }} $ECR_REGISTRY/$ECR_REPOSITORY:${{ github.sha }}
          docker tag careercraft-api:${{ github.sha }} $ECR_REGISTRY/$ECR_REPOSITORY:latest
          docker push $ECR_REGISTRY/$ECR_REPOSITORY:${{ github.sha }}
          docker push $ECR_REGISTRY/$ECR_REPOSITORY:latest

  deploy-staging:
    needs: build
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/develop'
    
    steps:
      - name: Deploy to staging
        run: |
          # Deploy to staging environment
          aws ecs update-service \
            --cluster careercraft-staging \
            --service careercraft-api-staging \
            --force-new-deployment

  deploy-production:
    needs: build
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
      - name: Deploy to production
        run: |
          # Deploy to production environment
          aws ecs update-service \
            --cluster careercraft-production \
            --service careercraft-api-production \
            --force-new-deployment
```

## Infrastructure as Code

### Terraform Configuration
```hcl
# AWS ECS Cluster for CareerCraft API
resource "aws_ecs_cluster" "careercraft" {
  name = "careercraft-${var.environment}"

  setting {
    name  = "containerInsights"
    value = "enabled"
  }
}

# ECS Task Definition
resource "aws_ecs_task_definition" "careercraft_api" {
  family                   = "careercraft-api-${var.environment}"
  network_mode             = "awsvpc"
  requires_compatibilities = ["FARGATE"]
  cpu                      = var.task_cpu
  memory                   = var.task_memory
  execution_role_arn       = aws_iam_role.ecs_execution_role.arn
  task_role_arn           = aws_iam_role.ecs_task_role.arn

  container_definitions = jsonencode([
    {
      name  = "careercraft-api"
      image = "${aws_ecr_repository.careercraft_api.repository_url}:latest"

      portMappings = [
        {
          containerPort = 3000
          protocol      = "tcp"
        }
      ]

      environment = [
        {
          name  = "NODE_ENV"
          value = var.environment
        },
        {
          name  = "PORT"
          value = "3000"
        }
      ]

      secrets = [
        {
          name      = "DATABASE_URL"
          valueFrom = aws_secretsmanager_secret.database_url.arn
        },
        {
          name      = "STRIPE_SECRET_KEY"
          valueFrom = aws_secretsmanager_secret.stripe_secret.arn
        }
      ]

      logConfiguration = {
        logDriver = "awslogs"
        options = {
          awslogs-group         = aws_cloudwatch_log_group.careercraft_api.name
          awslogs-region        = var.aws_region
          awslogs-stream-prefix = "ecs"
        }
      }

      healthCheck = {
        command     = ["CMD-SHELL", "curl -f http://localhost:3000/health || exit 1"]
        interval    = 30
        timeout     = 5
        retries     = 3
        startPeriod = 60
      }
    }
  ])
}

# RDS PostgreSQL Database
resource "aws_db_instance" "careercraft" {
  identifier = "careercraft-${var.environment}"

  engine         = "postgres"
  engine_version = "14.9"
  instance_class = var.db_instance_class

  allocated_storage     = var.db_allocated_storage
  max_allocated_storage = var.db_max_allocated_storage
  storage_type          = "gp2"
  storage_encrypted     = true

  db_name  = "careercraft"
  username = var.db_username
  password = var.db_password

  vpc_security_group_ids = [aws_security_group.rds.id]
  db_subnet_group_name   = aws_db_subnet_group.careercraft.name

  backup_retention_period = var.environment == "production" ? 30 : 7
  backup_window          = "03:00-04:00"
  maintenance_window     = "sun:04:00-sun:05:00"

  skip_final_snapshot = var.environment != "production"
  deletion_protection = var.environment == "production"

  performance_insights_enabled = true
  monitoring_interval         = 60
  monitoring_role_arn        = aws_iam_role.rds_monitoring.arn

  tags = {
    Name        = "careercraft-${var.environment}"
    Environment = var.environment
  }
}

# ElastiCache Redis Cluster
resource "aws_elasticache_replication_group" "careercraft" {
  replication_group_id       = "careercraft-${var.environment}"
  description                = "Redis cluster for CareerCraft ${var.environment}"

  node_type                  = var.redis_node_type
  port                       = 6379
  parameter_group_name       = "default.redis7"

  num_cache_clusters         = var.redis_num_cache_nodes
  automatic_failover_enabled = var.redis_num_cache_nodes > 1
  multi_az_enabled          = var.environment == "production"

  subnet_group_name = aws_elasticache_subnet_group.careercraft.name
  security_group_ids = [aws_security_group.redis.id]

  at_rest_encryption_enabled = true
  transit_encryption_enabled = true

  tags = {
    Name        = "careercraft-${var.environment}"
    Environment = var.environment
  }
}
```

## Monitoring and Alerting

### CloudWatch Alarms
```hcl
# API Response Time Alarm
resource "aws_cloudwatch_metric_alarm" "api_response_time" {
  alarm_name          = "careercraft-api-response-time-${var.environment}"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "2"
  metric_name         = "TargetResponseTime"
  namespace           = "AWS/ApplicationELB"
  period              = "300"
  statistic           = "Average"
  threshold           = "1000"
  alarm_description   = "This metric monitors API response time"
  alarm_actions       = [aws_sns_topic.alerts.arn]

  dimensions = {
    LoadBalancer = aws_lb.careercraft.arn_suffix
  }
}

# Database Connection Alarm
resource "aws_cloudwatch_metric_alarm" "db_connections" {
  alarm_name          = "careercraft-db-connections-${var.environment}"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "2"
  metric_name         = "DatabaseConnections"
  namespace           = "AWS/RDS"
  period              = "300"
  statistic           = "Average"
  threshold           = "80"
  alarm_description   = "This metric monitors database connections"
  alarm_actions       = [aws_sns_topic.alerts.arn]

  dimensions = {
    DBInstanceIdentifier = aws_db_instance.careercraft.id
  }
}

# Payment Processing Error Rate
resource "aws_cloudwatch_metric_alarm" "payment_errors" {
  alarm_name          = "careercraft-payment-errors-${var.environment}"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "1"
  metric_name         = "PaymentErrors"
  namespace           = "CareerCraft/Payments"
  period              = "300"
  statistic           = "Sum"
  threshold           = "5"
  alarm_description   = "This metric monitors payment processing errors"
  alarm_actions       = [aws_sns_topic.critical_alerts.arn]
}
```

### Application Monitoring
```javascript
// Custom metrics for payment processing
const cloudwatch = new AWS.CloudWatch()

class PaymentMetrics {
  static async recordPaymentSuccess(amount, currency) {
    await cloudwatch.putMetricData({
      Namespace: 'CareerCraft/Payments',
      MetricData: [
        {
          MetricName: 'PaymentSuccess',
          Value: 1,
          Unit: 'Count',
          Dimensions: [
            {
              Name: 'Currency',
              Value: currency
            }
          ]
        },
        {
          MetricName: 'PaymentAmount',
          Value: amount,
          Unit: 'None',
          Dimensions: [
            {
              Name: 'Currency',
              Value: currency
            }
          ]
        }
      ]
    }).promise()
  }

  static async recordPaymentFailure(errorType, currency) {
    await cloudwatch.putMetricData({
      Namespace: 'CareerCraft/Payments',
      MetricData: [
        {
          MetricName: 'PaymentErrors',
          Value: 1,
          Unit: 'Count',
          Dimensions: [
            {
              Name: 'ErrorType',
              Value: errorType
            },
            {
              Name: 'Currency',
              Value: currency
            }
          ]
        }
      ]
    }).promise()
  }
}
```

## Deployment Procedures

### Pre-Deployment Checklist
- [ ] All tests passing (unit, integration, e2e)
- [ ] Security scans completed
- [ ] Database migrations tested
- [ ] Environment variables configured
- [ ] Stripe webhooks configured
- [ ] Monitoring dashboards updated
- [ ] Rollback plan prepared

### Deployment Steps
1. **Database Migration**
   ```bash
   npm run migrate:production
   ```

2. **Application Deployment**
   ```bash
   aws ecs update-service \
     --cluster careercraft-production \
     --service careercraft-api-production \
     --force-new-deployment
   ```

3. **Health Check Verification**
   ```bash
   curl -f https://api.careercraft.com/health
   ```

4. **Smoke Tests**
   ```bash
   npm run test:smoke:production
   ```

### Post-Deployment Verification
- [ ] Application health checks passing
- [ ] Payment processing functional
- [ ] Webhook delivery working
- [ ] Database connections stable
- [ ] Monitoring alerts configured
- [ ] Performance metrics within thresholds

## Rollback Procedures

### Automatic Rollback Triggers
- Health check failures for >5 minutes
- Error rate >5% for >2 minutes
- Payment processing failures >10% for >1 minute

### Manual Rollback Steps
1. **Identify Previous Stable Version**
   ```bash
   aws ecs describe-services \
     --cluster careercraft-production \
     --services careercraft-api-production
   ```

2. **Rollback Application**
   ```bash
   aws ecs update-service \
     --cluster careercraft-production \
     --service careercraft-api-production \
     --task-definition careercraft-api-production:PREVIOUS_REVISION
   ```

3. **Rollback Database (if needed)**
   ```bash
   npm run migrate:rollback:production
   ```

4. **Verify Rollback**
   ```bash
   npm run test:smoke:production
   ```

## Security Considerations

### Secrets Management
- All sensitive data stored in AWS Secrets Manager
- Automatic rotation for database passwords
- Stripe keys managed separately for each environment
- Regular security audits and key rotation

### Network Security
- VPC with private subnets for database and cache
- Security groups with minimal required access
- WAF protection for public endpoints
- SSL/TLS encryption for all communications

### Compliance
- PCI DSS compliance for payment processing
- GDPR compliance for data protection
- SOC 2 Type II certification
- Regular security assessments

This comprehensive build and deployment documentation ensures reliable, secure, and scalable deployment of the Stripe payment integration features.
