# CareerCraft Simple Setup Script (Windows PowerShell)
# Run as Administrator

Write-Host "🚀 CareerCraft Simple Setup" -ForegroundColor Green
Write-Host "===========================" -ForegroundColor Green

# Check if running as Administrator
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "❌ Please run this script as Administrator" -ForegroundColor Red
    exit 1
}

# Step 1: Install Chocolatey if not installed
if (!(Get-Command choco -ErrorAction SilentlyContinue)) {
    Write-Host "📦 Installing Chocolatey..." -ForegroundColor Yellow
    Set-ExecutionPolicy Bypass -Scope Process -Force
    [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072
    iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
}

# Step 2: Install prerequisites
Write-Host "📦 Installing prerequisites..." -ForegroundColor Yellow
choco install nodejs postgresql redis-64 git vscode stripe-cli -y

# Refresh environment
refreshenv

# Step 3: Create project directory
$projectName = "careercraft-local"
Write-Host "📁 Creating project: $projectName" -ForegroundColor Yellow

if (Test-Path $projectName) {
    $response = Read-Host "Directory exists. Remove it? (y/n)"
    if ($response -eq "y") {
        Remove-Item -Recurse -Force $projectName
    } else {
        exit 1
    }
}

# Step 4: Create Next.js project
Write-Host "📦 Creating Next.js project..." -ForegroundColor Yellow
npx create-next-app@latest $projectName --typescript --tailwind --eslint --app --src-dir --import-alias "@/*"

Set-Location $projectName

# Step 5: Install dependencies
Write-Host "📦 Installing dependencies..." -ForegroundColor Yellow

# Core dependencies
npm install @prisma/client prisma next-auth "@auth/prisma-adapter" stripe "@stripe/stripe-js" openai

# UI dependencies
npm install "@radix-ui/react-dialog" "@radix-ui/react-dropdown-menu" lucide-react

# Form dependencies
npm install "@hookform/resolvers" react-hook-form zod

# Utility dependencies
npm install axios swr recharts date-fns clsx tailwind-merge

# Dev dependencies
npm install -D "@types/node" "@types/react" "@types/react-dom" jest "@testing-library/react" "@testing-library/jest-dom" playwright "@playwright/test" prisma-erd-generator cross-env concurrently tsx

# Step 6: Create basic files
Write-Host "📝 Creating configuration files..." -ForegroundColor Yellow

# Create .env.local
@'
# Database
DATABASE_URL="postgresql://careercraft_user:local_password@localhost:5432/careercraft_local"

# NextAuth
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-local-secret-key-change-this-in-production"

# Google OAuth
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"

# Stripe (Test Mode)
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY="pk_test_..."
STRIPE_SECRET_KEY="sk_test_..."

# OpenAI
OPENAI_API_KEY="sk-..."

# App Settings
NODE_ENV="development"
NEXT_PUBLIC_APP_URL="http://localhost:3000"
'@ | Out-File -FilePath ".env.local" -Encoding UTF8

# Create basic Prisma schema
New-Item -ItemType Directory -Path "prisma" -Force
@'
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  name      String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("users")
}
'@ | Out-File -FilePath "prisma/schema.prisma" -Encoding UTF8

# Step 7: Database setup
Write-Host "🗄️ Setting up database..." -ForegroundColor Yellow

# Start PostgreSQL service
Start-Service postgresql-x64-14 -ErrorAction SilentlyContinue

# Create database and user
try {
    & psql -U postgres -c "CREATE USER careercraft_user WITH PASSWORD 'local_password';" 2>$null
    & psql -U postgres -c "CREATE DATABASE careercraft_local OWNER careercraft_user;" 2>$null
    & psql -U postgres -c "GRANT ALL PRIVILEGES ON DATABASE careercraft_local TO careercraft_user;" 2>$null
    Write-Host "✅ Database created successfully!" -ForegroundColor Green
} catch {
    Write-Host "⚠️ Database setup may have issues. Please check PostgreSQL installation." -ForegroundColor Yellow
}

# Generate Prisma client
npx prisma generate
npx prisma db push

# Step 8: Create startup script
@'
@echo off
echo Starting CareerCraft Development Server
echo ======================================

echo Checking PostgreSQL...
pg_isready -h localhost -p 5432 >nul 2>&1
if %errorlevel% neq 0 (
    echo PostgreSQL not running. Please start it from Services.
    pause
    exit /b 1
)

echo Starting development server...
echo Visit: http://localhost:3000

npm run dev
'@ | Out-File -FilePath "start-dev.bat" -Encoding ASCII

Write-Host ""
Write-Host "🎉 Setup Complete!" -ForegroundColor Green
Write-Host "==================" -ForegroundColor Green
Write-Host ""
Write-Host "📝 Next steps:" -ForegroundColor Yellow
Write-Host "1. Add your API keys to .env.local" -ForegroundColor White
Write-Host "2. Run: start-dev.bat" -ForegroundColor White
Write-Host "3. Visit: http://localhost:3000" -ForegroundColor White
Write-Host ""
Write-Host "🔧 Useful commands:" -ForegroundColor Yellow
Write-Host "   npm run dev       - Start development server" -ForegroundColor White
Write-Host "   npx prisma studio - Open database admin" -ForegroundColor White
Write-Host ""

Write-Host "Press any key to exit..." -ForegroundColor Yellow
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
