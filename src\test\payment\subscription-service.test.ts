/**
 * Subscription Service Unit Tests
 * 
 * Comprehensive test suite for subscription management,
 * plan changes, and billing cycle operations.
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { SubscriptionService } from '../../services/payment/subscription-service'
import { PricingService } from '../../services/payment/pricing-service'
import { UsageService } from '../../services/payment/usage-service'

vi.mock('../../services/payment/pricing-service')
vi.mock('../../services/payment/usage-service')
vi.mock('../../services/database/database-service')

describe('SubscriptionService', () => {
  let subscriptionService: SubscriptionService
  let mockPricingService: any
  let mockUsageService: any
  let mockDatabaseService: any

  beforeEach(() => {
    mockPricingService = {
      getPlan: vi.fn(),
      calculateProration: vi.fn(),
      getAvailableUpgrades: vi.fn(),
      getAvailableDowngrades: vi.fn()
    }

    mockUsageService = {
      getCurrentUsage: vi.fn(),
      resetUsage: vi.fn(),
      checkLimits: vi.fn()
    }

    mockDatabaseService = {
      subscriptions: {
        findByUserId: vi.fn(),
        findById: vi.fn(),
        create: vi.fn(),
        update: vi.fn(),
        delete: vi.fn()
      },
      plans: {
        findById: vi.fn(),
        findAll: vi.fn()
      }
    }

    subscriptionService = new SubscriptionService(
      mockPricingService,
      mockUsageService,
      mockDatabaseService
    )
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('getUserSubscription', () => {
    it('should return active subscription for user', async () => {
      const userId = 'user-123'
      const mockSubscription = {
        id: 'sub-123',
        userId,
        planId: 'premium-monthly',
        status: 'active',
        currentPeriodStart: new Date('2024-01-01'),
        currentPeriodEnd: new Date('2024-02-01'),
        cancelAtPeriodEnd: false
      }

      mockDatabaseService.subscriptions.findByUserId.mockResolvedValue(mockSubscription)

      const result = await subscriptionService.getUserSubscription(userId)

      expect(result).toEqual(mockSubscription)
      expect(mockDatabaseService.subscriptions.findByUserId).toHaveBeenCalledWith(userId)
    })

    it('should return null for user without subscription', async () => {
      const userId = 'user-456'

      mockDatabaseService.subscriptions.findByUserId.mockResolvedValue(null)

      const result = await subscriptionService.getUserSubscription(userId)

      expect(result).toBeNull()
    })
  })

  describe('getSubscriptionStatus', () => {
    it('should return detailed subscription status', async () => {
      const userId = 'user-123'
      const mockSubscription = {
        id: 'sub-123',
        userId,
        planId: 'premium-monthly',
        status: 'active',
        currentPeriodStart: new Date('2024-01-01'),
        currentPeriodEnd: new Date('2024-02-01'),
        cancelAtPeriodEnd: false
      }

      const mockPlan = {
        id: 'premium-monthly',
        name: 'Premium Monthly',
        features: ['autofill', 'ai_customization'],
        limits: { applicationsPerMonth: -1 }
      }

      const mockUsage = {
        applicationsThisMonth: 25,
        aiCustomizationsThisMonth: 10
      }

      mockDatabaseService.subscriptions.findByUserId.mockResolvedValue(mockSubscription)
      mockPricingService.getPlan.mockResolvedValue(mockPlan)
      mockUsageService.getCurrentUsage.mockResolvedValue(mockUsage)

      const result = await subscriptionService.getSubscriptionStatus(userId)

      expect(result.subscription).toEqual(mockSubscription)
      expect(result.plan).toEqual(mockPlan)
      expect(result.usage).toEqual(mockUsage)
      expect(result.isActive).toBe(true)
      expect(result.daysUntilRenewal).toBeGreaterThan(0)
    })

    it('should handle canceled subscription', async () => {
      const userId = 'user-123'
      const mockSubscription = {
        id: 'sub-123',
        status: 'canceled',
        canceledAt: new Date('2024-01-15'),
        currentPeriodEnd: new Date('2024-02-01')
      }

      mockDatabaseService.subscriptions.findByUserId.mockResolvedValue(mockSubscription)

      const result = await subscriptionService.getSubscriptionStatus(userId)

      expect(result.isActive).toBe(false)
      expect(result.isCanceled).toBe(true)
      expect(result.accessUntil).toEqual(mockSubscription.currentPeriodEnd)
    })
  })

  describe('changePlan', () => {
    it('should upgrade plan successfully', async () => {
      const subscriptionId = 'sub-123'
      const newPlanId = 'enterprise-monthly'

      const mockSubscription = {
        id: subscriptionId,
        planId: 'premium-monthly',
        status: 'active'
      }

      const mockNewPlan = {
        id: newPlanId,
        name: 'Enterprise Monthly',
        priceCents: 2999
      }

      const mockProration = {
        amount: 2000,
        isUpgrade: true,
        effectiveDate: new Date()
      }

      mockDatabaseService.subscriptions.findById.mockResolvedValue(mockSubscription)
      mockPricingService.getPlan.mockResolvedValue(mockNewPlan)
      mockPricingService.calculateProration.mockResolvedValue(mockProration)
      mockDatabaseService.subscriptions.update.mockResolvedValue({
        ...mockSubscription,
        planId: newPlanId
      })

      const result = await subscriptionService.changePlan(subscriptionId, newPlanId)

      expect(result.success).toBe(true)
      expect(result.subscription.planId).toBe(newPlanId)
      expect(result.proration).toEqual(mockProration)
      expect(mockDatabaseService.subscriptions.update).toHaveBeenCalledWith(
        subscriptionId,
        expect.objectContaining({ planId: newPlanId })
      )
    })

    it('should handle downgrade with end-of-period change', async () => {
      const subscriptionId = 'sub-123'
      const newPlanId = 'basic-monthly'

      const mockSubscription = {
        id: subscriptionId,
        planId: 'premium-monthly',
        status: 'active',
        currentPeriodEnd: new Date('2024-02-01')
      }

      const mockNewPlan = {
        id: newPlanId,
        priceCents: 499
      }

      mockDatabaseService.subscriptions.findById.mockResolvedValue(mockSubscription)
      mockPricingService.getPlan.mockResolvedValue(mockNewPlan)
      mockPricingService.calculateProration.mockResolvedValue({
        amount: -500,
        isUpgrade: false
      })

      const result = await subscriptionService.changePlan(subscriptionId, newPlanId, {
        changeAtPeriodEnd: true
      })

      expect(result.success).toBe(true)
      expect(result.effectiveDate).toEqual(mockSubscription.currentPeriodEnd)
      expect(mockDatabaseService.subscriptions.update).toHaveBeenCalledWith(
        subscriptionId,
        expect.objectContaining({
          pendingPlanChange: newPlanId,
          planChangeEffectiveDate: mockSubscription.currentPeriodEnd
        })
      )
    })

    it('should reject invalid plan change', async () => {
      const subscriptionId = 'sub-123'
      const invalidPlanId = 'invalid-plan'

      mockDatabaseService.subscriptions.findById.mockResolvedValue({
        id: subscriptionId,
        planId: 'premium-monthly'
      })
      mockPricingService.getPlan.mockResolvedValue(null)

      const result = await subscriptionService.changePlan(subscriptionId, invalidPlanId)

      expect(result.success).toBe(false)
      expect(result.error).toContain('Invalid plan')
    })
  })

  describe('pauseSubscription', () => {
    it('should pause subscription for specified duration', async () => {
      const subscriptionId = 'sub-123'
      const pauseDuration = 30 // days

      const mockSubscription = {
        id: subscriptionId,
        status: 'active',
        currentPeriodEnd: new Date('2024-02-01')
      }

      mockDatabaseService.subscriptions.findById.mockResolvedValue(mockSubscription)
      mockDatabaseService.subscriptions.update.mockResolvedValue({
        ...mockSubscription,
        status: 'paused',
        pausedAt: new Date(),
        resumeAt: new Date(Date.now() + pauseDuration * 24 * 60 * 60 * 1000)
      })

      const result = await subscriptionService.pauseSubscription(subscriptionId, pauseDuration)

      expect(result.success).toBe(true)
      expect(result.subscription.status).toBe('paused')
      expect(result.subscription.resumeAt).toBeDefined()
    })

    it('should reject pause for already paused subscription', async () => {
      const subscriptionId = 'sub-123'

      mockDatabaseService.subscriptions.findById.mockResolvedValue({
        id: subscriptionId,
        status: 'paused'
      })

      const result = await subscriptionService.pauseSubscription(subscriptionId, 30)

      expect(result.success).toBe(false)
      expect(result.error).toContain('already paused')
    })
  })

  describe('resumeSubscription', () => {
    it('should resume paused subscription', async () => {
      const subscriptionId = 'sub-123'

      const mockSubscription = {
        id: subscriptionId,
        status: 'paused',
        pausedAt: new Date('2024-01-15'),
        resumeAt: new Date('2024-02-15')
      }

      mockDatabaseService.subscriptions.findById.mockResolvedValue(mockSubscription)
      mockDatabaseService.subscriptions.update.mockResolvedValue({
        ...mockSubscription,
        status: 'active',
        pausedAt: null,
        resumeAt: null
      })

      const result = await subscriptionService.resumeSubscription(subscriptionId)

      expect(result.success).toBe(true)
      expect(result.subscription.status).toBe('active')
      expect(result.subscription.pausedAt).toBeNull()
    })
  })

  describe('getAvailablePlans', () => {
    it('should return available upgrade and downgrade options', async () => {
      const userId = 'user-123'

      const mockSubscription = {
        planId: 'premium-monthly'
      }

      const mockUpgrades = [
        { id: 'enterprise-monthly', name: 'Enterprise Monthly' }
      ]

      const mockDowngrades = [
        { id: 'basic-monthly', name: 'Basic Monthly' }
      ]

      mockDatabaseService.subscriptions.findByUserId.mockResolvedValue(mockSubscription)
      mockPricingService.getAvailableUpgrades.mockResolvedValue(mockUpgrades)
      mockPricingService.getAvailableDowngrades.mockResolvedValue(mockDowngrades)

      const result = await subscriptionService.getAvailablePlans(userId)

      expect(result.currentPlan).toBe('premium-monthly')
      expect(result.upgrades).toEqual(mockUpgrades)
      expect(result.downgrades).toEqual(mockDowngrades)
    })
  })

  describe('processRenewal', () => {
    it('should process subscription renewal successfully', async () => {
      const subscriptionId = 'sub-123'

      const mockSubscription = {
        id: subscriptionId,
        status: 'active',
        currentPeriodEnd: new Date('2024-02-01')
      }

      const newPeriodEnd = new Date('2024-03-01')

      mockDatabaseService.subscriptions.findById.mockResolvedValue(mockSubscription)
      mockDatabaseService.subscriptions.update.mockResolvedValue({
        ...mockSubscription,
        currentPeriodStart: mockSubscription.currentPeriodEnd,
        currentPeriodEnd: newPeriodEnd
      })
      mockUsageService.resetUsage.mockResolvedValue(true)

      const result = await subscriptionService.processRenewal(subscriptionId)

      expect(result.success).toBe(true)
      expect(result.subscription.currentPeriodEnd).toEqual(newPeriodEnd)
      expect(mockUsageService.resetUsage).toHaveBeenCalledWith(
        mockSubscription.userId,
        mockSubscription.currentPeriodEnd
      )
    })

    it('should handle renewal failure', async () => {
      const subscriptionId = 'sub-123'

      mockDatabaseService.subscriptions.findById.mockResolvedValue({
        id: subscriptionId,
        status: 'past_due'
      })

      const result = await subscriptionService.processRenewal(subscriptionId)

      expect(result.success).toBe(false)
      expect(result.error).toContain('Cannot renew subscription')
    })
  })

  describe('applyDiscount', () => {
    it('should apply discount to subscription', async () => {
      const subscriptionId = 'sub-123'
      const discountCode = 'SAVE20'

      const mockSubscription = {
        id: subscriptionId,
        status: 'active'
      }

      const mockDiscount = {
        code: discountCode,
        type: 'percentage',
        value: 20,
        validUntil: new Date('2024-12-31')
      }

      mockDatabaseService.subscriptions.findById.mockResolvedValue(mockSubscription)
      mockDatabaseService.discounts = {
        findByCode: vi.fn().mockResolvedValue(mockDiscount)
      }
      mockDatabaseService.subscriptions.update.mockResolvedValue({
        ...mockSubscription,
        discountCode,
        discountAppliedAt: new Date()
      })

      const result = await subscriptionService.applyDiscount(subscriptionId, discountCode)

      expect(result.success).toBe(true)
      expect(result.subscription.discountCode).toBe(discountCode)
      expect(result.discount).toEqual(mockDiscount)
    })

    it('should reject invalid discount code', async () => {
      const subscriptionId = 'sub-123'
      const invalidCode = 'INVALID'

      mockDatabaseService.subscriptions.findById.mockResolvedValue({
        id: subscriptionId
      })
      mockDatabaseService.discounts = {
        findByCode: vi.fn().mockResolvedValue(null)
      }

      const result = await subscriptionService.applyDiscount(subscriptionId, invalidCode)

      expect(result.success).toBe(false)
      expect(result.error).toContain('Invalid discount code')
    })
  })

  describe('getSubscriptionMetrics', () => {
    it('should return subscription analytics', async () => {
      const userId = 'user-123'
      const dateRange = {
        start: new Date('2024-01-01'),
        end: new Date('2024-01-31')
      }

      const mockMetrics = {
        totalSpent: 999,
        paymentsCount: 1,
        averageMonthlySpend: 999,
        subscriptionDuration: 30,
        featuresUsed: ['autofill', 'ai_customization'],
        usageStats: {
          applicationsSubmitted: 45,
          aiCustomizations: 12
        }
      }

      mockDatabaseService.subscriptions.findByUserId.mockResolvedValue({
        userId,
        createdAt: new Date('2024-01-01')
      })
      mockDatabaseService.payments = {
        getMetricsByUserId: vi.fn().mockResolvedValue(mockMetrics)
      }
      mockUsageService.getUsageStats = vi.fn().mockResolvedValue(mockMetrics.usageStats)

      const result = await subscriptionService.getSubscriptionMetrics(userId, dateRange)

      expect(result.totalSpent).toBe(999)
      expect(result.usageStats.applicationsSubmitted).toBe(45)
      expect(result.featuresUsed).toContain('autofill')
    })
  })

  describe('scheduleSubscriptionChanges', () => {
    it('should process scheduled plan changes', async () => {
      const today = new Date()
      const mockSubscriptions = [
        {
          id: 'sub-123',
          pendingPlanChange: 'basic-monthly',
          planChangeEffectiveDate: today
        }
      ]

      mockDatabaseService.subscriptions.findScheduledChanges = vi.fn()
        .mockResolvedValue(mockSubscriptions)
      mockDatabaseService.subscriptions.update.mockResolvedValue({
        id: 'sub-123',
        planId: 'basic-monthly',
        pendingPlanChange: null
      })

      const result = await subscriptionService.processScheduledChanges()

      expect(result.processedCount).toBe(1)
      expect(mockDatabaseService.subscriptions.update).toHaveBeenCalledWith(
        'sub-123',
        expect.objectContaining({
          planId: 'basic-monthly',
          pendingPlanChange: null,
          planChangeEffectiveDate: null
        })
      )
    })
  })
})
