/**
 * Template Sync Service
 * 
 * Handles cloud-based template synchronization, versioning, and conflict resolution
 */

import { prisma } from '@/lib/db'
import { z } from 'zod'

// Template sync schemas
export const TemplateSyncDataSchema = z.object({
  templateId: z.string(),
  userId: z.string(),
  templateData: z.object({
    name: z.string(),
    description: z.string().optional(),
    category: z.string().optional(),
    tags: z.array(z.string()).optional(),
    config: z.any(),
    preview: z.string().optional(),
    thumbnail: z.string().optional(),
    difficulty: z.enum(['beginner', 'intermediate', 'advanced']).optional()
  }),
  lastModified: z.date(),
  checksum: z.string()
})

export const ConflictResolutionSchema = z.object({
  conflictId: z.string(),
  resolution: z.enum(['prefer_local', 'prefer_remote', 'manual_merge']),
  mergedData: z.any().optional()
})

export const TemplateVersionSchema = z.object({
  templateId: z.string(),
  versionName: z.string().optional(),
  changesSummary: z.string().optional(),
  templateData: z.any()
})

export interface SyncResult {
  success: boolean
  syncedTemplates: number
  conflicts: TemplateConflict[]
  errors: SyncError[]
}

export interface TemplateConflict {
  id: string
  templateId: string
  conflictType: 'concurrent_edit' | 'version_mismatch'
  localVersion: any
  remoteVersion: any
  timestamp: Date
}

export interface SyncError {
  templateId: string
  error: string
  code: string
}

export interface TemplateMetadata {
  size: number
  checksum: string
  lastModified: Date
  version: number
}

export class TemplateSyncService {
  /**
   * Synchronize all templates for a user
   */
  async syncUserTemplates(userId: string): Promise<SyncResult> {
    try {
      const result: SyncResult = {
        success: true,
        syncedTemplates: 0,
        conflicts: [],
        errors: []
      }

      // Get user's templates that need syncing
      const templates = await prisma.template.findMany({
        where: {
          OR: [
            { createdBy: userId },
            {
              shares: {
                some: {
                  sharedWith: userId,
                  permissionLevel: { in: ['edit', 'admin'] }
                }
              }
            }
          ]
        },
        include: {
          cloudStorage: {
            where: { userId }
          },
          versions: {
            orderBy: { versionNumber: 'desc' },
            take: 1
          }
        }
      })

      for (const template of templates) {
        try {
          await this.syncSingleTemplate(template.id, userId)
          result.syncedTemplates++
        } catch (error) {
          result.errors.push({
            templateId: template.id,
            error: error instanceof Error ? error.message : 'Unknown error',
            code: 'SYNC_ERROR'
          })
        }
      }

      // Check for conflicts
      const conflicts = await this.detectConflicts(userId)
      result.conflicts = conflicts

      return result
    } catch (error) {
      console.error('Error syncing user templates:', error)
      throw new Error('Failed to sync templates')
    }
  }

  /**
   * Synchronize a single template
   */
  async syncSingleTemplate(templateId: string, userId: string): Promise<void> {
    try {
      const template = await prisma.template.findUnique({
        where: { id: templateId },
        include: {
          cloudStorage: {
            where: { userId }
          },
          versions: {
            orderBy: { versionNumber: 'desc' },
            take: 1
          }
        }
      })

      if (!template) {
        throw new Error('Template not found')
      }

      const cloudStorage = template.cloudStorage[0]
      const latestVersion = template.versions[0]

      // Check if template needs syncing
      if (cloudStorage && cloudStorage.syncStatus === 'synced') {
        const localChecksum = this.calculateChecksum(template)
        if (localChecksum === cloudStorage.checksum) {
          return // Already in sync
        }
      }

      // Create new version if template has changed
      if (latestVersion) {
        const currentChecksum = this.calculateChecksum(template)
        const versionChecksum = this.calculateChecksum(JSON.parse(latestVersion.templateData))
        
        if (currentChecksum !== versionChecksum) {
          await this.createTemplateVersion(templateId, {
            templateData: template,
            changesSummary: 'Auto-sync update'
          }, userId)
        }
      } else {
        // Create initial version
        await this.createTemplateVersion(templateId, {
          templateData: template,
          changesSummary: 'Initial version'
        }, userId)
      }

      // Update cloud storage record
      await this.updateCloudStorage(templateId, userId, template)

    } catch (error) {
      console.error('Error syncing single template:', error)
      throw error
    }
  }

  /**
   * Detect synchronization conflicts
   */
  async detectConflicts(userId: string): Promise<TemplateConflict[]> {
    try {
      const conflicts = await prisma.templateSyncConflict.findMany({
        where: {
          userId,
          resolvedAt: null
        },
        include: {
          template: true
        }
      })

      return conflicts.map(conflict => ({
        id: conflict.id,
        templateId: conflict.templateId,
        conflictType: conflict.conflictType as 'concurrent_edit' | 'version_mismatch',
        localVersion: JSON.parse(conflict.localVersion),
        remoteVersion: JSON.parse(conflict.remoteVersion),
        timestamp: conflict.createdAt
      }))
    } catch (error) {
      console.error('Error detecting conflicts:', error)
      return []
    }
  }

  /**
   * Resolve a synchronization conflict
   */
  async resolveConflict(conflictId: string, resolution: z.infer<typeof ConflictResolutionSchema>): Promise<void> {
    try {
      const validatedResolution = ConflictResolutionSchema.parse(resolution)
      
      const conflict = await prisma.templateSyncConflict.findUnique({
        where: { id: conflictId },
        include: { template: true }
      })

      if (!conflict) {
        throw new Error('Conflict not found')
      }

      let resolvedData: any

      switch (validatedResolution.resolution) {
        case 'prefer_local':
          resolvedData = JSON.parse(conflict.localVersion)
          break
        case 'prefer_remote':
          resolvedData = JSON.parse(conflict.remoteVersion)
          break
        case 'manual_merge':
          if (!validatedResolution.mergedData) {
            throw new Error('Merged data required for manual resolution')
          }
          resolvedData = validatedResolution.mergedData
          break
        default:
          throw new Error('Invalid resolution strategy')
      }

      // Update template with resolved data
      await prisma.template.update({
        where: { id: conflict.templateId },
        data: {
          name: resolvedData.name,
          description: resolvedData.description,
          category: resolvedData.category,
          tags: resolvedData.tags ? JSON.stringify(resolvedData.tags) : null,
          config: resolvedData.config ? JSON.stringify(resolvedData.config) : null,
          preview: resolvedData.preview,
          thumbnail: resolvedData.thumbnail,
          difficulty: resolvedData.difficulty,
          updatedAt: new Date()
        }
      })

      // Mark conflict as resolved
      await prisma.templateSyncConflict.update({
        where: { id: conflictId },
        data: {
          resolutionStrategy: validatedResolution.resolution,
          resolvedAt: new Date()
        }
      })

      // Create new version with resolved data
      await this.createTemplateVersion(conflict.templateId, {
        templateData: resolvedData,
        changesSummary: `Conflict resolved: ${validatedResolution.resolution}`
      }, conflict.userId)

    } catch (error) {
      console.error('Error resolving conflict:', error)
      throw error
    }
  }

  /**
   * Create a new template version
   */
  async createTemplateVersion(
    templateId: string, 
    versionData: z.infer<typeof TemplateVersionSchema>,
    userId: string
  ): Promise<string> {
    try {
      const validatedData = TemplateVersionSchema.parse({
        ...versionData,
        templateId
      })

      // Get next version number
      const latestVersion = await prisma.templateVersion.findFirst({
        where: { templateId },
        orderBy: { versionNumber: 'desc' }
      })

      const nextVersionNumber = (latestVersion?.versionNumber || 0) + 1

      const version = await prisma.templateVersion.create({
        data: {
          templateId,
          versionNumber: nextVersionNumber,
          versionName: validatedData.versionName || `Version ${nextVersionNumber}`,
          changesSummary: validatedData.changesSummary,
          templateData: JSON.stringify(validatedData.templateData),
          createdBy: userId
        }
      })

      return version.id
    } catch (error) {
      console.error('Error creating template version:', error)
      throw error
    }
  }

  /**
   * Get template version history
   */
  async getVersionHistory(templateId: string): Promise<any[]> {
    try {
      const versions = await prisma.templateVersion.findMany({
        where: { templateId },
        include: {
          creator: {
            select: {
              id: true,
              name: true,
              email: true
            }
          }
        },
        orderBy: { versionNumber: 'desc' }
      })

      return versions.map(version => ({
        id: version.id,
        versionNumber: version.versionNumber,
        versionName: version.versionName,
        changesSummary: version.changesSummary,
        createdBy: version.creator,
        createdAt: version.createdAt,
        templateData: JSON.parse(version.templateData)
      }))
    } catch (error) {
      console.error('Error getting version history:', error)
      throw error
    }
  }

  /**
   * Rollback to a specific version
   */
  async rollbackToVersion(templateId: string, versionId: string, userId: string): Promise<void> {
    try {
      const version = await prisma.templateVersion.findUnique({
        where: { id: versionId }
      })

      if (!version || version.templateId !== templateId) {
        throw new Error('Version not found')
      }

      const versionData = JSON.parse(version.templateData)

      // Update template with version data
      await prisma.template.update({
        where: { id: templateId },
        data: {
          name: versionData.name,
          description: versionData.description,
          category: versionData.category,
          tags: versionData.tags ? JSON.stringify(versionData.tags) : null,
          config: versionData.config ? JSON.stringify(versionData.config) : null,
          preview: versionData.preview,
          thumbnail: versionData.thumbnail,
          difficulty: versionData.difficulty,
          updatedAt: new Date()
        }
      })

      // Create new version for rollback
      await this.createTemplateVersion(templateId, {
        templateData: versionData,
        changesSummary: `Rolled back to version ${version.versionNumber}`
      }, userId)

    } catch (error) {
      console.error('Error rolling back to version:', error)
      throw error
    }
  }

  /**
   * Calculate checksum for template data
   */
  private calculateChecksum(data: any): string {
    const crypto = require('crypto')
    const content = JSON.stringify(data, Object.keys(data).sort())
    return crypto.createHash('md5').update(content).digest('hex')
  }

  /**
   * Update cloud storage record
   */
  private async updateCloudStorage(templateId: string, userId: string, template: any): Promise<void> {
    try {
      const checksum = this.calculateChecksum(template)
      const templateSize = JSON.stringify(template).length

      await prisma.templateCloudStorage.upsert({
        where: {
          templateId_userId: {
            templateId,
            userId
          }
        },
        update: {
          checksum,
          fileSize: templateSize,
          lastSynced: new Date(),
          syncStatus: 'synced',
          updatedAt: new Date()
        },
        create: {
          templateId,
          userId,
          cloudUrl: `templates/${templateId}/${userId}`,
          storageProvider: 'local', // Can be extended to support AWS S3, etc.
          fileSize: templateSize,
          checksum,
          syncStatus: 'synced'
        }
      })
    } catch (error) {
      console.error('Error updating cloud storage:', error)
      throw error
    }
  }

  /**
   * Get sync status for user templates
   */
  async getSyncStatus(userId: string): Promise<any> {
    try {
      const cloudStorage = await prisma.templateCloudStorage.findMany({
        where: { userId },
        include: {
          template: {
            select: {
              id: true,
              name: true,
              updatedAt: true
            }
          }
        }
      })

      const conflicts = await prisma.templateSyncConflict.count({
        where: {
          userId,
          resolvedAt: null
        }
      })

      return {
        totalTemplates: cloudStorage.length,
        syncedTemplates: cloudStorage.filter(cs => cs.syncStatus === 'synced').length,
        pendingSync: cloudStorage.filter(cs => cs.syncStatus === 'pending').length,
        syncErrors: cloudStorage.filter(cs => cs.syncStatus === 'error').length,
        conflicts,
        lastSyncTime: cloudStorage.reduce((latest, cs) => 
          cs.lastSynced > latest ? cs.lastSynced : latest, 
          new Date(0)
        )
      }
    } catch (error) {
      console.error('Error getting sync status:', error)
      throw error
    }
  }
}

export const templateSyncService = new TemplateSyncService()
