{"name": "@careercraft/types", "version": "2.0.0", "private": true, "main": "./src/index.ts", "types": "./src/index.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "type-check": "tsc --noEmit", "clean": "rm -rf dist .turbo node_modules/.cache"}, "dependencies": {"zod": "^3.22.4"}, "devDependencies": {"@types/node": "^20.10.0", "typescript": "^5.3.0"}}