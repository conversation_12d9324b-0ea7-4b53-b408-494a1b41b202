/**
 * Payment System Unit Tests
 * 
 * Tests for Stripe integration, subscription management, and feature access control
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { StripeService } from '@/lib/payments/stripe-service'
import { SubscriptionService } from '@/lib/payments/subscription-service'
import { FeatureGate, FEATURES } from '@/lib/payments/feature-gate'
import { WebhookHandler } from '@/lib/payments/webhook-handler'

// Mock Stripe
const mockStripe = {
  customers: {
    create: vi.fn(),
    update: vi.fn(),
    retrieve: vi.fn()
  },
  subscriptions: {
    create: vi.fn(),
    update: vi.fn(),
    cancel: vi.fn(),
    retrieve: vi.fn(),
    list: vi.fn()
  },
  paymentIntents: {
    create: vi.fn(),
    confirm: vi.fn()
  },
  invoices: {
    create: vi.fn(),
    finalizeInvoice: vi.fn(),
    sendInvoice: vi.fn(),
    list: vi.fn()
  },
  invoiceItems: {
    create: vi.fn()
  },
  coupons: {
    create: vi.fn()
  },
  paymentMethods: {
    list: vi.fn()
  },
  billingPortal: {
    sessions: {
      create: vi.fn()
    }
  },
  checkout: {
    sessions: {
      create: vi.fn()
    }
  },
  webhooks: {
    constructEvent: vi.fn()
  }
}

vi.mock('stripe', () => {
  return {
    default: vi.fn(() => mockStripe)
  }
})

// Mock Prisma
const mockPrisma = {
  subscriptionPlan: {
    findMany: vi.fn(),
    findUnique: vi.fn(),
    upsert: vi.fn()
  },
  userSubscription: {
    findUnique: vi.fn(),
    findFirst: vi.fn(),
    create: vi.fn(),
    update: vi.fn(),
    updateMany: vi.fn(),
    upsert: vi.fn(),
    count: vi.fn()
  },
  payment: {
    create: vi.fn(),
    findMany: vi.fn(),
    aggregate: vi.fn()
  },
  featureUsage: {
    upsert: vi.fn(),
    aggregate: vi.fn(),
    groupBy: vi.fn(),
    findMany: vi.fn()
  },
  billingEvent: {
    create: vi.fn(),
    updateMany: vi.fn()
  },
  coupon: {
    findUnique: vi.fn(),
    update: vi.fn(),
    upsert: vi.fn(),
    deleteMany: vi.fn()
  },
  couponUsage: {
    create: vi.fn(),
    deleteMany: vi.fn()
  },
  user: {
    findUnique: vi.fn()
  }
}

vi.mock('@/lib/db', () => ({
  prisma: mockPrisma
}))

describe('Payment System', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('StripeService', () => {
    let stripeService: StripeService

    beforeEach(() => {
      stripeService = new StripeService()
    })

    describe('createCustomer', () => {
      it('should create a new Stripe customer', async () => {
        const customerData = {
          email: '<EMAIL>',
          name: 'Test User'
        }

        const mockCustomer = {
          id: 'cus_test123',
          email: '<EMAIL>',
          name: 'Test User'
        }

        mockStripe.customers.create.mockResolvedValue(mockCustomer)

        const result = await stripeService.createCustomer(customerData)

        expect(mockStripe.customers.create).toHaveBeenCalledWith({
          email: '<EMAIL>',
          name: 'Test User',
          metadata: {}
        })
        expect(result).toEqual(mockCustomer)
      })

      it('should handle customer creation errors', async () => {
        mockStripe.customers.create.mockRejectedValue(new Error('Stripe error'))

        await expect(stripeService.createCustomer({
          email: '<EMAIL>'
        })).rejects.toThrow('Failed to create customer')
      })
    })

    describe('createSubscription', () => {
      it('should create a new subscription', async () => {
        const subscriptionData = {
          customerId: 'cus_test123',
          priceId: 'price_test123'
        }

        const mockSubscription = {
          id: 'sub_test123',
          customer: 'cus_test123',
          status: 'active',
          current_period_start: 1640995200,
          current_period_end: 1643673600
        }

        mockStripe.subscriptions.create.mockResolvedValue(mockSubscription)

        const result = await stripeService.createSubscription(subscriptionData)

        expect(mockStripe.subscriptions.create).toHaveBeenCalledWith({
          customer: 'cus_test123',
          items: [{ price: 'price_test123' }],
          payment_behavior: 'default_incomplete',
          payment_settings: { save_default_payment_method: 'on_subscription' },
          expand: ['latest_invoice.payment_intent'],
          metadata: {}
        })
        expect(result).toEqual(mockSubscription)
      })

      it('should create subscription with trial period', async () => {
        const subscriptionData = {
          customerId: 'cus_test123',
          priceId: 'price_test123',
          trialDays: 14
        }

        mockStripe.subscriptions.create.mockResolvedValue({
          id: 'sub_test123',
          trial_period_days: 14
        })

        await stripeService.createSubscription(subscriptionData)

        expect(mockStripe.subscriptions.create).toHaveBeenCalledWith(
          expect.objectContaining({
            trial_period_days: 14
          })
        )
      })
    })

    describe('createPaymentIntent', () => {
      it('should create a payment intent', async () => {
        const paymentData = {
          amount: 9.99,
          currency: 'usd',
          customerId: 'cus_test123'
        }

        const mockPaymentIntent = {
          id: 'pi_test123',
          amount: 999,
          currency: 'usd',
          customer: 'cus_test123'
        }

        mockStripe.paymentIntents.create.mockResolvedValue(mockPaymentIntent)

        const result = await stripeService.createPaymentIntent(paymentData)

        expect(mockStripe.paymentIntents.create).toHaveBeenCalledWith({
          amount: 999, // Converted to cents
          currency: 'usd',
          customer: 'cus_test123',
          description: undefined,
          metadata: {},
          setup_future_usage: undefined,
          automatic_payment_methods: { enabled: true }
        })
        expect(result).toEqual(mockPaymentIntent)
      })
    })

    describe('verifyWebhookSignature', () => {
      it('should verify webhook signature', () => {
        const payload = 'test payload'
        const signature = 'test signature'
        const secret = 'test secret'
        const mockEvent = { id: 'evt_test123', type: 'customer.created' }

        mockStripe.webhooks.constructEvent.mockReturnValue(mockEvent)

        const result = stripeService.verifyWebhookSignature(payload, signature, secret)

        expect(mockStripe.webhooks.constructEvent).toHaveBeenCalledWith(
          payload,
          signature,
          secret
        )
        expect(result).toEqual(mockEvent)
      })

      it('should handle invalid webhook signature', () => {
        mockStripe.webhooks.constructEvent.mockImplementation(() => {
          throw new Error('Invalid signature')
        })

        expect(() => {
          stripeService.verifyWebhookSignature('payload', 'signature', 'secret')
        }).toThrow('Invalid webhook signature')
      })
    })
  })

  describe('SubscriptionService', () => {
    let subscriptionService: SubscriptionService

    beforeEach(() => {
      subscriptionService = new SubscriptionService()
    })

    describe('getPlans', () => {
      it('should return active subscription plans', async () => {
        const mockPlans = [
          {
            id: 'free',
            name: 'Free',
            priceMonthly: 0,
            features: '{"maxResumes": 1}',
            isActive: true,
            sortOrder: 1
          },
          {
            id: 'pro',
            name: 'Pro',
            priceMonthly: 9.99,
            features: '{"maxResumes": -1}',
            isActive: true,
            sortOrder: 2
          }
        ]

        mockPrisma.subscriptionPlan.findMany.mockResolvedValue(mockPlans)

        const result = await subscriptionService.getPlans()

        expect(mockPrisma.subscriptionPlan.findMany).toHaveBeenCalledWith({
          where: { isActive: true },
          orderBy: { sortOrder: 'asc' }
        })
        expect(result).toHaveLength(2)
        expect(result[0].features).toEqual({ maxResumes: 1 })
      })
    })

    describe('getUserSubscription', () => {
      it('should return user subscription with plan details', async () => {
        const mockSubscription = {
          id: 'sub_test123',
          userId: 'user_test123',
          status: 'active',
          plan: {
            id: 'pro',
            name: 'Pro',
            features: '{"maxResumes": -1}'
          }
        }

        mockPrisma.userSubscription.findUnique.mockResolvedValue(mockSubscription)

        const result = await subscriptionService.getUserSubscription('user_test123')

        expect(mockPrisma.userSubscription.findUnique).toHaveBeenCalledWith({
          where: { userId: 'user_test123' },
          include: { plan: true }
        })
        expect(result?.plan.features).toEqual({ maxResumes: -1 })
      })

      it('should return null for non-existent subscription', async () => {
        mockPrisma.userSubscription.findUnique.mockResolvedValue(null)

        const result = await subscriptionService.getUserSubscription('user_test123')

        expect(result).toBeNull()
      })
    })

    describe('createSubscription', () => {
      it('should create a new subscription', async () => {
        const subscriptionData = {
          userId: 'user_test123',
          planId: 'pro',
          paymentMethodId: 'pm_test123'
        }

        const mockUser = {
          id: 'user_test123',
          email: '<EMAIL>',
          name: 'Test User'
        }

        const mockPlan = {
          id: 'pro',
          name: 'Pro',
          stripePriceIdMonthly: 'price_test123'
        }

        const mockStripeCustomer = { id: 'cus_test123' }
        const mockStripeSubscription = {
          id: 'sub_test123',
          status: 'active',
          current_period_start: 1640995200,
          current_period_end: 1643673600
        }

        const mockDbSubscription = {
          id: 'db_sub_123',
          userId: 'user_test123',
          planId: 'pro',
          plan: mockPlan
        }

        mockPrisma.user.findUnique.mockResolvedValue(mockUser)
        mockPrisma.subscriptionPlan.findUnique.mockResolvedValue(mockPlan)
        mockPrisma.userSubscription.findUnique.mockResolvedValue(null)
        mockPrisma.userSubscription.findFirst.mockResolvedValue(null)
        mockStripe.customers.create.mockResolvedValue(mockStripeCustomer)
        mockStripe.subscriptions.create.mockResolvedValue(mockStripeSubscription)
        mockPrisma.userSubscription.create.mockResolvedValue(mockDbSubscription)

        const result = await subscriptionService.createSubscription(subscriptionData)

        expect(mockStripe.customers.create).toHaveBeenCalledWith({
          email: '<EMAIL>',
          name: 'Test User',
          metadata: { userId: 'user_test123' }
        })
        expect(mockStripe.subscriptions.create).toHaveBeenCalled()
        expect(mockPrisma.userSubscription.create).toHaveBeenCalled()
        expect(result.subscription).toBeDefined()
      })

      it('should throw error for existing subscription', async () => {
        const subscriptionData = {
          userId: 'user_test123',
          planId: 'pro'
        }

        mockPrisma.user.findUnique.mockResolvedValue({ id: 'user_test123' })
        mockPrisma.subscriptionPlan.findUnique.mockResolvedValue({ id: 'pro' })
        mockPrisma.userSubscription.findUnique.mockResolvedValue({ id: 'existing' })

        await expect(subscriptionService.createSubscription(subscriptionData))
          .rejects.toThrow('User already has an active subscription')
      })
    })

    describe('cancelSubscription', () => {
      it('should cancel subscription at period end', async () => {
        const mockSubscription = {
          id: 'sub_test123',
          stripeSubscriptionId: 'sub_stripe123',
          plan: { features: '{}' }
        }

        const mockUpdatedSubscription = {
          ...mockSubscription,
          cancelAtPeriodEnd: true,
          canceledAt: new Date()
        }

        mockPrisma.userSubscription.findUnique.mockResolvedValue(mockSubscription)
        mockStripe.subscriptions.update.mockResolvedValue({})
        mockPrisma.userSubscription.update.mockResolvedValue(mockUpdatedSubscription)

        const result = await subscriptionService.cancelSubscription('user_test123')

        expect(mockStripe.subscriptions.update).toHaveBeenCalledWith(
          'sub_stripe123',
          { cancel_at_period_end: true }
        )
        expect(result.cancelAtPeriodEnd).toBe(true)
      })
    })
  })

  describe('FeatureGate', () => {
    let featureGate: FeatureGate

    beforeEach(() => {
      featureGate = new FeatureGate()
    })

    describe('checkAccess', () => {
      it('should allow access for premium features with valid subscription', async () => {
        const mockSubscription = {
          status: 'active',
          plan: {
            features: {
              advancedAI: true,
              customTemplates: true
            }
          }
        }

        // Mock the subscription service
        vi.doMock('@/lib/payments/subscription-service', () => ({
          subscriptionService: {
            getUserSubscription: vi.fn().mockResolvedValue(mockSubscription)
          }
        }))

        const result = await featureGate.checkAccess('user_test123', FEATURES.ADVANCED_AI)

        expect(result.hasAccess).toBe(true)
      })

      it('should deny access for premium features without subscription', async () => {
        // Mock no subscription
        vi.doMock('@/lib/payments/subscription-service', () => ({
          subscriptionService: {
            getUserSubscription: vi.fn().mockResolvedValue(null)
          }
        }))

        const result = await featureGate.checkAccess('user_test123', FEATURES.ADVANCED_AI)

        expect(result.hasAccess).toBe(false)
        expect(result.upgradeRequired).toBe(true)
      })

      it('should allow access to free features', async () => {
        // Mock no subscription
        vi.doMock('@/lib/payments/subscription-service', () => ({
          subscriptionService: {
            getUserSubscription: vi.fn().mockResolvedValue(null)
          }
        }))

        const result = await featureGate.checkAccess('user_test123', FEATURES.RESUME_CREATION)

        expect(result.hasAccess).toBe(true)
      })
    })

    describe('checkUsageLimit', () => {
      it('should return unlimited usage for premium plans', async () => {
        const mockSubscription = {
          plan: {
            features: {
              aiSuggestionsLimit: -1
            }
          }
        }

        vi.doMock('@/lib/payments/subscription-service', () => ({
          subscriptionService: {
            getUserSubscription: vi.fn().mockResolvedValue(mockSubscription)
          }
        }))

        const result = await featureGate.checkUsageLimit('user_test123', FEATURES.AI_SUGGESTIONS)

        expect(result.remaining).toBe('unlimited')
        expect(result.allowed).toBe(true)
      })

      it('should track usage limits for free plans', async () => {
        mockPrisma.featureUsage.aggregate.mockResolvedValue({
          _sum: { usageCount: 3 }
        })

        const result = await featureGate.checkUsageLimit('user_test123', FEATURES.AI_SUGGESTIONS)

        expect(result.remaining).toBe(2) // 5 limit - 3 used = 2 remaining
        expect(result.allowed).toBe(true)
      })

      it('should deny usage when limit exceeded', async () => {
        mockPrisma.featureUsage.aggregate.mockResolvedValue({
          _sum: { usageCount: 5 }
        })

        const result = await featureGate.checkUsageLimit('user_test123', FEATURES.AI_SUGGESTIONS)

        expect(result.remaining).toBe(0)
        expect(result.allowed).toBe(false)
      })
    })

    describe('trackUsage', () => {
      it('should track feature usage', async () => {
        const usageData = {
          userId: 'user_test123',
          feature: FEATURES.AI_SUGGESTIONS,
          amount: 1
        }

        mockPrisma.featureUsage.upsert.mockResolvedValue({})

        await featureGate.trackUsage(usageData)

        expect(mockPrisma.featureUsage.upsert).toHaveBeenCalledWith({
          where: {
            userId_featureName_usageDate: {
              userId: 'user_test123',
              featureName: FEATURES.AI_SUGGESTIONS,
              usageDate: expect.any(Date)
            }
          },
          update: {
            usageCount: { increment: 1 },
            metadata: undefined
          },
          create: {
            userId: 'user_test123',
            featureName: FEATURES.AI_SUGGESTIONS,
            usageCount: 1,
            usageDate: expect.any(Date),
            metadata: undefined
          }
        })
      })

      it('should handle tracking errors gracefully', async () => {
        mockPrisma.featureUsage.upsert.mockRejectedValue(new Error('Database error'))

        // Should not throw error
        await expect(featureGate.trackUsage({
          userId: 'user_test123',
          feature: FEATURES.AI_SUGGESTIONS,
          amount: 1
        })).resolves.not.toThrow()
      })
    })
  })

  describe('WebhookHandler', () => {
    let webhookHandler: WebhookHandler

    beforeEach(() => {
      webhookHandler = new WebhookHandler()
    })

    describe('handleWebhook', () => {
      it('should process subscription created event', async () => {
        const mockEvent = {
          id: 'evt_test123',
          type: 'customer.subscription.created',
          data: {
            object: {
              id: 'sub_test123',
              customer: 'cus_test123',
              status: 'active',
              current_period_start: 1640995200,
              current_period_end: 1643673600,
              metadata: {
                userId: 'user_test123',
                planId: 'pro'
              }
            }
          }
        }

        mockPrisma.billingEvent.create.mockResolvedValue({})
        mockPrisma.userSubscription.upsert.mockResolvedValue({})
        mockPrisma.billingEvent.updateMany.mockResolvedValue({})

        await webhookHandler.handleWebhook(mockEvent)

        expect(mockPrisma.userSubscription.upsert).toHaveBeenCalledWith({
          where: { userId: 'user_test123' },
          update: expect.objectContaining({
            stripeSubscriptionId: 'sub_test123',
            status: 'active'
          }),
          create: expect.objectContaining({
            userId: 'user_test123',
            planId: 'pro',
            stripeSubscriptionId: 'sub_test123'
          })
        })
      })

      it('should process payment succeeded event', async () => {
        const mockEvent = {
          id: 'evt_test123',
          type: 'payment_intent.succeeded',
          data: {
            object: {
              id: 'pi_test123',
              customer: 'cus_test123',
              amount: 999,
              currency: 'usd',
              status: 'succeeded'
            }
          }
        }

        const mockSubscription = {
          id: 'sub_test123',
          userId: 'user_test123'
        }

        mockPrisma.billingEvent.create.mockResolvedValue({})
        mockPrisma.userSubscription.findFirst.mockResolvedValue(mockSubscription)
        mockPrisma.payment.create.mockResolvedValue({})
        mockPrisma.billingEvent.updateMany.mockResolvedValue({})

        await webhookHandler.handleWebhook(mockEvent)

        expect(mockPrisma.payment.create).toHaveBeenCalledWith({
          data: expect.objectContaining({
            userId: 'user_test123',
            subscriptionId: 'sub_test123',
            stripePaymentIntentId: 'pi_test123',
            amount: 9.99,
            status: 'succeeded'
          })
        })
      })

      it('should log billing events', async () => {
        const mockEvent = {
          id: 'evt_test123',
          type: 'customer.created',
          data: { object: {} }
        }

        mockPrisma.billingEvent.create.mockResolvedValue({})
        mockPrisma.billingEvent.updateMany.mockResolvedValue({})

        await webhookHandler.handleWebhook(mockEvent)

        expect(mockPrisma.billingEvent.create).toHaveBeenCalledWith({
          data: {
            userId: null,
            eventType: 'customer.created',
            stripeEventId: 'evt_test123',
            data: JSON.stringify({ object: {} }),
            processed: false
          }
        })
      })
    })
  })

  describe('Integration Tests', () => {
    it('should handle complete subscription flow', async () => {
      // Mock complete flow from subscription creation to feature access
      const mockUser = { id: 'user_test123', email: '<EMAIL>' }
      const mockPlan = { id: 'pro', stripePriceIdMonthly: 'price_test123' }
      const mockSubscription = {
        id: 'sub_test123',
        userId: 'user_test123',
        status: 'active',
        plan: {
          features: { advancedAI: true, aiSuggestionsLimit: -1 }
        }
      }

      mockPrisma.user.findUnique.mockResolvedValue(mockUser)
      mockPrisma.subscriptionPlan.findUnique.mockResolvedValue(mockPlan)
      mockPrisma.userSubscription.findUnique.mockResolvedValue(null)
      mockPrisma.userSubscription.create.mockResolvedValue(mockSubscription)
      mockStripe.customers.create.mockResolvedValue({ id: 'cus_test123' })
      mockStripe.subscriptions.create.mockResolvedValue({
        id: 'sub_stripe123',
        status: 'active',
        current_period_start: 1640995200,
        current_period_end: 1643673600
      })

      const subscriptionService = new SubscriptionService()
      const featureGate = new FeatureGate()

      // Create subscription
      const result = await subscriptionService.createSubscription({
        userId: 'user_test123',
        planId: 'pro'
      })

      expect(result.subscription).toBeDefined()

      // Mock subscription for feature access check
      vi.doMock('@/lib/payments/subscription-service', () => ({
        subscriptionService: {
          getUserSubscription: vi.fn().mockResolvedValue(mockSubscription)
        }
      }))

      // Check feature access
      const accessResult = await featureGate.checkAccess('user_test123', FEATURES.ADVANCED_AI)
      expect(accessResult.hasAccess).toBe(true)
    })
  })
})
