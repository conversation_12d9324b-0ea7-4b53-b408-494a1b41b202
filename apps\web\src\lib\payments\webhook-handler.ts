/**
 * Stripe Webhook Handler
 * 
 * Processes Stripe webhook events for subscription and payment updates
 */

import { prisma } from '@/lib/db'
import { stripeService } from './stripe-service'
import type Stripe from 'stripe'

export class WebhookHandler {
  /**
   * Process Stripe webhook event
   */
  async handleWebhook(event: Stripe.Event): Promise<void> {
    try {
      // Log the event
      await this.logBillingEvent(event)

      // Process based on event type
      switch (event.type) {
        // Customer events
        case 'customer.created':
          await this.handleCustomerCreated(event.data.object as Stripe.Customer)
          break
        case 'customer.updated':
          await this.handleCustomerUpdated(event.data.object as Stripe.Customer)
          break
        case 'customer.deleted':
          await this.handleCustomerDeleted(event.data.object as Stripe.Customer)
          break

        // Subscription events
        case 'customer.subscription.created':
          await this.handleSubscriptionCreated(event.data.object as Stripe.Subscription)
          break
        case 'customer.subscription.updated':
          await this.handleSubscriptionUpdated(event.data.object as Stripe.Subscription)
          break
        case 'customer.subscription.deleted':
          await this.handleSubscriptionDeleted(event.data.object as Stripe.Subscription)
          break
        case 'customer.subscription.trial_will_end':
          await this.handleTrialWillEnd(event.data.object as Stripe.Subscription)
          break

        // Payment events
        case 'payment_intent.succeeded':
          await this.handlePaymentSucceeded(event.data.object as Stripe.PaymentIntent)
          break
        case 'payment_intent.payment_failed':
          await this.handlePaymentFailed(event.data.object as Stripe.PaymentIntent)
          break

        // Invoice events
        case 'invoice.created':
          await this.handleInvoiceCreated(event.data.object as Stripe.Invoice)
          break
        case 'invoice.payment_succeeded':
          await this.handleInvoicePaymentSucceeded(event.data.object as Stripe.Invoice)
          break
        case 'invoice.payment_failed':
          await this.handleInvoicePaymentFailed(event.data.object as Stripe.Invoice)
          break
        case 'invoice.upcoming':
          await this.handleInvoiceUpcoming(event.data.object as Stripe.Invoice)
          break

        // Payment method events
        case 'payment_method.attached':
          await this.handlePaymentMethodAttached(event.data.object as Stripe.PaymentMethod)
          break

        default:
          console.log(`Unhandled event type: ${event.type}`)
      }

      // Mark event as processed
      await this.markEventProcessed(event.id)
    } catch (error) {
      console.error(`Error processing webhook event ${event.id}:`, error)
      throw error
    }
  }

  /**
   * Handle customer created
   */
  private async handleCustomerCreated(customer: Stripe.Customer): Promise<void> {
    try {
      const userId = customer.metadata?.userId
      if (!userId) return

      // Update user subscription with customer ID
      await prisma.userSubscription.updateMany({
        where: { userId },
        data: { stripeCustomerId: customer.id }
      })

      console.log(`Customer created: ${customer.id} for user: ${userId}`)
    } catch (error) {
      console.error('Error handling customer created:', error)
    }
  }

  /**
   * Handle customer updated
   */
  private async handleCustomerUpdated(customer: Stripe.Customer): Promise<void> {
    try {
      // Update customer information if needed
      console.log(`Customer updated: ${customer.id}`)
    } catch (error) {
      console.error('Error handling customer updated:', error)
    }
  }

  /**
   * Handle customer deleted
   */
  private async handleCustomerDeleted(customer: Stripe.Customer): Promise<void> {
    try {
      // Handle customer deletion
      await prisma.userSubscription.updateMany({
        where: { stripeCustomerId: customer.id },
        data: { 
          stripeCustomerId: null,
          status: 'canceled'
        }
      })

      console.log(`Customer deleted: ${customer.id}`)
    } catch (error) {
      console.error('Error handling customer deleted:', error)
    }
  }

  /**
   * Handle subscription created
   */
  private async handleSubscriptionCreated(subscription: Stripe.Subscription): Promise<void> {
    try {
      const customerId = subscription.customer as string
      const userId = subscription.metadata?.userId

      if (!userId) {
        console.warn(`No userId in subscription metadata: ${subscription.id}`)
        return
      }

      // Update subscription record
      await prisma.userSubscription.upsert({
        where: { userId },
        update: {
          stripeSubscriptionId: subscription.id,
          status: subscription.status,
          currentPeriodStart: new Date(subscription.current_period_start * 1000),
          currentPeriodEnd: new Date(subscription.current_period_end * 1000),
          trialStart: subscription.trial_start 
            ? new Date(subscription.trial_start * 1000) 
            : null,
          trialEnd: subscription.trial_end 
            ? new Date(subscription.trial_end * 1000) 
            : null
        },
        create: {
          userId,
          planId: subscription.metadata?.planId || '',
          stripeCustomerId: customerId,
          stripeSubscriptionId: subscription.id,
          status: subscription.status,
          currentPeriodStart: new Date(subscription.current_period_start * 1000),
          currentPeriodEnd: new Date(subscription.current_period_end * 1000),
          trialStart: subscription.trial_start 
            ? new Date(subscription.trial_start * 1000) 
            : null,
          trialEnd: subscription.trial_end 
            ? new Date(subscription.trial_end * 1000) 
            : null
        }
      })

      console.log(`Subscription created: ${subscription.id} for user: ${userId}`)
    } catch (error) {
      console.error('Error handling subscription created:', error)
    }
  }

  /**
   * Handle subscription updated
   */
  private async handleSubscriptionUpdated(subscription: Stripe.Subscription): Promise<void> {
    try {
      await prisma.userSubscription.updateMany({
        where: { stripeSubscriptionId: subscription.id },
        data: {
          status: subscription.status,
          currentPeriodStart: new Date(subscription.current_period_start * 1000),
          currentPeriodEnd: new Date(subscription.current_period_end * 1000),
          cancelAtPeriodEnd: subscription.cancel_at_period_end,
          canceledAt: subscription.canceled_at 
            ? new Date(subscription.canceled_at * 1000) 
            : null,
          trialStart: subscription.trial_start 
            ? new Date(subscription.trial_start * 1000) 
            : null,
          trialEnd: subscription.trial_end 
            ? new Date(subscription.trial_end * 1000) 
            : null
        }
      })

      console.log(`Subscription updated: ${subscription.id}`)
    } catch (error) {
      console.error('Error handling subscription updated:', error)
    }
  }

  /**
   * Handle subscription deleted
   */
  private async handleSubscriptionDeleted(subscription: Stripe.Subscription): Promise<void> {
    try {
      await prisma.userSubscription.updateMany({
        where: { stripeSubscriptionId: subscription.id },
        data: {
          status: 'canceled',
          canceledAt: new Date()
        }
      })

      console.log(`Subscription deleted: ${subscription.id}`)
    } catch (error) {
      console.error('Error handling subscription deleted:', error)
    }
  }

  /**
   * Handle trial will end
   */
  private async handleTrialWillEnd(subscription: Stripe.Subscription): Promise<void> {
    try {
      // Send trial ending notification
      const userSubscription = await prisma.userSubscription.findFirst({
        where: { stripeSubscriptionId: subscription.id },
        include: { user: true }
      })

      if (userSubscription) {
        // TODO: Send email notification about trial ending
        console.log(`Trial ending for user: ${userSubscription.userId}`)
      }
    } catch (error) {
      console.error('Error handling trial will end:', error)
    }
  }

  /**
   * Handle payment succeeded
   */
  private async handlePaymentSucceeded(paymentIntent: Stripe.PaymentIntent): Promise<void> {
    try {
      const customerId = paymentIntent.customer as string
      
      // Find user by customer ID
      const subscription = await prisma.userSubscription.findFirst({
        where: { stripeCustomerId: customerId }
      })

      if (!subscription) return

      // Record payment
      await prisma.payment.create({
        data: {
          userId: subscription.userId,
          subscriptionId: subscription.id,
          stripePaymentIntentId: paymentIntent.id,
          amount: paymentIntent.amount / 100, // Convert from cents
          currency: paymentIntent.currency.toUpperCase(),
          status: 'succeeded',
          paymentMethod: paymentIntent.payment_method_types[0] || 'unknown',
          description: paymentIntent.description || 'Subscription payment',
          metadata: JSON.stringify(paymentIntent.metadata)
        }
      })

      console.log(`Payment succeeded: ${paymentIntent.id} for user: ${subscription.userId}`)
    } catch (error) {
      console.error('Error handling payment succeeded:', error)
    }
  }

  /**
   * Handle payment failed
   */
  private async handlePaymentFailed(paymentIntent: Stripe.PaymentIntent): Promise<void> {
    try {
      const customerId = paymentIntent.customer as string
      
      // Find user by customer ID
      const subscription = await prisma.userSubscription.findFirst({
        where: { stripeCustomerId: customerId }
      })

      if (!subscription) return

      // Record failed payment
      await prisma.payment.create({
        data: {
          userId: subscription.userId,
          subscriptionId: subscription.id,
          stripePaymentIntentId: paymentIntent.id,
          amount: paymentIntent.amount / 100,
          currency: paymentIntent.currency.toUpperCase(),
          status: 'failed',
          paymentMethod: paymentIntent.payment_method_types[0] || 'unknown',
          description: paymentIntent.description || 'Subscription payment',
          metadata: JSON.stringify(paymentIntent.metadata)
        }
      })

      // TODO: Send payment failed notification
      console.log(`Payment failed: ${paymentIntent.id} for user: ${subscription.userId}`)
    } catch (error) {
      console.error('Error handling payment failed:', error)
    }
  }

  /**
   * Handle invoice created
   */
  private async handleInvoiceCreated(invoice: Stripe.Invoice): Promise<void> {
    try {
      console.log(`Invoice created: ${invoice.id}`)
    } catch (error) {
      console.error('Error handling invoice created:', error)
    }
  }

  /**
   * Handle invoice payment succeeded
   */
  private async handleInvoicePaymentSucceeded(invoice: Stripe.Invoice): Promise<void> {
    try {
      const customerId = invoice.customer as string
      
      // Find user by customer ID
      const subscription = await prisma.userSubscription.findFirst({
        where: { stripeCustomerId: customerId }
      })

      if (!subscription) return

      // Update subscription status if needed
      if (subscription.status !== 'active') {
        await prisma.userSubscription.update({
          where: { id: subscription.id },
          data: { status: 'active' }
        })
      }

      console.log(`Invoice payment succeeded: ${invoice.id} for user: ${subscription.userId}`)
    } catch (error) {
      console.error('Error handling invoice payment succeeded:', error)
    }
  }

  /**
   * Handle invoice payment failed
   */
  private async handleInvoicePaymentFailed(invoice: Stripe.Invoice): Promise<void> {
    try {
      const customerId = invoice.customer as string
      
      // Find user by customer ID
      const subscription = await prisma.userSubscription.findFirst({
        where: { stripeCustomerId: customerId }
      })

      if (!subscription) return

      // Update subscription status
      await prisma.userSubscription.update({
        where: { id: subscription.id },
        data: { status: 'past_due' }
      })

      // TODO: Send payment failed notification
      console.log(`Invoice payment failed: ${invoice.id} for user: ${subscription.userId}`)
    } catch (error) {
      console.error('Error handling invoice payment failed:', error)
    }
  }

  /**
   * Handle upcoming invoice
   */
  private async handleInvoiceUpcoming(invoice: Stripe.Invoice): Promise<void> {
    try {
      const customerId = invoice.customer as string
      
      // Find user by customer ID
      const subscription = await prisma.userSubscription.findFirst({
        where: { stripeCustomerId: customerId },
        include: { user: true }
      })

      if (!subscription) return

      // TODO: Send upcoming invoice notification
      console.log(`Upcoming invoice: ${invoice.id} for user: ${subscription.userId}`)
    } catch (error) {
      console.error('Error handling upcoming invoice:', error)
    }
  }

  /**
   * Handle payment method attached
   */
  private async handlePaymentMethodAttached(paymentMethod: Stripe.PaymentMethod): Promise<void> {
    try {
      console.log(`Payment method attached: ${paymentMethod.id}`)
    } catch (error) {
      console.error('Error handling payment method attached:', error)
    }
  }

  /**
   * Log billing event
   */
  private async logBillingEvent(event: Stripe.Event): Promise<void> {
    try {
      // Extract user ID from event data if available
      let userId: string | null = null
      
      if (event.data.object && typeof event.data.object === 'object') {
        const obj = event.data.object as any
        
        // Try to get userId from metadata
        if (obj.metadata?.userId) {
          userId = obj.metadata.userId
        }
        
        // Try to get userId from customer
        if (!userId && obj.customer) {
          const subscription = await prisma.userSubscription.findFirst({
            where: { stripeCustomerId: obj.customer }
          })
          if (subscription) {
            userId = subscription.userId
          }
        }
      }

      await prisma.billingEvent.create({
        data: {
          userId,
          eventType: event.type,
          stripeEventId: event.id,
          data: JSON.stringify(event.data),
          processed: false
        }
      })
    } catch (error) {
      console.error('Error logging billing event:', error)
      // Don't throw error as logging is not critical
    }
  }

  /**
   * Mark event as processed
   */
  private async markEventProcessed(eventId: string): Promise<void> {
    try {
      await prisma.billingEvent.updateMany({
        where: { stripeEventId: eventId },
        data: { processed: true }
      })
    } catch (error) {
      console.error('Error marking event as processed:', error)
      // Don't throw error as this is not critical
    }
  }
}

// Export singleton instance
export const webhookHandler = new WebhookHandler()
