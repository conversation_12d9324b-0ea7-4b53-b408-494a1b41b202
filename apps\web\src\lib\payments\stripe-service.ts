/**
 * Stripe Payment Service
 * 
 * Handles all Stripe payment processing, subscription management, and billing operations
 */

import Stripe from 'stripe'
import { prisma } from '@/lib/db'
import { z } from 'zod'

// Stripe configuration
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2023-10-16',
  typescript: true
})

// Validation schemas
export const CreateCustomerSchema = z.object({
  email: z.string().email(),
  name: z.string().optional(),
  metadata: z.record(z.string()).optional()
})

export const CreateSubscriptionSchema = z.object({
  customerId: z.string(),
  priceId: z.string(),
  trialDays: z.number().optional(),
  couponId: z.string().optional(),
  metadata: z.record(z.string()).optional()
})

export const CreatePaymentIntentSchema = z.object({
  amount: z.number().positive(),
  currency: z.string().default('usd'),
  customerId: z.string(),
  description: z.string().optional(),
  metadata: z.record(z.string()).optional()
})

export interface SubscriptionOptions {
  trialDays?: number
  couponId?: string
  metadata?: Record<string, string>
}

export interface PaymentIntentOptions {
  description?: string
  metadata?: Record<string, string>
  setupFutureUsage?: 'on_session' | 'off_session'
}

export class StripeService {
  /**
   * Create a new Stripe customer
   */
  async createCustomer(data: z.infer<typeof CreateCustomerSchema>): Promise<Stripe.Customer> {
    try {
      const validatedData = CreateCustomerSchema.parse(data)
      
      const customer = await stripe.customers.create({
        email: validatedData.email,
        name: validatedData.name,
        metadata: validatedData.metadata || {}
      })

      return customer
    } catch (error) {
      console.error('Error creating Stripe customer:', error)
      throw new Error('Failed to create customer')
    }
  }

  /**
   * Update an existing Stripe customer
   */
  async updateCustomer(
    customerId: string, 
    updates: Partial<Stripe.CustomerUpdateParams>
  ): Promise<Stripe.Customer> {
    try {
      const customer = await stripe.customers.update(customerId, updates)
      return customer
    } catch (error) {
      console.error('Error updating Stripe customer:', error)
      throw new Error('Failed to update customer')
    }
  }

  /**
   * Get a Stripe customer
   */
  async getCustomer(customerId: string): Promise<Stripe.Customer> {
    try {
      const customer = await stripe.customers.retrieve(customerId)
      
      if (customer.deleted) {
        throw new Error('Customer has been deleted')
      }

      return customer as Stripe.Customer
    } catch (error) {
      console.error('Error retrieving Stripe customer:', error)
      throw new Error('Failed to retrieve customer')
    }
  }

  /**
   * Create a new subscription
   */
  async createSubscription(
    data: z.infer<typeof CreateSubscriptionSchema>
  ): Promise<Stripe.Subscription> {
    try {
      const validatedData = CreateSubscriptionSchema.parse(data)
      
      const subscriptionParams: Stripe.SubscriptionCreateParams = {
        customer: validatedData.customerId,
        items: [{ price: validatedData.priceId }],
        payment_behavior: 'default_incomplete',
        payment_settings: { save_default_payment_method: 'on_subscription' },
        expand: ['latest_invoice.payment_intent'],
        metadata: validatedData.metadata || {}
      }

      // Add trial period if specified
      if (validatedData.trialDays) {
        subscriptionParams.trial_period_days = validatedData.trialDays
      }

      // Add coupon if specified
      if (validatedData.couponId) {
        subscriptionParams.coupon = validatedData.couponId
      }

      const subscription = await stripe.subscriptions.create(subscriptionParams)
      return subscription
    } catch (error) {
      console.error('Error creating subscription:', error)
      throw new Error('Failed to create subscription')
    }
  }

  /**
   * Update an existing subscription
   */
  async updateSubscription(
    subscriptionId: string,
    updates: Partial<Stripe.SubscriptionUpdateParams>
  ): Promise<Stripe.Subscription> {
    try {
      const subscription = await stripe.subscriptions.update(subscriptionId, updates)
      return subscription
    } catch (error) {
      console.error('Error updating subscription:', error)
      throw new Error('Failed to update subscription')
    }
  }

  /**
   * Cancel a subscription
   */
  async cancelSubscription(
    subscriptionId: string,
    cancelAtPeriodEnd: boolean = true
  ): Promise<Stripe.Subscription> {
    try {
      if (cancelAtPeriodEnd) {
        // Cancel at period end
        const subscription = await stripe.subscriptions.update(subscriptionId, {
          cancel_at_period_end: true
        })
        return subscription
      } else {
        // Cancel immediately
        const subscription = await stripe.subscriptions.cancel(subscriptionId)
        return subscription
      }
    } catch (error) {
      console.error('Error canceling subscription:', error)
      throw new Error('Failed to cancel subscription')
    }
  }

  /**
   * Create a payment intent
   */
  async createPaymentIntent(
    data: z.infer<typeof CreatePaymentIntentSchema>,
    options: PaymentIntentOptions = {}
  ): Promise<Stripe.PaymentIntent> {
    try {
      const validatedData = CreatePaymentIntentSchema.parse(data)
      
      const paymentIntent = await stripe.paymentIntents.create({
        amount: Math.round(validatedData.amount * 100), // Convert to cents
        currency: validatedData.currency,
        customer: validatedData.customerId,
        description: options.description,
        metadata: options.metadata || {},
        setup_future_usage: options.setupFutureUsage,
        automatic_payment_methods: { enabled: true }
      })

      return paymentIntent
    } catch (error) {
      console.error('Error creating payment intent:', error)
      throw new Error('Failed to create payment intent')
    }
  }

  /**
   * Confirm a payment intent
   */
  async confirmPaymentIntent(
    paymentIntentId: string,
    paymentMethodId?: string
  ): Promise<Stripe.PaymentIntent> {
    try {
      const confirmParams: Stripe.PaymentIntentConfirmParams = {}
      
      if (paymentMethodId) {
        confirmParams.payment_method = paymentMethodId
      }

      const paymentIntent = await stripe.paymentIntents.confirm(
        paymentIntentId,
        confirmParams
      )

      return paymentIntent
    } catch (error) {
      console.error('Error confirming payment intent:', error)
      throw new Error('Failed to confirm payment')
    }
  }

  /**
   * Create an invoice
   */
  async createInvoice(
    customerId: string,
    items: Array<{ priceId: string; quantity?: number }>
  ): Promise<Stripe.Invoice> {
    try {
      // Add invoice items
      for (const item of items) {
        await stripe.invoiceItems.create({
          customer: customerId,
          price: item.priceId,
          quantity: item.quantity || 1
        })
      }

      // Create and finalize invoice
      const invoice = await stripe.invoices.create({
        customer: customerId,
        auto_advance: true
      })

      await stripe.invoices.finalizeInvoice(invoice.id)
      return invoice
    } catch (error) {
      console.error('Error creating invoice:', error)
      throw new Error('Failed to create invoice')
    }
  }

  /**
   * Send an invoice
   */
  async sendInvoice(invoiceId: string): Promise<Stripe.Invoice> {
    try {
      const invoice = await stripe.invoices.sendInvoice(invoiceId)
      return invoice
    } catch (error) {
      console.error('Error sending invoice:', error)
      throw new Error('Failed to send invoice')
    }
  }

  /**
   * Create a coupon
   */
  async createCoupon(couponData: {
    id?: string
    name: string
    percentOff?: number
    amountOff?: number
    currency?: string
    duration: 'forever' | 'once' | 'repeating'
    durationInMonths?: number
    maxRedemptions?: number
    redeemBy?: number
  }): Promise<Stripe.Coupon> {
    try {
      const coupon = await stripe.coupons.create(couponData)
      return coupon
    } catch (error) {
      console.error('Error creating coupon:', error)
      throw new Error('Failed to create coupon')
    }
  }

  /**
   * Apply a coupon to a subscription
   */
  async applyCoupon(
    subscriptionId: string,
    couponId: string
  ): Promise<Stripe.Subscription> {
    try {
      const subscription = await stripe.subscriptions.update(subscriptionId, {
        coupon: couponId
      })
      return subscription
    } catch (error) {
      console.error('Error applying coupon:', error)
      throw new Error('Failed to apply coupon')
    }
  }

  /**
   * Get customer's payment methods
   */
  async getPaymentMethods(customerId: string): Promise<Stripe.PaymentMethod[]> {
    try {
      const paymentMethods = await stripe.paymentMethods.list({
        customer: customerId,
        type: 'card'
      })
      return paymentMethods.data
    } catch (error) {
      console.error('Error retrieving payment methods:', error)
      throw new Error('Failed to retrieve payment methods')
    }
  }

  /**
   * Create a billing portal session
   */
  async createBillingPortalSession(
    customerId: string,
    returnUrl: string
  ): Promise<Stripe.BillingPortal.Session> {
    try {
      const session = await stripe.billingPortal.sessions.create({
        customer: customerId,
        return_url: returnUrl
      })
      return session
    } catch (error) {
      console.error('Error creating billing portal session:', error)
      throw new Error('Failed to create billing portal session')
    }
  }

  /**
   * Create a checkout session
   */
  async createCheckoutSession(
    customerId: string,
    priceId: string,
    successUrl: string,
    cancelUrl: string,
    options: {
      mode?: 'payment' | 'subscription' | 'setup'
      trialDays?: number
      couponId?: string
      allowPromotionCodes?: boolean
    } = {}
  ): Promise<Stripe.Checkout.Session> {
    try {
      const sessionParams: Stripe.Checkout.SessionCreateParams = {
        customer: customerId,
        payment_method_types: ['card'],
        line_items: [
          {
            price: priceId,
            quantity: 1
          }
        ],
        mode: options.mode || 'subscription',
        success_url: successUrl,
        cancel_url: cancelUrl,
        allow_promotion_codes: options.allowPromotionCodes || true
      }

      // Add subscription-specific options
      if (options.mode === 'subscription') {
        if (options.trialDays) {
          sessionParams.subscription_data = {
            trial_period_days: options.trialDays
          }
        }

        if (options.couponId) {
          sessionParams.discounts = [{ coupon: options.couponId }]
        }
      }

      const session = await stripe.checkout.sessions.create(sessionParams)
      return session
    } catch (error) {
      console.error('Error creating checkout session:', error)
      throw new Error('Failed to create checkout session')
    }
  }

  /**
   * Verify webhook signature
   */
  verifyWebhookSignature(
    payload: string | Buffer,
    signature: string,
    secret: string
  ): Stripe.Event {
    try {
      const event = stripe.webhooks.constructEvent(payload, signature, secret)
      return event
    } catch (error) {
      console.error('Error verifying webhook signature:', error)
      throw new Error('Invalid webhook signature')
    }
  }

  /**
   * Get subscription by ID
   */
  async getSubscription(subscriptionId: string): Promise<Stripe.Subscription> {
    try {
      const subscription = await stripe.subscriptions.retrieve(subscriptionId, {
        expand: ['latest_invoice', 'customer']
      })
      return subscription
    } catch (error) {
      console.error('Error retrieving subscription:', error)
      throw new Error('Failed to retrieve subscription')
    }
  }

  /**
   * List customer's subscriptions
   */
  async getCustomerSubscriptions(customerId: string): Promise<Stripe.Subscription[]> {
    try {
      const subscriptions = await stripe.subscriptions.list({
        customer: customerId,
        status: 'all',
        expand: ['data.latest_invoice']
      })
      return subscriptions.data
    } catch (error) {
      console.error('Error retrieving customer subscriptions:', error)
      throw new Error('Failed to retrieve subscriptions')
    }
  }

  /**
   * Get customer's invoices
   */
  async getCustomerInvoices(
    customerId: string,
    limit: number = 10
  ): Promise<Stripe.Invoice[]> {
    try {
      const invoices = await stripe.invoices.list({
        customer: customerId,
        limit
      })
      return invoices.data
    } catch (error) {
      console.error('Error retrieving customer invoices:', error)
      throw new Error('Failed to retrieve invoices')
    }
  }
}

// Export singleton instance
export const stripeService = new StripeService()
