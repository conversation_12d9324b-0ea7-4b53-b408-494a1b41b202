/**
 * LinkedIn Integration Component Tests
 * 
 * Tests for LinkedIn integration React component
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { LinkedInIntegration } from '@/components/linkedin/LinkedInIntegration'

// Mock fetch for API calls
global.fetch = vi.fn()

// Mock window.open for OAuth popup
Object.defineProperty(window, 'open', {
  writable: true,
  value: vi.fn()
})

// Mock window.addEventListener for popup messages
Object.defineProperty(window, 'addEventListener', {
  writable: true,
  value: vi.fn()
})

Object.defineProperty(window, 'removeEventListener', {
  writable: true,
  value: vi.fn()
})

describe('LinkedInIntegration', () => {
  const mockOnImportComplete = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
    
    // Mock successful status and history API calls by default
    ;(global.fetch as any).mockImplementation((url: string) => {
      if (url.includes('action=status')) {
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve({ connected: false })
        })
      }
      if (url.includes('action=history')) {
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve({ history: [] })
        })
      }
      return Promise.resolve({
        ok: true,
        json: () => Promise.resolve({})
      })
    })
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('Initial State', () => {
    it('should render loading state initially', () => {
      render(<LinkedInIntegration />)
      
      expect(screen.getByText('Loading LinkedIn integration...')).toBeInTheDocument()
    })

    it('should render connection prompt when not connected', async () => {
      render(<LinkedInIntegration />)
      
      await waitFor(() => {
        expect(screen.getByText('Connect Your LinkedIn Profile')).toBeInTheDocument()
      })
      
      expect(screen.getByText('Import your professional information directly from LinkedIn')).toBeInTheDocument()
      expect(screen.getByRole('button', { name: /Connect LinkedIn/i })).toBeInTheDocument()
    })

    it('should render connected state when LinkedIn is connected', async () => {
      ;(global.fetch as any).mockImplementation((url: string) => {
        if (url.includes('action=status')) {
          return Promise.resolve({
            ok: true,
            json: () => Promise.resolve({
              connected: true,
              lastSynced: '2023-12-01T00:00:00Z',
              profileData: {
                name: 'John Doe',
                headline: 'Software Engineer'
              }
            })
          })
        }
        if (url.includes('action=history')) {
          return Promise.resolve({
            ok: true,
            json: () => Promise.resolve({ history: [] })
          })
        }
        return Promise.resolve({ ok: true, json: () => Promise.resolve({}) })
      })

      render(<LinkedInIntegration resumeId="resume-123" />)
      
      await waitFor(() => {
        expect(screen.getByText('Connected to LinkedIn')).toBeInTheDocument()
      })
      
      expect(screen.getByText('John Doe • Software Engineer')).toBeInTheDocument()
      expect(screen.getByText(/Last synced:/)).toBeInTheDocument()
      expect(screen.getByRole('button', { name: /Disconnect/i })).toBeInTheDocument()
    })
  })

  describe('LinkedIn Connection', () => {
    it('should handle LinkedIn connection flow', async () => {
      const user = userEvent.setup()
      const mockPopup = { close: vi.fn(), closed: false }
      
      ;(window.open as any).mockReturnValue(mockPopup)
      ;(global.fetch as any).mockImplementation((url: string) => {
        if (url.includes('action=authorize')) {
          return Promise.resolve({
            ok: true,
            json: () => Promise.resolve({
              authUrl: 'https://linkedin.com/oauth/authorize?client_id=test',
              state: 'test-state'
            })
          })
        }
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve({ connected: false })
        })
      })

      render(<LinkedInIntegration />)
      
      await waitFor(() => {
        expect(screen.getByRole('button', { name: /Connect LinkedIn/i })).toBeInTheDocument()
      })

      const connectButton = screen.getByRole('button', { name: /Connect LinkedIn/i })
      await user.click(connectButton)

      expect(window.open).toHaveBeenCalledWith(
        'https://linkedin.com/oauth/authorize?client_id=test',
        'linkedin-auth',
        'width=600,height=600,scrollbars=yes,resizable=yes'
      )
    })

    it('should handle connection errors', async () => {
      const user = userEvent.setup()
      
      ;(global.fetch as any).mockImplementation((url: string) => {
        if (url.includes('action=authorize')) {
          return Promise.resolve({
            ok: false,
            json: () => Promise.resolve({ error: 'Authorization failed' })
          })
        }
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve({ connected: false })
        })
      })

      render(<LinkedInIntegration />)
      
      await waitFor(() => {
        expect(screen.getByRole('button', { name: /Connect LinkedIn/i })).toBeInTheDocument()
      })

      const connectButton = screen.getByRole('button', { name: /Connect LinkedIn/i })
      await user.click(connectButton)

      await waitFor(() => {
        expect(screen.getByText(/Failed to get authorization URL/)).toBeInTheDocument()
      })
    })

    it('should handle disconnect', async () => {
      const user = userEvent.setup()
      
      ;(global.fetch as any).mockImplementation((url: string, options?: any) => {
        if (url.includes('action=status')) {
          return Promise.resolve({
            ok: true,
            json: () => Promise.resolve({
              connected: true,
              profileData: { name: 'John Doe' }
            })
          })
        }
        if (options?.method === 'DELETE') {
          return Promise.resolve({
            ok: true,
            json: () => Promise.resolve({
              success: true,
              message: 'LinkedIn account disconnected successfully'
            })
          })
        }
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve({ history: [] })
        })
      })

      render(<LinkedInIntegration />)
      
      await waitFor(() => {
        expect(screen.getByText('Connected to LinkedIn')).toBeInTheDocument()
      })

      const disconnectButton = screen.getByRole('button', { name: /Disconnect/i })
      await user.click(disconnectButton)

      await waitFor(() => {
        expect(screen.getByText('LinkedIn account disconnected successfully!')).toBeInTheDocument()
      })
    })
  })

  describe('Data Import', () => {
    beforeEach(() => {
      ;(global.fetch as any).mockImplementation((url: string) => {
        if (url.includes('action=status')) {
          return Promise.resolve({
            ok: true,
            json: () => Promise.resolve({
              connected: true,
              profileData: { name: 'John Doe' }
            })
          })
        }
        if (url.includes('action=history')) {
          return Promise.resolve({
            ok: true,
            json: () => Promise.resolve({ history: [] })
          })
        }
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve({})
        })
      })
    })

    it('should render import section when connected and resumeId provided', async () => {
      render(<LinkedInIntegration resumeId="resume-123" />)
      
      await waitFor(() => {
        expect(screen.getByText('Import Data')).toBeInTheDocument()
      })
      
      expect(screen.getByText('Select which sections to import from your LinkedIn profile')).toBeInTheDocument()
      expect(screen.getByLabelText('Personal Information')).toBeInTheDocument()
      expect(screen.getByLabelText('Work Experience')).toBeInTheDocument()
      expect(screen.getByLabelText('Education')).toBeInTheDocument()
      expect(screen.getByLabelText('Skills')).toBeInTheDocument()
    })

    it('should handle section selection', async () => {
      const user = userEvent.setup()
      
      render(<LinkedInIntegration resumeId="resume-123" />)
      
      await waitFor(() => {
        expect(screen.getByLabelText('Personal Information')).toBeInTheDocument()
      })

      const personalInfoCheckbox = screen.getByLabelText('Personal Information')
      const educationCheckbox = screen.getByLabelText('Education')

      // Personal Info should be checked by default
      expect(personalInfoCheckbox).toBeChecked()
      
      // Check education
      await user.click(educationCheckbox)
      expect(educationCheckbox).toBeChecked()
      
      // Uncheck personal info
      await user.click(personalInfoCheckbox)
      expect(personalInfoCheckbox).not.toBeChecked()
    })

    it('should import selected data successfully', async () => {
      const user = userEvent.setup()
      
      ;(global.fetch as any).mockImplementation((url: string, options?: any) => {
        if (url.includes('action=status')) {
          return Promise.resolve({
            ok: true,
            json: () => Promise.resolve({
              connected: true,
              profileData: { name: 'John Doe' }
            })
          })
        }
        if (options?.method === 'POST' && url.includes('/api/linkedin/import')) {
          return Promise.resolve({
            ok: true,
            json: () => Promise.resolve({
              success: true,
              importId: 'import-123',
              importedData: {
                personalInfo: { firstName: 'John', lastName: 'Doe' },
                experience: []
              },
              message: 'LinkedIn data imported successfully'
            })
          })
        }
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve({ history: [] })
        })
      })

      render(<LinkedInIntegration resumeId="resume-123" onImportComplete={mockOnImportComplete} />)
      
      await waitFor(() => {
        expect(screen.getByRole('button', { name: /Import Selected Data/i })).toBeInTheDocument()
      })

      const importButton = screen.getByRole('button', { name: /Import Selected Data/i })
      await user.click(importButton)

      await waitFor(() => {
        expect(screen.getByText('LinkedIn data imported successfully!')).toBeInTheDocument()
      })

      expect(mockOnImportComplete).toHaveBeenCalledWith({
        personalInfo: { firstName: 'John', lastName: 'Doe' },
        experience: []
      })
    })

    it('should handle import errors', async () => {
      const user = userEvent.setup()
      
      ;(global.fetch as any).mockImplementation((url: string, options?: any) => {
        if (url.includes('action=status')) {
          return Promise.resolve({
            ok: true,
            json: () => Promise.resolve({
              connected: true,
              profileData: { name: 'John Doe' }
            })
          })
        }
        if (options?.method === 'POST' && url.includes('/api/linkedin/import')) {
          return Promise.resolve({
            ok: false,
            json: () => Promise.resolve({
              error: 'Import failed',
              details: ['LinkedIn API error']
            })
          })
        }
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve({ history: [] })
        })
      })

      render(<LinkedInIntegration resumeId="resume-123" />)
      
      await waitFor(() => {
        expect(screen.getByRole('button', { name: /Import Selected Data/i })).toBeInTheDocument()
      })

      const importButton = screen.getByRole('button', { name: /Import Selected Data/i })
      await user.click(importButton)

      await waitFor(() => {
        expect(screen.getByText('Import failed')).toBeInTheDocument()
      })
    })

    it('should disable import button when no sections selected', async () => {
      const user = userEvent.setup()
      
      render(<LinkedInIntegration resumeId="resume-123" />)
      
      await waitFor(() => {
        expect(screen.getByLabelText('Personal Information')).toBeInTheDocument()
      })

      // Uncheck all default selections
      const personalInfoCheckbox = screen.getByLabelText('Personal Information')
      const experienceCheckbox = screen.getByLabelText('Work Experience')
      
      await user.click(personalInfoCheckbox)
      await user.click(experienceCheckbox)

      const importButton = screen.getByRole('button', { name: /Import Selected Data/i })
      expect(importButton).toBeDisabled()
    })
  })

  describe('Import History', () => {
    it('should display import history when available', async () => {
      ;(global.fetch as any).mockImplementation((url: string) => {
        if (url.includes('action=status')) {
          return Promise.resolve({
            ok: true,
            json: () => Promise.resolve({
              connected: true,
              profileData: { name: 'John Doe' }
            })
          })
        }
        if (url.includes('action=history')) {
          return Promise.resolve({
            ok: true,
            json: () => Promise.resolve({
              history: [
                {
                  id: 'import-1',
                  resumeTitle: 'Software Engineer Resume',
                  status: 'completed',
                  createdAt: '2023-12-01T00:00:00Z',
                  importedSections: ['personalInfo', 'experience']
                },
                {
                  id: 'import-2',
                  resumeTitle: 'Data Scientist Resume',
                  status: 'failed',
                  createdAt: '2023-11-30T00:00:00Z',
                  importedSections: ['personalInfo']
                }
              ]
            })
          })
        }
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve({})
        })
      })

      render(<LinkedInIntegration />)
      
      await waitFor(() => {
        expect(screen.getByText('Import History')).toBeInTheDocument()
      })
      
      expect(screen.getByText('Software Engineer Resume')).toBeInTheDocument()
      expect(screen.getByText('Data Scientist Resume')).toBeInTheDocument()
      expect(screen.getByText('personalInfo, experience')).toBeInTheDocument()
      expect(screen.getByText('completed')).toBeInTheDocument()
      expect(screen.getByText('failed')).toBeInTheDocument()
    })

    it('should not display history section when no history exists', async () => {
      render(<LinkedInIntegration />)
      
      await waitFor(() => {
        expect(screen.getByText('Connect Your LinkedIn Profile')).toBeInTheDocument()
      })
      
      expect(screen.queryByText('Import History')).not.toBeInTheDocument()
    })
  })

  describe('Error Handling', () => {
    it('should display error messages', async () => {
      ;(global.fetch as any).mockRejectedValue(new Error('Network error'))

      render(<LinkedInIntegration />)
      
      // Component should still render even with API errors
      await waitFor(() => {
        expect(screen.getByText('Connect Your LinkedIn Profile')).toBeInTheDocument()
      })
    })

    it('should clear error messages when new actions are performed', async () => {
      const user = userEvent.setup()
      
      // First render with error
      ;(global.fetch as any).mockImplementation((url: string) => {
        if (url.includes('action=status')) {
          return Promise.resolve({
            ok: false,
            json: () => Promise.resolve({ error: 'API Error' })
          })
        }
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve({ history: [] })
        })
      })

      render(<LinkedInIntegration />)
      
      await waitFor(() => {
        expect(screen.getByRole('button', { name: /Connect LinkedIn/i })).toBeInTheDocument()
      })

      // Simulate an action that would clear errors
      const connectButton = screen.getByRole('button', { name: /Connect LinkedIn/i })
      await user.click(connectButton)

      // Error should be cleared when new action starts
      expect(screen.queryByText('API Error')).not.toBeInTheDocument()
    })
  })
})
