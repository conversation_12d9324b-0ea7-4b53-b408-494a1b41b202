'use client';

import { ReactNode, useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { Header } from './header';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { Icons } from '@/components/ui/icons';
import { cn } from '@/lib/utils';

interface DashboardLayoutProps {
  children: ReactNode;
}

interface SidebarNavItem {
  title: string;
  href: string;
  icon: keyof typeof Icons;
  badge?: string;
  disabled?: boolean;
}

const sidebarNavItems: SidebarNavItem[] = [
  {
    title: 'Dashboard',
    href: '/dashboard',
    icon: 'home',
  },
  {
    title: 'Resumes',
    href: '/dashboard/resumes',
    icon: 'fileText',
  },
  {
    title: 'Cover Letters',
    href: '/dashboard/cover-letters',
    icon: 'mail',
  },
  {
    title: 'Templates',
    href: '/dashboard/templates',
    icon: 'layout',
  },
  {
    title: 'Analytics',
    href: '/dashboard/analytics',
    icon: 'barChart',
    badge: 'Pro',
  },
  {
    title: 'Settings',
    href: '/dashboard/settings',
    icon: 'settings',
  },
];

function DashboardSidebar({ className }: { className?: string }) {
  const pathname = usePathname();

  return (
    <div className={cn('pb-12 w-64', className)}>
      <div className="space-y-4 py-4">
        <div className="px-3 py-2">
          <h2 className="mb-2 px-4 text-lg font-semibold tracking-tight">
            Workspace
          </h2>
          <div className="space-y-1">
            {sidebarNavItems.map((item) => {
              const Icon = Icons[item.icon];
              const isActive = pathname === item.href;
              
              return (
                <Button
                  key={item.href}
                  variant={isActive ? 'secondary' : 'ghost'}
                  className={cn(
                    'w-full justify-start',
                    isActive && 'bg-secondary',
                    item.disabled && 'opacity-50 cursor-not-allowed'
                  )}
                  asChild={!item.disabled}
                  disabled={item.disabled}
                >
                  {item.disabled ? (
                    <div className="flex items-center">
                      <Icon className="mr-2 h-4 w-4" />
                      {item.title}
                      {item.badge && (
                        <span className="ml-auto text-xs bg-primary text-primary-foreground px-2 py-1 rounded-full">
                          {item.badge}
                        </span>
                      )}
                    </div>
                  ) : (
                    <Link href={item.href} className="flex items-center">
                      <Icon className="mr-2 h-4 w-4" />
                      {item.title}
                      {item.badge && (
                        <span className="ml-auto text-xs bg-primary text-primary-foreground px-2 py-1 rounded-full">
                          {item.badge}
                        </span>
                      )}
                    </Link>
                  )}
                </Button>
              );
            })}
          </div>
        </div>
        
        <Separator />
        
        <div className="px-3 py-2">
          <h2 className="mb-2 px-4 text-lg font-semibold tracking-tight">
            Quick Actions
          </h2>
          <div className="space-y-1">
            <Button
              variant="ghost"
              className="w-full justify-start"
              asChild
            >
              <Link href="/dashboard/resumes/new">
                <Icons.plus className="mr-2 h-4 w-4" />
                New Resume
              </Link>
            </Button>
            <Button
              variant="ghost"
              className="w-full justify-start"
              asChild
            >
              <Link href="/dashboard/cover-letters/new">
                <Icons.plus className="mr-2 h-4 w-4" />
                New Cover Letter
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}

function MobileSidebar({ 
  isOpen, 
  onClose 
}: { 
  isOpen: boolean; 
  onClose: () => void; 
}) {
  const pathname = usePathname();

  if (!isOpen) return null;

  return (
    <>
      {/* Overlay */}
      <div 
        className="fixed inset-0 z-40 bg-background/80 backdrop-blur-sm lg:hidden"
        onClick={onClose}
      />
      
      {/* Sidebar */}
      <div className="fixed inset-y-0 left-0 z-50 w-64 bg-background border-r lg:hidden">
        <div className="flex items-center justify-between p-4 border-b">
          <Link href="/" className="flex items-center space-x-2">
            <Icons.fileText className="h-6 w-6 text-primary" />
            <span className="text-xl font-bold">CareerCraft</span>
          </Link>
          <Button variant="ghost" size="icon" onClick={onClose}>
            <Icons.close className="h-4 w-4" />
          </Button>
        </div>
        
        <ScrollArea className="h-full pb-16">
          <DashboardSidebar />
        </ScrollArea>
      </div>
    </>
  );
}

export function DashboardLayout({ children }: DashboardLayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(false);

  return (
    <div className="min-h-screen">
      <Header />
      
      <div className="flex">
        {/* Desktop Sidebar */}
        <aside className="hidden lg:block fixed left-0 top-16 h-[calc(100vh-4rem)] border-r bg-background">
          <ScrollArea className="h-full">
            <DashboardSidebar />
          </ScrollArea>
        </aside>

        {/* Mobile Sidebar */}
        <MobileSidebar 
          isOpen={sidebarOpen} 
          onClose={() => setSidebarOpen(false)} 
        />

        {/* Main Content */}
        <div className="flex-1 lg:ml-64">
          {/* Mobile Header */}
          <div className="lg:hidden flex items-center justify-between p-4 border-b">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setSidebarOpen(true)}
            >
              <Icons.menu className="h-5 w-5" />
            </Button>
            <h1 className="text-lg font-semibold">Dashboard</h1>
            <div className="w-9" /> {/* Spacer for centering */}
          </div>

          {/* Page Content */}
          <main className="p-4 lg:p-6">
            {children}
          </main>
        </div>
      </div>
    </div>
  );
}
