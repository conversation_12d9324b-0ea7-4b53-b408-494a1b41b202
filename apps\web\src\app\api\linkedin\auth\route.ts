/**
 * LinkedIn OAuth Authentication API
 * 
 * Handles LinkedIn OAuth flow for profile integration
 */

import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { linkedInClient } from '@/lib/linkedin/client'
import { linkedInService } from '@/lib/linkedin/service'
import { z } from 'zod'

// Request schemas
const AuthRequestSchema = z.object({
  action: z.enum(['authorize', 'callback']),
  code: z.string().optional(),
  state: z.string().optional(),
  error: z.string().optional()
})

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const action = searchParams.get('action')
    
    if (action === 'authorize') {
      // Generate authorization URL
      const state = `user_${session.user.id}_${Date.now()}`
      const authUrl = linkedInClient.getAuthorizationUrl(state)
      
      return NextResponse.json({
        authUrl,
        state
      })
    }

    return NextResponse.json(
      { error: 'Invalid action' },
      { status: 400 }
    )
  } catch (error) {
    console.error('LinkedIn auth error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { action, code, state, error } = AuthRequestSchema.parse(body)

    if (action === 'callback') {
      // Handle OAuth callback
      if (error) {
        return NextResponse.json(
          { error: `LinkedIn OAuth error: ${error}` },
          { status: 400 }
        )
      }

      if (!code) {
        return NextResponse.json(
          { error: 'Authorization code is required' },
          { status: 400 }
        )
      }

      // Validate state parameter
      if (state && !state.startsWith(`user_${session.user.id}_`)) {
        return NextResponse.json(
          { error: 'Invalid state parameter' },
          { status: 400 }
        )
      }

      try {
        // Exchange code for access token
        const accessToken = await linkedInClient.getAccessToken(code)
        
        // Fetch and save LinkedIn profile
        const profile = await linkedInClient.getProfile(accessToken)
        await linkedInService.saveLinkedInProfile(
          session.user.id,
          profile.id,
          profile
        )

        return NextResponse.json({
          success: true,
          message: 'LinkedIn account connected successfully',
          profile: {
            name: `${Object.values(profile.firstName.localized)[0]} ${Object.values(profile.lastName.localized)[0]}`,
            headline: profile.headline,
            profileImage: profile.profilePicture?.displayImage
          }
        })
      } catch (error) {
        console.error('LinkedIn callback error:', error)
        return NextResponse.json(
          { error: 'Failed to connect LinkedIn account' },
          { status: 500 }
        )
      }
    }

    return NextResponse.json(
      { error: 'Invalid action' },
      { status: 400 }
    )
  } catch (error) {
    console.error('LinkedIn auth POST error:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Disconnect LinkedIn account
    const success = await linkedInService.disconnectLinkedIn(session.user.id)
    
    if (!success) {
      return NextResponse.json(
        { error: 'Failed to disconnect LinkedIn account' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'LinkedIn account disconnected successfully'
    })
  } catch (error) {
    console.error('LinkedIn disconnect error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
