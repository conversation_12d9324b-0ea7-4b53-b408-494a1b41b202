/**
 * Content Script Styles
 * 
 * Styles for form field highlighting and UI overlay
 * injected into job application pages.
 */

/* CareerCraft field highlighting */
.careercraft-field-detected {
  outline: 2px solid #4f46e5 !important;
  outline-offset: 2px !important;
  transition: all 0.3s ease !important;
}

.careercraft-field-filled {
  background-color: #dcfce7 !important;
  border-color: #16a34a !important;
  transition: all 0.3s ease !important;
}

.careercraft-field-error {
  background-color: #fee2e2 !important;
  border-color: #dc2626 !important;
  transition: all 0.3s ease !important;
}

.careercraft-field-processing {
  background: linear-gradient(90deg, #f3f4f6 25%, #e5e7eb 50%, #f3f4f6 75%) !important;
  background-size: 200% 100% !important;
  animation: careercraft-shimmer 1.5s infinite !important;
}

@keyframes careercraft-shimmer {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

/* Form detection indicator */
.careercraft-form-detected {
  position: relative !important;
}

.careercraft-form-detected::before {
  content: '' !important;
  position: absolute !important;
  top: -2px !important;
  left: -2px !important;
  right: -2px !important;
  bottom: -2px !important;
  border: 2px solid #4f46e5 !important;
  border-radius: 8px !important;
  pointer-events: none !important;
  z-index: 1000 !important;
  animation: careercraft-pulse 2s infinite !important;
}

@keyframes careercraft-pulse {
  0%, 100% { opacity: 0.5; transform: scale(1); }
  50% { opacity: 1; transform: scale(1.02); }
}

/* Confidence indicator */
.careercraft-confidence-indicator {
  position: absolute !important;
  top: -8px !important;
  right: -8px !important;
  width: 24px !important;
  height: 24px !important;
  border-radius: 50% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-size: 10px !important;
  font-weight: bold !important;
  color: white !important;
  z-index: 1001 !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

.careercraft-confidence-high {
  background: #16a34a !important;
}

.careercraft-confidence-medium {
  background: #d97706 !important;
}

.careercraft-confidence-low {
  background: #dc2626 !important;
}

/* Tooltip styles */
.careercraft-tooltip {
  position: absolute !important;
  background: #1f2937 !important;
  color: white !important;
  padding: 8px 12px !important;
  border-radius: 6px !important;
  font-size: 12px !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
  z-index: 10000 !important;
  pointer-events: none !important;
  opacity: 0 !important;
  transform: translateY(4px) !important;
  transition: all 0.2s ease !important;
  max-width: 200px !important;
  word-wrap: break-word !important;
}

.careercraft-tooltip::before {
  content: '' !important;
  position: absolute !important;
  top: -4px !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  border-left: 4px solid transparent !important;
  border-right: 4px solid transparent !important;
  border-bottom: 4px solid #1f2937 !important;
}

.careercraft-tooltip.show {
  opacity: 1 !important;
  transform: translateY(0) !important;
}

/* Progress indicator */
.careercraft-progress {
  position: fixed !important;
  top: 20px !important;
  right: 20px !important;
  background: white !important;
  border-radius: 8px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  padding: 16px !important;
  z-index: 10000 !important;
  min-width: 280px !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
  border: 1px solid #e5e7eb !important;
}

.careercraft-progress-header {
  display: flex !important;
  align-items: center !important;
  justify-content: space-between !important;
  margin-bottom: 12px !important;
}

.careercraft-progress-title {
  font-size: 14px !important;
  font-weight: 600 !important;
  color: #111827 !important;
  margin: 0 !important;
}

.careercraft-progress-close {
  background: none !important;
  border: none !important;
  color: #6b7280 !important;
  cursor: pointer !important;
  padding: 2px !important;
  border-radius: 4px !important;
  transition: color 0.2s !important;
}

.careercraft-progress-close:hover {
  color: #374151 !important;
}

.careercraft-progress-bar {
  width: 100% !important;
  height: 6px !important;
  background: #f3f4f6 !important;
  border-radius: 3px !important;
  overflow: hidden !important;
  margin-bottom: 8px !important;
}

.careercraft-progress-fill {
  height: 100% !important;
  background: linear-gradient(90deg, #4f46e5, #7c3aed) !important;
  border-radius: 3px !important;
  transition: width 0.3s ease !important;
}

.careercraft-progress-text {
  font-size: 12px !important;
  color: #6b7280 !important;
  margin: 0 !important;
}

/* Success animation */
.careercraft-success-animation {
  animation: careercraft-success-bounce 0.6s ease-out !important;
}

@keyframes careercraft-success-bounce {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

/* Error shake animation */
.careercraft-error-animation {
  animation: careercraft-error-shake 0.5s ease-in-out !important;
}

@keyframes careercraft-error-shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-4px); }
  75% { transform: translateX(4px); }
}

/* Field mapping indicator */
.careercraft-field-mapped {
  position: relative !important;
}

.careercraft-field-mapped::after {
  content: '✓' !important;
  position: absolute !important;
  top: 4px !important;
  right: 4px !important;
  width: 16px !important;
  height: 16px !important;
  background: #16a34a !important;
  color: white !important;
  border-radius: 50% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-size: 10px !important;
  font-weight: bold !important;
  z-index: 1001 !important;
}

/* Loading overlay */
.careercraft-loading-overlay {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  background: rgba(0, 0, 0, 0.5) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  z-index: 10000 !important;
  backdrop-filter: blur(2px) !important;
}

.careercraft-loading-content {
  background: white !important;
  border-radius: 12px !important;
  padding: 32px !important;
  text-align: center !important;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2) !important;
  max-width: 320px !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
}

.careercraft-loading-spinner {
  width: 40px !important;
  height: 40px !important;
  border: 3px solid #f3f4f6 !important;
  border-top: 3px solid #4f46e5 !important;
  border-radius: 50% !important;
  animation: careercraft-spin 1s linear infinite !important;
  margin: 0 auto 16px auto !important;
}

@keyframes careercraft-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.careercraft-loading-text {
  font-size: 16px !important;
  font-weight: 500 !important;
  color: #111827 !important;
  margin: 0 0 8px 0 !important;
}

.careercraft-loading-subtext {
  font-size: 14px !important;
  color: #6b7280 !important;
  margin: 0 !important;
}

/* Notification styles */
.careercraft-notification {
  position: fixed !important;
  top: 20px !important;
  right: 20px !important;
  background: white !important;
  border-radius: 8px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  border-left: 4px solid #4f46e5 !important;
  padding: 16px !important;
  z-index: 10000 !important;
  max-width: 320px !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
  transform: translateX(100%) !important;
  transition: transform 0.3s ease !important;
}

.careercraft-notification.show {
  transform: translateX(0) !important;
}

.careercraft-notification-success {
  border-left-color: #16a34a !important;
}

.careercraft-notification-error {
  border-left-color: #dc2626 !important;
}

.careercraft-notification-warning {
  border-left-color: #d97706 !important;
}

.careercraft-notification-content {
  display: flex !important;
  align-items: flex-start !important;
  gap: 12px !important;
}

.careercraft-notification-icon {
  flex-shrink: 0 !important;
  width: 20px !important;
  height: 20px !important;
}

.careercraft-notification-text {
  flex: 1 !important;
}

.careercraft-notification-title {
  font-size: 14px !important;
  font-weight: 600 !important;
  color: #111827 !important;
  margin: 0 0 4px 0 !important;
}

.careercraft-notification-message {
  font-size: 13px !important;
  color: #6b7280 !important;
  margin: 0 !important;
  line-height: 1.4 !important;
}

.careercraft-notification-close {
  background: none !important;
  border: none !important;
  color: #9ca3af !important;
  cursor: pointer !important;
  padding: 2px !important;
  border-radius: 4px !important;
  transition: color 0.2s !important;
  flex-shrink: 0 !important;
}

.careercraft-notification-close:hover {
  color: #6b7280 !important;
}

/* Hide on print */
@media print {
  .careercraft-overlay,
  .careercraft-progress,
  .careercraft-notification,
  .careercraft-loading-overlay,
  .careercraft-tooltip {
    display: none !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .careercraft-field-detected {
    outline-width: 3px !important;
  }
  
  .careercraft-field-filled {
    background-color: #bbf7d0 !important;
    border-color: #059669 !important;
  }
  
  .careercraft-field-error {
    background-color: #fecaca !important;
    border-color: #b91c1c !important;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .careercraft-field-detected,
  .careercraft-field-filled,
  .careercraft-field-error,
  .careercraft-tooltip,
  .careercraft-notification,
  .careercraft-progress-fill {
    transition: none !important;
  }
  
  .careercraft-shimmer,
  .careercraft-pulse,
  .careercraft-spin,
  .careercraft-success-bounce,
  .careercraft-error-shake {
    animation: none !important;
  }
}
