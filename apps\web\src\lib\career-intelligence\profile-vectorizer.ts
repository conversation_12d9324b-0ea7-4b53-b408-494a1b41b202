/**
 * Profile Vectorization Service
 * 
 * Converts user resume data into structured vectors for AI analysis
 * Implements FR-5.1: Profile Vectorization
 */

import { prisma } from '@/lib/db'
import { openai } from '@/lib/ai/openai'

export interface ProfileData {
  personalInfo: {
    name?: string
    email?: string
    phone?: string
    location?: string
    summary?: string
  }
  experience: Array<{
    company: string
    position: string
    location?: string
    startDate: Date
    endDate?: Date
    description?: string
    achievements?: string[]
  }>
  education: Array<{
    institution: string
    degree: string
    field?: string
    location?: string
    startDate: Date
    endDate?: Date
    gpa?: string
  }>
  skills: Array<{
    name: string
    category?: string
    level?: string
  }>
  projects: Array<{
    name: string
    description?: string
    technologies?: string[]
    url?: string
  }>
}

export interface ProfileVector {
  vector: number[]
  skillsExtracted: string[]
  experienceLevel: 'ENTRY' | 'MID' | 'SENIOR' | 'EXECUTIVE'
  primaryRole: string
  industries: string[]
  locations: string[]
}

export class ProfileVectorizer {
  private readonly VECTOR_DIMENSION = 1536 // OpenAI text-embedding-ada-002 dimension

  /**
   * Extract and structure profile data from resume
   */
  async extractProfileData(resumeId: string): Promise<ProfileData> {
    const resume = await prisma.resume.findUnique({
      where: { id: resumeId },
      include: {
        experiences: true,
        educations: true,
        skills: true,
        projects: true
      }
    })

    if (!resume) {
      throw new Error(`Resume not found: ${resumeId}`)
    }

    // Parse JSON fields safely
    const personalInfo = resume.personalInfo ? JSON.parse(resume.personalInfo) : {}
    
    return {
      personalInfo: {
        name: personalInfo.name,
        email: personalInfo.email,
        phone: personalInfo.phone,
        location: personalInfo.location,
        summary: personalInfo.summary
      },
      experience: resume.experiences.map(exp => ({
        company: exp.company,
        position: exp.position,
        location: exp.location || undefined,
        startDate: exp.startDate,
        endDate: exp.endDate || undefined,
        description: exp.description || undefined,
        achievements: exp.achievements ? JSON.parse(exp.achievements) : []
      })),
      education: resume.educations.map(edu => ({
        institution: edu.institution,
        degree: edu.degree,
        field: edu.field || undefined,
        location: edu.location || undefined,
        startDate: edu.startDate,
        endDate: edu.endDate || undefined,
        gpa: edu.gpa || undefined
      })),
      skills: resume.skills.map(skill => ({
        name: skill.name,
        category: skill.category || undefined,
        level: skill.level || undefined
      })),
      projects: resume.projects.map(project => ({
        name: project.name,
        description: project.description || undefined,
        technologies: project.technologies ? JSON.parse(project.technologies) : [],
        url: project.url || undefined
      }))
    }
  }

  /**
   * Generate text representation for vectorization
   */
  generateProfileText(profileData: ProfileData): string {
    const sections: string[] = []

    // Personal summary
    if (profileData.personalInfo.summary) {
      sections.push(`Professional Summary: ${profileData.personalInfo.summary}`)
    }

    // Experience section
    if (profileData.experience.length > 0) {
      const experienceText = profileData.experience.map(exp => {
        const duration = this.formatDateRange(exp.startDate, exp.endDate)
        const achievements = exp.achievements?.join('. ') || ''
        return `${exp.position} at ${exp.company} (${duration}). ${exp.description || ''} ${achievements}`
      }).join(' ')
      sections.push(`Work Experience: ${experienceText}`)
    }

    // Education section
    if (profileData.education.length > 0) {
      const educationText = profileData.education.map(edu => {
        const duration = this.formatDateRange(edu.startDate, edu.endDate)
        return `${edu.degree} in ${edu.field || 'General Studies'} from ${edu.institution} (${duration})`
      }).join(' ')
      sections.push(`Education: ${educationText}`)
    }

    // Skills section
    if (profileData.skills.length > 0) {
      const skillsText = profileData.skills.map(skill => {
        const level = skill.level ? ` (${skill.level})` : ''
        return `${skill.name}${level}`
      }).join(', ')
      sections.push(`Skills: ${skillsText}`)
    }

    // Projects section
    if (profileData.projects.length > 0) {
      const projectsText = profileData.projects.map(project => {
        const tech = project.technologies?.join(', ') || ''
        return `${project.name}: ${project.description || ''} Technologies: ${tech}`
      }).join(' ')
      sections.push(`Projects: ${projectsText}`)
    }

    return sections.join('\n\n')
  }

  /**
   * Create vector embedding using OpenAI
   */
  async createEmbedding(text: string): Promise<number[]> {
    try {
      const response = await openai.embeddings.create({
        model: 'text-embedding-ada-002',
        input: text,
        encoding_format: 'float'
      })

      return response.data[0].embedding
    } catch (error) {
      console.error('Error creating embedding:', error)
      throw new Error('Failed to create profile embedding')
    }
  }

  /**
   * Extract skills using AI analysis
   */
  async extractSkills(profileText: string): Promise<string[]> {
    try {
      const prompt = `
        Analyze the following resume text and extract all technical and professional skills.
        Return only a JSON array of skill names, no explanations.
        Focus on specific technologies, tools, programming languages, frameworks, and professional competencies.
        
        Resume text:
        ${profileText}
      `

      const response = await openai.chat.completions.create({
        model: 'gpt-4',
        messages: [{ role: 'user', content: prompt }],
        temperature: 0.1,
        max_tokens: 500
      })

      const content = response.choices[0]?.message?.content
      if (!content) {
        throw new Error('No response from OpenAI')
      }

      // Parse JSON response
      const skills = JSON.parse(content)
      return Array.isArray(skills) ? skills : []
    } catch (error) {
      console.error('Error extracting skills:', error)
      // Fallback to basic skill extraction
      return this.extractSkillsFallback(profileText)
    }
  }

  /**
   * Determine experience level based on work history
   */
  determineExperienceLevel(experience: ProfileData['experience']): 'ENTRY' | 'MID' | 'SENIOR' | 'EXECUTIVE' {
    if (experience.length === 0) return 'ENTRY'

    // Calculate total years of experience
    const totalYears = experience.reduce((total, exp) => {
      const start = new Date(exp.startDate)
      const end = exp.endDate ? new Date(exp.endDate) : new Date()
      const years = (end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24 * 365)
      return total + years
    }, 0)

    // Check for leadership indicators
    const hasLeadershipRole = experience.some(exp => 
      /director|manager|lead|head|chief|vp|vice president|senior|principal/i.test(exp.position)
    )

    if (totalYears >= 10 || hasLeadershipRole) return 'EXECUTIVE'
    if (totalYears >= 5) return 'SENIOR'
    if (totalYears >= 2) return 'MID'
    return 'ENTRY'
  }

  /**
   * Extract primary role from most recent experience
   */
  extractPrimaryRole(experience: ProfileData['experience']): string {
    if (experience.length === 0) return 'Professional'

    // Sort by start date (most recent first)
    const sortedExperience = [...experience].sort((a, b) => 
      new Date(b.startDate).getTime() - new Date(a.startDate).getTime()
    )

    return sortedExperience[0].position
  }

  /**
   * Extract industries from experience
   */
  extractIndustries(experience: ProfileData['experience']): string[] {
    // This is a simplified version - in production, you'd use a more sophisticated
    // industry classification system or AI analysis
    const industries = new Set<string>()
    
    experience.forEach(exp => {
      // Basic industry inference from company names and descriptions
      const text = `${exp.company} ${exp.description || ''}`.toLowerCase()
      
      if (text.includes('tech') || text.includes('software') || text.includes('startup')) {
        industries.add('Technology')
      }
      if (text.includes('finance') || text.includes('bank') || text.includes('investment')) {
        industries.add('Finance')
      }
      if (text.includes('health') || text.includes('medical') || text.includes('hospital')) {
        industries.add('Healthcare')
      }
      if (text.includes('retail') || text.includes('ecommerce') || text.includes('commerce')) {
        industries.add('Retail')
      }
      if (text.includes('education') || text.includes('university') || text.includes('school')) {
        industries.add('Education')
      }
    })

    return Array.from(industries)
  }

  /**
   * Extract preferred locations
   */
  extractLocations(profileData: ProfileData): string[] {
    const locations = new Set<string>()
    
    // Add current location
    if (profileData.personalInfo.location) {
      locations.add(profileData.personalInfo.location)
    }

    // Add work locations
    profileData.experience.forEach(exp => {
      if (exp.location) {
        locations.add(exp.location)
      }
    })

    return Array.from(locations)
  }

  /**
   * Fallback skill extraction using regex patterns
   */
  private extractSkillsFallback(text: string): string[] {
    const skillPatterns = [
      /\b(JavaScript|TypeScript|Python|Java|C\+\+|C#|PHP|Ruby|Go|Rust|Swift|Kotlin)\b/gi,
      /\b(React|Vue|Angular|Node\.js|Express|Django|Flask|Spring|Laravel)\b/gi,
      /\b(AWS|Azure|GCP|Docker|Kubernetes|Jenkins|Git|GitHub|GitLab)\b/gi,
      /\b(SQL|MySQL|PostgreSQL|MongoDB|Redis|Elasticsearch)\b/gi,
      /\b(HTML|CSS|SASS|SCSS|Tailwind|Bootstrap)\b/gi
    ]

    const skills = new Set<string>()
    skillPatterns.forEach(pattern => {
      const matches = text.match(pattern)
      if (matches) {
        matches.forEach(match => skills.add(match))
      }
    })

    return Array.from(skills)
  }

  /**
   * Format date range for display
   */
  private formatDateRange(startDate: Date, endDate?: Date): string {
    const start = new Date(startDate)
    const end = endDate ? new Date(endDate) : null
    
    const startStr = start.toLocaleDateString('en-US', { year: 'numeric', month: 'short' })
    const endStr = end ? end.toLocaleDateString('en-US', { year: 'numeric', month: 'short' }) : 'Present'
    
    return `${startStr} - ${endStr}`
  }
}
