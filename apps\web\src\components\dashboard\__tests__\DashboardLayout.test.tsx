import { describe, it, expect, vi, beforeEach } from 'vitest'
import { screen, fireEvent, waitFor } from '@testing-library/react'
import { DashboardLayout } from '../DashboardLayout'
import { render, mockSession } from '@/test/utils'

// Mock the useRouter hook
const mockPush = vi.fn()
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: mockPush,
    refresh: vi.fn(),
  }),
  usePathname: () => '/dashboard',
}))

// Mock next-auth
vi.mock('next-auth/react', () => ({
  useSession: () => ({
    data: mockSession,
    status: 'authenticated',
  }),
  signOut: vi.fn(),
}))

describe('DashboardLayout', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders the layout with navigation items', () => {
    render(
      <DashboardLayout currentPage="dashboard">
        <div>Test Content</div>
      </DashboardLayout>
    )

    // Check if logo is present
    expect(screen.getByText('CareerCraft')).toBeInTheDocument()

    // Check if navigation items are present
    expect(screen.getByText('Dashboard')).toBeInTheDocument()
    expect(screen.getByText('My Resumes')).toBeInTheDocument()
    expect(screen.getByText('Templates')).toBeInTheDocument()
    expect(screen.getByText('Analytics')).toBeInTheDocument()

    // Check if create resume button is present
    expect(screen.getByText('Create Resume')).toBeInTheDocument()

    // Check if user profile is displayed
    expect(screen.getByText('Test User')).toBeInTheDocument()
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument()

    // Check if content is rendered
    expect(screen.getByText('Test Content')).toBeInTheDocument()
  })

  it('highlights the current page in navigation', () => {
    render(
      <DashboardLayout currentPage="resumes">
        <div>Test Content</div>
      </DashboardLayout>
    )

    // The current page should have different styling
    const resumesButton = screen.getByRole('button', { name: /my resumes/i })
    expect(resumesButton).toHaveClass('bg-gradient-to-r')
  })

  it('navigates to correct pages when navigation items are clicked', async () => {
    render(
      <DashboardLayout currentPage="dashboard">
        <div>Test Content</div>
      </DashboardLayout>
    )

    // Click on Templates navigation
    const templatesButton = screen.getByRole('button', { name: /templates/i })
    fireEvent.click(templatesButton)

    await waitFor(() => {
      expect(mockPush).toHaveBeenCalledWith('/dashboard/templates')
    })

    // Click on My Resumes navigation
    const resumesButton = screen.getByRole('button', { name: /my resumes/i })
    fireEvent.click(resumesButton)

    await waitFor(() => {
      expect(mockPush).toHaveBeenCalledWith('/dashboard/resumes')
    })
  })

  it('navigates to create resume page when create button is clicked', async () => {
    render(
      <DashboardLayout currentPage="dashboard">
        <div>Test Content</div>
      </DashboardLayout>
    )

    const createButton = screen.getByRole('button', { name: /create resume/i })
    fireEvent.click(createButton)

    await waitFor(() => {
      expect(mockPush).toHaveBeenCalledWith('/dashboard/resumes/new')
    })
  })

  it('opens and closes mobile sidebar', async () => {
    // Mock mobile viewport
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 768,
    })

    render(
      <DashboardLayout currentPage="dashboard">
        <div>Test Content</div>
      </DashboardLayout>
    )

    // Find mobile menu button
    const menuButtons = screen.getAllByRole('button')
    const mobileMenuButton = menuButtons.find(button => 
      button.querySelector('[data-testid="mock-icon"]')
    )

    expect(mobileMenuButton).toBeInTheDocument()

    // Click to open sidebar
    if (mobileMenuButton) {
      fireEvent.click(mobileMenuButton)
    }

    // Sidebar should be visible (check for close button)
    await waitFor(() => {
      const closeButtons = screen.getAllByRole('button')
      const closeButton = closeButtons.find(button => 
        button.getAttribute('class')?.includes('lg:hidden')
      )
      expect(closeButton).toBeInTheDocument()
    })
  })

  it('displays user dropdown menu', async () => {
    render(
      <DashboardLayout currentPage="dashboard">
        <div>Test Content</div>
      </DashboardLayout>
    )

    // Click on user profile to open dropdown
    const userButton = screen.getByRole('button', { name: /test user/i })
    fireEvent.click(userButton)

    await waitFor(() => {
      expect(screen.getByText('My Account')).toBeInTheDocument()
      expect(screen.getByText('Profile Settings')).toBeInTheDocument()
      expect(screen.getByText('Preferences')).toBeInTheDocument()
      expect(screen.getByText('Sign Out')).toBeInTheDocument()
    })
  })

  it('handles theme toggle', async () => {
    const mockSetTheme = vi.fn()
    
    // Mock useTheme hook
    vi.mock('next-themes', () => ({
      useTheme: () => ({
        theme: 'light',
        setTheme: mockSetTheme,
      }),
    }))

    render(
      <DashboardLayout currentPage="dashboard">
        <div>Test Content</div>
      </DashboardLayout>
    )

    // Find theme toggle buttons
    const themeButtons = screen.getAllByRole('button')
    const themeToggle = themeButtons.find(button => 
      button.getAttribute('class')?.includes('glass-input')
    )

    if (themeToggle) {
      fireEvent.click(themeToggle)
      await waitFor(() => {
        expect(mockSetTheme).toHaveBeenCalled()
      })
    }
  })

  it('applies glassmorphism styling', () => {
    render(
      <DashboardLayout currentPage="dashboard">
        <div>Test Content</div>
      </DashboardLayout>
    )

    // Check for glass effect classes
    const glassElements = document.querySelectorAll('.glass-panel, .glass-card, .glass-input')
    expect(glassElements.length).toBeGreaterThan(0)
  })

  it('is responsive on different screen sizes', () => {
    render(
      <DashboardLayout currentPage="dashboard">
        <div>Test Content</div>
      </DashboardLayout>
    )

    // Check for responsive classes
    const responsiveElements = document.querySelectorAll('[class*="lg:"], [class*="md:"], [class*="sm:"]')
    expect(responsiveElements.length).toBeGreaterThan(0)
  })

  it('handles sign out functionality', async () => {
    const mockSignOut = vi.fn()
    
    vi.mock('next-auth/react', () => ({
      useSession: () => ({
        data: mockSession,
        status: 'authenticated',
      }),
      signOut: mockSignOut,
    }))

    render(
      <DashboardLayout currentPage="dashboard">
        <div>Test Content</div>
      </DashboardLayout>
    )

    // Open user dropdown
    const userButton = screen.getByRole('button', { name: /test user/i })
    fireEvent.click(userButton)

    await waitFor(() => {
      const signOutButton = screen.getByText('Sign Out')
      fireEvent.click(signOutButton)
    })

    await waitFor(() => {
      expect(mockSignOut).toHaveBeenCalledWith({ callbackUrl: '/' })
    })
  })

  it('renders without session data gracefully', () => {
    render(
      <DashboardLayout currentPage="dashboard">
        <div>Test Content</div>
      </DashboardLayout>,
      { session: null }
    )

    // Should still render the layout structure
    expect(screen.getByText('CareerCraft')).toBeInTheDocument()
    expect(screen.getByText('Test Content')).toBeInTheDocument()
  })

  it('handles navigation with keyboard', async () => {
    render(
      <DashboardLayout currentPage="dashboard">
        <div>Test Content</div>
      </DashboardLayout>
    )

    const dashboardButton = screen.getByRole('button', { name: /dashboard/i })
    
    // Focus and press Enter
    dashboardButton.focus()
    fireEvent.keyDown(dashboardButton, { key: 'Enter', code: 'Enter' })

    await waitFor(() => {
      expect(mockPush).toHaveBeenCalledWith('/dashboard')
    })
  })
})
