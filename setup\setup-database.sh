#!/bin/bash
# CareerCraft Database Setup Script

echo "🗄️  CareerCraft Database Setup"
echo "============================="

# Check if PostgreSQL is running
if ! pg_isready -h localhost -p 5432 >/dev/null 2>&1; then
    echo "❌ PostgreSQL is not running. Please start PostgreSQL first."
    echo "   macOS: brew services start postgresql@14"
    echo "   Linux: sudo systemctl start postgresql"
    echo "   Windows: Start PostgreSQL service"
    exit 1
fi

echo "✅ PostgreSQL is running"

# Database configuration
DB_NAME="careercraft_local"
DB_USER="careercraft_user"
DB_PASSWORD="local_password"
DB_HOST="localhost"
DB_PORT="5432"

echo "🔧 Setting up database and user..."

# Check if we're on Linux and need to use sudo
if [[ "$OSTYPE" == "linux-gnu"* ]]; then
    PSQL_CMD="sudo -u postgres psql"
else
    PSQL_CMD="psql postgres"
fi

# Create database user
echo "👤 Creating database user: $DB_USER"
$PSQL_CMD -c "CREATE USER $DB_USER WITH PASSWORD '$DB_PASSWORD';" 2>/dev/null || echo "   User already exists"

# Create database
echo "🗄️  Creating database: $DB_NAME"
$PSQL_CMD -c "CREATE DATABASE $DB_NAME OWNER $DB_USER;" 2>/dev/null || echo "   Database already exists"

# Grant privileges
echo "🔑 Granting privileges..."
$PSQL_CMD -c "GRANT ALL PRIVILEGES ON DATABASE $DB_NAME TO $DB_USER;"
$PSQL_CMD -c "ALTER USER $DB_USER CREATEDB;"

# Test connection
echo "🔌 Testing database connection..."
if PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "SELECT 1;" >/dev/null 2>&1; then
    echo "✅ Database connection successful"
else
    echo "❌ Database connection failed"
    exit 1
fi

# Create .env.local if it doesn't exist
if [ ! -f ".env.local" ]; then
    echo "📝 Creating .env.local file..."
    cat > .env.local << EOF
# Database
DATABASE_URL="postgresql://$DB_USER:$DB_PASSWORD@$DB_HOST:$DB_PORT/$DB_NAME"

# NextAuth
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-local-secret-key-change-this-in-production"

# Google OAuth (Get from Google Cloud Console)
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"

# GitHub OAuth (Get from GitHub Developer Settings)
GITHUB_ID="your-github-client-id"
GITHUB_SECRET="your-github-client-secret"

# Stripe (Test Mode - Get from Stripe Dashboard)
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY="pk_test_..."
STRIPE_SECRET_KEY="sk_test_..."
STRIPE_WEBHOOK_SECRET="whsec_..."

# OpenAI (Get from OpenAI Platform)
OPENAI_API_KEY="sk-..."

# Redis
REDIS_URL="redis://localhost:6379"

# Email (Local testing)
EMAIL_SERVER="smtp://localhost:1025"
EMAIL_FROM="noreply@localhost"

# App Settings
NODE_ENV="development"
NEXT_PUBLIC_APP_URL="http://localhost:3000"
EOF
else
    echo "📝 Updating DATABASE_URL in .env.local..."
    # Update DATABASE_URL in existing .env.local
    if grep -q "DATABASE_URL=" .env.local; then
        sed -i.bak "s|DATABASE_URL=.*|DATABASE_URL=\"postgresql://$DB_USER:$DB_PASSWORD@$DB_HOST:$DB_PORT/$DB_NAME\"|" .env.local
    else
        echo "DATABASE_URL=\"postgresql://$DB_USER:$DB_PASSWORD@$DB_HOST:$DB_PORT/$DB_NAME\"" >> .env.local
    fi
fi

# Copy Prisma schema
echo "📋 Setting up Prisma schema..."
if [ ! -d "prisma" ]; then
    mkdir prisma
fi

cp setup/prisma-schema.prisma prisma/schema.prisma

# Initialize Prisma if not already done
if [ ! -f "prisma/schema.prisma" ]; then
    echo "🔧 Initializing Prisma..."
    npx prisma init
    cp setup/prisma-schema.prisma prisma/schema.prisma
fi

# Generate Prisma client
echo "⚙️  Generating Prisma client..."
npx prisma generate

# Push schema to database
echo "📤 Pushing schema to database..."
npx prisma db push

# Create seed file
echo "🌱 Creating database seed file..."
cat > prisma/seed.ts << 'EOF'
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Seeding database...')

  // Create subscription plans
  const freePlan = await prisma.subscriptionPlan.upsert({
    where: { stripePriceId: 'price_free' },
    update: {},
    create: {
      name: 'Free',
      description: 'Perfect for getting started',
      stripePriceId: 'price_free',
      priceCents: 0,
      currency: 'USD',
      billingInterval: 'monthly',
      features: [
        'basic_resume_builder',
        'basic_ai_content',
        'pdf_export',
        'ats_check',
        'basic_analytics'
      ],
      limits: {
        resumes: 3,
        templates: 3,
        ai_generations: 10,
        applications_tracked: 10
      },
      sortOrder: 1
    }
  })

  const proPlan = await prisma.subscriptionPlan.upsert({
    where: { stripePriceId: 'price_pro_monthly' },
    update: {},
    create: {
      name: 'Pro',
      description: 'For serious job seekers',
      stripePriceId: 'price_pro_monthly',
      priceCents: 1999,
      currency: 'USD',
      billingInterval: 'monthly',
      features: [
        'unlimited_resumes',
        'advanced_ai_content',
        'all_templates',
        'cover_letters',
        'portfolio_builder',
        'linkedin_optimization',
        'interview_prep',
        'career_intelligence',
        'browser_extension',
        'mobile_app',
        'job_tracking'
      ],
      limits: {
        resumes: -1,
        templates: -1,
        ai_generations: -1,
        applications_tracked: -1
      },
      sortOrder: 2
    }
  })

  const enterprisePlan = await prisma.subscriptionPlan.upsert({
    where: { stripePriceId: 'price_enterprise_monthly' },
    update: {},
    create: {
      name: 'Enterprise',
      description: 'For teams and organizations',
      stripePriceId: 'price_enterprise_monthly',
      priceCents: 4999,
      currency: 'USD',
      billingInterval: 'monthly',
      features: [
        'everything_in_pro',
        'team_collaboration',
        'expert_feedback',
        'custom_branding',
        'api_access',
        'advanced_analytics',
        'priority_support',
        'white_label'
      ],
      limits: {
        resumes: -1,
        templates: -1,
        ai_generations: -1,
        applications_tracked: -1,
        team_members: 50
      },
      sortOrder: 3
    }
  })

  // Create sample templates
  const modernTemplate = await prisma.template.upsert({
    where: { id: 'template_modern_1' },
    update: {},
    create: {
      id: 'template_modern_1',
      name: 'Modern Professional',
      description: 'Clean and modern design perfect for tech roles',
      category: 'resume',
      type: 'free',
      content: {
        layout: 'single_column',
        colors: { primary: '#2563eb', secondary: '#64748b' },
        fonts: { heading: 'Inter', body: 'Inter' },
        sections: ['header', 'summary', 'experience', 'education', 'skills']
      },
      tags: ['modern', 'tech', 'clean', 'professional'],
      isActive: true,
      isApproved: true
    }
  })

  const creativeTemplate = await prisma.template.upsert({
    where: { id: 'template_creative_1' },
    update: {},
    create: {
      id: 'template_creative_1',
      name: 'Creative Portfolio',
      description: 'Eye-catching design for creative professionals',
      category: 'resume',
      type: 'premium',
      content: {
        layout: 'two_column',
        colors: { primary: '#7c3aed', secondary: '#a855f7' },
        fonts: { heading: 'Poppins', body: 'Open Sans' },
        sections: ['header', 'portfolio', 'experience', 'skills', 'education']
      },
      tags: ['creative', 'portfolio', 'design', 'colorful'],
      price: 999,
      isActive: true,
      isApproved: true
    }
  })

  console.log('✅ Database seeded successfully!')
  console.log(`📊 Created plans: ${freePlan.name}, ${proPlan.name}, ${enterprisePlan.name}`)
  console.log(`🎨 Created templates: ${modernTemplate.name}, ${creativeTemplate.name}`)
}

main()
  .catch((e) => {
    console.error('❌ Error seeding database:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
EOF

# Run seed
echo "🌱 Seeding database with initial data..."
npx tsx prisma/seed.ts

# Generate ERD
echo "📊 Generating database ERD..."
npx prisma generate

echo ""
echo "✅ Database setup complete!"
echo ""
echo "📊 Database Information:"
echo "   Host: $DB_HOST:$DB_PORT"
echo "   Database: $DB_NAME"
echo "   User: $DB_USER"
echo "   Connection URL: postgresql://$DB_USER:***@$DB_HOST:$DB_PORT/$DB_NAME"
echo ""
echo "🔧 Useful commands:"
echo "   View database: npx prisma studio"
echo "   Reset database: npx prisma migrate reset"
echo "   Generate client: npx prisma generate"
echo ""
echo "🎯 Next steps:"
echo "   1. Set up your API keys in .env.local"
echo "   2. Run: npm run dev"
echo "   3. Visit: http://localhost:3000"
