#!/usr/bin/env node

const { spawn } = require('child_process')
const path = require('path')

// ANSI color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
}

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`)
}

function runCommand(command, args = [], options = {}) {
  return new Promise((resolve, reject) => {
    log(`\n${colors.cyan}Running: ${command} ${args.join(' ')}${colors.reset}`)
    
    const child = spawn(command, args, {
      stdio: 'inherit',
      shell: true,
      ...options,
    })

    child.on('close', (code) => {
      if (code === 0) {
        resolve(code)
      } else {
        reject(new Error(`Command failed with exit code ${code}`))
      }
    })

    child.on('error', (error) => {
      reject(error)
    })
  })
}

async function runTests() {
  const args = process.argv.slice(2)
  const testType = args[0] || 'all'

  log(`${colors.bright}🧪 CareerCraft Test Runner${colors.reset}`)
  log(`${colors.blue}Running test suite: ${testType}${colors.reset}`)

  try {
    switch (testType) {
      case 'unit':
        log(`${colors.yellow}📋 Running unit tests...${colors.reset}`)
        await runCommand('npm', ['run', 'test'])
        log(`${colors.green}✅ Unit tests completed successfully${colors.reset}`)
        break

      case 'unit:watch':
        log(`${colors.yellow}👀 Running unit tests in watch mode...${colors.reset}`)
        await runCommand('npm', ['run', 'test:watch'])
        break

      case 'unit:coverage':
        log(`${colors.yellow}📊 Running unit tests with coverage...${colors.reset}`)
        await runCommand('npm', ['run', 'test:coverage'])
        log(`${colors.green}✅ Unit tests with coverage completed${colors.reset}`)
        break

      case 'unit:ui':
        log(`${colors.yellow}🎨 Running unit tests with UI...${colors.reset}`)
        await runCommand('npm', ['run', 'test:ui'])
        break

      case 'e2e':
        log(`${colors.yellow}🌐 Running E2E tests...${colors.reset}`)
        await runCommand('npm', ['run', 'test:e2e'])
        log(`${colors.green}✅ E2E tests completed successfully${colors.reset}`)
        break

      case 'e2e:ui':
        log(`${colors.yellow}🎭 Running E2E tests with UI...${colors.reset}`)
        await runCommand('npm', ['run', 'test:e2e:ui'])
        break

      case 'e2e:headed':
        log(`${colors.yellow}👁️ Running E2E tests in headed mode...${colors.reset}`)
        await runCommand('npm', ['run', 'test:e2e:headed'])
        break

      case 'lint':
        log(`${colors.yellow}🔍 Running linting...${colors.reset}`)
        await runCommand('npm', ['run', 'lint'])
        log(`${colors.green}✅ Linting completed successfully${colors.reset}`)
        break

      case 'type-check':
        log(`${colors.yellow}🔧 Running type checking...${colors.reset}`)
        await runCommand('npm', ['run', 'type-check'])
        log(`${colors.green}✅ Type checking completed successfully${colors.reset}`)
        break

      case 'all':
        log(`${colors.yellow}🚀 Running complete test suite...${colors.reset}`)
        
        // Run type checking first
        log(`${colors.magenta}Step 1/4: Type checking${colors.reset}`)
        await runCommand('npm', ['run', 'type-check'])
        
        // Run linting
        log(`${colors.magenta}Step 2/4: Linting${colors.reset}`)
        await runCommand('npm', ['run', 'lint'])
        
        // Run unit tests with coverage
        log(`${colors.magenta}Step 3/4: Unit tests with coverage${colors.reset}`)
        await runCommand('npm', ['run', 'test:coverage'])
        
        // Run E2E tests
        log(`${colors.magenta}Step 4/4: E2E tests${colors.reset}`)
        await runCommand('npm', ['run', 'test:e2e'])
        
        log(`${colors.green}🎉 All tests completed successfully!${colors.reset}`)
        break

      case 'ci':
        log(`${colors.yellow}🤖 Running CI test suite...${colors.reset}`)
        
        // Set CI environment
        process.env.CI = 'true'
        
        // Run type checking
        log(`${colors.magenta}CI Step 1/4: Type checking${colors.reset}`)
        await runCommand('npm', ['run', 'type-check'])
        
        // Run linting
        log(`${colors.magenta}CI Step 2/4: Linting${colors.reset}`)
        await runCommand('npm', ['run', 'lint'])
        
        // Run unit tests with coverage
        log(`${colors.magenta}CI Step 3/4: Unit tests with coverage${colors.reset}`)
        await runCommand('npm', ['run', 'test:coverage'])
        
        // Run E2E tests
        log(`${colors.magenta}CI Step 4/4: E2E tests${colors.reset}`)
        await runCommand('npm', ['run', 'test:e2e'])
        
        log(`${colors.green}🎉 CI test suite completed successfully!${colors.reset}`)
        break

      default:
        log(`${colors.red}❌ Unknown test type: ${testType}${colors.reset}`)
        log(`${colors.yellow}Available options:${colors.reset}`)
        log(`  unit          - Run unit tests`)
        log(`  unit:watch    - Run unit tests in watch mode`)
        log(`  unit:coverage - Run unit tests with coverage`)
        log(`  unit:ui       - Run unit tests with UI`)
        log(`  e2e           - Run E2E tests`)
        log(`  e2e:ui        - Run E2E tests with UI`)
        log(`  e2e:headed    - Run E2E tests in headed mode`)
        log(`  lint          - Run linting`)
        log(`  type-check    - Run type checking`)
        log(`  all           - Run complete test suite`)
        log(`  ci            - Run CI test suite`)
        process.exit(1)
    }

  } catch (error) {
    log(`${colors.red}❌ Test execution failed:${colors.reset}`)
    log(`${colors.red}${error.message}${colors.reset}`)
    process.exit(1)
  }
}

// Handle process termination
process.on('SIGINT', () => {
  log(`${colors.yellow}\n⚠️ Test execution interrupted${colors.reset}`)
  process.exit(1)
})

process.on('SIGTERM', () => {
  log(`${colors.yellow}\n⚠️ Test execution terminated${colors.reset}`)
  process.exit(1)
})

// Run the tests
runTests().catch((error) => {
  log(`${colors.red}❌ Unexpected error:${colors.reset}`)
  log(`${colors.red}${error.message}${colors.reset}`)
  process.exit(1)
})
