{"name": "careercraft-v2", "version": "2.0.0", "description": "AI-Powered Resume Platform - Complete rewrite with modern architecture", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"dev": "turbo run dev", "build": "turbo run build", "start": "turbo run start", "quick-start": "tsx scripts/quick-start.ts", "lint": "turbo run lint", "lint:fix": "turbo run lint:fix", "type-check": "turbo run type-check", "test": "turbo run test", "test:watch": "turbo run test:watch", "test:coverage": "turbo run test:coverage", "test:e2e": "turbo run test:e2e", "test:db": "tsx scripts/test-database.ts", "test:auth": "tsx scripts/test-auth.ts", "test:ui": "tsx scripts/test-ui.ts", "test:resume": "tsx scripts/test-resume-builder.ts", "test:templates": "tsx scripts/test-template-system.ts", "test:ai": "tsx scripts/test-ai-system.ts", "verify": "tsx scripts/verify-setup.ts", "db:generate": "turbo run db:generate", "db:push": "turbo run db:push", "db:migrate": "turbo run db:migrate", "db:studio": "turbo run db:studio", "db:seed": "turbo run db:seed", "db:reset": "turbo run db:reset", "db:setup": "npm run db:generate && npm run db:push && npm run db:seed", "db:test-setup": "DATABASE_URL=$DATABASE_URL npm run db:setup", "clean": "turbo run clean", "format": "prettier --write \"**/*.{ts,tsx,md,json}\"", "prepare": "husky install"}, "devDependencies": {"@types/node": "^20.10.0", "@typescript-eslint/eslint-plugin": "^6.13.0", "@typescript-eslint/parser": "^6.13.0", "eslint": "^8.54.0", "eslint-config-next": "^14.0.3", "eslint-config-prettier": "^9.0.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "husky": "^8.0.3", "lint-staged": "^15.1.0", "prettier": "^3.1.0", "turbo": "^1.11.0", "typescript": "^5.3.0"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "lint-staged": {"*.{ts,tsx,js,jsx}": ["eslint --fix", "prettier --write"], "*.{md,json}": ["prettier --write"]}, "repository": {"type": "git", "url": "https://github.com/your-org/careercraft-v2.git"}, "keywords": ["resume", "ai", "career", "nextjs", "typescript", "tailwindcss"], "author": "CareerCraft Team", "license": "MIT"}