{"name": "@careercraft/browser-extension", "version": "1.0.0", "description": "CareerCraft Intelligent Application Autofill Browser Extension", "private": true, "scripts": {"dev": "webpack --mode development --watch", "build": "webpack --mode production", "build:chrome": "npm run build && npm run package:chrome", "build:firefox": "npm run build && npm run package:firefox", "build:edge": "npm run build && npm run package:edge", "package:chrome": "web-ext build --source-dir=dist/chrome --artifacts-dir=packages", "package:firefox": "web-ext build --source-dir=dist/firefox --artifacts-dir=packages", "package:edge": "web-ext build --source-dir=dist/edge --artifacts-dir=packages", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.{ts,tsx}", "lint:fix": "eslint src/**/*.{ts,tsx} --fix", "type-check": "tsc --noEmit", "clean": "rimraf dist packages", "zip:chrome": "cd dist/chrome && zip -r ../../packages/chrome-extension.zip .", "zip:firefox": "cd dist/firefox && zip -r ../../packages/firefox-extension.zip .", "zip:edge": "cd dist/edge && zip -r ../../packages/edge-extension.zip ."}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "webextension-polyfill": "^0.10.0", "zustand": "^4.4.7", "lucide-react": "^0.294.0", "clsx": "^2.0.0", "tailwind-merge": "^2.0.0"}, "devDependencies": {"@types/chrome": "^0.0.251", "@types/react": "^18.2.38", "@types/react-dom": "^18.2.17", "@types/webextension-polyfill": "^0.10.7", "@types/jest": "^29.5.8", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "autoprefixer": "^10.4.16", "copy-webpack-plugin": "^11.0.0", "css-loader": "^6.8.1", "eslint": "^8.54.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "html-webpack-plugin": "^5.5.3", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "mini-css-extract-plugin": "^2.7.6", "postcss": "^8.4.31", "postcss-loader": "^7.3.3", "rimraf": "^5.0.5", "tailwindcss": "^3.3.5", "ts-jest": "^29.1.1", "ts-loader": "^9.5.1", "typescript": "^5.3.0", "web-ext": "^7.8.0", "webpack": "^5.89.0", "webpack-cli": "^5.1.4"}, "jest": {"preset": "ts-jest", "testEnvironment": "jsdom", "setupFilesAfterEnv": ["<rootDir>/src/test/setup.ts"], "moduleNameMapping": {"^@/(.*)$": "<rootDir>/src/$1"}, "collectCoverageFrom": ["src/**/*.{ts,tsx}", "!src/**/*.d.ts", "!src/test/**/*"], "coverageThreshold": {"global": {"branches": 80, "functions": 80, "lines": 80, "statements": 80}}}, "browserslist": ["chrome >= 88", "firefox >= 78", "edge >= 88"], "keywords": ["browser-extension", "job-application", "autofill", "career", "ai", "automation"], "author": "CareerCraft Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/Octa-src/CareerCraft.git", "directory": "extensions/careercraft-autofill"}, "bugs": {"url": "https://github.com/Octa-src/CareerCraft/issues"}, "homepage": "https://careercraft.onlinejobsearchhelp.com"}