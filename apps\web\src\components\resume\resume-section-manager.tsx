'use client';

import { useState } from 'react';
import { ResumeSection, ResumeSectionType } from '@careercraft/shared/types/resume';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Icons } from '@/components/ui/icons';
import { cn } from '@/lib/utils';

interface ResumeSectionManagerProps {
  sections: ResumeSection[];
  onSectionUpdate: (sections: ResumeSection[]) => void;
  isLoading?: boolean;
  className?: string;
}

const SECTION_CONFIG = {
  [ResumeSectionType.PERSONAL_INFO]: {
    title: 'Personal Information',
    description: 'Your contact details and basic information',
    icon: Icons.user,
    required: true,
  },
  [ResumeSectionType.SUMMARY]: {
    title: 'Professional Summary',
    description: 'A brief overview of your professional background',
    icon: Icons.fileText,
    required: false,
  },
  [ResumeSectionType.WORK_EXPERIENCE]: {
    title: 'Work Experience',
    description: 'Your professional work history and achievements',
    icon: Icons.briefcase,
    required: true,
  },
  [ResumeSectionType.EDUCATION]: {
    title: 'Education',
    description: 'Your educational background and qualifications',
    icon: Icons.graduationCap,
    required: true,
  },
  [ResumeSectionType.PROJECTS]: {
    title: 'Projects',
    description: 'Personal and professional projects you\'ve worked on',
    icon: Icons.code,
    required: false,
  },
  [ResumeSectionType.SKILLS]: {
    title: 'Skills',
    description: 'Technical and soft skills relevant to your career',
    icon: Icons.star,
    required: false,
  },
  [ResumeSectionType.CERTIFICATIONS]: {
    title: 'Certifications',
    description: 'Professional certifications and licenses',
    icon: Icons.award,
    required: false,
  },
  [ResumeSectionType.LANGUAGES]: {
    title: 'Languages',
    description: 'Languages you speak and your proficiency level',
    icon: Icons.globe,
    required: false,
  },
  [ResumeSectionType.AWARDS]: {
    title: 'Awards & Honors',
    description: 'Recognition and awards you\'ve received',
    icon: Icons.trophy,
    required: false,
  },
  [ResumeSectionType.PUBLICATIONS]: {
    title: 'Publications',
    description: 'Articles, papers, and other publications',
    icon: Icons.book,
    required: false,
  },
  [ResumeSectionType.VOLUNTEER]: {
    title: 'Volunteer Experience',
    description: 'Volunteer work and community involvement',
    icon: Icons.heart,
    required: false,
  },
  [ResumeSectionType.CUSTOM]: {
    title: 'Custom Section',
    description: 'Add a custom section for additional information',
    icon: Icons.plus,
    required: false,
  },
};

export function ResumeSectionManager({
  sections,
  onSectionUpdate,
  isLoading = false,
  className,
}: ResumeSectionManagerProps) {
  const [draggedSection, setDraggedSection] = useState<string | null>(null);

  const handleSectionToggle = (sectionId: string, isVisible: boolean) => {
    const updatedSections = sections.map(section =>
      section.id === sectionId ? { ...section, isVisible } : section
    );
    onSectionUpdate(updatedSections);
  };

  const handleSectionReorder = (draggedId: string, targetId: string) => {
    const draggedIndex = sections.findIndex(s => s.id === draggedId);
    const targetIndex = sections.findIndex(s => s.id === targetId);
    
    if (draggedIndex === -1 || targetIndex === -1) return;

    const reorderedSections = [...sections];
    const [draggedSection] = reorderedSections.splice(draggedIndex, 1);
    reorderedSections.splice(targetIndex, 0, draggedSection);

    // Update order values
    const updatedSections = reorderedSections.map((section, index) => ({
      ...section,
      order: index,
    }));

    onSectionUpdate(updatedSections);
  };

  const handleDragStart = (sectionId: string) => {
    setDraggedSection(sectionId);
  };

  const handleDragEnd = () => {
    setDraggedSection(null);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
  };

  const handleDrop = (e: React.DragEvent, targetId: string) => {
    e.preventDefault();
    if (draggedSection && draggedSection !== targetId) {
      handleSectionReorder(draggedSection, targetId);
    }
    setDraggedSection(null);
  };

  const sortedSections = [...sections].sort((a, b) => a.order - b.order);

  return (
    <Card className={cn('w-full', className)}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Icons.layout className="h-5 w-5" />
          Resume Sections
        </CardTitle>
        <CardDescription>
          Customize which sections appear on your resume and their order
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-3">
          {sortedSections.map((section) => {
            const config = SECTION_CONFIG[section.type];
            const IconComponent = config?.icon || Icons.fileText;
            
            return (
              <div
                key={section.id}
                draggable={!config?.required}
                onDragStart={() => handleDragStart(section.id)}
                onDragEnd={handleDragEnd}
                onDragOver={handleDragOver}
                onDrop={(e) => handleDrop(e, section.id)}
                className={cn(
                  'flex items-center justify-between p-4 border rounded-lg transition-all',
                  'hover:bg-accent/50',
                  draggedSection === section.id && 'opacity-50',
                  !config?.required && 'cursor-move'
                )}
              >
                <div className="flex items-center gap-3 flex-1">
                  <div className="flex items-center gap-2">
                    {!config?.required && (
                      <Icons.gripVertical className="h-4 w-4 text-muted-foreground" />
                    )}
                    <IconComponent className="h-5 w-5 text-muted-foreground" />
                  </div>
                  
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <h4 className="font-medium">{config?.title || section.title}</h4>
                      {config?.required && (
                        <span className="text-xs bg-primary/10 text-primary px-2 py-1 rounded">
                          Required
                        </span>
                      )}
                    </div>
                    <p className="text-sm text-muted-foreground">
                      {config?.description || 'Custom section'}
                    </p>
                  </div>
                </div>

                <div className="flex items-center gap-3">
                  <div className="flex items-center space-x-2">
                    <Switch
                      id={`section-${section.id}`}
                      checked={section.isVisible}
                      onCheckedChange={(checked) => handleSectionToggle(section.id, checked)}
                      disabled={config?.required || isLoading}
                    />
                    <Label
                      htmlFor={`section-${section.id}`}
                      className="text-sm font-medium"
                    >
                      {section.isVisible ? 'Visible' : 'Hidden'}
                    </Label>
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        <div className="pt-4 border-t">
          <Button
            variant="outline"
            className="w-full"
            disabled={isLoading}
          >
            <Icons.plus className="h-4 w-4 mr-2" />
            Add Custom Section
          </Button>
        </div>

        <div className="text-xs text-muted-foreground space-y-1">
          <p>• Drag sections to reorder them on your resume</p>
          <p>• Required sections cannot be hidden or removed</p>
          <p>• Toggle visibility to show or hide sections</p>
        </div>
      </CardContent>
    </Card>
  );
}
