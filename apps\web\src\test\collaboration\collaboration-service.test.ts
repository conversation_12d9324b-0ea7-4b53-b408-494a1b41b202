/**
 * Collaboration Service Unit Tests
 * 
 * Tests for collaboration database operations and business logic
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { CollaborationService } from '@/lib/collaboration/service'

// Mock Prisma
const mockPrisma = {
  resume: {
    findFirst: vi.fn()
  },
  collaborationSession: {
    create: vi.fn(),
    findUnique: vi.fn(),
    findMany: vi.fn(),
    update: vi.fn(),
    delete: vi.fn(),
    deleteMany: vi.fn()
  },
  collaborationPermission: {
    create: vi.fn(),
    findFirst: vi.fn(),
    findMany: vi.fn(),
    update: vi.fn(),
    deleteMany: vi.fn()
  },
  collaborationChange: {
    create: vi.fn(),
    findMany: vi.fn()
  },
  collaborationComment: {
    create: vi.fn(),
    findMany: vi.fn(),
    findUnique: vi.fn(),
    update: vi.fn()
  },
  collaborationPresence: {
    upsert: vi.fn(),
    findMany: vi.fn()
  },
  collaborationCursor: {
    upsert: vi.fn()
  }
}

vi.mock('@/lib/db', () => ({
  prisma: mockPrisma
}))

vi.mock('nanoid', () => ({
  nanoid: vi.fn(() => 'mock-session-token-123')
}))

describe('CollaborationService', () => {
  let service: CollaborationService

  beforeEach(() => {
    service = new CollaborationService()
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('createSession', () => {
    it('should create collaboration session successfully', async () => {
      const mockResume = {
        id: 'resume-123',
        userId: 'user-123',
        title: 'Software Engineer Resume'
      }

      const mockSession = {
        id: 'session-123',
        resumeId: 'resume-123',
        ownerId: 'user-123',
        sessionToken: 'mock-session-token-123',
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000),
        createdAt: new Date(),
        permissions: []
      }

      mockPrisma.resume.findFirst.mockResolvedValue(mockResume)
      mockPrisma.collaborationSession.create.mockResolvedValue(mockSession)
      mockPrisma.collaborationPermission.create.mockResolvedValue({})

      const result = await service.createSession({
        resumeId: 'resume-123',
        ownerId: 'user-123'
      })

      expect(result.id).toBe('session-123')
      expect(result.sessionToken).toBe('mock-session-token-123')
      expect(result.ownerId).toBe('user-123')
      
      expect(mockPrisma.resume.findFirst).toHaveBeenCalledWith({
        where: {
          id: 'resume-123',
          userId: 'user-123'
        }
      })
      
      expect(mockPrisma.collaborationSession.create).toHaveBeenCalled()
      expect(mockPrisma.collaborationPermission.create).toHaveBeenCalledWith({
        data: {
          sessionId: 'session-123',
          userId: 'user-123',
          permissionLevel: 'admin',
          grantedBy: 'user-123'
        }
      })
    })

    it('should throw error if resume not found', async () => {
      mockPrisma.resume.findFirst.mockResolvedValue(null)

      await expect(service.createSession({
        resumeId: 'nonexistent-resume',
        ownerId: 'user-123'
      })).rejects.toThrow('Resume not found or access denied')
    })

    it('should use custom expiration time', async () => {
      const mockResume = { id: 'resume-123', userId: 'user-123' }
      const customExpiresIn = 12 * 60 * 60 * 1000 // 12 hours

      mockPrisma.resume.findFirst.mockResolvedValue(mockResume)
      mockPrisma.collaborationSession.create.mockResolvedValue({
        id: 'session-123',
        resumeId: 'resume-123',
        ownerId: 'user-123',
        sessionToken: 'token-123',
        expiresAt: new Date(Date.now() + customExpiresIn),
        createdAt: new Date(),
        permissions: []
      })
      mockPrisma.collaborationPermission.create.mockResolvedValue({})

      await service.createSession({
        resumeId: 'resume-123',
        ownerId: 'user-123',
        expiresIn: customExpiresIn
      })

      const createCall = mockPrisma.collaborationSession.create.mock.calls[0][0]
      const expiresAt = createCall.data.expiresAt
      const expectedExpiry = new Date(Date.now() + customExpiresIn)
      
      // Allow for small time difference in test execution
      expect(Math.abs(expiresAt.getTime() - expectedExpiry.getTime())).toBeLessThan(1000)
    })
  })

  describe('getSessionByToken', () => {
    it('should return session by token', async () => {
      const mockSession = {
        id: 'session-123',
        resumeId: 'resume-123',
        ownerId: 'user-123',
        sessionToken: 'token-123',
        expiresAt: new Date(Date.now() + 60000), // 1 minute from now
        createdAt: new Date(),
        permissions: []
      }

      mockPrisma.collaborationSession.findUnique.mockResolvedValue(mockSession)

      const result = await service.getSessionByToken('token-123')

      expect(result).toBeDefined()
      expect(result!.id).toBe('session-123')
      expect(result!.sessionToken).toBe('token-123')
    })

    it('should return null for non-existent token', async () => {
      mockPrisma.collaborationSession.findUnique.mockResolvedValue(null)

      const result = await service.getSessionByToken('invalid-token')

      expect(result).toBeNull()
    })

    it('should delete and return null for expired session', async () => {
      const expiredSession = {
        id: 'session-123',
        resumeId: 'resume-123',
        ownerId: 'user-123',
        sessionToken: 'token-123',
        expiresAt: new Date(Date.now() - 60000), // 1 minute ago
        createdAt: new Date(),
        permissions: []
      }

      mockPrisma.collaborationSession.findUnique.mockResolvedValue(expiredSession)
      mockPrisma.collaborationSession.delete.mockResolvedValue({})

      const result = await service.getSessionByToken('token-123')

      expect(result).toBeNull()
      expect(mockPrisma.collaborationSession.delete).toHaveBeenCalledWith({
        where: { id: 'session-123' }
      })
    })
  })

  describe('inviteUser', () => {
    it('should invite user with admin permission', async () => {
      const adminPermission = {
        sessionId: 'session-123',
        userId: 'admin-123',
        permissionLevel: 'admin'
      }

      mockPrisma.collaborationPermission.findFirst
        .mockResolvedValueOnce(adminPermission) // Admin check
        .mockResolvedValueOnce(null) // Existing permission check

      mockPrisma.collaborationPermission.create.mockResolvedValue({})

      const result = await service.inviteUser({
        sessionId: 'session-123',
        userId: 'user-456',
        permissionLevel: 'edit',
        grantedBy: 'admin-123'
      })

      expect(result).toBe(true)
      expect(mockPrisma.collaborationPermission.create).toHaveBeenCalledWith({
        data: {
          sessionId: 'session-123',
          userId: 'user-456',
          permissionLevel: 'edit',
          grantedBy: 'admin-123'
        }
      })
    })

    it('should update existing user permission', async () => {
      const adminPermission = {
        sessionId: 'session-123',
        userId: 'admin-123',
        permissionLevel: 'admin'
      }

      const existingPermission = {
        id: 'permission-456',
        sessionId: 'session-123',
        userId: 'user-456',
        permissionLevel: 'view'
      }

      mockPrisma.collaborationPermission.findFirst
        .mockResolvedValueOnce(adminPermission)
        .mockResolvedValueOnce(existingPermission)

      mockPrisma.collaborationPermission.update.mockResolvedValue({})

      await service.inviteUser({
        sessionId: 'session-123',
        userId: 'user-456',
        permissionLevel: 'edit',
        grantedBy: 'admin-123'
      })

      expect(mockPrisma.collaborationPermission.update).toHaveBeenCalledWith({
        where: { id: 'permission-456' },
        data: {
          permissionLevel: 'edit',
          grantedBy: 'admin-123'
        }
      })
    })

    it('should throw error if granter lacks admin permission', async () => {
      mockPrisma.collaborationPermission.findFirst.mockResolvedValue(null)

      await expect(service.inviteUser({
        sessionId: 'session-123',
        userId: 'user-456',
        permissionLevel: 'edit',
        grantedBy: 'non-admin-123'
      })).rejects.toThrow('Insufficient permissions to invite users')
    })
  })

  describe('getUserPermission', () => {
    it('should return user permission level', async () => {
      const permission = {
        sessionId: 'session-123',
        userId: 'user-123',
        permissionLevel: 'edit'
      }

      mockPrisma.collaborationPermission.findFirst.mockResolvedValue(permission)

      const result = await service.getUserPermission('session-123', 'user-123')

      expect(result).toBe('edit')
    })

    it('should return null if user has no permission', async () => {
      mockPrisma.collaborationPermission.findFirst.mockResolvedValue(null)

      const result = await service.getUserPermission('session-123', 'user-123')

      expect(result).toBeNull()
    })
  })

  describe('recordChange', () => {
    it('should record collaboration change', async () => {
      const changeData = {
        operation: {
          type: 'insert',
          path: ['personalInfo', 'firstName'],
          value: 'John'
        }
      }

      mockPrisma.collaborationChange.create.mockResolvedValue({})

      await service.recordChange('session-123', 'user-123', 'edit', changeData)

      expect(mockPrisma.collaborationChange.create).toHaveBeenCalledWith({
        data: {
          sessionId: 'session-123',
          userId: 'user-123',
          changeType: 'edit',
          changeData: JSON.stringify(changeData)
        }
      })
    })
  })

  describe('createComment', () => {
    it('should create comment with proper permission', async () => {
      const permission = {
        sessionId: 'session-123',
        userId: 'user-123',
        permissionLevel: 'comment'
      }

      const mockComment = {
        id: 'comment-123',
        sessionId: 'session-123',
        userId: 'user-123',
        sectionPath: 'experience.0',
        content: 'Great experience!',
        parentId: null,
        isResolved: false,
        createdAt: new Date(),
        updatedAt: new Date(),
        user: {
          id: 'user-123',
          name: 'Test User',
          image: 'avatar.jpg'
        }
      }

      mockPrisma.collaborationPermission.findFirst.mockResolvedValue(permission)
      mockPrisma.collaborationComment.create.mockResolvedValue(mockComment)

      const result = await service.createComment({
        sessionId: 'session-123',
        userId: 'user-123',
        sectionPath: 'experience.0',
        content: 'Great experience!'
      })

      expect(result.id).toBe('comment-123')
      expect(result.content).toBe('Great experience!')
      expect(result.user.name).toBe('Test User')
    })

    it('should throw error if user lacks comment permission', async () => {
      mockPrisma.collaborationPermission.findFirst.mockResolvedValue({
        permissionLevel: 'view'
      })

      await expect(service.createComment({
        sessionId: 'session-123',
        userId: 'user-123',
        sectionPath: 'experience.0',
        content: 'Great experience!'
      })).rejects.toThrow('Insufficient permissions to comment')
    })
  })

  describe('updatePresence', () => {
    it('should update user presence', async () => {
      mockPrisma.collaborationPresence.upsert.mockResolvedValue({})
      mockPrisma.collaborationCursor.upsert.mockResolvedValue({})

      await service.updatePresence({
        sessionId: 'session-123',
        userId: 'user-123',
        status: 'active',
        sectionPath: 'personalInfo',
        position: 10
      })

      expect(mockPrisma.collaborationPresence.upsert).toHaveBeenCalledWith({
        where: {
          sessionId_userId: {
            sessionId: 'session-123',
            userId: 'user-123'
          }
        },
        update: {
          status: 'active',
          lastSeen: expect.any(Date)
        },
        create: {
          sessionId: 'session-123',
          userId: 'user-123',
          status: 'active',
          lastSeen: expect.any(Date)
        }
      })

      expect(mockPrisma.collaborationCursor.upsert).toHaveBeenCalledWith({
        where: {
          sessionId_userId: {
            sessionId: 'session-123',
            userId: 'user-123'
          }
        },
        update: {
          sectionPath: 'personalInfo',
          position: 10,
          updatedAt: expect.any(Date)
        },
        create: {
          sessionId: 'session-123',
          userId: 'user-123',
          sectionPath: 'personalInfo',
          position: 10
        }
      })
    })

    it('should update presence without cursor', async () => {
      mockPrisma.collaborationPresence.upsert.mockResolvedValue({})

      await service.updatePresence({
        sessionId: 'session-123',
        userId: 'user-123',
        status: 'idle'
      })

      expect(mockPrisma.collaborationPresence.upsert).toHaveBeenCalled()
      expect(mockPrisma.collaborationCursor.upsert).not.toHaveBeenCalled()
    })
  })

  describe('getActiveUsers', () => {
    it('should return active users within threshold', async () => {
      const mockPresence = [
        {
          userId: 'user-123',
          status: 'active',
          lastSeen: new Date(),
          user: {
            id: 'user-123',
            name: 'Test User',
            image: 'avatar.jpg'
          }
        }
      ]

      mockPrisma.collaborationPresence.findMany.mockResolvedValue(mockPresence)

      const result = await service.getActiveUsers('session-123')

      expect(result).toHaveLength(1)
      expect(result[0].userId).toBe('user-123')
      expect(result[0].status).toBe('active')
      
      // Check that query filters by recent activity
      const findManyCall = mockPrisma.collaborationPresence.findMany.mock.calls[0][0]
      expect(findManyCall.where.lastSeen.gte).toBeInstanceOf(Date)
    })
  })

  describe('cleanupExpiredSessions', () => {
    it('should delete expired sessions', async () => {
      mockPrisma.collaborationSession.deleteMany.mockResolvedValue({ count: 3 })

      const result = await service.cleanupExpiredSessions()

      expect(result).toBe(3)
      expect(mockPrisma.collaborationSession.deleteMany).toHaveBeenCalledWith({
        where: {
          expiresAt: {
            lt: expect.any(Date)
          }
        }
      })
    })
  })

  describe('deleteSession', () => {
    it('should delete session successfully', async () => {
      mockPrisma.collaborationSession.delete.mockResolvedValue({})

      const result = await service.deleteSession('session-123')

      expect(result).toBe(true)
      expect(mockPrisma.collaborationSession.delete).toHaveBeenCalledWith({
        where: { id: 'session-123' }
      })
    })

    it('should handle deletion errors', async () => {
      mockPrisma.collaborationSession.delete.mockRejectedValue(new Error('Database error'))

      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})

      const result = await service.deleteSession('session-123')

      expect(result).toBe(false)
      expect(consoleSpy).toHaveBeenCalled()

      consoleSpy.mockRestore()
    })
  })
})
