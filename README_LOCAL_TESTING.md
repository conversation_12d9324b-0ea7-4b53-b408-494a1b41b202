# 🚀 CareerCraft - Local Testing Instructions

Welcome! This guide will help you quickly set up and test all the features we've built so far.

## 🎯 Quick Start (Recommended)

### Option 1: Automated Setup (Windows)
```bash
# Double-click or run in Command Prompt
start-local-dev.bat
```

### Option 2: Automated Setup (Mac/Linux)
```bash
# Make executable and run
chmod +x scripts/setup-local-dev.sh
./scripts/setup-local-dev.sh
```

### Option 3: Manual Setup
```bash
# 1. Install dependencies
npm install

# 2. Setup environment
cp .env.example .env.local
# Edit .env.local with your settings (optional)

# 3. Setup database
npm run db:push
npm run db:seed

# 4. Verify setup
npm run verify

# 5. Start development server
npm run dev
```

## 🌐 Access the Application

Once running, visit: **http://localhost:3000**

### 🔐 Demo Accounts

| Account | Email | Purpose |
|---------|-------|---------|
| Demo User | `<EMAIL>` | General testing |
| <PERSON> | `<EMAIL>` | Software Engineer resume |
| <PERSON> | `<EMAIL>` | Marketing Manager resume |

*Note: No passwords needed for demo accounts*

## ✅ Feature Testing Checklist

### 🏠 **Homepage & Navigation**
- [ ] Visit `http://localhost:3000`
- [ ] Check hero section loads
- [ ] Test navigation menu
- [ ] Verify responsive design

### 🔐 **Authentication**
- [ ] Visit `/auth/signin`
- [ ] Test demo account login
- [ ] Check session persistence
- [ ] Test logout functionality

### 📊 **Dashboard**
- [ ] Visit `/dashboard`
- [ ] Check resume cards display
- [ ] Verify user stats
- [ ] Test navigation sidebar

### 📝 **Resume Builder**
- [ ] Visit `/dashboard/resumes/new`
- [ ] Test template selection
- [ ] Add personal information
- [ ] Create work experience entries
- [ ] Add education records
- [ ] Manage skills section
- [ ] Verify real-time preview
- [ ] Test save functionality

### 🎨 **Template System**
- [ ] Visit `/templates`
- [ ] Browse template gallery
- [ ] Test template filtering
- [ ] Preview templates
- [ ] Test template customization:
  - [ ] Change colors
  - [ ] Modify fonts
  - [ ] Adjust spacing
  - [ ] Switch layouts

### 🤖 **AI Content Generation**
- [ ] Visit `/ai-demo`
- [ ] Test content generator:
  - [ ] Professional summary
  - [ ] Work experience descriptions
  - [ ] Achievement bullets
  - [ ] Skills suggestions
- [ ] Verify multiple suggestions
- [ ] Test content editing
- [ ] Apply generated content

### 🛡️ **ATS Optimization**
- [ ] Visit `/ai-demo` (ATS Analysis tab)
- [ ] Run ATS analysis
- [ ] Check ATS score (0-100)
- [ ] Review identified issues
- [ ] Read recommendations
- [ ] Test with job description

### 📄 **PDF Export**
- [ ] Open any resume
- [ ] Click export button
- [ ] Test different formats:
  - [ ] PDF
  - [ ] DOCX
  - [ ] HTML
- [ ] Adjust quality settings
- [ ] Customize page layout
- [ ] Download exported file

### 📱 **Responsive Design**
- [ ] Test on desktop (1920x1080)
- [ ] Test on laptop (1366x768)
- [ ] Test on tablet (768x1024)
- [ ] Test on mobile (375x667)

## 🧪 Running Tests

### Comprehensive Test Suite
```bash
# Run all tests
npm run test:db      # Database layer
npm run test:auth    # Authentication
npm run test:ui      # UI components
npm run test:resume  # Resume builder
npm run test:templates # Template system
npm run test:ai      # AI features

# Verify setup
npm run verify
```

### Expected Results
```
✅ Database Tests: 12/12 passed
✅ Authentication Tests: 8/8 passed  
✅ UI Component Tests: 15/15 passed
✅ Resume Builder Tests: 10/10 passed
✅ Template System Tests: 8/8 passed
✅ AI Features Tests: 8/8 passed

🎉 All systems operational!
```

## 🔧 Development Tools

### Database Management
```bash
npm run db:studio    # Open Prisma Studio
npm run db:reset     # Reset database
npm run db:seed      # Add sample data
```

### Code Quality
```bash
npm run lint         # Check code style
npm run format       # Format code
npm run type-check   # TypeScript validation
```

## 🐛 Troubleshooting

### Common Issues

**Port 3000 already in use:**
```bash
npx kill-port 3000
# or
npm run dev -- -p 3001
```

**Database connection error:**
```bash
# Check if PostgreSQL is running
# Or switch to SQLite in .env.local:
DATABASE_URL="file:./dev.db"
```

**Missing dependencies:**
```bash
rm -rf node_modules package-lock.json
npm install
```

**Build errors:**
```bash
npm run clean
npm install
npm run build:packages
```

## 📊 Performance Expectations

### Page Load Times
- Homepage: < 2 seconds
- Dashboard: < 3 seconds
- Resume Builder: < 4 seconds
- Template Gallery: < 3 seconds

### AI Features
- Content Generation: 2-5 seconds
- ATS Analysis: 1-3 seconds
- Template Rendering: < 1 second

## 🎯 Key Features to Validate

### ✅ Core Functionality
- User authentication and sessions
- Resume CRUD operations
- Template selection and customization
- Real-time preview updates
- Data persistence

### ✅ Advanced Features
- AI content generation (8 content types)
- ATS optimization analysis
- PDF/DOCX export
- Responsive design
- Error handling

### ✅ User Experience
- Intuitive navigation
- Smooth interactions
- Loading states
- Error messages
- Mobile responsiveness

## 🚀 Ready for Step 8?

Once you've verified all features work correctly:

1. ✅ All test suites pass
2. ✅ Core user flows work end-to-end
3. ✅ AI features generate quality content
4. ✅ Export functionality works properly
5. ✅ No critical console errors
6. ✅ Responsive design verified
7. ✅ Performance meets expectations

**You're ready to proceed to Step 8: Real-time Collaboration & Sharing!** 🎉

## 📞 Need Help?

If you encounter any issues:

1. Check browser console for errors
2. Verify `.env.local` configuration
3. Run `npm run verify` to check setup
4. Check database connection
5. Ensure all dependencies are installed

The application should work smoothly with mock data even without real API keys configured.
