# 🔧 Manual Setup Guide - CareerCraft Local Testing

## 🚨 If Automated Scripts Fail, Follow This Manual Process

---

## **📋 Prerequisites Installation**

### **1. Install Node.js**
- Download from: https://nodejs.org/
- Choose LTS version (18.x or higher)
- Verify: `node --version` and `npm --version`

### **2. Install PostgreSQL**
- Download from: https://www.postgresql.org/download/
- During installation, remember the password for `postgres` user
- Verify: `psql --version`

### **3. Install Git**
- Download from: https://git-scm.com/
- Verify: `git --version`

### **4. Install VS Code (Optional)**
- Download from: https://code.visualstudio.com/

---

## **🏗️ Project Setup**

### **Step 1: Create Project**
```bash
# Create and navigate to project
npx create-next-app@latest careercraft-local --typescript --tailwind --eslint --app --src-dir --import-alias "@/*"
cd careercraft-local
```

### **Step 2: Install Core Dependencies**
```bash
# Authentication and database
npm install @prisma/client prisma next-auth @auth/prisma-adapter

# Payment processing
npm install stripe @stripe/stripe-js

# AI integration
npm install openai

# UI components
npm install @radix-ui/react-dialog @radix-ui/react-dropdown-menu lucide-react

# Forms and validation
npm install @hookform/resolvers react-hook-form zod

# HTTP and data fetching
npm install axios swr

# Charts and utilities
npm install recharts date-fns clsx tailwind-merge
```

### **Step 3: Install Development Dependencies**
```bash
npm install -D @types/node @types/react @types/react-dom
npm install -D jest @testing-library/react @testing-library/jest-dom
npm install -D playwright @playwright/test
npm install -D prisma-erd-generator cross-env concurrently tsx
```

---

## **🗄️ Database Setup**

### **Step 1: Create Database**
```sql
-- Connect to PostgreSQL as postgres user
psql -U postgres

-- Create user and database
CREATE USER careercraft_user WITH PASSWORD 'local_password';
CREATE DATABASE careercraft_local OWNER careercraft_user;
GRANT ALL PRIVILEGES ON DATABASE careercraft_local TO careercraft_user;
ALTER USER careercraft_user CREATEDB;

-- Exit PostgreSQL
\q
```

### **Step 2: Create Prisma Schema**
Create `prisma/schema.prisma`:
```prisma
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  name      String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("users")
}

model Resume {
  id        String   @id @default(cuid())
  userId    String
  title     String
  content   Json
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  user User @relation(fields: [userId], references: [id])

  @@map("resumes")
}
```

### **Step 3: Setup Prisma**
```bash
# Generate Prisma client
npx prisma generate

# Push schema to database
npx prisma db push
```

---

## **⚙️ Configuration Files**

### **Create .env.local**
```bash
# Database
DATABASE_URL="postgresql://careercraft_user:local_password@localhost:5432/careercraft_local"

# NextAuth
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-local-secret-key-change-this-in-production"

# Google OAuth (Optional - get from Google Cloud Console)
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"

# Stripe (Optional - get from Stripe Dashboard)
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY="pk_test_..."
STRIPE_SECRET_KEY="sk_test_..."

# OpenAI (Optional - get from OpenAI Platform)
OPENAI_API_KEY="sk-..."

# App Settings
NODE_ENV="development"
NEXT_PUBLIC_APP_URL="http://localhost:3000"
```

### **Update package.json Scripts**
Add these scripts to your `package.json`:
```json
{
  "scripts": {
    "dev": "next dev",
    "build": "next build",
    "start": "next start",
    "lint": "next lint",
    "db:generate": "prisma generate",
    "db:push": "prisma db push",
    "db:studio": "prisma studio",
    "test": "jest",
    "test:watch": "jest --watch"
  }
}
```

---

## **🧪 Basic Testing Setup**

### **Create jest.config.js**
```javascript
const nextJest = require('next/jest')

const createJestConfig = nextJest({
  dir: './',
})

const customJestConfig = {
  setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],
  testEnvironment: 'jest-environment-jsdom',
}

module.exports = createJestConfig(customJestConfig)
```

### **Create jest.setup.js**
```javascript
import '@testing-library/jest-dom'
```

---

## **🚀 Basic Application Files**

### **Create src/lib/prisma.ts**
```typescript
import { PrismaClient } from '@prisma/client'

const globalForPrisma = globalThis as unknown as {
  prisma: PrismaClient | undefined
}

export const prisma = globalForPrisma.prisma ?? new PrismaClient()

if (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma
```

### **Create src/app/api/health/route.ts**
```typescript
import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET() {
  try {
    await prisma.$queryRaw`SELECT 1`
    
    return NextResponse.json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      services: {
        database: 'connected',
      },
    })
  } catch (error) {
    return NextResponse.json(
      {
        status: 'unhealthy',
        error: 'Database connection failed',
      },
      { status: 500 }
    )
  }
}
```

### **Update src/app/page.tsx**
```typescript
export default function Home() {
  return (
    <main className="flex min-h-screen flex-col items-center justify-center p-24">
      <div className="text-center">
        <h1 className="text-4xl font-bold mb-4">
          🚀 CareerCraft Local Testing
        </h1>
        <p className="text-xl text-gray-600 mb-8">
          Your local development environment is ready!
        </p>
        <div className="space-y-4">
          <div className="p-4 bg-green-100 rounded-lg">
            <p className="text-green-800">✅ Next.js App Running</p>
          </div>
          <div className="p-4 bg-blue-100 rounded-lg">
            <p className="text-blue-800">🗄️ Database Connected</p>
          </div>
          <div className="p-4 bg-purple-100 rounded-lg">
            <p className="text-purple-800">🧪 Ready for Testing</p>
          </div>
        </div>
      </div>
    </main>
  )
}
```

---

## **🎯 Start Development**

### **1. Start the Development Server**
```bash
npm run dev
```

### **2. Test the Application**
- Visit: http://localhost:3000
- Check health: http://localhost:3000/api/health
- Open database admin: `npm run db:studio` (http://localhost:5555)

### **3. Run Tests**
```bash
npm run test
```

---

## **🔧 Troubleshooting**

### **Database Connection Issues**
```bash
# Check if PostgreSQL is running
pg_isready -h localhost -p 5432

# Start PostgreSQL (Windows)
net start postgresql-x64-14

# Connect to database manually
psql -h localhost -p 5432 -U careercraft_user -d careercraft_local
```

### **Port Already in Use**
```bash
# Find what's using port 3000
netstat -ano | findstr :3000

# Kill the process (replace PID)
taskkill /PID <PID> /F
```

### **Node Modules Issues**
```bash
# Clear cache and reinstall
rm -rf node_modules package-lock.json
npm install
```

---

## **✅ Verification Checklist**

- [ ] Node.js 18+ installed
- [ ] PostgreSQL installed and running
- [ ] Project created with Next.js
- [ ] Dependencies installed
- [ ] Database created and connected
- [ ] Prisma schema applied
- [ ] Environment variables configured
- [ ] Development server starts successfully
- [ ] Health check endpoint works
- [ ] Database admin accessible

---

## **🎉 Success!**

If all steps complete successfully, you have:
- ✅ Complete local development environment
- ✅ Database setup and connected
- ✅ Basic application structure
- ✅ Testing framework configured
- ✅ Ready for Epic feature development

**Next: Start implementing Epic 8.0 payment features or Epic 1.0 career intelligence!**
