/**
 * Comprehensive Test Suite for Market Analysis Engine
 * Tests Milestone 1.3: Market Analysis Engine functionality
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { MarketAnalysisEngine } from '@/lib/market-analysis/market-analysis-engine'

// Mock dependencies
vi.mock('@/lib/database', () => ({
  prisma: {
    jobPosting: {
      findMany: vi.fn(),
    },
    marketAnalysis: {
      create: vi.fn(),
    },
    userProfileVector: {
      findFirst: vi.fn(),
    }
  }
}))

vi.mock('@/lib/openai', () => ({
  openai: {
    chat: {
      completions: {
        create: vi.fn()
      }
    }
  }
}))

vi.mock('@/lib/redis', () => ({
  redis: {
    get: vi.fn(),
    setex: vi.fn()
  }
}))

vi.mock('@/lib/logger', () => ({
  logger: {
    info: vi.fn(),
    error: vi.fn(),
    debug: vi.fn()
  }
}))

describe('MarketAnalysisEngine', () => {
  let engine: MarketAnalysisEngine
  let mockJobData: any[]

  beforeEach(() => {
    engine = new MarketAnalysisEngine()
    
    // Mock job data
    mockJobData = [
      {
        id: '1',
        title: 'Senior Software Engineer',
        company: 'Tech Corp',
        location: 'San Francisco, CA',
        salaryMin: 120000,
        salaryMax: 180000,
        skills: ['JavaScript', 'React', 'Node.js'],
        experienceLevel: 'SENIOR',
        jobType: 'FULL_TIME',
        industry: 'Technology',
        scrapedAt: new Date(),
        source: 'linkedin'
      },
      {
        id: '2',
        title: 'Data Scientist',
        company: 'Data Inc',
        location: 'New York, NY',
        salaryMin: 100000,
        salaryMax: 150000,
        skills: ['Python', 'Machine Learning', 'SQL'],
        experienceLevel: 'MID',
        jobType: 'FULL_TIME',
        industry: 'Technology',
        scrapedAt: new Date(),
        source: 'indeed'
      },
      {
        id: '3',
        title: 'Product Manager',
        company: 'Product Co',
        location: 'Seattle, WA',
        salaryMin: 110000,
        salaryMax: 160000,
        skills: ['Product Strategy', 'Analytics', 'Leadership'],
        experienceLevel: 'SENIOR',
        jobType: 'FULL_TIME',
        industry: 'Technology',
        scrapedAt: new Date(),
        source: 'company'
      }
    ]

    // Reset mocks
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('Market Analysis Generation', () => {
    it('should generate comprehensive market analysis', async () => {
      // Mock database response
      const { prisma } = await import('@/lib/database')
      vi.mocked(prisma.jobPosting.findMany).mockResolvedValue(mockJobData)

      // Mock OpenAI responses
      const { openai } = await import('@/lib/openai')
      vi.mocked(openai.chat.completions.create).mockResolvedValue({
        choices: [{
          message: {
            content: JSON.stringify({
              marketHealth: 'STRONG',
              competitionLevel: 'MEDIUM',
              salaryTrend: 'INCREASING',
              demandTrend: 'GROWING',
              skillGaps: [{ skill: 'React', gap: 20, opportunity: 'High demand' }],
              emergingSkills: [{ skill: 'TypeScript', growth: 25, adoption: 15 }],
              marketOpportunities: [{ opportunity: 'Frontend development', potential: 30, timeframe: '6 months' }],
              riskFactors: [{ risk: 'Market saturation', severity: 3, mitigation: 'Specialize' }]
            })
          }
        }]
      } as any)

      // Mock Redis (no cache)
      const { redis } = await import('@/lib/redis')
      vi.mocked(redis.get).mockResolvedValue(null)
      vi.mocked(redis.setex).mockResolvedValue('OK')

      const analysis = await engine.generateMarketAnalysis({
        analysisType: 'REAL_TIME',
        targetRole: 'Software Engineer',
        targetLocation: 'San Francisco',
        timeframe: '30d'
      })

      expect(analysis).toBeDefined()
      expect(analysis.analysisType).toBe('REAL_TIME')
      expect(analysis.targetRole).toBe('Software Engineer')
      expect(analysis.targetLocation).toBe('San Francisco')
      expect(analysis.metrics).toBeDefined()
      expect(analysis.insights).toBeDefined()
      expect(analysis.predictions).toBeDefined()
      expect(analysis.recommendations).toBeDefined()
      expect(analysis.confidence).toBeGreaterThan(0)
      expect(analysis.confidence).toBeLessThanOrEqual(100)
    })

    it('should handle cached analysis', async () => {
      const cachedAnalysis = {
        id: 'cached_analysis',
        analysisType: 'REAL_TIME',
        expiresAt: new Date(Date.now() + 3600000), // 1 hour from now
        generatedAt: new Date(),
        confidence: 85
      }

      // Mock Redis cache hit
      const { redis } = await import('@/lib/redis')
      vi.mocked(redis.get).mockResolvedValue(JSON.stringify(cachedAnalysis))

      const analysis = await engine.generateMarketAnalysis({
        analysisType: 'REAL_TIME',
        targetRole: 'Software Engineer'
      })

      expect(analysis.id).toBe('cached_analysis')
      expect(analysis.confidence).toBe(85)
    })

    it('should handle different analysis types', async () => {
      const { prisma } = await import('@/lib/database')
      vi.mocked(prisma.jobPosting.findMany).mockResolvedValue(mockJobData)

      const { redis } = await import('@/lib/redis')
      vi.mocked(redis.get).mockResolvedValue(null)

      const analysisTypes = ['REAL_TIME', 'HISTORICAL', 'PREDICTIVE', 'COMPARATIVE'] as const

      for (const type of analysisTypes) {
        const analysis = await engine.generateMarketAnalysis({
          analysisType: type,
          targetRole: 'Software Engineer'
        })

        expect(analysis.analysisType).toBe(type)
        expect(analysis).toBeDefined()
      }
    })
  })

  describe('Market Metrics Calculation', () => {
    it('should calculate accurate market metrics', async () => {
      const { prisma } = await import('@/lib/database')
      vi.mocked(prisma.jobPosting.findMany).mockResolvedValue(mockJobData)

      const { redis } = await import('@/lib/redis')
      vi.mocked(redis.get).mockResolvedValue(null)

      const analysis = await engine.generateMarketAnalysis({
        analysisType: 'REAL_TIME',
        targetRole: 'Software Engineer'
      })

      const metrics = analysis.metrics

      // Verify basic metrics
      expect(metrics.totalJobs).toBe(3)
      expect(metrics.averageSalary).toBeGreaterThan(0)
      expect(metrics.salaryRange.min).toBeLessThanOrEqual(metrics.salaryRange.max)

      // Verify skill analysis
      expect(metrics.topSkills).toBeDefined()
      expect(Array.isArray(metrics.topSkills)).toBe(true)
      expect(metrics.topSkills.length).toBeGreaterThan(0)

      // Verify company analysis
      expect(metrics.topCompanies).toBeDefined()
      expect(Array.isArray(metrics.topCompanies)).toBe(true)
      expect(metrics.topCompanies.length).toBeGreaterThan(0)

      // Verify location analysis
      expect(metrics.locationDistribution).toBeDefined()
      expect(Array.isArray(metrics.locationDistribution)).toBe(true)

      // Verify experience level analysis
      expect(metrics.experienceLevels).toBeDefined()
      expect(Array.isArray(metrics.experienceLevels)).toBe(true)

      // Verify industry breakdown
      expect(metrics.industryBreakdown).toBeDefined()
      expect(Array.isArray(metrics.industryBreakdown)).toBe(true)
    })

    it('should handle empty job data gracefully', async () => {
      const { prisma } = await import('@/lib/database')
      vi.mocked(prisma.jobPosting.findMany).mockResolvedValue([])

      const { redis } = await import('@/lib/redis')
      vi.mocked(redis.get).mockResolvedValue(null)

      const analysis = await engine.generateMarketAnalysis({
        analysisType: 'REAL_TIME',
        targetRole: 'Nonexistent Role'
      })

      expect(analysis.metrics.totalJobs).toBe(0)
      expect(analysis.metrics.averageSalary).toBe(0)
      expect(analysis.confidence).toBeLessThan(70) // Low confidence for empty data
    })
  })

  describe('AI Insights Generation', () => {
    it('should generate market insights with AI fallback', async () => {
      const { prisma } = await import('@/lib/database')
      vi.mocked(prisma.jobPosting.findMany).mockResolvedValue(mockJobData)

      const { redis } = await import('@/lib/redis')
      vi.mocked(redis.get).mockResolvedValue(null)

      // Mock AI failure to test fallback
      const { openai } = await import('@/lib/openai')
      vi.mocked(openai.chat.completions.create).mockRejectedValue(new Error('AI service unavailable'))

      const analysis = await engine.generateMarketAnalysis({
        analysisType: 'REAL_TIME',
        targetRole: 'Software Engineer'
      })

      // Should still generate insights using fallback methods
      expect(analysis.insights).toBeDefined()
      expect(analysis.insights.marketHealth).toMatch(/STRONG|MODERATE|WEAK/)
      expect(analysis.insights.competitionLevel).toMatch(/LOW|MEDIUM|HIGH/)
      expect(analysis.insights.salaryTrend).toMatch(/INCREASING|STABLE|DECREASING/)
      expect(analysis.insights.demandTrend).toMatch(/GROWING|STABLE|DECLINING/)
    })

    it('should identify skill gaps correctly', async () => {
      const { prisma } = await import('@/lib/database')
      vi.mocked(prisma.jobPosting.findMany).mockResolvedValue(mockJobData)

      const { redis } = await import('@/lib/redis')
      vi.mocked(redis.get).mockResolvedValue(null)

      const analysis = await engine.generateMarketAnalysis({
        analysisType: 'REAL_TIME',
        targetRole: 'Software Engineer'
      })

      expect(analysis.insights.skillGaps).toBeDefined()
      expect(Array.isArray(analysis.insights.skillGaps)).toBe(true)
      
      if (analysis.insights.skillGaps.length > 0) {
        const skillGap = analysis.insights.skillGaps[0]
        expect(skillGap).toHaveProperty('skill')
        expect(skillGap).toHaveProperty('gap')
        expect(skillGap).toHaveProperty('opportunity')
        expect(typeof skillGap.gap).toBe('number')
      }
    })

    it('should identify emerging skills', async () => {
      const { prisma } = await import('@/lib/database')
      vi.mocked(prisma.jobPosting.findMany).mockResolvedValue(mockJobData)

      const { redis } = await import('@/lib/redis')
      vi.mocked(redis.get).mockResolvedValue(null)

      const analysis = await engine.generateMarketAnalysis({
        analysisType: 'REAL_TIME',
        targetRole: 'Software Engineer'
      })

      expect(analysis.insights.emergingSkills).toBeDefined()
      expect(Array.isArray(analysis.insights.emergingSkills)).toBe(true)
      
      if (analysis.insights.emergingSkills.length > 0) {
        const emergingSkill = analysis.insights.emergingSkills[0]
        expect(emergingSkill).toHaveProperty('skill')
        expect(emergingSkill).toHaveProperty('growth')
        expect(emergingSkill).toHaveProperty('adoption')
        expect(typeof emergingSkill.growth).toBe('number')
        expect(typeof emergingSkill.adoption).toBe('number')
      }
    })
  })

  describe('Market Predictions', () => {
    it('should generate salary forecasts', async () => {
      const { prisma } = await import('@/lib/database')
      vi.mocked(prisma.jobPosting.findMany).mockResolvedValue(mockJobData)

      const { redis } = await import('@/lib/redis')
      vi.mocked(redis.get).mockResolvedValue(null)

      const analysis = await engine.generateMarketAnalysis({
        analysisType: 'PREDICTIVE',
        targetRole: 'Software Engineer'
      })

      expect(analysis.predictions.salaryForecast).toBeDefined()
      expect(Array.isArray(analysis.predictions.salaryForecast)).toBe(true)
      expect(analysis.predictions.salaryForecast.length).toBeGreaterThan(0)

      const forecast = analysis.predictions.salaryForecast[0]
      expect(forecast).toHaveProperty('period')
      expect(forecast).toHaveProperty('predictedSalary')
      expect(forecast).toHaveProperty('confidence')
      expect(typeof forecast.predictedSalary).toBe('number')
      expect(typeof forecast.confidence).toBe('number')
      expect(forecast.confidence).toBeGreaterThan(0)
      expect(forecast.confidence).toBeLessThanOrEqual(100)
    })

    it('should generate demand forecasts', async () => {
      const { prisma } = await import('@/lib/database')
      vi.mocked(prisma.jobPosting.findMany).mockResolvedValue(mockJobData)

      const { redis } = await import('@/lib/redis')
      vi.mocked(redis.get).mockResolvedValue(null)

      const analysis = await engine.generateMarketAnalysis({
        analysisType: 'PREDICTIVE',
        targetRole: 'Software Engineer'
      })

      expect(analysis.predictions.demandForecast).toBeDefined()
      expect(Array.isArray(analysis.predictions.demandForecast)).toBe(true)
      expect(analysis.predictions.demandForecast.length).toBeGreaterThan(0)

      const forecast = analysis.predictions.demandForecast[0]
      expect(forecast).toHaveProperty('period')
      expect(forecast).toHaveProperty('predictedDemand')
      expect(forecast).toHaveProperty('confidence')
      expect(typeof forecast.predictedDemand).toBe('number')
      expect(typeof forecast.confidence).toBe('number')
    })

    it('should assess automation risk', async () => {
      const { prisma } = await import('@/lib/database')
      vi.mocked(prisma.jobPosting.findMany).mockResolvedValue(mockJobData)

      const { redis } = await import('@/lib/redis')
      vi.mocked(redis.get).mockResolvedValue(null)

      const analysis = await engine.generateMarketAnalysis({
        analysisType: 'PREDICTIVE',
        targetRole: 'Data Entry Clerk' // High automation risk role
      })

      expect(analysis.predictions.automationRisk).toBeDefined()
      expect(analysis.predictions.automationRisk).toHaveProperty('risk')
      expect(analysis.predictions.automationRisk).toHaveProperty('timeframe')
      expect(analysis.predictions.automationRisk).toHaveProperty('affectedRoles')
      expect(typeof analysis.predictions.automationRisk.risk).toBe('number')
      expect(analysis.predictions.automationRisk.risk).toBeGreaterThanOrEqual(0)
      expect(analysis.predictions.automationRisk.risk).toBeLessThanOrEqual(100)
    })
  })

  describe('Market Recommendations', () => {
    it('should generate career move recommendations', async () => {
      const { prisma } = await import('@/lib/database')
      vi.mocked(prisma.jobPosting.findMany).mockResolvedValue(mockJobData)

      const { redis } = await import('@/lib/redis')
      vi.mocked(redis.get).mockResolvedValue(null)

      const analysis = await engine.generateMarketAnalysis({
        analysisType: 'REAL_TIME',
        targetRole: 'Software Engineer'
      })

      expect(analysis.recommendations.careerMoves).toBeDefined()
      expect(Array.isArray(analysis.recommendations.careerMoves)).toBe(true)
      
      if (analysis.recommendations.careerMoves.length > 0) {
        const careerMove = analysis.recommendations.careerMoves[0]
        expect(careerMove).toHaveProperty('move')
        expect(careerMove).toHaveProperty('rationale')
        expect(careerMove).toHaveProperty('timeline')
        expect(careerMove).toHaveProperty('difficulty')
        expect(typeof careerMove.difficulty).toBe('number')
        expect(careerMove.difficulty).toBeGreaterThanOrEqual(1)
        expect(careerMove.difficulty).toBeLessThanOrEqual(10)
      }
    })

    it('should generate skill development recommendations', async () => {
      const { prisma } = await import('@/lib/database')
      vi.mocked(prisma.jobPosting.findMany).mockResolvedValue(mockJobData)

      const { redis } = await import('@/lib/redis')
      vi.mocked(redis.get).mockResolvedValue(null)

      const analysis = await engine.generateMarketAnalysis({
        analysisType: 'REAL_TIME',
        targetRole: 'Software Engineer'
      })

      expect(analysis.recommendations.skillDevelopment).toBeDefined()
      expect(Array.isArray(analysis.recommendations.skillDevelopment)).toBe(true)
      
      if (analysis.recommendations.skillDevelopment.length > 0) {
        const skillRec = analysis.recommendations.skillDevelopment[0]
        expect(skillRec).toHaveProperty('skill')
        expect(skillRec).toHaveProperty('priority')
        expect(skillRec).toHaveProperty('learningPath')
        expect(skillRec).toHaveProperty('roi')
        expect(typeof skillRec.priority).toBe('number')
        expect(typeof skillRec.roi).toBe('number')
      }
    })

    it('should generate location recommendations', async () => {
      const { prisma } = await import('@/lib/database')
      vi.mocked(prisma.jobPosting.findMany).mockResolvedValue(mockJobData)

      const { redis } = await import('@/lib/redis')
      vi.mocked(redis.get).mockResolvedValue(null)

      const analysis = await engine.generateMarketAnalysis({
        analysisType: 'REAL_TIME',
        targetRole: 'Software Engineer'
      })

      expect(analysis.recommendations.locationRecommendations).toBeDefined()
      expect(Array.isArray(analysis.recommendations.locationRecommendations)).toBe(true)
      
      if (analysis.recommendations.locationRecommendations.length > 0) {
        const locationRec = analysis.recommendations.locationRecommendations[0]
        expect(locationRec).toHaveProperty('location')
        expect(locationRec).toHaveProperty('advantages')
        expect(locationRec).toHaveProperty('salary')
        expect(Array.isArray(locationRec.advantages)).toBe(true)
        expect(typeof locationRec.salary).toBe('number')
      }
    })
  })

  describe('Confidence Scoring', () => {
    it('should calculate confidence based on data quality', async () => {
      const { prisma } = await import('@/lib/database')
      
      // Test with high-quality data
      const highQualityData = mockJobData.map(job => ({
        ...job,
        scrapedAt: new Date() // Fresh data
      }))
      
      vi.mocked(prisma.jobPosting.findMany).mockResolvedValue(highQualityData)

      const { redis } = await import('@/lib/redis')
      vi.mocked(redis.get).mockResolvedValue(null)

      const analysis = await engine.generateMarketAnalysis({
        analysisType: 'REAL_TIME',
        targetRole: 'Software Engineer'
      })

      expect(analysis.confidence).toBeGreaterThan(50)
      expect(analysis.confidence).toBeLessThanOrEqual(95)
    })

    it('should lower confidence for old data', async () => {
      const { prisma } = await import('@/lib/database')
      
      // Test with old data
      const oldData = mockJobData.map(job => ({
        ...job,
        scrapedAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // 30 days old
      }))
      
      vi.mocked(prisma.jobPosting.findMany).mockResolvedValue(oldData)

      const { redis } = await import('@/lib/redis')
      vi.mocked(redis.get).mockResolvedValue(null)

      const analysis = await engine.generateMarketAnalysis({
        analysisType: 'REAL_TIME',
        targetRole: 'Software Engineer'
      })

      expect(analysis.confidence).toBeLessThan(80) // Lower confidence for old data
    })
  })

  describe('Error Handling', () => {
    it('should handle database errors gracefully', async () => {
      const { prisma } = await import('@/lib/database')
      vi.mocked(prisma.jobPosting.findMany).mockRejectedValue(new Error('Database connection failed'))

      await expect(engine.generateMarketAnalysis({
        analysisType: 'REAL_TIME',
        targetRole: 'Software Engineer'
      })).rejects.toThrow('Market analysis generation failed')
    })

    it('should handle OpenAI API errors gracefully', async () => {
      const { prisma } = await import('@/lib/database')
      vi.mocked(prisma.jobPosting.findMany).mockResolvedValue(mockJobData)

      const { openai } = await import('@/lib/openai')
      vi.mocked(openai.chat.completions.create).mockRejectedValue(new Error('OpenAI API error'))

      const { redis } = await import('@/lib/redis')
      vi.mocked(redis.get).mockResolvedValue(null)

      // Should still complete analysis with fallback methods
      const analysis = await engine.generateMarketAnalysis({
        analysisType: 'REAL_TIME',
        targetRole: 'Software Engineer'
      })

      expect(analysis).toBeDefined()
      expect(analysis.insights).toBeDefined()
      expect(analysis.predictions).toBeDefined()
      expect(analysis.recommendations).toBeDefined()
    })

    it('should handle Redis cache errors gracefully', async () => {
      const { prisma } = await import('@/lib/database')
      vi.mocked(prisma.jobPosting.findMany).mockResolvedValue(mockJobData)

      const { redis } = await import('@/lib/redis')
      vi.mocked(redis.get).mockRejectedValue(new Error('Redis connection failed'))
      vi.mocked(redis.setex).mockRejectedValue(new Error('Redis write failed'))

      // Should still complete analysis without caching
      const analysis = await engine.generateMarketAnalysis({
        analysisType: 'REAL_TIME',
        targetRole: 'Software Engineer'
      })

      expect(analysis).toBeDefined()
    })
  })

  describe('Performance', () => {
    it('should complete analysis within reasonable time', async () => {
      const { prisma } = await import('@/lib/database')
      vi.mocked(prisma.jobPosting.findMany).mockResolvedValue(mockJobData)

      const { redis } = await import('@/lib/redis')
      vi.mocked(redis.get).mockResolvedValue(null)

      const startTime = Date.now()
      
      await engine.generateMarketAnalysis({
        analysisType: 'REAL_TIME',
        targetRole: 'Software Engineer'
      })

      const duration = Date.now() - startTime
      expect(duration).toBeLessThan(10000) // Should complete within 10 seconds
    })
  })
})
