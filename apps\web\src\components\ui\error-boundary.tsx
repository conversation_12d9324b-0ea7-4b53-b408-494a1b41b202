'use client';

import React, { Component, ErrorInfo, ReactNode } from 'react';
import { Button } from './button';
import { Alert, AlertDescription, AlertTitle } from './alert';
import { Icons } from './icons';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error?: Error;
}

export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    this.props.onError?.(error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return <DefaultErrorFallback error={this.state.error} reset={() => this.setState({ hasError: false })} />;
    }

    return this.props.children;
  }
}

interface ErrorFallbackProps {
  error?: Error;
  reset?: () => void;
  title?: string;
  description?: string;
  showDetails?: boolean;
}

export function DefaultErrorFallback({ 
  error, 
  reset, 
  title = 'Something went wrong',
  description = 'An unexpected error occurred. Please try again.',
  showDetails = process.env.NODE_ENV === 'development'
}: ErrorFallbackProps) {
  return (
    <div className="flex flex-col items-center justify-center min-h-[400px] p-6">
      <Alert variant="destructive" className="max-w-md">
        <Icons.alertCircle className="h-4 w-4" />
        <AlertTitle>{title}</AlertTitle>
        <AlertDescription className="mt-2">
          {description}
          {showDetails && error && (
            <details className="mt-4">
              <summary className="cursor-pointer text-sm font-medium">
                Error Details
              </summary>
              <pre className="mt-2 text-xs bg-muted p-2 rounded overflow-auto">
                {error.message}
                {error.stack && (
                  <>
                    {'\n\n'}
                    {error.stack}
                  </>
                )}
              </pre>
            </details>
          )}
        </AlertDescription>
        {reset && (
          <div className="mt-4">
            <Button onClick={reset} variant="outline" size="sm">
              Try Again
            </Button>
          </div>
        )}
      </Alert>
    </div>
  );
}

interface AsyncErrorBoundaryProps {
  children: ReactNode;
  fallback?: (error: Error, retry: () => void) => ReactNode;
}

export function AsyncErrorBoundary({ children, fallback }: AsyncErrorBoundaryProps) {
  const [error, setError] = React.useState<Error | null>(null);

  const resetError = () => setError(null);

  React.useEffect(() => {
    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      setError(new Error(event.reason));
    };

    window.addEventListener('unhandledrejection', handleUnhandledRejection);

    return () => {
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
    };
  }, []);

  if (error) {
    if (fallback) {
      return fallback(error, resetError);
    }
    return <DefaultErrorFallback error={error} reset={resetError} />;
  }

  return (
    <ErrorBoundary onError={(error) => setError(error)}>
      {children}
    </ErrorBoundary>
  );
}

// Hook for handling async errors
export function useErrorHandler() {
  const [error, setError] = React.useState<Error | null>(null);

  const handleError = React.useCallback((error: Error) => {
    console.error('Async error:', error);
    setError(error);
  }, []);

  const resetError = React.useCallback(() => {
    setError(null);
  }, []);

  return { error, handleError, resetError };
}

// Higher-order component for wrapping components with error boundary
export function withErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  errorFallback?: ReactNode
) {
  const WrappedComponent = (props: P) => (
    <ErrorBoundary fallback={errorFallback}>
      <Component {...props} />
    </ErrorBoundary>
  );

  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`;

  return WrappedComponent;
}

// Error page component for route-level errors
interface ErrorPageProps {
  error?: Error;
  statusCode?: number;
  title?: string;
  description?: string;
  showHomeButton?: boolean;
}

export function ErrorPage({ 
  error,
  statusCode = 500,
  title,
  description,
  showHomeButton = true 
}: ErrorPageProps) {
  const defaultTitles: Record<number, string> = {
    404: 'Page Not Found',
    403: 'Access Forbidden',
    500: 'Internal Server Error',
    503: 'Service Unavailable',
  };

  const defaultDescriptions: Record<number, string> = {
    404: 'The page you are looking for does not exist.',
    403: 'You do not have permission to access this resource.',
    500: 'An internal server error occurred.',
    503: 'The service is temporarily unavailable.',
  };

  const finalTitle = title || defaultTitles[statusCode] || 'An Error Occurred';
  const finalDescription = description || defaultDescriptions[statusCode] || 'Something went wrong.';

  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-6">
      <div className="text-center space-y-6 max-w-md">
        <div className="space-y-2">
          <h1 className="text-6xl font-bold text-muted-foreground">{statusCode}</h1>
          <h2 className="text-2xl font-semibold">{finalTitle}</h2>
          <p className="text-muted-foreground">{finalDescription}</p>
        </div>

        {process.env.NODE_ENV === 'development' && error && (
          <Alert variant="destructive">
            <Icons.alertCircle className="h-4 w-4" />
            <AlertTitle>Development Error Details</AlertTitle>
            <AlertDescription>
              <pre className="text-xs mt-2 overflow-auto">
                {error.message}
                {error.stack && (
                  <>
                    {'\n\n'}
                    {error.stack}
                  </>
                )}
              </pre>
            </AlertDescription>
          </Alert>
        )}

        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button onClick={() => window.history.back()} variant="outline">
            <Icons.chevronLeft className="mr-2 h-4 w-4" />
            Go Back
          </Button>
          {showHomeButton && (
            <Button onClick={() => window.location.href = '/'}>
              <Icons.home className="mr-2 h-4 w-4" />
              Go Home
            </Button>
          )}
        </div>
      </div>
    </div>
  );
}
