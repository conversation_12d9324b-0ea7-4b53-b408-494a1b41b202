<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CareerCraft Settings</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f9fafb;
            min-height: 100vh;
        }
        
        #root {
            width: 100%;
            min-height: 100vh;
        }
        
        /* Loading state */
        .loading {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100vh;
            flex-direction: column;
            gap: 24px;
        }
        
        .loading-spinner {
            width: 48px;
            height: 48px;
            border: 4px solid #e5e7eb;
            border-top: 4px solid #4f46e5;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .loading-text {
            color: #6b7280;
            font-size: 16px;
            font-weight: 500;
        }
        
        /* Error state */
        .error {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100vh;
            flex-direction: column;
            gap: 24px;
            padding: 40px;
            text-align: center;
            max-width: 600px;
            margin: 0 auto;
        }
        
        .error-icon {
            width: 64px;
            height: 64px;
            color: #dc2626;
        }
        
        .error-title {
            font-size: 24px;
            font-weight: 700;
            color: #111827;
            margin: 0;
        }
        
        .error-message {
            font-size: 16px;
            color: #6b7280;
            margin: 0;
            line-height: 1.6;
        }
        
        .error-actions {
            display: flex;
            gap: 12px;
            flex-wrap: wrap;
            justify-content: center;
        }
        
        .error-button {
            background: #4f46e5;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        
        .error-button:hover {
            background: #4338ca;
            transform: translateY(-1px);
        }
        
        .error-button-secondary {
            background: #f3f4f6;
            color: #374151;
            border: 1px solid #d1d5db;
        }
        
        .error-button-secondary:hover {
            background: #e5e7eb;
        }
        
        /* Header styles */
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 32px 0;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 24px;
        }
        
        .header-title {
            font-size: 32px;
            font-weight: 700;
            margin: 0 0 8px 0;
        }
        
        .header-subtitle {
            font-size: 16px;
            opacity: 0.9;
            margin: 0;
        }
        
        /* Main content */
        .main-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 32px 24px;
        }
        
        /* Navigation */
        .nav-tabs {
            display: flex;
            border-bottom: 1px solid #e5e7eb;
            margin-bottom: 32px;
            overflow-x: auto;
        }
        
        .nav-tab {
            padding: 12px 24px;
            border: none;
            background: none;
            color: #6b7280;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.2s;
            white-space: nowrap;
        }
        
        .nav-tab:hover {
            color: #374151;
        }
        
        .nav-tab.active {
            color: #4f46e5;
            border-bottom-color: #4f46e5;
        }
        
        /* Cards */
        .card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
            border: 1px solid #e5e7eb;
            margin-bottom: 24px;
        }
        
        .card-header {
            padding: 24px 24px 0 24px;
        }
        
        .card-title {
            font-size: 18px;
            font-weight: 600;
            color: #111827;
            margin: 0 0 8px 0;
        }
        
        .card-description {
            font-size: 14px;
            color: #6b7280;
            margin: 0;
        }
        
        .card-content {
            padding: 24px;
        }
        
        /* Form elements */
        .form-group {
            margin-bottom: 24px;
        }
        
        .form-label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: #374151;
            margin-bottom: 8px;
        }
        
        .form-input {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.2s;
        }
        
        .form-input:focus {
            outline: none;
            border-color: #4f46e5;
            box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
        }
        
        .form-checkbox {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 16px;
        }
        
        .form-checkbox input {
            width: 16px;
            height: 16px;
        }
        
        .form-checkbox label {
            font-size: 14px;
            color: #374151;
            margin: 0;
        }
        
        /* Buttons */
        .btn {
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            border: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        
        .btn-primary {
            background: #4f46e5;
            color: white;
        }
        
        .btn-primary:hover {
            background: #4338ca;
            transform: translateY(-1px);
        }
        
        .btn-secondary {
            background: #f3f4f6;
            color: #374151;
            border: 1px solid #d1d5db;
        }
        
        .btn-secondary:hover {
            background: #e5e7eb;
        }
        
        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }
        
        /* Responsive */
        @media (max-width: 768px) {
            .header-content,
            .main-content {
                padding-left: 16px;
                padding-right: 16px;
            }
            
            .header-title {
                font-size: 24px;
            }
            
            .nav-tabs {
                margin-left: -16px;
                margin-right: -16px;
                padding-left: 16px;
                padding-right: 16px;
            }
        }
    </style>
</head>
<body>
    <div id="root">
        <div class="loading">
            <div class="loading-spinner"></div>
            <div class="loading-text">Loading CareerCraft Settings...</div>
        </div>
    </div>
    
    <script>
        // Show error state if React fails to load
        setTimeout(() => {
            const root = document.getElementById('root');
            if (root && root.innerHTML.includes('loading')) {
                root.innerHTML = `
                    <div class="error">
                        <svg class="error-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                        </svg>
                        <h1 class="error-title">Settings Failed to Load</h1>
                        <p class="error-message">CareerCraft settings page failed to initialize. This might be due to a temporary issue or extension corruption.</p>
                        <div class="error-actions">
                            <button class="error-button" onclick="window.location.reload()">
                                <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                </svg>
                                Retry
                            </button>
                            <a href="https://careercraft.onlinejobsearchhelp.com/help" class="error-button error-button-secondary" target="_blank">
                                <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                Get Help
                            </a>
                        </div>
                    </div>
                `;
            }
        }, 10000);
    </script>
</body>
</html>
