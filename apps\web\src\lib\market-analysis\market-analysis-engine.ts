/**
 * Market Analysis Engine
 * Implements FR-5.3 & FR-5.4: Market Analysis and Trend Prediction
 * 
 * Core engine for analyzing job market data and generating insights
 */

import { prisma } from '@/lib/database'
import { openai } from '@/lib/openai'
import { redis } from '@/lib/redis'
import { logger } from '@/lib/logger'

export interface MarketMetrics {
  totalJobs: number
  averageSalary: number
  salaryRange: { min: number; max: number }
  topSkills: Array<{ skill: string; demand: number; growth: number }>
  topCompanies: Array<{ company: string; jobCount: number; avgSalary: number }>
  locationDistribution: Array<{ location: string; jobCount: number; avgSalary: number }>
  experienceLevels: Array<{ level: string; jobCount: number; avgSalary: number }>
  industryBreakdown: Array<{ industry: string; jobCount: number; growth: number }>
  jobTypeDistribution: Array<{ type: string; percentage: number }>
  demandTrends: Array<{ date: string; jobCount: number; avgSalary: number }>
}

export interface MarketAnalysis {
  id: string
  userId?: string
  analysisType: 'REAL_TIME' | 'HISTORICAL' | 'PREDICTIVE' | 'COMPARATIVE'
  targetRole?: string
  targetLocation?: string
  targetSkills?: string[]
  timeframe: string
  metrics: MarketMetrics
  insights: MarketInsights
  predictions: MarketPredictions
  recommendations: MarketRecommendations
  confidence: number
  generatedAt: Date
  expiresAt: Date
}

export interface MarketInsights {
  marketHealth: 'STRONG' | 'MODERATE' | 'WEAK'
  competitionLevel: 'LOW' | 'MEDIUM' | 'HIGH'
  salaryTrend: 'INCREASING' | 'STABLE' | 'DECREASING'
  demandTrend: 'GROWING' | 'STABLE' | 'DECLINING'
  skillGaps: Array<{ skill: string; gap: number; opportunity: string }>
  emergingSkills: Array<{ skill: string; growth: number; adoption: number }>
  marketOpportunities: Array<{ opportunity: string; potential: number; timeframe: string }>
  riskFactors: Array<{ risk: string; severity: number; mitigation: string }>
}

export interface MarketPredictions {
  salaryForecast: Array<{ period: string; predictedSalary: number; confidence: number }>
  demandForecast: Array<{ period: string; predictedDemand: number; confidence: number }>
  skillDemandForecast: Array<{ skill: string; currentDemand: number; predictedDemand: number }>
  industryGrowth: Array<{ industry: string; currentSize: number; predictedGrowth: number }>
  locationTrends: Array<{ location: string; currentJobs: number; predictedGrowth: number }>
  automationRisk: { risk: number; timeframe: string; affectedRoles: string[] }
}

export interface MarketRecommendations {
  careerMoves: Array<{ move: string; rationale: string; timeline: string; difficulty: number }>
  skillDevelopment: Array<{ skill: string; priority: number; learningPath: string; roi: number }>
  locationRecommendations: Array<{ location: string; advantages: string[]; salary: number }>
  industryTransitions: Array<{ industry: string; transferability: number; requirements: string[] }>
  timingRecommendations: Array<{ action: string; timing: string; rationale: string }>
}

export class MarketAnalysisEngine {
  private cachePrefix = 'market_analysis:'
  private cacheTTL = 3600 // 1 hour

  constructor() {
    logger.info('Market Analysis Engine initialized')
  }

  /**
   * Generate comprehensive market analysis
   */
  async generateMarketAnalysis(params: {
    userId?: string
    analysisType: MarketAnalysis['analysisType']
    targetRole?: string
    targetLocation?: string
    targetSkills?: string[]
    timeframe?: string
  }): Promise<MarketAnalysis> {
    const startTime = Date.now()
    
    try {
      logger.info('Starting market analysis generation', { params })

      // Check cache first
      const cacheKey = this.generateCacheKey(params)
      const cached = await this.getCachedAnalysis(cacheKey)
      if (cached) {
        logger.info('Returning cached market analysis')
        return cached
      }

      // Gather market data
      const marketData = await this.gatherMarketData(params)
      
      // Calculate metrics
      const metrics = await this.calculateMarketMetrics(marketData, params)
      
      // Generate AI insights
      const insights = await this.generateMarketInsights(metrics, params)
      
      // Generate predictions
      const predictions = await this.generateMarketPredictions(metrics, marketData, params)
      
      // Generate recommendations
      const recommendations = await this.generateMarketRecommendations(insights, predictions, params)
      
      // Calculate confidence score
      const confidence = this.calculateConfidenceScore(marketData, metrics)

      const analysis: MarketAnalysis = {
        id: `analysis_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        userId: params.userId,
        analysisType: params.analysisType,
        targetRole: params.targetRole,
        targetLocation: params.targetLocation,
        targetSkills: params.targetSkills,
        timeframe: params.timeframe || '30d',
        metrics,
        insights,
        predictions,
        recommendations,
        confidence,
        generatedAt: new Date(),
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours
      }

      // Cache the analysis
      await this.cacheAnalysis(cacheKey, analysis)
      
      // Store in database
      await this.storeAnalysis(analysis)

      const duration = Date.now() - startTime
      logger.info('Market analysis generated successfully', { 
        analysisId: analysis.id, 
        duration,
        confidence: analysis.confidence 
      })

      return analysis

    } catch (error) {
      logger.error('Error generating market analysis', { error, params })
      throw new Error(`Market analysis generation failed: ${error.message}`)
    }
  }

  /**
   * Gather relevant market data based on analysis parameters
   */
  private async gatherMarketData(params: any): Promise<any> {
    const filters: any = {
      isActive: true,
      scrapedAt: {
        gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // Last 30 days
      }
    }

    // Apply filters based on parameters
    if (params.targetRole) {
      filters.OR = [
        { title: { contains: params.targetRole, mode: 'insensitive' } },
        { description: { contains: params.targetRole, mode: 'insensitive' } }
      ]
    }

    if (params.targetLocation) {
      filters.location = { contains: params.targetLocation, mode: 'insensitive' }
    }

    if (params.targetSkills && params.targetSkills.length > 0) {
      filters.skills = {
        hasSome: params.targetSkills
      }
    }

    // Fetch job data
    const jobs = await prisma.jobPosting.findMany({
      where: filters,
      select: {
        id: true,
        title: true,
        company: true,
        location: true,
        salaryMin: true,
        salaryMax: true,
        skills: true,
        experienceLevel: true,
        jobType: true,
        industry: true,
        scrapedAt: true,
        source: true
      },
      orderBy: { scrapedAt: 'desc' },
      take: 10000 // Limit for performance
    })

    // Fetch historical data for trends
    const historicalData = await this.getHistoricalData(params)

    // Fetch user profile data for comparison
    const profileData = params.userId ? await this.getUserProfileData(params.userId) : null

    return {
      jobs,
      historicalData,
      profileData,
      totalJobs: jobs.length,
      dataFreshness: jobs.length > 0 ? jobs[0].scrapedAt : new Date()
    }
  }

  /**
   * Calculate comprehensive market metrics
   */
  private async calculateMarketMetrics(marketData: any, params: any): Promise<MarketMetrics> {
    const { jobs } = marketData

    // Basic metrics
    const totalJobs = jobs.length
    const salariesWithData = jobs.filter(job => job.salaryMin && job.salaryMax)
    const averageSalary = salariesWithData.length > 0 
      ? salariesWithData.reduce((sum, job) => sum + ((job.salaryMin + job.salaryMax) / 2), 0) / salariesWithData.length
      : 0

    const salaryRange = {
      min: Math.min(...salariesWithData.map(job => job.salaryMin)),
      max: Math.max(...salariesWithData.map(job => job.salaryMax))
    }

    // Skill analysis
    const skillCounts = new Map<string, number>()
    jobs.forEach(job => {
      if (job.skills && Array.isArray(job.skills)) {
        job.skills.forEach(skill => {
          skillCounts.set(skill, (skillCounts.get(skill) || 0) + 1)
        })
      }
    })

    const topSkills = Array.from(skillCounts.entries())
      .sort(([,a], [,b]) => b - a)
      .slice(0, 20)
      .map(([skill, demand]) => ({
        skill,
        demand,
        growth: await this.calculateSkillGrowth(skill, marketData.historicalData)
      }))

    // Company analysis
    const companyCounts = new Map<string, { count: number; salaries: number[] }>()
    jobs.forEach(job => {
      const company = job.company
      if (!companyCounts.has(company)) {
        companyCounts.set(company, { count: 0, salaries: [] })
      }
      const companyData = companyCounts.get(company)!
      companyData.count++
      if (job.salaryMin && job.salaryMax) {
        companyData.salaries.push((job.salaryMin + job.salaryMax) / 2)
      }
    })

    const topCompanies = Array.from(companyCounts.entries())
      .sort(([,a], [,b]) => b.count - a.count)
      .slice(0, 15)
      .map(([company, data]) => ({
        company,
        jobCount: data.count,
        avgSalary: data.salaries.length > 0 
          ? data.salaries.reduce((sum, sal) => sum + sal, 0) / data.salaries.length 
          : 0
      }))

    // Location analysis
    const locationCounts = new Map<string, { count: number; salaries: number[] }>()
    jobs.forEach(job => {
      const location = job.location || 'Unknown'
      if (!locationCounts.has(location)) {
        locationCounts.set(location, { count: 0, salaries: [] })
      }
      const locationData = locationCounts.get(location)!
      locationData.count++
      if (job.salaryMin && job.salaryMax) {
        locationData.salaries.push((job.salaryMin + job.salaryMax) / 2)
      }
    })

    const locationDistribution = Array.from(locationCounts.entries())
      .sort(([,a], [,b]) => b.count - a.count)
      .slice(0, 15)
      .map(([location, data]) => ({
        location,
        jobCount: data.count,
        avgSalary: data.salaries.length > 0 
          ? data.salaries.reduce((sum, sal) => sum + sal, 0) / data.salaries.length 
          : 0
      }))

    // Experience level analysis
    const experienceCounts = new Map<string, { count: number; salaries: number[] }>()
    jobs.forEach(job => {
      const level = job.experienceLevel || 'Unknown'
      if (!experienceCounts.has(level)) {
        experienceCounts.set(level, { count: 0, salaries: [] })
      }
      const levelData = experienceCounts.get(level)!
      levelData.count++
      if (job.salaryMin && job.salaryMax) {
        levelData.salaries.push((job.salaryMin + job.salaryMax) / 2)
      }
    })

    const experienceLevels = Array.from(experienceCounts.entries())
      .map(([level, data]) => ({
        level,
        jobCount: data.count,
        avgSalary: data.salaries.length > 0 
          ? data.salaries.reduce((sum, sal) => sum + sal, 0) / data.salaries.length 
          : 0
      }))

    // Industry analysis
    const industryCounts = new Map<string, number>()
    jobs.forEach(job => {
      const industry = job.industry || 'Unknown'
      industryCounts.set(industry, (industryCounts.get(industry) || 0) + 1)
    })

    const industryBreakdown = Array.from(industryCounts.entries())
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10)
      .map(([industry, jobCount]) => ({
        industry,
        jobCount,
        growth: await this.calculateIndustryGrowth(industry, marketData.historicalData)
      }))

    // Job type distribution
    const jobTypeCounts = new Map<string, number>()
    jobs.forEach(job => {
      const type = job.jobType || 'Unknown'
      jobTypeCounts.set(type, (jobTypeCounts.get(type) || 0) + 1)
    })

    const jobTypeDistribution = Array.from(jobTypeCounts.entries())
      .map(([type, count]) => ({
        type,
        percentage: (count / totalJobs) * 100
      }))

    // Demand trends (simplified - would need more historical data)
    const demandTrends = await this.calculateDemandTrends(marketData.historicalData)

    return {
      totalJobs,
      averageSalary: Math.round(averageSalary),
      salaryRange,
      topSkills,
      topCompanies,
      locationDistribution,
      experienceLevels,
      industryBreakdown,
      jobTypeDistribution,
      demandTrends
    }
  }

  /**
   * Generate AI-powered market insights
   */
  private async generateMarketInsights(metrics: MarketMetrics, params: any): Promise<MarketInsights> {
    try {
      const prompt = `
        Analyze this job market data and provide comprehensive insights:

        Market Data:
        - Total Jobs: ${metrics.totalJobs}
        - Average Salary: $${metrics.averageSalary}
        - Top Skills: ${metrics.topSkills.slice(0, 10).map(s => s.skill).join(', ')}
        - Top Companies: ${metrics.topCompanies.slice(0, 5).map(c => c.company).join(', ')}
        - Target Role: ${params.targetRole || 'General'}
        - Target Location: ${params.targetLocation || 'All locations'}

        Provide analysis in JSON format with:
        {
          "marketHealth": "STRONG|MODERATE|WEAK",
          "competitionLevel": "LOW|MEDIUM|HIGH",
          "salaryTrend": "INCREASING|STABLE|DECREASING",
          "demandTrend": "GROWING|STABLE|DECLINING",
          "skillGaps": [{"skill": "string", "gap": number, "opportunity": "string"}],
          "emergingSkills": [{"skill": "string", "growth": number, "adoption": number}],
          "marketOpportunities": [{"opportunity": "string", "potential": number, "timeframe": "string"}],
          "riskFactors": [{"risk": "string", "severity": number, "mitigation": "string"}]
        }
      `

      const response = await openai.chat.completions.create({
        model: 'gpt-4',
        messages: [{ role: 'user', content: prompt }],
        temperature: 0.1,
        max_tokens: 1500
      })

      const aiInsights = JSON.parse(response.choices[0].message.content || '{}')

      // Validate and enhance AI insights with calculated data
      return {
        marketHealth: aiInsights.marketHealth || this.calculateMarketHealth(metrics),
        competitionLevel: aiInsights.competitionLevel || this.calculateCompetitionLevel(metrics),
        salaryTrend: aiInsights.salaryTrend || 'STABLE',
        demandTrend: aiInsights.demandTrend || 'STABLE',
        skillGaps: aiInsights.skillGaps || this.identifySkillGaps(metrics),
        emergingSkills: aiInsights.emergingSkills || this.identifyEmergingSkills(metrics),
        marketOpportunities: aiInsights.marketOpportunities || this.identifyMarketOpportunities(metrics),
        riskFactors: aiInsights.riskFactors || this.identifyRiskFactors(metrics)
      }

    } catch (error) {
      logger.error('Error generating AI insights', { error })

      // Fallback to calculated insights
      return {
        marketHealth: this.calculateMarketHealth(metrics),
        competitionLevel: this.calculateCompetitionLevel(metrics),
        salaryTrend: 'STABLE',
        demandTrend: 'STABLE',
        skillGaps: this.identifySkillGaps(metrics),
        emergingSkills: this.identifyEmergingSkills(metrics),
        marketOpportunities: this.identifyMarketOpportunities(metrics),
        riskFactors: this.identifyRiskFactors(metrics)
      }
    }
  }

  /**
   * Generate market predictions using AI and statistical analysis
   */
  private async generateMarketPredictions(metrics: MarketMetrics, marketData: any, params: any): Promise<MarketPredictions> {
    try {
      const prompt = `
        Based on this market data, generate predictions for the next 12 months:

        Current Metrics:
        - Average Salary: $${metrics.averageSalary}
        - Total Jobs: ${metrics.totalJobs}
        - Top Growing Skills: ${metrics.topSkills.filter(s => s.growth > 0).slice(0, 5).map(s => s.skill).join(', ')}
        - Industry Distribution: ${metrics.industryBreakdown.slice(0, 3).map(i => i.industry).join(', ')}

        Provide predictions in JSON format:
        {
          "salaryForecast": [{"period": "3m", "predictedSalary": number, "confidence": number}],
          "demandForecast": [{"period": "3m", "predictedDemand": number, "confidence": number}],
          "skillDemandForecast": [{"skill": "string", "currentDemand": number, "predictedDemand": number}],
          "industryGrowth": [{"industry": "string", "currentSize": number, "predictedGrowth": number}],
          "locationTrends": [{"location": "string", "currentJobs": number, "predictedGrowth": number}],
          "automationRisk": {"risk": number, "timeframe": "string", "affectedRoles": ["string"]}
        }
      `

      const response = await openai.chat.completions.create({
        model: 'gpt-4',
        messages: [{ role: 'user', content: prompt }],
        temperature: 0.2,
        max_tokens: 2000
      })

      const aiPredictions = JSON.parse(response.choices[0].message.content || '{}')

      // Enhance with statistical predictions
      return {
        salaryForecast: aiPredictions.salaryForecast || this.generateSalaryForecast(metrics),
        demandForecast: aiPredictions.demandForecast || this.generateDemandForecast(metrics),
        skillDemandForecast: aiPredictions.skillDemandForecast || this.generateSkillDemandForecast(metrics),
        industryGrowth: aiPredictions.industryGrowth || this.generateIndustryGrowthForecast(metrics),
        locationTrends: aiPredictions.locationTrends || this.generateLocationTrends(metrics),
        automationRisk: aiPredictions.automationRisk || this.assessAutomationRisk(params.targetRole)
      }

    } catch (error) {
      logger.error('Error generating predictions', { error })

      // Fallback to statistical predictions
      return {
        salaryForecast: this.generateSalaryForecast(metrics),
        demandForecast: this.generateDemandForecast(metrics),
        skillDemandForecast: this.generateSkillDemandForecast(metrics),
        industryGrowth: this.generateIndustryGrowthForecast(metrics),
        locationTrends: this.generateLocationTrends(metrics),
        automationRisk: this.assessAutomationRisk(params.targetRole)
      }
    }
  }

  /**
   * Generate actionable market recommendations
   */
  private async generateMarketRecommendations(
    insights: MarketInsights,
    predictions: MarketPredictions,
    params: any
  ): Promise<MarketRecommendations> {
    try {
      const prompt = `
        Generate career recommendations based on market analysis:

        Market Health: ${insights.marketHealth}
        Competition: ${insights.competitionLevel}
        Emerging Skills: ${insights.emergingSkills.slice(0, 5).map(s => s.skill).join(', ')}
        Market Opportunities: ${insights.marketOpportunities.slice(0, 3).map(o => o.opportunity).join(', ')}
        Target Role: ${params.targetRole || 'General'}

        Provide recommendations in JSON format:
        {
          "careerMoves": [{"move": "string", "rationale": "string", "timeline": "string", "difficulty": number}],
          "skillDevelopment": [{"skill": "string", "priority": number, "learningPath": "string", "roi": number}],
          "locationRecommendations": [{"location": "string", "advantages": ["string"], "salary": number}],
          "industryTransitions": [{"industry": "string", "transferability": number, "requirements": ["string"]}],
          "timingRecommendations": [{"action": "string", "timing": "string", "rationale": "string"}]
        }
      `

      const response = await openai.chat.completions.create({
        model: 'gpt-4',
        messages: [{ role: 'user', content: prompt }],
        temperature: 0.3,
        max_tokens: 2000
      })

      const aiRecommendations = JSON.parse(response.choices[0].message.content || '{}')

      return {
        careerMoves: aiRecommendations.careerMoves || this.generateCareerMoves(insights, predictions),
        skillDevelopment: aiRecommendations.skillDevelopment || this.generateSkillRecommendations(insights),
        locationRecommendations: aiRecommendations.locationRecommendations || this.generateLocationRecommendations(predictions),
        industryTransitions: aiRecommendations.industryTransitions || this.generateIndustryTransitions(insights),
        timingRecommendations: aiRecommendations.timingRecommendations || this.generateTimingRecommendations(insights, predictions)
      }

    } catch (error) {
      logger.error('Error generating recommendations', { error })

      // Fallback recommendations
      return {
        careerMoves: this.generateCareerMoves(insights, predictions),
        skillDevelopment: this.generateSkillRecommendations(insights),
        locationRecommendations: this.generateLocationRecommendations(predictions),
        industryTransitions: this.generateIndustryTransitions(insights),
        timingRecommendations: this.generateTimingRecommendations(insights, predictions)
      }
    }
  }

  // Helper methods for calculations and analysis
  private calculateMarketHealth(metrics: MarketMetrics): 'STRONG' | 'MODERATE' | 'WEAK' {
    const jobCount = metrics.totalJobs
    const avgSalary = metrics.averageSalary

    if (jobCount > 1000 && avgSalary > 80000) return 'STRONG'
    if (jobCount > 500 && avgSalary > 60000) return 'MODERATE'
    return 'WEAK'
  }

  private calculateCompetitionLevel(metrics: MarketMetrics): 'LOW' | 'MEDIUM' | 'HIGH' {
    const jobsPerCompany = metrics.totalJobs / metrics.topCompanies.length

    if (jobsPerCompany > 50) return 'LOW'
    if (jobsPerCompany > 20) return 'MEDIUM'
    return 'HIGH'
  }

  private identifySkillGaps(metrics: MarketMetrics): Array<{ skill: string; gap: number; opportunity: string }> {
    return metrics.topSkills
      .filter(skill => skill.growth > 20)
      .slice(0, 5)
      .map(skill => ({
        skill: skill.skill,
        gap: skill.growth,
        opportunity: `High demand growth of ${skill.growth}% indicates strong opportunity`
      }))
  }

  private identifyEmergingSkills(metrics: MarketMetrics): Array<{ skill: string; growth: number; adoption: number }> {
    return metrics.topSkills
      .filter(skill => skill.growth > 15)
      .slice(0, 8)
      .map(skill => ({
        skill: skill.skill,
        growth: skill.growth,
        adoption: skill.demand / metrics.totalJobs * 100
      }))
  }

  private identifyMarketOpportunities(metrics: MarketMetrics): Array<{ opportunity: string; potential: number; timeframe: string }> {
    const opportunities = []

    // High growth industries
    const growingIndustries = metrics.industryBreakdown.filter(industry => industry.growth > 10)
    growingIndustries.forEach(industry => {
      opportunities.push({
        opportunity: `${industry.industry} industry expansion`,
        potential: industry.growth,
        timeframe: '6-12 months'
      })
    })

    // Emerging skills
    const emergingSkills = metrics.topSkills.filter(skill => skill.growth > 25)
    emergingSkills.forEach(skill => {
      opportunities.push({
        opportunity: `${skill.skill} skill specialization`,
        potential: skill.growth,
        timeframe: '3-6 months'
      })
    })

    return opportunities.slice(0, 5)
  }

  private identifyRiskFactors(metrics: MarketMetrics): Array<{ risk: string; severity: number; mitigation: string }> {
    const risks = []

    // Market saturation
    if (metrics.totalJobs < 500) {
      risks.push({
        risk: 'Limited job availability',
        severity: 7,
        mitigation: 'Consider expanding search criteria or location flexibility'
      })
    }

    // Salary stagnation
    if (metrics.averageSalary < 60000) {
      risks.push({
        risk: 'Below-average salary range',
        severity: 5,
        mitigation: 'Focus on skill development and specialization'
      })
    }

    // High competition
    const avgJobsPerCompany = metrics.totalJobs / metrics.topCompanies.length
    if (avgJobsPerCompany < 10) {
      risks.push({
        risk: 'High competition for positions',
        severity: 6,
        mitigation: 'Differentiate through unique skill combinations'
      })
    }

    return risks
  }

  private generateSalaryForecast(metrics: MarketMetrics): Array<{ period: string; predictedSalary: number; confidence: number }> {
    const baseSalary = metrics.averageSalary
    const growthRate = 0.03 // 3% annual growth assumption

    return [
      { period: '3m', predictedSalary: Math.round(baseSalary * 1.0075), confidence: 85 },
      { period: '6m', predictedSalary: Math.round(baseSalary * 1.015), confidence: 80 },
      { period: '12m', predictedSalary: Math.round(baseSalary * (1 + growthRate)), confidence: 70 }
    ]
  }

  private generateDemandForecast(metrics: MarketMetrics): Array<{ period: string; predictedDemand: number; confidence: number }> {
    const baseDemand = metrics.totalJobs

    return [
      { period: '3m', predictedDemand: Math.round(baseDemand * 1.05), confidence: 80 },
      { period: '6m', predictedDemand: Math.round(baseDemand * 1.12), confidence: 75 },
      { period: '12m', predictedDemand: Math.round(baseDemand * 1.25), confidence: 65 }
    ]
  }

  private generateSkillDemandForecast(metrics: MarketMetrics): Array<{ skill: string; currentDemand: number; predictedDemand: number }> {
    return metrics.topSkills.slice(0, 10).map(skill => ({
      skill: skill.skill,
      currentDemand: skill.demand,
      predictedDemand: Math.round(skill.demand * (1 + skill.growth / 100))
    }))
  }

  private generateIndustryGrowthForecast(metrics: MarketMetrics): Array<{ industry: string; currentSize: number; predictedGrowth: number }> {
    return metrics.industryBreakdown.slice(0, 8).map(industry => ({
      industry: industry.industry,
      currentSize: industry.jobCount,
      predictedGrowth: industry.growth
    }))
  }

  private generateLocationTrends(metrics: MarketMetrics): Array<{ location: string; currentJobs: number; predictedGrowth: number }> {
    return metrics.locationDistribution.slice(0, 10).map(location => ({
      location: location.location,
      currentJobs: location.jobCount,
      predictedGrowth: Math.random() * 20 + 5 // Simplified growth prediction
    }))
  }

  private assessAutomationRisk(targetRole?: string): { risk: number; timeframe: string; affectedRoles: string[] } {
    const highRiskRoles = ['data entry', 'basic accounting', 'customer service', 'telemarketing']
    const mediumRiskRoles = ['junior developer', 'content writer', 'analyst', 'coordinator']
    const lowRiskRoles = ['senior engineer', 'manager', 'designer', 'consultant']

    let risk = 30 // Default medium risk

    if (targetRole) {
      const role = targetRole.toLowerCase()
      if (highRiskRoles.some(r => role.includes(r))) risk = 70
      else if (mediumRiskRoles.some(r => role.includes(r))) risk = 40
      else if (lowRiskRoles.some(r => role.includes(r))) risk = 15
    }

    return {
      risk,
      timeframe: risk > 50 ? '3-5 years' : risk > 30 ? '5-10 years' : '10+ years',
      affectedRoles: risk > 50 ? highRiskRoles : risk > 30 ? mediumRiskRoles : []
    }
  }

  private generateCareerMoves(insights: MarketInsights, predictions: MarketPredictions): Array<{ move: string; rationale: string; timeline: string; difficulty: number }> {
    const moves = []

    if (insights.marketHealth === 'STRONG') {
      moves.push({
        move: 'Pursue senior-level positions',
        rationale: 'Strong market conditions favor career advancement',
        timeline: '3-6 months',
        difficulty: 6
      })
    }

    if (insights.emergingSkills.length > 0) {
      moves.push({
        move: `Specialize in ${insights.emergingSkills[0].skill}`,
        rationale: `High growth potential (${insights.emergingSkills[0].growth}% growth)`,
        timeline: '6-12 months',
        difficulty: 4
      })
    }

    return moves.slice(0, 5)
  }

  private generateSkillRecommendations(insights: MarketInsights): Array<{ skill: string; priority: number; learningPath: string; roi: number }> {
    return insights.emergingSkills.slice(0, 8).map((skill, index) => ({
      skill: skill.skill,
      priority: 10 - index,
      learningPath: this.generateLearningPath(skill.skill),
      roi: skill.growth * 2
    }))
  }

  private generateLocationRecommendations(predictions: MarketPredictions): Array<{ location: string; advantages: string[]; salary: number }> {
    return predictions.locationTrends
      .filter(location => location.predictedGrowth > 10)
      .slice(0, 5)
      .map(location => ({
        location: location.location,
        advantages: [
          `${location.predictedGrowth}% job growth predicted`,
          'Growing market opportunity',
          'Competitive salary range'
        ],
        salary: Math.round(location.currentJobs * 1000 + Math.random() * 50000)
      }))
  }

  private generateIndustryTransitions(insights: MarketInsights): Array<{ industry: string; transferability: number; requirements: string[] }> {
    return insights.marketOpportunities.slice(0, 5).map(opportunity => ({
      industry: opportunity.opportunity.split(' ')[0],
      transferability: Math.round(opportunity.potential * 2),
      requirements: ['Relevant experience', 'Industry knowledge', 'Networking']
    }))
  }

  private generateTimingRecommendations(insights: MarketInsights, predictions: MarketPredictions): Array<{ action: string; timing: string; rationale: string }> {
    const recommendations = []

    if (insights.marketHealth === 'STRONG') {
      recommendations.push({
        action: 'Job search or career move',
        timing: 'Now - Next 3 months',
        rationale: 'Strong market conditions provide optimal timing'
      })
    }

    if (predictions.salaryForecast[0].predictedSalary > predictions.salaryForecast[2].predictedSalary) {
      recommendations.push({
        action: 'Salary negotiation',
        timing: 'Immediate',
        rationale: 'Current market conditions favor salary increases'
      })
    }

    return recommendations
  }

  private generateLearningPath(skill: string): string {
    const learningPaths = {
      'JavaScript': 'Online courses → Practice projects → Open source contributions',
      'Python': 'Fundamentals → Data science libraries → Real-world projects',
      'React': 'Component basics → State management → Advanced patterns',
      'AWS': 'Cloud practitioner → Solutions architect → Hands-on labs',
      'Machine Learning': 'Statistics → Algorithms → Practical implementation'
    }

    return learningPaths[skill] || 'Online courses → Practice → Certification → Real-world application'
  }

  // Caching and data management methods
  private generateCacheKey(params: any): string {
    const keyParts = [
      params.analysisType,
      params.targetRole || 'all',
      params.targetLocation || 'all',
      params.targetSkills?.join(',') || 'all',
      params.timeframe || '30d'
    ]
    return `${this.cachePrefix}${keyParts.join(':')}`
  }

  private async getCachedAnalysis(cacheKey: string): Promise<MarketAnalysis | null> {
    try {
      const cached = await redis.get(cacheKey)
      if (cached) {
        const analysis = JSON.parse(cached)
        // Check if not expired
        if (new Date(analysis.expiresAt) > new Date()) {
          return analysis
        }
      }
    } catch (error) {
      logger.error('Error getting cached analysis', { error, cacheKey })
    }
    return null
  }

  private async cacheAnalysis(cacheKey: string, analysis: MarketAnalysis): Promise<void> {
    try {
      await redis.setex(cacheKey, this.cacheTTL, JSON.stringify(analysis))
    } catch (error) {
      logger.error('Error caching analysis', { error, cacheKey })
    }
  }

  private async storeAnalysis(analysis: MarketAnalysis): Promise<void> {
    try {
      await prisma.marketAnalysis.create({
        data: {
          id: analysis.id,
          userId: analysis.userId,
          analysisType: analysis.analysisType,
          targetRole: analysis.targetRole,
          targetLocation: analysis.targetLocation,
          targetSkills: analysis.targetSkills,
          timeframe: analysis.timeframe,
          metrics: analysis.metrics as any,
          insights: analysis.insights as any,
          predictions: analysis.predictions as any,
          recommendations: analysis.recommendations as any,
          confidence: analysis.confidence,
          generatedAt: analysis.generatedAt,
          expiresAt: analysis.expiresAt
        }
      })
    } catch (error) {
      logger.error('Error storing analysis in database', { error, analysisId: analysis.id })
    }
  }

  private calculateConfidenceScore(marketData: any, metrics: MarketMetrics): number {
    let confidence = 50 // Base confidence

    // Data freshness
    const dataAge = Date.now() - new Date(marketData.dataFreshness).getTime()
    const daysOld = dataAge / (1000 * 60 * 60 * 24)
    if (daysOld < 7) confidence += 20
    else if (daysOld < 14) confidence += 10

    // Data volume
    if (metrics.totalJobs > 1000) confidence += 15
    else if (metrics.totalJobs > 500) confidence += 10
    else if (metrics.totalJobs > 100) confidence += 5

    // Data completeness
    const salaryDataCompleteness = metrics.topCompanies.filter(c => c.avgSalary > 0).length / metrics.topCompanies.length
    confidence += Math.round(salaryDataCompleteness * 15)

    return Math.min(confidence, 95) // Cap at 95%
  }

  // Historical data methods (simplified implementations)
  private async getHistoricalData(params: any): Promise<any> {
    // In a real implementation, this would fetch historical job market data
    return {
      skillTrends: new Map(),
      industryTrends: new Map(),
      salaryTrends: []
    }
  }

  private async getUserProfileData(userId: string): Promise<any> {
    try {
      return await prisma.userProfileVector.findFirst({
        where: { userId },
        orderBy: { lastUpdated: 'desc' }
      })
    } catch (error) {
      logger.error('Error fetching user profile data', { error, userId })
      return null
    }
  }

  private async calculateSkillGrowth(skill: string, historicalData: any): Promise<number> {
    // Simplified growth calculation
    return Math.random() * 30 - 5 // -5% to 25% growth
  }

  private async calculateIndustryGrowth(industry: string, historicalData: any): Promise<number> {
    // Simplified industry growth calculation
    return Math.random() * 20 - 2 // -2% to 18% growth
  }

  private async calculateDemandTrends(historicalData: any): Promise<Array<{ date: string; jobCount: number; avgSalary: number }>> {
    // Simplified demand trends
    const trends = []
    const now = new Date()

    for (let i = 29; i >= 0; i--) {
      const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000)
      trends.push({
        date: date.toISOString().split('T')[0],
        jobCount: Math.round(Math.random() * 100 + 50),
        avgSalary: Math.round(Math.random() * 20000 + 70000)
      })
    }

    return trends
  }
}
