import { FullConfig } from '@playwright/test'

async function globalTeardown(config: FullConfig) {
  console.log('🧹 Starting global teardown for E2E tests...')

  try {
    // Cleanup test database if needed
    console.log('🗑️ Cleaning up test environment...')

    // You could cleanup test data here
    // await cleanupTestDatabase()
    
    // Or cleanup mock services
    // await cleanupMockServices()

    // Remove any temporary files
    console.log('📁 Removing temporary files...')
    
    // You could remove auth files or other temp files
    // await fs.unlink('e2e/auth.json').catch(() => {})

    console.log('✅ Global teardown completed successfully')

  } catch (error) {
    console.error('❌ Global teardown failed:', error)
    // Don't throw here as it might mask test failures
  }
}

export default globalTeardown
