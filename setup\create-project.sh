#!/bin/bash
# CareerCraft Local Development - Project Creation Script

echo "🚀 Creating CareerCraft Local Development Project"
echo "================================================"

# Create project directory
PROJECT_NAME="careercraft-local"
echo "📁 Creating project directory: $PROJECT_NAME"

if [ -d "$PROJECT_NAME" ]; then
    echo "⚠️  Directory $PROJECT_NAME already exists. Remove it? (y/n)"
    read -r response
    if [[ "$response" =~ ^[Yy]$ ]]; then
        rm -rf "$PROJECT_NAME"
        echo "🗑️  Removed existing directory"
    else
        echo "❌ Aborting setup"
        exit 1
    fi
fi

mkdir "$PROJECT_NAME"
cd "$PROJECT_NAME"

echo "📦 Initializing Next.js project..."
npx create-next-app@latest . --typescript --tailwind --eslint --app --src-dir --import-alias "@/*"

echo "📦 Installing core dependencies..."

# Core framework dependencies
npm install @prisma/client prisma

# Authentication
npm install next-auth @auth/prisma-adapter

# Payment processing
npm install stripe @stripe/stripe-js

# AI and external APIs
npm install openai

# UI Components
npm install @radix-ui/react-dialog @radix-ui/react-dropdown-menu @radix-ui/react-select
npm install @radix-ui/react-tabs @radix-ui/react-toast @radix-ui/react-tooltip
npm install @radix-ui/react-accordion @radix-ui/react-avatar @radix-ui/react-button
npm install lucide-react

# Forms and validation
npm install @hookform/resolvers react-hook-form zod

# HTTP client and data fetching
npm install axios swr

# Charts and analytics
npm install recharts

# PDF generation
npm install react-pdf @react-pdf/renderer jspdf html2canvas

# Date handling
npm install date-fns

# Utilities
npm install clsx tailwind-merge
npm install class-variance-authority

# Redis client
npm install redis

# Email
npm install nodemailer @types/nodemailer

echo "📦 Installing development dependencies..."

# Development dependencies
npm install -D @types/node @types/react @types/react-dom
npm install -D eslint-config-prettier prettier
npm install -D @typescript-eslint/eslint-plugin @typescript-eslint/parser

# Testing dependencies
npm install -D jest @testing-library/react @testing-library/jest-dom @testing-library/user-event
npm install -D jest-environment-jsdom
npm install -D playwright @playwright/test
npm install -D vitest @vitejs/plugin-react

# Database tools
npm install -D prisma-erd-generator

# Build tools
npm install -D cross-env

echo "🔧 Setting up project structure..."

# Create directory structure
mkdir -p src/components/ui
mkdir -p src/components/forms
mkdir -p src/components/dashboard
mkdir -p src/components/payment
mkdir -p src/components/career
mkdir -p src/lib
mkdir -p src/hooks
mkdir -p src/services
mkdir -p src/types
mkdir -p src/utils
mkdir -p tests/unit
mkdir -p tests/integration
mkdir -p tests/e2e
mkdir -p docs
mkdir -p scripts
mkdir -p public/templates
mkdir -p public/icons

echo "📝 Creating configuration files..."

# Create .env.local template
cat > .env.local << 'EOF'
# Database
DATABASE_URL="postgresql://careercraft_user:local_password@localhost:5432/careercraft_local"

# NextAuth
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-local-secret-key-change-this-in-production"

# Google OAuth (Get from Google Cloud Console)
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"

# GitHub OAuth (Get from GitHub Developer Settings)
GITHUB_ID="your-github-client-id"
GITHUB_SECRET="your-github-client-secret"

# Stripe (Test Mode - Get from Stripe Dashboard)
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY="pk_test_..."
STRIPE_SECRET_KEY="sk_test_..."
STRIPE_WEBHOOK_SECRET="whsec_..."

# OpenAI (Get from OpenAI Platform)
OPENAI_API_KEY="sk-..."

# Redis
REDIS_URL="redis://localhost:6379"

# Email (Local testing)
EMAIL_SERVER="smtp://localhost:1025"
EMAIL_FROM="noreply@localhost"

# App Settings
NODE_ENV="development"
NEXT_PUBLIC_APP_URL="http://localhost:3000"
EOF

# Create .env.example
cp .env.local .env.example
sed -i 's/=.*/=""/g' .env.example

# Create prettier config
cat > .prettierrc << 'EOF'
{
  "semi": false,
  "trailingComma": "es5",
  "singleQuote": true,
  "tabWidth": 2,
  "useTabs": false,
  "printWidth": 80
}
EOF

# Create prettier ignore
cat > .prettierignore << 'EOF'
node_modules
.next
out
dist
build
coverage
.env*
*.log
EOF

# Update package.json scripts
cat > package.json.tmp << 'EOF'
{
  "name": "careercraft-local",
  "version": "0.1.0",
  "private": true,
  "scripts": {
    "dev": "next dev",
    "build": "next build",
    "start": "next start",
    "lint": "next lint",
    "lint:fix": "next lint --fix",
    "format": "prettier --write .",
    "format:check": "prettier --check .",
    "type-check": "tsc --noEmit",
    "db:generate": "prisma generate",
    "db:push": "prisma db push",
    "db:migrate": "prisma migrate dev",
    "db:studio": "prisma studio",
    "db:seed": "tsx prisma/seed.ts",
    "db:reset": "prisma migrate reset",
    "test": "jest",
    "test:watch": "jest --watch",
    "test:coverage": "jest --coverage",
    "test:e2e": "playwright test",
    "test:e2e:ui": "playwright test --ui",
    "stripe:listen": "stripe listen --forward-to localhost:3000/api/webhooks/stripe",
    "dev:all": "concurrently \"npm run dev\" \"npm run stripe:listen\"",
    "setup:db": "npm run db:push && npm run db:seed"
  }
}
EOF

# Merge with existing package.json
node -e "
const fs = require('fs');
const existing = JSON.parse(fs.readFileSync('package.json', 'utf8'));
const template = JSON.parse(fs.readFileSync('package.json.tmp', 'utf8'));
const merged = { ...existing, scripts: { ...existing.scripts, ...template.scripts } };
fs.writeFileSync('package.json', JSON.stringify(merged, null, 2));
"
rm package.json.tmp

# Install additional script dependencies
npm install -D concurrently tsx

echo "✅ Project structure created successfully!"
echo ""
echo "📝 Next steps:"
echo "   1. cd $PROJECT_NAME"
echo "   2. Set up your API keys in .env.local"
echo "   3. Run: npm run setup:db"
echo "   4. Run: npm run dev"
echo ""
echo "🔧 Database setup will be next..."
