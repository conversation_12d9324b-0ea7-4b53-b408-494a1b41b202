/**
 * Version Control Components Unit Tests
 * 
 * Tests for version control React components
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { VersionHistory } from '@/components/version-control/VersionHistory'
import { BackupManager } from '@/components/version-control/BackupManager'

// Mock sonner for toast notifications
vi.mock('sonner', () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn(),
    info: vi.fn()
  }
}))

// Mock date-fns
vi.mock('date-fns', () => ({
  formatDistanceToNow: vi.fn(() => '2 hours ago'),
  format: vi.fn(() => '2024-01-15 10:30 AM')
}))

// Mock fetch for API calls
global.fetch = vi.fn()

describe('Version Control Components', () => {
  const mockVersions = [
    {
      id: 'version-2',
      resumeId: 'resume-1',
      versionNumber: 2,
      versionName: 'Latest Version',
      changeSummary: 'Updated experience section',
      changeType: 'auto',
      createdBy: 'user-1',
      createdAt: new Date('2024-01-15T10:30:00Z'),
      metadata: null,
      creator: {
        id: 'user-1',
        name: 'John Doe',
        image: 'https://example.com/avatar.jpg'
      }
    },
    {
      id: 'version-1',
      resumeId: 'resume-1',
      versionNumber: 1,
      versionName: 'Initial Version',
      changeSummary: 'Created resume',
      changeType: 'manual',
      createdBy: 'user-1',
      createdAt: new Date('2024-01-14T09:00:00Z'),
      metadata: null,
      creator: {
        id: 'user-1',
        name: 'John Doe',
        image: null
      }
    }
  ]

  const mockBackups = [
    {
      id: 'backup-1',
      resumeId: 'resume-1',
      backupName: 'Manual Backup',
      backupType: 'manual',
      createdBy: 'user-1',
      createdAt: new Date('2024-01-15T08:00:00Z'),
      expiresAt: null,
      metadata: null,
      creator: {
        id: 'user-1',
        name: 'John Doe',
        image: null
      }
    },
    {
      id: 'backup-2',
      resumeId: 'resume-1',
      backupName: 'Auto Backup',
      backupType: 'auto',
      createdBy: 'user-1',
      createdAt: new Date('2024-01-14T12:00:00Z'),
      expiresAt: new Date('2024-02-14T12:00:00Z'),
      metadata: null,
      creator: {
        id: 'user-1',
        name: 'John Doe',
        image: null
      }
    }
  ]

  const mockDiff = {
    operations: [
      {
        type: 'modify',
        path: ['personalInfo', 'name'],
        oldValue: 'John Doe',
        newValue: 'Jane Doe'
      },
      {
        type: 'add',
        path: ['experience', '0'],
        newValue: { company: 'New Company', position: 'Developer' }
      }
    ],
    summary: {
      additions: 1,
      deletions: 0,
      modifications: 1,
      moves: 0
    },
    metadata: {
      fromVersion: 1,
      toVersion: 2,
      complexity: 'medium'
    }
  }

  beforeEach(() => {
    vi.clearAllMocks()
    
    ;(global.fetch as any).mockResolvedValue({
      ok: true,
      json: () => Promise.resolve({})
    })
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('VersionHistory', () => {
    it('should render version history list', async () => {
      ;(global.fetch as any).mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({ versions: mockVersions })
      })

      render(<VersionHistory resumeId="resume-1" />)

      await waitFor(() => {
        expect(screen.getByText('Version History')).toBeInTheDocument()
        expect(screen.getByText('Version 2')).toBeInTheDocument()
        expect(screen.getByText('Version 1')).toBeInTheDocument()
        expect(screen.getByText('Latest Version')).toBeInTheDocument()
        expect(screen.getByText('Initial Version')).toBeInTheDocument()
      })
    })

    it('should show loading state initially', () => {
      render(<VersionHistory resumeId="resume-1" />)

      expect(screen.getByText('Version History')).toBeInTheDocument()
      // Should show loading skeletons
      expect(document.querySelectorAll('.animate-pulse')).toHaveLength(3)
    })

    it('should handle version selection', async () => {
      const onVersionSelect = vi.fn()
      
      ;(global.fetch as any).mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({ versions: mockVersions })
      })

      render(<VersionHistory resumeId="resume-1" onVersionSelect={onVersionSelect} />)

      await waitFor(() => {
        expect(screen.getByText('Version 2')).toBeInTheDocument()
      })

      const versionCard = screen.getByText('Version 2').closest('div')
      fireEvent.click(versionCard!)

      expect(onVersionSelect).toHaveBeenCalledWith(mockVersions[0])
    })

    it('should enable compare button when two versions selected', async () => {
      const user = userEvent.setup()
      
      ;(global.fetch as any).mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({ versions: mockVersions })
      })

      render(<VersionHistory resumeId="resume-1" />)

      await waitFor(() => {
        expect(screen.getByText('Version 2')).toBeInTheDocument()
      })

      // Select first version
      const version2Card = screen.getByText('Version 2').closest('div')
      await user.click(version2Card!)

      // Select second version
      const version1Card = screen.getByText('Version 1').closest('div')
      await user.click(version1Card!)

      // Compare button should be visible
      expect(screen.getByText('Compare')).toBeInTheDocument()
    })

    it('should handle version comparison', async () => {
      const user = userEvent.setup()
      
      ;(global.fetch as any)
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ versions: mockVersions })
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ diff: mockDiff })
        })

      render(<VersionHistory resumeId="resume-1" />)

      await waitFor(() => {
        expect(screen.getByText('Version 2')).toBeInTheDocument()
      })

      // Select two versions
      const version2Card = screen.getByText('Version 2').closest('div')
      await user.click(version2Card!)

      const version1Card = screen.getByText('Version 1').closest('div')
      await user.click(version1Card!)

      // Click compare button
      const compareButton = screen.getByText('Compare')
      await user.click(compareButton)

      await waitFor(() => {
        expect(screen.getByText('Version Comparison')).toBeInTheDocument()
        expect(screen.getByText('Comparing version 2 with version 1')).toBeInTheDocument()
      })
    })

    it('should handle rollback preview', async () => {
      const user = userEvent.setup()
      
      ;(global.fetch as any)
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ versions: mockVersions })
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ diff: mockDiff })
        })

      render(<VersionHistory resumeId="resume-1" />)

      await waitFor(() => {
        expect(screen.getByText('Version 1')).toBeInTheDocument()
      })

      // Find rollback button for version 1 (not the current version)
      const version1Card = screen.getByText('Version 1').closest('div')
      const rollbackButton = version1Card!.querySelector('button[title="Rollback to this version"]') ||
                            version1Card!.querySelector('svg[data-testid="rollback-icon"]')?.closest('button')

      if (rollbackButton) {
        await user.click(rollbackButton)

        await waitFor(() => {
          expect(screen.getByText('Confirm Rollback')).toBeInTheDocument()
        })
      }
    })

    it('should handle rollback confirmation', async () => {
      const user = userEvent.setup()
      const { toast } = require('sonner')
      
      ;(global.fetch as any)
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ versions: mockVersions })
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ diff: mockDiff })
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ success: true })
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ versions: mockVersions })
        })

      render(<VersionHistory resumeId="resume-1" />)

      await waitFor(() => {
        expect(screen.getByText('Version 1')).toBeInTheDocument()
      })

      // Simulate rollback flow
      const version1Card = screen.getByText('Version 1').closest('div')
      const rollbackButton = version1Card!.querySelector('button')

      if (rollbackButton) {
        await user.click(rollbackButton)

        await waitFor(() => {
          expect(screen.getByText('Confirm Rollback')).toBeInTheDocument()
        })

        const confirmButton = screen.getByText('Confirm Rollback')
        await user.click(confirmButton)

        await waitFor(() => {
          expect(toast.success).toHaveBeenCalledWith('Successfully rolled back to selected version')
        })
      }
    })

    it('should show empty state when no versions', async () => {
      ;(global.fetch as any).mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({ versions: [] })
      })

      render(<VersionHistory resumeId="resume-1" />)

      await waitFor(() => {
        expect(screen.getByText('No version history available')).toBeInTheDocument()
        expect(screen.getByText('Versions will appear here as you make changes to your resume')).toBeInTheDocument()
      })
    })

    it('should handle API errors', async () => {
      const { toast } = require('sonner')
      
      ;(global.fetch as any).mockResolvedValueOnce({
        ok: false,
        status: 500
      })

      render(<VersionHistory resumeId="resume-1" />)

      await waitFor(() => {
        expect(toast.error).toHaveBeenCalledWith('Failed to load version history')
      })
    })
  })

  describe('BackupManager', () => {
    it('should render backup list', async () => {
      ;(global.fetch as any).mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({ backups: mockBackups })
      })

      render(<BackupManager resumeId="resume-1" />)

      await waitFor(() => {
        expect(screen.getByText('Backup Manager')).toBeInTheDocument()
        expect(screen.getByText('Manual Backup')).toBeInTheDocument()
        expect(screen.getByText('Auto Backup')).toBeInTheDocument()
      })
    })

    it('should show create backup dialog', async () => {
      const user = userEvent.setup()
      
      ;(global.fetch as any).mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({ backups: mockBackups })
      })

      render(<BackupManager resumeId="resume-1" />)

      await waitFor(() => {
        expect(screen.getByText('Create Backup')).toBeInTheDocument()
      })

      const createButton = screen.getByText('Create Backup')
      await user.click(createButton)

      await waitFor(() => {
        expect(screen.getByText('Create New Backup')).toBeInTheDocument()
        expect(screen.getByLabelText('Backup Name')).toBeInTheDocument()
        expect(screen.getByLabelText('Backup Type')).toBeInTheDocument()
      })
    })

    it('should create a new backup', async () => {
      const user = userEvent.setup()
      const { toast } = require('sonner')
      
      ;(global.fetch as any)
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ backups: mockBackups })
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ success: true, backup: mockBackups[0] })
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ backups: mockBackups })
        })

      render(<BackupManager resumeId="resume-1" />)

      await waitFor(() => {
        expect(screen.getByText('Create Backup')).toBeInTheDocument()
      })

      // Open create dialog
      const createButton = screen.getByText('Create Backup')
      await user.click(createButton)

      await waitFor(() => {
        expect(screen.getByLabelText('Backup Name')).toBeInTheDocument()
      })

      // Fill form
      const nameInput = screen.getByLabelText('Backup Name')
      await user.type(nameInput, 'Test Backup')

      // Submit form
      const submitButton = screen.getByRole('button', { name: /create backup/i })
      await user.click(submitButton)

      await waitFor(() => {
        expect(toast.success).toHaveBeenCalledWith('Backup created successfully')
      })
    })

    it('should handle backup restoration', async () => {
      const user = userEvent.setup()
      const { toast } = require('sonner')
      
      ;(global.fetch as any)
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ backups: mockBackups })
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ success: true })
        })

      render(<BackupManager resumeId="resume-1" />)

      await waitFor(() => {
        expect(screen.getByText('Manual Backup')).toBeInTheDocument()
      })

      // Find restore button
      const backupCard = screen.getByText('Manual Backup').closest('div')
      const restoreButton = backupCard!.querySelector('button')

      if (restoreButton) {
        await user.click(restoreButton)

        await waitFor(() => {
          expect(screen.getByText('Confirm Restore')).toBeInTheDocument()
        })

        const confirmButton = screen.getByText('Confirm Restore')
        await user.click(confirmButton)

        await waitFor(() => {
          expect(toast.success).toHaveBeenCalledWith('Backup restored successfully')
        })
      }
    })

    it('should show expired backup warning', async () => {
      const expiredBackup = {
        ...mockBackups[1],
        expiresAt: new Date('2023-01-01T00:00:00Z') // Past date
      }

      ;(global.fetch as any).mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({ backups: [expiredBackup] })
      })

      render(<BackupManager resumeId="resume-1" />)

      await waitFor(() => {
        expect(screen.getByText('Expired')).toBeInTheDocument()
      })
    })

    it('should show empty state when no backups', async () => {
      ;(global.fetch as any).mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({ backups: [] })
      })

      render(<BackupManager resumeId="resume-1" />)

      await waitFor(() => {
        expect(screen.getByText('No backups available')).toBeInTheDocument()
        expect(screen.getByText('Create your first backup to protect your resume data')).toBeInTheDocument()
      })
    })

    it('should validate backup name before creation', async () => {
      const user = userEvent.setup()
      const { toast } = require('sonner')
      
      ;(global.fetch as any).mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({ backups: mockBackups })
      })

      render(<BackupManager resumeId="resume-1" />)

      await waitFor(() => {
        expect(screen.getByText('Create Backup')).toBeInTheDocument()
      })

      // Open create dialog
      const createButton = screen.getByText('Create Backup')
      await user.click(createButton)

      await waitFor(() => {
        expect(screen.getByLabelText('Backup Name')).toBeInTheDocument()
      })

      // Try to submit without name
      const submitButton = screen.getByRole('button', { name: /create backup/i })
      await user.click(submitButton)

      expect(toast.error).toHaveBeenCalledWith('Please enter a backup name')
    })

    it('should handle API errors', async () => {
      const { toast } = require('sonner')
      
      ;(global.fetch as any).mockResolvedValueOnce({
        ok: false,
        status: 500
      })

      render(<BackupManager resumeId="resume-1" />)

      await waitFor(() => {
        expect(toast.error).toHaveBeenCalledWith('Failed to load backups')
      })
    })
  })

  describe('Integration', () => {
    it('should handle network errors gracefully', async () => {
      const { toast } = require('sonner')
      
      ;(global.fetch as any).mockRejectedValue(new Error('Network error'))

      render(<VersionHistory resumeId="resume-1" />)

      await waitFor(() => {
        expect(toast.error).toHaveBeenCalledWith('Failed to load version history')
      })
    })

    it('should handle malformed API responses', async () => {
      const { toast } = require('sonner')
      
      ;(global.fetch as any).mockResolvedValueOnce({
        ok: true,
        json: () => Promise.reject(new Error('Invalid JSON'))
      })

      render(<VersionHistory resumeId="resume-1" />)

      await waitFor(() => {
        expect(toast.error).toHaveBeenCalledWith('Failed to load version history')
      })
    })
  })
})
