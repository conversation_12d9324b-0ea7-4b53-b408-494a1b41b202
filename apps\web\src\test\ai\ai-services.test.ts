/**
 * AI Services Unit Tests
 * 
 * Comprehensive testing for AI-powered features including:
 * - OpenAI integration
 * - Content generation
 * - ATS optimization
 * - Error handling
 * - Rate limiting
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { AIService } from '@/lib/ai/openai'
import type { 
  ContentGenerationRequest, 
  ResumeOptimizationRequest, 
  ATSOptimizationRequest,
  ATSOptimizationResult 
} from '@/lib/ai/openai'

// Mock OpenAI
vi.mock('openai', () => ({
  default: vi.fn().mockImplementation(() => ({
    chat: {
      completions: {
        create: vi.fn()
      }
    }
  }))
}))

describe('AI Services', () => {
  let aiService: AIService
  let mockOpenAI: any

  beforeEach(() => {
    // Reset mocks
    vi.clearAllMocks()
    
    // Get AI service instance
    aiService = AIService.getInstance()
    
    // Mock OpenAI responses
    mockOpenAI = {
      chat: {
        completions: {
          create: vi.fn()
        }
      }
    }
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('Content Generation', () => {
    it('should generate professional summary', async () => {
      // Mock OpenAI response
      const mockResponse = {
        choices: [{
          message: {
            content: 'Experienced software engineer with 5+ years of expertise in full-stack development, specializing in React, Node.js, and cloud technologies. Proven track record of delivering scalable applications and leading cross-functional teams to achieve business objectives.'
          }
        }]
      }

      mockOpenAI.chat.completions.create.mockResolvedValue(mockResponse)

      const request: ContentGenerationRequest = {
        type: 'summary',
        context: {
          role: 'Software Engineer',
          industry: 'Technology',
          experience: '5 years',
          skills: ['React', 'Node.js', 'AWS']
        },
        tone: 'professional'
      }

      const result = await aiService.generateContent(request)

      expect(result).toBeDefined()
      expect(result.length).toBeGreaterThan(50)
      expect(result).toContain('software engineer')
      expect(mockOpenAI.chat.completions.create).toHaveBeenCalledTimes(1)
    })

    it('should generate work experience descriptions', async () => {
      const mockResponse = {
        choices: [{
          message: {
            content: '• Led development of customer-facing web applications serving 100K+ users\n• Implemented microservices architecture reducing system latency by 40%\n• Mentored junior developers and established code review processes'
          }
        }]
      }

      mockOpenAI.chat.completions.create.mockResolvedValue(mockResponse)

      const request: ContentGenerationRequest = {
        type: 'experience',
        context: {
          role: 'Senior Software Engineer',
          company: 'TechCorp',
          achievements: ['Led team of 5 developers', 'Improved performance by 40%']
        },
        tone: 'professional'
      }

      const result = await aiService.generateContent(request)

      expect(result).toBeDefined()
      expect(result).toContain('•')
      expect(result).toContain('Led')
      expect(result).toContain('40%')
    })

    it('should generate cover letters', async () => {
      const mockResponse = {
        choices: [{
          message: {
            content: 'Dear Hiring Manager,\n\nI am writing to express my strong interest in the Senior Software Engineer position at TechCorp. With over 5 years of experience in full-stack development and a proven track record of delivering scalable solutions, I am confident I would be a valuable addition to your team.\n\nSincerely,\nJohn Doe'
          }
        }]
      }

      mockOpenAI.chat.completions.create.mockResolvedValue(mockResponse)

      const request: ContentGenerationRequest = {
        type: 'cover_letter',
        context: {
          role: 'Senior Software Engineer',
          company: 'TechCorp',
          experience: '5 years'
        },
        tone: 'professional'
      }

      const result = await aiService.generateContent(request)

      expect(result).toBeDefined()
      expect(result).toContain('Dear Hiring Manager')
      expect(result).toContain('TechCorp')
      expect(result).toContain('Sincerely')
    })

    it('should handle different content tones', async () => {
      const mockResponse = {
        choices: [{
          message: {
            content: 'Dynamic and innovative software engineer with a passion for cutting-edge technologies and creative problem-solving approaches.'
          }
        }]
      }

      mockOpenAI.chat.completions.create.mockResolvedValue(mockResponse)

      const request: ContentGenerationRequest = {
        type: 'summary',
        context: {
          role: 'Software Engineer',
          industry: 'Technology'
        },
        tone: 'creative'
      }

      const result = await aiService.generateContent(request)

      expect(result).toBeDefined()
      expect(result).toContain('Dynamic')
      expect(result).toContain('innovative')
    })
  })

  describe('Resume Optimization', () => {
    it('should optimize resume content for ATS', async () => {
      const mockResponse = {
        choices: [{
          message: {
            content: 'Experienced Software Engineer with expertise in JavaScript, React, Node.js, and AWS. Proven track record of developing scalable web applications and implementing agile methodologies. Strong background in full-stack development with focus on user experience and performance optimization.'
          }
        }]
      }

      mockOpenAI.chat.completions.create.mockResolvedValue(mockResponse)

      const request: ResumeOptimizationRequest = {
        content: 'I am a developer who knows JavaScript and React.',
        jobDescription: 'Looking for a Software Engineer with React and Node.js experience',
        targetRole: 'Software Engineer',
        industry: 'Technology',
        experienceLevel: 'mid'
      }

      const result = await aiService.optimizeResumeContent(request)

      expect(result).toBeDefined()
      expect(result.length).toBeGreaterThan(request.content.length)
      expect(result).toContain('Software Engineer')
      expect(result).toContain('React')
      expect(result).toContain('Node.js')
    })

    it('should include relevant keywords from job description', async () => {
      const mockResponse = {
        choices: [{
          message: {
            content: 'Senior Software Engineer with expertise in Python, Django, PostgreSQL, and Docker. Experience with agile development, CI/CD pipelines, and cloud deployment on AWS. Strong background in API development and microservices architecture.'
          }
        }]
      }

      mockOpenAI.chat.completions.create.mockResolvedValue(mockResponse)

      const request: ResumeOptimizationRequest = {
        content: 'Python developer with web development experience',
        jobDescription: 'Senior Software Engineer role requiring Python, Django, PostgreSQL, Docker, AWS, and microservices experience',
        targetRole: 'Senior Software Engineer',
        experienceLevel: 'senior'
      }

      const result = await aiService.optimizeResumeContent(request)

      expect(result).toContain('Python')
      expect(result).toContain('Django')
      expect(result).toContain('PostgreSQL')
      expect(result).toContain('Docker')
      expect(result).toContain('AWS')
      expect(result).toContain('microservices')
    })
  })

  describe('ATS Analysis', () => {
    it('should analyze resume for ATS compatibility', async () => {
      const mockAnalysis: ATSOptimizationResult = {
        score: 78,
        missingKeywords: ['agile', 'scrum', 'CI/CD'],
        suggestions: [
          'Add more industry-specific keywords',
          'Include quantifiable achievements',
          'Use stronger action verbs'
        ],
        keywordDensity: {
          'javascript': 0.05,
          'react': 0.03,
          'nodejs': 0.02
        }
      }

      const mockResponse = {
        choices: [{
          message: {
            content: JSON.stringify(mockAnalysis)
          }
        }]
      }

      mockOpenAI.chat.completions.create.mockResolvedValue(mockResponse)

      const request: ATSOptimizationRequest = {
        resumeContent: 'Software engineer with JavaScript and React experience',
        jobDescription: 'Looking for a developer with JavaScript, React, Node.js, agile, and CI/CD experience',
        targetKeywords: ['javascript', 'react', 'nodejs', 'agile', 'cicd']
      }

      const result = await aiService.analyzeATSCompatibility(request)

      expect(result).toBeDefined()
      expect(result.score).toBe(78)
      expect(result.missingKeywords).toContain('agile')
      expect(result.suggestions).toHaveLength(3)
      expect(result.keywordDensity).toHaveProperty('javascript')
    })

    it('should provide keyword suggestions', async () => {
      const mockSuggestions = [
        'full-stack development',
        'responsive design',
        'version control',
        'unit testing',
        'code review'
      ]

      const mockResponse = {
        choices: [{
          message: {
            content: mockSuggestions.join(', ')
          }
        }]
      }

      mockOpenAI.chat.completions.create.mockResolvedValue(mockResponse)

      const result = await aiService.generateKeywordSuggestions(
        'Software Engineer position requiring web development skills',
        'JavaScript developer with React experience'
      )

      expect(result).toBeDefined()
      expect(result.length).toBeGreaterThan(0)
      expect(result).toContain('full-stack')
    })
  })

  describe('Error Handling', () => {
    it('should handle OpenAI API errors gracefully', async () => {
      mockOpenAI.chat.completions.create.mockRejectedValue(new Error('API Error'))

      const request: ContentGenerationRequest = {
        type: 'summary',
        context: { role: 'Developer' },
        tone: 'professional'
      }

      await expect(aiService.generateContent(request)).rejects.toThrow('Failed to generate content')
    })

    it('should handle invalid JSON in ATS analysis', async () => {
      const mockResponse = {
        choices: [{
          message: {
            content: 'Invalid JSON response'
          }
        }]
      }

      mockOpenAI.chat.completions.create.mockResolvedValue(mockResponse)

      const request: ATSOptimizationRequest = {
        resumeContent: 'Test content',
        jobDescription: 'Test job description'
      }

      await expect(aiService.analyzeATSCompatibility(request)).rejects.toThrow('Failed to analyze ATS compatibility')
    })

    it('should handle empty responses', async () => {
      const mockResponse = {
        choices: [{
          message: {
            content: ''
          }
        }]
      }

      mockOpenAI.chat.completions.create.mockResolvedValue(mockResponse)

      const request: ContentGenerationRequest = {
        type: 'summary',
        context: { role: 'Developer' },
        tone: 'professional'
      }

      const result = await aiService.generateContent(request)
      expect(result).toBe('')
    })
  })

  describe('Configuration', () => {
    it('should use correct default model and parameters', () => {
      const instance = AIService.getInstance()
      expect(instance).toBeDefined()
      
      // Test singleton pattern
      const instance2 = AIService.getInstance()
      expect(instance).toBe(instance2)
    })

    it('should handle custom generation options', async () => {
      const mockResponse = {
        choices: [{
          message: {
            content: 'Test response'
          }
        }]
      }

      mockOpenAI.chat.completions.create.mockResolvedValue(mockResponse)

      const request: ContentGenerationRequest = {
        type: 'summary',
        context: { role: 'Developer' },
        tone: 'professional'
      }

      const options = {
        temperature: 0.5,
        maxTokens: 500,
        model: 'gpt-3.5-turbo'
      }

      await aiService.generateContent(request, options)

      expect(mockOpenAI.chat.completions.create).toHaveBeenCalledWith(
        expect.objectContaining({
          model: 'gpt-3.5-turbo',
          temperature: 0.5,
          max_tokens: 500
        })
      )
    })
  })
})
