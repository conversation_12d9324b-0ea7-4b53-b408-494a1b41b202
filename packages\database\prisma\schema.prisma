// Development schema for SQLite
generator client {
  provider = "prisma-client-js"
  output   = "../src/generated"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

// User Management
model User {
  id            String    @id @default(cuid())
  email         String    @unique
  name          String?
  image         String?
  emailVerified DateTime?
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  // Relations
  accounts Account[]
  sessions Session[]
  resumes  Resume[]
  profiles UserProfile[]

  // Advanced Features Relations
  linkedinProfile         LinkedInProfile?
  linkedinImports         LinkedInImport[]
  ownedCollaborationSessions CollaborationSession[]
  collaborationPermissions CollaborationPermission[]
  grantedPermissions      CollaborationPermission[] @relation("PermissionGranter")
  collaborationChanges    CollaborationChange[]
  collaborationComments   CollaborationComment[]
  collaborationCursors    CollaborationCursor[]
  collaborationPresence   CollaborationPresence[]

  // Version Control Relations
  createdVersions         ResumeVersion[]
  createdBackups          ResumeBackup[]
  versionActivities       VersionActivity[]

  // Job Matching Relations
  jobApplications         JobApplication[]
  jobRecommendations      JobRecommendation[]
  jobPreferences          UserJobPreferences?
  skillAssessments        SkillAssessment[]
  interviewPreparations   InterviewPreparation[]

  // Template Sync & Cloud Management Relations
  templateCloudStorage    TemplateCloudStorage[]
  templateVersions        TemplateVersion[]
  templateMarketplace     TemplateMarketplace[]
  templateMarketplaceApprovals TemplateMarketplace[] @relation("TemplateApprover")
  templatePurchases       TemplatePurchase[]
  templateReviews         TemplateReview[]
  templateCollections     TemplateCollection[]
  templateSharesGiven     TemplateShare[] @relation("TemplateSharer")
  templateSharesReceived  TemplateShare[] @relation("TemplateRecipient")
  templateSyncConflicts   TemplateSyncConflict[]
  templateUsageAnalytics  TemplateUsageAnalytics[]

  // Payment & Subscription Relations
  subscription            UserSubscription?
  payments                Payment[]
  featureUsage            FeatureUsage[]
  billingEvents           BillingEvent[]
  couponUsage             CouponUsage[]

  // Career Intelligence Relations - V2.0
  profileVectors          UserProfileVector[]
  marketAnalyses          MarketAnalysis[]
  jobMatches              JobMatch[]

  @@map("users")
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map("accounts")
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
  @@map("verification_tokens")
}

// User Profile for additional information
model UserProfile {
  id          String   @id @default(cuid())
  userId      String   @unique
  firstName   String?
  lastName    String?
  phone       String?
  location    String?
  website     String?
  linkedinUrl String?
  githubUrl   String?
  bio         String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("user_profiles")
}

// Resume Management (Simplified for SQLite)
model Resume {
  id          String   @id @default(cuid())
  userId      String
  title       String
  description String?
  templateId  String?
  isPublic    Boolean  @default(false)
  publicUrl   String?  @unique
  status      String   @default("DRAFT") // DRAFT, PUBLISHED, ARCHIVED
  
  // Simplified JSON fields as TEXT
  personalInfo String? // JSON as string
  sections     String? // JSON as string
  settings     String? // JSON as string
  metadata     String? // JSON as string
  
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  user         User           @relation(fields: [userId], references: [id], onDelete: Cascade)
  template     Template?      @relation(fields: [templateId], references: [id])
  experiences  Experience[]
  educations   Education[]
  skills       Skill[]
  projects     Project[]
  coverLetters CoverLetter[]

  // Advanced Features Relations
  linkedinImports         LinkedInImport[]
  collaborationSessions   CollaborationSession[]

  // Version Control Relations
  versions        ResumeVersion[]
  comparisons     VersionComparison[]
  backups         ResumeBackup[]
  versionActivities VersionActivity[]

  // Job Matching Relations
  jobApplications JobApplication[]

  // Career Intelligence Relations - V2.0
  profileVectors  UserProfileVector[]
  marketAnalyses  MarketAnalysis[]
  jobMatches      JobMatch[]

  @@map("resumes")
}

// Resume Sections (Simplified)
model Experience {
  id           String   @id @default(cuid())
  resumeId     String
  company      String
  position     String
  location     String?
  startDate    DateTime
  endDate      DateTime?
  isCurrent    Boolean  @default(false)
  description  String?
  achievements String?  // JSON as string
  displayOrder Int      @default(0)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  resume Resume @relation(fields: [resumeId], references: [id], onDelete: Cascade)

  @@map("experiences")
}

model Education {
  id           String   @id @default(cuid())
  resumeId     String
  institution  String
  degree       String
  field        String?
  location     String?
  startDate    DateTime
  endDate      DateTime?
  gpa          String?
  description  String?
  displayOrder Int      @default(0)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  resume Resume @relation(fields: [resumeId], references: [id], onDelete: Cascade)

  @@map("educations")
}

model Skill {
  id           String   @id @default(cuid())
  resumeId     String
  name         String
  category     String?   // e.g., "Technical", "Soft Skills", "Languages"
  level        String?   // BEGINNER, INTERMEDIATE, ADVANCED, EXPERT
  displayOrder Int      @default(0)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  resume Resume @relation(fields: [resumeId], references: [id], onDelete: Cascade)

  @@map("skills")
}

model Project {
  id           String   @id @default(cuid())
  resumeId     String
  name         String
  description  String?
  url          String?
  githubUrl    String?
  technologies String?  // JSON as string
  startDate    DateTime?
  endDate      DateTime?
  displayOrder Int      @default(0)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  resume Resume @relation(fields: [resumeId], references: [id], onDelete: Cascade)

  @@map("projects")
}

// Templates (Enhanced for Cloud Sync)
model Template {
  id          String   @id @default(cuid())
  name        String
  description String?
  category    String?
  tags        String?  // JSON array of tags
  isPremium   Boolean  @default(false)
  isActive    Boolean  @default(true)
  isPublic    Boolean  @default(false)
  isOfficial  Boolean  @default(false)
  config      String?  // JSON as string
  preview     String?  // Preview image URL
  thumbnail   String?  // Thumbnail image URL
  difficulty  String?  // beginner, intermediate, advanced
  usageCount  Int      @default(0)
  rating      Float    @default(0.0)
  reviewCount Int      @default(0)
  createdBy   String?  // User ID who created the template
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  resumes              Resume[]
  cloudStorage         TemplateCloudStorage[]
  versions             TemplateVersion[]
  marketplaceListing   TemplateMarketplace?
  reviews              TemplateReview[]
  collectionItems      TemplateCollectionItem[]
  shares               TemplateShare[]
  syncConflicts        TemplateSyncConflict[]
  usageAnalytics       TemplateUsageAnalytics[]

  @@map("templates")
}

// Template Cloud Storage
model TemplateCloudStorage {
  id              String   @id @default(cuid())
  templateId      String
  userId          String
  cloudUrl        String
  storageProvider String   // aws, gcp, azure
  fileSize        BigInt
  checksum        String
  lastSynced      DateTime @default(now())
  syncStatus      String   @default("synced") // synced, pending, error
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  template Template @relation(fields: [templateId], references: [id], onDelete: Cascade)
  user     User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([templateId, userId])
  @@map("template_cloud_storage")
}

// Template Versions
model TemplateVersion {
  id             String   @id @default(cuid())
  templateId     String
  versionNumber  Int
  versionName    String?
  changesSummary String?
  templateData   String   // JSON template data
  createdBy      String?
  createdAt      DateTime @default(now())

  template Template @relation(fields: [templateId], references: [id], onDelete: Cascade)
  creator  User?    @relation(fields: [createdBy], references: [id], onDelete: SetNull)

  @@unique([templateId, versionNumber])
  @@map("template_versions")
}

// Template Marketplace
model TemplateMarketplace {
  id            String    @id @default(cuid())
  templateId    String    @unique
  sellerId      String
  price         Float     @default(0.00)
  isFeatured    Boolean   @default(false)
  downloadCount Int       @default(0)
  status        String    @default("pending") // pending, approved, rejected
  approvedBy    String?
  approvedAt    DateTime?
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  template  Template @relation(fields: [templateId], references: [id], onDelete: Cascade)
  seller    User     @relation(fields: [sellerId], references: [id], onDelete: Cascade)
  approver  User?    @relation("TemplateApprover", fields: [approvedBy], references: [id], onDelete: SetNull)
  purchases TemplatePurchase[]

  @@map("template_marketplace")
}

// Template Purchases
model TemplatePurchase {
  id                   String   @id @default(cuid())
  templateMarketplaceId String
  userId               String
  price                Float
  paymentMethod        String?
  paymentId            String?
  purchasedAt          DateTime @default(now())

  templateMarketplace TemplateMarketplace @relation(fields: [templateMarketplaceId], references: [id], onDelete: Cascade)
  user                User                @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([templateMarketplaceId, userId])
  @@map("template_purchases")
}

// Template Reviews
model TemplateReview {
  id                  String   @id @default(cuid())
  templateId          String
  userId              String
  rating              Int      // 1-5 stars
  reviewText          String?
  isVerifiedPurchase  Boolean  @default(false)
  createdAt           DateTime @default(now())
  updatedAt           DateTime @updatedAt

  template Template @relation(fields: [templateId], references: [id], onDelete: Cascade)
  user     User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([templateId, userId])
  @@map("template_reviews")
}

// Template Collections
model TemplateCollection {
  id          String   @id @default(cuid())
  name        String
  description String?
  createdBy   String
  isPublic    Boolean  @default(false)
  isOfficial  Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  creator User                     @relation(fields: [createdBy], references: [id], onDelete: Cascade)
  items   TemplateCollectionItem[]

  @@map("template_collections")
}

// Template Collection Items
model TemplateCollectionItem {
  id           String   @id @default(cuid())
  collectionId String
  templateId   String
  orderIndex   Int
  addedAt      DateTime @default(now())

  collection TemplateCollection @relation(fields: [collectionId], references: [id], onDelete: Cascade)
  template   Template           @relation(fields: [templateId], references: [id], onDelete: Cascade)

  @@unique([collectionId, templateId])
  @@map("template_collection_items")
}

// Template Sharing
model TemplateShare {
  id              String    @id @default(cuid())
  templateId      String
  sharedBy        String
  sharedWith      String?   // null for public shares
  permissionLevel String    // view, edit, admin
  shareToken      String?   @unique
  expiresAt       DateTime?
  createdAt       DateTime  @default(now())

  template   Template @relation(fields: [templateId], references: [id], onDelete: Cascade)
  sharer     User     @relation("TemplateSharer", fields: [sharedBy], references: [id], onDelete: Cascade)
  recipient  User?    @relation("TemplateRecipient", fields: [sharedWith], references: [id], onDelete: Cascade)

  @@map("template_shares")
}

// Template Sync Conflicts
model TemplateSyncConflict {
  id                 String    @id @default(cuid())
  templateId         String
  userId             String
  conflictType       String    // concurrent_edit, version_mismatch
  localVersion       String    // JSON data
  remoteVersion      String    // JSON data
  resolutionStrategy String?   // manual, auto_merge, prefer_local, prefer_remote
  resolvedAt         DateTime?
  createdAt          DateTime  @default(now())

  template Template @relation(fields: [templateId], references: [id], onDelete: Cascade)
  user     User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("template_sync_conflicts")
}

// Template Usage Analytics
model TemplateUsageAnalytics {
  id         String   @id @default(cuid())
  templateId String
  userId     String?
  actionType String   // view, download, use, customize
  deviceType String?  // desktop, tablet, mobile
  userAgent  String?
  ipAddress  String?
  metadata   String?  // JSON metadata
  createdAt  DateTime @default(now())

  template Template @relation(fields: [templateId], references: [id], onDelete: Cascade)
  user     User?    @relation(fields: [userId], references: [id], onDelete: SetNull)

  @@map("template_usage_analytics")
}

// ============================================================================
// CAREER INTELLIGENCE ENGINE - V2.0
// ============================================================================

// User Profile Vectors for AI Analysis
model UserProfileVector {
  id              String   @id @default(cuid())
  userId          String
  resumeId        String
  profileVector   String   // JSON array representing the vector embedding
  skillsExtracted String   // JSON array of extracted skills
  experienceLevel String?  // ENTRY, MID, SENIOR, EXECUTIVE
  primaryRole     String?  // Primary job title/role
  industries      String?  // JSON array of industries
  locations       String?  // JSON array of preferred locations
  lastUpdated     DateTime @default(now())
  createdAt       DateTime @default(now())

  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  resume Resume @relation(fields: [resumeId], references: [id], onDelete: Cascade)

  @@unique([userId, resumeId])
  @@map("user_profile_vectors")
}

// Note: JobPosting model already exists below - using existing model

// Market Analysis Results
model MarketAnalysis {
  id                    String   @id @default(cuid())
  userId                String
  resumeId              String?
  analysisType          String   // SALARY_ESTIMATE, MARKET_FIT, SKILL_GAP, CAREER_PATH
  salaryEstimateMin     Int?
  salaryEstimateMax     Int?
  confidenceLevel       Float?   // 0.0 to 1.0
  marketFitScore        Float?   // 0.0 to 1.0
  skillGaps             String?  // JSON array of missing skills
  careerOpportunities   String?  // JSON array of suggested roles/paths
  competitiveAnalysis   String?  // JSON object with market positioning
  recommendations       String?  // JSON array of actionable recommendations
  dataPoints            Int?     // Number of job postings analyzed
  lastRefreshed         DateTime @default(now())
  createdAt             DateTime @default(now())

  user   User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  resume Resume? @relation(fields: [resumeId], references: [id], onDelete: SetNull)

  @@index([userId, analysisType])
  @@map("market_analysis")
}

// Job Matching Results - Enhanced for Career Intelligence
model JobMatch {
  id                String   @id @default(cuid())
  userId            String
  resumeId          String
  jobPostingId      String
  compatibilityScore Float   // 0.0 to 1.0
  skillsScore       Float?   // 0.0 to 1.0
  experienceScore   Float?   // 0.0 to 1.0
  locationScore     Float?   // 0.0 to 1.0
  salaryScore       Float?   // 0.0 to 1.0
  matchReasons      String?  // JSON array of why it's a good match
  skillGaps         String?  // JSON array of missing skills for this job
  recommendations   String?  // JSON array of how to improve match
  isBookmarked      Boolean  @default(false)
  isApplied         Boolean  @default(false)
  createdAt         DateTime @default(now())

  user       User       @relation(fields: [userId], references: [id], onDelete: Cascade)
  resume     Resume     @relation(fields: [resumeId], references: [id], onDelete: Cascade)
  jobPosting JobPosting @relation(fields: [jobPostingId], references: [id], onDelete: Cascade)

  @@unique([userId, resumeId, jobPostingId])
  @@index([userId, compatibilityScore])
  @@map("job_matches")
}

// ============================================================================
// PAYMENT & SUBSCRIPTION SYSTEM
// ============================================================================

// Subscription Plans
model SubscriptionPlan {
  id                    String   @id @default(cuid())
  name                  String   @unique
  description           String?
  priceMonthly          Float
  priceYearly           Float?
  stripePriceIdMonthly  String?  @unique
  stripePriceIdYearly   String?  @unique
  features              String   // JSON object defining feature access
  maxResumes            Int      @default(-1) // -1 for unlimited
  maxTemplates          Int      @default(-1) // -1 for unlimited
  maxCollaborators      Int      @default(-1) // -1 for unlimited
  aiSuggestionsLimit    Int      @default(-1) // -1 for unlimited
  isActive              Boolean  @default(true)
  sortOrder             Int      @default(0)
  createdAt             DateTime @default(now())
  updatedAt             DateTime @updatedAt

  // Relations
  subscriptions UserSubscription[]

  @@map("subscription_plans")
}

// User Subscriptions
model UserSubscription {
  id                    String    @id @default(cuid())
  userId                String    @unique
  planId                String
  stripeCustomerId      String?   @unique
  stripeSubscriptionId  String?   @unique
  status                String    // active, canceled, past_due, unpaid, trialing
  currentPeriodStart    DateTime?
  currentPeriodEnd      DateTime?
  cancelAtPeriodEnd     Boolean   @default(false)
  canceledAt            DateTime?
  trialStart            DateTime?
  trialEnd              DateTime?
  metadata              String?   // JSON for additional data
  createdAt             DateTime  @default(now())
  updatedAt             DateTime  @updatedAt

  // Relations
  user     User             @relation(fields: [userId], references: [id], onDelete: Cascade)
  plan     SubscriptionPlan @relation(fields: [planId], references: [id])
  payments Payment[]

  @@map("user_subscriptions")
}

// Payment History
model Payment {
  id                      String    @id @default(cuid())
  userId                  String
  subscriptionId          String?
  stripePaymentIntentId   String?   @unique
  amount                  Float
  currency                String    @default("USD")
  status                  String    // succeeded, failed, pending, canceled
  paymentMethod           String?   // card, bank_transfer, etc.
  description             String?
  metadata                String?   // JSON for additional data
  createdAt               DateTime  @default(now())

  // Relations
  user         User              @relation(fields: [userId], references: [id], onDelete: Cascade)
  subscription UserSubscription? @relation(fields: [subscriptionId], references: [id])

  @@map("payments")
}

// Feature Usage Tracking
model FeatureUsage {
  id           String   @id @default(cuid())
  userId       String
  featureName  String
  usageCount   Int      @default(1)
  usageDate    DateTime @default(now())
  metadata     String?  // JSON for additional data
  createdAt    DateTime @default(now())

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, featureName, usageDate])
  @@map("feature_usage")
}

// Billing Events (Webhook Processing)
model BillingEvent {
  id            String   @id @default(cuid())
  userId        String?
  eventType     String   // subscription_created, payment_succeeded, etc.
  stripeEventId String?  @unique
  data          String   // JSON event data
  processed     Boolean  @default(false)
  createdAt     DateTime @default(now())

  // Relations
  user User? @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("billing_events")
}

// Coupons & Discounts
model Coupon {
  id                  String    @id @default(cuid())
  code                String    @unique
  name                String
  description         String?
  discountType        String    // percentage, fixed_amount
  discountValue       Float
  stripeCouponId      String?   @unique
  maxRedemptions      Int?
  currentRedemptions  Int       @default(0)
  validFrom           DateTime  @default(now())
  validUntil          DateTime?
  isActive            Boolean   @default(true)
  createdAt           DateTime  @default(now())
  updatedAt           DateTime  @updatedAt

  // Relations
  usage CouponUsage[]

  @@map("coupons")
}

// Coupon Usage Tracking
model CouponUsage {
  id             String    @id @default(cuid())
  couponId       String
  userId         String
  subscriptionId String?
  usedAt         DateTime  @default(now())

  // Relations
  coupon Coupon @relation(fields: [couponId], references: [id], onDelete: Cascade)
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([couponId, userId])
  @@map("coupon_usage")
}

// Cover Letters (Simplified)
model CoverLetter {
  id          String   @id @default(cuid())
  resumeId    String?
  title       String
  content     String
  jobTitle    String?
  company     String?
  jobDescription String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  resume Resume? @relation(fields: [resumeId], references: [id], onDelete: SetNull)

  // Job Matching Relations
  jobApplications JobApplication[]

  @@map("cover_letters")
}

// AI Generation History (Simplified)
model AIGeneration {
  id        String   @id @default(cuid())
  userId    String
  type      String   // BULLET_POINT, SUMMARY, COVER_LETTER, REWRITE, KEYWORDS
  prompt    String
  response  String
  metadata  String?  // JSON as string
  createdAt DateTime @default(now())

  @@map("ai_generations")
}

// LinkedIn Integration
model LinkedInProfile {
  id          String   @id @default(cuid())
  userId      String   @unique
  linkedinId  String   @unique
  profileData String   // JSON as string
  lastSynced  DateTime @default(now())
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  user    User              @relation(fields: [userId], references: [id], onDelete: Cascade)
  imports LinkedInImport[]

  @@map("linkedin_profiles")
}

model LinkedInImport {
  id           String   @id @default(cuid())
  userId       String
  resumeId     String?
  profileId    String
  importedData String   // JSON as string
  importStatus String   @default("pending") // pending, completed, failed
  createdAt    DateTime @default(now())

  user    User             @relation(fields: [userId], references: [id], onDelete: Cascade)
  resume  Resume?          @relation(fields: [resumeId], references: [id], onDelete: SetNull)
  profile LinkedInProfile  @relation(fields: [profileId], references: [id], onDelete: Cascade)

  @@map("linkedin_imports")
}

// Real-time Collaboration
model CollaborationSession {
  id           String   @id @default(cuid())
  resumeId     String
  ownerId      String
  sessionToken String   @unique
  expiresAt    DateTime
  createdAt    DateTime @default(now())

  resume      Resume                     @relation(fields: [resumeId], references: [id], onDelete: Cascade)
  owner       User                       @relation(fields: [ownerId], references: [id], onDelete: Cascade)
  permissions CollaborationPermission[]
  changes     CollaborationChange[]
  comments    CollaborationComment[]
  cursors     CollaborationCursor[]
  presence    CollaborationPresence[]

  @@map("collaboration_sessions")
}

model CollaborationPermission {
  id              String   @id @default(cuid())
  sessionId       String
  userId          String
  permissionLevel String   @default("view") // view, edit, admin
  grantedBy       String?
  createdAt       DateTime @default(now())

  session       CollaborationSession @relation(fields: [sessionId], references: [id], onDelete: Cascade)
  user          User                 @relation(fields: [userId], references: [id], onDelete: Cascade)
  grantedByUser User?                @relation("PermissionGranter", fields: [grantedBy], references: [id])

  @@unique([sessionId, userId])
  @@map("collaboration_permissions")
}

model CollaborationChange {
  id         String   @id @default(cuid())
  sessionId  String
  userId     String
  changeType String
  changeData String   // JSON as string
  timestamp  DateTime @default(now())

  session CollaborationSession @relation(fields: [sessionId], references: [id], onDelete: Cascade)
  user    User                 @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("collaboration_changes")
}

// Collaboration Comments
model CollaborationComment {
  id          String   @id @default(cuid())
  sessionId   String
  userId      String
  parentId    String?
  sectionPath String
  content     String
  isResolved  Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  session CollaborationSession    @relation(fields: [sessionId], references: [id], onDelete: Cascade)
  user    User                    @relation(fields: [userId], references: [id], onDelete: Cascade)
  parent  CollaborationComment?   @relation("CommentReplies", fields: [parentId], references: [id], onDelete: Cascade)
  replies CollaborationComment[]  @relation("CommentReplies")

  @@map("collaboration_comments")
}

// Collaboration Cursors
model CollaborationCursor {
  id          String   @id @default(cuid())
  sessionId   String
  userId      String
  sectionPath String
  position    Int
  updatedAt   DateTime @updatedAt

  session CollaborationSession @relation(fields: [sessionId], references: [id], onDelete: Cascade)
  user    User                 @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([sessionId, userId])
  @@map("collaboration_cursors")
}

// Collaboration Presence
model CollaborationPresence {
  id        String   @id @default(cuid())
  sessionId String
  userId    String
  status    String   @default("active") // active, idle, away
  lastSeen  DateTime @default(now())

  session CollaborationSession @relation(fields: [sessionId], references: [id], onDelete: Cascade)
  user    User                 @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([sessionId, userId])
  @@map("collaboration_presence")
}

// Resume Version Control
model ResumeVersion {
  id              String   @id @default(cuid())
  resumeId        String
  versionNumber   Int
  versionName     String?
  contentSnapshot String   // JSON as string
  changeSummary   String?
  changeType      String   @default("auto") // auto, manual, rollback
  createdBy       String
  createdAt       DateTime @default(now())
  metadata        String?  // JSON as string

  resume     Resume              @relation(fields: [resumeId], references: [id], onDelete: Cascade)
  creator    User                @relation(fields: [createdBy], references: [id], onDelete: Cascade)
  activities VersionActivity[]

  @@unique([resumeId, versionNumber])
  @@map("resume_versions")
}

// Version Comparisons (cached)
model VersionComparison {
  id          String   @id @default(cuid())
  resumeId    String
  versionFrom Int
  versionTo   Int
  diffData    String   // JSON as string
  createdAt   DateTime @default(now())

  resume      Resume        @relation(fields: [resumeId], references: [id], onDelete: Cascade)

  @@unique([resumeId, versionFrom, versionTo])
  @@map("version_comparisons")
}

// Resume Backups
model ResumeBackup {
  id         String    @id @default(cuid())
  resumeId   String
  backupName String?
  backupData String    // JSON as string
  backupType String    @default("manual") // manual, auto, scheduled
  createdBy  String
  createdAt  DateTime  @default(now())
  expiresAt  DateTime?
  metadata   String?   // JSON as string

  resume  Resume @relation(fields: [resumeId], references: [id], onDelete: Cascade)
  creator User   @relation(fields: [createdBy], references: [id], onDelete: Cascade)

  @@map("resume_backups")
}

// Version Activities
model VersionActivity {
  id           String   @id @default(cuid())
  resumeId     String
  versionId    String
  activityType String   // created, restored, compared, deleted
  userId       String
  activityData String?  // JSON as string
  createdAt    DateTime @default(now())

  resume  Resume        @relation(fields: [resumeId], references: [id], onDelete: Cascade)
  version ResumeVersion @relation(fields: [versionId], references: [id], onDelete: Cascade)
  user    User          @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("version_activities")
}

// Job Matching & AI Recommendations
model JobPosting {
  id              String    @id @default(cuid())
  externalId      String?   @unique
  title           String
  company         String
  description     String
  requirements    String?
  location        String?
  salaryMin       Int?
  salaryMax       Int?
  employmentType  String?   // full-time, part-time, contract, internship
  remoteType      String?   // remote, hybrid, on-site
  experienceLevel String?   // entry, mid, senior, executive
  skills          String?   // JSON as string
  benefits        String?   // JSON as string
  postedDate      DateTime?
  expiresDate     DateTime?
  source          String?   // linkedin, indeed, glassdoor, etc.
  sourceUrl       String?
  isActive        Boolean   @default(true)
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt

  // Relations
  applications    JobApplication[]
  recommendations JobRecommendation[]
  jobMatches      JobMatch[]  // Career Intelligence matches

  @@map("job_postings")
}

model JobApplication {
  id              String    @id @default(cuid())
  userId          String
  jobPostingId    String
  resumeId        String?
  coverLetterId   String?
  status          String    @default("applied") // applied, screening, interview, offer, rejected, withdrawn
  appliedDate     DateTime  @default(now())
  lastUpdated     DateTime  @default(now())
  notes           String?
  interviewDates  String?   // JSON as string
  followUpDate    DateTime?
  salaryOffered   Int?
  metadata        String?   // JSON as string
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt

  // Relations
  user         User         @relation(fields: [userId], references: [id], onDelete: Cascade)
  jobPosting   JobPosting   @relation(fields: [jobPostingId], references: [id], onDelete: Cascade)
  resume       Resume?      @relation(fields: [resumeId], references: [id], onDelete: SetNull)
  coverLetter  CoverLetter? @relation(fields: [coverLetterId], references: [id], onDelete: SetNull)
  preparations InterviewPreparation[]

  @@map("job_applications")
}

model JobRecommendation {
  id            String    @id @default(cuid())
  userId        String
  jobPostingId  String
  matchScore    Float
  reasoning     String?   // JSON as string
  isViewed      Boolean   @default(false)
  isSaved       Boolean   @default(false)
  isDismissed   Boolean   @default(false)
  recommendedAt DateTime  @default(now())
  viewedAt      DateTime?
  createdAt     DateTime  @default(now())

  // Relations
  user       User       @relation(fields: [userId], references: [id], onDelete: Cascade)
  jobPosting JobPosting @relation(fields: [jobPostingId], references: [id], onDelete: Cascade)

  @@unique([userId, jobPostingId])
  @@map("job_recommendations")
}

model UserJobPreferences {
  id                      String   @id @default(cuid())
  userId                  String   @unique
  preferredTitles         String?  // JSON as string
  preferredCompanies      String?  // JSON as string
  preferredLocations      String?  // JSON as string
  salaryMin               Int?
  salaryMax               Int?
  employmentTypes         String?  // JSON as string
  remotePreferences       String?  // JSON as string
  experienceLevel         String?
  industryPreferences     String?  // JSON as string
  companySizePreferences  String?  // JSON as string
  notificationPreferences String?  // JSON as string
  createdAt               DateTime @default(now())
  updatedAt               DateTime @updatedAt

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("user_job_preferences")
}

model SkillAssessment {
  id                 String    @id @default(cuid())
  userId             String
  skillName          String
  proficiencyLevel   String?   // beginner, intermediate, advanced, expert
  assessmentScore    Int?
  assessmentDate     DateTime  @default(now())
  verified           Boolean   @default(false)
  verificationSource String?
  createdAt          DateTime  @default(now())
  updatedAt          DateTime  @updatedAt

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("skill_assessments")
}

model InterviewPreparation {
  id                   String    @id @default(cuid())
  userId               String
  jobApplicationId     String
  interviewType        String?   // phone, video, in-person, technical
  scheduledDate        DateTime?
  preparationNotes     String?
  questionsPracticed   String?   // JSON as string
  mockInterviewScores  String?   // JSON as string
  feedback             String?   // JSON as string
  createdAt            DateTime  @default(now())
  updatedAt            DateTime  @updatedAt

  // Relations
  user           User           @relation(fields: [userId], references: [id], onDelete: Cascade)
  jobApplication JobApplication @relation(fields: [jobApplicationId], references: [id], onDelete: Cascade)

  @@map("interview_preparations")
}
