/**
 * Stripe Webhooks Integration Tests
 * 
 * Comprehensive test suite for Stripe webhook processing,
 * event handling, and database synchronization.
 */

import { describe, it, expect, beforeEach, afterEach, beforeAll, afterAll } from 'vitest'
import request from 'supertest'
import crypto from 'crypto'
import { app } from '../../app'
import { DatabaseService } from '../../services/database/database-service'
import { setupTestDatabase, cleanupTestDatabase } from '../helpers/database-helper'

describe('Stripe Webhooks Integration', () => {
  let testDb: DatabaseService
  let webhookSecret: string

  beforeAll(async () => {
    testDb = await setupTestDatabase()
    webhookSecret = process.env.STRIPE_WEBHOOK_SECRET || 'whsec_test_secret'
  })

  afterAll(async () => {
    await cleanupTestDatabase(testDb)
  })

  beforeEach(async () => {
    // Clean up test data before each test
    await testDb.payments.deleteAll()
    await testDb.subscriptions.deleteAll()
    await testDb.users.deleteAll()
  })

  describe('Webhook Signature Verification', () => {
    it('should reject webhook with invalid signature', async () => {
      const payload = JSON.stringify({
        type: 'payment_intent.succeeded',
        data: { object: { id: 'pi_test' } }
      })

      const invalidSignature = 'invalid_signature'

      const response = await request(app)
        .post('/api/webhooks/stripe')
        .set('stripe-signature', invalidSignature)
        .send(payload)
        .expect(400)

      expect(response.body.error).toContain('Invalid signature')
    })

    it('should accept webhook with valid signature', async () => {
      const payload = JSON.stringify({
        type: 'payment_intent.succeeded',
        data: {
          object: {
            id: 'pi_test_123',
            status: 'succeeded',
            amount: 999,
            currency: 'usd'
          }
        }
      })

      const signature = generateValidSignature(payload, webhookSecret)

      const response = await request(app)
        .post('/api/webhooks/stripe')
        .set('stripe-signature', signature)
        .send(payload)
        .expect(200)

      expect(response.body.received).toBe(true)
    })
  })

  describe('Payment Intent Events', () => {
    it('should handle payment_intent.succeeded event', async () => {
      // Create test user and subscription
      const user = await testDb.users.create({
        id: 'user-123',
        email: '<EMAIL>',
        stripeCustomerId: 'cus_test_123'
      })

      const subscription = await testDb.subscriptions.create({
        id: 'sub-123',
        userId: user.id,
        stripeSubscriptionId: 'sub_stripe_123',
        planId: 'premium-monthly',
        status: 'active'
      })

      const payload = JSON.stringify({
        type: 'payment_intent.succeeded',
        data: {
          object: {
            id: 'pi_test_123',
            status: 'succeeded',
            amount: 999,
            currency: 'usd',
            customer: 'cus_test_123',
            metadata: {
              subscription_id: subscription.id
            }
          }
        }
      })

      const signature = generateValidSignature(payload, webhookSecret)

      await request(app)
        .post('/api/webhooks/stripe')
        .set('stripe-signature', signature)
        .send(payload)
        .expect(200)

      // Verify payment record was created
      const payment = await testDb.payments.findByStripeId('pi_test_123')
      expect(payment).toBeDefined()
      expect(payment.status).toBe('succeeded')
      expect(payment.amount).toBe(999)
      expect(payment.userId).toBe(user.id)
    })

    it('should handle payment_intent.payment_failed event', async () => {
      const user = await testDb.users.create({
        id: 'user-123',
        email: '<EMAIL>',
        stripeCustomerId: 'cus_test_123'
      })

      const payload = JSON.stringify({
        type: 'payment_intent.payment_failed',
        data: {
          object: {
            id: 'pi_test_failed',
            status: 'requires_payment_method',
            amount: 999,
            currency: 'usd',
            customer: 'cus_test_123',
            last_payment_error: {
              message: 'Your card was declined.',
              decline_code: 'generic_decline'
            }
          }
        }
      })

      const signature = generateValidSignature(payload, webhookSecret)

      await request(app)
        .post('/api/webhooks/stripe')
        .set('stripe-signature', signature)
        .send(payload)
        .expect(200)

      // Verify payment failure was recorded
      const payment = await testDb.payments.findByStripeId('pi_test_failed')
      expect(payment).toBeDefined()
      expect(payment.status).toBe('failed')
      expect(payment.errorMessage).toContain('card was declined')
    })
  })

  describe('Subscription Events', () => {
    it('should handle customer.subscription.created event', async () => {
      const user = await testDb.users.create({
        id: 'user-123',
        email: '<EMAIL>',
        stripeCustomerId: 'cus_test_123'
      })

      const payload = JSON.stringify({
        type: 'customer.subscription.created',
        data: {
          object: {
            id: 'sub_stripe_new',
            customer: 'cus_test_123',
            status: 'active',
            current_period_start: 1640995200,
            current_period_end: 1643673600,
            items: {
              data: [{
                price: {
                  id: 'price_premium_monthly',
                  recurring: { interval: 'month' }
                }
              }]
            },
            metadata: {
              plan_id: 'premium-monthly'
            }
          }
        }
      })

      const signature = generateValidSignature(payload, webhookSecret)

      await request(app)
        .post('/api/webhooks/stripe')
        .set('stripe-signature', signature)
        .send(payload)
        .expect(200)

      // Verify subscription was created in database
      const subscription = await testDb.subscriptions.findByStripeId('sub_stripe_new')
      expect(subscription).toBeDefined()
      expect(subscription.userId).toBe(user.id)
      expect(subscription.status).toBe('active')
      expect(subscription.planId).toBe('premium-monthly')
    })

    it('should handle customer.subscription.updated event', async () => {
      const user = await testDb.users.create({
        id: 'user-123',
        email: '<EMAIL>',
        stripeCustomerId: 'cus_test_123'
      })

      const subscription = await testDb.subscriptions.create({
        id: 'sub-123',
        userId: user.id,
        stripeSubscriptionId: 'sub_stripe_123',
        planId: 'premium-monthly',
        status: 'active'
      })

      const payload = JSON.stringify({
        type: 'customer.subscription.updated',
        data: {
          object: {
            id: 'sub_stripe_123',
            customer: 'cus_test_123',
            status: 'active',
            current_period_start: 1640995200,
            current_period_end: 1643673600,
            items: {
              data: [{
                price: {
                  id: 'price_enterprise_monthly'
                }
              }]
            },
            metadata: {
              plan_id: 'enterprise-monthly'
            }
          }
        }
      })

      const signature = generateValidSignature(payload, webhookSecret)

      await request(app)
        .post('/api/webhooks/stripe')
        .set('stripe-signature', signature)
        .send(payload)
        .expect(200)

      // Verify subscription was updated
      const updatedSubscription = await testDb.subscriptions.findById(subscription.id)
      expect(updatedSubscription.planId).toBe('enterprise-monthly')
    })

    it('should handle customer.subscription.deleted event', async () => {
      const user = await testDb.users.create({
        id: 'user-123',
        email: '<EMAIL>',
        stripeCustomerId: 'cus_test_123'
      })

      const subscription = await testDb.subscriptions.create({
        id: 'sub-123',
        userId: user.id,
        stripeSubscriptionId: 'sub_stripe_123',
        planId: 'premium-monthly',
        status: 'active'
      })

      const payload = JSON.stringify({
        type: 'customer.subscription.deleted',
        data: {
          object: {
            id: 'sub_stripe_123',
            customer: 'cus_test_123',
            status: 'canceled',
            canceled_at: Math.floor(Date.now() / 1000)
          }
        }
      })

      const signature = generateValidSignature(payload, webhookSecret)

      await request(app)
        .post('/api/webhooks/stripe')
        .set('stripe-signature', signature)
        .send(payload)
        .expect(200)

      // Verify subscription was canceled
      const canceledSubscription = await testDb.subscriptions.findById(subscription.id)
      expect(canceledSubscription.status).toBe('canceled')
      expect(canceledSubscription.canceledAt).toBeDefined()
    })
  })

  describe('Invoice Events', () => {
    it('should handle invoice.payment_succeeded event', async () => {
      const user = await testDb.users.create({
        id: 'user-123',
        email: '<EMAIL>',
        stripeCustomerId: 'cus_test_123'
      })

      const subscription = await testDb.subscriptions.create({
        id: 'sub-123',
        userId: user.id,
        stripeSubscriptionId: 'sub_stripe_123',
        planId: 'premium-monthly',
        status: 'active'
      })

      const payload = JSON.stringify({
        type: 'invoice.payment_succeeded',
        data: {
          object: {
            id: 'in_test_123',
            customer: 'cus_test_123',
            subscription: 'sub_stripe_123',
            amount_paid: 999,
            currency: 'usd',
            status: 'paid',
            hosted_invoice_url: 'https://invoice.stripe.com/test',
            invoice_pdf: 'https://invoice.stripe.com/test.pdf'
          }
        }
      })

      const signature = generateValidSignature(payload, webhookSecret)

      await request(app)
        .post('/api/webhooks/stripe')
        .set('stripe-signature', signature)
        .send(payload)
        .expect(200)

      // Verify invoice was recorded
      const invoice = await testDb.invoices.findByStripeId('in_test_123')
      expect(invoice).toBeDefined()
      expect(invoice.status).toBe('paid')
      expect(invoice.amount).toBe(999)
      expect(invoice.userId).toBe(user.id)
    })

    it('should handle invoice.payment_failed event', async () => {
      const user = await testDb.users.create({
        id: 'user-123',
        email: '<EMAIL>',
        stripeCustomerId: 'cus_test_123'
      })

      const subscription = await testDb.subscriptions.create({
        id: 'sub-123',
        userId: user.id,
        stripeSubscriptionId: 'sub_stripe_123',
        planId: 'premium-monthly',
        status: 'active'
      })

      const payload = JSON.stringify({
        type: 'invoice.payment_failed',
        data: {
          object: {
            id: 'in_test_failed',
            customer: 'cus_test_123',
            subscription: 'sub_stripe_123',
            amount_due: 999,
            currency: 'usd',
            status: 'open',
            attempt_count: 1
          }
        }
      })

      const signature = generateValidSignature(payload, webhookSecret)

      await request(app)
        .post('/api/webhooks/stripe')
        .set('stripe-signature', signature)
        .send(payload)
        .expect(200)

      // Verify subscription status was updated
      const updatedSubscription = await testDb.subscriptions.findById(subscription.id)
      expect(updatedSubscription.status).toBe('past_due')

      // Verify invoice was recorded
      const invoice = await testDb.invoices.findByStripeId('in_test_failed')
      expect(invoice.status).toBe('open')
    })
  })

  describe('Customer Events', () => {
    it('should handle customer.updated event', async () => {
      const user = await testDb.users.create({
        id: 'user-123',
        email: '<EMAIL>',
        stripeCustomerId: 'cus_test_123'
      })

      const payload = JSON.stringify({
        type: 'customer.updated',
        data: {
          object: {
            id: 'cus_test_123',
            email: '<EMAIL>',
            name: 'Updated Name',
            metadata: {
              user_id: user.id
            }
          }
        }
      })

      const signature = generateValidSignature(payload, webhookSecret)

      await request(app)
        .post('/api/webhooks/stripe')
        .set('stripe-signature', signature)
        .send(payload)
        .expect(200)

      // Verify user was updated
      const updatedUser = await testDb.users.findById(user.id)
      expect(updatedUser.email).toBe('<EMAIL>')
      expect(updatedUser.name).toBe('Updated Name')
    })
  })

  describe('Idempotency', () => {
    it('should handle duplicate webhook events', async () => {
      const user = await testDb.users.create({
        id: 'user-123',
        email: '<EMAIL>',
        stripeCustomerId: 'cus_test_123'
      })

      const payload = JSON.stringify({
        id: 'evt_test_duplicate',
        type: 'payment_intent.succeeded',
        data: {
          object: {
            id: 'pi_test_duplicate',
            status: 'succeeded',
            amount: 999,
            currency: 'usd',
            customer: 'cus_test_123'
          }
        }
      })

      const signature = generateValidSignature(payload, webhookSecret)

      // Send the same webhook twice
      await request(app)
        .post('/api/webhooks/stripe')
        .set('stripe-signature', signature)
        .send(payload)
        .expect(200)

      await request(app)
        .post('/api/webhooks/stripe')
        .set('stripe-signature', signature)
        .send(payload)
        .expect(200)

      // Verify only one payment record was created
      const payments = await testDb.payments.findByStripeId('pi_test_duplicate')
      expect(payments).toHaveLength(1)
    })
  })

  describe('Error Handling', () => {
    it('should handle malformed webhook payload', async () => {
      const invalidPayload = 'invalid json'
      const signature = generateValidSignature(invalidPayload, webhookSecret)

      const response = await request(app)
        .post('/api/webhooks/stripe')
        .set('stripe-signature', signature)
        .send(invalidPayload)
        .expect(400)

      expect(response.body.error).toContain('Invalid payload')
    })

    it('should handle unknown event types gracefully', async () => {
      const payload = JSON.stringify({
        type: 'unknown.event.type',
        data: {
          object: {
            id: 'unknown_object'
          }
        }
      })

      const signature = generateValidSignature(payload, webhookSecret)

      const response = await request(app)
        .post('/api/webhooks/stripe')
        .set('stripe-signature', signature)
        .send(payload)
        .expect(200)

      expect(response.body.received).toBe(true)
      expect(response.body.processed).toBe(false)
    })
  })
})

/**
 * Generate valid Stripe webhook signature for testing
 */
function generateValidSignature(payload: string, secret: string): string {
  const timestamp = Math.floor(Date.now() / 1000)
  const signedPayload = `${timestamp}.${payload}`
  const signature = crypto
    .createHmac('sha256', secret)
    .update(signedPayload)
    .digest('hex')
  
  return `t=${timestamp},v1=${signature}`
}
