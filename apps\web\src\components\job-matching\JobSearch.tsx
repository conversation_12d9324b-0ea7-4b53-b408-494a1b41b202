'use client'

import { useState, useEffect } from 'react'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Separator } from '@/components/ui/separator'
import { Slider } from '@/components/ui/slider'
import { 
  Search, 
  MapPin, 
  DollarSign, 
  Clock, 
  Building, 
  Star,
  Bookmark,
  ExternalLink,
  Filter,
  SortAsc,
  Heart,
  TrendingUp
} from 'lucide-react'
import { formatDistanceToNow } from 'date-fns'
import { toast } from 'sonner'

interface JobPosting {
  id: string
  title: string
  company: string
  description: string
  location?: string
  salaryMin?: number
  salaryMax?: number
  employmentType?: string
  remoteType?: string
  experienceLevel?: string
  skills?: string[]
  postedDate?: Date
  sourceUrl?: string
  isActive: boolean
}

interface JobSearchProps {
  className?: string
  onJobSelect?: (job: JobPosting) => void
}

export function JobSearch({ className, onJobSelect }: JobSearchProps) {
  const [jobs, setJobs] = useState<JobPosting[]>([])
  const [loading, setLoading] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const [location, setLocation] = useState('')
  const [remoteType, setRemoteType] = useState<string>('')
  const [employmentType, setEmploymentType] = useState<string>('')
  const [experienceLevel, setExperienceLevel] = useState<string>('')
  const [salaryRange, setSalaryRange] = useState([0, 200000])
  const [showFilters, setShowFilters] = useState(false)
  const [savedJobs, setSavedJobs] = useState<Set<string>>(new Set())

  useEffect(() => {
    searchJobs()
  }, [])

  const searchJobs = async () => {
    try {
      setLoading(true)
      
      const params = new URLSearchParams({
        action: 'search',
        ...(searchQuery && { query: searchQuery }),
        ...(location && { location }),
        ...(remoteType && { remoteType }),
        ...(employmentType && { employmentType }),
        ...(experienceLevel && { experienceLevel }),
        ...(salaryRange[0] > 0 && { salaryMin: salaryRange[0].toString() }),
        ...(salaryRange[1] < 200000 && { salaryMax: salaryRange[1].toString() }),
        limit: '20'
      })

      const response = await fetch(`/api/job-matching/jobs?${params}`)
      
      if (!response.ok) {
        throw new Error('Failed to search jobs')
      }

      const data = await response.json()
      setJobs(data.jobs || [])
    } catch (error) {
      console.error('Error searching jobs:', error)
      toast.error('Failed to search jobs')
    } finally {
      setLoading(false)
    }
  }

  const handleSaveJob = async (jobId: string) => {
    try {
      // This would save the job to user's saved jobs
      setSavedJobs(prev => new Set([...prev, jobId]))
      toast.success('Job saved successfully')
    } catch (error) {
      toast.error('Failed to save job')
    }
  }

  const handleApplyToJob = async (job: JobPosting) => {
    try {
      const response = await fetch('/api/job-matching/applications', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'apply',
          jobPostingId: job.id
        })
      })

      if (!response.ok) {
        throw new Error('Failed to apply to job')
      }

      toast.success('Application submitted successfully')
    } catch (error) {
      console.error('Error applying to job:', error)
      toast.error('Failed to apply to job')
    }
  }

  const formatSalary = (min?: number, max?: number) => {
    if (!min && !max) return 'Salary not specified'
    if (min && max) return `$${(min / 1000).toFixed(0)}k - $${(max / 1000).toFixed(0)}k`
    if (min) return `$${(min / 1000).toFixed(0)}k+`
    if (max) return `Up to $${(max / 1000).toFixed(0)}k`
    return 'Salary not specified'
  }

  const getEmploymentTypeColor = (type?: string) => {
    switch (type) {
      case 'full-time': return 'bg-green-100 text-green-800'
      case 'part-time': return 'bg-blue-100 text-blue-800'
      case 'contract': return 'bg-orange-100 text-orange-800'
      case 'internship': return 'bg-purple-100 text-purple-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getRemoteTypeIcon = (type?: string) => {
    switch (type) {
      case 'remote': return '🏠'
      case 'hybrid': return '🏢'
      case 'on-site': return '🏢'
      default: return '📍'
    }
  }

  return (
    <div className={className}>
      {/* Search Header */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Search className="w-5 h-5" />
            <span>Job Search</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Main Search */}
          <div className="flex space-x-2">
            <div className="flex-1">
              <Input
                placeholder="Search jobs, companies, or keywords..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && searchJobs()}
              />
            </div>
            <div className="w-64">
              <Input
                placeholder="Location"
                value={location}
                onChange={(e) => setLocation(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && searchJobs()}
              />
            </div>
            <Button onClick={searchJobs} disabled={loading}>
              {loading ? 'Searching...' : 'Search'}
            </Button>
          </div>

          {/* Filters Toggle */}
          <div className="flex items-center justify-between">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center space-x-1"
            >
              <Filter className="w-4 h-4" />
              <span>Filters</span>
            </Button>
            <div className="text-sm text-muted-foreground">
              {jobs.length} jobs found
            </div>
          </div>

          {/* Advanced Filters */}
          {showFilters && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 p-4 bg-gray-50 rounded-lg">
              <div>
                <Label htmlFor="remote-type">Work Type</Label>
                <Select value={remoteType} onValueChange={setRemoteType}>
                  <SelectTrigger>
                    <SelectValue placeholder="Any" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">Any</SelectItem>
                    <SelectItem value="remote">Remote</SelectItem>
                    <SelectItem value="hybrid">Hybrid</SelectItem>
                    <SelectItem value="on-site">On-site</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="employment-type">Employment Type</Label>
                <Select value={employmentType} onValueChange={setEmploymentType}>
                  <SelectTrigger>
                    <SelectValue placeholder="Any" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">Any</SelectItem>
                    <SelectItem value="full-time">Full-time</SelectItem>
                    <SelectItem value="part-time">Part-time</SelectItem>
                    <SelectItem value="contract">Contract</SelectItem>
                    <SelectItem value="internship">Internship</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="experience-level">Experience Level</Label>
                <Select value={experienceLevel} onValueChange={setExperienceLevel}>
                  <SelectTrigger>
                    <SelectValue placeholder="Any" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">Any</SelectItem>
                    <SelectItem value="entry">Entry Level</SelectItem>
                    <SelectItem value="mid">Mid Level</SelectItem>
                    <SelectItem value="senior">Senior Level</SelectItem>
                    <SelectItem value="executive">Executive</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="salary-range">
                  Salary Range: ${(salaryRange[0] / 1000).toFixed(0)}k - ${(salaryRange[1] / 1000).toFixed(0)}k
                </Label>
                <Slider
                  value={salaryRange}
                  onValueChange={setSalaryRange}
                  max={200000}
                  min={0}
                  step={5000}
                  className="mt-2"
                />
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Job Results */}
      <div className="space-y-4">
        {loading ? (
          <div className="space-y-4">
            {[...Array(5)].map((_, i) => (
              <Card key={i} className="animate-pulse">
                <CardContent className="p-6">
                  <div className="space-y-3">
                    <div className="h-4 bg-gray-200 rounded w-3/4" />
                    <div className="h-3 bg-gray-200 rounded w-1/2" />
                    <div className="h-3 bg-gray-200 rounded w-full" />
                    <div className="h-3 bg-gray-200 rounded w-2/3" />
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : jobs.length === 0 ? (
          <Card>
            <CardContent className="p-12 text-center">
              <Search className="w-12 h-12 mx-auto text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No jobs found</h3>
              <p className="text-gray-500">
                Try adjusting your search criteria or filters to find more opportunities.
              </p>
            </CardContent>
          </Card>
        ) : (
          jobs.map((job) => (
            <Card key={job.id} className="hover:shadow-md transition-shadow cursor-pointer">
              <CardContent className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    <div className="flex items-start justify-between mb-2">
                      <div>
                        <h3 
                          className="text-lg font-semibold text-gray-900 hover:text-blue-600 cursor-pointer"
                          onClick={() => onJobSelect?.(job)}
                        >
                          {job.title}
                        </h3>
                        <div className="flex items-center space-x-2 text-sm text-gray-600 mt-1">
                          <Building className="w-4 h-4" />
                          <span>{job.company}</span>
                          {job.location && (
                            <>
                              <span>•</span>
                              <MapPin className="w-4 h-4" />
                              <span>{job.location}</span>
                            </>
                          )}
                          {job.remoteType && (
                            <>
                              <span>•</span>
                              <span>{getRemoteTypeIcon(job.remoteType)} {job.remoteType}</span>
                            </>
                          )}
                        </div>
                      </div>
                      
                      <div className="flex items-center space-x-2 ml-4">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleSaveJob(job.id)}
                          className={savedJobs.has(job.id) ? 'text-red-500' : 'text-gray-500'}
                        >
                          <Heart className={`w-4 h-4 ${savedJobs.has(job.id) ? 'fill-current' : ''}`} />
                        </Button>
                        {job.sourceUrl && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => window.open(job.sourceUrl, '_blank')}
                          >
                            <ExternalLink className="w-4 h-4" />
                          </Button>
                        )}
                      </div>
                    </div>

                    <p className="text-gray-700 mb-3 line-clamp-2">
                      {job.description.substring(0, 200)}...
                    </p>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        {job.employmentType && (
                          <Badge className={getEmploymentTypeColor(job.employmentType)}>
                            {job.employmentType}
                          </Badge>
                        )}
                        {job.experienceLevel && (
                          <Badge variant="outline">
                            {job.experienceLevel} level
                          </Badge>
                        )}
                        <div className="flex items-center space-x-1 text-sm text-gray-600">
                          <DollarSign className="w-4 h-4" />
                          <span>{formatSalary(job.salaryMin, job.salaryMax)}</span>
                        </div>
                      </div>

                      <div className="flex items-center space-x-2">
                        {job.postedDate && (
                          <div className="flex items-center space-x-1 text-sm text-gray-500">
                            <Clock className="w-4 h-4" />
                            <span>{formatDistanceToNow(job.postedDate, { addSuffix: true })}</span>
                          </div>
                        )}
                      </div>
                    </div>

                    {job.skills && job.skills.length > 0 && (
                      <div className="mt-3">
                        <div className="flex flex-wrap gap-1">
                          {job.skills.slice(0, 5).map((skill, index) => (
                            <Badge key={index} variant="secondary" className="text-xs">
                              {skill}
                            </Badge>
                          ))}
                          {job.skills.length > 5 && (
                            <Badge variant="secondary" className="text-xs">
                              +{job.skills.length - 5} more
                            </Badge>
                          )}
                        </div>
                      </div>
                    )}

                    <div className="flex items-center justify-between mt-4">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => onJobSelect?.(job)}
                      >
                        View Details
                      </Button>
                      <Button
                        size="sm"
                        onClick={() => handleApplyToJob(job)}
                      >
                        Apply Now
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>
    </div>
  )
}
