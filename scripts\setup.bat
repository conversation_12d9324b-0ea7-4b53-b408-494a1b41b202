@echo off
setlocal enabledelayedexpansion

:: CareerCraft Setup Script for Windows
:: This script sets up the development environment for CareerCraft

echo.
echo 🚀 Setting up CareerCraft Development Environment
echo ==================================================
echo.

:: Check if Node.js is installed
echo [INFO] Checking Node.js installation...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Node.js is not installed. Please install Node.js 18 or higher.
    echo Download from: https://nodejs.org/
    pause
    exit /b 1
) else (
    for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
    echo [SUCCESS] Node.js is installed: !NODE_VERSION!
)

:: Check if npm is available
echo [INFO] Checking npm availability...
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] npm is not available. Please ensure Node.js is properly installed.
    pause
    exit /b 1
) else (
    echo [SUCCESS] npm is available
)

:: Create necessary directories
echo [INFO] Creating necessary directories...
if not exist "apps\web\src\components\ui" mkdir "apps\web\src\components\ui"
if not exist "apps\web\src\lib" mkdir "apps\web\src\lib"
if not exist "apps\web\src\hooks" mkdir "apps\web\src\hooks"
if not exist "apps\web\src\store" mkdir "apps\web\src\store"
if not exist "apps\web\public\images" mkdir "apps\web\public\images"
if not exist "packages\ui\src" mkdir "packages\ui\src"
if not exist "packages\utils\src" mkdir "packages\utils\src"
echo [SUCCESS] Directory structure created

:: Install dependencies
echo [INFO] Installing dependencies...
call npm install
if %errorlevel% neq 0 (
    echo [ERROR] Failed to install dependencies
    pause
    exit /b 1
)
echo [SUCCESS] Dependencies installed successfully

:: Setup environment variables
echo [INFO] Setting up environment variables...
if not exist ".env.local" (
    copy ".env.example" ".env.local" >nul
    echo [SUCCESS] Created .env.local from .env.example
    echo [WARNING] Please update .env.local with your actual configuration values:
    echo   - DATABASE_URL (PostgreSQL connection string)
    echo   - NEXTAUTH_SECRET (random secret key)
    echo   - OPENAI_API_KEY (for AI features)
    echo   - OAuth provider credentials (Google, GitHub)
) else (
    echo [WARNING] .env.local already exists. Please verify your configuration.
)

:: Generate Prisma client
echo [INFO] Setting up database...
call npm run db:generate
if %errorlevel% neq 0 (
    echo [ERROR] Failed to generate Prisma client
    pause
    exit /b 1
)
echo [SUCCESS] Prisma client generated

:: Setup Git hooks (if in a Git repository)
echo [INFO] Setting up Git hooks...
if exist ".git" (
    call npm run prepare
    if %errorlevel% neq 0 (
        echo [WARNING] Failed to setup Git hooks
    ) else (
        echo [SUCCESS] Git hooks configured with Husky
    )
) else (
    echo [WARNING] Not a Git repository. Skipping Git hooks setup.
)

:: Final instructions
echo.
echo [SUCCESS] 🎉 CareerCraft setup completed!
echo.
echo [INFO] Next steps:
echo 1. Update your .env.local file with actual values
echo 2. Set up your PostgreSQL database
echo 3. Run 'npm run db:push' to create database tables
echo 4. Run 'npm run dev' to start the development server
echo.
echo [INFO] Useful commands:
echo   npm run dev          - Start development server
echo   npm run build        - Build for production
echo   npm run test         - Run tests
echo   npm run lint         - Run linting
echo   npm run db:studio    - Open Prisma Studio
echo   npm run db:migrate   - Run database migrations
echo.
echo [INFO] Documentation:
echo   README.md                     - Project overview
echo   docs\architecture\README.md   - Architecture documentation
echo   docs\user-flows\README.md     - User flow diagrams
echo.
echo [SUCCESS] Happy coding! 🚀
echo.
pause
