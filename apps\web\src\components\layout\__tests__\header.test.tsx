import { describe, it, expect, jest, beforeEach } from '@jest/globals';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { SessionProvider } from 'next-auth/react';
import { ThemeProvider } from 'next-themes';
import { Header } from '../header';

// Mock next/navigation
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    prefetch: jest.fn(),
  }),
  usePathname: () => '/',
}));

// Mock next-auth/react
const mockSignOut = jest.fn();
jest.mock('next-auth/react', () => ({
  useSession: jest.fn(),
  signOut: mockSignOut,
  SessionProvider: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
}));

// Mock hooks/use-auth
jest.mock('@/hooks/use-auth', () => ({
  useAuth: () => ({
    signOut: mockSignOut,
  }),
}));

// Mock next-themes
jest.mock('next-themes', () => ({
  useTheme: () => ({
    theme: 'light',
    setTheme: jest.fn(),
  }),
  ThemeProvider: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
}));

const { useSession } = require('next-auth/react');

function renderHeader(session: any = null) {
  useSession.mockReturnValue({
    data: session,
    status: session ? 'authenticated' : 'unauthenticated',
  });

  return render(
    <ThemeProvider attribute="class" defaultTheme="system" enableSystem>
      <SessionProvider session={session}>
        <Header />
      </SessionProvider>
    </ThemeProvider>
  );
}

describe('Header Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Unauthenticated State', () => {
    it('should render logo and brand name', () => {
      renderHeader();
      
      expect(screen.getByText('CareerCraft')).toBeInTheDocument();
    });

    it('should show sign in and get started buttons when not authenticated', () => {
      renderHeader();
      
      expect(screen.getByText('Sign in')).toBeInTheDocument();
      expect(screen.getByText('Get Started')).toBeInTheDocument();
    });

    it('should show public navigation items only', () => {
      renderHeader();
      
      expect(screen.getByText('Templates')).toBeInTheDocument();
      expect(screen.getByText('Examples')).toBeInTheDocument();
      expect(screen.getByText('Pricing')).toBeInTheDocument();
      expect(screen.queryByText('Dashboard')).not.toBeInTheDocument();
    });

    it('should have correct links for auth buttons', () => {
      renderHeader();
      
      const signInLink = screen.getByText('Sign in').closest('a');
      const getStartedLink = screen.getByText('Get Started').closest('a');
      
      expect(signInLink).toHaveAttribute('href', '/auth/signin');
      expect(getStartedLink).toHaveAttribute('href', '/auth/signup');
    });
  });

  describe('Authenticated State', () => {
    const mockUser = {
      id: '1',
      name: 'John Doe',
      email: '<EMAIL>',
      image: 'https://example.com/avatar.jpg',
    };

    it('should show user avatar when authenticated', () => {
      renderHeader({ user: mockUser });
      
      // Avatar should be present
      const avatar = screen.getByRole('button', { name: /toggle user menu/i });
      expect(avatar).toBeInTheDocument();
    });

    it('should show dashboard navigation when authenticated', () => {
      renderHeader({ user: mockUser });
      
      expect(screen.getByText('Dashboard')).toBeInTheDocument();
    });

    it('should show user menu when avatar is clicked', async () => {
      renderHeader({ user: mockUser });
      
      const avatarButton = screen.getByRole('button');
      fireEvent.click(avatarButton);
      
      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeInTheDocument();
        expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
        expect(screen.getByText('Profile')).toBeInTheDocument();
        expect(screen.getByText('Settings')).toBeInTheDocument();
        expect(screen.getByText('Sign out')).toBeInTheDocument();
      });
    });

    it('should call signOut when sign out is clicked', async () => {
      renderHeader({ user: mockUser });
      
      const avatarButton = screen.getByRole('button');
      fireEvent.click(avatarButton);
      
      await waitFor(() => {
        const signOutButton = screen.getByText('Sign out');
        fireEvent.click(signOutButton);
        expect(mockSignOut).toHaveBeenCalled();
      });
    });

    it('should show user initials when no image is provided', () => {
      const userWithoutImage = { ...mockUser, image: null };
      renderHeader({ user: userWithoutImage });
      
      expect(screen.getByText('J')).toBeInTheDocument();
    });
  });

  describe('Theme Toggle', () => {
    it('should render theme toggle button', () => {
      renderHeader();
      
      const themeButton = screen.getByRole('button', { name: /toggle theme/i });
      expect(themeButton).toBeInTheDocument();
    });

    it('should show sun and moon icons for theme toggle', () => {
      renderHeader();
      
      // Icons should be present (though one might be hidden via CSS)
      const themeButton = screen.getByRole('button', { name: /toggle theme/i });
      expect(themeButton).toBeInTheDocument();
    });
  });

  describe('Mobile Navigation', () => {
    it('should show mobile menu button', () => {
      renderHeader();
      
      const mobileMenuButton = screen.getByRole('button', { name: /toggle menu/i });
      expect(mobileMenuButton).toBeInTheDocument();
    });

    it('should toggle mobile menu when button is clicked', () => {
      renderHeader();
      
      const mobileMenuButton = screen.getByRole('button', { name: /toggle menu/i });
      
      // Menu should not be visible initially
      expect(screen.queryByRole('navigation')).not.toBeInTheDocument();
      
      // Click to open menu
      fireEvent.click(mobileMenuButton);
      
      // Menu should now be visible
      expect(screen.getByRole('navigation')).toBeInTheDocument();
    });

    it('should close mobile menu when navigation link is clicked', () => {
      renderHeader();
      
      const mobileMenuButton = screen.getByRole('button', { name: /toggle menu/i });
      fireEvent.click(mobileMenuButton);
      
      // Menu should be open
      expect(screen.getByRole('navigation')).toBeInTheDocument();
      
      // Click a navigation link
      const templatesLink = screen.getByText('Templates');
      fireEvent.click(templatesLink);
      
      // Menu should close
      expect(screen.queryByRole('navigation')).not.toBeInTheDocument();
    });
  });

  describe('Navigation Links', () => {
    it('should have correct href attributes for navigation links', () => {
      renderHeader();
      
      const templatesLink = screen.getByText('Templates').closest('a');
      const examplesLink = screen.getByText('Examples').closest('a');
      const pricingLink = screen.getByText('Pricing').closest('a');
      
      expect(templatesLink).toHaveAttribute('href', '/templates');
      expect(examplesLink).toHaveAttribute('href', '/examples');
      expect(pricingLink).toHaveAttribute('href', '/pricing');
    });

    it('should show dashboard link when authenticated', () => {
      const mockUser = {
        id: '1',
        name: 'John Doe',
        email: '<EMAIL>',
      };
      
      renderHeader({ user: mockUser });
      
      const dashboardLink = screen.getByText('Dashboard').closest('a');
      expect(dashboardLink).toHaveAttribute('href', '/dashboard');
    });
  });

  describe('Accessibility', () => {
    it('should have proper ARIA labels', () => {
      renderHeader();
      
      expect(screen.getByRole('button', { name: /toggle theme/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /toggle menu/i })).toBeInTheDocument();
    });

    it('should have proper heading structure', () => {
      renderHeader();
      
      // Logo should be a link, not a heading
      const logo = screen.getByText('CareerCraft').closest('a');
      expect(logo).toBeInTheDocument();
    });

    it('should support keyboard navigation', () => {
      renderHeader();
      
      const themeButton = screen.getByRole('button', { name: /toggle theme/i });
      const mobileMenuButton = screen.getByRole('button', { name: /toggle menu/i });
      
      // Buttons should be focusable
      themeButton.focus();
      expect(document.activeElement).toBe(themeButton);
      
      mobileMenuButton.focus();
      expect(document.activeElement).toBe(mobileMenuButton);
    });
  });

  describe('Responsive Design', () => {
    it('should apply correct CSS classes for responsive behavior', () => {
      renderHeader();
      
      // Desktop navigation should have hidden class for mobile
      const desktopNav = screen.getByRole('navigation', { hidden: true });
      expect(desktopNav).toHaveClass('hidden', 'md:flex');
      
      // Mobile menu button should have class to hide on desktop
      const mobileMenuButton = screen.getByRole('button', { name: /toggle menu/i });
      expect(mobileMenuButton).toHaveClass('md:hidden');
    });
  });

  describe('Error Handling', () => {
    it('should handle missing user data gracefully', () => {
      renderHeader({ user: {} });
      
      // Should not crash and should show fallback
      expect(screen.getByText('U')).toBeInTheDocument(); // Fallback initial
    });

    it('should handle null user name gracefully', () => {
      renderHeader({ user: { id: '1', name: null, email: '<EMAIL>' } });
      
      // Should show fallback initial
      expect(screen.getByText('U')).toBeInTheDocument();
    });
  });
});
