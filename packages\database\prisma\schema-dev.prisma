// Development schema for SQLite
generator client {
  provider = "prisma-client-js"
  output   = "../src/generated"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

// User Management
model User {
  id            String    @id @default(cuid())
  email         String    @unique
  name          String?
  image         String?
  emailVerified DateTime?
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  // Relations
  accounts Account[]
  sessions Session[]
  resumes  Resume[]
  profiles UserProfile[]

  @@map("users")
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map("accounts")
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
  @@map("verification_tokens")
}

// User Profile for additional information
model UserProfile {
  id          String   @id @default(cuid())
  userId      String   @unique
  firstName   String?
  lastName    String?
  phone       String?
  location    String?
  website     String?
  linkedinUrl String?
  githubUrl   String?
  bio         String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("user_profiles")
}

// Resume Management (Simplified for SQLite)
model Resume {
  id          String   @id @default(cuid())
  userId      String
  title       String
  description String?
  templateId  String?
  isPublic    Boolean  @default(false)
  publicUrl   String?  @unique
  status      String   @default("DRAFT") // DRAFT, PUBLISHED, ARCHIVED
  
  // Simplified JSON fields as TEXT
  personalInfo String? // JSON as string
  sections     String? // JSON as string
  settings     String? // JSON as string
  metadata     String? // JSON as string
  
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  user         User           @relation(fields: [userId], references: [id], onDelete: Cascade)
  template     Template?      @relation(fields: [templateId], references: [id])
  experiences  Experience[]
  educations   Education[]
  skills       Skill[]
  projects     Project[]
  coverLetters CoverLetter[]

  @@map("resumes")
}

// Resume Sections (Simplified)
model Experience {
  id           String   @id @default(cuid())
  resumeId     String
  company      String
  position     String
  location     String?
  startDate    DateTime
  endDate      DateTime?
  isCurrent    Boolean  @default(false)
  description  String?
  achievements String?  // JSON as string
  displayOrder Int      @default(0)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  resume Resume @relation(fields: [resumeId], references: [id], onDelete: Cascade)

  @@map("experiences")
}

model Education {
  id           String   @id @default(cuid())
  resumeId     String
  institution  String
  degree       String
  field        String?
  location     String?
  startDate    DateTime
  endDate      DateTime?
  gpa          String?
  description  String?
  displayOrder Int      @default(0)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  resume Resume @relation(fields: [resumeId], references: [id], onDelete: Cascade)

  @@map("educations")
}

model Skill {
  id           String   @id @default(cuid())
  resumeId     String
  name         String
  category     String?   // e.g., "Technical", "Soft Skills", "Languages"
  level        String?   // BEGINNER, INTERMEDIATE, ADVANCED, EXPERT
  displayOrder Int      @default(0)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  resume Resume @relation(fields: [resumeId], references: [id], onDelete: Cascade)

  @@map("skills")
}

model Project {
  id           String   @id @default(cuid())
  resumeId     String
  name         String
  description  String?
  url          String?
  githubUrl    String?
  technologies String?  // JSON as string
  startDate    DateTime?
  endDate      DateTime?
  displayOrder Int      @default(0)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  resume Resume @relation(fields: [resumeId], references: [id], onDelete: Cascade)

  @@map("projects")
}

// Templates (Simplified)
model Template {
  id          String   @id @default(cuid())
  name        String
  description String?
  category    String?
  isPremium   Boolean  @default(false)
  isActive    Boolean  @default(true)
  config      String?  // JSON as string
  preview     String?  // Preview image URL
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  resumes Resume[]

  @@map("templates")
}

// Cover Letters (Simplified)
model CoverLetter {
  id          String   @id @default(cuid())
  resumeId    String?
  title       String
  content     String
  jobTitle    String?
  company     String?
  jobDescription String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  resume Resume? @relation(fields: [resumeId], references: [id], onDelete: SetNull)

  @@map("cover_letters")
}

// AI Generation History (Simplified)
model AIGeneration {
  id        String   @id @default(cuid())
  userId    String
  type      String   // BULLET_POINT, SUMMARY, COVER_LETTER, REWRITE, KEYWORDS
  prompt    String
  response  String
  metadata  String?  // JSON as string
  createdAt DateTime @default(now())

  @@map("ai_generations")
}
