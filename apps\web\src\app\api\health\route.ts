import { NextResponse } from 'next/server';
import { healthCheck } from '@careercraft/database';

export async function GET() {
  try {
    const dbHealth = await healthCheck();
    
    const health = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      version: process.env.npm_package_version || '2.0.0',
      environment: process.env.NODE_ENV || 'development',
      services: {
        database: dbHealth,
        api: {
          status: 'healthy',
          uptime: process.uptime(),
        },
      },
    };

    // If database is unhealthy, mark overall status as unhealthy
    if (dbHealth.status === 'unhealthy') {
      health.status = 'unhealthy';
    }

    const statusCode = health.status === 'healthy' ? 200 : 503;
    
    return NextResponse.json(health, { status: statusCode });
  } catch (error) {
    console.error('Health check failed:', error);
    
    return NextResponse.json(
      {
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        error: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 503 }
    );
  }
}

// Handle HEAD requests for simple health checks
export async function HEAD() {
  try {
    const dbHealth = await healthCheck();
    const statusCode = dbHealth.status === 'healthy' ? 200 : 503;
    return new Response(null, { status: statusCode });
  } catch {
    return new Response(null, { status: 503 });
  }
}
