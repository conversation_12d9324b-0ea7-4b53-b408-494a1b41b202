'use client'

import { useState, useEffect } from 'react'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { Separator } from '@/components/ui/separator'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import { 
  Star, 
  TrendingUp, 
  MapPin, 
  Building, 
  DollarSign,
  Clock,
  ThumbsUp,
  ThumbsDown,
  Bookmark,
  ExternalLink,
  Sparkles,
  Target,
  Award,
  RefreshCw
} from 'lucide-react'
import { formatDistanceToNow } from 'date-fns'
import { toast } from 'sonner'

interface JobRecommendation {
  id: string
  jobPostingId: string
  matchScore: number
  reasoning?: any
  isViewed: boolean
  isSaved: boolean
  isDismissed: boolean
  recommendedAt: Date
  jobPosting?: {
    id: string
    title: string
    company: string
    description: string
    location?: string
    salaryMin?: number
    salaryMax?: number
    employmentType?: string
    remoteType?: string
    experienceLevel?: string
    skills?: string[]
    postedDate?: Date
    sourceUrl?: string
  }
}

interface JobRecommendationsProps {
  className?: string
  onJobSelect?: (job: any) => void
}

export function JobRecommendations({ className, onJobSelect }: JobRecommendationsProps) {
  const [recommendations, setRecommendations] = useState<JobRecommendation[]>([])
  const [loading, setLoading] = useState(true)
  const [refreshing, setRefreshing] = useState(false)

  useEffect(() => {
    loadRecommendations()
  }, [])

  const loadRecommendations = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/job-matching/jobs?action=recommendations&limit=10')
      
      if (!response.ok) {
        throw new Error('Failed to load recommendations')
      }

      const data = await response.json()
      setRecommendations(data.recommendations.map((rec: any) => ({
        ...rec,
        recommendedAt: new Date(rec.recommendedAt),
        jobPosting: rec.jobPosting ? {
          ...rec.jobPosting,
          postedDate: rec.jobPosting.postedDate ? new Date(rec.jobPosting.postedDate) : undefined
        } : undefined
      })))
    } catch (error) {
      console.error('Error loading recommendations:', error)
      toast.error('Failed to load job recommendations')
    } finally {
      setLoading(false)
    }
  }

  const refreshRecommendations = async () => {
    try {
      setRefreshing(true)
      const response = await fetch('/api/job-matching/jobs?action=recommendations&refresh=true&limit=10')
      
      if (!response.ok) {
        throw new Error('Failed to refresh recommendations')
      }

      await loadRecommendations()
      toast.success('Recommendations refreshed')
    } catch (error) {
      console.error('Error refreshing recommendations:', error)
      toast.error('Failed to refresh recommendations')
    } finally {
      setRefreshing(false)
    }
  }

  const handleRecommendationAction = async (recommendationId: string, action: 'save' | 'dismiss') => {
    try {
      const response = await fetch('/api/job-matching/jobs', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'update-recommendation',
          recommendationId,
          status: action
        })
      })

      if (!response.ok) {
        throw new Error(`Failed to ${action} recommendation`)
      }

      // Update local state
      setRecommendations(prev => prev.map(rec => 
        rec.id === recommendationId 
          ? { 
              ...rec, 
              isSaved: action === 'save' ? true : rec.isSaved,
              isDismissed: action === 'dismiss' ? true : rec.isDismissed
            }
          : rec
      ))

      toast.success(`Recommendation ${action}d successfully`)
    } catch (error) {
      console.error(`Error ${action}ing recommendation:`, error)
      toast.error(`Failed to ${action} recommendation`)
    }
  }

  const markAsViewed = async (recommendationId: string) => {
    try {
      await fetch('/api/job-matching/jobs', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'mark-viewed',
          recommendationId
        })
      })

      setRecommendations(prev => prev.map(rec => 
        rec.id === recommendationId ? { ...rec, isViewed: true } : rec
      ))
    } catch (error) {
      console.error('Error marking recommendation as viewed:', error)
    }
  }

  const getMatchScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600'
    if (score >= 60) return 'text-yellow-600'
    return 'text-red-600'
  }

  const getMatchScoreLabel = (score: number) => {
    if (score >= 80) return 'Excellent Match'
    if (score >= 60) return 'Good Match'
    if (score >= 40) return 'Fair Match'
    return 'Poor Match'
  }

  const formatSalary = (min?: number, max?: number) => {
    if (!min && !max) return 'Salary not specified'
    if (min && max) return `$${(min / 1000).toFixed(0)}k - $${(max / 1000).toFixed(0)}k`
    if (min) return `$${(min / 1000).toFixed(0)}k+`
    if (max) return `Up to $${(max / 1000).toFixed(0)}k`
    return 'Salary not specified'
  }

  const visibleRecommendations = recommendations.filter(rec => !rec.isDismissed)

  if (loading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Sparkles className="w-5 h-5" />
            <span>AI Job Recommendations</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="animate-pulse">
                <div className="flex items-center space-x-3">
                  <div className="w-12 h-12 bg-gray-200 rounded-lg" />
                  <div className="flex-1">
                    <div className="h-4 bg-gray-200 rounded w-3/4 mb-2" />
                    <div className="h-3 bg-gray-200 rounded w-1/2 mb-2" />
                    <div className="h-3 bg-gray-200 rounded w-2/3" />
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <Sparkles className="w-5 h-5" />
            <span>AI Job Recommendations</span>
            <Badge variant="secondary">{visibleRecommendations.length}</Badge>
          </CardTitle>
          <Button
            variant="outline"
            size="sm"
            onClick={refreshRecommendations}
            disabled={refreshing}
            className="flex items-center space-x-1"
          >
            <RefreshCw className={`w-4 h-4 ${refreshing ? 'animate-spin' : ''}`} />
            <span>Refresh</span>
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {visibleRecommendations.length === 0 ? (
          <div className="text-center py-8">
            <Target className="w-12 h-12 mx-auto text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No recommendations yet</h3>
            <p className="text-gray-500 mb-4">
              Complete your profile and set job preferences to get personalized recommendations.
            </p>
            <Button onClick={refreshRecommendations} disabled={refreshing}>
              Generate Recommendations
            </Button>
          </div>
        ) : (
          <div className="space-y-4">
            {visibleRecommendations.map((recommendation) => {
              const job = recommendation.jobPosting
              if (!job) return null

              return (
                <div
                  key={recommendation.id}
                  className={`p-4 rounded-lg border transition-colors ${
                    !recommendation.isViewed 
                      ? 'border-blue-200 bg-blue-50' 
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => markAsViewed(recommendation.id)}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-start justify-between mb-2">
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-1">
                            <h3 
                              className="text-lg font-semibold text-gray-900 hover:text-blue-600 cursor-pointer"
                              onClick={() => onJobSelect?.(job)}
                            >
                              {job.title}
                            </h3>
                            {!recommendation.isViewed && (
                              <Badge variant="default" className="text-xs">
                                New
                              </Badge>
                            )}
                          </div>
                          
                          <div className="flex items-center space-x-2 text-sm text-gray-600 mb-2">
                            <Building className="w-4 h-4" />
                            <span>{job.company}</span>
                            {job.location && (
                              <>
                                <span>•</span>
                                <MapPin className="w-4 h-4" />
                                <span>{job.location}</span>
                              </>
                            )}
                            {job.remoteType && (
                              <>
                                <span>•</span>
                                <span>{job.remoteType}</span>
                              </>
                            )}
                          </div>

                          <p className="text-gray-700 mb-3 line-clamp-2">
                            {job.description.substring(0, 150)}...
                          </p>
                        </div>

                        <div className="ml-4 text-right">
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger>
                                <div className="flex items-center space-x-2">
                                  <div className="text-right">
                                    <div className={`text-lg font-bold ${getMatchScoreColor(recommendation.matchScore)}`}>
                                      {recommendation.matchScore}%
                                    </div>
                                    <div className="text-xs text-gray-500">
                                      {getMatchScoreLabel(recommendation.matchScore)}
                                    </div>
                                  </div>
                                  <Award className={`w-5 h-5 ${getMatchScoreColor(recommendation.matchScore)}`} />
                                </div>
                              </TooltipTrigger>
                              <TooltipContent>
                                <div className="text-sm">
                                  <div className="font-medium mb-1">Match Score Breakdown:</div>
                                  <div>Skills: {Math.round(recommendation.matchScore * 0.6)}%</div>
                                  <div>Experience: {Math.round(recommendation.matchScore * 0.3)}%</div>
                                  <div>Location: {Math.round(recommendation.matchScore * 0.1)}%</div>
                                </div>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </div>
                      </div>

                      <div className="mb-3">
                        <Progress value={recommendation.matchScore} className="h-2" />
                      </div>

                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center space-x-4">
                          {job.employmentType && (
                            <Badge variant="outline" className="text-xs">
                              {job.employmentType}
                            </Badge>
                          )}
                          {job.experienceLevel && (
                            <Badge variant="outline" className="text-xs">
                              {job.experienceLevel} level
                            </Badge>
                          )}
                          <div className="flex items-center space-x-1 text-sm text-gray-600">
                            <DollarSign className="w-4 h-4" />
                            <span>{formatSalary(job.salaryMin, job.salaryMax)}</span>
                          </div>
                        </div>

                        <div className="flex items-center space-x-1 text-sm text-gray-500">
                          <Clock className="w-4 h-4" />
                          <span>{formatDistanceToNow(recommendation.recommendedAt, { addSuffix: true })}</span>
                        </div>
                      </div>

                      {job.skills && job.skills.length > 0 && (
                        <div className="mb-3">
                          <div className="flex flex-wrap gap-1">
                            {job.skills.slice(0, 4).map((skill, index) => (
                              <Badge key={index} variant="secondary" className="text-xs">
                                {skill}
                              </Badge>
                            ))}
                            {job.skills.length > 4 && (
                              <Badge variant="secondary" className="text-xs">
                                +{job.skills.length - 4} more
                              </Badge>
                            )}
                          </div>
                        </div>
                      )}

                      <Separator className="my-3" />

                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => onJobSelect?.(job)}
                          >
                            View Details
                          </Button>
                          <Button
                            size="sm"
                            onClick={() => {
                              // Apply to job logic
                              toast.success('Application submitted')
                            }}
                          >
                            Apply Now
                          </Button>
                        </div>

                        <div className="flex items-center space-x-1">
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleRecommendationAction(recommendation.id, 'save')}
                                  className={recommendation.isSaved ? 'text-blue-600' : 'text-gray-500'}
                                >
                                  <Bookmark className={`w-4 h-4 ${recommendation.isSaved ? 'fill-current' : ''}`} />
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>Save job</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>

                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleRecommendationAction(recommendation.id, 'dismiss')}
                                  className="text-gray-500"
                                >
                                  <ThumbsDown className="w-4 h-4" />
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>Not interested</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>

                          {job.sourceUrl && (
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => window.open(job.sourceUrl, '_blank')}
                                    className="text-gray-500"
                                  >
                                    <ExternalLink className="w-4 h-4" />
                                  </Button>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p>View on job board</p>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
