import { describe, it, expect, vi, beforeEach } from 'vitest'
import { screen, fireEvent, waitFor } from '@testing-library/react'
import { TemplateGallery } from '../TemplateGallery'
import { render, mockTemplates } from '@/test/utils'

// Mock the useRouter hook
const mockPush = vi.fn()
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: mockPush,
  }),
}))

describe('TemplateGallery', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders template cards with correct information', async () => {
    render(<TemplateGallery />)

    await waitFor(() => {
      // Check for template names
      expect(screen.getByText('Modern Professional')).toBeInTheDocument()
      expect(screen.getByText('Creative Designer')).toBeInTheDocument()

      // Check for descriptions
      expect(screen.getByText(/Clean and contemporary design/)).toBeInTheDocument()
      expect(screen.getByText(/Bold and artistic design/)).toBeInTheDocument()

      // Check for categories
      expect(screen.getByText('Modern')).toBeInTheDocument()
      expect(screen.getByText('Creative')).toBeInTheDocument()
    })
  })

  it('displays premium and popular badges correctly', async () => {
    render(<TemplateGallery />)

    await waitFor(() => {
      // Check for premium badge
      expect(screen.getByText('Premium')).toBeInTheDocument()
      
      // Check for popular badges
      const popularBadges = screen.getAllByText('Popular')
      expect(popularBadges.length).toBeGreaterThan(0)
    })
  })

  it('shows loading state initially', () => {
    render(<TemplateGallery />)

    // Should show loading skeletons
    const loadingElements = document.querySelectorAll('.animate-pulse')
    expect(loadingElements.length).toBeGreaterThan(0)
  })

  it('filters templates by search query', async () => {
    render(<TemplateGallery />)

    await waitFor(() => {
      // Wait for templates to load first
      expect(screen.getByText('Modern Professional')).toBeInTheDocument()
    })

    // Find and use the search input
    const searchInput = screen.getByPlaceholderText('Search templates...')
    fireEvent.change(searchInput, { target: { value: 'Modern' } })

    await waitFor(() => {
      // Should show only Modern template
      expect(screen.getByText('Modern Professional')).toBeInTheDocument()
      expect(screen.queryByText('Creative Designer')).not.toBeInTheDocument()
    })
  })

  it('filters templates by category', async () => {
    render(<TemplateGallery />)

    await waitFor(() => {
      // Wait for templates to load
      expect(screen.getByText('Modern Professional')).toBeInTheDocument()
    })

    // Find and use the category filter
    const categorySelect = screen.getByRole('combobox')
    fireEvent.click(categorySelect)

    await waitFor(() => {
      const creativeOption = screen.getByText('Creative')
      fireEvent.click(creativeOption)
    })

    await waitFor(() => {
      // Should show only Creative template
      expect(screen.getByText('Creative Designer')).toBeInTheDocument()
      expect(screen.queryByText('Modern Professional')).not.toBeInTheDocument()
    })
  })

  it('shows empty state when no templates found', async () => {
    render(<TemplateGallery />)

    await waitFor(() => {
      // Wait for templates to load first
      expect(screen.getByText('Modern Professional')).toBeInTheDocument()
    })

    // Search for non-existent template
    const searchInput = screen.getByPlaceholderText('Search templates...')
    fireEvent.change(searchInput, { target: { value: 'nonexistent' } })

    await waitFor(() => {
      expect(screen.getByText('No templates found')).toBeInTheDocument()
      expect(screen.getByText('Try adjusting your search or filters')).toBeInTheDocument()
    })
  })

  it('navigates to preview when preview button is clicked', async () => {
    render(<TemplateGallery />)

    await waitFor(() => {
      // Hover over a template card to show preview button
      const templateCard = screen.getByText('Modern Professional').closest('.glass-card')
      if (templateCard) {
        fireEvent.mouseEnter(templateCard)
        
        const previewButton = screen.getByText('Preview')
        fireEvent.click(previewButton)
      }
    })

    expect(mockPush).toHaveBeenCalledWith('/dashboard/templates/modern-1/preview')
  })

  it('calls onSelectTemplate when template is selected', async () => {
    const mockOnSelectTemplate = vi.fn()
    
    render(<TemplateGallery onSelectTemplate={mockOnSelectTemplate} showSelectButton={true} />)

    await waitFor(() => {
      const selectButton = screen.getByText('Select Template')
      fireEvent.click(selectButton)
    })

    expect(mockOnSelectTemplate).toHaveBeenCalledWith(
      expect.objectContaining({
        id: 'modern-1',
        name: 'Modern Professional'
      })
    )
  })

  it('navigates to create resume when no onSelectTemplate provided', async () => {
    render(<TemplateGallery />)

    await waitFor(() => {
      const useTemplateButton = screen.getByText('Use This Template')
      fireEvent.click(useTemplateButton)
    })

    expect(mockPush).toHaveBeenCalledWith('/dashboard/resumes/new?template=modern-1')
  })

  it('displays template ratings and download counts', async () => {
    render(<TemplateGallery />)

    await waitFor(() => {
      // Check for ratings
      expect(screen.getByText('4.8')).toBeInTheDocument()
      expect(screen.getByText('4.7')).toBeInTheDocument()

      // Check for download counts
      expect(screen.getByText('1,250 downloads')).toBeInTheDocument()
      expect(screen.getByText('2,100 downloads')).toBeInTheDocument()
    })
  })

  it('displays template features', async () => {
    render(<TemplateGallery />)

    await waitFor(() => {
      // Check for feature badges
      expect(screen.getByText('ATS Optimized')).toBeInTheDocument()
      expect(screen.getByText('Clean Layout')).toBeInTheDocument()
      expect(screen.getByText('Creative Layout')).toBeInTheDocument()
      expect(screen.getByText('Color Accents')).toBeInTheDocument()
    })
  })

  it('shows color palette for each template', async () => {
    render(<TemplateGallery />)

    await waitFor(() => {
      // Check for color circles
      const colorElements = document.querySelectorAll('.w-3.h-3.rounded-full')
      expect(colorElements.length).toBeGreaterThan(0)
    })
  })

  it('handles template selection by clicking on card', async () => {
    render(<TemplateGallery />)

    await waitFor(() => {
      const templateCard = screen.getByText('Modern Professional').closest('.glass-card')
      if (templateCard) {
        fireEvent.click(templateCard)
        
        // Card should be selected (have ring styling)
        expect(templateCard).toHaveClass('ring-2', 'ring-blue-500')
      }
    })
  })

  it('applies glassmorphism styling', async () => {
    render(<TemplateGallery />)

    await waitFor(() => {
      const glassCards = document.querySelectorAll('.glass-card')
      expect(glassCards.length).toBeGreaterThan(0)

      const glassPanels = document.querySelectorAll('.glass-panel')
      expect(glassPanels.length).toBeGreaterThan(0)
    })
  })

  it('has hover effects on template cards', async () => {
    render(<TemplateGallery />)

    await waitFor(() => {
      const templateCards = document.querySelectorAll('.glass-card')
      templateCards.forEach(card => {
        expect(card).toHaveClass('hover:scale-105')
      })
    })
  })

  it('is responsive on different screen sizes', async () => {
    render(<TemplateGallery />)

    await waitFor(() => {
      // Check for responsive grid classes
      const gridContainer = document.querySelector('.grid')
      expect(gridContainer).toHaveClass('grid-cols-1', 'md:grid-cols-2', 'lg:grid-cols-3')
    })
  })

  it('displays category icons correctly', async () => {
    render(<TemplateGallery />)

    await waitFor(() => {
      // Check that category badges have icons (mocked as div elements)
      const categoryBadges = document.querySelectorAll('[data-testid="mock-icon"]')
      expect(categoryBadges.length).toBeGreaterThan(0)
    })
  })

  it('shows more features indicator when template has many features', async () => {
    render(<TemplateGallery />)

    await waitFor(() => {
      // Templates with more than 2 features should show "+X more" badge
      const moreFeaturesBadges = screen.getAllByText(/\+\d+ more/)
      expect(moreFeaturesBadges.length).toBeGreaterThan(0)
    })
  })

  it('handles search input changes', async () => {
    render(<TemplateGallery />)

    const searchInput = screen.getByPlaceholderText('Search templates...')
    
    // Test typing in search
    fireEvent.change(searchInput, { target: { value: 'Creative' } })
    expect(searchInput).toHaveValue('Creative')

    // Test clearing search
    fireEvent.change(searchInput, { target: { value: '' } })
    expect(searchInput).toHaveValue('')
  })

  it('prevents event bubbling on preview button click', async () => {
    const mockCardClick = vi.fn()
    
    render(<TemplateGallery />)

    await waitFor(() => {
      const templateCard = screen.getByText('Modern Professional').closest('.glass-card')
      if (templateCard) {
        templateCard.addEventListener('click', mockCardClick)
        
        fireEvent.mouseEnter(templateCard)
        
        const previewButton = screen.getByText('Preview')
        fireEvent.click(previewButton)
        
        // Card click should not be triggered when preview button is clicked
        expect(mockCardClick).not.toHaveBeenCalled()
      }
    })
  })
})
