import { config } from 'dotenv';
import { join } from 'path';

// Load test environment variables
config({ path: join(__dirname, '../../../../.env.test') });

// Set test environment
process.env.NODE_ENV = 'test';

// Ensure we have a test database URL
if (!process.env.DATABASE_URL) {
  throw new Error('DATABASE_URL must be set for tests');
}

// Verify we're using a test database
if (!process.env.DATABASE_URL.includes('test')) {
  console.warn('⚠️  Warning: DATABASE_URL does not contain "test" - ensure you are using a test database');
}

// Global test timeout
jest.setTimeout(30000);
