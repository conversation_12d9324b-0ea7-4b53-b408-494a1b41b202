/**
 * Resume Validation Schemas
 * 
 * Zod schemas for validating resume data throughout the application.
 */

import { z } from 'zod';
import {
  SkillCategory,
  SkillLevel,
  LanguageProficiency,
  ResumeSectionType,
  TemplateCategory,
  SuggestionType,
  SuggestionSeverity,
} from '../types/resume';

// Base validation schemas
export const personalInfoSchema = z.object({
  firstName: z.string().min(1, 'First name is required').max(50, 'First name too long'),
  lastName: z.string().min(1, 'Last name is required').max(50, 'Last name too long'),
  email: z.string().email('Invalid email address'),
  phone: z.string().min(10, 'Phone number must be at least 10 digits').max(20, 'Phone number too long'),
  location: z.string().min(1, 'Location is required').max(100, 'Location too long'),
  website: z.string().url('Invalid website URL').optional().or(z.literal('')),
  linkedin: z.string().url('Invalid LinkedIn URL').optional().or(z.literal('')),
  github: z.string().url('Invalid GitHub URL').optional().or(z.literal('')),
  portfolio: z.string().url('Invalid portfolio URL').optional().or(z.literal('')),
  summary: z.string().max(500, 'Summary too long').optional(),
});

export const workExperienceSchema = z.object({
  id: z.string(),
  company: z.string().min(1, 'Company name is required').max(100, 'Company name too long'),
  position: z.string().min(1, 'Position is required').max(100, 'Position too long'),
  location: z.string().min(1, 'Location is required').max(100, 'Location too long'),
  startDate: z.string().min(1, 'Start date is required'),
  endDate: z.string().optional(),
  isCurrentRole: z.boolean(),
  description: z.string().min(1, 'Description is required').max(1000, 'Description too long'),
  achievements: z.array(z.string().max(200, 'Achievement too long')).max(10, 'Too many achievements'),
  technologies: z.array(z.string().max(50, 'Technology name too long')).max(20, 'Too many technologies').optional(),
}).refine((data) => {
  if (!data.isCurrentRole && !data.endDate) {
    return false;
  }
  return true;
}, {
  message: 'End date is required for past roles',
  path: ['endDate'],
});

export const educationSchema = z.object({
  id: z.string(),
  institution: z.string().min(1, 'Institution is required').max(100, 'Institution name too long'),
  degree: z.string().min(1, 'Degree is required').max(100, 'Degree too long'),
  field: z.string().min(1, 'Field of study is required').max(100, 'Field too long'),
  location: z.string().min(1, 'Location is required').max(100, 'Location too long'),
  startDate: z.string().min(1, 'Start date is required'),
  endDate: z.string().optional(),
  isCurrentlyEnrolled: z.boolean(),
  gpa: z.string().max(10, 'GPA too long').optional(),
  honors: z.array(z.string().max(100, 'Honor too long')).max(5, 'Too many honors').optional(),
  relevantCoursework: z.array(z.string().max(100, 'Course name too long')).max(10, 'Too many courses').optional(),
}).refine((data) => {
  if (!data.isCurrentlyEnrolled && !data.endDate) {
    return false;
  }
  return true;
}, {
  message: 'End date is required for completed education',
  path: ['endDate'],
});

export const projectSchema = z.object({
  id: z.string(),
  name: z.string().min(1, 'Project name is required').max(100, 'Project name too long'),
  description: z.string().min(1, 'Description is required').max(500, 'Description too long'),
  technologies: z.array(z.string().max(50, 'Technology name too long')).min(1, 'At least one technology required').max(15, 'Too many technologies'),
  startDate: z.string().min(1, 'Start date is required'),
  endDate: z.string().optional(),
  isOngoing: z.boolean(),
  url: z.string().url('Invalid project URL').optional().or(z.literal('')),
  github: z.string().url('Invalid GitHub URL').optional().or(z.literal('')),
  highlights: z.array(z.string().max(200, 'Highlight too long')).max(5, 'Too many highlights'),
}).refine((data) => {
  if (!data.isOngoing && !data.endDate) {
    return false;
  }
  return true;
}, {
  message: 'End date is required for completed projects',
  path: ['endDate'],
});

export const skillSchema = z.object({
  id: z.string(),
  name: z.string().min(1, 'Skill name is required').max(50, 'Skill name too long'),
  category: z.nativeEnum(SkillCategory),
  level: z.nativeEnum(SkillLevel),
  yearsOfExperience: z.number().min(0, 'Years cannot be negative').max(50, 'Years too high').optional(),
});

export const certificationSchema = z.object({
  id: z.string(),
  name: z.string().min(1, 'Certification name is required').max(100, 'Certification name too long'),
  issuer: z.string().min(1, 'Issuer is required').max(100, 'Issuer name too long'),
  issueDate: z.string().min(1, 'Issue date is required'),
  expirationDate: z.string().optional(),
  credentialId: z.string().max(100, 'Credential ID too long').optional(),
  url: z.string().url('Invalid certification URL').optional().or(z.literal('')),
});

export const languageSchema = z.object({
  id: z.string(),
  name: z.string().min(1, 'Language name is required').max(50, 'Language name too long'),
  proficiency: z.nativeEnum(LanguageProficiency),
});

export const awardSchema = z.object({
  id: z.string(),
  title: z.string().min(1, 'Award title is required').max(100, 'Award title too long'),
  issuer: z.string().min(1, 'Issuer is required').max(100, 'Issuer name too long'),
  date: z.string().min(1, 'Date is required'),
  description: z.string().max(300, 'Description too long').optional(),
});

export const publicationSchema = z.object({
  id: z.string(),
  title: z.string().min(1, 'Publication title is required').max(200, 'Title too long'),
  publisher: z.string().min(1, 'Publisher is required').max(100, 'Publisher name too long'),
  date: z.string().min(1, 'Publication date is required'),
  url: z.string().url('Invalid publication URL').optional().or(z.literal('')),
  description: z.string().max(300, 'Description too long').optional(),
});

export const volunteerExperienceSchema = z.object({
  id: z.string(),
  organization: z.string().min(1, 'Organization is required').max(100, 'Organization name too long'),
  role: z.string().min(1, 'Role is required').max(100, 'Role too long'),
  location: z.string().min(1, 'Location is required').max(100, 'Location too long'),
  startDate: z.string().min(1, 'Start date is required'),
  endDate: z.string().optional(),
  isOngoing: z.boolean(),
  description: z.string().min(1, 'Description is required').max(500, 'Description too long'),
  achievements: z.array(z.string().max(200, 'Achievement too long')).max(5, 'Too many achievements'),
}).refine((data) => {
  if (!data.isOngoing && !data.endDate) {
    return false;
  }
  return true;
}, {
  message: 'End date is required for completed volunteer work',
  path: ['endDate'],
});

export const resumeSectionSchema = z.object({
  id: z.string(),
  type: z.nativeEnum(ResumeSectionType),
  title: z.string().min(1, 'Section title is required').max(50, 'Section title too long'),
  isVisible: z.boolean(),
  order: z.number().min(0, 'Order cannot be negative'),
  data: z.any(), // Will be validated based on section type
});

export const templateStyleSchema = z.object({
  fontFamily: z.string().min(1, 'Font family is required'),
  fontSize: z.number().min(8, 'Font size too small').max(24, 'Font size too large'),
  lineHeight: z.number().min(1, 'Line height too small').max(3, 'Line height too large'),
  margins: z.object({
    top: z.number().min(0, 'Margin cannot be negative'),
    right: z.number().min(0, 'Margin cannot be negative'),
    bottom: z.number().min(0, 'Margin cannot be negative'),
    left: z.number().min(0, 'Margin cannot be negative'),
  }),
  colors: z.object({
    primary: z.string().regex(/^#[0-9A-F]{6}$/i, 'Invalid color format'),
    secondary: z.string().regex(/^#[0-9A-F]{6}$/i, 'Invalid color format'),
    text: z.string().regex(/^#[0-9A-F]{6}$/i, 'Invalid color format'),
    accent: z.string().regex(/^#[0-9A-F]{6}$/i, 'Invalid color format'),
  }),
  spacing: z.object({
    sectionGap: z.number().min(0, 'Spacing cannot be negative'),
    itemGap: z.number().min(0, 'Spacing cannot be negative'),
  }),
});

export const resumeTemplateSchema = z.object({
  id: z.string(),
  name: z.string().min(1, 'Template name is required').max(50, 'Template name too long'),
  description: z.string().min(1, 'Description is required').max(200, 'Description too long'),
  category: z.nativeEnum(TemplateCategory),
  previewImage: z.string().url('Invalid preview image URL'),
  isPremium: z.boolean(),
  sections: z.array(z.nativeEnum(ResumeSectionType)).min(1, 'At least one section required'),
  styling: templateStyleSchema,
});

export const resumeSettingsSchema = z.object({
  isPublic: z.boolean(),
  allowComments: z.boolean(),
  seoOptimized: z.boolean(),
  atsOptimized: z.boolean(),
  targetJobTitle: z.string().max(100, 'Job title too long').optional(),
  targetCompany: z.string().max(100, 'Company name too long').optional(),
  keywords: z.array(z.string().max(50, 'Keyword too long')).max(20, 'Too many keywords'),
});

export const resumeMetadataSchema = z.object({
  version: z.number().min(1, 'Version must be positive'),
  lastEditedBy: z.string().min(1, 'Last edited by is required'),
  wordCount: z.number().min(0, 'Word count cannot be negative'),
  pageCount: z.number().min(1, 'Page count must be positive'),
  atsScore: z.number().min(0, 'ATS score cannot be negative').max(100, 'ATS score cannot exceed 100').optional(),
  readabilityScore: z.number().min(0, 'Readability score cannot be negative').max(100, 'Readability score cannot exceed 100').optional(),
  completionPercentage: z.number().min(0, 'Completion cannot be negative').max(100, 'Completion cannot exceed 100'),
});

export const resumeSchema = z.object({
  id: z.string(),
  userId: z.string(),
  title: z.string().min(1, 'Resume title is required').max(100, 'Resume title too long'),
  templateId: z.string().min(1, 'Template ID is required'),
  personalInfo: personalInfoSchema,
  sections: z.array(resumeSectionSchema),
  settings: resumeSettingsSchema,
  metadata: resumeMetadataSchema,
  createdAt: z.string(),
  updatedAt: z.string(),
});

export const analysisSuggestionSchema = z.object({
  id: z.string(),
  type: z.nativeEnum(SuggestionType),
  severity: z.nativeEnum(SuggestionSeverity),
  title: z.string().min(1, 'Suggestion title is required').max(100, 'Title too long'),
  description: z.string().min(1, 'Description is required').max(300, 'Description too long'),
  section: z.nativeEnum(ResumeSectionType).optional(),
  actionable: z.boolean(),
});

export const resumeAnalysisSchema = z.object({
  atsScore: z.number().min(0, 'ATS score cannot be negative').max(100, 'ATS score cannot exceed 100'),
  readabilityScore: z.number().min(0, 'Readability score cannot be negative').max(100, 'Readability score cannot exceed 100'),
  keywordDensity: z.record(z.string(), z.number().min(0, 'Keyword density cannot be negative')),
  suggestions: z.array(analysisSuggestionSchema),
  strengths: z.array(z.string().max(100, 'Strength description too long')),
  weaknesses: z.array(z.string().max(100, 'Weakness description too long')),
});

// Form schemas for creating/updating
export const createResumeSchema = resumeSchema.omit({
  id: true,
  userId: true,
  createdAt: true,
  updatedAt: true,
  metadata: true,
});

export const updateResumeSchema = resumeSchema.partial().omit({
  id: true,
  userId: true,
  createdAt: true,
});

// Section-specific validation
export const workExperienceArraySchema = z.array(workExperienceSchema);
export const educationArraySchema = z.array(educationSchema);
export const projectArraySchema = z.array(projectSchema);
export const skillArraySchema = z.array(skillSchema);
export const certificationArraySchema = z.array(certificationSchema);
export const languageArraySchema = z.array(languageSchema);
export const awardArraySchema = z.array(awardSchema);
export const publicationArraySchema = z.array(publicationSchema);
export const volunteerArraySchema = z.array(volunteerExperienceSchema);

// Export types inferred from schemas
export type PersonalInfoInput = z.infer<typeof personalInfoSchema>;
export type WorkExperienceInput = z.infer<typeof workExperienceSchema>;
export type EducationInput = z.infer<typeof educationSchema>;
export type ProjectInput = z.infer<typeof projectSchema>;
export type SkillInput = z.infer<typeof skillSchema>;
export type CertificationInput = z.infer<typeof certificationSchema>;
export type LanguageInput = z.infer<typeof languageSchema>;
export type AwardInput = z.infer<typeof awardSchema>;
export type PublicationInput = z.infer<typeof publicationSchema>;
export type VolunteerExperienceInput = z.infer<typeof volunteerExperienceSchema>;
export type ResumeInput = z.infer<typeof createResumeSchema>;
export type ResumeUpdateInput = z.infer<typeof updateResumeSchema>;
