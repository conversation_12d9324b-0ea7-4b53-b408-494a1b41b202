const path = require('path')
const webpack = require('webpack')
const CopyWebpackPlugin = require('copy-webpack-plugin')
const HtmlWebpackPlugin = require('html-webpack-plugin')
const MiniCssExtractPlugin = require('mini-css-extract-plugin')

const isProduction = process.env.NODE_ENV === 'production'

// Base configuration
const baseConfig = {
  mode: isProduction ? 'production' : 'development',
  devtool: isProduction ? false : 'cheap-module-source-map',
  
  resolve: {
    extensions: ['.ts', '.tsx', '.js', '.jsx'],
    alias: {
      '@': path.resolve(__dirname, 'src'),
    },
  },

  module: {
    rules: [
      {
        test: /\.tsx?$/,
        use: 'ts-loader',
        exclude: /node_modules/,
      },
      {
        test: /\.css$/,
        use: [
          MiniCssExtractPlugin.loader,
          'css-loader',
          'postcss-loader',
        ],
      },
      {
        test: /\.(png|jpg|jpeg|gif|svg)$/,
        type: 'asset/resource',
        generator: {
          filename: 'images/[name][ext]',
        },
      },
    ],
  },

  plugins: [
    new MiniCssExtractPlugin({
      filename: '[name].css',
    }),
    new webpack.DefinePlugin({
      'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV || 'development'),
    }),
  ],

  optimization: {
    minimize: isProduction,
  },
}

// Chrome extension configuration
const chromeConfig = {
  ...baseConfig,
  name: 'chrome',
  entry: {
    background: './src/background/background.ts',
    content: './src/content/content.ts',
    popup: './src/popup/popup.tsx',
    options: './src/options/options.tsx',
  },

  output: {
    path: path.resolve(__dirname, 'dist/chrome'),
    filename: '[name]/[name].js',
    clean: true,
  },

  plugins: [
    ...baseConfig.plugins,
    
    // Copy manifest and static files
    new CopyWebpackPlugin({
      patterns: [
        {
          from: 'src/manifest/chrome.json',
          to: 'manifest.json',
        },
        {
          from: 'src/icons',
          to: 'icons',
        },
        {
          from: 'src/content/content.css',
          to: 'content/content.css',
        },
      ],
    }),

    // Generate HTML files
    new HtmlWebpackPlugin({
      template: 'src/popup/popup.html',
      filename: 'popup/popup.html',
      chunks: ['popup'],
      inject: 'body',
    }),

    new HtmlWebpackPlugin({
      template: 'src/options/options.html',
      filename: 'options/options.html',
      chunks: ['options'],
      inject: 'body',
    }),
  ],
}

// Firefox extension configuration
const firefoxConfig = {
  ...baseConfig,
  name: 'firefox',
  entry: {
    background: './src/background/background.ts',
    content: './src/content/content.ts',
    popup: './src/popup/popup.tsx',
    options: './src/options/options.tsx',
  },

  output: {
    path: path.resolve(__dirname, 'dist/firefox'),
    filename: '[name]/[name].js',
    clean: true,
  },

  plugins: [
    ...baseConfig.plugins,
    
    // Copy manifest and static files
    new CopyWebpackPlugin({
      patterns: [
        {
          from: 'src/manifest/firefox.json',
          to: 'manifest.json',
        },
        {
          from: 'src/icons',
          to: 'icons',
        },
        {
          from: 'src/content/content.css',
          to: 'content/content.css',
        },
      ],
    }),

    // Generate HTML files
    new HtmlWebpackPlugin({
      template: 'src/popup/popup.html',
      filename: 'popup/popup.html',
      chunks: ['popup'],
      inject: 'body',
    }),

    new HtmlWebpackPlugin({
      template: 'src/options/options.html',
      filename: 'options/options.html',
      chunks: ['options'],
      inject: 'body',
    }),
  ],
}

// Edge extension configuration
const edgeConfig = {
  ...baseConfig,
  name: 'edge',
  entry: {
    background: './src/background/background.ts',
    content: './src/content/content.ts',
    popup: './src/popup/popup.tsx',
    options: './src/options/options.tsx',
  },

  output: {
    path: path.resolve(__dirname, 'dist/edge'),
    filename: '[name]/[name].js',
    clean: true,
  },

  plugins: [
    ...baseConfig.plugins,
    
    // Copy manifest and static files
    new CopyWebpackPlugin({
      patterns: [
        {
          from: 'src/manifest/edge.json',
          to: 'manifest.json',
        },
        {
          from: 'src/icons',
          to: 'icons',
        },
        {
          from: 'src/content/content.css',
          to: 'content/content.css',
        },
      ],
    }),

    // Generate HTML files
    new HtmlWebpackPlugin({
      template: 'src/popup/popup.html',
      filename: 'popup/popup.html',
      chunks: ['popup'],
      inject: 'body',
    }),

    new HtmlWebpackPlugin({
      template: 'src/options/options.html',
      filename: 'options/options.html',
      chunks: ['options'],
      inject: 'body',
    }),
  ],
}

// Export configuration based on target browser
module.exports = (env, argv) => {
  const target = env?.target || 'chrome'
  
  switch (target) {
    case 'firefox':
      return firefoxConfig
    case 'edge':
      return edgeConfig
    case 'chrome':
    default:
      return chromeConfig
  }
}
