'use client';

import { TemplateRenderContext } from '@careercraft/shared/types/template';
import { ModernTemplate } from './modern-template';

interface CreativeTemplateProps {
  context: TemplateRenderContext;
  interactive?: boolean;
  preview?: boolean;
}

export function CreativeTemplate(props: CreativeTemplateProps) {
  // For now, use the modern template as a base
  // In a full implementation, this would have its own unique design
  return <ModernTemplate {...props} />;
}
