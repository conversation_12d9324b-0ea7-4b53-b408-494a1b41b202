import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { aiService } from '@/lib/ai/openai'
import { z } from 'zod'

const generateRequestSchema = z.object({
  type: z.enum(['summary', 'experience', 'skills', 'achievement', 'cover_letter']),
  context: z.object({
    role: z.string().optional(),
    company: z.string().optional(),
    industry: z.string().optional(),
    experience: z.string().optional(),
    skills: z.array(z.string()).optional(),
    achievements: z.array(z.string()).optional(),
  }),
  tone: z.enum(['professional', 'creative', 'technical', 'executive']).optional(),
})

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Parse and validate request body
    const body = await request.json()
    const validatedData = generateRequestSchema.parse(body)

    // Check if user has AI credits or subscription
    const hasAIAccess = await checkUserAIAccess(session.user.id)
    if (!hasAIAccess) {
      return NextResponse.json(
        { error: 'AI content generation requires a premium subscription' },
        { status: 403 }
      )
    }

    // Call AI service to generate content
    const generatedContent = await aiService.generateContent({
      type: validatedData.type,
      context: validatedData.context,
      tone: validatedData.tone,
    })

    // Log AI usage for billing/analytics
    await logAIUsage(session.user.id, 'generate', {
      type: validatedData.type,
      tone: validatedData.tone,
      outputLength: generatedContent.length,
    })

    return NextResponse.json({
      content: generatedContent,
      type: validatedData.type,
      tone: validatedData.tone || 'professional',
      generatedAt: new Date().toISOString(),
    })

  } catch (error) {
    console.error('Error in AI content generation:', error)

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 }
      )
    }

    if (error instanceof Error) {
      // Handle specific AI service errors
      if (error.message.includes('API key')) {
        return NextResponse.json(
          { error: 'AI service configuration error' },
          { status: 500 }
        )
      }

      if (error.message.includes('rate limit')) {
        return NextResponse.json(
          { error: 'AI service rate limit exceeded. Please try again later.' },
          { status: 429 }
        )
      }
    }

    return NextResponse.json(
      { error: 'Failed to generate content' },
      { status: 500 }
    )
  }
}

async function checkUserAIAccess(userId: string): Promise<boolean> {
  // Mock implementation - replace with actual database check
  return true
}

async function logAIUsage(
  userId: string, 
  operation: string, 
  metadata: Record<string, any>
): Promise<void> {
  // Mock implementation - replace with actual logging
  console.log('AI Usage:', { userId, operation, metadata, timestamp: new Date() })
}
