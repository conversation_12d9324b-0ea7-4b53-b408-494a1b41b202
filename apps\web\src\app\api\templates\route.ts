import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@careercraft/database';
import { templateSchema, createTemplateSchema } from '@careercraft/shared/schemas/template';
import { TemplateCategory } from '@careercraft/shared/types/template';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const category = searchParams.get('category') as TemplateCategory | null;
    const search = searchParams.get('search') || '';
    const isPremium = searchParams.get('isPremium');
    const sortBy = searchParams.get('sortBy') || 'popular';

    const skip = (page - 1) * limit;

    const where = {
      ...(category && { category }),
      ...(isPremium !== null && { isPremium: isPremium === 'true' }),
      ...(search && {
        OR: [
          { name: { contains: search, mode: 'insensitive' as const } },
          { description: { contains: search, mode: 'insensitive' as const } },
          { tags: { has: search } },
        ],
      }),
    };

    const orderBy = (() => {
      switch (sortBy) {
        case 'newest':
          return { createdAt: 'desc' as const };
        case 'rating':
          return { rating: 'desc' as const };
        case 'popular':
        default:
          return { usageCount: 'desc' as const };
      }
    })();

    const [templates, total] = await Promise.all([
      prisma.template.findMany({
        where,
        skip,
        take: limit,
        orderBy,
      }),
      prisma.template.count({ where }),
    ]);

    return NextResponse.json({
      templates,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
      filters: {
        category,
        isPremium: isPremium ? isPremium === 'true' : undefined,
        search: search || undefined,
      },
    });
  } catch (error) {
    console.error('Error fetching templates:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Check if user has permission to create templates
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
    });

    if (!user || user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Forbidden - Admin access required' },
        { status: 403 }
      );
    }

    const body = await request.json();
    
    // Validate the request body
    const validationResult = createTemplateSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          error: 'Validation failed',
          details: validationResult.error.errors,
        },
        { status: 400 }
      );
    }

    const templateData = validationResult.data;

    // Create the template in the database
    const template = await prisma.template.create({
      data: {
        ...templateData,
        createdBy: session.user.id,
        usageCount: 0,
        rating: 0,
        reviewCount: 0,
      },
    });

    return NextResponse.json(template, { status: 201 });
  } catch (error) {
    console.error('Error creating template:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
