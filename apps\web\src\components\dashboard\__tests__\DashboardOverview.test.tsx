import { describe, it, expect, vi, beforeEach } from 'vitest'
import { screen, fireEvent, waitFor } from '@testing-library/react'
import { DashboardOverview } from '../DashboardOverview'
import { render, mockSession, mockDashboardStats, mockRecentActivity } from '@/test/utils'

// Mock the useRouter hook
const mockPush = vi.fn()
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: mockPush,
  }),
}))

// Mock next-auth
vi.mock('next-auth/react', () => ({
  useSession: () => ({
    data: mockSession,
    status: 'authenticated',
  }),
}))

describe('DashboardOverview', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders welcome message with user name', async () => {
    render(<DashboardOverview />)

    await waitFor(() => {
      expect(screen.getByText(/Welcome back, Test!/)).toBeInTheDocument()
      expect(screen.getByText(/Ready to craft your perfect resume/)).toBeInTheDocument()
    })
  })

  it('displays dashboard statistics', async () => {
    render(<DashboardOverview />)

    await waitFor(() => {
      // Check for stats cards
      expect(screen.getByText('Total Resumes')).toBeInTheDocument()
      expect(screen.getByText('Downloads')).toBeInTheDocument()
      expect(screen.getByText('Profile Views')).toBeInTheDocument()
      expect(screen.getByText('Active Resumes')).toBeInTheDocument()

      // Check for stat values (these are mocked in the component)
      expect(screen.getByText('3')).toBeInTheDocument() // Total resumes
      expect(screen.getByText('12')).toBeInTheDocument() // Downloads
      expect(screen.getByText('45')).toBeInTheDocument() // Views
      expect(screen.getByText('2')).toBeInTheDocument() // Active resumes
    })
  })

  it('displays quick actions section', async () => {
    render(<DashboardOverview />)

    await waitFor(() => {
      expect(screen.getByText('Quick Actions')).toBeInTheDocument()
      expect(screen.getByText('Create New Resume')).toBeInTheDocument()
      expect(screen.getByText('Browse Templates')).toBeInTheDocument()
      expect(screen.getByText('View All Resumes')).toBeInTheDocument()
      expect(screen.getByText('View Analytics')).toBeInTheDocument()
    })
  })

  it('displays recent activity section', async () => {
    render(<DashboardOverview />)

    await waitFor(() => {
      expect(screen.getByText('Recent Activity')).toBeInTheDocument()
      expect(screen.getByText('Software Engineer Resume')).toBeInTheDocument()
      expect(screen.getByText('Product Manager Resume')).toBeInTheDocument()
      expect(screen.getByText('Data Scientist Resume')).toBeInTheDocument()
    })
  })

  it('navigates to create resume when main create button is clicked', async () => {
    render(<DashboardOverview />)

    await waitFor(() => {
      const createButtons = screen.getAllByText('Create Resume')
      const mainCreateButton = createButtons[0] // The main prominent button
      fireEvent.click(mainCreateButton)
    })

    expect(mockPush).toHaveBeenCalledWith('/dashboard/resumes/new')
  })

  it('navigates to correct pages when quick action buttons are clicked', async () => {
    render(<DashboardOverview />)

    await waitFor(() => {
      // Test Create New Resume quick action
      const createNewButton = screen.getByRole('button', { name: /create new resume/i })
      fireEvent.click(createNewButton)
      expect(mockPush).toHaveBeenCalledWith('/dashboard/resumes/new')

      // Test Browse Templates quick action
      const templatesButton = screen.getByRole('button', { name: /browse templates/i })
      fireEvent.click(templatesButton)
      expect(mockPush).toHaveBeenCalledWith('/dashboard/templates')

      // Test View All Resumes quick action
      const resumesButton = screen.getByRole('button', { name: /view all resumes/i })
      fireEvent.click(resumesButton)
      expect(mockPush).toHaveBeenCalledWith('/dashboard/resumes')

      // Test View Analytics quick action
      const analyticsButton = screen.getByRole('button', { name: /view analytics/i })
      fireEvent.click(analyticsButton)
      expect(mockPush).toHaveBeenCalledWith('/dashboard/analytics')
    })
  })

  it('shows loading state initially', () => {
    render(<DashboardOverview />)

    // Should show loading skeletons initially
    const loadingElements = document.querySelectorAll('.animate-pulse')
    expect(loadingElements.length).toBeGreaterThan(0)
  })

  it('displays activity badges with correct colors', async () => {
    render(<DashboardOverview />)

    await waitFor(() => {
      // Check for activity type badges
      expect(screen.getByText('created')).toBeInTheDocument()
      expect(screen.getByText('downloaded')).toBeInTheDocument()
      expect(screen.getByText('updated')).toBeInTheDocument()
    })
  })

  it('formats time ago correctly', async () => {
    render(<DashboardOverview />)

    await waitFor(() => {
      // Check for time ago text (mocked to return "2 hours ago")
      const timeElements = screen.getAllByText(/ago/)
      expect(timeElements.length).toBeGreaterThan(0)
    })
  })

  it('applies glassmorphism styling to cards', async () => {
    render(<DashboardOverview />)

    await waitFor(() => {
      // Check for glass effect classes
      const glassCards = document.querySelectorAll('.glass-card')
      expect(glassCards.length).toBeGreaterThan(0)

      const glassPanels = document.querySelectorAll('.glass-panel')
      expect(glassPanels.length).toBeGreaterThan(0)
    })
  })

  it('has hover effects on stat cards', async () => {
    render(<DashboardOverview />)

    await waitFor(() => {
      // Check for hover transition classes
      const hoverElements = document.querySelectorAll('.hover\\:scale-105')
      expect(hoverElements.length).toBeGreaterThan(0)
    })
  })

  it('displays gradient text for the welcome title', async () => {
    render(<DashboardOverview />)

    await waitFor(() => {
      const titleElement = screen.getByText(/Welcome back, Test!/)
      expect(titleElement).toHaveClass('bg-gradient-to-r', 'from-blue-600', 'to-purple-600', 'bg-clip-text', 'text-transparent')
    })
  })

  it('shows empty state when no recent activity', async () => {
    // Mock empty activity
    const emptyActivityComponent = () => {
      const [recentActivity, setRecentActivity] = React.useState([])
      
      React.useEffect(() => {
        setRecentActivity([])
      }, [])

      return <DashboardOverview />
    }

    render(<DashboardOverview />)

    // The component should handle empty activity gracefully
    // Since we're mocking data, we'll check that the component renders without errors
    await waitFor(() => {
      expect(screen.getByText('Recent Activity')).toBeInTheDocument()
    })
  })

  it('handles user without name gracefully', () => {
    const sessionWithoutName = {
      ...mockSession,
      user: {
        ...mockSession.user,
        name: null,
      },
    }

    render(<DashboardOverview />, { session: sessionWithoutName })

    // Should show "User" as fallback
    expect(screen.getByText(/Welcome back, User!/)).toBeInTheDocument()
  })

  it('is responsive on different screen sizes', async () => {
    render(<DashboardOverview />)

    await waitFor(() => {
      // Check for responsive grid classes
      const responsiveGrids = document.querySelectorAll('[class*="md:grid-cols"], [class*="lg:grid-cols"]')
      expect(responsiveGrids.length).toBeGreaterThan(0)
    })
  })

  it('displays correct icons for different sections', async () => {
    render(<DashboardOverview />)

    await waitFor(() => {
      // Check that icons are rendered (mocked as div elements)
      const icons = document.querySelectorAll('[data-testid="mock-icon"]')
      expect(icons.length).toBeGreaterThan(0)
    })
  })

  it('handles click events on stat cards', async () => {
    render(<DashboardOverview />)

    await waitFor(() => {
      const statCards = document.querySelectorAll('.glass-card')
      const firstStatCard = statCards[0]
      
      // Stat cards should be clickable (have hover effects)
      expect(firstStatCard).toHaveClass('hover:scale-105')
    })
  })
})
