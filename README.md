# CareerCraft - AI Powered Resume Platform

## 🎯 Vision
To create an intelligent career development platform that empowers professionals to articulate their value effortlessly. We move beyond static document creation to a dynamic system that grows with the user's career, ensuring they are always interview-ready.

## 🏗️ Architecture Overview

### Tech Stack
- **Frontend**: Next.js 14 with TypeScript
- **Styling**: TailwindCSS + Framer Motion
- **State Management**: Zustand
- **Database**: PostgreSQL with Prisma ORM
- **Authentication**: NextAuth.js
- **AI Services**: OpenAI GPT-4
- **PDF Generation**: Puppeteer
- **Deployment**: Vercel (Frontend) + Railway/Render (Backend Services)

### Microservices Architecture
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   API Gateway   │    │   Auth Service  │
│   (Next.js)     │◄──►│   (Next.js API) │◄──►│   (NextAuth)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                ┌───────────────┼───────────────┐
                │               │               │
        ┌───────▼──────┐ ┌──────▼──────┐ ┌─────▼──────┐
        │ Resume       │ │ AI Content  │ │ PDF Export │
        │ Service      │ │ Service     │ │ Service    │
        └──────────────┘ └─────────────┘ └────────────┘
                │               │               │
        ┌───────▼──────┐ ┌──────▼──────┐ ┌─────▼──────┐
        │ PostgreSQL   │ │ OpenAI API  │ │ File       │
        │ Database     │ │             │ │ Storage    │
        └──────────────┘ └─────────────┘ └────────────┘
```

## ✨ AI-Powered Features

### 🤖 Intelligent Resume Builder
- **Smart Content Generation**: AI-crafted professional summaries and job descriptions
- **ATS Optimization**: Real-time compatibility scoring and keyword analysis
- **Contextual Suggestions**: Intelligent recommendations based on job requirements
- **Grammar & Style**: Advanced language processing for professional writing

### 📊 Analytics Dashboard
- **Performance Tracking**: Monitor resume effectiveness and improvements
- **Keyword Analysis**: Detailed keyword density and optimization reports
- **ATS Scoring**: Real-time applicant tracking system compatibility
- **Usage Insights**: Track AI feature usage and optimization impact

### 🎨 Template Intelligence
- **Smart Matching**: AI recommends templates based on industry and role
- **Dynamic Layouts**: Responsive templates that adapt to content length
- **Professional Standards**: All templates optimized for ATS systems
- **Glassmorphism Design**: Modern UI with smooth animations

## 🚀 Quick Start

### Prerequisites
- Node.js 18+
- PostgreSQL 14+
- npm or yarn

### Installation
```bash
# Clone the repository
git clone <repository-url>
cd careercraft-v2

# Install dependencies
npm install

# Set up environment variables
cp .env.example .env.local

# Set up database
npm run db:setup

# Start development server
npm run dev
```

## 📁 Project Structure
```
careercraft-v2/
├── apps/
│   ├── web/                    # Next.js frontend
│   ├── api/                    # API services
│   └── docs/                   # Documentation
├── packages/
│   ├── ui/                     # Shared UI components
│   ├── database/               # Database schema & migrations
│   ├── types/                  # Shared TypeScript types
│   └── utils/                  # Shared utilities
├── docs/
│   ├── architecture/           # Architecture diagrams
│   ├── api/                    # API documentation
│   └── user-flows/             # User flow diagrams
└── scripts/                    # Build and deployment scripts
```

## 🎯 Development Phases

### Phase 1 (MVP) - Core Features ✅ COMPLETE
- [x] User authentication & profile management
- [x] Resume builder with AI assistance
- [x] Professional templates (50+ templates)
- [x] PDF export functionality
- [x] Basic ATS optimization

### Phase 2 (Growth) - Enhanced Features ✅ COMPLETE
- [x] Cover letter generator
- [x] Advanced AI content suggestions
- [x] Template gallery with filtering
- [x] Resume analytics & scoring
- [x] Comprehensive testing suite

### Phase 3 (AI Integration) - AI Features ✅ COMPLETE
- [x] OpenAI GPT-4 integration
- [x] Real-time ATS analysis
- [x] Keyword optimization engine
- [x] AI-powered content generation
- [x] Smart resume optimization
- [x] Performance analytics dashboard

### Phase 4 (Scale) - Premium Features 🚧 PLANNED
- [ ] Real-time collaboration
- [ ] A/B testing for resumes
- [ ] Career path simulation
- [ ] Enterprise solutions
- [ ] LinkedIn integration

## 📊 Key Metrics
- **North Star Metric**: Monthly Active Resumes (MAR)
- **Performance**: LCP < 2.5s, API latency P95 < 250ms
- **Quality**: >80% test coverage, strict TypeScript
- **Reliability**: 99.9% uptime target

## 🔧 Development Guidelines
- TypeScript strict mode enabled
- ESLint + Prettier enforced
- Comprehensive testing (Unit + Integration + E2E)
- Automated CI/CD pipeline
- Real-time monitoring with Sentry

## 📚 Documentation
- [Architecture Overview](./docs/architecture/README.md)
- [API Documentation](./docs/api/README.md)
- [User Flow Diagrams](./docs/user-flows/README.md)
- [Development Setup](./docs/development/README.md)

## 🤝 Contributing
Please read our [Contributing Guidelines](./CONTRIBUTING.md) before submitting any changes.

## 📄 License
This project is licensed under the MIT License - see the [LICENSE](./LICENSE) file for details.
