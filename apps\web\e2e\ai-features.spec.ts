/**
 * AI Features End-to-End Tests
 * 
 * Complete user workflow tests for AI-powered features:
 * - Resume optimization with AI
 * - ATS analysis and scoring
 * - Content generation workflows
 * - Cover letter generation
 * - Keyword optimization
 */

import { test, expect, Page } from '@playwright/test'

test.describe('AI Features E2E', () => {
  let page: Page

  test.beforeEach(async ({ browser }) => {
    page = await browser.newPage()
    
    // Mock authentication
    await page.goto('/auth/signin')
    await page.fill('[data-testid="email"]', '<EMAIL>')
    await page.fill('[data-testid="password"]', 'password123')
    await page.click('[data-testid="signin-button"]')
    
    // Wait for redirect to dashboard
    await page.waitForURL('/dashboard')
  })

  test.describe('Resume AI Optimization', () => {
    test('should optimize resume with AI suggestions', async () => {
      // Navigate to resume builder
      await page.click('[data-testid="create-resume"]')
      await page.waitForURL('/dashboard/resumes/new')

      // Fill basic resume information
      await page.fill('[data-testid="first-name"]', '<PERSON>')
      await page.fill('[data-testid="last-name"]', 'Doe')
      await page.fill('[data-testid="email"]', '<EMAIL>')
      await page.fill('[data-testid="phone"]', '******-0123')

      // Add professional summary
      await page.fill('[data-testid="summary"]', 'I am a developer with some experience')

      // Open AI Assistant
      await page.click('[data-testid="ai-assistant-toggle"]')
      await expect(page.locator('[data-testid="ai-assistant-panel"]')).toBeVisible()

      // Generate improved summary
      await page.click('[data-testid="generate-summary"]')
      await page.fill('[data-testid="generation-context"]', 'Senior Software Engineer with 5 years experience in React and Node.js')
      await page.click('[data-testid="generate-content-button"]')

      // Wait for AI generation
      await expect(page.locator('[data-testid="generated-content"]')).toBeVisible({ timeout: 10000 })
      
      // Apply generated content
      await page.click('[data-testid="apply-generated-content"]')
      
      // Verify content was applied
      const summaryField = page.locator('[data-testid="summary"]')
      await expect(summaryField).not.toHaveValue('I am a developer with some experience')
    })

    test('should analyze ATS compatibility', async () => {
      // Navigate to existing resume
      await page.goto('/dashboard/resumes/test-resume-id/edit')

      // Open AI Assistant
      await page.click('[data-testid="ai-assistant-toggle"]')

      // Add job description for analysis
      await page.fill('[data-testid="job-description"]', `
        We are looking for a Senior Software Engineer with:
        - 5+ years of React experience
        - Node.js and Express.js expertise
        - AWS cloud experience
        - Agile development experience
        - Strong communication skills
      `)

      // Run ATS analysis
      await page.click('[data-testid="analyze-ats"]')

      // Wait for analysis results
      await expect(page.locator('[data-testid="ats-score"]')).toBeVisible({ timeout: 15000 })
      
      // Verify ATS score is displayed
      const atsScore = await page.locator('[data-testid="ats-score-value"]').textContent()
      expect(parseInt(atsScore || '0')).toBeGreaterThan(0)

      // Check for missing keywords
      await expect(page.locator('[data-testid="missing-keywords"]')).toBeVisible()
      
      // Check for improvement suggestions
      await expect(page.locator('[data-testid="ats-suggestions"]')).toBeVisible()
    })

    test('should show keyword optimization suggestions', async () => {
      await page.goto('/dashboard/resumes/test-resume-id/edit')

      // Open keyword optimizer
      await page.click('[data-testid="keyword-optimizer"]')
      await expect(page.locator('[data-testid="keyword-optimizer-panel"]')).toBeVisible()

      // Add target job description
      await page.fill('[data-testid="target-job-description"]', 'React developer with TypeScript and GraphQL experience')

      // Analyze keywords
      await page.click('[data-testid="analyze-keywords"]')

      // Wait for keyword analysis
      await expect(page.locator('[data-testid="keyword-density"]')).toBeVisible({ timeout: 10000 })
      
      // Check for keyword suggestions
      await expect(page.locator('[data-testid="keyword-suggestions"]')).toBeVisible()
      
      // Apply keyword optimization
      await page.click('[data-testid="optimize-keywords"]')
      
      // Verify optimization was applied
      await expect(page.locator('[data-testid="optimization-applied"]')).toBeVisible()
    })
  })

  test.describe('Cover Letter Generation', () => {
    test('should generate personalized cover letter', async () => {
      // Navigate to cover letter generator
      await page.goto('/dashboard/cover-letters/new')

      // Fill job application details
      await page.fill('[data-testid="company-name"]', 'TechCorp Inc.')
      await page.fill('[data-testid="position-title"]', 'Senior Software Engineer')
      await page.fill('[data-testid="hiring-manager"]', 'Sarah Johnson')

      // Add job description
      await page.fill('[data-testid="job-description"]', `
        We are seeking a Senior Software Engineer to join our growing team.
        The ideal candidate will have experience with React, Node.js, and AWS.
        You will be responsible for building scalable web applications.
      `)

      // Select resume to base cover letter on
      await page.selectOption('[data-testid="base-resume"]', 'test-resume-id')

      // Generate cover letter
      await page.click('[data-testid="generate-cover-letter"]')

      // Wait for generation
      await expect(page.locator('[data-testid="generated-cover-letter"]')).toBeVisible({ timeout: 15000 })

      // Verify cover letter content
      const coverLetterContent = await page.locator('[data-testid="cover-letter-content"]').textContent()
      expect(coverLetterContent).toContain('TechCorp Inc.')
      expect(coverLetterContent).toContain('Senior Software Engineer')
      expect(coverLetterContent).toContain('Dear Sarah Johnson')

      // Save cover letter
      await page.click('[data-testid="save-cover-letter"]')
      await expect(page.locator('[data-testid="save-success"]')).toBeVisible()
    })

    test('should customize cover letter tone and style', async () => {
      await page.goto('/dashboard/cover-letters/new')

      // Fill basic details
      await page.fill('[data-testid="company-name"]', 'StartupXYZ')
      await page.fill('[data-testid="position-title"]', 'Frontend Developer')

      // Select creative tone
      await page.selectOption('[data-testid="tone-selector"]', 'creative')
      
      // Select startup style
      await page.selectOption('[data-testid="style-selector"]', 'startup')

      // Generate cover letter
      await page.click('[data-testid="generate-cover-letter"]')

      // Wait for generation
      await expect(page.locator('[data-testid="generated-cover-letter"]')).toBeVisible({ timeout: 15000 })

      // Verify tone is reflected in content
      const content = await page.locator('[data-testid="cover-letter-content"]').textContent()
      expect(content).toMatch(/(innovative|creative|dynamic|passionate)/i)
    })
  })

  test.describe('AI Analytics Dashboard', () => {
    test('should display AI usage analytics', async () => {
      // Navigate to analytics
      await page.goto('/dashboard/analytics')

      // Check AI usage section
      await expect(page.locator('[data-testid="ai-usage-analytics"]')).toBeVisible()

      // Verify metrics are displayed
      await expect(page.locator('[data-testid="total-optimizations"]')).toBeVisible()
      await expect(page.locator('[data-testid="average-ats-score"]')).toBeVisible()
      await expect(page.locator('[data-testid="improvement-rate"]')).toBeVisible()
      await expect(page.locator('[data-testid="time-saved"]')).toBeVisible()

      // Check usage charts
      await expect(page.locator('[data-testid="usage-chart"]')).toBeVisible()
      await expect(page.locator('[data-testid="score-trend-chart"]')).toBeVisible()
    })

    test('should show AI feature usage breakdown', async () => {
      await page.goto('/dashboard/analytics')

      // Check feature usage breakdown
      await expect(page.locator('[data-testid="feature-usage-breakdown"]')).toBeVisible()

      // Verify individual feature metrics
      await expect(page.locator('[data-testid="content-generation-usage"]')).toBeVisible()
      await expect(page.locator('[data-testid="ats-analysis-usage"]')).toBeVisible()
      await expect(page.locator('[data-testid="keyword-optimization-usage"]')).toBeVisible()
      await expect(page.locator('[data-testid="cover-letter-generation-usage"]')).toBeVisible()
    })
  })

  test.describe('AI Error Handling', () => {
    test('should handle AI service errors gracefully', async () => {
      // Mock API error
      await page.route('/api/ai/generate', route => {
        route.fulfill({
          status: 500,
          contentType: 'application/json',
          body: JSON.stringify({ error: 'AI service temporarily unavailable' })
        })
      })

      await page.goto('/dashboard/resumes/test-resume-id/edit')
      await page.click('[data-testid="ai-assistant-toggle"]')
      await page.click('[data-testid="generate-summary"]')

      // Verify error message is displayed
      await expect(page.locator('[data-testid="ai-error-message"]')).toBeVisible()
      await expect(page.locator('[data-testid="ai-error-message"]')).toContainText('temporarily unavailable')

      // Verify retry option is available
      await expect(page.locator('[data-testid="retry-ai-request"]')).toBeVisible()
    })

    test('should handle rate limiting', async () => {
      // Mock rate limit error
      await page.route('/api/ai/**', route => {
        route.fulfill({
          status: 429,
          contentType: 'application/json',
          body: JSON.stringify({ error: 'Rate limit exceeded. Please try again later.' })
        })
      })

      await page.goto('/dashboard/resumes/test-resume-id/edit')
      await page.click('[data-testid="ai-assistant-toggle"]')
      await page.click('[data-testid="generate-summary"]')

      // Verify rate limit message
      await expect(page.locator('[data-testid="rate-limit-message"]')).toBeVisible()
      await expect(page.locator('[data-testid="upgrade-prompt"]')).toBeVisible()
    })
  })

  test.describe('AI Performance', () => {
    test('should complete AI operations within reasonable time', async () => {
      await page.goto('/dashboard/resumes/test-resume-id/edit')
      await page.click('[data-testid="ai-assistant-toggle"]')

      // Measure content generation time
      const startTime = Date.now()
      await page.click('[data-testid="generate-summary"]')
      await page.fill('[data-testid="generation-context"]', 'Software Engineer')
      await page.click('[data-testid="generate-content-button"]')
      
      await expect(page.locator('[data-testid="generated-content"]')).toBeVisible({ timeout: 30000 })
      const endTime = Date.now()

      // Verify reasonable response time (under 30 seconds)
      expect(endTime - startTime).toBeLessThan(30000)
    })

    test('should handle concurrent AI requests', async () => {
      await page.goto('/dashboard/resumes/test-resume-id/edit')
      await page.click('[data-testid="ai-assistant-toggle"]')

      // Start multiple AI operations simultaneously
      const operations = [
        page.click('[data-testid="generate-summary"]'),
        page.click('[data-testid="analyze-ats"]'),
        page.click('[data-testid="optimize-keywords"]')
      ]

      await Promise.all(operations)

      // Verify all operations complete successfully
      await expect(page.locator('[data-testid="ai-operations-complete"]')).toBeVisible({ timeout: 45000 })
    })
  })

  test.describe('AI Content Quality', () => {
    test('should generate relevant and professional content', async () => {
      await page.goto('/dashboard/resumes/test-resume-id/edit')
      await page.click('[data-testid="ai-assistant-toggle"]')

      // Generate professional summary
      await page.click('[data-testid="generate-summary"]')
      await page.fill('[data-testid="generation-context"]', 'Senior Data Scientist with machine learning expertise')
      await page.selectOption('[data-testid="tone-selector"]', 'professional')
      await page.click('[data-testid="generate-content-button"]')

      await expect(page.locator('[data-testid="generated-content"]')).toBeVisible({ timeout: 15000 })

      // Verify content quality
      const generatedContent = await page.locator('[data-testid="generated-content"]').textContent()
      expect(generatedContent).toMatch(/(data scientist|machine learning|analytics|statistical)/i)
      expect(generatedContent?.length || 0).toBeGreaterThan(100) // Reasonable length
      expect(generatedContent?.length || 0).toBeLessThan(500) // Not too long
    })
  })
})
