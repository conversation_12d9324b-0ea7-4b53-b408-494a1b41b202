import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { aiService } from '@/lib/ai/openai'
import { z } from 'zod'

const analyzeRequestSchema = z.object({
  resumeContent: z.string().min(1, 'Resume content is required'),
  jobDescription: z.string().min(1, 'Job description is required'),
  targetKeywords: z.array(z.string()).optional(),
})

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Parse and validate request body
    const body = await request.json()
    const validatedData = analyzeRequestSchema.parse(body)

    // Check if user has AI credits or subscription
    const hasAIAccess = await checkUserAIAccess(session.user.id)
    if (!hasAIAccess) {
      return NextResponse.json(
        { error: 'ATS analysis requires a premium subscription' },
        { status: 403 }
      )
    }

    // Call AI service to analyze ATS compatibility
    const analysisResult = await aiService.analyzeATSCompatibility({
      resumeContent: validatedData.resumeContent,
      jobDescription: validatedData.jobDescription,
      targetKeywords: validatedData.targetKeywords,
    })

    // Generate keyword suggestions
    const keywordSuggestions = await aiService.generateKeywordSuggestions(
      validatedData.jobDescription,
      validatedData.resumeContent
    )

    // Log AI usage for billing/analytics
    await logAIUsage(session.user.id, 'analyze', {
      resumeLength: validatedData.resumeContent.length,
      jobDescriptionLength: validatedData.jobDescription.length,
      score: analysisResult.score,
    })

    return NextResponse.json({
      ...analysisResult,
      keywordSuggestions,
      analyzedAt: new Date().toISOString(),
    })

  } catch (error) {
    console.error('Error in ATS analysis:', error)

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 }
      )
    }

    if (error instanceof Error) {
      // Handle specific AI service errors
      if (error.message.includes('API key')) {
        return NextResponse.json(
          { error: 'AI service configuration error' },
          { status: 500 }
        )
      }

      if (error.message.includes('rate limit')) {
        return NextResponse.json(
          { error: 'AI service rate limit exceeded. Please try again later.' },
          { status: 429 }
        )
      }
    }

    return NextResponse.json(
      { error: 'Failed to analyze resume' },
      { status: 500 }
    )
  }
}

async function checkUserAIAccess(userId: string): Promise<boolean> {
  // Mock implementation - replace with actual database check
  return true
}

async function logAIUsage(
  userId: string, 
  operation: string, 
  metadata: Record<string, any>
): Promise<void> {
  // Mock implementation - replace with actual logging
  console.log('AI Usage:', { userId, operation, metadata, timestamp: new Date() })
}
