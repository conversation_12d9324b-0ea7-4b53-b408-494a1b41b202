#!/bin/bash

# CareerCraft Setup Script
# This script sets up the development environment for CareerCraft

set -e

echo "🚀 Setting up CareerCraft Development Environment"
echo "=================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Node.js is installed
check_node() {
    print_status "Checking Node.js installation..."
    if command -v node &> /dev/null; then
        NODE_VERSION=$(node --version)
        print_success "Node.js is installed: $NODE_VERSION"
        
        # Check if version is >= 18
        MAJOR_VERSION=$(echo $NODE_VERSION | cut -d'.' -f1 | sed 's/v//')
        if [ "$MAJOR_VERSION" -lt 18 ]; then
            print_error "Node.js version 18 or higher is required. Current version: $NODE_VERSION"
            exit 1
        fi
    else
        print_error "Node.js is not installed. Please install Node.js 18 or higher."
        exit 1
    fi
}

# Check if PostgreSQL is available
check_postgres() {
    print_status "Checking PostgreSQL availability..."
    if command -v psql &> /dev/null; then
        print_success "PostgreSQL client is available"
    else
        print_warning "PostgreSQL client not found. You'll need to set up a PostgreSQL database."
        print_warning "You can use a cloud service like Supabase, Railway, or Neon."
    fi
}

# Install dependencies
install_dependencies() {
    print_status "Installing dependencies..."
    
    if command -v npm &> /dev/null; then
        npm install
        print_success "Dependencies installed successfully"
    else
        print_error "npm is not available. Please install Node.js with npm."
        exit 1
    fi
}

# Setup environment variables
setup_env() {
    print_status "Setting up environment variables..."
    
    if [ ! -f ".env.local" ]; then
        cp .env.example .env.local
        print_success "Created .env.local from .env.example"
        print_warning "Please update .env.local with your actual configuration values:"
        print_warning "  - DATABASE_URL (PostgreSQL connection string)"
        print_warning "  - NEXTAUTH_SECRET (random secret key)"
        print_warning "  - OPENAI_API_KEY (for AI features)"
        print_warning "  - OAuth provider credentials (Google, GitHub)"
    else
        print_warning ".env.local already exists. Please verify your configuration."
    fi
}

# Generate Prisma client
setup_database() {
    print_status "Setting up database..."
    
    # Generate Prisma client
    npm run db:generate
    print_success "Prisma client generated"
    
    print_warning "Next steps for database setup:"
    print_warning "1. Ensure your PostgreSQL database is running"
    print_warning "2. Update DATABASE_URL in .env.local"
    print_warning "3. Run 'npm run db:push' to create tables"
    print_warning "4. Optionally run 'npm run db:seed' to add sample data"
}

# Setup Git hooks
setup_git_hooks() {
    print_status "Setting up Git hooks..."
    
    if [ -d ".git" ]; then
        npm run prepare
        print_success "Git hooks configured with Husky"
    else
        print_warning "Not a Git repository. Skipping Git hooks setup."
    fi
}

# Create necessary directories
create_directories() {
    print_status "Creating necessary directories..."
    
    mkdir -p apps/web/src/components/ui
    mkdir -p apps/web/src/lib
    mkdir -p apps/web/src/hooks
    mkdir -p apps/web/src/store
    mkdir -p apps/web/public/images
    mkdir -p packages/ui/src
    mkdir -p packages/utils/src
    
    print_success "Directory structure created"
}

# Main setup function
main() {
    echo
    print_status "Starting CareerCraft setup..."
    echo
    
    check_node
    check_postgres
    create_directories
    install_dependencies
    setup_env
    setup_database
    setup_git_hooks
    
    echo
    print_success "🎉 CareerCraft setup completed!"
    echo
    print_status "Next steps:"
    echo "1. Update your .env.local file with actual values"
    echo "2. Set up your PostgreSQL database"
    echo "3. Run 'npm run db:push' to create database tables"
    echo "4. Run 'npm run dev' to start the development server"
    echo
    print_status "Useful commands:"
    echo "  npm run dev          - Start development server"
    echo "  npm run build        - Build for production"
    echo "  npm run test         - Run tests"
    echo "  npm run lint         - Run linting"
    echo "  npm run db:studio    - Open Prisma Studio"
    echo "  npm run db:migrate   - Run database migrations"
    echo
    print_status "Documentation:"
    echo "  README.md                     - Project overview"
    echo "  docs/architecture/README.md   - Architecture documentation"
    echo "  docs/user-flows/README.md     - User flow diagrams"
    echo
    print_success "Happy coding! 🚀"
}

# Run main function
main
