/**
 * Autofill Engine
 * 
 * Intelligently populates form fields with user data,
 * handling validation, formatting, and error recovery.
 */

export interface AutofillData {
  [fieldName: string]: any
}

export interface AutofillResult {
  success: boolean
  filledFields: number
  totalFields: number
  errors: AutofillError[]
  warnings: string[]
}

export interface AutofillError {
  field: string
  error: string
  element?: HTMLElement
}

export interface DetectedForm {
  element: HTMLFormElement
  fields: FormField[]
  confidence: number
  type: string
  metadata: any
}

export interface FormField {
  element: HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
  type: string
  label: string
  placeholder: string
  required: boolean
  confidence: number
  mappedTo?: string
}

export class AutofillEngine {
  private fillHistory: Map<string, any> = new Map()
  private validationRules: Map<string, ValidationRule> = new Map()

  constructor() {
    this.initializeValidationRules()
  }

  /**
   * Fill a form with provided data
   */
  async fillForm(form: DetectedForm, data: AutofillData): Promise<AutofillResult> {
    const result: AutofillResult = {
      success: true,
      filledFields: 0,
      totalFields: form.fields.length,
      errors: [],
      warnings: []
    }

    try {
      // Pre-fill validation
      const validationResult = this.validateFormData(form, data)
      if (!validationResult.valid) {
        result.warnings.push(...validationResult.warnings)
      }

      // Fill fields sequentially
      for (const field of form.fields) {
        try {
          const filled = await this.fillField(field, data)
          if (filled) {
            result.filledFields++
          }
        } catch (error) {
          result.errors.push({
            field: field.mappedTo || field.label,
            error: error.message,
            element: field.element
          })
        }
      }

      // Post-fill validation
      await this.validateFilledForm(form, result)

      // Store fill history
      this.storeFillHistory(form, data, result)

      result.success = result.errors.length === 0

    } catch (error) {
      result.success = false
      result.errors.push({
        field: 'form',
        error: `Form filling failed: ${error.message}`
      })
    }

    return result
  }

  /**
   * Fill a single form field
   */
  private async fillField(field: FormField, data: AutofillData): Promise<boolean> {
    if (!field.mappedTo || !data.hasOwnProperty(field.mappedTo)) {
      return false
    }

    const value = data[field.mappedTo]
    if (value === null || value === undefined) {
      return false
    }

    try {
      // Format value for field type
      const formattedValue = this.formatValueForField(value, field)
      
      // Set field value based on element type
      const success = await this.setFieldValue(field.element, formattedValue, field.type)
      
      if (success) {
        // Trigger change events
        this.triggerChangeEvents(field.element)
        
        // Add visual feedback
        this.addVisualFeedback(field.element, 'success')
        
        return true
      }
    } catch (error) {
      this.addVisualFeedback(field.element, 'error')
      throw error
    }

    return false
  }

  /**
   * Set value for different field types
   */
  private async setFieldValue(
    element: HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement,
    value: any,
    fieldType: string
  ): Promise<boolean> {
    try {
      if (element instanceof HTMLSelectElement) {
        return this.setSelectValue(element, value)
      } else if (element instanceof HTMLInputElement) {
        return this.setInputValue(element, value, fieldType)
      } else if (element instanceof HTMLTextAreaElement) {
        return this.setTextAreaValue(element, value)
      }
    } catch (error) {
      console.error('Failed to set field value:', error)
      return false
    }

    return false
  }

  /**
   * Set value for input elements
   */
  private setInputValue(element: HTMLInputElement, value: any, fieldType: string): boolean {
    switch (element.type.toLowerCase()) {
      case 'text':
      case 'email':
      case 'tel':
      case 'url':
        element.value = String(value)
        return true

      case 'number':
        const numValue = Number(value)
        if (!isNaN(numValue)) {
          element.value = String(numValue)
          return true
        }
        return false

      case 'date':
        const dateValue = this.formatDateForInput(value)
        if (dateValue) {
          element.value = dateValue
          return true
        }
        return false

      case 'checkbox':
        element.checked = Boolean(value)
        return true

      case 'radio':
        if (element.value === String(value)) {
          element.checked = true
          return true
        }
        return false

      case 'file':
        // File inputs cannot be programmatically set for security reasons
        return false

      default:
        element.value = String(value)
        return true
    }
  }

  /**
   * Set value for select elements
   */
  private setSelectValue(element: HTMLSelectElement, value: any): boolean {
    const stringValue = String(value).toLowerCase()
    
    // Try exact match first
    for (const option of element.options) {
      if (option.value === stringValue || option.text.toLowerCase() === stringValue) {
        element.selectedIndex = option.index
        return true
      }
    }

    // Try partial match
    for (const option of element.options) {
      if (option.text.toLowerCase().includes(stringValue) || 
          stringValue.includes(option.text.toLowerCase())) {
        element.selectedIndex = option.index
        return true
      }
    }

    return false
  }

  /**
   * Set value for textarea elements
   */
  private setTextAreaValue(element: HTMLTextAreaElement, value: any): boolean {
    element.value = String(value)
    return true
  }

  /**
   * Format value based on field type and requirements
   */
  private formatValueForField(value: any, field: FormField): any {
    switch (field.type) {
      case 'email':
        return this.formatEmail(value)
      
      case 'phone':
      case 'tel':
        return this.formatPhoneNumber(value)
      
      case 'date':
        return this.formatDate(value)
      
      case 'currency':
        return this.formatCurrency(value)
      
      case 'url':
        return this.formatUrl(value)
      
      default:
        return value
    }
  }

  /**
   * Format email address
   */
  private formatEmail(value: any): string {
    const email = String(value).trim().toLowerCase()
    // Basic email validation
    if (email.includes('@') && email.includes('.')) {
      return email
    }
    return value
  }

  /**
   * Format phone number
   */
  private formatPhoneNumber(value: any): string {
    const phone = String(value).replace(/\D/g, '')
    
    if (phone.length === 10) {
      return `(${phone.slice(0, 3)}) ${phone.slice(3, 6)}-${phone.slice(6)}`
    } else if (phone.length === 11 && phone.startsWith('1')) {
      return `+1 (${phone.slice(1, 4)}) ${phone.slice(4, 7)}-${phone.slice(7)}`
    }
    
    return value
  }

  /**
   * Format date for input fields
   */
  private formatDate(value: any): string {
    try {
      const date = new Date(value)
      if (!isNaN(date.getTime())) {
        return date.toISOString().split('T')[0] // YYYY-MM-DD format
      }
    } catch (error) {
      // Invalid date
    }
    return value
  }

  /**
   * Format date for input elements
   */
  private formatDateForInput(value: any): string | null {
    try {
      const date = new Date(value)
      if (!isNaN(date.getTime())) {
        return date.toISOString().split('T')[0]
      }
    } catch (error) {
      // Invalid date
    }
    return null
  }

  /**
   * Format currency values
   */
  private formatCurrency(value: any): string {
    const num = Number(value)
    if (!isNaN(num)) {
      return num.toLocaleString('en-US', {
        style: 'currency',
        currency: 'USD'
      })
    }
    return value
  }

  /**
   * Format URL values
   */
  private formatUrl(value: any): string {
    const url = String(value).trim()
    if (url && !url.startsWith('http://') && !url.startsWith('https://')) {
      return `https://${url}`
    }
    return url
  }

  /**
   * Trigger change events on form elements
   */
  private triggerChangeEvents(element: HTMLElement): void {
    // Trigger input event
    element.dispatchEvent(new Event('input', { bubbles: true }))
    
    // Trigger change event
    element.dispatchEvent(new Event('change', { bubbles: true }))
    
    // Trigger blur event
    element.dispatchEvent(new Event('blur', { bubbles: true }))
    
    // For React applications, trigger React's synthetic events
    if ('_reactInternalFiber' in element || '_reactInternalInstance' in element) {
      this.triggerReactEvents(element)
    }
  }

  /**
   * Trigger React synthetic events
   */
  private triggerReactEvents(element: HTMLElement): void {
    try {
      // Create and dispatch React-compatible events
      const nativeInputValueSetter = Object.getOwnPropertyDescriptor(
        window.HTMLInputElement.prototype,
        'value'
      )?.set

      if (nativeInputValueSetter && element instanceof HTMLInputElement) {
        nativeInputValueSetter.call(element, element.value)
        element.dispatchEvent(new Event('input', { bubbles: true }))
      }
    } catch (error) {
      // Fallback to standard events
      console.warn('Failed to trigger React events:', error)
    }
  }

  /**
   * Add visual feedback to filled fields
   */
  private addVisualFeedback(element: HTMLElement, type: 'success' | 'error'): void {
    // Remove existing feedback classes
    element.classList.remove('careercraft-filled', 'careercraft-error')
    
    // Add appropriate feedback class
    if (type === 'success') {
      element.classList.add('careercraft-filled')
    } else {
      element.classList.add('careercraft-error')
    }

    // Remove feedback after delay
    setTimeout(() => {
      element.classList.remove('careercraft-filled', 'careercraft-error')
    }, 3000)
  }

  /**
   * Validate form data before filling
   */
  private validateFormData(form: DetectedForm, data: AutofillData): {
    valid: boolean
    warnings: string[]
  } {
    const warnings: string[] = []
    let valid = true

    // Check for required fields
    const requiredFields = form.fields.filter(f => f.required)
    for (const field of requiredFields) {
      if (field.mappedTo && (!data[field.mappedTo] || data[field.mappedTo] === '')) {
        warnings.push(`Required field "${field.label}" has no data`)
        valid = false
      }
    }

    // Check data quality
    for (const field of form.fields) {
      if (field.mappedTo && data[field.mappedTo]) {
        const validation = this.validateFieldData(field, data[field.mappedTo])
        if (!validation.valid) {
          warnings.push(`${field.label}: ${validation.message}`)
        }
      }
    }

    return { valid, warnings }
  }

  /**
   * Validate individual field data
   */
  private validateFieldData(field: FormField, value: any): {
    valid: boolean
    message?: string
  } {
    const rule = this.validationRules.get(field.type)
    if (rule) {
      return rule.validate(value)
    }
    return { valid: true }
  }

  /**
   * Validate filled form
   */
  private async validateFilledForm(form: DetectedForm, result: AutofillResult): Promise<void> {
    // Check if form has built-in validation
    if (form.element.checkValidity) {
      const isValid = form.element.checkValidity()
      if (!isValid) {
        result.warnings.push('Form contains validation errors')
      }
    }

    // Check individual field validation
    for (const field of form.fields) {
      if (field.element.checkValidity && !field.element.checkValidity()) {
        result.errors.push({
          field: field.mappedTo || field.label,
          error: field.element.validationMessage || 'Validation failed',
          element: field.element
        })
      }
    }
  }

  /**
   * Store fill history for learning
   */
  private storeFillHistory(form: DetectedForm, data: AutofillData, result: AutofillResult): void {
    const historyKey = `${form.metadata.source}_${form.type}`
    const historyEntry = {
      timestamp: Date.now(),
      url: form.metadata.url,
      success: result.success,
      filledFields: result.filledFields,
      totalFields: result.totalFields,
      errors: result.errors.length
    }

    this.fillHistory.set(historyKey, historyEntry)
  }

  /**
   * Initialize validation rules
   */
  private initializeValidationRules(): void {
    this.validationRules.set('email', {
      validate: (value: any) => {
        const email = String(value)
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
        return {
          valid: emailRegex.test(email),
          message: emailRegex.test(email) ? undefined : 'Invalid email format'
        }
      }
    })

    this.validationRules.set('phone', {
      validate: (value: any) => {
        const phone = String(value).replace(/\D/g, '')
        return {
          valid: phone.length >= 10,
          message: phone.length >= 10 ? undefined : 'Phone number too short'
        }
      }
    })

    this.validationRules.set('url', {
      validate: (value: any) => {
        try {
          new URL(value)
          return { valid: true }
        } catch {
          return { valid: false, message: 'Invalid URL format' }
        }
      }
    })
  }

  /**
   * Get fill statistics
   */
  getFillStatistics(): {
    totalFills: number
    successRate: number
    averageFieldsFilled: number
  } {
    const entries = Array.from(this.fillHistory.values())
    const totalFills = entries.length
    const successfulFills = entries.filter(e => e.success).length
    const totalFieldsFilled = entries.reduce((sum, e) => sum + e.filledFields, 0)

    return {
      totalFills,
      successRate: totalFills > 0 ? successfulFills / totalFills : 0,
      averageFieldsFilled: totalFills > 0 ? totalFieldsFilled / totalFills : 0
    }
  }

  /**
   * Clear fill history
   */
  clearHistory(): void {
    this.fillHistory.clear()
  }
}

interface ValidationRule {
  validate: (value: any) => { valid: boolean; message?: string }
}
