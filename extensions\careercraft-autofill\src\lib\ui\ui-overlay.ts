/**
 * UI Overlay
 * 
 * Manages the in-page user interface overlay for form detection,
 * autofill controls, and user feedback.
 */

export interface DetectedForm {
  confidence: number
  type: string
  fieldCount?: number
  company?: string
  jobTitle?: string
}

export class UIOverlay {
  private overlay: HTMLElement | null = null
  private isVisible: boolean = false
  private currentForms: DetectedForm[] = []
  private animationDuration = 300

  constructor() {
    this.createOverlayStyles()
  }

  /**
   * Show form detected notification
   */
  showFormDetected(forms: DetectedForm[]): void {
    this.currentForms = forms
    this.createOverlay()
    this.updateOverlayContent()
    this.showOverlay()
  }

  /**
   * Show success message
   */
  showSuccess(message: string): void {
    this.showNotification(message, 'success')
  }

  /**
   * Show error message
   */
  showError(message: string): void {
    this.showNotification(message, 'error')
  }

  /**
   * Show warning message
   */
  showWarning(message: string): void {
    this.showNotification(message, 'warning')
  }

  /**
   * Show info message
   */
  showInfo(message: string): void {
    this.showNotification(message, 'info')
  }

  /**
   * Show overlay
   */
  show(): void {
    if (this.overlay) {
      this.showOverlay()
    }
  }

  /**
   * Hide overlay
   */
  hide(): void {
    this.hideOverlay()
  }

  /**
   * Toggle overlay visibility
   */
  toggle(): void {
    if (this.isVisible) {
      this.hide()
    } else {
      this.show()
    }
  }

  /**
   * Destroy overlay and cleanup
   */
  destroy(): void {
    if (this.overlay) {
      this.overlay.remove()
      this.overlay = null
    }
    this.removeOverlayStyles()
    this.isVisible = false
  }

  /**
   * Create overlay element
   */
  private createOverlay(): void {
    if (this.overlay) {
      return
    }

    this.overlay = document.createElement('div')
    this.overlay.id = 'careercraft-overlay'
    this.overlay.className = 'careercraft-overlay'
    
    // Ensure overlay is on top
    this.overlay.style.zIndex = '2147483647'
    
    document.body.appendChild(this.overlay)
  }

  /**
   * Update overlay content
   */
  private updateOverlayContent(): void {
    if (!this.overlay || this.currentForms.length === 0) {
      return
    }

    const primaryForm = this.currentForms[0]
    const confidence = Math.round(primaryForm.confidence * 100)

    this.overlay.innerHTML = `
      <div class="careercraft-overlay-content">
        <div class="careercraft-header">
          <div class="careercraft-logo">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
              <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
              <path d="M2 17L12 22L22 17" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
              <path d="M2 12L12 17L22 12" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
            </svg>
            <span>CareerCraft</span>
          </div>
          <button class="careercraft-close" onclick="this.closest('.careercraft-overlay').style.display='none'">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
              <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
            </svg>
          </button>
        </div>
        
        <div class="careercraft-body">
          <div class="careercraft-status">
            <div class="careercraft-status-icon ${this.getStatusIconClass(confidence)}">
              ${this.getStatusIcon(confidence)}
            </div>
            <div class="careercraft-status-text">
              <h3>Job Application Form Detected</h3>
              <p>Confidence: ${confidence}% • ${primaryForm.fieldCount || 0} fields</p>
              ${primaryForm.company ? `<p class="careercraft-company">${primaryForm.company}</p>` : ''}
              ${primaryForm.jobTitle ? `<p class="careercraft-job-title">${primaryForm.jobTitle}</p>` : ''}
            </div>
          </div>
          
          <div class="careercraft-actions">
            <button class="careercraft-btn careercraft-btn-primary" onclick="window.postMessage({type: 'CAREERCRAFT_QUICK_FILL'}, '*')">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                <path d="M13 2L3 14H12L11 22L21 10H12L13 2Z" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
              </svg>
              Quick Fill
            </button>
            <button class="careercraft-btn careercraft-btn-secondary" onclick="window.postMessage({type: 'CAREERCRAFT_CUSTOM_FILL'}, '*')">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                <path d="M12 20H21M3 20H12M3 20V4M12 20V4M21 12H12M12 12H3" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
              </svg>
              Custom Fill
            </button>
          </div>
          
          <div class="careercraft-options">
            <label class="careercraft-checkbox">
              <input type="checkbox" checked>
              <span>Customize for this job</span>
            </label>
            <label class="careercraft-checkbox">
              <input type="checkbox" checked>
              <span>Track application</span>
            </label>
          </div>
        </div>
      </div>
    `

    // Add event listeners
    this.addEventListeners()
  }

  /**
   * Show notification
   */
  private showNotification(message: string, type: 'success' | 'error' | 'warning' | 'info'): void {
    const notification = document.createElement('div')
    notification.className = `careercraft-notification careercraft-notification-${type}`
    notification.style.zIndex = '2147483648'
    
    notification.innerHTML = `
      <div class="careercraft-notification-content">
        <div class="careercraft-notification-icon">
          ${this.getNotificationIcon(type)}
        </div>
        <div class="careercraft-notification-message">${message}</div>
        <button class="careercraft-notification-close">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
            <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
          </svg>
        </button>
      </div>
    `

    document.body.appendChild(notification)

    // Add close functionality
    const closeBtn = notification.querySelector('.careercraft-notification-close')
    closeBtn?.addEventListener('click', () => {
      this.hideNotification(notification)
    })

    // Auto-hide after 5 seconds
    setTimeout(() => {
      this.hideNotification(notification)
    }, 5000)

    // Show with animation
    requestAnimationFrame(() => {
      notification.classList.add('careercraft-notification-show')
    })
  }

  /**
   * Hide notification
   */
  private hideNotification(notification: HTMLElement): void {
    notification.classList.remove('careercraft-notification-show')
    notification.classList.add('careercraft-notification-hide')
    
    setTimeout(() => {
      notification.remove()
    }, this.animationDuration)
  }

  /**
   * Show overlay with animation
   */
  private showOverlay(): void {
    if (!this.overlay) {
      return
    }

    this.overlay.style.display = 'block'
    this.isVisible = true

    requestAnimationFrame(() => {
      this.overlay?.classList.add('careercraft-overlay-show')
    })
  }

  /**
   * Hide overlay with animation
   */
  private hideOverlay(): void {
    if (!this.overlay) {
      return
    }

    this.overlay.classList.remove('careercraft-overlay-show')
    this.overlay.classList.add('careercraft-overlay-hide')
    this.isVisible = false

    setTimeout(() => {
      if (this.overlay) {
        this.overlay.style.display = 'none'
        this.overlay.classList.remove('careercraft-overlay-hide')
      }
    }, this.animationDuration)
  }

  /**
   * Add event listeners
   */
  private addEventListeners(): void {
    // Listen for messages from overlay buttons
    window.addEventListener('message', (event) => {
      if (event.data.type === 'CAREERCRAFT_QUICK_FILL') {
        this.handleQuickFill()
      } else if (event.data.type === 'CAREERCRAFT_CUSTOM_FILL') {
        this.handleCustomFill()
      }
    })
  }

  /**
   * Handle quick fill action
   */
  private handleQuickFill(): void {
    // Send message to content script
    window.postMessage({
      type: 'CAREERCRAFT_ACTION',
      action: 'QUICK_FILL'
    }, '*')
    
    this.hideOverlay()
  }

  /**
   * Handle custom fill action
   */
  private handleCustomFill(): void {
    // Send message to content script
    window.postMessage({
      type: 'CAREERCRAFT_ACTION',
      action: 'CUSTOM_FILL'
    }, '*')
    
    this.hideOverlay()
  }

  /**
   * Get status icon based on confidence
   */
  private getStatusIcon(confidence: number): string {
    if (confidence >= 90) {
      return `<svg width="20" height="20" viewBox="0 0 24 24" fill="none">
        <path d="M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>`
    } else if (confidence >= 70) {
      return `<svg width="20" height="20" viewBox="0 0 24 24" fill="none">
        <path d="M12 9V13M12 17H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>`
    } else {
      return `<svg width="20" height="20" viewBox="0 0 24 24" fill="none">
        <path d="M10.29 3.86L1.82 18A2 2 0 0 0 3.24 21H20.76A2 2 0 0 0 22.18 18L13.71 3.86A2 2 0 0 0 10.29 3.86Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M12 9V13M12 17H12.01" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>`
    }
  }

  /**
   * Get status icon class based on confidence
   */
  private getStatusIconClass(confidence: number): string {
    if (confidence >= 90) {
      return 'careercraft-status-success'
    } else if (confidence >= 70) {
      return 'careercraft-status-warning'
    } else {
      return 'careercraft-status-error'
    }
  }

  /**
   * Get notification icon
   */
  private getNotificationIcon(type: string): string {
    switch (type) {
      case 'success':
        return `<svg width="20" height="20" viewBox="0 0 24 24" fill="none">
          <path d="M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="currentColor" stroke-width="2"/>
        </svg>`
      case 'error':
        return `<svg width="20" height="20" viewBox="0 0 24 24" fill="none">
          <path d="M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="currentColor" stroke-width="2"/>
          <path d="M15 9L9 15M9 9L15 15" stroke="currentColor" stroke-width="2"/>
        </svg>`
      case 'warning':
        return `<svg width="20" height="20" viewBox="0 0 24 24" fill="none">
          <path d="M10.29 3.86L1.82 18A2 2 0 0 0 3.24 21H20.76A2 2 0 0 0 22.18 18L13.71 3.86A2 2 0 0 0 10.29 3.86Z" stroke="currentColor" stroke-width="2"/>
          <path d="M12 9V13M12 17H12.01" stroke="currentColor" stroke-width="2"/>
        </svg>`
      case 'info':
      default:
        return `<svg width="20" height="20" viewBox="0 0 24 24" fill="none">
          <path d="M12 16V12M12 8H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="currentColor" stroke-width="2"/>
        </svg>`
    }
  }

  /**
   * Create overlay styles
   */
  private createOverlayStyles(): void {
    if (document.getElementById('careercraft-overlay-styles')) {
      return
    }

    const styles = document.createElement('style')
    styles.id = 'careercraft-overlay-styles'
    styles.textContent = `
      .careercraft-overlay {
        position: fixed;
        top: 20px;
        right: 20px;
        width: 320px;
        background: white;
        border-radius: 12px;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        border: 1px solid #e5e7eb;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        font-size: 14px;
        line-height: 1.5;
        z-index: 2147483647;
        display: none;
        opacity: 0;
        transform: translateY(-10px);
        transition: all 0.3s ease;
      }
      
      .careercraft-overlay-show {
        opacity: 1 !important;
        transform: translateY(0) !important;
      }
      
      .careercraft-overlay-hide {
        opacity: 0 !important;
        transform: translateY(-10px) !important;
      }
      
      .careercraft-overlay-content {
        padding: 0;
      }
      
      .careercraft-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 16px 20px;
        border-bottom: 1px solid #e5e7eb;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 12px 12px 0 0;
      }
      
      .careercraft-logo {
        display: flex;
        align-items: center;
        gap: 8px;
        font-weight: 600;
        font-size: 16px;
      }
      
      .careercraft-close {
        background: none;
        border: none;
        color: white;
        cursor: pointer;
        padding: 4px;
        border-radius: 4px;
        transition: background-color 0.2s;
      }
      
      .careercraft-close:hover {
        background: rgba(255, 255, 255, 0.1);
      }
      
      .careercraft-body {
        padding: 20px;
      }
      
      .careercraft-status {
        display: flex;
        align-items: flex-start;
        gap: 12px;
        margin-bottom: 20px;
      }
      
      .careercraft-status-icon {
        flex-shrink: 0;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      
      .careercraft-status-success {
        background: #dcfce7;
        color: #16a34a;
      }
      
      .careercraft-status-warning {
        background: #fef3c7;
        color: #d97706;
      }
      
      .careercraft-status-error {
        background: #fee2e2;
        color: #dc2626;
      }
      
      .careercraft-status-text h3 {
        margin: 0 0 4px 0;
        font-size: 16px;
        font-weight: 600;
        color: #111827;
      }
      
      .careercraft-status-text p {
        margin: 0;
        color: #6b7280;
        font-size: 13px;
      }
      
      .careercraft-company {
        font-weight: 500 !important;
        color: #374151 !important;
      }
      
      .careercraft-job-title {
        font-weight: 500 !important;
        color: #4f46e5 !important;
      }
      
      .careercraft-actions {
        display: flex;
        gap: 8px;
        margin-bottom: 16px;
      }
      
      .careercraft-btn {
        flex: 1;
        padding: 10px 16px;
        border-radius: 8px;
        border: none;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 6px;
        transition: all 0.2s;
      }
      
      .careercraft-btn-primary {
        background: #4f46e5;
        color: white;
      }
      
      .careercraft-btn-primary:hover {
        background: #4338ca;
        transform: translateY(-1px);
      }
      
      .careercraft-btn-secondary {
        background: #f3f4f6;
        color: #374151;
        border: 1px solid #d1d5db;
      }
      
      .careercraft-btn-secondary:hover {
        background: #e5e7eb;
        transform: translateY(-1px);
      }
      
      .careercraft-options {
        display: flex;
        flex-direction: column;
        gap: 8px;
      }
      
      .careercraft-checkbox {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 13px;
        color: #6b7280;
        cursor: pointer;
      }
      
      .careercraft-checkbox input {
        margin: 0;
      }
      
      .careercraft-notification {
        position: fixed;
        top: 20px;
        right: 20px;
        width: 320px;
        background: white;
        border-radius: 8px;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        border-left: 4px solid;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        font-size: 14px;
        opacity: 0;
        transform: translateX(100%);
        transition: all 0.3s ease;
      }
      
      .careercraft-notification-show {
        opacity: 1 !important;
        transform: translateX(0) !important;
      }
      
      .careercraft-notification-hide {
        opacity: 0 !important;
        transform: translateX(100%) !important;
      }
      
      .careercraft-notification-success {
        border-left-color: #16a34a;
      }
      
      .careercraft-notification-error {
        border-left-color: #dc2626;
      }
      
      .careercraft-notification-warning {
        border-left-color: #d97706;
      }
      
      .careercraft-notification-info {
        border-left-color: #2563eb;
      }
      
      .careercraft-notification-content {
        display: flex;
        align-items: flex-start;
        gap: 12px;
        padding: 16px;
      }
      
      .careercraft-notification-icon {
        flex-shrink: 0;
      }
      
      .careercraft-notification-success .careercraft-notification-icon {
        color: #16a34a;
      }
      
      .careercraft-notification-error .careercraft-notification-icon {
        color: #dc2626;
      }
      
      .careercraft-notification-warning .careercraft-notification-icon {
        color: #d97706;
      }
      
      .careercraft-notification-info .careercraft-notification-icon {
        color: #2563eb;
      }
      
      .careercraft-notification-message {
        flex: 1;
        color: #374151;
        line-height: 1.5;
      }
      
      .careercraft-notification-close {
        background: none;
        border: none;
        color: #9ca3af;
        cursor: pointer;
        padding: 2px;
        border-radius: 4px;
        transition: color 0.2s;
      }
      
      .careercraft-notification-close:hover {
        color: #6b7280;
      }
      
      .careercraft-filled {
        background-color: #dcfce7 !important;
        border-color: #16a34a !important;
        transition: all 0.3s ease;
      }
      
      .careercraft-error {
        background-color: #fee2e2 !important;
        border-color: #dc2626 !important;
        transition: all 0.3s ease;
      }
    `
    
    document.head.appendChild(styles)
  }

  /**
   * Remove overlay styles
   */
  private removeOverlayStyles(): void {
    const styles = document.getElementById('careercraft-overlay-styles')
    if (styles) {
      styles.remove()
    }
  }
}
