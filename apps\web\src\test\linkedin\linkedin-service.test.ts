/**
 * LinkedIn Service Unit Tests
 * 
 * Tests for LinkedIn database operations and business logic
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { LinkedInService } from '@/lib/linkedin/service'

// Mock Prisma
const mockPrisma = {
  linkedInProfile: {
    upsert: vi.fn(),
    findUnique: vi.fn(),
    delete: vi.fn()
  },
  linkedInImport: {
    create: vi.fn(),
    findMany: vi.fn(),
    findFirst: vi.fn()
  },
  resume: {
    findFirst: vi.fn(),
    update: vi.fn()
  }
}

vi.mock('@/lib/db', () => ({
  prisma: mockPrisma
}))

// Mock LinkedIn client
const mockLinkedInClient = {
  getProfile: vi.fn(),
  getPositions: vi.fn(),
  getEducations: vi.fn(),
  getSkills: vi.fn(),
  transformToResumeData: vi.fn()
}

vi.mock('@/lib/linkedin/client', () => ({
  linkedInClient: mockLinkedInClient
}))

describe('LinkedInService', () => {
  let service: LinkedInService

  beforeEach(() => {
    service = new LinkedInService()
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('saveLinkedInProfile', () => {
    it('should save LinkedIn profile to database', async () => {
      const mockProfile = {
        id: 'linkedin-123',
        firstName: { localized: { 'en_US': 'John' } },
        lastName: { localized: { 'en_US': 'Doe' } }
      }

      mockPrisma.linkedInProfile.upsert.mockResolvedValue({
        id: 'profile-id',
        userId: 'user-123',
        linkedinId: 'linkedin-123'
      })

      await service.saveLinkedInProfile('user-123', 'linkedin-123', mockProfile as any)

      expect(mockPrisma.linkedInProfile.upsert).toHaveBeenCalledWith({
        where: { userId: 'user-123' },
        update: {
          linkedinId: 'linkedin-123',
          profileData: JSON.stringify(mockProfile),
          lastSynced: expect.any(Date),
          updatedAt: expect.any(Date)
        },
        create: {
          userId: 'user-123',
          linkedinId: 'linkedin-123',
          profileData: JSON.stringify(mockProfile),
          lastSynced: expect.any(Date)
        }
      })
    })
  })

  describe('getLinkedInProfile', () => {
    it('should retrieve LinkedIn profile from database', async () => {
      const mockProfile = {
        id: 'linkedin-123',
        firstName: { localized: { 'en_US': 'John' } },
        lastName: { localized: { 'en_US': 'Doe' } }
      }

      mockPrisma.linkedInProfile.findUnique.mockResolvedValue({
        id: 'profile-id',
        userId: 'user-123',
        profileData: JSON.stringify(mockProfile)
      })

      const result = await service.getLinkedInProfile('user-123')

      expect(result).toEqual(mockProfile)
      expect(mockPrisma.linkedInProfile.findUnique).toHaveBeenCalledWith({
        where: { userId: 'user-123' }
      })
    })

    it('should return null if profile not found', async () => {
      mockPrisma.linkedInProfile.findUnique.mockResolvedValue(null)

      const result = await service.getLinkedInProfile('user-123')

      expect(result).toBeNull()
    })

    it('should handle JSON parsing errors', async () => {
      mockPrisma.linkedInProfile.findUnique.mockResolvedValue({
        id: 'profile-id',
        userId: 'user-123',
        profileData: 'invalid-json'
      })

      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})

      const result = await service.getLinkedInProfile('user-123')

      expect(result).toBeNull()
      expect(consoleSpy).toHaveBeenCalledWith(
        'Failed to parse LinkedIn profile data:',
        expect.any(Error)
      )

      consoleSpy.mockRestore()
    })
  })

  describe('hasLinkedInProfile', () => {
    it('should return true if user has LinkedIn profile', async () => {
      mockPrisma.linkedInProfile.findUnique.mockResolvedValue({
        id: 'profile-id'
      })

      const result = await service.hasLinkedInProfile('user-123')

      expect(result).toBe(true)
    })

    it('should return false if user has no LinkedIn profile', async () => {
      mockPrisma.linkedInProfile.findUnique.mockResolvedValue(null)

      const result = await service.hasLinkedInProfile('user-123')

      expect(result).toBe(false)
    })
  })

  describe('importLinkedInData', () => {
    it('should import LinkedIn data successfully', async () => {
      const mockProfile = {
        id: 'linkedin-123',
        firstName: { localized: { 'en_US': 'John' } },
        lastName: { localized: { 'en_US': 'Doe' } }
      }

      const mockResumeData = {
        personalInfo: { firstName: 'John', lastName: 'Doe' },
        experience: [],
        education: [],
        skills: []
      }

      // Mock LinkedIn API calls
      mockLinkedInClient.getProfile.mockResolvedValue(mockProfile)
      mockLinkedInClient.getPositions.mockResolvedValue({ values: [] })
      mockLinkedInClient.getEducations.mockResolvedValue({ values: [] })
      mockLinkedInClient.getSkills.mockResolvedValue({ values: [] })
      mockLinkedInClient.transformToResumeData.mockReturnValue(mockResumeData)

      // Mock database operations
      mockPrisma.linkedInProfile.upsert.mockResolvedValue({
        id: 'profile-id',
        userId: 'user-123'
      })
      mockPrisma.linkedInProfile.findUnique.mockResolvedValue({
        id: 'profile-id',
        userId: 'user-123'
      })
      mockPrisma.linkedInImport.create.mockResolvedValue({
        id: 'import-id',
        userId: 'user-123'
      })

      const result = await service.importLinkedInData(
        'user-123',
        'access-token',
        { resumeId: 'resume-123', sections: ['personalInfo'] }
      )

      expect(result.success).toBe(true)
      expect(result.importId).toBe('import-id')
      expect(result.importedData).toEqual({ personalInfo: mockResumeData.personalInfo })
    })

    it('should handle import errors gracefully', async () => {
      mockLinkedInClient.getProfile.mockRejectedValue(new Error('API Error'))

      const result = await service.importLinkedInData(
        'user-123',
        'invalid-token',
        { resumeId: 'resume-123' }
      )

      expect(result.success).toBe(false)
      expect(result.errors).toContain('API Error')
    })
  })

  describe('getImportHistory', () => {
    it('should retrieve import history for user', async () => {
      const mockImports = [
        {
          id: 'import-1',
          userId: 'user-123',
          resumeId: 'resume-123',
          importStatus: 'completed',
          createdAt: new Date(),
          importedData: JSON.stringify({
            personalInfo: { firstName: 'John' },
            experience: [{ company: 'TechCorp' }]
          }),
          resume: {
            id: 'resume-123',
            title: 'Software Engineer Resume'
          }
        }
      ]

      mockPrisma.linkedInImport.findMany.mockResolvedValue(mockImports)

      const result = await service.getImportHistory('user-123', 5)

      expect(result).toHaveLength(1)
      expect(result[0]).toEqual({
        id: 'import-1',
        resumeId: 'resume-123',
        resumeTitle: 'Software Engineer Resume',
        status: 'completed',
        createdAt: mockImports[0].createdAt,
        importedSections: ['personalInfo', 'experience']
      })

      expect(mockPrisma.linkedInImport.findMany).toHaveBeenCalledWith({
        where: { userId: 'user-123' },
        orderBy: { createdAt: 'desc' },
        take: 5,
        include: {
          resume: {
            select: {
              id: true,
              title: true
            }
          }
        }
      })
    })
  })

  describe('applyImportToResume', () => {
    it('should apply imported data to resume', async () => {
      const mockImport = {
        id: 'import-123',
        userId: 'user-123',
        importedData: JSON.stringify({
          personalInfo: { firstName: 'John', lastName: 'Doe' },
          experience: [{ company: 'TechCorp', position: 'Engineer' }]
        })
      }

      const mockResume = {
        id: 'resume-123',
        userId: 'user-123',
        personalInfo: JSON.stringify({ email: '<EMAIL>' }),
        sections: JSON.stringify({ skills: [{ name: 'JavaScript' }] })
      }

      mockPrisma.linkedInImport.findFirst.mockResolvedValue(mockImport)
      mockPrisma.resume.findFirst.mockResolvedValue(mockResume)
      mockPrisma.resume.update.mockResolvedValue({})

      const result = await service.applyImportToResume(
        'user-123',
        'resume-123',
        'import-123',
        ['personalInfo', 'experience']
      )

      expect(result).toBe(true)
      expect(mockPrisma.resume.update).toHaveBeenCalledWith({
        where: { id: 'resume-123' },
        data: {
          personalInfo: JSON.stringify({
            email: '<EMAIL>',
            firstName: 'John',
            lastName: 'Doe'
          }),
          sections: JSON.stringify({
            skills: [{ name: 'JavaScript' }],
            experience: [{ company: 'TechCorp', position: 'Engineer' }]
          }),
          updatedAt: expect.any(Date)
        }
      })
    })

    it('should handle apply errors gracefully', async () => {
      mockPrisma.linkedInImport.findFirst.mockResolvedValue(null)

      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})

      const result = await service.applyImportToResume(
        'user-123',
        'resume-123',
        'invalid-import',
        ['personalInfo']
      )

      expect(result).toBe(false)
      expect(consoleSpy).toHaveBeenCalled()

      consoleSpy.mockRestore()
    })
  })

  describe('disconnectLinkedIn', () => {
    it('should disconnect LinkedIn profile successfully', async () => {
      mockPrisma.linkedInProfile.delete.mockResolvedValue({})

      const result = await service.disconnectLinkedIn('user-123')

      expect(result).toBe(true)
      expect(mockPrisma.linkedInProfile.delete).toHaveBeenCalledWith({
        where: { userId: 'user-123' }
      })
    })

    it('should handle disconnect errors gracefully', async () => {
      mockPrisma.linkedInProfile.delete.mockRejectedValue(new Error('Database error'))

      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})

      const result = await service.disconnectLinkedIn('user-123')

      expect(result).toBe(false)
      expect(consoleSpy).toHaveBeenCalled()

      consoleSpy.mockRestore()
    })
  })

  describe('getConnectionStatus', () => {
    it('should return connection status with profile data', async () => {
      const mockProfile = {
        id: 'profile-id',
        userId: 'user-123',
        lastSynced: new Date(),
        profileData: JSON.stringify({
          firstName: { localized: { 'en_US': 'John' } },
          lastName: { localized: { 'en_US': 'Doe' } },
          headline: 'Software Engineer'
        })
      }

      mockPrisma.linkedInProfile.findUnique.mockResolvedValue(mockProfile)

      const result = await service.getConnectionStatus('user-123')

      expect(result.connected).toBe(true)
      expect(result.lastSynced).toEqual(mockProfile.lastSynced)
      expect(result.profileData).toEqual({
        name: 'John Doe',
        headline: 'Software Engineer',
        profileImage: undefined
      })
    })

    it('should return disconnected status when no profile exists', async () => {
      mockPrisma.linkedInProfile.findUnique.mockResolvedValue(null)

      const result = await service.getConnectionStatus('user-123')

      expect(result.connected).toBe(false)
      expect(result.lastSynced).toBeUndefined()
      expect(result.profileData).toBeUndefined()
    })
  })
})
