/**
 * Pricing Page Component
 * 
 * Displays subscription plans with features and pricing
 */

'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Switch } from '@/components/ui/switch'
import { Check, Star, Zap, Building, Crown } from 'lucide-react'
import { toast } from 'sonner'

interface SubscriptionPlan {
  id: string
  name: string
  description?: string
  priceMonthly: number
  priceYearly?: number
  features: any
  maxResumes: number
  maxTemplates: number
  maxCollaborators: number
  aiSuggestionsLimit: number
  isActive: boolean
  sortOrder: number
}

interface PricingPageProps {
  onPlanSelect?: (planId: string, billingCycle: 'monthly' | 'yearly') => void
  showCurrentPlan?: boolean
}

export function PricingPage({ onPlanSelect, showCurrentPlan = true }: PricingPageProps) {
  const { data: session } = useSession()
  const [plans, setPlans] = useState<SubscriptionPlan[]>([])
  const [currentSubscription, setCurrentSubscription] = useState<any>(null)
  const [isYearly, setIsYearly] = useState(false)
  const [loading, setLoading] = useState(true)
  const [processingPlan, setProcessingPlan] = useState<string | null>(null)

  useEffect(() => {
    loadPlans()
    if (session?.user && showCurrentPlan) {
      loadCurrentSubscription()
    }
  }, [session, showCurrentPlan])

  const loadPlans = async () => {
    try {
      const response = await fetch('/api/payments?action=plans')
      if (!response.ok) throw new Error('Failed to load plans')
      
      const data = await response.json()
      setPlans(data.plans || [])
    } catch (error) {
      console.error('Error loading plans:', error)
      toast.error('Failed to load pricing plans')
    } finally {
      setLoading(false)
    }
  }

  const loadCurrentSubscription = async () => {
    try {
      const response = await fetch('/api/payments?action=subscription')
      if (!response.ok) return
      
      const data = await response.json()
      setCurrentSubscription(data.subscription)
    } catch (error) {
      console.error('Error loading subscription:', error)
    }
  }

  const handlePlanSelect = async (planId: string) => {
    if (!session?.user) {
      toast.error('Please sign in to subscribe')
      return
    }

    setProcessingPlan(planId)

    try {
      if (onPlanSelect) {
        onPlanSelect(planId, isYearly ? 'yearly' : 'monthly')
      } else {
        // Default behavior: create checkout session
        const response = await fetch('/api/payments', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            action: 'create-checkout-session',
            planId,
            billingCycle: isYearly ? 'yearly' : 'monthly',
            successUrl: `${window.location.origin}/dashboard/billing?success=true`,
            cancelUrl: `${window.location.origin}/pricing?canceled=true`,
            trialDays: 14 // 14-day free trial
          })
        })

        if (!response.ok) throw new Error('Failed to create checkout session')

        const data = await response.json()
        if (data.url) {
          window.location.href = data.url
        }
      }
    } catch (error) {
      console.error('Error selecting plan:', error)
      toast.error('Failed to process subscription')
    } finally {
      setProcessingPlan(null)
    }
  }

  const getPlanIcon = (planName: string) => {
    switch (planName.toLowerCase()) {
      case 'free':
        return <Star className="h-6 w-6" />
      case 'pro':
        return <Zap className="h-6 w-6" />
      case 'business':
        return <Building className="h-6 w-6" />
      case 'enterprise':
        return <Crown className="h-6 w-6" />
      default:
        return <Star className="h-6 w-6" />
    }
  }

  const getPlanColor = (planName: string) => {
    switch (planName.toLowerCase()) {
      case 'free':
        return 'border-gray-200'
      case 'pro':
        return 'border-blue-500 ring-2 ring-blue-500'
      case 'business':
        return 'border-purple-500'
      case 'enterprise':
        return 'border-yellow-500'
      default:
        return 'border-gray-200'
    }
  }

  const formatPrice = (plan: SubscriptionPlan) => {
    if (plan.priceMonthly === 0) return 'Free'
    
    const price = isYearly && plan.priceYearly ? plan.priceYearly : plan.priceMonthly
    const period = isYearly ? 'year' : 'month'
    
    return `$${price}/${period}`
  }

  const getSavings = (plan: SubscriptionPlan) => {
    if (!plan.priceYearly || plan.priceMonthly === 0) return null
    
    const monthlyTotal = plan.priceMonthly * 12
    const savings = monthlyTotal - plan.priceYearly
    const percentage = Math.round((savings / monthlyTotal) * 100)
    
    return { amount: savings, percentage }
  }

  const isCurrentPlan = (planId: string) => {
    return currentSubscription?.plan?.id === planId
  }

  const getFeatureList = (plan: SubscriptionPlan) => {
    const features = []
    
    // Resume features
    if (plan.maxResumes === -1) {
      features.push('Unlimited resumes')
    } else {
      features.push(`${plan.maxResumes} resume${plan.maxResumes > 1 ? 's' : ''}`)
    }
    
    // Template features
    if (plan.maxTemplates === -1) {
      features.push('All premium templates')
    } else {
      features.push(`${plan.maxTemplates} template${plan.maxTemplates > 1 ? 's' : ''}`)
    }
    
    // AI features
    if (plan.aiSuggestionsLimit === -1) {
      features.push('Unlimited AI suggestions')
    } else {
      features.push(`${plan.aiSuggestionsLimit} AI suggestions/month`)
    }
    
    // Collaboration features
    if (plan.maxCollaborators === -1) {
      features.push('Unlimited collaborators')
    } else if (plan.maxCollaborators > 0) {
      features.push(`Up to ${plan.maxCollaborators} collaborators`)
    }
    
    // Plan-specific features
    const planFeatures = plan.features || {}
    
    if (planFeatures.advancedAI) features.push('Advanced AI features')
    if (planFeatures.jobMatching) features.push('Smart job matching')
    if (planFeatures.interviewPrep) features.push('Interview preparation')
    if (planFeatures.realTimeEditing) features.push('Real-time collaboration')
    if (planFeatures.versionControl) features.push('Version control & history')
    if (planFeatures.customBranding) features.push('Custom branding')
    if (planFeatures.linkedinIntegration) features.push('LinkedIn integration')
    if (planFeatures.advancedAnalytics) features.push('Advanced analytics')
    if (planFeatures.apiAccess) features.push('API access')
    if (planFeatures.ssoIntegration) features.push('SSO integration')
    
    // Support level
    if (planFeatures.supportLevel === 'priority') features.push('Priority support')
    if (planFeatures.supportLevel === 'dedicated') features.push('Dedicated support')
    
    return features
  }

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center mb-8">
          <div className="h-8 bg-gray-200 rounded w-64 mx-auto mb-4 animate-pulse"></div>
          <div className="h-4 bg-gray-200 rounded w-96 mx-auto animate-pulse"></div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[1, 2, 3, 4].map((i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader>
                <div className="h-6 bg-gray-200 rounded w-24 mb-2"></div>
                <div className="h-4 bg-gray-200 rounded w-32"></div>
              </CardHeader>
              <CardContent>
                <div className="h-8 bg-gray-200 rounded w-20 mb-4"></div>
                <div className="space-y-2">
                  {[1, 2, 3, 4, 5].map((j) => (
                    <div key={j} className="h-4 bg-gray-200 rounded"></div>
                  ))}
                </div>
              </CardContent>
              <CardFooter>
                <div className="h-10 bg-gray-200 rounded w-full"></div>
              </CardFooter>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="text-center mb-8">
        <h1 className="text-4xl font-bold mb-4">Choose Your Plan</h1>
        <p className="text-xl text-gray-600 mb-6">
          Unlock the full potential of your career with our premium features
        </p>
        
        {/* Billing Toggle */}
        <div className="flex items-center justify-center gap-4 mb-8">
          <span className={`text-sm ${!isYearly ? 'font-semibold' : 'text-gray-500'}`}>
            Monthly
          </span>
          <Switch
            checked={isYearly}
            onCheckedChange={setIsYearly}
            className="data-[state=checked]:bg-blue-600"
          />
          <span className={`text-sm ${isYearly ? 'font-semibold' : 'text-gray-500'}`}>
            Yearly
          </span>
          {isYearly && (
            <Badge variant="secondary" className="ml-2">
              Save up to 17%
            </Badge>
          )}
        </div>
      </div>

      {/* Pricing Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {plans.map((plan) => {
          const savings = getSavings(plan)
          const features = getFeatureList(plan)
          const isCurrent = isCurrentPlan(plan.id)
          const isProcessing = processingPlan === plan.id

          return (
            <Card 
              key={plan.id} 
              className={`relative ${getPlanColor(plan.name)} ${
                plan.name.toLowerCase() === 'pro' ? 'scale-105' : ''
              }`}
            >
              {plan.name.toLowerCase() === 'pro' && (
                <Badge className="absolute -top-3 left-1/2 transform -translate-x-1/2 bg-blue-600">
                  Most Popular
                </Badge>
              )}
              
              <CardHeader className="text-center">
                <div className="flex items-center justify-center mb-2">
                  {getPlanIcon(plan.name)}
                </div>
                <CardTitle className="text-xl">{plan.name}</CardTitle>
                <CardDescription>{plan.description}</CardDescription>
              </CardHeader>

              <CardContent className="text-center">
                <div className="mb-4">
                  <div className="text-3xl font-bold">
                    {formatPrice(plan)}
                  </div>
                  {isYearly && savings && (
                    <div className="text-sm text-green-600 font-medium">
                      Save ${savings.amount}/year ({savings.percentage}% off)
                    </div>
                  )}
                </div>

                <ul className="space-y-2 text-sm text-left">
                  {features.map((feature, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <Check className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                      <span>{feature}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>

              <CardFooter>
                {isCurrent ? (
                  <Button variant="outline" className="w-full" disabled>
                    Current Plan
                  </Button>
                ) : (
                  <Button
                    className="w-full"
                    variant={plan.name.toLowerCase() === 'pro' ? 'default' : 'outline'}
                    onClick={() => handlePlanSelect(plan.id)}
                    disabled={isProcessing}
                  >
                    {isProcessing ? 'Processing...' : 
                     plan.priceMonthly === 0 ? 'Get Started' : 'Start Free Trial'}
                  </Button>
                )}
              </CardFooter>
            </Card>
          )
        })}
      </div>

      {/* FAQ or Additional Info */}
      <div className="text-center text-sm text-gray-600">
        <p>All paid plans include a 14-day free trial. No credit card required.</p>
        <p className="mt-2">
          Need help choosing? <a href="/contact" className="text-blue-600 hover:underline">Contact our sales team</a>
        </p>
      </div>
    </div>
  )
}
