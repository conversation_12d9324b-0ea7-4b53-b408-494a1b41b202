# CareerCraft Browser Extension - Build & Deployment Documentation

## Overview
This document provides comprehensive build and deployment procedures for the CareerCraft Browser Extension across multiple browser platforms and distribution channels.

## Build System Architecture

### Technology Stack
- **Build Tool**: Webpack 5 with TypeScript support
- **Package Manager**: npm with package-lock.json
- **Transpilation**: TypeScript compiler with strict mode
- **Bundling**: Webpack with code splitting and optimization
- **Styling**: Tailwind CSS with PostCSS processing
- **Linting**: ESLint with TypeScript and React rules
- **Formatting**: Prettier with consistent code style

### Project Structure
```
extensions/careercraft-autofill/
├── src/
│   ├── background/          # Background service worker
│   ├── content/             # Content scripts
│   ├── popup/               # Popup interface
│   ├── options/             # Options page
│   ├── lib/                 # Core library components
│   ├── manifest/            # Browser-specific manifests
│   ├── icons/               # Extension icons
│   └── test/                # Test files
├── dist/                    # Build output
│   ├── chrome/              # Chrome extension build
│   ├── firefox/             # Firefox add-on build
│   └── edge/                # Edge extension build
├── packages/                # Distribution packages
├── webpack.config.js        # Build configuration
├── tsconfig.json           # TypeScript configuration
└── package.json            # Project dependencies
```

## Environment Setup

### Prerequisites
- **Node.js**: v18.17.0 or higher
- **npm**: v9.0.0 or higher
- **Git**: Latest version
- **Browser Development Tools**: Chrome DevTools, Firefox Developer Edition

### Development Environment
```bash
# Clone repository
git clone https://github.com/Octa-src/CareerCraft.git
cd CareerCraft/extensions/careercraft-autofill

# Install dependencies
npm install

# Install development tools
npm install -g web-ext

# Setup environment variables
cp .env.example .env.local
```

### Environment Variables
```bash
# Development environment
NODE_ENV=development
CAREERCRAFT_API_URL=http://localhost:3000/api
ANALYTICS_ENABLED=false
DEBUG_MODE=true

# Production environment
NODE_ENV=production
CAREERCRAFT_API_URL=https://careercraft.onlinejobsearchhelp.com/api
ANALYTICS_ENABLED=true
DEBUG_MODE=false
```

## Build Configuration

### Webpack Configuration
```javascript
// webpack.config.js
const path = require('path')
const CopyWebpackPlugin = require('copy-webpack-plugin')
const HtmlWebpackPlugin = require('html-webpack-plugin')
const MiniCssExtractPlugin = require('mini-css-extract-plugin')

module.exports = (env) => {
  const target = env.target || 'chrome'
  const isProduction = env.NODE_ENV === 'production'

  return {
    mode: isProduction ? 'production' : 'development',
    entry: {
      background: './src/background/background.ts',
      content: './src/content/content.ts',
      popup: './src/popup/popup.tsx',
      options: './src/options/options.tsx'
    },
    output: {
      path: path.resolve(__dirname, `dist/${target}`),
      filename: '[name]/[name].js',
      clean: true
    },
    // ... additional configuration
  }
}
```

### TypeScript Configuration
```json
{
  "compilerOptions": {
    "target": "ES2020",
    "lib": ["DOM", "DOM.Iterable", "ES2020"],
    "module": "ESNext",
    "moduleResolution": "node",
    "strict": true,
    "jsx": "react-jsx",
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"]
    }
  },
  "include": ["src/**/*"],
  "exclude": ["node_modules", "dist", "packages"]
}
```

## Build Process

### Development Build
```bash
# Start development build with watch mode
npm run dev

# Build for specific browser
npm run dev:chrome
npm run dev:firefox
npm run dev:edge

# Run with hot reload
npm run dev:watch
```

### Production Build
```bash
# Build all browser targets
npm run build

# Build specific browser
npm run build:chrome
npm run build:firefox
npm run build:edge

# Build with optimization
npm run build:production
```

### Build Scripts
```json
{
  "scripts": {
    "dev": "webpack --mode development --watch",
    "build": "npm run build:chrome && npm run build:firefox && npm run build:edge",
    "build:chrome": "webpack --env target=chrome --mode production",
    "build:firefox": "webpack --env target=firefox --mode production",
    "build:edge": "webpack --env target=edge --mode production",
    "package": "npm run package:chrome && npm run package:firefox && npm run package:edge",
    "package:chrome": "cd dist/chrome && zip -r ../../packages/chrome-extension.zip .",
    "package:firefox": "web-ext build --source-dir=dist/firefox --artifacts-dir=packages",
    "package:edge": "cd dist/edge && zip -r ../../packages/edge-extension.zip .",
    "test": "jest",
    "test:e2e": "playwright test",
    "lint": "eslint src/**/*.{ts,tsx}",
    "lint:fix": "eslint src/**/*.{ts,tsx} --fix",
    "type-check": "tsc --noEmit"
  }
}
```

## Browser-Specific Builds

### Chrome Extension (Manifest V3)
```json
{
  "manifest_version": 3,
  "name": "CareerCraft Autofill",
  "version": "1.0.0",
  "description": "Intelligent job application autofill powered by AI",
  "background": {
    "service_worker": "background/background.js",
    "type": "module"
  },
  "content_scripts": [{
    "matches": ["https://*.linkedin.com/*", "https://*.indeed.com/*"],
    "js": ["content/content.js"],
    "run_at": "document_idle"
  }],
  "action": {
    "default_popup": "popup/popup.html"
  },
  "permissions": ["activeTab", "storage", "identity"],
  "host_permissions": ["https://*.careercraft.onlinejobsearchhelp.com/*"]
}
```

### Firefox Add-on (WebExtensions)
```json
{
  "manifest_version": 2,
  "name": "CareerCraft Autofill",
  "version": "1.0.0",
  "description": "Intelligent job application autofill powered by AI",
  "background": {
    "scripts": ["background/background.js"],
    "persistent": false
  },
  "content_scripts": [{
    "matches": ["https://*.linkedin.com/*", "https://*.indeed.com/*"],
    "js": ["content/content.js"],
    "run_at": "document_idle"
  }],
  "browser_action": {
    "default_popup": "popup/popup.html"
  },
  "permissions": ["activeTab", "storage", "identity"],
  "applications": {
    "gecko": {
      "id": "<EMAIL>"
    }
  }
}
```

### Edge Extension (Chromium-based)
```json
{
  "manifest_version": 3,
  "name": "CareerCraft Autofill",
  "version": "1.0.0",
  "description": "Intelligent job application autofill powered by AI",
  "background": {
    "service_worker": "background/background.js",
    "type": "module"
  },
  "content_scripts": [{
    "matches": ["https://*.linkedin.com/*", "https://*.indeed.com/*"],
    "js": ["content/content.js"],
    "run_at": "document_idle"
  }],
  "action": {
    "default_popup": "popup/popup.html"
  },
  "permissions": ["activeTab", "storage", "identity"],
  "host_permissions": ["https://*.careercraft.onlinejobsearchhelp.com/*"]
}
```

## Quality Assurance

### Pre-build Checks
```bash
# Type checking
npm run type-check

# Linting
npm run lint

# Unit tests
npm run test

# Integration tests
npm run test:integration

# End-to-end tests
npm run test:e2e
```

### Build Validation
```bash
# Validate build output
npm run validate:build

# Check bundle sizes
npm run analyze:bundle

# Security audit
npm audit

# Dependency check
npm run check:deps
```

### Performance Optimization
```bash
# Bundle analysis
npm run analyze

# Performance testing
npm run test:performance

# Memory profiling
npm run profile:memory

# Load testing
npm run test:load
```

## Distribution Packaging

### Chrome Web Store Package
```bash
# Build Chrome extension
npm run build:chrome

# Create distribution package
npm run package:chrome

# Validate package
web-ext lint --source-dir=dist/chrome

# Upload to Chrome Web Store
# (Manual process through Chrome Web Store Developer Dashboard)
```

### Firefox Add-ons Package
```bash
# Build Firefox add-on
npm run build:firefox

# Create signed package
web-ext build --source-dir=dist/firefox --artifacts-dir=packages

# Sign for distribution
web-ext sign --source-dir=dist/firefox --api-key=$AMO_JWT_ISSUER --api-secret=$AMO_JWT_SECRET

# Upload to Firefox Add-ons
# (Manual process through Firefox Add-ons Developer Hub)
```

### Edge Add-ons Package
```bash
# Build Edge extension
npm run build:edge

# Create distribution package
npm run package:edge

# Validate package
# (Use Edge Add-ons validation tools)

# Upload to Microsoft Edge Add-ons
# (Manual process through Microsoft Partner Center)
```

## Deployment Pipeline

### Continuous Integration
```yaml
name: Extension Build and Test
on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
      - run: npm ci
      - run: npm run lint
      - run: npm run type-check
      - run: npm run test:coverage
      - run: npm run test:e2e

  build:
    needs: test
    runs-on: ubuntu-latest
    strategy:
      matrix:
        browser: [chrome, firefox, edge]
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
      - run: npm ci
      - run: npm run build:${{ matrix.browser }}
      - run: npm run package:${{ matrix.browser }}
      - uses: actions/upload-artifact@v3
        with:
          name: ${{ matrix.browser }}-extension
          path: packages/
```

### Release Process
```bash
# Version bump
npm version patch|minor|major

# Create release tag
git tag -a v1.0.0 -m "Release version 1.0.0"

# Push changes and tags
git push origin main --tags

# Build release packages
npm run build:production
npm run package

# Create GitHub release
gh release create v1.0.0 packages/* --title "CareerCraft Extension v1.0.0"
```

### Deployment Environments

#### Development Environment
- **Purpose**: Local development and testing
- **URL**: http://localhost:3000
- **Features**: Hot reload, debug mode, mock data
- **Build**: Development build with source maps

#### Staging Environment
- **Purpose**: Pre-production testing
- **URL**: https://staging.careercraft.onlinejobsearchhelp.com
- **Features**: Production-like environment, real API
- **Build**: Production build with staging configuration

#### Production Environment
- **Purpose**: Live user environment
- **URL**: https://careercraft.onlinejobsearchhelp.com
- **Features**: Full production features, analytics
- **Build**: Optimized production build

## Monitoring & Maintenance

### Build Monitoring
```bash
# Build health checks
npm run health:build

# Dependency vulnerability scanning
npm audit

# Bundle size monitoring
npm run monitor:bundle

# Performance regression testing
npm run test:performance:regression
```

### Update Management
```bash
# Check for dependency updates
npm outdated

# Update dependencies
npm update

# Security updates
npm audit fix

# Major version updates
npx npm-check-updates -u
```

### Rollback Procedures
```bash
# Rollback to previous version
git checkout v1.0.0

# Rebuild and redeploy
npm run build:production
npm run package

# Emergency rollback
# (Use browser store rollback features)
```

## Security Considerations

### Build Security
- **Dependency Scanning**: Regular vulnerability assessments
- **Code Signing**: Signed packages for distribution
- **Secure Build Environment**: Isolated build processes
- **Access Control**: Limited access to build systems

### Distribution Security
- **Package Integrity**: Checksums and signatures
- **Secure Upload**: HTTPS and authenticated uploads
- **Version Control**: Immutable version history
- **Audit Trail**: Complete deployment logging

This comprehensive build and deployment documentation ensures reliable, secure, and efficient distribution of the CareerCraft Browser Extension across all supported platforms.
