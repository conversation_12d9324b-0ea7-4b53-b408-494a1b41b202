'use client';

import { useState, useRef } from 'react';
import { Resume } from '@careercraft/shared/types/resume';
import { Template, ExportOptions } from '@careercraft/shared/types/template';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Icons } from '@/components/ui/icons';
import { LoadingSpinner } from '@/components/ui/loading';
import { TemplateRenderer } from '@/components/templates/template-renderer';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';

interface PDFExportProps {
  resume: Resume;
  template: Template;
  className?: string;
}

export function PDFExport({ resume, template, className }: PDFExportProps) {
  const [isExporting, setIsExporting] = useState(false);
  const [exportOptions, setExportOptions] = useState<ExportOptions>({
    format: 'pdf',
    quality: 'high',
    pageSize: 'a4',
    orientation: 'portrait',
    margins: {
      top: 20,
      right: 20,
      bottom: 20,
      left: 20,
    },
    includeMetadata: true,
  });
  const [previewScale, setPreviewScale] = useState(0.75);
  const templateRef = useRef<HTMLDivElement>(null);

  const handleExport = async () => {
    try {
      setIsExporting(true);

      // Create export job
      const response = await fetch('/api/export', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          resumeId: resume.id,
          templateId: template.id,
          options: exportOptions,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to start export');
      }

      const exportJob = await response.json();

      // Poll for completion
      const pollInterval = setInterval(async () => {
        try {
          const statusResponse = await fetch(`/api/export/${exportJob.id}`);
          if (statusResponse.ok) {
            const job = await statusResponse.json();
            
            if (job.status === 'completed' && job.downloadUrl) {
              clearInterval(pollInterval);
              
              // Download the file
              const link = document.createElement('a');
              link.href = job.downloadUrl;
              link.download = `${resume.title}.${exportOptions.format}`;
              document.body.appendChild(link);
              link.click();
              document.body.removeChild(link);
              
              toast.success('Resume exported successfully!');
              setIsExporting(false);
            } else if (job.status === 'failed') {
              clearInterval(pollInterval);
              throw new Error(job.error || 'Export failed');
            }
          }
        } catch (error) {
          clearInterval(pollInterval);
          throw error;
        }
      }, 2000);

      // Timeout after 2 minutes
      setTimeout(() => {
        clearInterval(pollInterval);
        if (isExporting) {
          setIsExporting(false);
          toast.error('Export timeout. Please try again.');
        }
      }, 120000);

    } catch (error) {
      console.error('Export error:', error);
      toast.error('Failed to export resume. Please try again.');
      setIsExporting(false);
    }
  };

  const handlePrint = () => {
    if (templateRef.current) {
      const printWindow = window.open('', '_blank');
      if (printWindow) {
        printWindow.document.write(`
          <!DOCTYPE html>
          <html>
            <head>
              <title>${resume.title}</title>
              <style>
                @media print {
                  body { margin: 0; }
                  .template-renderer { 
                    transform: none !important; 
                    width: 100% !important; 
                  }
                }
                ${getTemplateStyles()}
              </style>
            </head>
            <body>
              ${templateRef.current.innerHTML}
            </body>
          </html>
        `);
        printWindow.document.close();
        printWindow.print();
      }
    }
  };

  const getTemplateStyles = () => {
    // Extract styles from the current document
    const styleSheets = Array.from(document.styleSheets);
    let styles = '';
    
    styleSheets.forEach(sheet => {
      try {
        const rules = Array.from(sheet.cssRules || []);
        rules.forEach(rule => {
          styles += rule.cssText + '\n';
        });
      } catch (e) {
        // Cross-origin stylesheets may not be accessible
      }
    });
    
    return styles;
  };

  const updateExportOptions = (key: keyof ExportOptions, value: any) => {
    setExportOptions(prev => ({
      ...prev,
      [key]: value,
    }));
  };

  const updateMargins = (side: keyof ExportOptions['margins'], value: number) => {
    setExportOptions(prev => ({
      ...prev,
      margins: {
        ...prev.margins,
        [side]: value,
      },
    }));
  };

  return (
    <div className={cn('space-y-6', className)}>
      {/* Export Options */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Icons.download className="h-5 w-5" />
            Export Options
          </CardTitle>
          <CardDescription>
            Customize your export settings before downloading
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Format */}
            <div className="space-y-2">
              <Label>Format</Label>
              <Select 
                value={exportOptions.format} 
                onValueChange={(value) => updateExportOptions('format', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="pdf">PDF</SelectItem>
                  <SelectItem value="docx">Word Document</SelectItem>
                  <SelectItem value="html">HTML</SelectItem>
                  <SelectItem value="png">PNG Image</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Quality */}
            <div className="space-y-2">
              <Label>Quality</Label>
              <Select 
                value={exportOptions.quality} 
                onValueChange={(value) => updateExportOptions('quality', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="draft">Draft</SelectItem>
                  <SelectItem value="standard">Standard</SelectItem>
                  <SelectItem value="high">High</SelectItem>
                  <SelectItem value="print">Print Ready</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Page Size */}
            <div className="space-y-2">
              <Label>Page Size</Label>
              <Select 
                value={exportOptions.pageSize} 
                onValueChange={(value) => updateExportOptions('pageSize', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="a4">A4</SelectItem>
                  <SelectItem value="letter">Letter</SelectItem>
                  <SelectItem value="legal">Legal</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Orientation */}
            <div className="space-y-2">
              <Label>Orientation</Label>
              <Select 
                value={exportOptions.orientation} 
                onValueChange={(value) => updateExportOptions('orientation', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="portrait">Portrait</SelectItem>
                  <SelectItem value="landscape">Landscape</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Margins */}
          <div className="space-y-2">
            <Label>Margins (mm)</Label>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
              <div>
                <Label className="text-xs">Top</Label>
                <Select 
                  value={exportOptions.margins.top.toString()} 
                  onValueChange={(value) => updateMargins('top', parseInt(value))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="10">10mm</SelectItem>
                    <SelectItem value="15">15mm</SelectItem>
                    <SelectItem value="20">20mm</SelectItem>
                    <SelectItem value="25">25mm</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label className="text-xs">Right</Label>
                <Select 
                  value={exportOptions.margins.right.toString()} 
                  onValueChange={(value) => updateMargins('right', parseInt(value))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="10">10mm</SelectItem>
                    <SelectItem value="15">15mm</SelectItem>
                    <SelectItem value="20">20mm</SelectItem>
                    <SelectItem value="25">25mm</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label className="text-xs">Bottom</Label>
                <Select 
                  value={exportOptions.margins.bottom.toString()} 
                  onValueChange={(value) => updateMargins('bottom', parseInt(value))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="10">10mm</SelectItem>
                    <SelectItem value="15">15mm</SelectItem>
                    <SelectItem value="20">20mm</SelectItem>
                    <SelectItem value="25">25mm</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label className="text-xs">Left</Label>
                <Select 
                  value={exportOptions.margins.left.toString()} 
                  onValueChange={(value) => updateMargins('left', parseInt(value))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="10">10mm</SelectItem>
                    <SelectItem value="15">15mm</SelectItem>
                    <SelectItem value="20">20mm</SelectItem>
                    <SelectItem value="25">25mm</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          {/* Additional Options */}
          <div className="space-y-3">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="includeMetadata"
                checked={exportOptions.includeMetadata}
                onCheckedChange={(checked) => updateExportOptions('includeMetadata', checked)}
              />
              <Label htmlFor="includeMetadata" className="text-sm">
                Include metadata (title, author, creation date)
              </Label>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-3 pt-4">
            <Button 
              onClick={handleExport} 
              disabled={isExporting}
              className="flex-1 sm:flex-none"
            >
              {isExporting && <LoadingSpinner size="sm" className="mr-2" />}
              {isExporting ? 'Exporting...' : `Export as ${exportOptions.format.toUpperCase()}`}
            </Button>
            <Button 
              variant="outline" 
              onClick={handlePrint}
              className="flex-1 sm:flex-none"
            >
              <Icons.printer className="mr-2 h-4 w-4" />
              Print
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Preview */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Export Preview</CardTitle>
              <CardDescription>
                Preview how your resume will look when exported
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              <Label className="text-sm">Scale:</Label>
              <Select 
                value={previewScale.toString()} 
                onValueChange={(value) => setPreviewScale(parseFloat(value))}
              >
                <SelectTrigger className="w-20">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="0.5">50%</SelectItem>
                  <SelectItem value="0.75">75%</SelectItem>
                  <SelectItem value="1">100%</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="border rounded-lg p-4 bg-gray-50 overflow-auto">
            <div className="mx-auto" style={{ width: 'fit-content' }}>
              <TemplateRenderer
                ref={templateRef}
                resume={resume}
                template={template}
                scale={previewScale}
                preview={false}
                interactive={false}
              />
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
