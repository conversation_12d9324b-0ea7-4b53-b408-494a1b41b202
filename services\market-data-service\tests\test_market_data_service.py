"""
Comprehensive Test Suite for Market Data Service
Tests all components of FR-5.2: Job Market Data Ingestion Service
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from datetime import datetime, timedelta
import json

# Import modules to test
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from scrapers.base_scraper import BaseScraper, JobPosting
from scrapers.linkedin_scraper import LinkedInScraper
from scrapers.indeed_scraper import IndeedScraper
from scrapers.company_scraper import CompanyScraper
from processors.job_processor import JobProcessor
from storage.database_manager import DatabaseManager
from utils.config import Config
from utils.ai_client import AIClient
from main import MarketDataService

class TestJobPosting:
    """Test JobPosting data class"""
    
    def test_job_posting_creation(self):
        """Test creating a JobPosting instance"""
        job = JobPosting(
            title="Software Engineer",
            company="Tech Corp",
            description="Great opportunity for a software engineer",
            location="San Francisco, CA",
            salary_min=100000,
            salary_max=150000,
            source="test"
        )
        
        assert job.title == "Software Engineer"
        assert job.company == "Tech Corp"
        assert job.salary_min == 100000
        assert job.salary_max == 150000
        assert job.source == "test"

class TestBaseScraper:
    """Test BaseScraper functionality"""
    
    def setup_method(self):
        """Setup test fixtures"""
        self.scraper = BaseScraper("test", "https://example.com", 0.1)
    
    def test_scraper_initialization(self):
        """Test scraper initialization"""
        assert self.scraper.source_name == "test"
        assert self.scraper.base_url == "https://example.com"
        assert self.scraper.rate_limit == 0.1
    
    def test_extract_salary_range(self):
        """Test salary range extraction"""
        # Test range format
        min_sal, max_sal = self.scraper.extract_salary_range("$80,000 - $120,000")
        assert min_sal == 80000
        assert max_sal == 120000
        
        # Test abbreviated format
        min_sal, max_sal = self.scraper.extract_salary_range("80k-120k")
        assert min_sal == 80000
        assert max_sal == 120000
        
        # Test single value
        min_sal, max_sal = self.scraper.extract_salary_range("$100,000")
        assert min_sal == 100000
        assert max_sal == 100000
        
        # Test invalid input
        min_sal, max_sal = self.scraper.extract_salary_range("negotiable")
        assert min_sal is None
        assert max_sal is None
    
    def test_extract_experience_level(self):
        """Test experience level extraction"""
        assert self.scraper.extract_experience_level("Senior Software Engineer") == "SENIOR"
        assert self.scraper.extract_experience_level("Junior Developer") == "ENTRY"
        assert self.scraper.extract_experience_level("Director of Engineering") == "EXECUTIVE"
        assert self.scraper.extract_experience_level("Mid-level Developer") == "MID"
        assert self.scraper.extract_experience_level("Software Engineer") is None
    
    def test_extract_job_type(self):
        """Test job type extraction"""
        assert self.scraper.extract_job_type("Remote Software Engineer") == "REMOTE"
        assert self.scraper.extract_job_type("Contract Developer") == "CONTRACT"
        assert self.scraper.extract_job_type("Part-time Designer") == "PART_TIME"
        assert self.scraper.extract_job_type("Full-time Engineer") == "FULL_TIME"
    
    def test_extract_skills_from_text(self):
        """Test skill extraction from text"""
        text = "We need someone with JavaScript, React, Node.js, and Python experience"
        skills = self.scraper.extract_skills_from_text(text)
        
        assert "JavaScript" in skills
        assert "React" in skills
        assert "Node.js" in skills
        assert "Python" in skills
    
    def test_validate_job_posting(self):
        """Test job posting validation"""
        # Valid job
        valid_job = JobPosting(
            title="Software Engineer",
            company="Tech Corp",
            description="A great opportunity for a software engineer with 3+ years of experience",
            source="test"
        )
        assert self.scraper.validate_job_posting(valid_job) == True
        
        # Invalid job - missing required fields
        invalid_job = JobPosting(
            title="",
            company="Tech Corp",
            description="Short",
            source="test"
        )
        assert self.scraper.validate_job_posting(invalid_job) == False

class TestLinkedInScraper:
    """Test LinkedIn scraper"""
    
    def setup_method(self):
        """Setup test fixtures"""
        self.scraper = LinkedInScraper()
    
    def test_build_search_url(self):
        """Test LinkedIn search URL building"""
        url = self.scraper.build_search_url("Software Engineer", "San Francisco", 1)
        
        assert "linkedin.com/jobs/search" in url
        assert "keywords=Software%20Engineer" in url
        assert "location=San%20Francisco" in url
        assert "start=0" in url
    
    @pytest.mark.asyncio
    async def test_scrape_with_mock(self):
        """Test scraping with mocked responses"""
        with patch.object(self.scraper, 'scrape_with_retry') as mock_scrape:
            # Mock HTML response
            mock_html = """
            <div class="job-search-card">
                <h3 class="job-search-card__title">
                    <a href="/jobs/view/123">Software Engineer</a>
                </h3>
                <h4 class="job-search-card__subtitle">Tech Corp</h4>
                <span class="job-search-card__location">San Francisco, CA</span>
            </div>
            """
            
            from bs4 import BeautifulSoup
            mock_soup = BeautifulSoup(mock_html, 'html.parser')
            mock_scrape.return_value = mock_soup
            
            jobs = await self.scraper.scrape_jobs(["Software Engineer"], ["San Francisco"], 1)
            
            # Should process the mocked job
            assert len(jobs) >= 0  # May be 0 if validation fails, but shouldn't error

class TestIndeedScraper:
    """Test Indeed scraper"""
    
    def setup_method(self):
        """Setup test fixtures"""
        self.scraper = IndeedScraper()
    
    def test_build_search_url(self):
        """Test Indeed search URL building"""
        url = self.scraper.build_search_url("Data Scientist", "New York", 2)
        
        assert "indeed.com/jobs" in url
        assert "q=Data%20Scientist" in url
        assert "l=New%20York" in url
        assert "start=10" in url  # Page 2 = start at 10

class TestCompanyScraper:
    """Test Company scraper"""
    
    def setup_method(self):
        """Setup test fixtures"""
        self.scraper = CompanyScraper()
    
    def test_company_configs(self):
        """Test company configuration setup"""
        assert 'google' in self.scraper.company_configs
        assert 'microsoft' in self.scraper.company_configs
        assert 'amazon' in self.scraper.company_configs
        
        google_config = self.scraper.company_configs['google']
        assert 'base_url' in google_config
        assert 'selectors' in google_config
    
    def test_build_company_search_url(self):
        """Test company search URL building"""
        google_config = self.scraper.company_configs['google']
        url = self.scraper._build_company_search_url(google_config, "Engineer", "California")
        
        assert "careers.google.com" in url
        assert "Engineer" in url

class TestJobProcessor:
    """Test Job Processor"""
    
    def setup_method(self):
        """Setup test fixtures"""
        self.processor = JobProcessor()
    
    def test_generate_job_hash(self):
        """Test job hash generation"""
        job = JobPosting(
            title="Software Engineer",
            company="Tech Corp",
            description="Great opportunity",
            location="SF",
            source="test"
        )
        
        hash1 = self.processor._generate_job_hash(job)
        hash2 = self.processor._generate_job_hash(job)
        
        assert hash1 == hash2  # Same job should generate same hash
        assert len(hash1) == 12  # Hash should be 12 characters
    
    def test_classify_industry(self):
        """Test industry classification"""
        tech_job = JobPosting(
            title="Software Engineer",
            company="Tech Corp",
            description="We are a software company looking for engineers",
            source="test"
        )
        
        industry = self.processor._classify_industry(tech_job)
        assert industry == "Technology"
        
        finance_job = JobPosting(
            title="Financial Analyst",
            company="Bank Corp",
            description="We are a banking institution",
            source="test"
        )
        
        industry = self.processor._classify_industry(finance_job)
        assert industry == "Finance"
    
    def test_extract_skills(self):
        """Test skill extraction"""
        job = JobPosting(
            title="Full Stack Developer",
            company="Tech Corp",
            description="Looking for someone with JavaScript, React, Node.js, Python, and AWS experience",
            source="test"
        )
        
        skills = self.processor._extract_skills(job)
        
        assert "JavaScript" in skills
        assert "React" in skills
        assert "Python" in skills
        assert "AWS" in skills
    
    def test_standardize_experience_level(self):
        """Test experience level standardization"""
        senior_job = JobPosting(
            title="Senior Software Engineer",
            company="Tech Corp",
            description="5+ years of experience required",
            source="test"
        )
        
        level = self.processor._standardize_experience_level(senior_job)
        assert level == "SENIOR"
        
        entry_job = JobPosting(
            title="Junior Developer",
            company="Tech Corp",
            description="Entry level position for new graduates",
            source="test"
        )
        
        level = self.processor._standardize_experience_level(entry_job)
        assert level == "ENTRY"
    
    @pytest.mark.asyncio
    async def test_process_single_job(self):
        """Test processing a single job"""
        job = JobPosting(
            title="Software Engineer",
            company="Tech Corp",
            description="We are looking for a software engineer with JavaScript and React experience",
            location="San Francisco, CA",
            source="test"
        )
        
        # Mock AI client to avoid API calls
        with patch.object(self.processor, 'config') as mock_config:
            mock_config.enable_ai_processing = False
            mock_config.enable_vectorization = False
            
            processed_job = await self.processor.process_single_job(job)
            
            assert processed_job.external_id is not None
            assert processed_job.industry is not None
            assert processed_job.experience_level in ["ENTRY", "MID", "SENIOR", "EXECUTIVE"]
            assert processed_job.job_type in ["FULL_TIME", "PART_TIME", "CONTRACT", "REMOTE"]

class TestDatabaseManager:
    """Test Database Manager"""
    
    def setup_method(self):
        """Setup test fixtures"""
        with patch('storage.database_manager.create_async_engine'):
            self.db_manager = DatabaseManager()
    
    def test_calculate_data_completeness(self):
        """Test data completeness calculation"""
        complete_job = JobPosting(
            title="Software Engineer",
            company="Tech Corp",
            description="A comprehensive job description with lots of details about the role and requirements",
            location="San Francisco, CA",
            salary_min=100000,
            salary_max=150000,
            skills=["JavaScript", "React", "Node.js"],
            requirements="Bachelor's degree required",
            experience_level="MID",
            job_type="FULL_TIME",
            industry="Technology",
            source="test"
        )
        
        score = self.db_manager._calculate_data_completeness(complete_job)
        assert score >= 0.8  # Should be high completeness
        
        minimal_job = JobPosting(
            title="Engineer",
            company="Corp",
            description="Job",
            source="test"
        )
        
        score = self.db_manager._calculate_data_completeness(minimal_job)
        assert score <= 0.5  # Should be low completeness

class TestConfig:
    """Test Configuration"""
    
    def test_config_initialization(self):
        """Test config initialization"""
        config = Config()
        
        assert hasattr(config, 'database_url')
        assert hasattr(config, 'scraper_configs')
        assert 'linkedin' in config.scraper_configs
        assert 'indeed' in config.scraper_configs
        assert 'company' in config.scraper_configs
    
    def test_scraper_config_methods(self):
        """Test scraper configuration methods"""
        config = Config()
        
        # Test enabled check
        assert isinstance(config.is_scraper_enabled('linkedin'), bool)
        
        # Test max pages
        assert isinstance(config.get_max_pages('linkedin'), int)
        assert config.get_max_pages('linkedin') > 0
        
        # Test rate limit
        assert isinstance(config.get_rate_limit('linkedin'), float)
        assert config.get_rate_limit('linkedin') > 0
    
    def test_config_validation(self):
        """Test configuration validation"""
        config = Config()
        issues = config.validate_config()
        
        # Should return list of issues (may be empty if config is valid)
        assert isinstance(issues, list)

class TestAIClient:
    """Test AI Client"""
    
    def setup_method(self):
        """Setup test fixtures"""
        with patch('utils.ai_client.AsyncOpenAI'):
            self.ai_client = AIClient()
    
    def test_ai_client_initialization(self):
        """Test AI client initialization"""
        assert hasattr(self.ai_client, 'config')
        assert hasattr(self.ai_client, 'logger')
    
    @pytest.mark.asyncio
    async def test_analyze_job_posting_mock(self):
        """Test job analysis with mocked AI"""
        if self.ai_client.client:
            with patch.object(self.ai_client.client.chat.completions, 'create') as mock_create:
                mock_response = Mock()
                mock_response.choices = [Mock()]
                mock_response.choices[0].message.content = '{"skills": ["Python", "JavaScript"]}'
                mock_create.return_value = mock_response
                
                result = await self.ai_client.analyze_job_posting("Test prompt")
                assert result is not None

class TestMarketDataService:
    """Test main Market Data Service"""
    
    def setup_method(self):
        """Setup test fixtures"""
        with patch('main.DatabaseManager'), \
             patch('main.JobProcessor'), \
             patch('main.LinkedInScraper'), \
             patch('main.IndeedScraper'), \
             patch('main.CompanyScraper'):
            self.service = MarketDataService()
    
    def test_service_initialization(self):
        """Test service initialization"""
        assert hasattr(self.service, 'scrapers')
        assert hasattr(self.service, 'processor')
        assert hasattr(self.service, 'db_manager')
        assert 'linkedin' in self.service.scrapers
        assert 'indeed' in self.service.scrapers
        assert 'company' in self.service.scrapers
    
    @pytest.mark.asyncio
    async def test_run_manual_scraping(self):
        """Test manual scraping execution"""
        # Mock scraper methods
        for scraper in self.service.scrapers.values():
            scraper.scrape_jobs = AsyncMock(return_value=[])
        
        self.service.processor.process_job_batch = AsyncMock(return_value=[])
        self.service.db_manager.store_jobs = AsyncMock(return_value=0)
        
        result = await self.service.run_manual_scraping(['linkedin'])
        
        assert 'manual_run' in result
        assert result['manual_run'] == True
        assert 'sources' in result

# Integration tests
class TestIntegration:
    """Integration tests for the complete pipeline"""
    
    @pytest.mark.asyncio
    async def test_end_to_end_pipeline(self):
        """Test complete job processing pipeline"""
        # Create a sample job
        job = JobPosting(
            title="Senior Software Engineer",
            company="Tech Corp",
            description="We are looking for a senior software engineer with 5+ years of experience in JavaScript, React, and Node.js",
            location="San Francisco, CA",
            salary_min=120000,
            salary_max=180000,
            source="test"
        )
        
        # Process the job
        processor = JobProcessor()
        
        with patch.object(processor, 'config') as mock_config:
            mock_config.enable_ai_processing = False
            mock_config.enable_vectorization = False
            
            processed_job = await processor.process_single_job(job)
            
            # Verify processing results
            assert processed_job.external_id is not None
            assert processed_job.industry is not None
            assert processed_job.experience_level == "SENIOR"
            assert processed_job.skills is not None
            assert len(processed_job.skills) > 0
            
            # Verify validation
            assert processor.validate_processed_job(processed_job) == True

if __name__ == "__main__":
    pytest.main([__file__, "-v"])
