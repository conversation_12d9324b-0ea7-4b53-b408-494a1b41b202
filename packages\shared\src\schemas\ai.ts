/**
 * AI Content Generation Validation Schemas
 * 
 * Zod schemas for validating AI-related data throughout the application.
 */

import { z } from 'zod';
import {
  ContentType,
  ExperienceLevel,
  ContentTone,
  ContentLength,
  ContentStyle,
  ATSIssueType,
  IssueSeverity,
  RecommendationType,
  EffortLevel,
  KeywordCategory,
  VariableType,
  AIProvider,
} from '../types/ai';

// Content context schema
export const contentContextSchema = z.object({
  firstName: z.string().max(50).optional(),
  lastName: z.string().max(50).optional(),
  currentRole: z.string().max(100).optional(),
  industry: z.string().max(100).optional(),
  experienceLevel: z.nativeEnum(ExperienceLevel),
  targetJobTitle: z.string().max(100).optional(),
  targetCompany: z.string().max(100).optional(),
  targetIndustry: z.string().max(100).optional(),
  jobDescription: z.string().max(5000).optional(),
  existingContent: z.string().max(2000).optional(),
  company: z.string().max(100).optional(),
  position: z.string().max(100).optional(),
  responsibilities: z.array(z.string().max(200)).max(10).optional(),
  achievements: z.array(z.string().max(200)).max(10).optional(),
  technologies: z.array(z.string().max(50)).max(20).optional(),
  skills: z.array(z.string().max(50)).max(30).optional(),
  certifications: z.array(z.string().max(100)).max(10).optional(),
  education: z.string().max(200).optional(),
  tone: z.nativeEnum(ContentTone).optional(),
  keywords: z.array(z.string().max(50)).max(20).optional(),
  customInstructions: z.string().max(500).optional(),
});

// Generation options schema
export const generationOptionsSchema = z.object({
  length: z.nativeEnum(ContentLength),
  style: z.nativeEnum(ContentStyle),
  includeKeywords: z.boolean(),
  atsOptimized: z.boolean(),
  industrySpecific: z.boolean(),
  creativityLevel: z.number().min(0).max(1),
  maxSuggestions: z.number().min(1).max(10),
  language: z.string().min(2).max(10),
});

// AI content request schema
export const aiContentRequestSchema = z.object({
  type: z.nativeEnum(ContentType),
  context: contentContextSchema,
  options: generationOptionsSchema,
  userId: z.string().min(1),
  resumeId: z.string().optional(),
});

// Content suggestion schema
export const contentSuggestionSchema = z.object({
  id: z.string(),
  content: z.string().min(1).max(2000),
  confidence: z.number().min(0).max(1),
  reasoning: z.string().max(500),
  keywords: z.array(z.string().max(50)).max(20),
  atsScore: z.number().min(0).max(100),
  improvements: z.array(z.string().max(200)).max(5),
  alternatives: z.array(z.string().max(200)).max(3),
});

// ATS issue schema
export const atsIssueSchema = z.object({
  id: z.string(),
  type: z.nativeEnum(ATSIssueType),
  severity: z.nativeEnum(IssueSeverity),
  description: z.string().min(1).max(300),
  location: z.string().max(100),
  suggestion: z.string().min(1).max(300),
  impact: z.number().min(0).max(100),
});

// ATS recommendation schema
export const atsRecommendationSchema = z.object({
  id: z.string(),
  type: z.nativeEnum(RecommendationType),
  priority: z.number().min(1).max(10),
  title: z.string().min(1).max(100),
  description: z.string().min(1).max(300),
  implementation: z.string().min(1).max(500),
  expectedImpact: z.number().min(0).max(100),
  effort: z.nativeEnum(EffortLevel),
});

// ATS optimization data schema
export const atsOptimizationDataSchema = z.object({
  score: z.number().min(0).max(100),
  issues: z.array(atsIssueSchema).max(20),
  recommendations: z.array(atsRecommendationSchema).max(15),
  keywordMatch: z.number().min(0).max(100),
  formatCompliance: z.number().min(0).max(100),
  readabilityScore: z.number().min(0).max(100),
});

// Extracted keyword schema
export const extractedKeywordSchema = z.object({
  keyword: z.string().min(1).max(50),
  frequency: z.number().min(1),
  relevance: z.number().min(0).max(1),
  category: z.nativeEnum(KeywordCategory),
  importance: z.number().min(0).max(1),
});

// Keyword recommendation schema
export const keywordRecommendationSchema = z.object({
  keyword: z.string().min(1).max(50),
  category: z.nativeEnum(KeywordCategory),
  priority: z.number().min(1).max(10),
  reasoning: z.string().min(1).max(200),
  suggestedPlacement: z.array(z.string().max(50)).max(5),
  alternatives: z.array(z.string().max(50)).max(3),
});

// Keyword analysis schema
export const keywordAnalysisSchema = z.object({
  extractedKeywords: z.array(extractedKeywordSchema).max(50),
  missingKeywords: z.array(z.string().max(50)).max(20),
  keywordDensity: z.record(z.string(), z.number().min(0)),
  industryKeywords: z.array(z.string().max(50)).max(30),
  skillKeywords: z.array(z.string().max(50)).max(30),
  actionVerbs: z.array(z.string().max(30)).max(20),
  recommendations: z.array(keywordRecommendationSchema).max(15),
});

// Generation metadata schema
export const generationMetadataSchema = z.object({
  model: z.string().min(1).max(50),
  processingTime: z.number().min(0),
  tokensUsed: z.number().min(0),
  confidence: z.number().min(0).max(1),
  atsOptimization: atsOptimizationDataSchema,
  keywordAnalysis: keywordAnalysisSchema,
});

// AI content response schema
export const aiContentResponseSchema = z.object({
  id: z.string(),
  requestId: z.string(),
  suggestions: z.array(contentSuggestionSchema).min(1).max(10),
  metadata: generationMetadataSchema,
  createdAt: z.string(),
});

// Template variable schema
export const templateVariableSchema = z.object({
  name: z.string().min(1).max(50),
  type: z.nativeEnum(VariableType),
  required: z.boolean(),
  description: z.string().min(1).max(200),
  defaultValue: z.string().max(100).optional(),
  options: z.array(z.string().max(50)).max(20).optional(),
});

// Content template schema
export const contentTemplateSchema = z.object({
  id: z.string(),
  name: z.string().min(1).max(100),
  description: z.string().min(1).max(300),
  type: z.nativeEnum(ContentType),
  industry: z.array(z.string().max(50)).max(10),
  experienceLevel: z.array(z.nativeEnum(ExperienceLevel)).max(5),
  template: z.string().min(1).max(2000),
  variables: z.array(templateVariableSchema).max(20),
  examples: z.array(z.string().max(500)).max(5),
  tags: z.array(z.string().max(30)).max(10),
});

// Salary range schema
export const salaryRangeSchema = z.object({
  role: z.string().min(1).max(100),
  experienceLevel: z.nativeEnum(ExperienceLevel),
  minSalary: z.number().min(0),
  maxSalary: z.number().min(0),
  currency: z.string().length(3),
  location: z.string().min(1).max(100),
});

// Industry data schema
export const industryDataSchema = z.object({
  id: z.string(),
  name: z.string().min(1).max(100),
  description: z.string().min(1).max(500),
  commonRoles: z.array(z.string().max(100)).max(20),
  keySkills: z.array(z.string().max(50)).max(30),
  technologies: z.array(z.string().max(50)).max(50),
  certifications: z.array(z.string().max(100)).max(20),
  keywords: z.array(z.string().max(50)).max(50),
  trends: z.array(z.string().max(100)).max(10),
  salaryRanges: z.array(salaryRangeSchema).max(20),
});

// AI model config schema
export const aiModelConfigSchema = z.object({
  provider: z.nativeEnum(AIProvider),
  model: z.string().min(1).max(50),
  apiKey: z.string().min(1),
  baseUrl: z.string().url().optional(),
  maxTokens: z.number().min(1).max(32000),
  temperature: z.number().min(0).max(2),
  topP: z.number().min(0).max(1),
  frequencyPenalty: z.number().min(-2).max(2),
  presencePenalty: z.number().min(-2).max(2),
});

// Content quality metrics schema
export const contentQualityMetricsSchema = z.object({
  readabilityScore: z.number().min(0).max(100),
  sentimentScore: z.number().min(-1).max(1),
  professionalismScore: z.number().min(0).max(100),
  uniquenessScore: z.number().min(0).max(100),
  keywordDensity: z.number().min(0).max(100),
  actionVerbCount: z.number().min(0),
  quantifiableAchievements: z.number().min(0),
  industryRelevance: z.number().min(0).max(100),
});

// Content feedback schema
export const contentFeedbackSchema = z.object({
  id: z.string(),
  contentId: z.string(),
  userId: z.string(),
  rating: z.number().min(1).max(5),
  feedback: z.string().max(1000),
  improvements: z.array(z.string().max(200)).max(5),
  wasUsed: z.boolean(),
  wasModified: z.boolean(),
  finalContent: z.string().max(2000).optional(),
  createdAt: z.string(),
});

// AI usage analytics schema
export const aiUsageAnalyticsSchema = z.object({
  userId: z.string(),
  period: z.enum(['day', 'week', 'month']),
  requestCount: z.number().min(0),
  tokensUsed: z.number().min(0),
  contentTypes: z.record(z.nativeEnum(ContentType), z.number().min(0)),
  averageConfidence: z.number().min(0).max(1),
  successRate: z.number().min(0).max(1),
  cost: z.number().min(0),
  date: z.string(),
});

// Form schemas for API requests
export const generateContentSchema = aiContentRequestSchema.omit({ userId: true });

export const optimizeContentSchema = z.object({
  content: z.string().min(1).max(2000),
  context: contentContextSchema,
  targetJobDescription: z.string().max(5000).optional(),
});

export const analyzeATSSchema = z.object({
  resumeId: z.string(),
  jobDescription: z.string().max(5000).optional(),
  targetKeywords: z.array(z.string().max(50)).max(20).optional(),
});

// Export types inferred from schemas
export type ContentContextInput = z.infer<typeof contentContextSchema>;
export type GenerationOptionsInput = z.infer<typeof generationOptionsSchema>;
export type AIContentRequestInput = z.infer<typeof generateContentSchema>;
export type ContentOptimizationInput = z.infer<typeof optimizeContentSchema>;
export type ATSAnalysisInput = z.infer<typeof analyzeATSSchema>;
export type ContentTemplateInput = z.infer<typeof contentTemplateSchema>;
export type IndustryDataInput = z.infer<typeof industryDataSchema>;
export type ContentFeedbackInput = z.infer<typeof contentFeedbackSchema>;

// Validation helper functions
export function validateContentContext(context: any): { isValid: boolean; errors: string[] } {
  const result = contentContextSchema.safeParse(context);
  return {
    isValid: result.success,
    errors: result.success ? [] : result.error.errors.map(e => e.message),
  };
}

export function validateGenerationOptions(options: any): { isValid: boolean; errors: string[] } {
  const result = generationOptionsSchema.safeParse(options);
  return {
    isValid: result.success,
    errors: result.success ? [] : result.error.errors.map(e => e.message),
  };
}

export function validateATSOptimization(data: any): { isValid: boolean; errors: string[] } {
  const result = atsOptimizationDataSchema.safeParse(data);
  return {
    isValid: result.success,
    errors: result.success ? [] : result.error.errors.map(e => e.message),
  };
}
