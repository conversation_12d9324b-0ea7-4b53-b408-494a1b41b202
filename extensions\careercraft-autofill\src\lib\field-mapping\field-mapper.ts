/**
 * Field Mapping Engine
 * 
 * Intelligently maps form fields to user profile data
 * using semantic analysis and pattern recognition.
 */

export interface FieldMappingRequest {
  element: HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
  label: string
  placeholder: string
  type: string
}

export interface FieldMappingResult {
  mappedTo: string | null
  confidence: number
  dataType: 'string' | 'number' | 'date' | 'boolean' | 'array' | 'file'
  format?: string
  validation?: FieldValidation
}

export interface FieldValidation {
  required: boolean
  minLength?: number
  maxLength?: number
  pattern?: string
  options?: string[]
}

export class FieldMapper {
  private fieldMappings: Map<string, FieldMappingConfig> = new Map()

  constructor() {
    this.initializeFieldMappings()
  }

  /**
   * Map a form field to profile data
   */
  async mapField(request: FieldMappingRequest): Promise<FieldMappingResult> {
    const { element, label, placeholder, type } = request

    // Combine all text sources for analysis
    const textSources = [
      label.toLowerCase(),
      placeholder.toLowerCase(),
      element.name?.toLowerCase() || '',
      element.id?.toLowerCase() || '',
      element.className?.toLowerCase() || ''
    ].filter(Boolean)

    const combinedText = textSources.join(' ')

    // Find best mapping
    let bestMapping: FieldMappingConfig | null = null
    let bestScore = 0

    for (const [key, mapping] of this.fieldMappings) {
      const score = this.calculateMappingScore(combinedText, mapping, type, element)
      if (score > bestScore) {
        bestScore = score
        bestMapping = mapping
      }
    }

    if (!bestMapping || bestScore < 0.3) {
      return {
        mappedTo: null,
        confidence: 0,
        dataType: this.inferDataType(type, element)
      }
    }

    return {
      mappedTo: bestMapping.key,
      confidence: Math.min(bestScore, 0.95),
      dataType: bestMapping.dataType,
      format: bestMapping.format,
      validation: this.extractValidation(element)
    }
  }

  /**
   * Calculate mapping score for a field
   */
  private calculateMappingScore(
    text: string,
    mapping: FieldMappingConfig,
    type: string,
    element: HTMLElement
  ): number {
    let score = 0

    // Exact keyword matches
    for (const keyword of mapping.keywords) {
      if (text.includes(keyword.toLowerCase())) {
        score += 0.3
      }
    }

    // Partial keyword matches
    for (const keyword of mapping.keywords) {
      const words = keyword.toLowerCase().split(' ')
      const matchedWords = words.filter(word => text.includes(word))
      if (matchedWords.length > 0) {
        score += (matchedWords.length / words.length) * 0.2
      }
    }

    // Type compatibility
    if (mapping.htmlTypes.includes(type)) {
      score += 0.2
    }

    // Element attribute analysis
    const name = element.getAttribute('name')?.toLowerCase() || ''
    const id = element.id?.toLowerCase() || ''
    
    if (mapping.namePatterns.some(pattern => 
      name.includes(pattern) || id.includes(pattern)
    )) {
      score += 0.25
    }

    // Context analysis (surrounding elements)
    const context = this.getFieldContext(element)
    for (const contextKeyword of mapping.contextKeywords || []) {
      if (context.includes(contextKeyword.toLowerCase())) {
        score += 0.1
      }
    }

    return Math.min(score, 1.0)
  }

  /**
   * Get context around a field element
   */
  private getFieldContext(element: HTMLElement): string {
    const contexts: string[] = []

    // Parent element text
    let parent = element.parentElement
    let depth = 0
    while (parent && depth < 3) {
      const text = parent.textContent?.trim()
      if (text && text.length < 200) {
        contexts.push(text.toLowerCase())
      }
      parent = parent.parentElement
      depth++
    }

    // Sibling elements
    const siblings = Array.from(element.parentElement?.children || [])
    siblings.forEach(sibling => {
      if (sibling !== element) {
        const text = sibling.textContent?.trim()
        if (text && text.length < 100) {
          contexts.push(text.toLowerCase())
        }
      }
    })

    return contexts.join(' ')
  }

  /**
   * Infer data type from HTML type and element
   */
  private inferDataType(
    type: string,
    element: HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
  ): FieldMappingResult['dataType'] {
    switch (type) {
      case 'email':
      case 'url':
      case 'tel':
      case 'text':
      case 'textarea':
        return 'string'
      case 'number':
        return 'number'
      case 'date':
      case 'datetime-local':
      case 'month':
      case 'week':
        return 'date'
      case 'checkbox':
        return 'boolean'
      case 'file':
        return 'file'
      case 'select':
        return element.hasAttribute('multiple') ? 'array' : 'string'
      default:
        return 'string'
    }
  }

  /**
   * Extract validation rules from element
   */
  private extractValidation(
    element: HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
  ): FieldValidation {
    const validation: FieldValidation = {
      required: element.hasAttribute('required') || 
                element.getAttribute('aria-required') === 'true'
    }

    if (element instanceof HTMLInputElement) {
      if (element.minLength > 0) validation.minLength = element.minLength
      if (element.maxLength > 0) validation.maxLength = element.maxLength
      if (element.pattern) validation.pattern = element.pattern
    }

    if (element instanceof HTMLSelectElement) {
      const options = Array.from(element.options)
        .map(option => option.value)
        .filter(value => value !== '')
      if (options.length > 0) {
        validation.options = options
      }
    }

    return validation
  }

  /**
   * Initialize field mapping configurations
   */
  private initializeFieldMappings() {
    const mappings: FieldMappingConfig[] = [
      // Personal Information
      {
        key: 'firstName',
        keywords: ['first name', 'given name', 'fname'],
        namePatterns: ['firstname', 'fname', 'given', 'first'],
        htmlTypes: ['text'],
        dataType: 'string',
        contextKeywords: ['personal', 'contact', 'name']
      },
      {
        key: 'lastName',
        keywords: ['last name', 'family name', 'surname', 'lname'],
        namePatterns: ['lastname', 'lname', 'family', 'surname', 'last'],
        htmlTypes: ['text'],
        dataType: 'string',
        contextKeywords: ['personal', 'contact', 'name']
      },
      {
        key: 'fullName',
        keywords: ['full name', 'name', 'your name'],
        namePatterns: ['fullname', 'name', 'applicant'],
        htmlTypes: ['text'],
        dataType: 'string',
        contextKeywords: ['personal', 'contact']
      },
      {
        key: 'email',
        keywords: ['email', 'email address', 'e-mail'],
        namePatterns: ['email', 'mail'],
        htmlTypes: ['email', 'text'],
        dataType: 'string',
        format: 'email',
        contextKeywords: ['contact', 'personal']
      },
      {
        key: 'phone',
        keywords: ['phone', 'telephone', 'mobile', 'cell'],
        namePatterns: ['phone', 'tel', 'mobile', 'cell'],
        htmlTypes: ['tel', 'text'],
        dataType: 'string',
        format: 'phone',
        contextKeywords: ['contact', 'personal']
      },
      {
        key: 'address',
        keywords: ['address', 'street address', 'home address'],
        namePatterns: ['address', 'street', 'addr'],
        htmlTypes: ['text', 'textarea'],
        dataType: 'string',
        contextKeywords: ['contact', 'personal', 'location']
      },
      {
        key: 'city',
        keywords: ['city', 'town'],
        namePatterns: ['city', 'town'],
        htmlTypes: ['text'],
        dataType: 'string',
        contextKeywords: ['address', 'location']
      },
      {
        key: 'state',
        keywords: ['state', 'province', 'region'],
        namePatterns: ['state', 'province', 'region'],
        htmlTypes: ['text', 'select'],
        dataType: 'string',
        contextKeywords: ['address', 'location']
      },
      {
        key: 'zipCode',
        keywords: ['zip', 'postal code', 'zip code'],
        namePatterns: ['zip', 'postal', 'postcode'],
        htmlTypes: ['text'],
        dataType: 'string',
        contextKeywords: ['address', 'location']
      },
      {
        key: 'country',
        keywords: ['country'],
        namePatterns: ['country'],
        htmlTypes: ['text', 'select'],
        dataType: 'string',
        contextKeywords: ['address', 'location']
      },

      // Professional Information
      {
        key: 'currentTitle',
        keywords: ['current title', 'job title', 'position', 'current position'],
        namePatterns: ['title', 'position', 'role'],
        htmlTypes: ['text'],
        dataType: 'string',
        contextKeywords: ['work', 'employment', 'current']
      },
      {
        key: 'currentCompany',
        keywords: ['current company', 'employer', 'current employer'],
        namePatterns: ['company', 'employer', 'organization'],
        htmlTypes: ['text'],
        dataType: 'string',
        contextKeywords: ['work', 'employment', 'current']
      },
      {
        key: 'yearsExperience',
        keywords: ['years of experience', 'experience', 'years experience'],
        namePatterns: ['experience', 'years'],
        htmlTypes: ['number', 'text', 'select'],
        dataType: 'number',
        contextKeywords: ['work', 'employment', 'professional']
      },
      {
        key: 'salary',
        keywords: ['salary', 'current salary', 'compensation'],
        namePatterns: ['salary', 'compensation', 'pay'],
        htmlTypes: ['number', 'text'],
        dataType: 'number',
        format: 'currency',
        contextKeywords: ['compensation', 'salary', 'pay']
      },
      {
        key: 'expectedSalary',
        keywords: ['expected salary', 'desired salary', 'salary expectation'],
        namePatterns: ['expected', 'desired', 'target'],
        htmlTypes: ['number', 'text'],
        dataType: 'number',
        format: 'currency',
        contextKeywords: ['compensation', 'salary', 'expected']
      },

      // Education
      {
        key: 'education',
        keywords: ['education', 'degree', 'university', 'college'],
        namePatterns: ['education', 'degree', 'school'],
        htmlTypes: ['text', 'select'],
        dataType: 'string',
        contextKeywords: ['education', 'academic']
      },
      {
        key: 'major',
        keywords: ['major', 'field of study', 'specialization'],
        namePatterns: ['major', 'field', 'specialization'],
        htmlTypes: ['text'],
        dataType: 'string',
        contextKeywords: ['education', 'academic', 'study']
      },
      {
        key: 'gpa',
        keywords: ['gpa', 'grade point average'],
        namePatterns: ['gpa', 'grade'],
        htmlTypes: ['number', 'text'],
        dataType: 'number',
        contextKeywords: ['education', 'academic', 'grade']
      },

      // Application Specific
      {
        key: 'resume',
        keywords: ['resume', 'cv', 'curriculum vitae'],
        namePatterns: ['resume', 'cv', 'file'],
        htmlTypes: ['file'],
        dataType: 'file',
        contextKeywords: ['upload', 'attach', 'document']
      },
      {
        key: 'coverLetter',
        keywords: ['cover letter', 'covering letter', 'motivation letter'],
        namePatterns: ['cover', 'letter', 'motivation'],
        htmlTypes: ['file', 'textarea'],
        dataType: 'file',
        contextKeywords: ['upload', 'attach', 'document']
      },
      {
        key: 'portfolio',
        keywords: ['portfolio', 'work samples', 'portfolio url'],
        namePatterns: ['portfolio', 'samples', 'work'],
        htmlTypes: ['url', 'text', 'file'],
        dataType: 'string',
        format: 'url',
        contextKeywords: ['portfolio', 'samples', 'work']
      },
      {
        key: 'linkedin',
        keywords: ['linkedin', 'linkedin profile', 'linkedin url'],
        namePatterns: ['linkedin', 'profile'],
        htmlTypes: ['url', 'text'],
        dataType: 'string',
        format: 'url',
        contextKeywords: ['social', 'profile', 'linkedin']
      },
      {
        key: 'availability',
        keywords: ['availability', 'start date', 'available'],
        namePatterns: ['availability', 'start', 'available'],
        htmlTypes: ['date', 'text', 'select'],
        dataType: 'date',
        contextKeywords: ['start', 'availability', 'when']
      },
      {
        key: 'workAuthorization',
        keywords: ['work authorization', 'visa status', 'eligible to work'],
        namePatterns: ['authorization', 'visa', 'eligible'],
        htmlTypes: ['select', 'radio', 'checkbox'],
        dataType: 'string',
        contextKeywords: ['authorization', 'visa', 'legal']
      }
    ]

    mappings.forEach(mapping => {
      this.fieldMappings.set(mapping.key, mapping)
    })
  }
}

interface FieldMappingConfig {
  key: string
  keywords: string[]
  namePatterns: string[]
  htmlTypes: string[]
  dataType: FieldMappingResult['dataType']
  format?: string
  contextKeywords?: string[]
}
