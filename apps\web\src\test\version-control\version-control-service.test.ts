/**
 * Version Control Service Unit Tests
 * 
 * Tests for version control database operations and business logic
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { VersionControlService } from '@/lib/version-control/service'

// Mock Prisma
const mockPrisma = {
  resume: {
    findFirst: vi.fn(),
    update: vi.fn()
  },
  resumeVersion: {
    create: vi.fn(),
    findMany: vi.fn(),
    findFirst: vi.fn(),
    findUnique: vi.fn(),
    deleteMany: vi.fn()
  },
  versionComparison: {
    findUnique: vi.fn(),
    create: vi.fn()
  },
  resumeBackup: {
    create: vi.fn(),
    findMany: vi.fn(),
    findUnique: vi.fn(),
    deleteMany: vi.fn()
  },
  versionActivity: {
    create: vi.fn(),
    findMany: vi.fn()
  },
  $transaction: vi.fn()
}

vi.mock('@/lib/db', () => ({
  prisma: mockPrisma
}))

describe('VersionControlService', () => {
  let service: VersionControlService

  beforeEach(() => {
    service = new VersionControlService()
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('createVersion', () => {
    const mockResume = {
      id: 'resume-1',
      userId: 'user-1',
      title: 'Test Resume',
      personalInfo: JSON.stringify({ name: 'John Doe' }),
      sections: JSON.stringify({ experience: [] }),
      template: 'modern',
      theme: 'blue',
      updatedAt: new Date()
    }

    const mockCreatedVersion = {
      id: 'version-1',
      resumeId: 'resume-1',
      versionNumber: 1,
      versionName: 'Initial version',
      contentSnapshot: JSON.stringify({}),
      changeSummary: 'Created initial version',
      changeType: 'manual',
      createdBy: 'user-1',
      createdAt: new Date(),
      metadata: null,
      creator: {
        id: 'user-1',
        name: 'John Doe',
        image: null
      }
    }

    beforeEach(() => {
      mockPrisma.resume.findFirst.mockResolvedValue(mockResume)
      mockPrisma.resumeVersion.findFirst.mockResolvedValue(null)
      mockPrisma.resumeVersion.create.mockResolvedValue(mockCreatedVersion)
      mockPrisma.versionActivity.create.mockResolvedValue({})
    })

    it('should create a new version successfully', async () => {
      const request = {
        resumeId: 'resume-1',
        userId: 'user-1',
        versionName: 'Initial version',
        changeType: 'manual' as const,
        changeSummary: 'Created initial version'
      }

      const result = await service.createVersion(request)

      expect(mockPrisma.resume.findFirst).toHaveBeenCalledWith({
        where: {
          id: 'resume-1',
          userId: 'user-1'
        }
      })

      expect(mockPrisma.resumeVersion.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          resumeId: 'resume-1',
          versionNumber: 1,
          versionName: 'Initial version',
          changeSummary: 'Created initial version',
          changeType: 'manual',
          createdBy: 'user-1'
        }),
        include: {
          creator: {
            select: {
              id: true,
              name: true,
              image: true
            }
          }
        }
      })

      expect(result).toEqual({
        id: 'version-1',
        resumeId: 'resume-1',
        versionNumber: 1,
        versionName: 'Initial version',
        changeSummary: 'Created initial version',
        changeType: 'manual',
        createdBy: 'user-1',
        createdAt: mockCreatedVersion.createdAt,
        metadata: null,
        creator: mockCreatedVersion.creator
      })
    })

    it('should increment version number correctly', async () => {
      const lastVersion = {
        versionNumber: 5
      }
      mockPrisma.resumeVersion.findFirst.mockResolvedValue(lastVersion)

      const request = {
        resumeId: 'resume-1',
        userId: 'user-1',
        changeType: 'auto' as const
      }

      await service.createVersion(request)

      expect(mockPrisma.resumeVersion.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          versionNumber: 6
        }),
        include: expect.any(Object)
      })
    })

    it('should throw error for non-existent resume', async () => {
      mockPrisma.resume.findFirst.mockResolvedValue(null)

      const request = {
        resumeId: 'non-existent',
        userId: 'user-1',
        changeType: 'auto' as const
      }

      await expect(service.createVersion(request)).rejects.toThrow('Resume not found or access denied')
    })

    it('should create content snapshot correctly', async () => {
      const request = {
        resumeId: 'resume-1',
        userId: 'user-1',
        changeType: 'auto' as const
      }

      await service.createVersion(request)

      const createCall = mockPrisma.resumeVersion.create.mock.calls[0][0]
      const contentSnapshot = JSON.parse(createCall.data.contentSnapshot)

      expect(contentSnapshot).toEqual({
        personalInfo: { name: 'John Doe' },
        sections: { experience: [] },
        template: 'modern',
        theme: 'blue',
        title: 'Test Resume',
        updatedAt: mockResume.updatedAt
      })
    })

    it('should log activity after version creation', async () => {
      const request = {
        resumeId: 'resume-1',
        userId: 'user-1',
        changeType: 'manual' as const,
        changeSummary: 'Test change'
      }

      await service.createVersion(request)

      expect(mockPrisma.versionActivity.create).toHaveBeenCalledWith({
        data: {
          resumeId: 'resume-1',
          versionId: 'version-1',
          activityType: 'created',
          userId: 'user-1',
          activityData: JSON.stringify({
            versionNumber: 1,
            changeType: 'manual',
            changeSummary: 'Test change'
          })
        }
      })
    })
  })

  describe('getVersions', () => {
    const mockVersions = [
      {
        id: 'version-2',
        resumeId: 'resume-1',
        versionNumber: 2,
        versionName: 'Latest',
        changeSummary: 'Updated experience',
        changeType: 'auto',
        createdBy: 'user-1',
        createdAt: new Date(),
        metadata: null,
        creator: { id: 'user-1', name: 'John Doe', image: null }
      },
      {
        id: 'version-1',
        resumeId: 'resume-1',
        versionNumber: 1,
        versionName: 'Initial',
        changeSummary: 'Created resume',
        changeType: 'manual',
        createdBy: 'user-1',
        createdAt: new Date(),
        metadata: null,
        creator: { id: 'user-1', name: 'John Doe', image: null }
      }
    ]

    beforeEach(() => {
      mockPrisma.resume.findFirst.mockResolvedValue({ id: 'resume-1', userId: 'user-1' })
      mockPrisma.resumeVersion.findMany.mockResolvedValue(mockVersions)
    })

    it('should return versions for authorized user', async () => {
      const result = await service.getVersions('resume-1', 'user-1', 10)

      expect(mockPrisma.resume.findFirst).toHaveBeenCalledWith({
        where: {
          id: 'resume-1',
          userId: 'user-1'
        }
      })

      expect(mockPrisma.resumeVersion.findMany).toHaveBeenCalledWith({
        where: { resumeId: 'resume-1' },
        orderBy: { versionNumber: 'desc' },
        take: 10,
        include: {
          creator: {
            select: {
              id: true,
              name: true,
              image: true
            }
          }
        }
      })

      expect(result).toHaveLength(2)
      expect(result[0].versionNumber).toBe(2)
      expect(result[1].versionNumber).toBe(1)
    })

    it('should throw error for unauthorized access', async () => {
      mockPrisma.resume.findFirst.mockResolvedValue(null)

      await expect(service.getVersions('resume-1', 'unauthorized-user')).rejects.toThrow('Resume not found or access denied')
    })

    it('should use default limit when not specified', async () => {
      await service.getVersions('resume-1', 'user-1')

      expect(mockPrisma.resumeVersion.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          take: 50
        })
      )
    })
  })

  describe('compareVersions', () => {
    const mockVersions = [
      {
        id: 'version-1',
        contentSnapshot: JSON.stringify({ name: 'John' })
      },
      {
        id: 'version-2',
        contentSnapshot: JSON.stringify({ name: 'Jane', age: 30 })
      }
    ]

    beforeEach(() => {
      mockPrisma.resume.findFirst.mockResolvedValue({ id: 'resume-1', userId: 'user-1' })
      mockPrisma.versionComparison.findUnique.mockResolvedValue(null)
      mockPrisma.resumeVersion.findFirst
        .mockResolvedValueOnce(mockVersions[0])
        .mockResolvedValueOnce(mockVersions[1])
      mockPrisma.versionComparison.create.mockResolvedValue({})
    })

    it('should calculate and cache version comparison', async () => {
      const result = await service.compareVersions('resume-1', 1, 2, 'user-1')

      expect(result).toBeDefined()
      expect(result.operations).toBeDefined()
      expect(result.summary).toBeDefined()
      expect(result.metadata.fromVersion).toBe(1)
      expect(result.metadata.toVersion).toBe(2)

      // Should cache the result
      expect(mockPrisma.versionComparison.create).toHaveBeenCalledWith({
        data: {
          resumeId: 'resume-1',
          versionFrom: 1,
          versionTo: 2,
          diffData: expect.any(String)
        }
      })
    })

    it('should return cached comparison if available', async () => {
      const cachedDiff = {
        operations: [],
        summary: { additions: 0, deletions: 0, modifications: 0, moves: 0 },
        metadata: { fromVersion: 1, toVersion: 2, calculatedAt: Date.now(), complexity: 'low' }
      }
      mockPrisma.versionComparison.findUnique.mockResolvedValue({
        diffData: JSON.stringify(cachedDiff)
      })

      const result = await service.compareVersions('resume-1', 1, 2, 'user-1')

      expect(result).toEqual(cachedDiff)
      expect(mockPrisma.resumeVersion.findFirst).not.toHaveBeenCalled()
    })

    it('should throw error if versions not found', async () => {
      mockPrisma.resumeVersion.findFirst
        .mockResolvedValueOnce(null)
        .mockResolvedValueOnce(mockVersions[1])

      await expect(service.compareVersions('resume-1', 1, 2, 'user-1')).rejects.toThrow('One or both versions not found')
    })
  })

  describe('rollbackToVersion', () => {
    const mockResume = {
      id: 'resume-1',
      userId: 'user-1',
      personalInfo: JSON.stringify({ name: 'Current' }),
      sections: JSON.stringify({ experience: [] })
    }

    const mockTargetVersion = {
      id: 'version-1',
      resumeId: 'resume-1',
      versionNumber: 1,
      contentSnapshot: JSON.stringify({
        personalInfo: { name: 'Old' },
        sections: { experience: [{ company: 'Old Company' }] },
        template: 'classic',
        theme: 'green',
        title: 'Old Title'
      })
    }

    beforeEach(() => {
      mockPrisma.resume.findFirst.mockResolvedValue(mockResume)
      mockPrisma.resumeVersion.findUnique.mockResolvedValue(mockTargetVersion)
      mockPrisma.$transaction.mockImplementation(async (callback) => {
        return await callback(mockPrisma)
      })
    })

    it('should rollback to target version successfully', async () => {
      const result = await service.rollbackToVersion('resume-1', 'version-1', 'user-1')

      expect(result).toBe(true)
      expect(mockPrisma.$transaction).toHaveBeenCalled()
    })

    it('should create backup before rollback when requested', async () => {
      const createBackupSpy = vi.spyOn(service, 'createBackup').mockResolvedValue({} as any)

      await service.rollbackToVersion('resume-1', 'version-1', 'user-1', {
        createBackup: true,
        backupName: 'Pre-rollback backup'
      })

      expect(createBackupSpy).toHaveBeenCalledWith({
        resumeId: 'resume-1',
        userId: 'user-1',
        backupName: 'Pre-rollback backup',
        backupType: 'auto'
      })
    })

    it('should throw error for non-existent resume', async () => {
      mockPrisma.resume.findFirst.mockResolvedValue(null)

      await expect(service.rollbackToVersion('resume-1', 'version-1', 'user-1')).rejects.toThrow('Resume or version not found')
    })

    it('should throw error for non-existent version', async () => {
      mockPrisma.resumeVersion.findUnique.mockResolvedValue(null)

      await expect(service.rollbackToVersion('resume-1', 'version-1', 'user-1')).rejects.toThrow('Resume or version not found')
    })

    it('should handle rollback failure gracefully', async () => {
      mockPrisma.$transaction.mockRejectedValue(new Error('Database error'))

      const result = await service.rollbackToVersion('resume-1', 'version-1', 'user-1')

      expect(result).toBe(false)
    })
  })

  describe('createBackup', () => {
    const mockResume = {
      id: 'resume-1',
      userId: 'user-1',
      personalInfo: JSON.stringify({ name: 'John' }),
      sections: JSON.stringify({ experience: [] }),
      template: 'modern',
      theme: 'blue',
      title: 'Test Resume',
      createdAt: new Date(),
      updatedAt: new Date()
    }

    const mockCreatedBackup = {
      id: 'backup-1',
      resumeId: 'resume-1',
      backupName: 'Test Backup',
      backupData: JSON.stringify({}),
      backupType: 'manual',
      createdBy: 'user-1',
      createdAt: new Date(),
      expiresAt: null,
      metadata: null,
      creator: {
        id: 'user-1',
        name: 'John Doe',
        image: null
      }
    }

    beforeEach(() => {
      mockPrisma.resume.findFirst.mockResolvedValue(mockResume)
      mockPrisma.resumeBackup.create.mockResolvedValue(mockCreatedBackup)
    })

    it('should create backup successfully', async () => {
      const request = {
        resumeId: 'resume-1',
        userId: 'user-1',
        backupName: 'Test Backup',
        backupType: 'manual' as const
      }

      const result = await service.createBackup(request)

      expect(mockPrisma.resumeBackup.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          resumeId: 'resume-1',
          backupName: 'Test Backup',
          backupType: 'manual',
          createdBy: 'user-1'
        }),
        include: {
          creator: {
            select: {
              id: true,
              name: true,
              image: true
            }
          }
        }
      })

      expect(result).toEqual({
        id: 'backup-1',
        resumeId: 'resume-1',
        backupName: 'Test Backup',
        backupType: 'manual',
        createdBy: 'user-1',
        createdAt: mockCreatedBackup.createdAt,
        expiresAt: null,
        metadata: null,
        creator: mockCreatedBackup.creator
      })
    })

    it('should include expiration date when provided', async () => {
      const expiresAt = new Date('2024-12-31')
      const request = {
        resumeId: 'resume-1',
        userId: 'user-1',
        backupType: 'scheduled' as const,
        expiresAt
      }

      await service.createBackup(request)

      expect(mockPrisma.resumeBackup.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          expiresAt
        }),
        include: expect.any(Object)
      })
    })

    it('should throw error for non-existent resume', async () => {
      mockPrisma.resume.findFirst.mockResolvedValue(null)

      const request = {
        resumeId: 'non-existent',
        userId: 'user-1',
        backupType: 'manual' as const
      }

      await expect(service.createBackup(request)).rejects.toThrow('Resume not found or access denied')
    })
  })

  describe('cleanupOldVersions', () => {
    it('should delete old versions beyond keep count', async () => {
      const versionsToDelete = [
        { id: 'version-old-1' },
        { id: 'version-old-2' }
      ]
      mockPrisma.resumeVersion.findMany.mockResolvedValue(versionsToDelete)
      mockPrisma.resumeVersion.deleteMany.mockResolvedValue({ count: 2 })

      const result = await service.cleanupOldVersions('resume-1', 10)

      expect(mockPrisma.resumeVersion.findMany).toHaveBeenCalledWith({
        where: { resumeId: 'resume-1' },
        orderBy: { versionNumber: 'desc' },
        skip: 10,
        select: { id: true }
      })

      expect(mockPrisma.resumeVersion.deleteMany).toHaveBeenCalledWith({
        where: {
          id: {
            in: ['version-old-1', 'version-old-2']
          }
        }
      })

      expect(result).toBe(2)
    })

    it('should return 0 when no versions to delete', async () => {
      mockPrisma.resumeVersion.findMany.mockResolvedValue([])

      const result = await service.cleanupOldVersions('resume-1', 10)

      expect(result).toBe(0)
      expect(mockPrisma.resumeVersion.deleteMany).not.toHaveBeenCalled()
    })
  })

  describe('cleanupExpiredBackups', () => {
    it('should delete expired backups', async () => {
      mockPrisma.resumeBackup.deleteMany.mockResolvedValue({ count: 3 })

      const result = await service.cleanupExpiredBackups()

      expect(mockPrisma.resumeBackup.deleteMany).toHaveBeenCalledWith({
        where: {
          expiresAt: {
            lt: expect.any(Date)
          }
        }
      })

      expect(result).toBe(3)
    })
  })
})
