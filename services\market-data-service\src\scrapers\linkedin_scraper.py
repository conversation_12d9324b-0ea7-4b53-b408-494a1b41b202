"""
LinkedIn Job Scraper
Implements job scraping from LinkedIn Jobs
Part of FR-5.2: Job Market Data Ingestion Service
"""

import asyncio
import logging
from typing import List, Optional
from urllib.parse import urlencode, quote
import re
from datetime import datetime

from .base_scraper import BaseScraper, JobPosting

class LinkedInScraper(BaseScraper):
    """LinkedIn job scraper using public job search"""
    
    def __init__(self):
        super().__init__(
            source_name="linkedin",
            base_url="https://www.linkedin.com",
            rate_limit=2.0  # Be respectful to LinkedIn
        )
        
        # LinkedIn-specific headers
        self.session.headers.update({
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.9',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache',
            'Sec-Ch-Ua': '"Google Chrome";v="119", "Chromium";v="119", "Not?A_Brand";v="24"',
            'Sec-Ch-Ua-Mobile': '?0',
            'Sec-Ch-Ua-Platform': '"Windows"',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Upgrade-Insecure-Requests': '1'
        })

    def build_search_url(self, search_term: str, location: str, page: int = 1) -> str:
        """Build LinkedIn job search URL"""
        params = {
            'keywords': search_term,
            'location': location,
            'start': (page - 1) * 25,  # LinkedIn shows 25 jobs per page
            'f_TPR': 'r604800',  # Jobs posted in last week
            'f_JT': 'F',  # Full-time jobs
            'sortBy': 'DD'  # Sort by date posted
        }
        
        query_string = urlencode(params, quote_via=quote)
        return f"{self.base_url}/jobs/search?{query_string}"

    async def scrape_jobs(self, search_terms: List[str], locations: List[str], max_pages: int = 5) -> List[JobPosting]:
        """Scrape jobs from LinkedIn"""
        all_jobs = []
        
        for search_term in search_terms:
            for location in locations:
                self.logger.info(f"Scraping LinkedIn for '{search_term}' in '{location}'")
                
                jobs = await self._scrape_search_results(search_term, location, max_pages)
                all_jobs.extend(jobs)
                
                # Add delay between different search terms
                await asyncio.sleep(3)
        
        # Process and validate jobs
        return await self.process_job_batch(all_jobs)

    async def _scrape_search_results(self, search_term: str, location: str, max_pages: int) -> List[JobPosting]:
        """Scrape search results for a specific term and location"""
        jobs = []
        
        for page in range(1, max_pages + 1):
            try:
                url = self.build_search_url(search_term, location, page)
                self.logger.debug(f"Scraping page {page}: {url}")
                
                soup = await self.scrape_with_retry(url)
                if not soup:
                    self.logger.warning(f"Failed to get page {page} for {search_term} in {location}")
                    continue
                
                page_jobs = self._parse_job_listings(soup, location)
                if not page_jobs:
                    self.logger.info(f"No more jobs found on page {page}, stopping")
                    break
                
                jobs.extend(page_jobs)
                self.logger.info(f"Found {len(page_jobs)} jobs on page {page}")
                
                # Check if we've reached the end
                if len(page_jobs) < 20:  # LinkedIn usually shows 25 per page
                    break
                    
            except Exception as e:
                self.logger.error(f"Error scraping page {page}: {e}")
                continue
        
        return jobs

    def _parse_job_listings(self, soup, location: str) -> List[JobPosting]:
        """Parse job listings from LinkedIn search results page"""
        jobs = []
        
        # LinkedIn job cards have specific selectors
        job_cards = soup.find_all('div', {'class': re.compile(r'job-search-card|jobs-search__results-list')})
        
        if not job_cards:
            # Try alternative selectors
            job_cards = soup.find_all('li', {'class': re.compile(r'result-card|job-result-card')})
        
        for card in job_cards:
            try:
                job = self._parse_job_card(card, location)
                if job:
                    jobs.append(job)
            except Exception as e:
                self.logger.debug(f"Error parsing job card: {e}")
                continue
        
        return jobs

    def _parse_job_card(self, card, location: str) -> Optional[JobPosting]:
        """Parse individual job card"""
        try:
            # Extract title
            title_elem = card.find('h3', {'class': re.compile(r'job-search-card__title|result-card__title')})
            if not title_elem:
                title_elem = card.find('a', {'class': re.compile(r'job-search-card__title-link')})
            
            if not title_elem:
                return None
            
            title = self.clean_text(title_elem.get_text())
            
            # Extract company
            company_elem = card.find('h4', {'class': re.compile(r'job-search-card__subtitle|result-card__subtitle')})
            if not company_elem:
                company_elem = card.find('a', {'class': re.compile(r'job-search-card__subtitle-link')})
            
            if not company_elem:
                return None
            
            company = self.clean_text(company_elem.get_text())
            
            # Extract location
            location_elem = card.find('span', {'class': re.compile(r'job-search-card__location|result-card__location')})
            job_location = location if not location_elem else self.clean_text(location_elem.get_text())
            
            # Extract job URL and ID
            link_elem = card.find('a', href=True)
            external_url = None
            external_id = None
            
            if link_elem:
                href = link_elem['href']
                if href.startswith('/'):
                    external_url = f"{self.base_url}{href}"
                else:
                    external_url = href
                
                # Extract job ID from URL
                job_id_match = re.search(r'/jobs/view/(\d+)', href)
                if job_id_match:
                    external_id = job_id_match.group(1)
            
            # Extract posted date
            date_elem = card.find('time', {'class': re.compile(r'job-search-card__listdate|result-card__listdate')})
            posted_date = None
            if date_elem:
                date_text = date_elem.get('datetime') or date_elem.get_text()
                posted_date = self.parse_date(date_text)
            
            # Extract salary if available
            salary_elem = card.find('span', {'class': re.compile(r'salary|compensation')})
            salary_min, salary_max = None, None
            if salary_elem:
                salary_text = self.clean_text(salary_elem.get_text())
                salary_min, salary_max = self.extract_salary_range(salary_text)
            
            # For LinkedIn, we need to fetch the full job description separately
            # For now, we'll use a placeholder and fetch details later if needed
            description = f"Job posting for {title} at {company} in {job_location}"
            
            # Extract basic job type from title/description
            job_type = self.extract_job_type(title)
            experience_level = self.extract_experience_level(title)
            
            return JobPosting(
                title=title,
                company=company,
                description=description,
                location=job_location,
                salary_min=salary_min,
                salary_max=salary_max,
                job_type=job_type,
                experience_level=experience_level,
                source=self.source_name,
                external_id=external_id,
                external_url=external_url,
                posted_date=posted_date
            )
            
        except Exception as e:
            self.logger.debug(f"Error parsing job card: {e}")
            return None

    async def fetch_job_details(self, job_url: str) -> Optional[dict]:
        """Fetch detailed job information from job page"""
        try:
            soup = await self.scrape_with_retry(job_url)
            if not soup:
                return None
            
            # Extract full job description
            description_elem = soup.find('div', {'class': re.compile(r'description|job-description')})
            description = ""
            if description_elem:
                description = self.clean_text(description_elem.get_text())
            
            # Extract requirements section
            requirements = ""
            req_elem = soup.find('div', {'class': re.compile(r'requirements|qualifications')})
            if req_elem:
                requirements = self.clean_text(req_elem.get_text())
            
            # Extract skills
            skills = self.extract_skills_from_text(f"{description} {requirements}")
            
            # Extract industry
            industry_elem = soup.find('span', {'class': re.compile(r'industry|company-industry')})
            industry = None
            if industry_elem:
                industry = self.clean_text(industry_elem.get_text())
            
            return {
                'description': description,
                'requirements': requirements,
                'skills': skills,
                'industry': industry
            }
            
        except Exception as e:
            self.logger.error(f"Error fetching job details from {job_url}: {e}")
            return None

    def _extract_linkedin_specific_data(self, soup) -> dict:
        """Extract LinkedIn-specific metadata"""
        data = {}
        
        # Extract company size
        company_size_elem = soup.find('span', {'class': re.compile(r'company-size|org-size')})
        if company_size_elem:
            data['company_size'] = self.clean_text(company_size_elem.get_text())
        
        # Extract seniority level
        seniority_elem = soup.find('span', {'class': re.compile(r'seniority|experience-level')})
        if seniority_elem:
            data['seniority_level'] = self.clean_text(seniority_elem.get_text())
        
        # Extract employment type
        employment_elem = soup.find('span', {'class': re.compile(r'employment-type|job-type')})
        if employment_elem:
            data['employment_type'] = self.clean_text(employment_elem.get_text())
        
        return data

    async def enrich_job_data(self, jobs: List[JobPosting]) -> List[JobPosting]:
        """Enrich job data by fetching detailed information"""
        enriched_jobs = []
        
        for i, job in enumerate(jobs):
            try:
                if job.external_url and i < 10:  # Limit detailed fetching to first 10 jobs
                    details = await self.fetch_job_details(job.external_url)
                    if details:
                        job.description = details.get('description', job.description)
                        job.requirements = details.get('requirements')
                        job.skills = details.get('skills', job.skills)
                        job.industry = details.get('industry')
                
                enriched_jobs.append(job)
                
                # Rate limiting for detail fetching
                if job.external_url:
                    await asyncio.sleep(1)
                    
            except Exception as e:
                self.logger.error(f"Error enriching job data: {e}")
                enriched_jobs.append(job)  # Add original job even if enrichment fails
        
        return enriched_jobs
