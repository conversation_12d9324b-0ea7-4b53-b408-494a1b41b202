/**
 * LinkedIn Service
 * 
 * Database operations and business logic for LinkedIn integration
 */

import { prisma } from '@/lib/db'
import { linkedInClient, type LinkedInProfile, type ResumeData } from './client'
import { z } from 'zod'

// LinkedIn Import Request Schema
export const LinkedInImportRequestSchema = z.object({
  resumeId: z.string().optional(),
  sections: z.array(z.enum(['personalInfo', 'experience', 'education', 'skills'])).optional()
})

export type LinkedInImportRequest = z.infer<typeof LinkedInImportRequestSchema>

// LinkedIn Import Result Schema
export const LinkedInImportResultSchema = z.object({
  success: z.boolean(),
  importId: z.string(),
  importedData: z.any(),
  errors: z.array(z.string()).optional()
})

export type LinkedInImportResult = z.infer<typeof LinkedInImportResultSchema>

export class LinkedInService {
  /**
   * Save LinkedIn profile to database
   */
  async saveLinkedInProfile(
    userId: string,
    linkedinId: string,
    profileData: LinkedInProfile
  ): Promise<void> {
    await prisma.linkedInProfile.upsert({
      where: { userId },
      update: {
        linkedinId,
        profileData: JSON.stringify(profileData),
        lastSynced: new Date(),
        updatedAt: new Date()
      },
      create: {
        userId,
        linkedinId,
        profileData: JSON.stringify(profileData),
        lastSynced: new Date()
      }
    })
  }

  /**
   * Get LinkedIn profile from database
   */
  async getLinkedInProfile(userId: string): Promise<LinkedInProfile | null> {
    const profile = await prisma.linkedInProfile.findUnique({
      where: { userId }
    })

    if (!profile) return null

    try {
      return JSON.parse(profile.profileData) as LinkedInProfile
    } catch (error) {
      console.error('Failed to parse LinkedIn profile data:', error)
      return null
    }
  }

  /**
   * Check if user has connected LinkedIn
   */
  async hasLinkedInProfile(userId: string): Promise<boolean> {
    const profile = await prisma.linkedInProfile.findUnique({
      where: { userId },
      select: { id: true }
    })

    return !!profile
  }

  /**
   * Import LinkedIn data to resume
   */
  async importLinkedInData(
    userId: string,
    accessToken: string,
    request: LinkedInImportRequest
  ): Promise<LinkedInImportResult> {
    try {
      // Fetch LinkedIn profile data
      const profile = await linkedInClient.getProfile(accessToken)
      const positions = await linkedInClient.getPositions(accessToken)
      const educations = await linkedInClient.getEducations(accessToken)
      const skills = await linkedInClient.getSkills(accessToken)

      // Save LinkedIn profile to database
      await this.saveLinkedInProfile(userId, profile.id, profile)

      // Transform to resume data
      const resumeData = linkedInClient.transformToResumeData(
        profile,
        positions,
        educations,
        skills
      )

      // Filter data based on requested sections
      const filteredData = this.filterImportData(resumeData, request.sections)

      // Create import record
      const linkedInProfile = await prisma.linkedInProfile.findUnique({
        where: { userId }
      })

      if (!linkedInProfile) {
        throw new Error('LinkedIn profile not found')
      }

      const importRecord = await prisma.linkedInImport.create({
        data: {
          userId,
          resumeId: request.resumeId,
          profileId: linkedInProfile.id,
          importedData: JSON.stringify(filteredData),
          importStatus: 'completed'
        }
      })

      return {
        success: true,
        importId: importRecord.id,
        importedData: filteredData
      }
    } catch (error) {
      console.error('LinkedIn import failed:', error)

      // Create failed import record
      try {
        const linkedInProfile = await prisma.linkedInProfile.findUnique({
          where: { userId }
        })

        if (linkedInProfile) {
          await prisma.linkedInImport.create({
            data: {
              userId,
              resumeId: request.resumeId,
              profileId: linkedInProfile.id,
              importedData: JSON.stringify({}),
              importStatus: 'failed'
            }
          })
        }
      } catch (dbError) {
        console.error('Failed to create import record:', dbError)
      }

      return {
        success: false,
        importId: '',
        importedData: {},
        errors: [error instanceof Error ? error.message : 'Unknown error']
      }
    }
  }

  /**
   * Filter import data based on selected sections
   */
  private filterImportData(
    data: ResumeData,
    sections?: string[]
  ): Partial<ResumeData> {
    if (!sections || sections.length === 0) {
      return data
    }

    const filtered: Partial<ResumeData> = {}

    if (sections.includes('personalInfo')) {
      filtered.personalInfo = data.personalInfo
    }
    if (sections.includes('experience')) {
      filtered.experience = data.experience
    }
    if (sections.includes('education')) {
      filtered.education = data.education
    }
    if (sections.includes('skills')) {
      filtered.skills = data.skills
    }

    return filtered
  }

  /**
   * Get import history for user
   */
  async getImportHistory(userId: string, limit = 10): Promise<any[]> {
    const imports = await prisma.linkedInImport.findMany({
      where: { userId },
      orderBy: { createdAt: 'desc' },
      take: limit,
      include: {
        resume: {
          select: {
            id: true,
            title: true
          }
        }
      }
    })

    return imports.map(imp => ({
      id: imp.id,
      resumeId: imp.resumeId,
      resumeTitle: imp.resume?.title,
      status: imp.importStatus,
      createdAt: imp.createdAt,
      importedSections: this.getImportedSections(imp.importedData)
    }))
  }

  /**
   * Get imported sections from import data
   */
  private getImportedSections(importedDataJson: string): string[] {
    try {
      const data = JSON.parse(importedDataJson)
      const sections: string[] = []

      if (data.personalInfo) sections.push('personalInfo')
      if (data.experience?.length > 0) sections.push('experience')
      if (data.education?.length > 0) sections.push('education')
      if (data.skills?.length > 0) sections.push('skills')

      return sections
    } catch {
      return []
    }
  }

  /**
   * Apply imported data to resume
   */
  async applyImportToResume(
    userId: string,
    resumeId: string,
    importId: string,
    sections: string[]
  ): Promise<boolean> {
    try {
      // Get import data
      const importRecord = await prisma.linkedInImport.findFirst({
        where: {
          id: importId,
          userId
        }
      })

      if (!importRecord) {
        throw new Error('Import record not found')
      }

      const importedData = JSON.parse(importRecord.importedData)

      // Get current resume
      const resume = await prisma.resume.findFirst({
        where: {
          id: resumeId,
          userId
        }
      })

      if (!resume) {
        throw new Error('Resume not found')
      }

      // Parse current resume data
      const currentPersonalInfo = resume.personalInfo ? JSON.parse(resume.personalInfo) : {}
      const currentSections = resume.sections ? JSON.parse(resume.sections) : {}

      // Apply imported data to selected sections
      const updatedPersonalInfo = sections.includes('personalInfo') && importedData.personalInfo
        ? { ...currentPersonalInfo, ...importedData.personalInfo }
        : currentPersonalInfo

      const updatedSections = { ...currentSections }

      if (sections.includes('experience') && importedData.experience) {
        updatedSections.experience = importedData.experience
      }
      if (sections.includes('education') && importedData.education) {
        updatedSections.education = importedData.education
      }
      if (sections.includes('skills') && importedData.skills) {
        updatedSections.skills = importedData.skills
      }

      // Update resume
      await prisma.resume.update({
        where: { id: resumeId },
        data: {
          personalInfo: JSON.stringify(updatedPersonalInfo),
          sections: JSON.stringify(updatedSections),
          updatedAt: new Date()
        }
      })

      return true
    } catch (error) {
      console.error('Failed to apply import to resume:', error)
      return false
    }
  }

  /**
   * Delete LinkedIn profile and all related data
   */
  async disconnectLinkedIn(userId: string): Promise<boolean> {
    try {
      await prisma.linkedInProfile.delete({
        where: { userId }
      })
      return true
    } catch (error) {
      console.error('Failed to disconnect LinkedIn:', error)
      return false
    }
  }

  /**
   * Get LinkedIn connection status
   */
  async getConnectionStatus(userId: string): Promise<{
    connected: boolean
    lastSynced?: Date
    profileData?: any
  }> {
    const profile = await prisma.linkedInProfile.findUnique({
      where: { userId }
    })

    if (!profile) {
      return { connected: false }
    }

    try {
      const profileData = JSON.parse(profile.profileData)
      return {
        connected: true,
        lastSynced: profile.lastSynced,
        profileData: {
          name: `${Object.values(profileData.firstName.localized)[0]} ${Object.values(profileData.lastName.localized)[0]}`,
          headline: profileData.headline,
          profileImage: profileData.profilePicture?.displayImage
        }
      }
    } catch {
      return {
        connected: true,
        lastSynced: profile.lastSynced
      }
    }
  }
}

export const linkedInService = new LinkedInService()
