/**
 * LinkedIn Features Validation Script
 * 
 * Validates LinkedIn integration implementation
 */

const fs = require('fs')
const path = require('path')

class LinkedInFeaturesValidator {
  constructor() {
    this.results = {
      files: { checked: 0, missing: 0, present: 0 },
      services: { checked: 0, missing: 0, present: 0 },
      apis: { checked: 0, missing: 0, present: 0 },
      components: { checked: 0, missing: 0, present: 0 },
      tests: { checked: 0, missing: 0, present: 0 },
      config: { checked: 0, missing: 0, present: 0 }
    }
  }

  log(message, type = 'info') {
    const colors = {
      info: '\x1b[36m',    // Cyan
      success: '\x1b[32m', // Green
      error: '\x1b[31m',   // Red
      warning: '\x1b[33m', // Yellow
      reset: '\x1b[0m'     // Reset
    }
    
    console.log(`${colors[type]}${message}${colors.reset}`)
  }

  checkFileExists(filePath, description) {
    const fullPath = path.join(__dirname, filePath)
    const exists = fs.existsSync(fullPath)
    
    if (exists) {
      this.log(`✅ ${description}: Found`, 'success')
      return true
    } else {
      this.log(`❌ ${description}: Missing (${filePath})`, 'error')
      return false
    }
  }

  checkFileContent(filePath, searchTerms, description) {
    const fullPath = path.join(__dirname, filePath)
    
    if (!fs.existsSync(fullPath)) {
      this.log(`❌ ${description}: File not found`, 'error')
      return false
    }

    try {
      const content = fs.readFileSync(fullPath, 'utf8')
      const missingTerms = searchTerms.filter(term => !content.includes(term))
      
      if (missingTerms.length === 0) {
        this.log(`✅ ${description}: All required content found`, 'success')
        return true
      } else {
        this.log(`⚠️  ${description}: Missing content: ${missingTerms.join(', ')}`, 'warning')
        return false
      }
    } catch (error) {
      this.log(`❌ ${description}: Error reading file - ${error.message}`, 'error')
      return false
    }
  }

  validateFileStructure() {
    this.log('\n📁 Validating LinkedIn File Structure...', 'info')
    
    const requiredFiles = [
      'src/lib/linkedin/client.ts',
      'src/lib/linkedin/service.ts',
      'src/app/api/linkedin/auth/route.ts',
      'src/app/api/linkedin/import/route.ts',
      'src/components/linkedin/LinkedInIntegration.tsx',
      'src/test/linkedin/linkedin-client.test.ts',
      'src/test/linkedin/linkedin-service.test.ts',
      'src/test/linkedin/linkedin-api.test.ts',
      'src/test/linkedin/linkedin-component.test.tsx'
    ]

    let present = 0
    let total = requiredFiles.length

    requiredFiles.forEach(file => {
      this.results.files.checked++
      if (this.checkFileExists(file, `File: ${file}`)) {
        present++
        this.results.files.present++
      } else {
        this.results.files.missing++
      }
    })

    this.log(`📊 File Structure: ${present}/${total} files present`, present === total ? 'success' : 'warning')
    return present === total
  }

  validateLinkedInServices() {
    this.log('\n🔗 Validating LinkedIn Services...', 'info')
    
    const serviceChecks = [
      {
        file: 'src/lib/linkedin/client.ts',
        description: 'LinkedIn Client Implementation',
        requiredContent: ['LinkedInClient', 'getAuthorizationUrl', 'getAccessToken', 'getProfile', 'transformToResumeData']
      },
      {
        file: 'src/lib/linkedin/service.ts',
        description: 'LinkedIn Service Implementation',
        requiredContent: ['LinkedInService', 'saveLinkedInProfile', 'importLinkedInData', 'applyImportToResume']
      }
    ]

    let passed = 0
    let total = serviceChecks.length

    serviceChecks.forEach(check => {
      this.results.services.checked++
      if (this.checkFileContent(check.file, check.requiredContent, check.description)) {
        passed++
        this.results.services.present++
      } else {
        this.results.services.missing++
      }
    })

    this.log(`📊 LinkedIn Services: ${passed}/${total} checks passed`, passed === total ? 'success' : 'warning')
    return passed === total
  }

  validateLinkedInAPIs() {
    this.log('\n🌐 Validating LinkedIn API Routes...', 'info')
    
    const apiChecks = [
      {
        file: 'src/app/api/linkedin/auth/route.ts',
        description: 'LinkedIn Auth API',
        requiredContent: ['GET', 'POST', 'DELETE', 'getAuthorizationUrl', 'getAccessToken']
      },
      {
        file: 'src/app/api/linkedin/import/route.ts',
        description: 'LinkedIn Import API',
        requiredContent: ['GET', 'POST', 'PUT', 'importLinkedInData', 'applyImportToResume']
      }
    ]

    let passed = 0
    let total = apiChecks.length

    apiChecks.forEach(check => {
      this.results.apis.checked++
      if (this.checkFileContent(check.file, check.requiredContent, check.description)) {
        passed++
        this.results.apis.present++
      } else {
        this.results.apis.missing++
      }
    })

    this.log(`📊 LinkedIn APIs: ${passed}/${total} checks passed`, passed === total ? 'success' : 'warning')
    return passed === total
  }

  validateLinkedInComponents() {
    this.log('\n⚛️  Validating LinkedIn Components...', 'info')
    
    const componentChecks = [
      {
        file: 'src/components/linkedin/LinkedInIntegration.tsx',
        description: 'LinkedIn Integration Component',
        requiredContent: ['LinkedInIntegration', 'connectLinkedIn', 'importData', 'disconnectLinkedIn']
      }
    ]

    let passed = 0
    let total = componentChecks.length

    componentChecks.forEach(check => {
      this.results.components.checked++
      if (this.checkFileContent(check.file, check.requiredContent, check.description)) {
        passed++
        this.results.components.present++
      } else {
        this.results.components.missing++
      }
    })

    this.log(`📊 LinkedIn Components: ${passed}/${total} checks passed`, passed === total ? 'success' : 'warning')
    return passed === total
  }

  validateLinkedInTests() {
    this.log('\n🧪 Validating LinkedIn Test Files...', 'info')
    
    const testChecks = [
      {
        file: 'src/test/linkedin/linkedin-client.test.ts',
        description: 'LinkedIn Client Tests',
        requiredContent: ['describe', 'it', 'expect', 'LinkedInClient', 'getAuthorizationUrl']
      },
      {
        file: 'src/test/linkedin/linkedin-service.test.ts',
        description: 'LinkedIn Service Tests',
        requiredContent: ['describe', 'it', 'expect', 'LinkedInService', 'saveLinkedInProfile']
      },
      {
        file: 'src/test/linkedin/linkedin-api.test.ts',
        description: 'LinkedIn API Tests',
        requiredContent: ['describe', 'it', 'expect', 'NextRequest', 'linkedin/auth']
      },
      {
        file: 'src/test/linkedin/linkedin-component.test.tsx',
        description: 'LinkedIn Component Tests',
        requiredContent: ['describe', 'it', 'expect', 'render', 'LinkedInIntegration']
      }
    ]

    let passed = 0
    let total = testChecks.length

    testChecks.forEach(check => {
      this.results.tests.checked++
      if (this.checkFileContent(check.file, check.requiredContent, check.description)) {
        passed++
        this.results.tests.present++
      } else {
        this.results.tests.missing++
      }
    })

    this.log(`📊 LinkedIn Tests: ${passed}/${total} checks passed`, passed === total ? 'success' : 'warning')
    return passed === total
  }

  validateConfiguration() {
    this.log('\n⚙️  Validating LinkedIn Configuration...', 'info')
    
    const configChecks = [
      {
        file: 'package.json',
        description: 'Package.json LinkedIn Scripts',
        requiredContent: ['test:linkedin', 'test:linkedin:client', 'test:linkedin:service']
      },
      {
        file: '.env.example',
        description: 'Environment Variables',
        requiredContent: ['LINKEDIN_CLIENT_ID', 'LINKEDIN_CLIENT_SECRET', 'LINKEDIN_REDIRECT_URI']
      },
      {
        file: 'packages/database/prisma/schema.prisma',
        description: 'Database Schema',
        requiredContent: ['LinkedInProfile', 'LinkedInImport', 'CollaborationSession']
      }
    ]

    let passed = 0
    let total = configChecks.length

    configChecks.forEach(check => {
      this.results.config.checked++
      if (this.checkFileContent(check.file, check.requiredContent, check.description)) {
        passed++
        this.results.config.present++
      } else {
        this.results.config.missing++
      }
    })

    this.log(`📊 LinkedIn Configuration: ${passed}/${total} checks passed`, passed === total ? 'success' : 'warning')
    return passed === total
  }

  generateReport() {
    this.log('\n📊 LinkedIn Features Validation Report', 'info')
    this.log('=' .repeat(60), 'info')
    
    const categories = ['files', 'services', 'apis', 'components', 'tests', 'config']
    let totalChecked = 0
    let totalPresent = 0
    let totalMissing = 0

    categories.forEach(category => {
      const result = this.results[category]
      totalChecked += result.checked
      totalPresent += result.present
      totalMissing += result.missing

      const status = result.missing === 0 ? '✅' : '⚠️ '
      const categoryName = category.charAt(0).toUpperCase() + category.slice(1)
      
      this.log(
        `${status} ${categoryName}: ${result.present}/${result.checked} present`,
        result.missing === 0 ? 'success' : 'warning'
      )
    })

    this.log('=' .repeat(60), 'info')
    this.log(`📈 Overall: ${totalPresent}/${totalChecked} checks passed`, 
             totalMissing === 0 ? 'success' : 'warning')
    
    const completionRate = totalChecked > 0 ? (totalPresent / totalChecked * 100).toFixed(1) : 0
    this.log(`📊 Completion Rate: ${completionRate}%`, 
             completionRate >= 90 ? 'success' : completionRate >= 70 ? 'warning' : 'error')

    // Recommendations
    this.log('\n💡 LinkedIn Integration Status:', 'info')
    if (totalMissing === 0) {
      this.log('🎉 All LinkedIn features are properly implemented!', 'success')
      this.log('✨ Ready for testing and integration', 'success')
    } else {
      this.log(`🔧 ${totalMissing} items need attention`, 'warning')
      if (this.results.files.missing > 0) {
        this.log('📁 Create missing files and implement required functionality', 'warning')
      }
      if (this.results.tests.missing > 0) {
        this.log('🧪 Add comprehensive tests for LinkedIn features', 'warning')
      }
      if (this.results.config.missing > 0) {
        this.log('⚙️  Update configuration files for LinkedIn integration', 'warning')
      }
    }

    return totalMissing === 0
  }

  async validate() {
    this.log('🚀 Starting LinkedIn Features Validation...', 'info')
    this.log('📅 ' + new Date().toISOString(), 'info')
    
    const results = [
      this.validateFileStructure(),
      this.validateLinkedInServices(),
      this.validateLinkedInAPIs(),
      this.validateLinkedInComponents(),
      this.validateLinkedInTests(),
      this.validateConfiguration()
    ]

    const allValid = this.generateReport()
    
    this.log('\n🏁 LinkedIn Validation Complete!', 'info')
    return allValid
  }
}

// Run validation if this script is executed directly
if (require.main === module) {
  const validator = new LinkedInFeaturesValidator()
  validator.validate()
    .then(success => {
      process.exit(success ? 0 : 1)
    })
    .catch(error => {
      console.error('Validation failed:', error)
      process.exit(1)
    })
}

module.exports = LinkedInFeaturesValidator
