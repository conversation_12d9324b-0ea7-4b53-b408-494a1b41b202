/**
 * Version Control API Unit Tests
 * 
 * Tests for version control API endpoints
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { NextRequest } from 'next/server'
import { POST, GET, DELETE } from '@/app/api/version-control/versions/route'
import { POST as BackupPOST, GET as BackupGET } from '@/app/api/version-control/backups/route'

// Mock next-auth
const mockSession = {
  user: {
    id: 'user-1',
    name: '<PERSON>',
    email: '<EMAIL>'
  }
}

vi.mock('next-auth', () => ({
  getServerSession: vi.fn()
}))

// Mock version control service
const mockVersionControlService = {
  createVersion: vi.fn(),
  getVersions: vi.fn(),
  getVersion: vi.fn(),
  compareVersions: vi.fn(),
  rollbackToVersion: vi.fn(),
  previewRollback: vi.fn(),
  getVersionActivities: vi.fn(),
  cleanupOldVersions: vi.fn(),
  createBackup: vi.fn(),
  getBackups: vi.fn(),
  restoreFromBackup: vi.fn(),
  cleanupExpiredBackups: vi.fn()
}

vi.mock('@/lib/version-control/service', () => ({
  versionControlService: mockVersionControlService
}))

describe('Version Control API', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    const { getServerSession } = require('next-auth')
    getServerSession.mockResolvedValue(mockSession)
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('Versions API', () => {
    describe('POST /api/version-control/versions', () => {
      it('should create a new version', async () => {
        const mockVersion = {
          id: 'version-1',
          resumeId: 'resume-1',
          versionNumber: 1,
          versionName: 'Test Version',
          changeType: 'manual',
          createdBy: 'user-1',
          createdAt: new Date(),
          creator: { id: 'user-1', name: 'John Doe', image: null }
        }

        mockVersionControlService.createVersion.mockResolvedValue(mockVersion)

        const request = new NextRequest('http://localhost/api/version-control/versions', {
          method: 'POST',
          body: JSON.stringify({
            action: 'create',
            resumeId: 'resume-1',
            versionName: 'Test Version',
            changeType: 'manual',
            changeSummary: 'Test change'
          })
        })

        const response = await POST(request)
        const data = await response.json()

        expect(response.status).toBe(200)
        expect(data.success).toBe(true)
        expect(data.version).toEqual(mockVersion)
        expect(mockVersionControlService.createVersion).toHaveBeenCalledWith({
          resumeId: 'resume-1',
          userId: 'user-1',
          versionName: 'Test Version',
          changeType: 'manual',
          changeSummary: 'Test change',
          metadata: undefined
        })
      })

      it('should handle rollback action', async () => {
        mockVersionControlService.rollbackToVersion.mockResolvedValue(true)

        const request = new NextRequest('http://localhost/api/version-control/versions', {
          method: 'POST',
          body: JSON.stringify({
            action: 'rollback',
            resumeId: 'resume-1',
            versionId: 'version-1',
            options: { createBackup: true }
          })
        })

        const response = await POST(request)
        const data = await response.json()

        expect(response.status).toBe(200)
        expect(data.success).toBe(true)
        expect(mockVersionControlService.rollbackToVersion).toHaveBeenCalledWith(
          'resume-1',
          'version-1',
          'user-1',
          { createBackup: true }
        )
      })

      it('should handle preview rollback action', async () => {
        const mockDiff = {
          operations: [],
          summary: { additions: 0, deletions: 0, modifications: 1, moves: 0 },
          metadata: { fromVersion: 2, toVersion: 1, complexity: 'low' }
        }

        mockVersionControlService.previewRollback.mockResolvedValue(mockDiff)

        const request = new NextRequest('http://localhost/api/version-control/versions', {
          method: 'POST',
          body: JSON.stringify({
            action: 'preview-rollback',
            resumeId: 'resume-1',
            versionId: 'version-1'
          })
        })

        const response = await POST(request)
        const data = await response.json()

        expect(response.status).toBe(200)
        expect(data.success).toBe(true)
        expect(data.diff).toEqual(mockDiff)
      })

      it('should handle compare action', async () => {
        const mockDiff = {
          operations: [
            {
              type: 'modify',
              path: ['name'],
              oldValue: 'John',
              newValue: 'Jane'
            }
          ],
          summary: { additions: 0, deletions: 0, modifications: 1, moves: 0 },
          metadata: { fromVersion: 1, toVersion: 2, complexity: 'low' }
        }

        mockVersionControlService.compareVersions.mockResolvedValue(mockDiff)

        const request = new NextRequest('http://localhost/api/version-control/versions', {
          method: 'POST',
          body: JSON.stringify({
            action: 'compare',
            resumeId: 'resume-1',
            fromVersion: 1,
            toVersion: 2
          })
        })

        const response = await POST(request)
        const data = await response.json()

        expect(response.status).toBe(200)
        expect(data.success).toBe(true)
        expect(data.diff).toEqual(mockDiff)
      })

      it('should return 401 for unauthenticated requests', async () => {
        const { getServerSession } = require('next-auth')
        getServerSession.mockResolvedValue(null)

        const request = new NextRequest('http://localhost/api/version-control/versions', {
          method: 'POST',
          body: JSON.stringify({ action: 'create' })
        })

        const response = await POST(request)

        expect(response.status).toBe(401)
      })

      it('should return 400 for invalid action', async () => {
        const request = new NextRequest('http://localhost/api/version-control/versions', {
          method: 'POST',
          body: JSON.stringify({ action: 'invalid' })
        })

        const response = await POST(request)

        expect(response.status).toBe(400)
      })

      it('should handle rollback failure', async () => {
        mockVersionControlService.rollbackToVersion.mockResolvedValue(false)

        const request = new NextRequest('http://localhost/api/version-control/versions', {
          method: 'POST',
          body: JSON.stringify({
            action: 'rollback',
            resumeId: 'resume-1',
            versionId: 'version-1'
          })
        })

        const response = await POST(request)

        expect(response.status).toBe(500)
      })
    })

    describe('GET /api/version-control/versions', () => {
      it('should list versions', async () => {
        const mockVersions = [
          {
            id: 'version-2',
            resumeId: 'resume-1',
            versionNumber: 2,
            versionName: 'Latest',
            changeType: 'auto',
            createdBy: 'user-1',
            createdAt: new Date(),
            creator: { id: 'user-1', name: 'John Doe', image: null }
          },
          {
            id: 'version-1',
            resumeId: 'resume-1',
            versionNumber: 1,
            versionName: 'Initial',
            changeType: 'manual',
            createdBy: 'user-1',
            createdAt: new Date(),
            creator: { id: 'user-1', name: 'John Doe', image: null }
          }
        ]

        mockVersionControlService.getVersions.mockResolvedValue(mockVersions)

        const request = new NextRequest('http://localhost/api/version-control/versions?action=list&resumeId=resume-1&limit=10')

        const response = await GET(request)
        const data = await response.json()

        expect(response.status).toBe(200)
        expect(data.versions).toEqual(mockVersions)
        expect(mockVersionControlService.getVersions).toHaveBeenCalledWith(
          'resume-1',
          'user-1',
          10
        )
      })

      it('should get specific version', async () => {
        const mockVersion = {
          id: 'version-1',
          resumeId: 'resume-1',
          versionNumber: 1,
          contentSnapshot: { name: 'John' },
          creator: { id: 'user-1', name: 'John Doe', image: null }
        }

        mockVersionControlService.getVersion.mockResolvedValue(mockVersion)

        const request = new NextRequest('http://localhost/api/version-control/versions?action=get&versionId=version-1')

        const response = await GET(request)
        const data = await response.json()

        expect(response.status).toBe(200)
        expect(data.version).toEqual(mockVersion)
      })

      it('should get version activities', async () => {
        const mockActivities = [
          {
            id: 'activity-1',
            activityType: 'created',
            userId: 'user-1',
            createdAt: new Date(),
            user: { id: 'user-1', name: 'John Doe', image: null }
          }
        ]

        mockVersionControlService.getVersionActivities.mockResolvedValue(mockActivities)

        const request = new NextRequest('http://localhost/api/version-control/versions?action=activities&resumeId=resume-1')

        const response = await GET(request)
        const data = await response.json()

        expect(response.status).toBe(200)
        expect(data.activities).toEqual(mockActivities)
      })

      it('should return 400 for missing resume ID', async () => {
        const request = new NextRequest('http://localhost/api/version-control/versions?action=list')

        const response = await GET(request)

        expect(response.status).toBe(400)
      })

      it('should return 404 for non-existent version', async () => {
        mockVersionControlService.getVersion.mockResolvedValue(null)

        const request = new NextRequest('http://localhost/api/version-control/versions?action=get&versionId=non-existent')

        const response = await GET(request)

        expect(response.status).toBe(404)
      })
    })

    describe('DELETE /api/version-control/versions', () => {
      it('should cleanup old versions', async () => {
        mockVersionControlService.cleanupOldVersions.mockResolvedValue(5)

        const request = new NextRequest('http://localhost/api/version-control/versions?action=cleanup&resumeId=resume-1&keepCount=10')

        const response = await DELETE(request)
        const data = await response.json()

        expect(response.status).toBe(200)
        expect(data.success).toBe(true)
        expect(data.deletedCount).toBe(5)
        expect(mockVersionControlService.cleanupOldVersions).toHaveBeenCalledWith(
          'resume-1',
          10
        )
      })

      it('should return 400 for invalid action', async () => {
        const request = new NextRequest('http://localhost/api/version-control/versions?action=invalid')

        const response = await DELETE(request)

        expect(response.status).toBe(400)
      })
    })
  })

  describe('Backups API', () => {
    describe('POST /api/version-control/backups', () => {
      it('should create a new backup', async () => {
        const mockBackup = {
          id: 'backup-1',
          resumeId: 'resume-1',
          backupName: 'Test Backup',
          backupType: 'manual',
          createdBy: 'user-1',
          createdAt: new Date(),
          creator: { id: 'user-1', name: 'John Doe', image: null }
        }

        mockVersionControlService.createBackup.mockResolvedValue(mockBackup)

        const request = new NextRequest('http://localhost/api/version-control/backups', {
          method: 'POST',
          body: JSON.stringify({
            action: 'create',
            resumeId: 'resume-1',
            backupName: 'Test Backup',
            backupType: 'manual'
          })
        })

        const response = await BackupPOST(request)
        const data = await response.json()

        expect(response.status).toBe(200)
        expect(data.success).toBe(true)
        expect(data.backup).toEqual(mockBackup)
      })

      it('should restore from backup', async () => {
        mockVersionControlService.restoreFromBackup.mockResolvedValue(true)

        const request = new NextRequest('http://localhost/api/version-control/backups', {
          method: 'POST',
          body: JSON.stringify({
            action: 'restore',
            backupId: 'backup-1'
          })
        })

        const response = await BackupPOST(request)
        const data = await response.json()

        expect(response.status).toBe(200)
        expect(data.success).toBe(true)
        expect(mockVersionControlService.restoreFromBackup).toHaveBeenCalledWith(
          'backup-1',
          'user-1'
        )
      })

      it('should handle restore failure', async () => {
        mockVersionControlService.restoreFromBackup.mockResolvedValue(false)

        const request = new NextRequest('http://localhost/api/version-control/backups', {
          method: 'POST',
          body: JSON.stringify({
            action: 'restore',
            backupId: 'backup-1'
          })
        })

        const response = await BackupPOST(request)

        expect(response.status).toBe(500)
      })
    })

    describe('GET /api/version-control/backups', () => {
      it('should list backups', async () => {
        const mockBackups = [
          {
            id: 'backup-1',
            resumeId: 'resume-1',
            backupName: 'Manual Backup',
            backupType: 'manual',
            createdBy: 'user-1',
            createdAt: new Date(),
            creator: { id: 'user-1', name: 'John Doe', image: null }
          }
        ]

        mockVersionControlService.getBackups.mockResolvedValue(mockBackups)

        const request = new NextRequest('http://localhost/api/version-control/backups?action=list&resumeId=resume-1')

        const response = await BackupGET(request)
        const data = await response.json()

        expect(response.status).toBe(200)
        expect(data.backups).toEqual(mockBackups)
        expect(mockVersionControlService.getBackups).toHaveBeenCalledWith(
          'resume-1',
          'user-1',
          20
        )
      })

      it('should return 400 for missing resume ID', async () => {
        const request = new NextRequest('http://localhost/api/version-control/backups?action=list')

        const response = await BackupGET(request)

        expect(response.status).toBe(400)
      })
    })
  })

  describe('Error Handling', () => {
    it('should handle validation errors', async () => {
      const request = new NextRequest('http://localhost/api/version-control/versions', {
        method: 'POST',
        body: JSON.stringify({
          action: 'create',
          // Missing required fields
        })
      })

      const response = await POST(request)

      expect(response.status).toBe(400)
    })

    it('should handle service errors', async () => {
      mockVersionControlService.createVersion.mockRejectedValue(new Error('Service error'))

      const request = new NextRequest('http://localhost/api/version-control/versions', {
        method: 'POST',
        body: JSON.stringify({
          action: 'create',
          resumeId: 'resume-1',
          changeType: 'manual'
        })
      })

      const response = await POST(request)

      expect(response.status).toBe(500)
    })

    it('should handle authentication errors', async () => {
      const { getServerSession } = require('next-auth')
      getServerSession.mockResolvedValue(null)

      const request = new NextRequest('http://localhost/api/version-control/versions', {
        method: 'POST',
        body: JSON.stringify({ action: 'create' })
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(401)
      expect(data.error).toBe('Unauthorized')
    })
  })
})
