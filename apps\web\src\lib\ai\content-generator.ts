/**
 * AI Content Generation Service
 * 
 * This service handles AI-powered content generation for resumes,
 * including professional summaries, job descriptions, and achievements.
 */

import {
  AIContentRequest,
  AIContentResponse,
  ContentSuggestion,
  ContentType,
  ContentContext,
  GenerationOptions,
  GenerationMetadata,
  ExperienceLevel,
  ContentTone,
  ContentLength,
} from '@careercraft/shared/types/ai';
import { nanoid } from 'nanoid';

export class ContentGenerator {
  private apiKey: string;
  private baseUrl: string;
  private model: string;

  constructor(config: {
    apiKey: string;
    baseUrl?: string;
    model?: string;
  }) {
    this.apiKey = config.apiKey;
    this.baseUrl = config.baseUrl || 'https://api.openai.com/v1';
    this.model = config.model || 'gpt-4';
  }

  async generateContent(request: AIContentRequest): Promise<AIContentResponse> {
    const startTime = Date.now();
    
    try {
      const prompt = this.buildPrompt(request.type, request.context, request.options);
      const response = await this.callAI(prompt, request.options);
      
      const suggestions = this.parseSuggestions(response, request.context);
      const metadata = this.buildMetadata(startTime, response, suggestions);

      return {
        id: nanoid(),
        requestId: nanoid(),
        suggestions,
        metadata,
        createdAt: new Date().toISOString(),
      };
    } catch (error) {
      console.error('Content generation error:', error);
      throw new Error('Failed to generate content');
    }
  }

  private buildPrompt(type: ContentType, context: ContentContext, options: GenerationOptions): string {
    const basePrompt = this.getBasePrompt(type);
    const contextPrompt = this.buildContextPrompt(context);
    const optionsPrompt = this.buildOptionsPrompt(options);
    const examplesPrompt = this.getExamplesPrompt(type, context.experienceLevel);

    return `${basePrompt}

${contextPrompt}

${optionsPrompt}

${examplesPrompt}

Please provide ${options.maxSuggestions} high-quality suggestions that are:
- ATS-optimized with relevant keywords
- Tailored to the ${context.experienceLevel} experience level
- Written in a ${context.tone || 'professional'} tone
- Formatted for ${options.length} length
- Industry-appropriate for ${context.industry || 'general'}

Return the response as a JSON array of suggestions, each with:
- content: the generated text
- keywords: array of relevant keywords used
- reasoning: brief explanation of the approach
- atsScore: estimated ATS compatibility score (0-100)`;
  }

  private getBasePrompt(type: ContentType): string {
    switch (type) {
      case ContentType.PROFESSIONAL_SUMMARY:
        return `You are an expert resume writer specializing in creating compelling professional summaries. 
        Create a professional summary that highlights the candidate's key strengths, experience, and value proposition.`;

      case ContentType.WORK_EXPERIENCE_DESCRIPTION:
        return `You are an expert resume writer specializing in work experience descriptions.
        Create a compelling job description that showcases responsibilities and impact.`;

      case ContentType.ACHIEVEMENT_BULLET:
        return `You are an expert resume writer specializing in achievement-focused bullet points.
        Create quantifiable achievement statements that demonstrate measurable impact and results.`;

      case ContentType.SKILLS_SUGGESTION:
        return `You are an expert career advisor specializing in skill recommendations.
        Suggest relevant skills based on the candidate's background and target role.`;

      case ContentType.COVER_LETTER:
        return `You are an expert cover letter writer.
        Create a compelling cover letter that connects the candidate's experience to the target role.`;

      case ContentType.LINKEDIN_HEADLINE:
        return `You are an expert LinkedIn profile optimizer.
        Create an attention-grabbing LinkedIn headline that showcases the candidate's value proposition.`;

      case ContentType.LINKEDIN_SUMMARY:
        return `You are an expert LinkedIn profile writer.
        Create an engaging LinkedIn summary that tells the candidate's professional story.`;

      default:
        return `You are an expert resume and career content writer.
        Create professional, compelling content that helps candidates stand out.`;
    }
  }

  private buildContextPrompt(context: ContentContext): string {
    let prompt = 'CANDIDATE CONTEXT:\n';

    if (context.firstName && context.lastName) {
      prompt += `Name: ${context.firstName} ${context.lastName}\n`;
    }

    if (context.currentRole) {
      prompt += `Current Role: ${context.currentRole}\n`;
    }

    if (context.industry) {
      prompt += `Industry: ${context.industry}\n`;
    }

    prompt += `Experience Level: ${context.experienceLevel}\n`;

    if (context.targetJobTitle) {
      prompt += `Target Role: ${context.targetJobTitle}\n`;
    }

    if (context.targetCompany) {
      prompt += `Target Company: ${context.targetCompany}\n`;
    }

    if (context.skills && context.skills.length > 0) {
      prompt += `Key Skills: ${context.skills.join(', ')}\n`;
    }

    if (context.technologies && context.technologies.length > 0) {
      prompt += `Technologies: ${context.technologies.join(', ')}\n`;
    }

    if (context.achievements && context.achievements.length > 0) {
      prompt += `Past Achievements:\n${context.achievements.map(a => `- ${a}`).join('\n')}\n`;
    }

    if (context.jobDescription) {
      prompt += `Target Job Description:\n${context.jobDescription}\n`;
    }

    if (context.existingContent) {
      prompt += `Existing Content to Improve:\n${context.existingContent}\n`;
    }

    if (context.customInstructions) {
      prompt += `Special Instructions: ${context.customInstructions}\n`;
    }

    return prompt;
  }

  private buildOptionsPrompt(options: GenerationOptions): string {
    let prompt = 'GENERATION REQUIREMENTS:\n';

    prompt += `Length: ${options.length}\n`;
    prompt += `Style: ${options.style}\n`;
    prompt += `ATS Optimized: ${options.atsOptimized ? 'Yes' : 'No'}\n`;
    prompt += `Include Keywords: ${options.includeKeywords ? 'Yes' : 'No'}\n`;
    prompt += `Industry Specific: ${options.industrySpecific ? 'Yes' : 'No'}\n`;
    prompt += `Creativity Level: ${Math.round(options.creativityLevel * 100)}%\n`;
    prompt += `Language: ${options.language}\n`;

    return prompt;
  }

  private getExamplesPrompt(type: ContentType, experienceLevel: ExperienceLevel): string {
    const examples = this.getExamples(type, experienceLevel);
    if (examples.length === 0) return '';

    return `EXAMPLES FOR REFERENCE:\n${examples.map((ex, i) => `${i + 1}. ${ex}`).join('\n\n')}`;
  }

  private getExamples(type: ContentType, experienceLevel: ExperienceLevel): string[] {
    // This would typically come from a database or configuration
    const exampleMap: Record<ContentType, Record<ExperienceLevel, string[]>> = {
      [ContentType.PROFESSIONAL_SUMMARY]: {
        [ExperienceLevel.ENTRY_LEVEL]: [
          'Recent computer science graduate with strong foundation in software development and passion for creating innovative solutions. Proficient in Java, Python, and web technologies with hands-on experience through internships and academic projects.',
        ],
        [ExperienceLevel.MID_LEVEL]: [
          'Results-driven software engineer with 5+ years of experience developing scalable web applications. Proven track record of improving system performance by 40% and leading cross-functional teams to deliver projects on time.',
        ],
        [ExperienceLevel.SENIOR_LEVEL]: [
          'Senior technology leader with 10+ years of experience architecting enterprise-scale solutions. Expert in cloud technologies and agile methodologies, with a history of building high-performing teams and driving digital transformation initiatives.',
        ],
        [ExperienceLevel.EXECUTIVE]: [
          'Visionary technology executive with 15+ years of experience leading digital transformation at Fortune 500 companies. Proven ability to scale engineering organizations, drive innovation, and deliver $100M+ revenue growth through strategic technology initiatives.',
        ],
        [ExperienceLevel.CAREER_CHANGE]: [
          'Accomplished marketing professional transitioning to UX design with strong analytical skills and user-centric mindset. Completed intensive UX bootcamp and applied design thinking principles to increase conversion rates by 25% in previous role.',
        ],
      },
      [ContentType.ACHIEVEMENT_BULLET]: {
        [ExperienceLevel.ENTRY_LEVEL]: [
          'Developed automated testing framework that reduced manual testing time by 60% and improved code quality',
          'Collaborated with senior developers to implement new features, contributing to 15% increase in user engagement',
        ],
        [ExperienceLevel.MID_LEVEL]: [
          'Led development of microservices architecture that improved system scalability by 300% and reduced downtime by 50%',
          'Mentored 3 junior developers and established code review processes that decreased bug reports by 40%',
        ],
        [ExperienceLevel.SENIOR_LEVEL]: [
          'Architected cloud migration strategy that reduced infrastructure costs by $2M annually while improving performance by 45%',
          'Built and led engineering team of 15 developers across 3 product lines, delivering 12 major releases on schedule',
        ],
        [ExperienceLevel.EXECUTIVE]: [
          'Spearheaded digital transformation initiative that generated $50M in new revenue and improved operational efficiency by 35%',
          'Established engineering culture and practices that reduced time-to-market by 40% and increased team productivity by 60%',
        ],
        [ExperienceLevel.CAREER_CHANGE]: [
          'Applied data analysis skills from finance background to optimize user flows, resulting in 20% increase in conversion rates',
          'Leveraged project management experience to coordinate cross-functional design projects and deliver 5 major redesigns',
        ],
      },
      // Add more examples for other content types...
    };

    return exampleMap[type]?.[experienceLevel] || [];
  }

  private async callAI(prompt: string, options: GenerationOptions): Promise<any> {
    // Mock AI response for development
    // In production, this would call the actual AI API
    const mockResponse = {
      choices: [{
        message: {
          content: JSON.stringify([
            {
              content: this.generateMockContent(options),
              keywords: ['leadership', 'innovation', 'results-driven', 'strategic'],
              reasoning: 'Focused on quantifiable achievements and industry-relevant keywords',
              atsScore: 85,
            },
            {
              content: this.generateMockContent(options),
              keywords: ['collaboration', 'efficiency', 'problem-solving', 'technical'],
              reasoning: 'Emphasized technical skills and collaborative approach',
              atsScore: 82,
            },
          ]),
        },
      }],
      usage: {
        total_tokens: 150,
      },
    };

    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    return mockResponse;
  }

  private generateMockContent(options: GenerationOptions): string {
    const templates = {
      [ContentLength.SHORT]: 'Experienced professional with proven track record of delivering results.',
      [ContentLength.MEDIUM]: 'Results-driven professional with 5+ years of experience in leading cross-functional teams and implementing innovative solutions that drive business growth.',
      [ContentLength.LONG]: 'Accomplished professional with extensive experience in strategic planning, team leadership, and process optimization. Proven ability to deliver measurable results through innovative problem-solving and collaborative approach. Strong background in technology and business development with track record of exceeding performance targets.',
    };

    return templates[options.length] || templates[ContentLength.MEDIUM];
  }

  private parseSuggestions(response: any, context: ContentContext): ContentSuggestion[] {
    try {
      const content = response.choices[0].message.content;
      const parsed = JSON.parse(content);
      
      return parsed.map((item: any) => ({
        id: nanoid(),
        content: item.content,
        confidence: 0.85, // Mock confidence score
        reasoning: item.reasoning,
        keywords: item.keywords || [],
        atsScore: item.atsScore || 80,
        improvements: [],
        alternatives: [],
      }));
    } catch (error) {
      console.error('Failed to parse AI response:', error);
      return [{
        id: nanoid(),
        content: 'Failed to generate content. Please try again.',
        confidence: 0,
        reasoning: 'Error in content generation',
        keywords: [],
        atsScore: 0,
        improvements: [],
        alternatives: [],
      }];
    }
  }

  private buildMetadata(startTime: number, response: any, suggestions: ContentSuggestion[]): GenerationMetadata {
    return {
      model: this.model,
      processingTime: Date.now() - startTime,
      tokensUsed: response.usage?.total_tokens || 0,
      confidence: suggestions.reduce((sum, s) => sum + s.confidence, 0) / suggestions.length,
      atsOptimization: {
        score: suggestions.reduce((sum, s) => sum + s.atsScore, 0) / suggestions.length,
        issues: [],
        recommendations: [],
        keywordMatch: 85,
        formatCompliance: 90,
        readabilityScore: 88,
      },
      keywordAnalysis: {
        extractedKeywords: [],
        missingKeywords: [],
        keywordDensity: {},
        industryKeywords: [],
        skillKeywords: [],
        actionVerbs: [],
        recommendations: [],
      },
    };
  }
}
