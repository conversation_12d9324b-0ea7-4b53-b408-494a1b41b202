#!/usr/bin/env node

/**
 * Firefox Add-on Packaging Script
 * 
 * Creates a Firefox Add-ons ready package
 */

const fs = require('fs')
const path = require('path')
const archiver = require('archiver')

console.log('🦊 Packaging Firefox Add-on...')

const sourceDir = path.join(__dirname, '../dist/firefox')
const outputDir = path.join(__dirname, '../packages')
const outputFile = path.join(outputDir, 'careercraft-autofill-firefox-v1.0.0.zip')

// Ensure output directory exists
if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir, { recursive: true })
}

// Create zip package
const output = fs.createWriteStream(outputFile)
const archive = archiver('zip', { zlib: { level: 9 } })

output.on('close', () => {
  const sizeKB = (archive.pointer() / 1024).toFixed(2)
  console.log(`✅ Firefox package created: ${sizeKB} KB`)
  console.log(`📁 Location: ${outputFile}`)
  
  // Validate package
  validateFirefoxPackage()
})

archive.on('error', (err) => {
  console.error('❌ Packaging failed:', err)
  process.exit(1)
})

archive.pipe(output)

// Add all files from dist/firefox
archive.directory(sourceDir, false)
archive.finalize()

function validateFirefoxPackage() {
  console.log('\n🔍 Validating Firefox package...')
  
  // Check required files
  const requiredFiles = [
    'manifest.json',
    'background/background.js',
    'content/content.js',
    'popup/popup.html',
    'popup/popup.js',
    'icons/icon-16.png',
    'icons/icon-48.png',
    'icons/icon-128.png'
  ]
  
  let allValid = true
  
  requiredFiles.forEach(file => {
    const filePath = path.join(sourceDir, file)
    if (fs.existsSync(filePath)) {
      console.log(`✅ ${file}`)
    } else {
      console.log(`❌ ${file} - MISSING`)
      allValid = false
    }
  })
  
  // Check manifest
  const manifestPath = path.join(sourceDir, 'manifest.json')
  if (fs.existsSync(manifestPath)) {
    const manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf8'))
    
    console.log('\n📋 Manifest Validation:')
    console.log(`✅ Version: ${manifest.version}`)
    console.log(`✅ Manifest Version: ${manifest.manifest_version}`)
    console.log(`✅ Name: ${manifest.name}`)
    console.log(`✅ Permissions: ${manifest.permissions?.length || 0}`)
    
    // Check Firefox-specific fields
    if (manifest.applications?.gecko?.id) {
      console.log(`✅ Add-on ID: ${manifest.applications.gecko.id}`)
    } else {
      console.log('⚠️  Warning: No add-on ID specified')
    }
  }
  
  if (allValid) {
    console.log('\n🎉 Firefox package validation successful!')
    console.log('\n📋 Next Steps:')
    console.log('1. Go to Firefox Add-ons Developer Hub')
    console.log('2. Click "Submit a New Add-on"')
    console.log('3. Upload the package file')
    console.log('4. Choose distribution channel')
    console.log('5. Fill in listing details')
    console.log('6. Submit for review')
  } else {
    console.log('\n❌ Package validation failed!')
    process.exit(1)
  }
}
