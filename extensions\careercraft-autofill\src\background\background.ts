/**
 * CareerCraft Browser Extension - Background Service Worker
 * 
 * Handles extension lifecycle, API communication, and coordination
 * between content scripts and popup interface.
 */

import browser from 'webextension-polyfill'
import { AuthManager } from '../lib/auth/auth-manager'
import { ApiClient } from '../lib/api/api-client'
import { StorageManager } from '../lib/storage/storage-manager'
import { AnalyticsManager } from '../lib/analytics/analytics-manager'
import { NotificationManager } from '../lib/notifications/notification-manager'

// Extension state management
interface ExtensionState {
  isAuthenticated: boolean
  userId?: string
  userProfile?: any
  settings: ExtensionSettings
  activeTab?: number
  formDetectionEnabled: boolean
}

interface ExtensionSettings {
  autoFillEnabled: boolean
  customizationEnabled: boolean
  trackingEnabled: boolean
  notificationsEnabled: boolean
  confidenceThreshold: number
  supportedSites: string[]
}

class BackgroundService {
  private state: ExtensionState
  private authManager: AuthManager
  private apiClient: ApiClient
  private storageManager: StorageManager
  private analyticsManager: AnalyticsManager
  private notificationManager: NotificationManager

  constructor() {
    this.state = {
      isAuthenticated: false,
      settings: this.getDefaultSettings(),
      formDetectionEnabled: true
    }

    this.authManager = new AuthManager()
    this.apiClient = new ApiClient()
    this.storageManager = new StorageManager()
    this.analyticsManager = new AnalyticsManager()
    this.notificationManager = new NotificationManager()

    this.initialize()
  }

  private getDefaultSettings(): ExtensionSettings {
    return {
      autoFillEnabled: true,
      customizationEnabled: true,
      trackingEnabled: true,
      notificationsEnabled: true,
      confidenceThreshold: 0.8,
      supportedSites: [
        'linkedin.com',
        'indeed.com',
        'glassdoor.com',
        'monster.com',
        'ziprecruiter.com',
        'careerbuilder.com',
        'dice.com',
        'workday.com',
        'greenhouse.io',
        'lever.co'
      ]
    }
  }

  private async initialize() {
    console.log('🚀 CareerCraft Extension: Initializing background service...')

    try {
      // Load stored state and settings
      await this.loadStoredState()

      // Set up event listeners
      this.setupEventListeners()

      // Initialize authentication
      await this.initializeAuth()

      // Set up periodic tasks
      this.setupPeriodicTasks()

      console.log('✅ CareerCraft Extension: Background service initialized')
    } catch (error) {
      console.error('❌ CareerCraft Extension: Initialization failed:', error)
      this.analyticsManager.trackError('background_init_failed', error)
    }
  }

  private async loadStoredState() {
    const stored = await this.storageManager.get(['settings', 'userProfile', 'authToken'])
    
    if (stored.settings) {
      this.state.settings = { ...this.state.settings, ...stored.settings }
    }

    if (stored.userProfile) {
      this.state.userProfile = stored.userProfile
    }

    if (stored.authToken) {
      this.state.isAuthenticated = true
      this.apiClient.setAuthToken(stored.authToken)
    }
  }

  private setupEventListeners() {
    // Extension installation/update
    browser.runtime.onInstalled.addListener(this.handleInstalled.bind(this))

    // Message handling from content scripts and popup
    browser.runtime.onMessage.addListener(this.handleMessage.bind(this))

    // Tab updates for form detection
    browser.tabs.onUpdated.addListener(this.handleTabUpdated.bind(this))

    // Tab activation for context switching
    browser.tabs.onActivated.addListener(this.handleTabActivated.bind(this))

    // Command shortcuts
    browser.commands.onCommand.addListener(this.handleCommand.bind(this))

    // Storage changes
    browser.storage.onChanged.addListener(this.handleStorageChanged.bind(this))
  }

  private async handleInstalled(details: browser.Runtime.OnInstalledDetailsType) {
    console.log('📦 Extension installed/updated:', details.reason)

    if (details.reason === 'install') {
      // First-time installation
      await this.handleFirstInstall()
    } else if (details.reason === 'update') {
      // Extension update
      await this.handleUpdate(details.previousVersion)
    }

    this.analyticsManager.trackEvent('extension_installed', {
      reason: details.reason,
      version: browser.runtime.getManifest().version
    })
  }

  private async handleFirstInstall() {
    // Set default settings
    await this.storageManager.set({ settings: this.state.settings })

    // Show welcome notification
    this.notificationManager.showWelcome()

    // Open onboarding page
    browser.tabs.create({
      url: 'https://careercraft.onlinejobsearchhelp.com/extension/welcome'
    })
  }

  private async handleUpdate(previousVersion?: string) {
    // Handle migration if needed
    if (previousVersion) {
      await this.migrateData(previousVersion)
    }

    // Show update notification
    this.notificationManager.showUpdate()
  }

  private async migrateData(previousVersion: string) {
    // Implement data migration logic for version updates
    console.log(`🔄 Migrating data from version ${previousVersion}`)
    
    // Example migration logic
    if (this.isVersionLessThan(previousVersion, '1.0.0')) {
      // Migrate to new storage format
      await this.migrateTo100()
    }
  }

  private isVersionLessThan(version1: string, version2: string): boolean {
    const v1Parts = version1.split('.').map(Number)
    const v2Parts = version2.split('.').map(Number)
    
    for (let i = 0; i < Math.max(v1Parts.length, v2Parts.length); i++) {
      const v1Part = v1Parts[i] || 0
      const v2Part = v2Parts[i] || 0
      
      if (v1Part < v2Part) return true
      if (v1Part > v2Part) return false
    }
    
    return false
  }

  private async migrateTo100() {
    // Example migration logic
    console.log('🔄 Migrating to version 1.0.0')
  }

  private async handleMessage(
    message: any,
    sender: browser.Runtime.MessageSender,
    sendResponse: (response?: any) => void
  ) {
    try {
      const { type, data } = message

      switch (type) {
        case 'GET_STATE':
          return this.getExtensionState()

        case 'AUTHENTICATE':
          return await this.authenticate(data)

        case 'GET_USER_PROFILE':
          return await this.getUserProfile()

        case 'FORM_DETECTED':
          return await this.handleFormDetected(data, sender.tab?.id)

        case 'REQUEST_AUTOFILL':
          return await this.handleAutofillRequest(data, sender.tab?.id)

        case 'TRACK_APPLICATION':
          return await this.trackApplication(data)

        case 'UPDATE_SETTINGS':
          return await this.updateSettings(data)

        case 'GET_ANALYTICS':
          return await this.getAnalytics()

        default:
          console.warn('Unknown message type:', type)
          return { success: false, error: 'Unknown message type' }
      }
    } catch (error) {
      console.error('Error handling message:', error)
      this.analyticsManager.trackError('message_handling_failed', error)
      return { success: false, error: error.message }
    }
  }

  private async handleTabUpdated(
    tabId: number,
    changeInfo: browser.Tabs.OnUpdatedChangeInfoType,
    tab: browser.Tabs.Tab
  ) {
    if (changeInfo.status === 'complete' && tab.url) {
      // Check if this is a supported job site
      const isSupportedSite = this.isSupportedSite(tab.url)
      
      if (isSupportedSite && this.state.formDetectionEnabled) {
        // Inject content script if not already injected
        await this.ensureContentScriptInjected(tabId)
        
        // Notify content script to start form detection
        browser.tabs.sendMessage(tabId, {
          type: 'START_FORM_DETECTION',
          data: { url: tab.url }
        }).catch(() => {
          // Content script might not be ready yet
        })
      }
    }
  }

  private async handleTabActivated(activeInfo: browser.Tabs.OnActivatedActiveInfoType) {
    this.state.activeTab = activeInfo.tabId
    
    // Update popup state if open
    browser.runtime.sendMessage({
      type: 'TAB_CHANGED',
      data: { tabId: activeInfo.tabId }
    }).catch(() => {
      // Popup might not be open
    })
  }

  private async handleCommand(command: string) {
    switch (command) {
      case 'toggle-autofill':
        await this.toggleAutofill()
        break
      case 'quick-fill':
        await this.quickFill()
        break
    }
  }

  private async handleStorageChanged(
    changes: { [key: string]: browser.Storage.StorageChange },
    areaName: string
  ) {
    if (areaName === 'local') {
      if (changes.settings) {
        this.state.settings = changes.settings.newValue
      }
      if (changes.userProfile) {
        this.state.userProfile = changes.userProfile.newValue
      }
    }
  }

  private isSupportedSite(url: string): boolean {
    return this.state.settings.supportedSites.some(site => 
      url.includes(site)
    )
  }

  private async ensureContentScriptInjected(tabId: number) {
    try {
      await browser.scripting.executeScript({
        target: { tabId },
        files: ['content/content.js']
      })
    } catch (error) {
      // Content script might already be injected
    }
  }

  private async initializeAuth() {
    if (this.state.isAuthenticated) {
      try {
        // Verify token is still valid
        const profile = await this.apiClient.getUserProfile()
        this.state.userProfile = profile
        this.state.userId = profile.id
      } catch (error) {
        // Token expired or invalid
        this.state.isAuthenticated = false
        await this.storageManager.remove(['authToken'])
      }
    }
  }

  private setupPeriodicTasks() {
    // Refresh user profile every hour
    setInterval(async () => {
      if (this.state.isAuthenticated) {
        try {
          const profile = await this.apiClient.getUserProfile()
          this.state.userProfile = profile
          await this.storageManager.set({ userProfile: profile })
        } catch (error) {
          console.error('Failed to refresh user profile:', error)
        }
      }
    }, 60 * 60 * 1000) // 1 hour

    // Send analytics every 5 minutes
    setInterval(() => {
      this.analyticsManager.flush()
    }, 5 * 60 * 1000) // 5 minutes
  }

  // Message handlers
  private getExtensionState() {
    return {
      success: true,
      data: {
        isAuthenticated: this.state.isAuthenticated,
        userId: this.state.userId,
        settings: this.state.settings,
        formDetectionEnabled: this.state.formDetectionEnabled
      }
    }
  }

  private async authenticate(data: { token: string }) {
    try {
      this.apiClient.setAuthToken(data.token)
      const profile = await this.apiClient.getUserProfile()
      
      this.state.isAuthenticated = true
      this.state.userId = profile.id
      this.state.userProfile = profile
      
      await this.storageManager.set({
        authToken: data.token,
        userProfile: profile
      })

      this.analyticsManager.trackEvent('user_authenticated', {
        userId: profile.id
      })

      return { success: true, data: profile }
    } catch (error) {
      return { success: false, error: error.message }
    }
  }

  private async getUserProfile() {
    if (!this.state.isAuthenticated) {
      return { success: false, error: 'Not authenticated' }
    }

    return { success: true, data: this.state.userProfile }
  }

  private async handleFormDetected(data: any, tabId?: number) {
    this.analyticsManager.trackEvent('form_detected', {
      url: data.url,
      formType: data.formType,
      fieldCount: data.fieldCount
    })

    // Update popup if open
    browser.runtime.sendMessage({
      type: 'FORM_DETECTED',
      data: { ...data, tabId }
    }).catch(() => {
      // Popup might not be open
    })

    return { success: true }
  }

  private async handleAutofillRequest(data: any, tabId?: number) {
    if (!this.state.isAuthenticated) {
      return { success: false, error: 'Not authenticated' }
    }

    try {
      // Get autofill data from API
      const autofillData = await this.apiClient.getAutofillData({
        formFields: data.formFields,
        jobDescription: data.jobDescription,
        customization: this.state.settings.customizationEnabled
      })

      this.analyticsManager.trackEvent('autofill_requested', {
        fieldCount: data.formFields.length,
        customization: this.state.settings.customizationEnabled
      })

      return { success: true, data: autofillData }
    } catch (error) {
      this.analyticsManager.trackError('autofill_failed', error)
      return { success: false, error: error.message }
    }
  }

  private async trackApplication(data: any) {
    if (!this.state.isAuthenticated || !this.state.settings.trackingEnabled) {
      return { success: false, error: 'Tracking disabled or not authenticated' }
    }

    try {
      await this.apiClient.trackApplication(data)
      
      this.analyticsManager.trackEvent('application_submitted', {
        jobTitle: data.jobTitle,
        company: data.company,
        source: data.source
      })

      return { success: true }
    } catch (error) {
      this.analyticsManager.trackError('application_tracking_failed', error)
      return { success: false, error: error.message }
    }
  }

  private async updateSettings(newSettings: Partial<ExtensionSettings>) {
    this.state.settings = { ...this.state.settings, ...newSettings }
    await this.storageManager.set({ settings: this.state.settings })
    
    this.analyticsManager.trackEvent('settings_updated', {
      changes: Object.keys(newSettings)
    })

    return { success: true, data: this.state.settings }
  }

  private async getAnalytics() {
    return {
      success: true,
      data: await this.analyticsManager.getStats()
    }
  }

  private async toggleAutofill() {
    const newState = !this.state.settings.autoFillEnabled
    await this.updateSettings({ autoFillEnabled: newState })
    
    this.notificationManager.showToggleNotification(newState)
  }

  private async quickFill() {
    if (this.state.activeTab) {
      browser.tabs.sendMessage(this.state.activeTab, {
        type: 'QUICK_FILL'
      }).catch(() => {
        this.notificationManager.showError('No form detected on current page')
      })
    }
  }
}

// Initialize background service
new BackgroundService()

// Export for testing
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { BackgroundService }
}
