/**
 * AI API Routes Integration Tests
 * 
 * Tests for AI-powered API endpoints including:
 * - Content generation API
 * - ATS analysis API
 * - Resume optimization API
 * - Authentication and rate limiting
 * - Error handling and validation
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { NextRequest } from 'next/server'
import { getServerSession } from 'next-auth'
import { POST as generateContent } from '@/app/api/ai/generate/route'
import { POST as analyzeATS } from '@/app/api/ai/analyze/route'
import { POST as optimizeContent } from '@/app/api/ai/optimize/route'

// Mock dependencies
vi.mock('next-auth', () => ({
  getServerSession: vi.fn()
}))

vi.mock('@/lib/ai/openai', () => ({
  aiService: {
    generateContent: vi.fn(),
    analyzeATSCompatibility: vi.fn(),
    generateKeywordSuggestions: vi.fn(),
    optimizeResumeContent: vi.fn()
  }
}))

vi.mock('@/lib/auth', () => ({
  authOptions: {}
}))

// Mock logging function
vi.mock('@/lib/ai/usage-logger', () => ({
  logAIUsage: vi.fn()
}))

describe('AI API Routes', () => {
  const mockSession = {
    user: {
      id: 'test-user-id',
      email: '<EMAIL>',
      name: 'Test User'
    }
  }

  beforeEach(() => {
    vi.clearAllMocks()
    ;(getServerSession as any).mockResolvedValue(mockSession)
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('POST /api/ai/generate', () => {
    it('should generate content successfully', async () => {
      const { aiService } = await import('@/lib/ai/openai')
      ;(aiService.generateContent as any).mockResolvedValue(
        'Generated professional summary content'
      )

      const requestBody = {
        type: 'summary',
        context: {
          role: 'Software Engineer',
          industry: 'Technology',
          experience: '5 years'
        },
        tone: 'professional'
      }

      const request = new NextRequest('http://localhost:3000/api/ai/generate', {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: {
          'Content-Type': 'application/json'
        }
      })

      const response = await generateContent(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.content).toBe('Generated professional summary content')
      expect(data.type).toBe('summary')
      expect(data.tone).toBe('professional')
      expect(data.generatedAt).toBeDefined()
    })

    it('should validate request body', async () => {
      const invalidRequestBody = {
        type: 'invalid-type',
        context: {},
        tone: 'invalid-tone'
      }

      const request = new NextRequest('http://localhost:3000/api/ai/generate', {
        method: 'POST',
        body: JSON.stringify(invalidRequestBody),
        headers: {
          'Content-Type': 'application/json'
        }
      })

      const response = await generateContent(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.error).toContain('Invalid request data')
    })

    it('should require authentication', async () => {
      ;(getServerSession as any).mockResolvedValue(null)

      const requestBody = {
        type: 'summary',
        context: { role: 'Developer' },
        tone: 'professional'
      }

      const request = new NextRequest('http://localhost:3000/api/ai/generate', {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: {
          'Content-Type': 'application/json'
        }
      })

      const response = await generateContent(request)
      const data = await response.json()

      expect(response.status).toBe(401)
      expect(data.error).toBe('Unauthorized')
    })

    it('should handle AI service errors', async () => {
      const { aiService } = await import('@/lib/ai/openai')
      ;(aiService.generateContent as any).mockRejectedValue(
        new Error('AI service error')
      )

      const requestBody = {
        type: 'summary',
        context: { role: 'Developer' },
        tone: 'professional'
      }

      const request = new NextRequest('http://localhost:3000/api/ai/generate', {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: {
          'Content-Type': 'application/json'
        }
      })

      const response = await generateContent(request)
      const data = await response.json()

      expect(response.status).toBe(500)
      expect(data.error).toContain('Failed to generate content')
    })
  })

  describe('POST /api/ai/analyze', () => {
    it('should analyze ATS compatibility successfully', async () => {
      const { aiService } = await import('@/lib/ai/openai')
      
      const mockAnalysis = {
        score: 85,
        missingKeywords: ['agile', 'scrum'],
        suggestions: ['Add more keywords', 'Improve formatting'],
        keywordDensity: { javascript: 0.05, react: 0.03 }
      }

      const mockKeywords = ['full-stack', 'responsive design']

      ;(aiService.analyzeATSCompatibility as any).mockResolvedValue(mockAnalysis)
      ;(aiService.generateKeywordSuggestions as any).mockResolvedValue(mockKeywords)

      const requestBody = {
        resumeContent: 'Software engineer with JavaScript experience',
        jobDescription: 'Looking for a full-stack developer with React and Node.js',
        targetKeywords: ['javascript', 'react', 'nodejs']
      }

      const request = new NextRequest('http://localhost:3000/api/ai/analyze', {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: {
          'Content-Type': 'application/json'
        }
      })

      const response = await analyzeATS(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.score).toBe(85)
      expect(data.missingKeywords).toEqual(['agile', 'scrum'])
      expect(data.keywordSuggestions).toEqual(mockKeywords)
      expect(data.analyzedAt).toBeDefined()
    })

    it('should validate required fields', async () => {
      const invalidRequestBody = {
        resumeContent: '',
        jobDescription: ''
      }

      const request = new NextRequest('http://localhost:3000/api/ai/analyze', {
        method: 'POST',
        body: JSON.stringify(invalidRequestBody),
        headers: {
          'Content-Type': 'application/json'
        }
      })

      const response = await analyzeATS(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.error).toContain('Invalid request data')
    })
  })

  describe('POST /api/ai/optimize', () => {
    it('should optimize resume content successfully', async () => {
      const { aiService } = await import('@/lib/ai/openai')
      
      const originalContent = 'I am a developer'
      const optimizedContent = 'Experienced Software Engineer with expertise in full-stack development'

      ;(aiService.optimizeResumeContent as any).mockResolvedValue(optimizedContent)

      const requestBody = {
        content: originalContent,
        jobDescription: 'Software Engineer position requiring full-stack development',
        targetRole: 'Software Engineer',
        industry: 'Technology',
        experienceLevel: 'mid'
      }

      const request = new NextRequest('http://localhost:3000/api/ai/optimize', {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: {
          'Content-Type': 'application/json'
        }
      })

      const response = await optimizeContent(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.optimizedContent).toBe(optimizedContent)
      expect(data.originalLength).toBe(originalContent.length)
      expect(data.optimizedLength).toBe(optimizedContent.length)
    })

    it('should handle missing required fields', async () => {
      const invalidRequestBody = {
        content: ''
      }

      const request = new NextRequest('http://localhost:3000/api/ai/optimize', {
        method: 'POST',
        body: JSON.stringify(invalidRequestBody),
        headers: {
          'Content-Type': 'application/json'
        }
      })

      const response = await optimizeContent(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.error).toContain('Invalid request data')
    })
  })

  describe('Rate Limiting and Usage Tracking', () => {
    it('should track AI usage for billing', async () => {
      const { logAIUsage } = await import('@/lib/ai/usage-logger')
      const { aiService } = await import('@/lib/ai/openai')
      
      ;(aiService.generateContent as any).mockResolvedValue('Generated content')

      const requestBody = {
        type: 'summary',
        context: { role: 'Developer' },
        tone: 'professional'
      }

      const request = new NextRequest('http://localhost:3000/api/ai/generate', {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: {
          'Content-Type': 'application/json'
        }
      })

      await generateContent(request)

      expect(logAIUsage).toHaveBeenCalledWith(
        'test-user-id',
        'generate',
        expect.objectContaining({
          type: 'summary',
          tone: 'professional'
        })
      )
    })

    it('should handle concurrent requests', async () => {
      const { aiService } = await import('@/lib/ai/openai')
      ;(aiService.generateContent as any).mockResolvedValue('Generated content')

      const requestBody = {
        type: 'summary',
        context: { role: 'Developer' },
        tone: 'professional'
      }

      const requests = Array.from({ length: 5 }, () => 
        new NextRequest('http://localhost:3000/api/ai/generate', {
          method: 'POST',
          body: JSON.stringify(requestBody),
          headers: {
            'Content-Type': 'application/json'
          }
        })
      )

      const responses = await Promise.all(
        requests.map(request => generateContent(request))
      )

      responses.forEach(response => {
        expect(response.status).toBe(200)
      })

      expect(aiService.generateContent).toHaveBeenCalledTimes(5)
    })
  })

  describe('Content Validation', () => {
    it('should validate content length limits', async () => {
      const longContent = 'a'.repeat(10000) // Very long content

      const requestBody = {
        content: longContent,
        jobDescription: 'Test job description'
      }

      const request = new NextRequest('http://localhost:3000/api/ai/optimize', {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: {
          'Content-Type': 'application/json'
        }
      })

      const response = await optimizeContent(request)
      
      // Should either succeed or fail gracefully with appropriate error
      expect([200, 400, 413]).toContain(response.status)
    })

    it('should sanitize input content', async () => {
      const { aiService } = await import('@/lib/ai/openai')
      ;(aiService.generateContent as any).mockResolvedValue('Clean content')

      const requestBody = {
        type: 'summary',
        context: {
          role: '<script>alert("xss")</script>Developer',
          industry: 'Technology'
        },
        tone: 'professional'
      }

      const request = new NextRequest('http://localhost:3000/api/ai/generate', {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: {
          'Content-Type': 'application/json'
        }
      })

      const response = await generateContent(request)
      
      // Should handle potentially malicious input gracefully
      expect([200, 400]).toContain(response.status)
    })
  })
})
