{"name": "@careercraft/web", "version": "2.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:e2e": "playwright test", "clean": "rm -rf .next dist"}, "dependencies": {"@prisma/client": "^5.7.0", "@hookform/resolvers": "^3.3.2", "@next-auth/prisma-adapter": "^1.0.7", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-collapsible": "^1.0.3", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slider": "^1.1.2", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.0.7", "bcryptjs": "^2.4.3", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "framer-motion": "^10.16.5", "lucide-react": "^0.294.0", "nanoid": "^5.0.4", "next": "^14.0.3", "next-auth": "^4.24.5", "next-themes": "^0.2.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.2", "sonner": "^1.2.4", "tailwind-merge": "^2.0.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.22.4"}, "devDependencies": {"prisma": "^5.7.0", "@playwright/test": "^1.40.0", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.1", "@types/bcryptjs": "^2.4.6", "@types/jest": "^29.5.8", "@types/node": "^20.10.0", "@types/react": "^18.2.38", "@types/react-dom": "^18.2.17", "autoprefixer": "^10.4.16", "eslint": "^8.54.0", "eslint-config-next": "^14.0.3", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "typescript": "^5.3.0"}}