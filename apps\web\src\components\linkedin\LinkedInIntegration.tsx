'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  Linkedin, 
  Download, 
  CheckCircle, 
  AlertCircle, 
  RefreshCw,
  User,
  Briefcase,
  GraduationCap,
  Award,
  Unlink
} from 'lucide-react'

interface LinkedInStatus {
  connected: boolean
  lastSynced?: string
  profileData?: {
    name: string
    headline?: string
    profileImage?: string
  }
}

interface ImportHistory {
  id: string
  resumeId?: string
  resumeTitle?: string
  status: string
  createdAt: string
  importedSections: string[]
}

interface LinkedInIntegrationProps {
  resumeId?: string
  onImportComplete?: (data: any) => void
}

export function LinkedInIntegration({ resumeId, onImportComplete }: LinkedInIntegrationProps) {
  const [status, setStatus] = useState<LinkedInStatus>({ connected: false })
  const [history, setHistory] = useState<ImportHistory[]>([])
  const [selectedSections, setSelectedSections] = useState<string[]>(['personalInfo', 'experience'])
  const [isConnecting, setIsConnecting] = useState(false)
  const [isImporting, setIsImporting] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)

  const sections = [
    { id: 'personalInfo', label: 'Personal Information', icon: User },
    { id: 'experience', label: 'Work Experience', icon: Briefcase },
    { id: 'education', label: 'Education', icon: GraduationCap },
    { id: 'skills', label: 'Skills', icon: Award }
  ]

  useEffect(() => {
    loadStatus()
    loadHistory()
  }, [])

  const loadStatus = async () => {
    try {
      const response = await fetch('/api/linkedin/import?action=status')
      if (response.ok) {
        const data = await response.json()
        setStatus(data)
      }
    } catch (error) {
      console.error('Failed to load LinkedIn status:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const loadHistory = async () => {
    try {
      const response = await fetch('/api/linkedin/import?action=history&limit=5')
      if (response.ok) {
        const data = await response.json()
        setHistory(data.history || [])
      }
    } catch (error) {
      console.error('Failed to load import history:', error)
    }
  }

  const connectLinkedIn = async () => {
    setIsConnecting(true)
    setError(null)

    try {
      // Get authorization URL
      const response = await fetch('/api/linkedin/auth?action=authorize')
      if (!response.ok) {
        throw new Error('Failed to get authorization URL')
      }

      const { authUrl } = await response.json()
      
      // Open LinkedIn OAuth in popup
      const popup = window.open(
        authUrl,
        'linkedin-auth',
        'width=600,height=600,scrollbars=yes,resizable=yes'
      )

      // Listen for popup messages
      const handleMessage = async (event: MessageEvent) => {
        if (event.origin !== window.location.origin) return

        if (event.data.type === 'LINKEDIN_AUTH_SUCCESS') {
          popup?.close()
          
          // Handle the callback
          const callbackResponse = await fetch('/api/linkedin/auth', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              action: 'callback',
              code: event.data.code,
              state: event.data.state
            })
          })

          if (callbackResponse.ok) {
            const result = await callbackResponse.json()
            setSuccess('LinkedIn account connected successfully!')
            await loadStatus()
          } else {
            const error = await callbackResponse.json()
            setError(error.error || 'Failed to connect LinkedIn account')
          }
        } else if (event.data.type === 'LINKEDIN_AUTH_ERROR') {
          popup?.close()
          setError(event.data.error || 'LinkedIn authentication failed')
        }

        window.removeEventListener('message', handleMessage)
      }

      window.addEventListener('message', handleMessage)

      // Check if popup was closed manually
      const checkClosed = setInterval(() => {
        if (popup?.closed) {
          clearInterval(checkClosed)
          window.removeEventListener('message', handleMessage)
          setIsConnecting(false)
        }
      }, 1000)
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to connect LinkedIn')
    } finally {
      setIsConnecting(false)
    }
  }

  const disconnectLinkedIn = async () => {
    try {
      const response = await fetch('/api/linkedin/auth', {
        method: 'DELETE'
      })

      if (response.ok) {
        setSuccess('LinkedIn account disconnected successfully!')
        setStatus({ connected: false })
        setHistory([])
      } else {
        const error = await response.json()
        setError(error.error || 'Failed to disconnect LinkedIn account')
      }
    } catch (error) {
      setError('Failed to disconnect LinkedIn account')
    }
  }

  const importData = async () => {
    if (!resumeId) {
      setError('Resume ID is required for import')
      return
    }

    setIsImporting(true)
    setError(null)

    try {
      const response = await fetch('/api/linkedin/import', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          resumeId,
          sections: selectedSections
        })
      })

      if (response.ok) {
        const result = await response.json()
        setSuccess('LinkedIn data imported successfully!')
        
        if (onImportComplete) {
          onImportComplete(result.importedData)
        }
        
        await loadHistory()
      } else {
        const error = await response.json()
        setError(error.error || 'Failed to import LinkedIn data')
      }
    } catch (error) {
      setError('Failed to import LinkedIn data')
    } finally {
      setIsImporting(false)
    }
  }

  const handleSectionToggle = (sectionId: string) => {
    setSelectedSections(prev => 
      prev.includes(sectionId)
        ? prev.filter(id => id !== sectionId)
        : [...prev, sectionId]
    )
  }

  if (isLoading) {
    return (
      <Card className="glass-card">
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <RefreshCw className="w-6 h-6 animate-spin mr-2" />
            <span>Loading LinkedIn integration...</span>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* Connection Status */}
      <Card className="glass-card">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Linkedin className="w-5 h-5 text-blue-600" />
            <span>LinkedIn Integration</span>
          </CardTitle>
          <CardDescription>
            Connect your LinkedIn profile to auto-populate resume data
          </CardDescription>
        </CardHeader>
        <CardContent>
          {status.connected ? (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <CheckCircle className="w-5 h-5 text-green-600" />
                  <div>
                    <div className="font-medium">Connected to LinkedIn</div>
                    {status.profileData && (
                      <div className="text-sm text-muted-foreground">
                        {status.profileData.name}
                        {status.profileData.headline && ` • ${status.profileData.headline}`}
                      </div>
                    )}
                    {status.lastSynced && (
                      <div className="text-xs text-muted-foreground">
                        Last synced: {new Date(status.lastSynced).toLocaleDateString()}
                      </div>
                    )}
                  </div>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={disconnectLinkedIn}
                  className="glass-input"
                >
                  <Unlink className="w-4 h-4 mr-1" />
                  Disconnect
                </Button>
              </div>
            </div>
          ) : (
            <div className="text-center py-6">
              <Linkedin className="w-12 h-12 text-blue-600 mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">Connect Your LinkedIn Profile</h3>
              <p className="text-muted-foreground mb-4">
                Import your professional information directly from LinkedIn
              </p>
              <Button
                onClick={connectLinkedIn}
                disabled={isConnecting}
                className="glass-card bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white border-0"
              >
                {isConnecting ? (
                  <>
                    <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                    Connecting...
                  </>
                ) : (
                  <>
                    <Linkedin className="w-4 h-4 mr-2" />
                    Connect LinkedIn
                  </>
                )}
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Import Data */}
      {status.connected && resumeId && (
        <Card className="glass-card">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Download className="w-5 h-5 text-green-600" />
              <span>Import Data</span>
            </CardTitle>
            <CardDescription>
              Select which sections to import from your LinkedIn profile
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {sections.map((section) => {
                const Icon = section.icon
                return (
                  <div key={section.id} className="flex items-center space-x-3 glass-input p-3 rounded-lg">
                    <Checkbox
                      id={section.id}
                      checked={selectedSections.includes(section.id)}
                      onCheckedChange={() => handleSectionToggle(section.id)}
                    />
                    <Icon className="w-4 h-4 text-muted-foreground" />
                    <label htmlFor={section.id} className="text-sm font-medium cursor-pointer">
                      {section.label}
                    </label>
                  </div>
                )
              })}
            </div>

            <Button
              onClick={importData}
              disabled={isImporting || selectedSections.length === 0}
              className="w-full glass-card bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white border-0"
            >
              {isImporting ? (
                <>
                  <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                  Importing...
                </>
              ) : (
                <>
                  <Download className="w-4 h-4 mr-2" />
                  Import Selected Data
                </>
              )}
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Import History */}
      {history.length > 0 && (
        <Card className="glass-card">
          <CardHeader>
            <CardTitle>Import History</CardTitle>
            <CardDescription>
              Recent LinkedIn data imports
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {history.map((item) => (
                <div key={item.id} className="flex items-center justify-between glass-input p-3 rounded-lg">
                  <div>
                    <div className="font-medium">
                      {item.resumeTitle || 'Untitled Resume'}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {new Date(item.createdAt).toLocaleDateString()} • 
                      {item.importedSections.join(', ')}
                    </div>
                  </div>
                  <Badge variant={item.status === 'completed' ? 'default' : 'destructive'}>
                    {item.status}
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Alerts */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {success && (
        <Alert>
          <CheckCircle className="h-4 w-4" />
          <AlertDescription>{success}</AlertDescription>
        </Alert>
      )}
    </div>
  )
}
