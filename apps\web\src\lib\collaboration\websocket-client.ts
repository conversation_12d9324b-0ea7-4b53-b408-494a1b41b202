/**
 * WebSocket Client for Real-time Collaboration
 * 
 * Handles client-side WebSocket connection and message handling
 * for collaborative resume editing.
 */

import { z } from 'zod'
import { CollaborationMessage, ChangeOperation, PresenceInfo } from './websocket-server'

export interface CollaborationClientOptions {
  sessionId: string
  token: string
  serverUrl?: string
  reconnectAttempts?: number
  reconnectDelay?: number
}

export interface CollaborationClientEvents {
  connected: () => void
  disconnected: () => void
  error: (error: Error) => void
  change: (change: ChangeOperation, author: any) => void
  presence: (presence: PresenceInfo[]) => void
  comment: (comment: any) => void
  cursor: (cursor: any) => void
  userJoined: (user: PresenceInfo) => void
  userLeft: (userId: string) => void
}

export class CollaborationWebSocketClient {
  private ws: WebSocket | null = null
  private options: CollaborationClientOptions
  private events: Partial<CollaborationClientEvents> = {}
  private reconnectAttempts = 0
  private reconnectTimer: NodeJS.Timeout | null = null
  private heartbeatTimer: NodeJS.Timeout | null = null
  private isConnected = false
  private isReconnecting = false

  constructor(options: CollaborationClientOptions) {
    this.options = {
      serverUrl: 'ws://localhost:8080',
      reconnectAttempts: 5,
      reconnectDelay: 1000,
      ...options
    }
  }

  /**
   * Connect to the WebSocket server
   */
  async connect(): Promise<void> {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      return
    }

    try {
      const url = new URL(this.options.serverUrl!)
      url.searchParams.set('token', this.options.token)
      url.searchParams.set('sessionId', this.options.sessionId)

      this.ws = new WebSocket(url.toString())
      
      this.ws.onopen = this.handleOpen.bind(this)
      this.ws.onmessage = this.handleMessage.bind(this)
      this.ws.onclose = this.handleClose.bind(this)
      this.ws.onerror = this.handleError.bind(this)

      // Wait for connection
      return new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('Connection timeout'))
        }, 10000)

        this.ws!.onopen = () => {
          clearTimeout(timeout)
          this.handleOpen()
          resolve()
        }

        this.ws!.onerror = (error) => {
          clearTimeout(timeout)
          reject(new Error('Connection failed'))
        }
      })
    } catch (error) {
      throw new Error(`Failed to connect: ${error}`)
    }
  }

  /**
   * Disconnect from the WebSocket server
   */
  disconnect(): void {
    this.isReconnecting = false
    
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
      this.reconnectTimer = null
    }

    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer)
      this.heartbeatTimer = null
    }

    if (this.ws) {
      this.ws.close(1000, 'Client disconnect')
      this.ws = null
    }

    this.isConnected = false
  }

  /**
   * Send a change operation
   */
  sendChange(operation: ChangeOperation): void {
    this.sendMessage({
      type: 'change',
      sessionId: this.options.sessionId,
      userId: '', // Will be set by server
      timestamp: Date.now(),
      data: { operation }
    })
  }

  /**
   * Send presence update
   */
  sendPresence(status: 'active' | 'idle' | 'away', cursor?: { sectionPath: string; position: number }): void {
    this.sendMessage({
      type: 'presence',
      sessionId: this.options.sessionId,
      userId: '', // Will be set by server
      timestamp: Date.now(),
      data: { status, cursor }
    })
  }

  /**
   * Send comment
   */
  sendComment(sectionPath: string, content: string, parentId?: string): void {
    this.sendMessage({
      type: 'comment',
      sessionId: this.options.sessionId,
      userId: '', // Will be set by server
      timestamp: Date.now(),
      data: { sectionPath, content, parentId }
    })
  }

  /**
   * Send cursor position
   */
  sendCursor(sectionPath: string, position: number): void {
    this.sendMessage({
      type: 'cursor',
      sessionId: this.options.sessionId,
      userId: '', // Will be set by server
      timestamp: Date.now(),
      data: { cursor: { sectionPath, position } }
    })
  }

  /**
   * Register event listener
   */
  on<K extends keyof CollaborationClientEvents>(
    event: K,
    listener: CollaborationClientEvents[K]
  ): void {
    this.events[event] = listener
  }

  /**
   * Remove event listener
   */
  off<K extends keyof CollaborationClientEvents>(event: K): void {
    delete this.events[event]
  }

  /**
   * Get connection status
   */
  isConnectedToServer(): boolean {
    return this.isConnected
  }

  private handleOpen(): void {
    console.log('✅ Connected to collaboration server')
    this.isConnected = true
    this.reconnectAttempts = 0
    this.isReconnecting = false

    // Start heartbeat
    this.heartbeatTimer = setInterval(() => {
      this.sendPresence('active')
    }, 30000)

    this.events.connected?.()
  }

  private handleMessage(event: MessageEvent): void {
    try {
      const message: CollaborationMessage = JSON.parse(event.data)
      
      switch (message.type) {
        case 'change':
          this.handleChangeMessage(message)
          break
        case 'presence':
          this.handlePresenceMessage(message)
          break
        case 'comment':
          this.handleCommentMessage(message)
          break
        case 'cursor':
          this.handleCursorMessage(message)
          break
        case 'join':
          console.log('📝 Joined collaboration session:', message.data.message)
          break
        default:
          console.warn('⚠️  Unknown message type:', message.type)
      }
    } catch (error) {
      console.error('❌ Error parsing message:', error)
    }
  }

  private handleChangeMessage(message: CollaborationMessage): void {
    const { operation, author } = message.data
    this.events.change?.(operation, author)
  }

  private handlePresenceMessage(message: CollaborationMessage): void {
    const { action, user, userId, activeUsers } = message.data
    
    switch (action) {
      case 'joined':
        this.events.userJoined?.(user)
        break
      case 'left':
        this.events.userLeft?.(userId)
        break
      case 'updated':
        this.events.presence?.(activeUsers)
        break
    }
  }

  private handleCommentMessage(message: CollaborationMessage): void {
    this.events.comment?.(message.data)
  }

  private handleCursorMessage(message: CollaborationMessage): void {
    this.events.cursor?.(message.data)
  }

  private handleClose(event: CloseEvent): void {
    console.log('🔌 Disconnected from collaboration server:', event.reason)
    this.isConnected = false

    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer)
      this.heartbeatTimer = null
    }

    this.events.disconnected?.()

    // Attempt reconnection if not intentional disconnect
    if (event.code !== 1000 && !this.isReconnecting) {
      this.attemptReconnect()
    }
  }

  private handleError(error: Event): void {
    console.error('❌ WebSocket error:', error)
    this.events.error?.(new Error('WebSocket connection error'))
  }

  private sendMessage(message: CollaborationMessage): void {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message))
    } else {
      console.warn('⚠️  Cannot send message: WebSocket not connected')
    }
  }

  private attemptReconnect(): void {
    if (this.reconnectAttempts >= this.options.reconnectAttempts!) {
      console.error('❌ Max reconnection attempts reached')
      return
    }

    this.isReconnecting = true
    this.reconnectAttempts++

    const delay = this.options.reconnectDelay! * Math.pow(2, this.reconnectAttempts - 1)
    
    console.log(`🔄 Attempting to reconnect (${this.reconnectAttempts}/${this.options.reconnectAttempts}) in ${delay}ms`)

    this.reconnectTimer = setTimeout(async () => {
      try {
        await this.connect()
      } catch (error) {
        console.error('❌ Reconnection failed:', error)
        this.attemptReconnect()
      }
    }, delay)
  }
}

// Factory function for creating collaboration client
export function createCollaborationClient(options: CollaborationClientOptions): CollaborationWebSocketClient {
  return new CollaborationWebSocketClient(options)
}

// Hook for React components
export function useCollaborationClient(options: CollaborationClientOptions) {
  const client = new CollaborationWebSocketClient(options)
  
  return {
    client,
    connect: () => client.connect(),
    disconnect: () => client.disconnect(),
    sendChange: (operation: ChangeOperation) => client.sendChange(operation),
    sendPresence: (status: 'active' | 'idle' | 'away', cursor?: { sectionPath: string; position: number }) => 
      client.sendPresence(status, cursor),
    sendComment: (sectionPath: string, content: string, parentId?: string) => 
      client.sendComment(sectionPath, content, parentId),
    sendCursor: (sectionPath: string, position: number) => 
      client.sendCursor(sectionPath, position),
    isConnected: () => client.isConnectedToServer()
  }
}
