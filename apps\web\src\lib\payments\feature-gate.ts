/**
 * Feature Access Control & Usage Tracking
 * 
 * Manages premium feature access based on subscription plans and usage limits
 */

import { prisma } from '@/lib/db'
import { subscriptionService, type FeatureAccess } from './subscription-service'
import { z } from 'zod'

// Feature definitions
export const FEATURES = {
  // Resume Management
  RESUME_CREATION: 'resume_creation',
  TEMPLATE_ACCESS: 'template_access',
  CUSTOM_TEMPLATES: 'custom_templates',
  TEMPLATE_MARKETPLACE_SELLING: 'template_marketplace_selling',
  
  // AI Features
  AI_SUGGESTIONS: 'ai_suggestions',
  ADVANCED_AI: 'advanced_ai',
  JOB_MATCHING: 'job_matching',
  INTERVIEW_PREP: 'interview_prep',
  
  // Collaboration
  REAL_TIME_EDITING: 'real_time_editing',
  VERSION_CONTROL: 'version_control',
  COMMENT_SYSTEM: 'comment_system',
  TEAM_COLLABORATION: 'team_collaboration',
  
  // Export & Sharing
  PDF_EXPORT: 'pdf_export',
  CUSTOM_BRANDING: 'custom_branding',
  PUBLIC_PROFILES: 'public_profiles',
  LINKEDIN_INTEGRATION: 'linkedin_integration',
  
  // Analytics & Insights
  BASIC_ANALYTICS: 'basic_analytics',
  ADVANCED_ANALYTICS: 'advanced_analytics',
  MARKET_INSIGHTS: 'market_insights',
  
  // Support & Services
  PRIORITY_SUPPORT: 'priority_support',
  API_ACCESS: 'api_access',
  SSO_INTEGRATION: 'sso_integration'
} as const

export type FeatureName = typeof FEATURES[keyof typeof FEATURES]

// Usage tracking schemas
export const TrackUsageSchema = z.object({
  userId: z.string(),
  feature: z.string(),
  amount: z.number().positive().default(1),
  metadata: z.record(z.string()).optional()
})

export interface FeatureUsageResult {
  allowed: boolean
  remaining: number | 'unlimited'
  limit: number | 'unlimited'
  resetDate?: Date
}

export interface FeatureAccessResult {
  hasAccess: boolean
  reason?: string
  upgradeRequired?: boolean
  suggestedPlan?: string
}

export class FeatureGate {
  /**
   * Check if user has access to a specific feature
   */
  async checkAccess(userId: string, feature: FeatureName): Promise<FeatureAccessResult> {
    try {
      const subscription = await subscriptionService.getUserSubscription(userId)
      
      // If no subscription, user has free tier access
      if (!subscription) {
        return this.checkFreeAccess(feature)
      }

      // Check if subscription is active
      if (!this.isSubscriptionActive(subscription)) {
        return {
          hasAccess: false,
          reason: 'Subscription is not active',
          upgradeRequired: true
        }
      }

      const features = subscription.plan.features
      
      // Check feature-specific access
      switch (feature) {
        case FEATURES.RESUME_CREATION:
          return { hasAccess: true } // All plans allow resume creation
          
        case FEATURES.TEMPLATE_ACCESS:
          return { hasAccess: true } // All plans have some template access
          
        case FEATURES.CUSTOM_TEMPLATES:
          return { 
            hasAccess: features.customTemplates,
            reason: !features.customTemplates ? 'Custom templates require Pro plan or higher' : undefined,
            upgradeRequired: !features.customTemplates
          }
          
        case FEATURES.TEMPLATE_MARKETPLACE_SELLING:
          return { 
            hasAccess: features.templateMarketplaceSelling,
            reason: !features.templateMarketplaceSelling ? 'Template selling requires Business plan or higher' : undefined,
            upgradeRequired: !features.templateMarketplaceSelling
          }
          
        case FEATURES.AI_SUGGESTIONS:
          return { hasAccess: true } // All plans have some AI access
          
        case FEATURES.ADVANCED_AI:
          return { 
            hasAccess: features.advancedAI,
            reason: !features.advancedAI ? 'Advanced AI features require Pro plan or higher' : undefined,
            upgradeRequired: !features.advancedAI
          }
          
        case FEATURES.JOB_MATCHING:
          return { 
            hasAccess: features.jobMatching,
            reason: !features.jobMatching ? 'Job matching requires Pro plan or higher' : undefined,
            upgradeRequired: !features.jobMatching
          }
          
        case FEATURES.INTERVIEW_PREP:
          return { 
            hasAccess: features.interviewPrep,
            reason: !features.interviewPrep ? 'Interview prep requires Pro plan or higher' : undefined,
            upgradeRequired: !features.interviewPrep
          }
          
        case FEATURES.REAL_TIME_EDITING:
          return { 
            hasAccess: features.realTimeEditing,
            reason: !features.realTimeEditing ? 'Real-time editing requires Pro plan or higher' : undefined,
            upgradeRequired: !features.realTimeEditing
          }
          
        case FEATURES.VERSION_CONTROL:
          return { 
            hasAccess: features.versionControl,
            reason: !features.versionControl ? 'Version control requires Pro plan or higher' : undefined,
            upgradeRequired: !features.versionControl
          }
          
        case FEATURES.COMMENT_SYSTEM:
          return { 
            hasAccess: features.commentSystem,
            reason: !features.commentSystem ? 'Comment system requires Pro plan or higher' : undefined,
            upgradeRequired: !features.commentSystem
          }
          
        case FEATURES.PDF_EXPORT:
          return { 
            hasAccess: features.pdfExport,
            reason: !features.pdfExport ? 'PDF export requires Pro plan or higher' : undefined,
            upgradeRequired: !features.pdfExport
          }
          
        case FEATURES.CUSTOM_BRANDING:
          return { 
            hasAccess: features.customBranding,
            reason: !features.customBranding ? 'Custom branding requires Pro plan or higher' : undefined,
            upgradeRequired: !features.customBranding
          }
          
        case FEATURES.PUBLIC_PROFILES:
          return { 
            hasAccess: features.publicProfiles,
            reason: !features.publicProfiles ? 'Public profiles require Pro plan or higher' : undefined,
            upgradeRequired: !features.publicProfiles
          }
          
        case FEATURES.LINKEDIN_INTEGRATION:
          return { 
            hasAccess: features.linkedinIntegration,
            reason: !features.linkedinIntegration ? 'LinkedIn integration requires Pro plan or higher' : undefined,
            upgradeRequired: !features.linkedinIntegration
          }
          
        case FEATURES.BASIC_ANALYTICS:
          return { 
            hasAccess: features.basicAnalytics,
            reason: !features.basicAnalytics ? 'Analytics require Pro plan or higher' : undefined,
            upgradeRequired: !features.basicAnalytics
          }
          
        case FEATURES.ADVANCED_ANALYTICS:
          return { 
            hasAccess: features.advancedAnalytics,
            reason: !features.advancedAnalytics ? 'Advanced analytics require Business plan or higher' : undefined,
            upgradeRequired: !features.advancedAnalytics
          }
          
        case FEATURES.MARKET_INSIGHTS:
          return { 
            hasAccess: features.marketInsights,
            reason: !features.marketInsights ? 'Market insights require Business plan or higher' : undefined,
            upgradeRequired: !features.marketInsights
          }
          
        case FEATURES.API_ACCESS:
          return { 
            hasAccess: features.apiAccess,
            reason: !features.apiAccess ? 'API access requires Business plan or higher' : undefined,
            upgradeRequired: !features.apiAccess
          }
          
        case FEATURES.SSO_INTEGRATION:
          return { 
            hasAccess: features.ssoIntegration,
            reason: !features.ssoIntegration ? 'SSO integration requires Enterprise plan' : undefined,
            upgradeRequired: !features.ssoIntegration
          }
          
        default:
          return { hasAccess: false, reason: 'Unknown feature' }
      }
    } catch (error) {
      console.error('Error checking feature access:', error)
      return { hasAccess: false, reason: 'Error checking access' }
    }
  }

  /**
   * Check usage limits for a feature
   */
  async checkUsageLimit(userId: string, feature: FeatureName): Promise<FeatureUsageResult> {
    try {
      const subscription = await subscriptionService.getUserSubscription(userId)
      
      // Get feature limits
      const limit = this.getFeatureLimit(subscription, feature)
      
      if (limit === 'unlimited') {
        return {
          allowed: true,
          remaining: 'unlimited',
          limit: 'unlimited'
        }
      }

      // Get current usage for this month
      const startOfMonth = new Date()
      startOfMonth.setDate(1)
      startOfMonth.setHours(0, 0, 0, 0)

      const usage = await prisma.featureUsage.aggregate({
        where: {
          userId,
          featureName: feature,
          usageDate: {
            gte: startOfMonth
          }
        },
        _sum: {
          usageCount: true
        }
      })

      const currentUsage = usage._sum.usageCount || 0
      const remaining = Math.max(0, limit - currentUsage)
      
      return {
        allowed: remaining > 0,
        remaining,
        limit,
        resetDate: new Date(startOfMonth.getFullYear(), startOfMonth.getMonth() + 1, 1)
      }
    } catch (error) {
      console.error('Error checking usage limit:', error)
      return {
        allowed: false,
        remaining: 0,
        limit: 0
      }
    }
  }

  /**
   * Track feature usage
   */
  async trackUsage(data: z.infer<typeof TrackUsageSchema>): Promise<void> {
    try {
      const validatedData = TrackUsageSchema.parse(data)
      
      const today = new Date()
      today.setHours(0, 0, 0, 0)

      await prisma.featureUsage.upsert({
        where: {
          userId_featureName_usageDate: {
            userId: validatedData.userId,
            featureName: validatedData.feature,
            usageDate: today
          }
        },
        update: {
          usageCount: {
            increment: validatedData.amount
          },
          metadata: validatedData.metadata ? JSON.stringify(validatedData.metadata) : undefined
        },
        create: {
          userId: validatedData.userId,
          featureName: validatedData.feature,
          usageCount: validatedData.amount,
          usageDate: today,
          metadata: validatedData.metadata ? JSON.stringify(validatedData.metadata) : undefined
        }
      })
    } catch (error) {
      console.error('Error tracking feature usage:', error)
      // Don't throw error as usage tracking is not critical
    }
  }

  /**
   * Get remaining usage for a feature
   */
  async getRemainingUsage(userId: string, feature: FeatureName): Promise<number | 'unlimited'> {
    try {
      const result = await this.checkUsageLimit(userId, feature)
      return result.remaining
    } catch (error) {
      console.error('Error getting remaining usage:', error)
      return 0
    }
  }

  /**
   * Check if user can perform an action (combines access and usage checks)
   */
  async canUseFeature(userId: string, feature: FeatureName): Promise<{
    allowed: boolean
    reason?: string
    upgradeRequired?: boolean
    usageExceeded?: boolean
  }> {
    try {
      // Check feature access
      const accessResult = await this.checkAccess(userId, feature)
      if (!accessResult.hasAccess) {
        return {
          allowed: false,
          reason: accessResult.reason,
          upgradeRequired: accessResult.upgradeRequired
        }
      }

      // Check usage limits
      const usageResult = await this.checkUsageLimit(userId, feature)
      if (!usageResult.allowed) {
        return {
          allowed: false,
          reason: `Usage limit exceeded. Limit: ${usageResult.limit}, Remaining: ${usageResult.remaining}`,
          usageExceeded: true
        }
      }

      return { allowed: true }
    } catch (error) {
      console.error('Error checking feature usage:', error)
      return { allowed: false, reason: 'Error checking feature access' }
    }
  }

  /**
   * Get user's feature usage analytics
   */
  async getUserUsageAnalytics(userId: string, days: number = 30): Promise<{
    totalUsage: number
    featureBreakdown: Array<{ feature: string; usage: number }>
    dailyUsage: Array<{ date: Date; usage: number }>
  }> {
    try {
      const startDate = new Date()
      startDate.setDate(startDate.getDate() - days)

      const [totalUsage, featureBreakdown, dailyUsage] = await Promise.all([
        prisma.featureUsage.aggregate({
          where: {
            userId,
            usageDate: { gte: startDate }
          },
          _sum: { usageCount: true }
        }),
        prisma.featureUsage.groupBy({
          by: ['featureName'],
          where: {
            userId,
            usageDate: { gte: startDate }
          },
          _sum: { usageCount: true }
        }),
        prisma.featureUsage.groupBy({
          by: ['usageDate'],
          where: {
            userId,
            usageDate: { gte: startDate }
          },
          _sum: { usageCount: true },
          orderBy: { usageDate: 'asc' }
        })
      ])

      return {
        totalUsage: totalUsage._sum.usageCount || 0,
        featureBreakdown: featureBreakdown.map(item => ({
          feature: item.featureName,
          usage: item._sum.usageCount || 0
        })),
        dailyUsage: dailyUsage.map(item => ({
          date: item.usageDate,
          usage: item._sum.usageCount || 0
        }))
      }
    } catch (error) {
      console.error('Error getting usage analytics:', error)
      throw new Error('Failed to get usage analytics')
    }
  }

  /**
   * Check free tier access
   */
  private checkFreeAccess(feature: FeatureName): FeatureAccessResult {
    const freeFeatures = [
      FEATURES.RESUME_CREATION,
      FEATURES.TEMPLATE_ACCESS,
      FEATURES.AI_SUGGESTIONS,
      FEATURES.PDF_EXPORT,
      FEATURES.BASIC_ANALYTICS
    ]

    if (freeFeatures.includes(feature)) {
      return { hasAccess: true }
    }

    return {
      hasAccess: false,
      reason: 'This feature requires a paid subscription',
      upgradeRequired: true,
      suggestedPlan: 'pro'
    }
  }

  /**
   * Get feature limit based on subscription
   */
  private getFeatureLimit(subscription: any, feature: FeatureName): number | 'unlimited' {
    if (!subscription) {
      // Free tier limits
      switch (feature) {
        case FEATURES.RESUME_CREATION:
          return 1
        case FEATURES.TEMPLATE_ACCESS:
          return 3
        case FEATURES.AI_SUGGESTIONS:
          return 5
        default:
          return 0
      }
    }

    const features = subscription.plan.features

    switch (feature) {
      case FEATURES.RESUME_CREATION:
        return features.maxResumes === -1 ? 'unlimited' : features.maxResumes
      case FEATURES.TEMPLATE_ACCESS:
        return features.maxTemplates === -1 ? 'unlimited' : features.maxTemplates
      case FEATURES.AI_SUGGESTIONS:
        return features.aiSuggestionsLimit === -1 ? 'unlimited' : features.aiSuggestionsLimit
      case FEATURES.TEAM_COLLABORATION:
        return features.maxCollaborators === -1 ? 'unlimited' : features.maxCollaborators
      default:
        return 'unlimited'
    }
  }

  /**
   * Check if subscription is active
   */
  private isSubscriptionActive(subscription: any): boolean {
    const activeStatuses = ['active', 'trialing']
    return activeStatuses.includes(subscription.status)
  }
}

// Export singleton instance
export const featureGate = new FeatureGate()
