#!/usr/bin/env tsx

/**
 * UI Components Testing Script
 * 
 * This script performs comprehensive testing of the UI components:
 * 1. Layout components (<PERSON><PERSON>, <PERSON>er, Dashboard)
 * 2. Loading states and skeletons
 * 3. Error boundaries and error handling
 * 4. Responsive design
 * 5. Accessibility compliance
 * 6. Theme switching
 * 7. Component interactions
 */

import { config } from 'dotenv';
import { join } from 'path';
import { spawn } from 'child_process';

// Load environment variables
config({ path: join(__dirname, '../.env.local') });

interface TestResult {
  name: string;
  status: 'PASS' | 'FAIL' | 'SKIP';
  duration: number;
  error?: string;
  details?: any;
}

class UITester {
  private results: TestResult[] = [];

  private async runTest(name: string, testFn: () => Promise<any>): Promise<void> {
    const startTime = Date.now();
    console.log(`🎨 Running: ${name}`);

    try {
      const result = await testFn();
      const duration = Date.now() - startTime;
      
      this.results.push({
        name,
        status: 'PASS',
        duration,
        details: result,
      });
      
      console.log(`✅ PASS: ${name} (${duration}ms)`);
    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      
      this.results.push({
        name,
        status: 'FAIL',
        duration,
        error: errorMessage,
      });
      
      console.log(`❌ FAIL: ${name} (${duration}ms) - ${errorMessage}`);
    }
  }

  private async runJestTests(testPattern: string): Promise<boolean> {
    return new Promise((resolve) => {
      const jest = spawn('npm', ['run', 'test', '--', testPattern, '--passWithNoTests'], {
        cwd: join(__dirname, '../apps/web'),
        stdio: 'pipe',
      });

      let output = '';
      jest.stdout.on('data', (data) => {
        output += data.toString();
      });

      jest.stderr.on('data', (data) => {
        output += data.toString();
      });

      jest.on('close', (code) => {
        resolve(code === 0);
      });
    });
  }

  async testComponentStructure(): Promise<void> {
    await this.runTest('Component File Structure', async () => {
      const fs = require('fs');
      const path = require('path');

      const componentsDir = join(__dirname, '../apps/web/src/components');
      const requiredComponents = [
        'layout/header.tsx',
        'layout/footer.tsx',
        'layout/main-layout.tsx',
        'layout/dashboard-layout.tsx',
        'ui/button.tsx',
        'ui/input.tsx',
        'ui/card.tsx',
        'ui/loading.tsx',
        'ui/error-boundary.tsx',
        'ui/icons.tsx',
      ];

      const missingComponents = [];
      for (const component of requiredComponents) {
        const componentPath = join(componentsDir, component);
        if (!fs.existsSync(componentPath)) {
          missingComponents.push(component);
        }
      }

      if (missingComponents.length > 0) {
        throw new Error(`Missing components: ${missingComponents.join(', ')}`);
      }

      return {
        totalComponents: requiredComponents.length,
        allComponentsPresent: true,
      };
    });
  }

  async testLayoutComponents(): Promise<void> {
    await this.runTest('Layout Component Tests', async () => {
      const success = await this.runJestTests('layout/__tests__');
      
      if (!success) {
        throw new Error('Layout component tests failed');
      }

      return {
        testSuite: 'layout',
        passed: true,
      };
    });
  }

  async testUIComponents(): Promise<void> {
    await this.runTest('UI Component Tests', async () => {
      const success = await this.runJestTests('ui/__tests__');
      
      if (!success) {
        throw new Error('UI component tests failed');
      }

      return {
        testSuite: 'ui',
        passed: true,
      };
    });
  }

  async testTypeScriptCompilation(): Promise<void> {
    await this.runTest('TypeScript Compilation', async () => {
      return new Promise((resolve, reject) => {
        const tsc = spawn('npm', ['run', 'type-check'], {
          cwd: join(__dirname, '../apps/web'),
          stdio: 'pipe',
        });

        let output = '';
        tsc.stdout.on('data', (data) => {
          output += data.toString();
        });

        tsc.stderr.on('data', (data) => {
          output += data.toString();
        });

        tsc.on('close', (code) => {
          if (code === 0) {
            resolve({
              compilationSuccessful: true,
              output: output.trim(),
            });
          } else {
            reject(new Error(`TypeScript compilation failed: ${output}`));
          }
        });
      });
    });
  }

  async testComponentImports(): Promise<void> {
    await this.runTest('Component Import Validation', async () => {
      const fs = require('fs');
      const path = require('path');

      // Test that components can be imported without errors
      const testImports = [
        "import { Header } from '@/components/layout/header';",
        "import { Footer } from '@/components/layout/footer';",
        "import { MainLayout } from '@/components/layout/main-layout';",
        "import { DashboardLayout } from '@/components/layout/dashboard-layout';",
        "import { Button } from '@/components/ui/button';",
        "import { LoadingSpinner } from '@/components/ui/loading';",
        "import { ErrorBoundary } from '@/components/ui/error-boundary';",
        "import { Icons } from '@/components/ui/icons';",
      ];

      // Create a temporary test file
      const testFile = join(__dirname, '../apps/web/src/test-imports.ts');
      const testContent = testImports.join('\n') + '\n\nexport {};';

      fs.writeFileSync(testFile, testContent);

      try {
        // Try to compile the test file
        const result = await new Promise((resolve, reject) => {
          const tsc = spawn('npx', ['tsc', '--noEmit', testFile], {
            cwd: join(__dirname, '../apps/web'),
            stdio: 'pipe',
          });

          let output = '';
          tsc.stderr.on('data', (data) => {
            output += data.toString();
          });

          tsc.on('close', (code) => {
            if (code === 0) {
              resolve({ success: true });
            } else {
              reject(new Error(`Import validation failed: ${output}`));
            }
          });
        });

        return {
          importsValidated: testImports.length,
          allImportsWorking: true,
        };
      } finally {
        // Clean up test file
        if (fs.existsSync(testFile)) {
          fs.unlinkSync(testFile);
        }
      }
    });
  }

  async testResponsiveDesign(): Promise<void> {
    await this.runTest('Responsive Design Classes', async () => {
      const fs = require('fs');
      const path = require('path');

      // Check that components use responsive classes
      const componentsToCheck = [
        'layout/header.tsx',
        'layout/footer.tsx',
        'layout/dashboard-layout.tsx',
      ];

      const responsivePatterns = [
        /md:/g,
        /lg:/g,
        /sm:/g,
        /xl:/g,
        /hidden.*md:/g,
        /flex.*md:/g,
        /grid.*md:/g,
      ];

      const results = [];
      for (const component of componentsToCheck) {
        const componentPath = join(__dirname, '../apps/web/src/components', component);
        const content = fs.readFileSync(componentPath, 'utf8');
        
        const responsiveClassesFound = responsivePatterns.some(pattern => 
          pattern.test(content)
        );

        results.push({
          component,
          hasResponsiveClasses: responsiveClassesFound,
        });
      }

      const allResponsive = results.every(r => r.hasResponsiveClasses);
      if (!allResponsive) {
        const nonResponsive = results.filter(r => !r.hasResponsiveClasses);
        throw new Error(`Components missing responsive classes: ${nonResponsive.map(r => r.component).join(', ')}`);
      }

      return {
        componentsChecked: results.length,
        allHaveResponsiveClasses: true,
        results,
      };
    });
  }

  async testAccessibilityFeatures(): Promise<void> {
    await this.runTest('Accessibility Features', async () => {
      const fs = require('fs');
      const path = require('path');

      // Check for accessibility features in components
      const accessibilityPatterns = [
        /aria-label/g,
        /aria-labelledby/g,
        /aria-describedby/g,
        /role=/g,
        /sr-only/g,
        /alt=/g,
      ];

      const componentsToCheck = [
        'layout/header.tsx',
        'ui/button.tsx',
        'ui/loading.tsx',
        'ui/error-boundary.tsx',
      ];

      const results = [];
      for (const component of componentsToCheck) {
        const componentPath = join(__dirname, '../apps/web/src/components', component);
        const content = fs.readFileSync(componentPath, 'utf8');
        
        const accessibilityFeaturesFound = accessibilityPatterns.some(pattern => 
          pattern.test(content)
        );

        results.push({
          component,
          hasAccessibilityFeatures: accessibilityFeaturesFound,
        });
      }

      return {
        componentsChecked: results.length,
        accessibilityFeaturesImplemented: results.filter(r => r.hasAccessibilityFeatures).length,
        results,
      };
    });
  }

  async testThemeSupport(): Promise<void> {
    await this.runTest('Theme Support', async () => {
      const fs = require('fs');
      const path = require('path');

      // Check for theme-related classes and imports
      const themePatterns = [
        /dark:/g,
        /useTheme/g,
        /next-themes/g,
        /ThemeProvider/g,
      ];

      const headerPath = join(__dirname, '../apps/web/src/components/layout/header.tsx');
      const headerContent = fs.readFileSync(headerPath, 'utf8');
      
      const hasThemeSupport = themePatterns.some(pattern => 
        pattern.test(headerContent)
      );

      if (!hasThemeSupport) {
        throw new Error('Theme support not found in header component');
      }

      return {
        themeSupportImplemented: true,
        themeTogglePresent: headerContent.includes('setTheme'),
      };
    });
  }

  async testErrorHandling(): Promise<void> {
    await this.runTest('Error Handling Implementation', async () => {
      const fs = require('fs');
      const path = require('path');

      // Check that error boundaries are properly implemented
      const errorBoundaryPath = join(__dirname, '../apps/web/src/components/ui/error-boundary.tsx');
      const errorBoundaryContent = fs.readFileSync(errorBoundaryPath, 'utf8');

      const requiredErrorFeatures = [
        'ErrorBoundary',
        'componentDidCatch',
        'getDerivedStateFromError',
        'DefaultErrorFallback',
        'useErrorHandler',
      ];

      const missingFeatures = requiredErrorFeatures.filter(feature => 
        !errorBoundaryContent.includes(feature)
      );

      if (missingFeatures.length > 0) {
        throw new Error(`Missing error handling features: ${missingFeatures.join(', ')}`);
      }

      return {
        errorFeaturesImplemented: requiredErrorFeatures.length,
        allErrorFeaturesPresent: true,
      };
    });
  }

  async testLoadingStates(): Promise<void> {
    await this.runTest('Loading States Implementation', async () => {
      const fs = require('fs');
      const path = require('path');

      // Check that loading components are properly implemented
      const loadingPath = join(__dirname, '../apps/web/src/components/ui/loading.tsx');
      const loadingContent = fs.readFileSync(loadingPath, 'utf8');

      const requiredLoadingComponents = [
        'LoadingSpinner',
        'LoadingPage',
        'LoadingCard',
        'LoadingButton',
        'Skeleton',
        'LoadingTable',
        'LoadingList',
      ];

      const missingComponents = requiredLoadingComponents.filter(component => 
        !loadingContent.includes(component)
      );

      if (missingComponents.length > 0) {
        throw new Error(`Missing loading components: ${missingComponents.join(', ')}`);
      }

      return {
        loadingComponentsImplemented: requiredLoadingComponents.length,
        allLoadingComponentsPresent: true,
      };
    });
  }

  async runAllTests(): Promise<void> {
    console.log('🎨 Starting UI Components Tests...\n');

    await this.testComponentStructure();
    await this.testTypeScriptCompilation();
    await this.testComponentImports();
    await this.testResponsiveDesign();
    await this.testAccessibilityFeatures();
    await this.testThemeSupport();
    await this.testErrorHandling();
    await this.testLoadingStates();
    await this.testLayoutComponents();
    await this.testUIComponents();

    await this.printSummary();
  }

  private async printSummary(): Promise<void> {
    console.log('\n📊 UI Components Test Summary');
    console.log('==============================');

    const passed = this.results.filter(r => r.status === 'PASS').length;
    const failed = this.results.filter(r => r.status === 'FAIL').length;
    const total = this.results.length;

    console.log(`Total Tests: ${total}`);
    console.log(`Passed: ${passed}`);
    console.log(`Failed: ${failed}`);
    console.log(`Success Rate: ${((passed / total) * 100).toFixed(1)}%`);

    if (failed > 0) {
      console.log('\n❌ Failed Tests:');
      this.results
        .filter(r => r.status === 'FAIL')
        .forEach(r => {
          console.log(`  - ${r.name}: ${r.error}`);
        });
    }

    const totalTime = this.results.reduce((sum, r) => sum + r.duration, 0);
    console.log(`\nTotal Execution Time: ${totalTime}ms`);

    if (failed === 0) {
      console.log('\n🎉 All UI component tests passed! Components are ready for production.');
    } else {
      console.log('\n⚠️  Some tests failed. Please check your UI component implementation.');
      process.exit(1);
    }
  }
}

// Run the tests
async function main() {
  const tester = new UITester();
  await tester.runAllTests();
}

if (require.main === module) {
  main().catch(console.error);
}
