#!/usr/bin/env node

/**
 * Build and Package Script
 * 
 * Builds the extension for all target browsers and creates
 * distribution packages ready for browser store submission.
 */

const fs = require('fs')
const path = require('path')
const { execSync } = require('child_process')
const archiver = require('archiver')

console.log('🔌 CAREERCRAFT BROWSER EXTENSION - BUILD & PACKAGE')
console.log('=' .repeat(60))

const browsers = ['chrome', 'firefox', 'edge']
const version = require('../package.json').version

// Build configuration
const buildConfig = {
  chrome: {
    name: 'CareerCraft Autofill',
    filename: `careercraft-autofill-chrome-v${version}.zip`,
    manifest: 'chrome.json',
    storeUrl: 'https://chrome.google.com/webstore'
  },
  firefox: {
    name: 'CareerCraft Autofill',
    filename: `careercraft-autofill-firefox-v${version}.zip`,
    manifest: 'firefox.json',
    storeUrl: 'https://addons.mozilla.org'
  },
  edge: {
    name: 'CareerCraft Autofill',
    filename: `careercraft-autofill-edge-v${version}.zip`,
    manifest: 'edge.json',
    storeUrl: 'https://microsoftedge.microsoft.com/addons'
  }
}

async function main() {
  try {
    console.log('🚀 Starting build and package process...\n')

    // Clean previous builds
    await cleanBuildDirectories()

    // Install dependencies
    await installDependencies()

    // Run tests
    await runTests()

    // Build for each browser
    for (const browser of browsers) {
      await buildForBrowser(browser)
    }

    // Create distribution packages
    for (const browser of browsers) {
      await createDistributionPackage(browser)
    }

    // Generate submission assets
    await generateSubmissionAssets()

    // Validate packages
    await validatePackages()

    console.log('\n🎉 BUILD AND PACKAGE COMPLETE!')
    console.log('✅ All browser extensions built successfully')
    console.log('📦 Distribution packages created')
    console.log('📋 Submission assets generated')
    console.log('\n📁 Output directory: ./packages/')
    
    // Display package information
    displayPackageInfo()

  } catch (error) {
    console.error('\n💥 BUILD FAILED:', error.message)
    process.exit(1)
  }
}

async function cleanBuildDirectories() {
  console.log('🧹 Cleaning build directories...')
  
  const directories = ['dist', 'packages']
  
  for (const dir of directories) {
    if (fs.existsSync(dir)) {
      fs.rmSync(dir, { recursive: true, force: true })
    }
    fs.mkdirSync(dir, { recursive: true })
  }
  
  console.log('✅ Build directories cleaned')
}

async function installDependencies() {
  console.log('📦 Installing dependencies...')
  
  try {
    execSync('npm ci', { stdio: 'inherit' })
    console.log('✅ Dependencies installed')
  } catch (error) {
    throw new Error('Failed to install dependencies')
  }
}

async function runTests() {
  console.log('🧪 Running tests...')
  
  try {
    execSync('npm run test', { stdio: 'inherit' })
    console.log('✅ All tests passed')
  } catch (error) {
    throw new Error('Tests failed - build aborted')
  }
}

async function buildForBrowser(browser) {
  console.log(`\n🏗️  Building for ${browser.toUpperCase()}...`)
  
  try {
    const command = `npm run build:${browser}`
    execSync(command, { stdio: 'inherit' })
    
    // Verify build output
    const buildPath = `dist/${browser}`
    if (!fs.existsSync(buildPath)) {
      throw new Error(`Build output not found: ${buildPath}`)
    }
    
    // Verify manifest
    const manifestPath = path.join(buildPath, 'manifest.json')
    if (!fs.existsSync(manifestPath)) {
      throw new Error(`Manifest not found: ${manifestPath}`)
    }
    
    // Verify required files
    const requiredFiles = [
      'background/background.js',
      'content/content.js',
      'popup/popup.html',
      'popup/popup.js'
    ]
    
    for (const file of requiredFiles) {
      const filePath = path.join(buildPath, file)
      if (!fs.existsSync(filePath)) {
        throw new Error(`Required file missing: ${file}`)
      }
    }
    
    console.log(`✅ ${browser.toUpperCase()} build completed`)
    
  } catch (error) {
    throw new Error(`Failed to build for ${browser}: ${error.message}`)
  }
}

async function createDistributionPackage(browser) {
  console.log(`📦 Creating ${browser.toUpperCase()} distribution package...`)
  
  const config = buildConfig[browser]
  const sourcePath = `dist/${browser}`
  const outputPath = `packages/${config.filename}`
  
  return new Promise((resolve, reject) => {
    const output = fs.createWriteStream(outputPath)
    const archive = archiver('zip', { zlib: { level: 9 } })
    
    output.on('close', () => {
      const sizeKB = (archive.pointer() / 1024).toFixed(2)
      console.log(`✅ ${config.filename} created (${sizeKB} KB)`)
      resolve()
    })
    
    archive.on('error', (err) => {
      reject(new Error(`Failed to create package: ${err.message}`))
    })
    
    archive.pipe(output)
    archive.directory(sourcePath, false)
    archive.finalize()
  })
}

async function generateSubmissionAssets() {
  console.log('\n📋 Generating submission assets...')
  
  // Create submission directory
  const submissionDir = 'packages/submission-assets'
  fs.mkdirSync(submissionDir, { recursive: true })
  
  // Copy store assets
  if (fs.existsSync('store-assets')) {
    execSync(`cp -r store-assets/* ${submissionDir}/`)
  }
  
  // Generate package manifest
  const packageManifest = {
    name: 'CareerCraft Autofill',
    version: version,
    buildDate: new Date().toISOString(),
    browsers: browsers.map(browser => ({
      browser: browser,
      filename: buildConfig[browser].filename,
      storeUrl: buildConfig[browser].storeUrl
    })),
    checksums: {}
  }
  
  // Calculate checksums
  for (const browser of browsers) {
    const filename = buildConfig[browser].filename
    const filePath = `packages/${filename}`
    if (fs.existsSync(filePath)) {
      const crypto = require('crypto')
      const fileBuffer = fs.readFileSync(filePath)
      const hashSum = crypto.createHash('sha256')
      hashSum.update(fileBuffer)
      packageManifest.checksums[filename] = hashSum.digest('hex')
    }
  }
  
  fs.writeFileSync(
    `${submissionDir}/package-manifest.json`,
    JSON.stringify(packageManifest, null, 2)
  )
  
  // Generate submission checklist
  const checklist = generateSubmissionChecklist()
  fs.writeFileSync(`${submissionDir}/submission-checklist.md`, checklist)
  
  console.log('✅ Submission assets generated')
}

async function validatePackages() {
  console.log('\n🔍 Validating packages...')
  
  for (const browser of browsers) {
    const config = buildConfig[browser]
    const packagePath = `packages/${config.filename}`
    
    if (!fs.existsSync(packagePath)) {
      throw new Error(`Package not found: ${config.filename}`)
    }
    
    const stats = fs.statSync(packagePath)
    const sizeKB = (stats.size / 1024).toFixed(2)
    
    // Check package size limits
    const maxSizeKB = browser === 'chrome' ? 20480 : 10240 // Chrome: 20MB, Others: 10MB
    if (stats.size > maxSizeKB * 1024) {
      throw new Error(`Package too large: ${config.filename} (${sizeKB} KB > ${maxSizeKB} KB)`)
    }
    
    console.log(`✅ ${config.filename} validated (${sizeKB} KB)`)
  }
}

function displayPackageInfo() {
  console.log('\n📊 PACKAGE INFORMATION')
  console.log('-'.repeat(40))
  
  for (const browser of browsers) {
    const config = buildConfig[browser]
    const packagePath = `packages/${config.filename}`
    
    if (fs.existsSync(packagePath)) {
      const stats = fs.statSync(packagePath)
      const sizeKB = (stats.size / 1024).toFixed(2)
      
      console.log(`\n🔧 ${browser.toUpperCase()} Extension:`)
      console.log(`   📦 File: ${config.filename}`)
      console.log(`   📏 Size: ${sizeKB} KB`)
      console.log(`   🏪 Store: ${config.storeUrl}`)
    }
  }
  
  console.log('\n🚀 NEXT STEPS:')
  console.log('1. Review packages in ./packages/ directory')
  console.log('2. Test extensions in target browsers')
  console.log('3. Submit to browser stores using submission assets')
  console.log('4. Monitor store review process')
  console.log('5. Announce launch when approved')
}

function generateSubmissionChecklist() {
  return `# Browser Extension Submission Checklist

## Pre-Submission Validation

### Technical Requirements
- [ ] All packages built successfully
- [ ] Manifest V3 compliance verified
- [ ] Required permissions documented
- [ ] Content Security Policy implemented
- [ ] Extension tested in target browsers
- [ ] Performance benchmarks met
- [ ] Security audit completed

### Store Requirements

#### Chrome Web Store
- [ ] Developer account verified
- [ ] Store listing prepared
- [ ] Screenshots and promotional images ready
- [ ] Privacy policy published
- [ ] Payment setup (if premium features)

#### Firefox Add-ons
- [ ] Developer account created
- [ ] Add-on listing prepared
- [ ] Source code review ready
- [ ] Privacy policy linked

#### Microsoft Edge Add-ons
- [ ] Partner Center account setup
- [ ] Store listing prepared
- [ ] Age rating assigned
- [ ] Privacy policy linked

### Legal & Compliance
- [ ] Privacy policy updated
- [ ] Terms of service reviewed
- [ ] GDPR compliance verified
- [ ] CCPA compliance verified
- [ ] Accessibility standards met

### Marketing & Launch
- [ ] Launch announcement prepared
- [ ] Social media assets created
- [ ] Press release drafted
- [ ] User documentation updated
- [ ] Support channels ready

## Submission Process

1. **Chrome Web Store**
   - Upload: packages/careercraft-autofill-chrome-v${version}.zip
   - Review time: 1-3 business days
   - Status: https://chrome.google.com/webstore/devconsole

2. **Firefox Add-ons**
   - Upload: packages/careercraft-autofill-firefox-v${version}.zip
   - Review time: 1-7 business days
   - Status: https://addons.mozilla.org/developers

3. **Microsoft Edge Add-ons**
   - Upload: packages/careercraft-autofill-edge-v${version}.zip
   - Review time: 7-14 business days
   - Status: https://partner.microsoft.com/dashboard

## Post-Submission

- [ ] Monitor review status daily
- [ ] Respond to reviewer feedback promptly
- [ ] Prepare hotfix releases if needed
- [ ] Track installation metrics
- [ ] Collect user feedback
- [ ] Plan next version features

Generated: ${new Date().toISOString()}
Version: ${version}
`
}

// Run the build process
if (require.main === module) {
  main()
}

module.exports = { main }
