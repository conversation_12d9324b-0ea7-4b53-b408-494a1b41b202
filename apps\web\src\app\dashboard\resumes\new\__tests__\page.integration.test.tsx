import { describe, it, expect, vi, beforeEach } from 'vitest'
import { screen, fireEvent, waitFor } from '@testing-library/react'
import NewResumePage from '../page'
import { render, mockTemplates } from '@/test/utils'

// Mock next/navigation
const mockPush = vi.fn()
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: mockPush,
  }),
  useSearchParams: () => ({
    get: vi.fn().mockReturnValue(null),
  }),
}))

// Mock the TemplateGallery component
vi.mock('@/components/templates/TemplateGallery', () => ({
  TemplateGallery: ({ onSelectTemplate, showSelectButton }: any) => (
    <div data-testid="template-gallery">
      <div>Template Gallery</div>
      {mockTemplates.map(template => (
        <div key={template.id} data-testid={`template-${template.id}`}>
          <span>{template.name}</span>
          <button
            onClick={() => onSelectTemplate && onSelectTemplate(template)}
            data-testid={`select-${template.id}`}
          >
            {showSelectButton ? 'Select Template' : 'Use This Template'}
          </button>
        </div>
      ))}
    </div>
  ),
}))

describe('NewResumePage Integration', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders the complete resume creation flow', () => {
    render(<NewResumePage />)

    // Check header
    expect(screen.getByText('Create New Resume')).toBeInTheDocument()
    expect(screen.getByText(/Build a professional resume in minutes/)).toBeInTheDocument()

    // Check progress steps
    expect(screen.getByText('Choose Template')).toBeInTheDocument()
    expect(screen.getByText('Resume Details')).toBeInTheDocument()
    expect(screen.getByText('Create Resume')).toBeInTheDocument()

    // Check initial step content
    expect(screen.getByText('Choose Your Template')).toBeInTheDocument()
    expect(screen.getByTestId('template-gallery')).toBeInTheDocument()
  })

  it('progresses through all steps of resume creation', async () => {
    render(<NewResumePage />)

    // Step 1: Select a template
    expect(screen.getByText('Choose Your Template')).toBeInTheDocument()
    
    const selectTemplateButton = screen.getByTestId('select-modern-1')
    fireEvent.click(selectTemplateButton)

    // Should progress to step 2
    const nextButton = screen.getByRole('button', { name: /next/i })
    fireEvent.click(nextButton)

    await waitFor(() => {
      expect(screen.getByText('Resume Details')).toBeInTheDocument()
      expect(screen.getByText('Selected Template')).toBeInTheDocument()
      expect(screen.getByText('Modern Professional')).toBeInTheDocument()
    })

    // Step 2: Fill in resume details
    const titleInput = screen.getByLabelText('Resume Title *')
    const descriptionInput = screen.getByLabelText('Description (Optional)')

    fireEvent.change(titleInput, { target: { value: 'My Software Engineer Resume' } })
    fireEvent.change(descriptionInput, { target: { value: 'Tailored for tech companies' } })

    const nextButton2 = screen.getByRole('button', { name: /next/i })
    fireEvent.click(nextButton2)

    await waitFor(() => {
      expect(screen.getByText('Ready to Create!')).toBeInTheDocument()
      expect(screen.getByText('My Software Engineer Resume')).toBeInTheDocument()
      expect(screen.getByText('Tailored for tech companies')).toBeInTheDocument()
    })

    // Step 3: Create resume
    const createButton = screen.getByRole('button', { name: /create my resume/i })
    fireEvent.click(createButton)

    await waitFor(() => {
      expect(screen.getByText('Creating Resume...')).toBeInTheDocument()
    })

    // Should navigate to editor after creation
    await waitFor(() => {
      expect(mockPush).toHaveBeenCalledWith('/dashboard/resumes/new-resume-id/edit')
    }, { timeout: 3000 })
  })

  it('handles back navigation correctly', async () => {
    render(<NewResumePage />)

    // Select template and go to step 2
    const selectTemplateButton = screen.getByTestId('select-modern-1')
    fireEvent.click(selectTemplateButton)

    const nextButton = screen.getByRole('button', { name: /next/i })
    fireEvent.click(nextButton)

    await waitFor(() => {
      expect(screen.getByText('Resume Details')).toBeInTheDocument()
    })

    // Go back to step 1
    const backButton = screen.getByRole('button', { name: /previous/i })
    fireEvent.click(backButton)

    await waitFor(() => {
      expect(screen.getByText('Choose Your Template')).toBeInTheDocument()
    })

    // From step 1, back should go to resumes page
    const backToResumesButton = screen.getByRole('button', { name: /back to resumes/i })
    fireEvent.click(backToResumesButton)

    expect(mockPush).toHaveBeenCalledWith('/dashboard/resumes')
  })

  it('validates required fields before allowing progression', async () => {
    render(<NewResumePage />)

    // Try to proceed without selecting template
    const nextButton = screen.getByRole('button', { name: /next/i })
    expect(nextButton).toBeDisabled()

    // Select template
    const selectTemplateButton = screen.getByTestId('select-modern-1')
    fireEvent.click(selectTemplateButton)

    // Now next button should be enabled
    expect(nextButton).not.toBeDisabled()
    fireEvent.click(nextButton)

    await waitFor(() => {
      expect(screen.getByText('Resume Details')).toBeInTheDocument()
    })

    // Go to step 3 without filling title
    const nextButton2 = screen.getByRole('button', { name: /next/i })
    fireEvent.click(nextButton2)

    await waitFor(() => {
      expect(screen.getByText('Ready to Create!')).toBeInTheDocument()
    })

    // Create button should be disabled without title
    const createButton = screen.getByRole('button', { name: /create my resume/i })
    expect(createButton).toBeDisabled()

    // Go back and fill title
    const backButton = screen.getByRole('button', { name: /previous/i })
    fireEvent.click(backButton)

    await waitFor(() => {
      const titleInput = screen.getByLabelText('Resume Title *')
      fireEvent.change(titleInput, { target: { value: 'Test Resume' } })
    })

    // Go forward again
    const nextButton3 = screen.getByRole('button', { name: /next/i })
    fireEvent.click(nextButton3)

    await waitFor(() => {
      const createButton = screen.getByRole('button', { name: /create my resume/i })
      expect(createButton).not.toBeDisabled()
    })
  })

  it('handles preselected template from URL', () => {
    // Mock URL with template parameter
    vi.mocked(vi.importMock('next/navigation')).useSearchParams.mockReturnValue({
      get: vi.fn().mockReturnValue('modern-1'),
    })

    render(<NewResumePage />)

    // Should skip to step 2 with preselected template
    expect(screen.getByText('Resume Details')).toBeInTheDocument()
  })

  it('displays progress indicators correctly', async () => {
    render(<NewResumePage />)

    // Step 1 should be active
    const step1 = screen.getByText('Choose Template').closest('div')
    expect(step1).toHaveClass('from-blue-600/20')

    // Select template and proceed
    const selectTemplateButton = screen.getByTestId('select-modern-1')
    fireEvent.click(selectTemplateButton)

    const nextButton = screen.getByRole('button', { name: /next/i })
    fireEvent.click(nextButton)

    await waitFor(() => {
      // Step 1 should be completed, Step 2 should be active
      const completedSteps = document.querySelectorAll('.bg-green-600')
      expect(completedSteps.length).toBeGreaterThan(0)
    })
  })

  it('shows what happens next information', async () => {
    render(<NewResumePage />)

    // Navigate to final step
    const selectTemplateButton = screen.getByTestId('select-modern-1')
    fireEvent.click(selectTemplateButton)

    let nextButton = screen.getByRole('button', { name: /next/i })
    fireEvent.click(nextButton)

    await waitFor(() => {
      const titleInput = screen.getByLabelText('Resume Title *')
      fireEvent.change(titleInput, { target: { value: 'Test Resume' } })
    })

    nextButton = screen.getByRole('button', { name: /next/i })
    fireEvent.click(nextButton)

    await waitFor(() => {
      expect(screen.getByText('What happens next?')).toBeInTheDocument()
      expect(screen.getByText(/We'll create your resume with the selected template/)).toBeInTheDocument()
      expect(screen.getByText(/You'll be taken to the resume editor/)).toBeInTheDocument()
      expect(screen.getByText(/AI will help you optimize your content/)).toBeInTheDocument()
    })
  })

  it('applies glassmorphism styling throughout the flow', () => {
    render(<NewResumePage />)

    // Check for glass effect classes
    const glassElements = document.querySelectorAll('.glass-panel, .glass-card, .glass-input')
    expect(glassElements.length).toBeGreaterThan(0)
  })

  it('is responsive on different screen sizes', () => {
    render(<NewResumePage />)

    // Check for responsive classes
    const responsiveElements = document.querySelectorAll('[class*="lg:"], [class*="md:"], [class*="sm:"]')
    expect(responsiveElements.length).toBeGreaterThan(0)
  })

  it('handles creation errors gracefully', async () => {
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})

    render(<NewResumePage />)

    // Complete the flow
    const selectTemplateButton = screen.getByTestId('select-modern-1')
    fireEvent.click(selectTemplateButton)

    let nextButton = screen.getByRole('button', { name: /next/i })
    fireEvent.click(nextButton)

    await waitFor(() => {
      const titleInput = screen.getByLabelText('Resume Title *')
      fireEvent.change(titleInput, { target: { value: 'Test Resume' } })
    })

    nextButton = screen.getByRole('button', { name: /next/i })
    fireEvent.click(nextButton)

    await waitFor(() => {
      const createButton = screen.getByRole('button', { name: /create my resume/i })
      fireEvent.click(createButton)
    })

    // Should handle errors and reset loading state
    await waitFor(() => {
      const createButton = screen.getByRole('button', { name: /create my resume/i })
      expect(createButton).not.toBeDisabled()
    }, { timeout: 3000 })

    consoleSpy.mockRestore()
  })
})
