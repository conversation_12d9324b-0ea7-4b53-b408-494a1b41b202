#!/bin/bash

# CareerCraft Setup Verification Script
# This script verifies that the database setup is working correctly

set -e

echo "🔍 Verifying CareerCraft Database Setup"
echo "=========================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if .env.local exists
check_env() {
    print_status "Checking environment configuration..."
    
    if [ -f ".env.local" ]; then
        print_success ".env.local file exists"
        
        # Check for required variables
        if grep -q "DATABASE_URL" .env.local; then
            print_success "DATABASE_URL is configured"
        else
            print_error "DATABASE_URL not found in .env.local"
            return 1
        fi
        
        if grep -q "NEXTAUTH_SECRET" .env.local; then
            print_success "NEXTAUTH_SECRET is configured"
        else
            print_warning "NEXTAUTH_SECRET not found in .env.local"
        fi
        
    else
        print_error ".env.local file not found"
        print_error "Please run the setup script first: ./scripts/setup.sh"
        return 1
    fi
}

# Check if dependencies are installed
check_dependencies() {
    print_status "Checking dependencies..."
    
    if [ -d "node_modules" ]; then
        print_success "Node modules are installed"
    else
        print_error "Node modules not found. Run: npm install"
        return 1
    fi
    
    # Check if Prisma client is generated
    if [ -d "packages/database/src/generated" ]; then
        print_success "Prisma client is generated"
    else
        print_warning "Prisma client not generated. Run: npm run db:generate"
    fi
}

# Test database connection
test_database() {
    print_status "Testing database connection..."
    
    # Run the database test script
    if npm run test:db > /dev/null 2>&1; then
        print_success "Database tests passed"
    else
        print_error "Database tests failed"
        print_error "Please check your database configuration and ensure PostgreSQL is running"
        return 1
    fi
}

# Test API health endpoint
test_api() {
    print_status "Testing API endpoints..."
    
    # Start the development server in background
    print_status "Starting development server..."
    npm run dev > /dev/null 2>&1 &
    DEV_PID=$!
    
    # Wait for server to start
    sleep 10
    
    # Test health endpoint
    if curl -f http://localhost:3000/api/health > /dev/null 2>&1; then
        print_success "Health endpoint is responding"
    else
        print_error "Health endpoint is not responding"
        kill $DEV_PID 2>/dev/null || true
        return 1
    fi
    
    # Stop the development server
    kill $DEV_PID 2>/dev/null || true
    sleep 2
}

# Run type checking
test_types() {
    print_status "Running TypeScript type checking..."
    
    if npm run type-check > /dev/null 2>&1; then
        print_success "TypeScript type checking passed"
    else
        print_error "TypeScript type checking failed"
        return 1
    fi
}

# Run linting
test_linting() {
    print_status "Running code linting..."
    
    if npm run lint > /dev/null 2>&1; then
        print_success "Code linting passed"
    else
        print_warning "Code linting found issues (run 'npm run lint:fix' to auto-fix)"
    fi
}

# Main verification function
main() {
    echo
    print_status "Starting verification process..."
    echo
    
    local failed=0
    
    check_env || failed=1
    check_dependencies || failed=1
    test_types || failed=1
    test_linting || failed=1
    test_database || failed=1
    
    # Skip API test if previous tests failed
    if [ $failed -eq 0 ]; then
        test_api || failed=1
    fi
    
    echo
    if [ $failed -eq 0 ]; then
        print_success "🎉 All verification checks passed!"
        echo
        print_status "Your CareerCraft setup is ready for development!"
        echo
        print_status "Next steps:"
        echo "1. Start development server: npm run dev"
        echo "2. Open browser: http://localhost:3000"
        echo "3. Check health endpoint: http://localhost:3000/api/health"
        echo "4. Open Prisma Studio: npm run db:studio"
        echo
    else
        print_error "❌ Some verification checks failed"
        echo
        print_status "Please fix the issues above and run the verification again"
        echo
        exit 1
    fi
}

# Run main function
main
