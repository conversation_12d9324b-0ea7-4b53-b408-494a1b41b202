import { describe, it, expect, beforeAll, afterAll } from '@jest/globals';
import { GET, HEAD } from '../route';
import { connectDB, disconnectDB, cleanupDatabase } from '@careercraft/database';

// Mock NextResponse for testing
jest.mock('next/server', () => ({
  NextResponse: {
    json: jest.fn((data, options) => ({
      json: async () => data,
      status: options?.status || 200,
      ok: (options?.status || 200) < 400,
    })),
  },
}));

describe('/api/health', () => {
  beforeAll(async () => {
    await connectDB();
  });

  afterAll(async () => {
    await cleanupDatabase();
    await disconnectDB();
  });

  describe('GET /api/health', () => {
    it('should return healthy status when database is connected', async () => {
      const response = await GET();
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.status).toBe('healthy');
      expect(data.timestamp).toBeDefined();
      expect(data.version).toBeDefined();
      expect(data.environment).toBeDefined();
      expect(data.services).toBeDefined();
      expect(data.services.database).toBeDefined();
      expect(data.services.database.status).toBe('healthy');
      expect(data.services.api).toBeDefined();
      expect(data.services.api.status).toBe('healthy');
      expect(data.services.api.uptime).toBeGreaterThan(0);
    });

    it('should include database connection details', async () => {
      const response = await GET();
      const data = await response.json();

      expect(data.services.database.details).toBeDefined();
      expect(data.services.database.details.connection).toBe(true);
      expect(data.services.database.details.queryTime).toBeGreaterThan(0);
      expect(data.services.database.details.version).toBeDefined();
    });

    it('should return proper content type', async () => {
      const response = await GET();
      expect(response).toBeDefined();
      // In a real environment, we would check headers
      // expect(response.headers.get('content-type')).toContain('application/json');
    });
  });

  describe('HEAD /api/health', () => {
    it('should return 200 status when healthy', async () => {
      const response = await HEAD();
      expect(response.status).toBe(200);
    });

    it('should not return body content', async () => {
      const response = await HEAD();
      // HEAD requests should not have a body
      expect(response).toBeDefined();
    });
  });

  describe('Error handling', () => {
    it('should handle database connection errors gracefully', async () => {
      // Mock a database error by temporarily breaking the connection
      const originalHealthCheck = require('@careercraft/database').healthCheck;
      
      // Mock healthCheck to throw an error
      jest.doMock('@careercraft/database', () => ({
        ...jest.requireActual('@careercraft/database'),
        healthCheck: jest.fn().mockRejectedValue(new Error('Database connection failed')),
      }));

      // Re-import the route handler with mocked dependency
      const { GET: mockedGET } = await import('../route');
      
      const response = await mockedGET();
      const data = await response.json();

      expect(response.status).toBe(503);
      expect(data.status).toBe('unhealthy');
      expect(data.error).toBeDefined();

      // Restore original implementation
      jest.doMock('@careercraft/database', () => ({
        ...jest.requireActual('@careercraft/database'),
        healthCheck: originalHealthCheck,
      }));
    });
  });

  describe('Environment information', () => {
    it('should include correct environment information', async () => {
      const response = await GET();
      const data = await response.json();

      expect(data.environment).toBe('test');
      expect(data.version).toBeDefined();
      expect(typeof data.version).toBe('string');
    });

    it('should include timestamp in ISO format', async () => {
      const response = await GET();
      const data = await response.json();

      expect(data.timestamp).toBeDefined();
      expect(() => new Date(data.timestamp)).not.toThrow();
      
      // Check if timestamp is recent (within last 5 seconds)
      const timestamp = new Date(data.timestamp);
      const now = new Date();
      const diffMs = now.getTime() - timestamp.getTime();
      expect(diffMs).toBeLessThan(5000);
    });
  });

  describe('Service status details', () => {
    it('should include API service uptime', async () => {
      const response = await GET();
      const data = await response.json();

      expect(data.services.api.uptime).toBeDefined();
      expect(typeof data.services.api.uptime).toBe('number');
      expect(data.services.api.uptime).toBeGreaterThan(0);
    });

    it('should include database query performance metrics', async () => {
      const response = await GET();
      const data = await response.json();

      expect(data.services.database.details.queryTime).toBeDefined();
      expect(typeof data.services.database.details.queryTime).toBe('number');
      expect(data.services.database.details.queryTime).toBeGreaterThan(0);
      expect(data.services.database.details.queryTime).toBeLessThan(1000); // Should be less than 1 second
    });
  });
});
