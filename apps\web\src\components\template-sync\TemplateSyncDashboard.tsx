'use client'

import { useState, useEffect } from 'react'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, <PERSON>alogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Progress } from '@/components/ui/progress'
import { Separator } from '@/components/ui/separator'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  Cloud, 
  CloudOff, 
  RefreshCw, 
  AlertTriangle, 
  CheckCircle, 
  Clock, 
  History,
  Download,
  Upload,
  Sync,
  Settings,
  FileText,
  GitBranch,
  Merge
} from 'lucide-react'
import { formatDistanceToNow } from 'date-fns'
import { toast } from 'sonner'

interface SyncStatus {
  totalTemplates: number
  syncedTemplates: number
  pendingSync: number
  syncErrors: number
  conflicts: number
  lastSyncTime: Date
}

interface TemplateConflict {
  id: string
  templateId: string
  conflictType: 'concurrent_edit' | 'version_mismatch'
  localVersion: any
  remoteVersion: any
  timestamp: Date
}

interface TemplateVersion {
  id: string
  versionNumber: number
  versionName: string
  changesSummary?: string
  createdBy: any
  createdAt: Date
}

interface TemplateSyncDashboardProps {
  className?: string
}

export function TemplateSyncDashboard({ className }: TemplateSyncDashboardProps) {
  const [syncStatus, setSyncStatus] = useState<SyncStatus | null>(null)
  const [conflicts, setConflicts] = useState<TemplateConflict[]>([])
  const [loading, setLoading] = useState(true)
  const [syncing, setSyncing] = useState(false)
  const [selectedTemplate, setSelectedTemplate] = useState<string | null>(null)
  const [versionHistory, setVersionHistory] = useState<TemplateVersion[]>([])

  useEffect(() => {
    loadSyncStatus()
    loadConflicts()
  }, [])

  const loadSyncStatus = async () => {
    try {
      const response = await fetch('/api/template-sync?action=status')
      
      if (!response.ok) {
        throw new Error('Failed to load sync status')
      }

      const data = await response.json()
      setSyncStatus({
        ...data.status,
        lastSyncTime: new Date(data.status.lastSyncTime)
      })
    } catch (error) {
      console.error('Error loading sync status:', error)
      toast.error('Failed to load sync status')
    } finally {
      setLoading(false)
    }
  }

  const loadConflicts = async () => {
    try {
      const response = await fetch('/api/template-sync?action=conflicts')
      
      if (!response.ok) {
        throw new Error('Failed to load conflicts')
      }

      const data = await response.json()
      setConflicts(data.conflicts.map((conflict: any) => ({
        ...conflict,
        timestamp: new Date(conflict.timestamp)
      })))
    } catch (error) {
      console.error('Error loading conflicts:', error)
    }
  }

  const loadVersionHistory = async (templateId: string) => {
    try {
      const response = await fetch(`/api/template-sync?action=history&templateId=${templateId}`)
      
      if (!response.ok) {
        throw new Error('Failed to load version history')
      }

      const data = await response.json()
      setVersionHistory(data.history.map((version: any) => ({
        ...version,
        createdAt: new Date(version.createdAt)
      })))
    } catch (error) {
      console.error('Error loading version history:', error)
      toast.error('Failed to load version history')
    }
  }

  const syncAllTemplates = async () => {
    try {
      setSyncing(true)
      
      const response = await fetch('/api/template-sync', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'sync' })
      })

      if (!response.ok) {
        throw new Error('Failed to sync templates')
      }

      const data = await response.json()
      
      if (data.result.errors.length > 0) {
        toast.warning(`Sync completed with ${data.result.errors.length} errors`)
      } else {
        toast.success(`Successfully synced ${data.result.syncedTemplates} templates`)
      }

      await loadSyncStatus()
      await loadConflicts()
    } catch (error) {
      console.error('Error syncing templates:', error)
      toast.error('Failed to sync templates')
    } finally {
      setSyncing(false)
    }
  }

  const resolveConflict = async (conflictId: string, resolution: string) => {
    try {
      const response = await fetch('/api/template-sync', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'resolve-conflict',
          conflictId,
          resolution
        })
      })

      if (!response.ok) {
        throw new Error('Failed to resolve conflict')
      }

      toast.success('Conflict resolved successfully')
      await loadConflicts()
      await loadSyncStatus()
    } catch (error) {
      console.error('Error resolving conflict:', error)
      toast.error('Failed to resolve conflict')
    }
  }

  const rollbackToVersion = async (templateId: string, versionId: string) => {
    try {
      const response = await fetch('/api/template-sync', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'rollback',
          templateId,
          versionId
        })
      })

      if (!response.ok) {
        throw new Error('Failed to rollback template')
      }

      toast.success('Template rolled back successfully')
      await loadVersionHistory(templateId)
    } catch (error) {
      console.error('Error rolling back template:', error)
      toast.error('Failed to rollback template')
    }
  }

  const getSyncStatusColor = () => {
    if (!syncStatus) return 'text-gray-500'
    if (syncStatus.conflicts > 0) return 'text-red-500'
    if (syncStatus.syncErrors > 0) return 'text-yellow-500'
    if (syncStatus.pendingSync > 0) return 'text-blue-500'
    return 'text-green-500'
  }

  const getSyncStatusIcon = () => {
    if (!syncStatus) return <CloudOff className="w-5 h-5" />
    if (syncStatus.conflicts > 0) return <AlertTriangle className="w-5 h-5" />
    if (syncStatus.syncErrors > 0) return <AlertTriangle className="w-5 h-5" />
    if (syncStatus.pendingSync > 0) return <Clock className="w-5 h-5" />
    return <CheckCircle className="w-5 h-5" />
  }

  const getSyncProgress = () => {
    if (!syncStatus || syncStatus.totalTemplates === 0) return 0
    return (syncStatus.syncedTemplates / syncStatus.totalTemplates) * 100
  }

  if (loading) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <div className="space-y-4">
            <div className="animate-pulse">
              <div className="h-4 bg-gray-200 rounded w-3/4 mb-2" />
              <div className="h-3 bg-gray-200 rounded w-1/2" />
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className={className}>
      {/* Sync Status Overview */}
      <Card className="mb-6">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center space-x-2">
              <Cloud className="w-5 h-5" />
              <span>Template Sync Status</span>
            </CardTitle>
            <Button
              onClick={syncAllTemplates}
              disabled={syncing}
              className="flex items-center space-x-2"
            >
              <RefreshCw className={`w-4 h-4 ${syncing ? 'animate-spin' : ''}`} />
              <span>{syncing ? 'Syncing...' : 'Sync All'}</span>
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900">
                {syncStatus?.totalTemplates || 0}
              </div>
              <div className="text-sm text-gray-600">Total Templates</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {syncStatus?.syncedTemplates || 0}
              </div>
              <div className="text-sm text-gray-600">Synced</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {syncStatus?.pendingSync || 0}
              </div>
              <div className="text-sm text-gray-600">Pending</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">
                {syncStatus?.conflicts || 0}
              </div>
              <div className="text-sm text-gray-600">Conflicts</div>
            </div>
          </div>

          <div className="space-y-4">
            <div>
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium">Sync Progress</span>
                <span className="text-sm text-gray-600">
                  {Math.round(getSyncProgress())}%
                </span>
              </div>
              <Progress value={getSyncProgress()} className="h-2" />
            </div>

            <div className="flex items-center justify-between">
              <div className={`flex items-center space-x-2 ${getSyncStatusColor()}`}>
                {getSyncStatusIcon()}
                <span className="text-sm font-medium">
                  {syncStatus?.conflicts ? 'Conflicts Need Resolution' :
                   syncStatus?.syncErrors ? 'Sync Errors Detected' :
                   syncStatus?.pendingSync ? 'Sync Pending' :
                   'All Templates Synced'}
                </span>
              </div>
              {syncStatus?.lastSyncTime && (
                <span className="text-sm text-gray-500">
                  Last sync: {formatDistanceToNow(syncStatus.lastSyncTime, { addSuffix: true })}
                </span>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Sync Management Tabs */}
      <Tabs defaultValue="conflicts" className="space-y-4">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="conflicts" className="flex items-center space-x-2">
            <AlertTriangle className="w-4 h-4" />
            <span>Conflicts ({conflicts.length})</span>
          </TabsTrigger>
          <TabsTrigger value="history" className="flex items-center space-x-2">
            <History className="w-4 h-4" />
            <span>Version History</span>
          </TabsTrigger>
          <TabsTrigger value="settings" className="flex items-center space-x-2">
            <Settings className="w-4 h-4" />
            <span>Sync Settings</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="conflicts">
          <Card>
            <CardHeader>
              <CardTitle>Sync Conflicts</CardTitle>
            </CardHeader>
            <CardContent>
              {conflicts.length === 0 ? (
                <div className="text-center py-8">
                  <CheckCircle className="w-12 h-12 mx-auto text-green-500 mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No Conflicts</h3>
                  <p className="text-gray-500">All templates are synchronized successfully.</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {conflicts.map((conflict) => (
                    <div key={conflict.id} className="p-4 border rounded-lg">
                      <div className="flex items-start justify-between mb-3">
                        <div>
                          <h4 className="font-medium text-gray-900">
                            Template Conflict
                          </h4>
                          <p className="text-sm text-gray-600">
                            {conflict.conflictType === 'concurrent_edit' 
                              ? 'Concurrent edits detected' 
                              : 'Version mismatch found'}
                          </p>
                          <p className="text-xs text-gray-500">
                            {formatDistanceToNow(conflict.timestamp, { addSuffix: true })}
                          </p>
                        </div>
                        <Badge variant="destructive">
                          {conflict.conflictType}
                        </Badge>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                        <div className="p-3 bg-blue-50 rounded">
                          <h5 className="font-medium text-blue-900 mb-2">Local Version</h5>
                          <p className="text-sm text-blue-700">
                            {conflict.localVersion.name || 'Unnamed Template'}
                          </p>
                        </div>
                        <div className="p-3 bg-green-50 rounded">
                          <h5 className="font-medium text-green-900 mb-2">Remote Version</h5>
                          <p className="text-sm text-green-700">
                            {conflict.remoteVersion.name || 'Unnamed Template'}
                          </p>
                        </div>
                      </div>

                      <div className="flex items-center space-x-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => resolveConflict(conflict.id, 'prefer_local')}
                        >
                          Keep Local
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => resolveConflict(conflict.id, 'prefer_remote')}
                        >
                          Keep Remote
                        </Button>
                        <Dialog>
                          <DialogTrigger asChild>
                            <Button size="sm">
                              <Merge className="w-4 h-4 mr-1" />
                              Manual Merge
                            </Button>
                          </DialogTrigger>
                          <DialogContent>
                            <DialogHeader>
                              <DialogTitle>Manual Merge</DialogTitle>
                              <DialogDescription>
                                Manually resolve the conflict by choosing which changes to keep.
                              </DialogDescription>
                            </DialogHeader>
                            <div className="space-y-4">
                              <p className="text-sm text-gray-600">
                                Manual merge functionality would be implemented here with a 
                                side-by-side comparison and merge editor.
                              </p>
                            </div>
                            <DialogFooter>
                              <Button
                                onClick={() => resolveConflict(conflict.id, 'manual_merge')}
                              >
                                Apply Merge
                              </Button>
                            </DialogFooter>
                          </DialogContent>
                        </Dialog>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="history">
          <Card>
            <CardHeader>
              <CardTitle>Version History</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <label className="text-sm font-medium">Select Template</label>
                  <select
                    className="w-full mt-1 p-2 border rounded-md"
                    value={selectedTemplate || ''}
                    onChange={(e) => {
                      setSelectedTemplate(e.target.value)
                      if (e.target.value) {
                        loadVersionHistory(e.target.value)
                      }
                    }}
                  >
                    <option value="">Choose a template...</option>
                    <option value="template-1">Professional Resume</option>
                    <option value="template-2">Creative Portfolio</option>
                    <option value="template-3">Modern CV</option>
                  </select>
                </div>

                {selectedTemplate && (
                  <div className="space-y-3">
                    {versionHistory.length === 0 ? (
                      <p className="text-gray-500 text-center py-4">
                        No version history available
                      </p>
                    ) : (
                      versionHistory.map((version) => (
                        <div key={version.id} className="flex items-center justify-between p-3 border rounded">
                          <div>
                            <div className="flex items-center space-x-2">
                              <GitBranch className="w-4 h-4 text-gray-500" />
                              <span className="font-medium">
                                {version.versionName}
                              </span>
                              <Badge variant="outline">
                                v{version.versionNumber}
                              </Badge>
                            </div>
                            <p className="text-sm text-gray-600 mt-1">
                              {version.changesSummary || 'No description'}
                            </p>
                            <p className="text-xs text-gray-500">
                              {formatDistanceToNow(version.createdAt, { addSuffix: true })} 
                              by {version.createdBy?.name || 'Unknown'}
                            </p>
                          </div>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => rollbackToVersion(selectedTemplate, version.id)}
                          >
                            Rollback
                          </Button>
                        </div>
                      ))
                    )}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="settings">
          <Card>
            <CardHeader>
              <CardTitle>Sync Settings</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div>
                  <h4 className="font-medium mb-3">Automatic Sync</h4>
                  <div className="space-y-2">
                    <label className="flex items-center space-x-2">
                      <input type="checkbox" defaultChecked />
                      <span className="text-sm">Enable automatic synchronization</span>
                    </label>
                    <label className="flex items-center space-x-2">
                      <input type="checkbox" defaultChecked />
                      <span className="text-sm">Sync on template save</span>
                    </label>
                    <label className="flex items-center space-x-2">
                      <input type="checkbox" />
                      <span className="text-sm">Background sync every 5 minutes</span>
                    </label>
                  </div>
                </div>

                <Separator />

                <div>
                  <h4 className="font-medium mb-3">Conflict Resolution</h4>
                  <div className="space-y-2">
                    <label className="flex items-center space-x-2">
                      <input type="radio" name="conflict" defaultChecked />
                      <span className="text-sm">Always ask for manual resolution</span>
                    </label>
                    <label className="flex items-center space-x-2">
                      <input type="radio" name="conflict" />
                      <span className="text-sm">Prefer local changes</span>
                    </label>
                    <label className="flex items-center space-x-2">
                      <input type="radio" name="conflict" />
                      <span className="text-sm">Prefer remote changes</span>
                    </label>
                  </div>
                </div>

                <Separator />

                <div>
                  <h4 className="font-medium mb-3">Storage Settings</h4>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Cloud storage provider</span>
                      <select className="p-1 border rounded text-sm">
                        <option>Local Storage</option>
                        <option>AWS S3</option>
                        <option>Google Cloud</option>
                        <option>Azure Blob</option>
                      </select>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Version retention</span>
                      <select className="p-1 border rounded text-sm">
                        <option>Keep all versions</option>
                        <option>Keep last 10 versions</option>
                        <option>Keep last 5 versions</option>
                        <option>Keep last 3 versions</option>
                      </select>
                    </div>
                  </div>
                </div>

                <div className="flex justify-end">
                  <Button>Save Settings</Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
