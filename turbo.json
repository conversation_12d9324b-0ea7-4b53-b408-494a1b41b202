{"$schema": "https://turbo.build/schema.json", "globalDependencies": ["**/.env.*local"], "pipeline": {"build": {"dependsOn": ["^build"], "outputs": [".next/**", "!.next/cache/**", "dist/**"]}, "dev": {"cache": false, "persistent": true}, "start": {"dependsOn": ["build"], "cache": false, "persistent": true}, "lint": {"dependsOn": ["^lint"], "outputs": []}, "lint:fix": {"dependsOn": ["^lint:fix"], "outputs": [], "cache": false}, "type-check": {"dependsOn": ["^type-check"], "outputs": []}, "test": {"dependsOn": ["^test"], "outputs": ["coverage/**"]}, "test:e2e": {"dependsOn": ["build"], "outputs": []}, "db:generate": {"cache": false}, "db:push": {"cache": false}, "db:migrate": {"cache": false}, "db:studio": {"cache": false, "persistent": true}, "clean": {"cache": false}}}