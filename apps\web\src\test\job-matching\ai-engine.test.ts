/**
 * AI Recommendation Engine Unit Tests
 * 
 * Tests for AI-powered job matching and recommendation algorithms
 */

import { describe, it, expect, beforeEach } from 'vitest'
import { AIRecommendationEngine } from '@/lib/job-matching/ai-engine'

describe('AIRecommendationEngine', () => {
  let aiEngine: AIRecommendationEngine

  beforeEach(() => {
    aiEngine = new AIRecommendationEngine()
  })

  describe('extractSkillsFromResume', () => {
    it('should extract skills from resume sections', async () => {
      const resumeData = {
        personalInfo: { name: '<PERSON>' },
        sections: {
          skills: [
            {
              name: 'Technical Skills',
              items: ['JavaScript', 'React', 'Node.js', 'Python']
            },
            {
              name: 'Tools',
              items: ['Git', 'Docker', 'AWS']
            }
          ],
          experience: [
            {
              position: 'Software Developer',
              company: 'Tech Corp',
              startDate: '2022-01',
              endDate: 'Present',
              description: 'Developed web applications using React and Node.js. Worked with PostgreSQL databases.'
            }
          ],
          education: [
            {
              degree: 'Bachelor of Science',
              field: 'Computer Science',
              institution: 'University of Technology'
            }
          ]
        },
        title: 'Software Developer Resume'
      }

      const result = await aiEngine.extractSkillsFromResume(resumeData)

      expect(result.skills).toBeDefined()
      expect(result.skills.length).toBeGreaterThan(0)
      
      // Check for explicitly listed skills
      const skillNames = result.skills.map(s => s.name)
      expect(skillNames).toContain('JavaScript')
      expect(skillNames).toContain('React')
      expect(skillNames).toContain('Node.js')
      expect(skillNames).toContain('Python')

      // Check skill categorization
      const jsSkill = result.skills.find(s => s.name === 'JavaScript')
      expect(jsSkill?.category).toBe('programming')
      expect(jsSkill?.confidence).toBe(0.9)

      // Check experience extraction
      expect(result.experience).toHaveLength(1)
      expect(result.experience[0].title).toBe('Software Developer')
      expect(result.experience[0].company).toBe('Tech Corp')
      expect(result.experience[0].skills).toContain('react')

      // Check education extraction
      expect(result.education).toHaveLength(1)
      expect(result.education[0].degree).toBe('Bachelor of Science')
      expect(result.education[0].field).toBe('Computer Science')
    })

    it('should handle resume with string sections', async () => {
      const resumeData = {
        personalInfo: { name: 'Jane Doe' },
        sections: JSON.stringify({
          skills: [
            { name: 'Programming', items: ['TypeScript', 'Vue.js'] }
          ]
        }),
        title: 'Frontend Developer Resume'
      }

      const result = await aiEngine.extractSkillsFromResume(resumeData)

      expect(result.skills).toBeDefined()
      const skillNames = result.skills.map(s => s.name)
      expect(skillNames).toContain('TypeScript')
      expect(skillNames).toContain('Vue.js')
    })

    it('should handle empty resume sections', async () => {
      const resumeData = {
        personalInfo: { name: 'Empty Resume' },
        sections: {},
        title: 'Empty Resume'
      }

      const result = await aiEngine.extractSkillsFromResume(resumeData)

      expect(result.skills).toEqual([])
      expect(result.experience).toEqual([])
      expect(result.education).toEqual([])
    })
  })

  describe('analyzeJobPosting', () => {
    it('should analyze job posting requirements', async () => {
      const jobPosting = {
        title: 'Senior Frontend Developer',
        company: 'Tech Startup',
        description: `We are looking for a Senior Frontend Developer to join our team. 
        You will be responsible for building user interfaces using React and TypeScript.
        Requirements: 5+ years of experience, Bachelor's degree in Computer Science.
        Benefits include health insurance, 401k, and flexible working hours.`,
        requirements: 'React, TypeScript, 5+ years experience, Computer Science degree',
        location: 'San Francisco',
        salaryMin: 120000,
        salaryMax: 160000,
        skills: ['React', 'TypeScript', 'JavaScript', 'CSS']
      }

      const result = await aiEngine.analyzeJobPosting(jobPosting)

      expect(result.requiredSkills).toBeDefined()
      expect(result.requiredSkills.length).toBeGreaterThan(0)
      expect(result.requiredSkills).toContain('react')

      expect(result.preferredSkills).toBeDefined()
      expect(result.experienceLevel).toBe('senior')

      expect(result.responsibilities).toBeDefined()
      expect(result.qualifications).toBeDefined()
      expect(result.benefits).toBeDefined()
      expect(result.benefits).toContain('health')

      expect(result.companyInfo).toBeDefined()
      expect(result.companyInfo.industry).toBeDefined()
    })

    it('should determine experience level from description', async () => {
      const entryLevelJob = {
        title: 'Junior Developer',
        company: 'Company',
        description: 'Entry level position for new graduates. 0-2 years experience required.',
        location: 'Remote'
      }

      const result = await aiEngine.analyzeJobPosting(entryLevelJob)
      expect(result.experienceLevel).toBe('entry')
    })

    it('should extract company information', async () => {
      const jobPosting = {
        title: 'Developer',
        company: 'Healthcare Solutions Inc',
        description: 'Join our innovative healthcare technology startup. We are a small, fast-paced team.',
        location: 'Boston'
      }

      const result = await aiEngine.analyzeJobPosting(jobPosting)
      expect(result.companyInfo.industry).toBe('healthcare')
      expect(result.companyInfo.size).toBe('small')
    })
  })

  describe('analyzeJobCompatibility', () => {
    it('should analyze compatibility between resume and job', async () => {
      const resume = {
        personalInfo: { name: 'John Doe' },
        sections: {
          skills: [
            { name: 'Programming', items: ['JavaScript', 'React', 'Node.js'] }
          ],
          experience: [
            {
              position: 'Software Developer',
              company: 'Tech Corp',
              startDate: '2020-01',
              endDate: 'Present',
              description: 'Built web applications'
            }
          ],
          education: [
            {
              degree: 'Bachelor of Science',
              field: 'Computer Science',
              institution: 'University'
            }
          ]
        },
        title: 'Software Developer Resume'
      }

      const jobPosting = {
        title: 'Frontend Developer',
        company: 'Startup',
        description: 'Looking for a React developer with JavaScript experience',
        requirements: 'React, JavaScript, 2+ years experience',
        location: 'San Francisco',
        salaryMin: 80000,
        salaryMax: 120000,
        skills: ['React', 'JavaScript', 'CSS']
      }

      const result = await aiEngine.analyzeJobCompatibility(resume, jobPosting)

      expect(result.overallScore).toBeGreaterThan(0)
      expect(result.overallScore).toBeLessThanOrEqual(100)

      expect(result.skillsScore).toBeGreaterThan(0)
      expect(result.experienceScore).toBeGreaterThan(0)
      expect(result.educationScore).toBeGreaterThan(0)

      expect(result.strengths).toBeDefined()
      expect(result.weaknesses).toBeDefined()
      expect(result.recommendations).toBeDefined()

      expect(Array.isArray(result.strengths)).toBe(true)
      expect(Array.isArray(result.weaknesses)).toBe(true)
      expect(Array.isArray(result.recommendations)).toBe(true)
    })

    it('should provide recommendations for skill gaps', async () => {
      const resume = {
        personalInfo: { name: 'John Doe' },
        sections: {
          skills: [
            { name: 'Programming', items: ['JavaScript'] }
          ]
        },
        title: 'Junior Developer Resume'
      }

      const jobPosting = {
        title: 'Senior React Developer',
        company: 'Company',
        description: 'Need expert React and TypeScript skills',
        requirements: 'React, TypeScript, Redux, 5+ years experience',
        skills: ['React', 'TypeScript', 'Redux']
      }

      const result = await aiEngine.analyzeJobCompatibility(resume, jobPosting)

      expect(result.skillsScore).toBeLessThan(70)
      expect(result.weaknesses.length).toBeGreaterThan(0)
      expect(result.recommendations.length).toBeGreaterThan(0)
      
      const hasSkillRecommendation = result.recommendations.some(rec => 
        rec.toLowerCase().includes('skill')
      )
      expect(hasSkillRecommendation).toBe(true)
    })
  })

  describe('predictCareerPath', () => {
    it('should predict next career roles', async () => {
      const userProfile = {
        currentTitle: 'Software Developer',
        skills: ['JavaScript', 'React', 'Node.js'],
        experience: [
          { title: 'Junior Developer', duration: '2 years' },
          { title: 'Software Developer', duration: '1 year' }
        ]
      }

      const result = await aiEngine.predictCareerPath(userProfile)

      expect(result.nextRoles).toBeDefined()
      expect(result.nextRoles.length).toBeGreaterThan(0)
      
      const nextRole = result.nextRoles[0]
      expect(nextRole.title).toBeDefined()
      expect(nextRole.probability).toBeGreaterThan(0)
      expect(nextRole.timeframe).toBeDefined()
      expect(nextRole.requiredSkills).toBeDefined()
      expect(nextRole.salaryRange).toBeDefined()

      expect(result.skillGaps).toBeDefined()
      expect(result.marketTrends).toBeDefined()
    })

    it('should identify skill gaps for career progression', async () => {
      const userProfile = {
        currentTitle: 'Data Analyst',
        skills: ['Excel', 'SQL'],
        experience: []
      }

      const result = await aiEngine.predictCareerPath(userProfile)

      expect(result.skillGaps.length).toBeGreaterThan(0)
      
      const skillGap = result.skillGaps[0]
      expect(skillGap.skill).toBeDefined()
      expect(skillGap.importance).toBeGreaterThan(0)
      expect(skillGap.learningResources).toBeDefined()
    })
  })

  describe('generateInterviewQuestions', () => {
    it('should generate relevant interview questions', async () => {
      const jobPosting = {
        title: 'Frontend Developer',
        company: 'Tech Company',
        description: 'Build user interfaces with React and TypeScript',
        requirements: 'React, TypeScript, 3+ years experience',
        skills: ['React', 'TypeScript', 'JavaScript']
      }

      const userProfile = {
        skills: ['React', 'JavaScript'],
        experience: ['Frontend Developer']
      }

      const questions = await aiEngine.generateInterviewQuestions(jobPosting, userProfile)

      expect(questions).toBeDefined()
      expect(questions.length).toBeGreaterThan(0)
      expect(Array.isArray(questions)).toBe(true)

      // Should include general questions
      const hasGeneralQuestion = questions.some(q => 
        q.toLowerCase().includes('experience') || q.toLowerCase().includes('interested')
      )
      expect(hasGeneralQuestion).toBe(true)

      // Should include skill-specific questions
      const hasSkillQuestion = questions.some(q => 
        q.toLowerCase().includes('react') || q.toLowerCase().includes('typescript')
      )
      expect(hasSkillQuestion).toBe(true)

      // Should include behavioral questions
      const hasBehavioralQuestion = questions.some(q => 
        q.toLowerCase().includes('describe') || q.toLowerCase().includes('tell me about')
      )
      expect(hasBehavioralQuestion).toBe(true)
    })

    it('should customize questions based on job requirements', async () => {
      const jobPosting = {
        title: 'Senior Backend Engineer',
        company: 'Enterprise Corp',
        description: 'Design scalable backend systems using Python and AWS',
        requirements: 'Python, AWS, microservices, 5+ years experience',
        skills: ['Python', 'AWS', 'Docker', 'Kubernetes']
      }

      const questions = await aiEngine.generateInterviewQuestions(jobPosting, {})

      expect(questions.length).toBeGreaterThan(0)
      
      const hasPythonQuestion = questions.some(q => 
        q.toLowerCase().includes('python')
      )
      expect(hasPythonQuestion).toBe(true)
    })
  })

  describe('skill categorization', () => {
    it('should categorize programming skills correctly', () => {
      const engine = new AIRecommendationEngine()
      
      // Access private method for testing (in real implementation, this would be tested indirectly)
      const categorizeSkill = (engine as any).categorizeSkill.bind(engine)
      
      expect(categorizeSkill('JavaScript')).toBe('programming')
      expect(categorizeSkill('Python')).toBe('programming')
      expect(categorizeSkill('React')).toBe('web')
      expect(categorizeSkill('AWS')).toBe('cloud')
      expect(categorizeSkill('Figma')).toBe('design')
      expect(categorizeSkill('Unknown Skill')).toBe('other')
    })
  })

  describe('string similarity', () => {
    it('should calculate string similarity correctly', () => {
      const engine = new AIRecommendationEngine()
      
      // Access private method for testing
      const calculateStringSimilarity = (engine as any).calculateStringSimilarity.bind(engine)
      
      expect(calculateStringSimilarity('react', 'react')).toBe(1.0)
      expect(calculateStringSimilarity('react', 'React')).toBeGreaterThan(0.8)
      expect(calculateStringSimilarity('javascript', 'java')).toBeLessThan(0.8)
      expect(calculateStringSimilarity('', '')).toBe(1.0)
    })
  })

  describe('experience level determination', () => {
    it('should determine experience level from job description', () => {
      const engine = new AIRecommendationEngine()
      
      // Access private method for testing
      const determineExperienceLevel = (engine as any).determineExperienceLevel.bind(engine)
      
      expect(determineExperienceLevel('Entry level position for new graduates')).toBe('entry')
      expect(determineExperienceLevel('Junior developer with 0-2 years experience')).toBe('entry')
      expect(determineExperienceLevel('Senior developer with 5+ years experience')).toBe('senior')
      expect(determineExperienceLevel('Lead engineer position')).toBe('lead')
      expect(determineExperienceLevel('Mid-level developer role')).toBe('mid')
    })
  })

  describe('skills extraction from text', () => {
    it('should extract skills from job description text', () => {
      const engine = new AIRecommendationEngine()
      
      // Access private method for testing
      const extractSkillsFromText = (engine as any).extractSkillsFromText.bind(engine)
      
      const text = 'We need someone with React, Node.js, and Python experience. Knowledge of AWS and Docker is preferred.'
      const skills = extractSkillsFromText(text)
      
      expect(skills).toContain('react')
      expect(skills).toContain('node.js')
      expect(skills).toContain('python')
      expect(skills).toContain('aws')
      expect(skills).toContain('docker')
    })

    it('should handle empty text', () => {
      const engine = new AIRecommendationEngine()
      const extractSkillsFromText = (engine as any).extractSkillsFromText.bind(engine)
      
      const skills = extractSkillsFromText('')
      expect(Array.isArray(skills)).toBe(true)
    })
  })

  describe('salary score calculation', () => {
    it('should calculate salary compatibility score', () => {
      const engine = new AIRecommendationEngine()
      
      // Access private method for testing
      const calculateSalaryScore = (engine as any).calculateSalaryScore.bind(engine)
      
      // For now, this returns a fixed score, but in a real implementation
      // it would consider user salary expectations
      const score = calculateSalaryScore(80000, 120000)
      expect(score).toBe(80)
    })
  })

  describe('education score calculation', () => {
    it('should calculate education relevance score', () => {
      const engine = new AIRecommendationEngine()
      
      // Access private method for testing
      const calculateEducationScore = (engine as any).calculateEducationScore.bind(engine)
      
      const userEducation = [
        { degree: 'Bachelor of Science', field: 'Computer Science', institution: 'University' }
      ]
      
      const qualifications = [
        'Bachelor degree in Computer Science or related field',
        'Strong analytical skills'
      ]
      
      const score = calculateEducationScore(userEducation, qualifications)
      expect(score).toBe(90) // Should match relevant education
      
      const noEducationScore = calculateEducationScore([], qualifications)
      expect(noEducationScore).toBe(50) // Neutral score for no education
    })
  })
})
