/**
 * ATS Optimization Service
 * 
 * This service analyzes resumes for ATS compatibility and provides
 * optimization recommendations to improve parsing and ranking.
 */

import {
  ATSOptimizationData,
  ATSIssue,
  ATSRecommendation,
  ATSIssueType,
  IssueSeverity,
  RecommendationType,
  EffortLevel,
  KeywordAnalysis,
  ExtractedKeyword,
  KeywordCategory,
  KeywordRecommendation,
} from '@careercraft/shared/types/ai';
import { Resume } from '@careercraft/shared/types/resume';
import { nanoid } from 'nanoid';

export class ATSOptimizer {
  private commonActionVerbs = [
    'achieved', 'administered', 'analyzed', 'built', 'collaborated', 'created',
    'delivered', 'developed', 'enhanced', 'established', 'executed', 'generated',
    'implemented', 'improved', 'increased', 'led', 'managed', 'optimized',
    'organized', 'reduced', 'resolved', 'streamlined', 'supervised', 'transformed',
  ];

  private industryKeywords: Record<string, string[]> = {
    technology: [
      'software', 'development', 'programming', 'coding', 'algorithms', 'databases',
      'cloud', 'api', 'frontend', 'backend', 'fullstack', 'devops', 'agile', 'scrum',
    ],
    marketing: [
      'campaigns', 'branding', 'analytics', 'conversion', 'engagement', 'roi',
      'social media', 'content', 'seo', 'sem', 'digital marketing', 'lead generation',
    ],
    finance: [
      'financial analysis', 'budgeting', 'forecasting', 'risk management', 'compliance',
      'accounting', 'auditing', 'investment', 'portfolio', 'financial modeling',
    ],
    healthcare: [
      'patient care', 'clinical', 'medical', 'healthcare', 'treatment', 'diagnosis',
      'nursing', 'pharmacy', 'medical records', 'hipaa', 'quality assurance',
    ],
  };

  async analyzeResume(resume: Resume, jobDescription?: string): Promise<ATSOptimizationData> {
    const issues = await this.identifyIssues(resume);
    const keywordAnalysis = await this.analyzeKeywords(resume, jobDescription);
    const recommendations = await this.generateRecommendations(issues, keywordAnalysis);

    const score = this.calculateATSScore(issues, keywordAnalysis);
    const keywordMatch = this.calculateKeywordMatch(keywordAnalysis, jobDescription);
    const formatCompliance = this.calculateFormatCompliance(resume);
    const readabilityScore = this.calculateReadabilityScore(resume);

    return {
      score,
      issues,
      recommendations,
      keywordMatch,
      formatCompliance,
      readabilityScore,
    };
  }

  private async identifyIssues(resume: Resume): Promise<ATSIssue[]> {
    const issues: ATSIssue[] = [];

    // Check for missing keywords
    const content = this.extractResumeText(resume);
    const actionVerbCount = this.countActionVerbs(content);
    
    if (actionVerbCount < 5) {
      issues.push({
        id: nanoid(),
        type: ATSIssueType.WEAK_ACTION_VERBS,
        severity: IssueSeverity.MEDIUM,
        description: 'Resume contains few strong action verbs',
        location: 'Work Experience',
        suggestion: 'Use more powerful action verbs like "achieved", "implemented", "optimized"',
        impact: 15,
      });
    }

    // Check for quantifiable achievements
    const quantifiableCount = this.countQuantifiableAchievements(content);
    if (quantifiableCount < 3) {
      issues.push({
        id: nanoid(),
        type: ATSIssueType.MISSING_KEYWORDS,
        severity: IssueSeverity.HIGH,
        description: 'Few quantifiable achievements found',
        location: 'Work Experience',
        suggestion: 'Add specific numbers, percentages, and metrics to demonstrate impact',
        impact: 25,
      });
    }

    // Check for consistent date formatting
    if (!this.hasConsistentDateFormatting(resume)) {
      issues.push({
        id: nanoid(),
        type: ATSIssueType.INCONSISTENT_DATES,
        severity: IssueSeverity.LOW,
        description: 'Inconsistent date formatting detected',
        location: 'Work Experience, Education',
        suggestion: 'Use consistent date format throughout (e.g., "Jan 2020 - Dec 2022")',
        impact: 10,
      });
    }

    // Check for special characters
    if (this.hasProblematicCharacters(content)) {
      issues.push({
        id: nanoid(),
        type: ATSIssueType.SPECIAL_CHARACTERS,
        severity: IssueSeverity.MEDIUM,
        description: 'Special characters that may cause parsing issues',
        location: 'Throughout resume',
        suggestion: 'Replace special characters with standard text',
        impact: 20,
      });
    }

    // Check sentence length
    const longSentences = this.findLongSentences(content);
    if (longSentences > 3) {
      issues.push({
        id: nanoid(),
        type: ATSIssueType.LONG_SENTENCES,
        severity: IssueSeverity.LOW,
        description: 'Multiple sentences exceed recommended length',
        location: 'Professional Summary, Work Experience',
        suggestion: 'Break long sentences into shorter, more readable ones',
        impact: 12,
      });
    }

    return issues;
  }

  private async analyzeKeywords(resume: Resume, jobDescription?: string): Promise<KeywordAnalysis> {
    const content = this.extractResumeText(resume);
    const extractedKeywords = this.extractKeywords(content);
    const industryKeywords = this.getIndustryKeywords(resume);
    const actionVerbs = this.extractActionVerbs(content);
    
    let missingKeywords: string[] = [];
    let recommendations: KeywordRecommendation[] = [];

    if (jobDescription) {
      const jobKeywords = this.extractKeywords(jobDescription);
      missingKeywords = this.findMissingKeywords(extractedKeywords, jobKeywords);
      recommendations = this.generateKeywordRecommendations(missingKeywords, extractedKeywords);
    }

    const keywordDensity = this.calculateKeywordDensity(content, extractedKeywords);
    const skillKeywords = extractedKeywords.filter(k => k.category === KeywordCategory.SKILL);

    return {
      extractedKeywords,
      missingKeywords,
      keywordDensity,
      industryKeywords,
      skillKeywords: skillKeywords.map(k => k.keyword),
      actionVerbs,
      recommendations,
    };
  }

  private extractResumeText(resume: Resume): string {
    let text = '';
    
    // Extract personal info
    const { personalInfo } = resume;
    text += `${personalInfo.firstName} ${personalInfo.lastName} `;
    if (personalInfo.summary) text += personalInfo.summary + ' ';

    // Extract work experience
    resume.sections.forEach(section => {
      if (section.data && Array.isArray(section.data)) {
        section.data.forEach((item: any) => {
          if (item.description) text += item.description + ' ';
          if (item.achievements) {
            item.achievements.forEach((achievement: string) => {
              text += achievement + ' ';
            });
          }
          if (item.technologies) {
            item.technologies.forEach((tech: string) => {
              text += tech + ' ';
            });
          }
        });
      }
    });

    return text.toLowerCase();
  }

  private extractKeywords(text: string): ExtractedKeyword[] {
    const words = text.toLowerCase().match(/\b\w+\b/g) || [];
    const wordFreq: Record<string, number> = {};
    
    words.forEach(word => {
      if (word.length > 2) {
        wordFreq[word] = (wordFreq[word] || 0) + 1;
      }
    });

    return Object.entries(wordFreq)
      .filter(([word, freq]) => freq > 1)
      .map(([word, freq]) => ({
        keyword: word,
        frequency: freq,
        relevance: this.calculateRelevance(word),
        category: this.categorizeKeyword(word),
        importance: this.calculateImportance(word, freq),
      }))
      .sort((a, b) => b.importance - a.importance)
      .slice(0, 30);
  }

  private categorizeKeyword(keyword: string): KeywordCategory {
    const techKeywords = ['javascript', 'python', 'react', 'node', 'sql', 'aws', 'docker'];
    const skillKeywords = ['leadership', 'communication', 'problem-solving', 'teamwork'];
    const actionVerbs = this.commonActionVerbs;

    if (techKeywords.includes(keyword)) return KeywordCategory.TECHNOLOGY;
    if (skillKeywords.includes(keyword)) return KeywordCategory.SOFT_SKILL;
    if (actionVerbs.includes(keyword)) return KeywordCategory.ACTION_VERB;
    
    return KeywordCategory.SKILL;
  }

  private calculateRelevance(keyword: string): number {
    // Simple relevance calculation based on keyword importance
    const importantKeywords = [
      'management', 'leadership', 'development', 'analysis', 'strategy',
      'implementation', 'optimization', 'collaboration', 'innovation',
    ];
    
    return importantKeywords.includes(keyword) ? 0.9 : 0.6;
  }

  private calculateImportance(keyword: string, frequency: number): number {
    const relevance = this.calculateRelevance(keyword);
    return relevance * Math.log(frequency + 1);
  }

  private countActionVerbs(content: string): number {
    return this.commonActionVerbs.filter(verb => 
      content.includes(verb)
    ).length;
  }

  private countQuantifiableAchievements(content: string): number {
    const numberPattern = /\d+[%$]?|\$\d+|\d+\+/g;
    const matches = content.match(numberPattern) || [];
    return matches.length;
  }

  private hasConsistentDateFormatting(resume: Resume): boolean {
    // Simplified check - in reality would be more sophisticated
    return true;
  }

  private hasProblematicCharacters(content: string): boolean {
    const problematicChars = /[•◦▪▫‣⁃]/g;
    return problematicChars.test(content);
  }

  private findLongSentences(content: string): number {
    const sentences = content.split(/[.!?]+/);
    return sentences.filter(sentence => sentence.split(' ').length > 25).length;
  }

  private extractActionVerbs(content: string): string[] {
    return this.commonActionVerbs.filter(verb => content.includes(verb));
  }

  private getIndustryKeywords(resume: Resume): string[] {
    // Simplified - would analyze resume content to determine industry
    return this.industryKeywords.technology || [];
  }

  private findMissingKeywords(resumeKeywords: ExtractedKeyword[], jobKeywords: ExtractedKeyword[]): string[] {
    const resumeKeywordSet = new Set(resumeKeywords.map(k => k.keyword));
    return jobKeywords
      .filter(k => !resumeKeywordSet.has(k.keyword) && k.importance > 0.5)
      .map(k => k.keyword)
      .slice(0, 10);
  }

  private generateKeywordRecommendations(
    missingKeywords: string[],
    existingKeywords: ExtractedKeyword[]
  ): KeywordRecommendation[] {
    return missingKeywords.map(keyword => ({
      keyword,
      category: this.categorizeKeyword(keyword),
      priority: Math.floor(Math.random() * 10) + 1,
      reasoning: `This keyword appears frequently in the target job description`,
      suggestedPlacement: ['Professional Summary', 'Work Experience'],
      alternatives: this.getKeywordAlternatives(keyword),
    }));
  }

  private getKeywordAlternatives(keyword: string): string[] {
    const alternatives: Record<string, string[]> = {
      'management': ['leadership', 'supervision', 'oversight'],
      'development': ['creation', 'building', 'implementation'],
      'analysis': ['evaluation', 'assessment', 'examination'],
    };
    
    return alternatives[keyword] || [];
  }

  private calculateKeywordDensity(content: string, keywords: ExtractedKeyword[]): Record<string, number> {
    const totalWords = content.split(/\s+/).length;
    const density: Record<string, number> = {};
    
    keywords.forEach(keyword => {
      density[keyword.keyword] = (keyword.frequency / totalWords) * 100;
    });
    
    return density;
  }

  private async generateRecommendations(
    issues: ATSIssue[],
    keywordAnalysis: KeywordAnalysis
  ): Promise<ATSRecommendation[]> {
    const recommendations: ATSRecommendation[] = [];

    // Generate recommendations based on issues
    issues.forEach(issue => {
      recommendations.push({
        id: nanoid(),
        type: this.mapIssueToRecommendationType(issue.type),
        priority: this.calculatePriority(issue.severity, issue.impact),
        title: `Fix ${issue.type.replace('_', ' ')}`,
        description: issue.suggestion,
        implementation: this.getImplementationGuidance(issue.type),
        expectedImpact: issue.impact,
        effort: this.getEffortLevel(issue.type),
      });
    });

    // Generate keyword recommendations
    keywordAnalysis.recommendations.forEach(keywordRec => {
      recommendations.push({
        id: nanoid(),
        type: RecommendationType.KEYWORD_OPTIMIZATION,
        priority: keywordRec.priority,
        title: `Add keyword: ${keywordRec.keyword}`,
        description: keywordRec.reasoning,
        implementation: `Consider adding "${keywordRec.keyword}" to ${keywordRec.suggestedPlacement.join(' or ')}`,
        expectedImpact: 15,
        effort: EffortLevel.LOW,
      });
    });

    return recommendations.sort((a, b) => b.priority - a.priority).slice(0, 10);
  }

  private mapIssueToRecommendationType(issueType: ATSIssueType): RecommendationType {
    switch (issueType) {
      case ATSIssueType.MISSING_KEYWORDS:
        return RecommendationType.KEYWORD_OPTIMIZATION;
      case ATSIssueType.POOR_FORMATTING:
        return RecommendationType.FORMAT_ENHANCEMENT;
      case ATSIssueType.COMPLEX_LANGUAGE:
        return RecommendationType.LANGUAGE_SIMPLIFICATION;
      default:
        return RecommendationType.CONTENT_IMPROVEMENT;
    }
  }

  private calculatePriority(severity: IssueSeverity, impact: number): number {
    const severityWeight = {
      [IssueSeverity.CRITICAL]: 10,
      [IssueSeverity.HIGH]: 8,
      [IssueSeverity.MEDIUM]: 6,
      [IssueSeverity.LOW]: 4,
    };
    
    return Math.min(10, severityWeight[severity] + Math.floor(impact / 10));
  }

  private getImplementationGuidance(issueType: ATSIssueType): string {
    const guidance: Record<ATSIssueType, string> = {
      [ATSIssueType.WEAK_ACTION_VERBS]: 'Replace weak verbs with strong action verbs like "achieved", "implemented", "optimized"',
      [ATSIssueType.MISSING_KEYWORDS]: 'Research job descriptions and incorporate relevant industry keywords naturally',
      [ATSIssueType.POOR_FORMATTING]: 'Use standard fonts, clear headings, and consistent formatting throughout',
      [ATSIssueType.COMPLEX_LANGUAGE]: 'Simplify complex sentences and use clear, direct language',
      [ATSIssueType.INCONSISTENT_DATES]: 'Use consistent date format (e.g., "Jan 2020 - Dec 2022") throughout',
      [ATSIssueType.SPECIAL_CHARACTERS]: 'Replace special bullets and symbols with standard text',
      [ATSIssueType.LONG_SENTENCES]: 'Break long sentences into shorter, more readable ones',
      [ATSIssueType.MISSING_SECTIONS]: 'Add missing sections like Skills, Education, or Certifications',
    };
    
    return guidance[issueType] || 'Review and improve this section';
  }

  private getEffortLevel(issueType: ATSIssueType): EffortLevel {
    const effortMap: Record<ATSIssueType, EffortLevel> = {
      [ATSIssueType.WEAK_ACTION_VERBS]: EffortLevel.LOW,
      [ATSIssueType.MISSING_KEYWORDS]: EffortLevel.MEDIUM,
      [ATSIssueType.POOR_FORMATTING]: EffortLevel.LOW,
      [ATSIssueType.COMPLEX_LANGUAGE]: EffortLevel.MEDIUM,
      [ATSIssueType.INCONSISTENT_DATES]: EffortLevel.LOW,
      [ATSIssueType.SPECIAL_CHARACTERS]: EffortLevel.LOW,
      [ATSIssueType.LONG_SENTENCES]: EffortLevel.MEDIUM,
      [ATSIssueType.MISSING_SECTIONS]: EffortLevel.HIGH,
    };
    
    return effortMap[issueType] || EffortLevel.MEDIUM;
  }

  private calculateATSScore(issues: ATSIssue[], keywordAnalysis: KeywordAnalysis): number {
    let score = 100;
    
    // Deduct points for issues
    issues.forEach(issue => {
      score -= issue.impact;
    });
    
    // Adjust for keyword coverage
    const keywordScore = Math.min(100, keywordAnalysis.extractedKeywords.length * 3);
    score = (score + keywordScore) / 2;
    
    return Math.max(0, Math.min(100, Math.round(score)));
  }

  private calculateKeywordMatch(keywordAnalysis: KeywordAnalysis, jobDescription?: string): number {
    if (!jobDescription) return 75; // Default score when no job description
    
    const jobKeywords = this.extractKeywords(jobDescription);
    const resumeKeywordSet = new Set(keywordAnalysis.extractedKeywords.map(k => k.keyword));
    
    const matchCount = jobKeywords.filter(k => resumeKeywordSet.has(k.keyword)).length;
    return Math.round((matchCount / Math.max(jobKeywords.length, 1)) * 100);
  }

  private calculateFormatCompliance(resume: Resume): number {
    // Simplified format compliance check
    let score = 100;
    
    // Check for required sections
    const requiredSections = ['work_experience', 'education'];
    const existingSections = resume.sections.map(s => s.type);
    
    requiredSections.forEach(section => {
      if (!existingSections.includes(section as any)) {
        score -= 20;
      }
    });
    
    return Math.max(0, score);
  }

  private calculateReadabilityScore(resume: Resume): number {
    const content = this.extractResumeText(resume);
    const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 0);
    const words = content.split(/\s+/).filter(w => w.length > 0);
    
    if (sentences.length === 0 || words.length === 0) return 0;
    
    const avgWordsPerSentence = words.length / sentences.length;
    const avgSyllablesPerWord = 1.5; // Simplified calculation
    
    // Simplified Flesch Reading Ease formula
    const score = 206.835 - (1.015 * avgWordsPerSentence) - (84.6 * avgSyllablesPerWord);
    
    return Math.max(0, Math.min(100, Math.round(score)));
  }
}
