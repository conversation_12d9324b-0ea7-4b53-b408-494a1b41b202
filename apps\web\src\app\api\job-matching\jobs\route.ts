/**
 * Job Matching API - Jobs
 * 
 * Handles job search, discovery, and management operations
 */

import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { jobMatchingService, JobSearchCriteriaSchema } from '@/lib/job-matching/service'
import { aiRecommendationEngine } from '@/lib/job-matching/ai-engine'
import { z } from 'zod'

// Request schemas
const SearchJobsSchema = z.object({
  query: z.string().optional(),
  location: z.string().optional(),
  remoteType: z.enum(['remote', 'hybrid', 'on-site']).optional(),
  employmentType: z.enum(['full-time', 'part-time', 'contract', 'internship']).optional(),
  experienceLevel: z.enum(['entry', 'mid', 'senior', 'executive']).optional(),
  salaryMin: z.number().optional(),
  salaryMax: z.number().optional(),
  skills: z.array(z.string()).optional(),
  companies: z.array(z.string()).optional(),
  limit: z.number().default(20),
  offset: z.number().default(0)
})

const AnalyzeJobSchema = z.object({
  jobId: z.string(),
  resumeId: z.string().optional()
})

const GetRecommendationsSchema = z.object({
  limit: z.number().default(10),
  refresh: z.boolean().default(false)
})

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const action = searchParams.get('action')

    if (action === 'search') {
      const searchCriteria = {
        query: searchParams.get('query') || undefined,
        location: searchParams.get('location') || undefined,
        remoteType: searchParams.get('remoteType') as any || undefined,
        employmentType: searchParams.get('employmentType') as any || undefined,
        experienceLevel: searchParams.get('experienceLevel') as any || undefined,
        salaryMin: searchParams.get('salaryMin') ? parseInt(searchParams.get('salaryMin')!) : undefined,
        salaryMax: searchParams.get('salaryMax') ? parseInt(searchParams.get('salaryMax')!) : undefined,
        skills: searchParams.get('skills') ? searchParams.get('skills')!.split(',') : undefined,
        companies: searchParams.get('companies') ? searchParams.get('companies')!.split(',') : undefined,
        limit: parseInt(searchParams.get('limit') || '20'),
        offset: parseInt(searchParams.get('offset') || '0')
      }

      const jobs = await jobMatchingService.searchJobs(searchCriteria)

      return NextResponse.json({
        success: true,
        jobs,
        total: jobs.length,
        hasMore: jobs.length === searchCriteria.limit
      })
    }

    if (action === 'recommendations') {
      const limit = parseInt(searchParams.get('limit') || '10')
      const refresh = searchParams.get('refresh') === 'true'

      if (refresh) {
        // Generate new recommendations
        await jobMatchingService.generateRecommendations(session.user.id)
      }

      const recommendations = await jobMatchingService.getRecommendations(session.user.id, limit)

      return NextResponse.json({
        success: true,
        recommendations
      })
    }

    if (action === 'trending') {
      // Mock trending jobs for now
      const trendingJobs = await jobMatchingService.searchJobs({
        limit: 10,
        offset: 0
      })

      return NextResponse.json({
        success: true,
        jobs: trendingJobs
      })
    }

    if (action === 'similar') {
      const jobId = searchParams.get('jobId')
      if (!jobId) {
        return NextResponse.json(
          { error: 'Job ID required' },
          { status: 400 }
        )
      }

      // Find similar jobs based on skills and company
      const similarJobs = await jobMatchingService.searchJobs({
        limit: 5,
        offset: 0
      })

      return NextResponse.json({
        success: true,
        jobs: similarJobs
      })
    }

    return NextResponse.json(
      { error: 'Invalid action' },
      { status: 400 }
    )
  } catch (error) {
    console.error('Job search error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const action = body.action

    if (action === 'analyze') {
      const { jobId, resumeId } = AnalyzeJobSchema.parse(body)

      // Get job posting
      const jobs = await jobMatchingService.searchJobs({ limit: 1000 })
      const job = jobs.find(j => j.id === jobId)

      if (!job) {
        return NextResponse.json(
          { error: 'Job not found' },
          { status: 404 }
        )
      }

      // Get user's resume if specified
      let resume = null
      if (resumeId) {
        // This would fetch the resume from the database
        // For now, we'll use a mock resume
        resume = {
          personalInfo: { name: 'John Doe' },
          sections: { skills: [], experience: [] },
          title: 'Software Developer Resume'
        }
      }

      // Analyze job posting
      const jobAnalysis = await aiRecommendationEngine.analyzeJobPosting({
        title: job.title,
        company: job.company,
        description: job.description,
        requirements: job.requirements,
        location: job.location,
        salaryMin: job.salaryMin,
        salaryMax: job.salaryMax,
        skills: job.skills
      })

      let compatibilityAnalysis = null
      if (resume) {
        compatibilityAnalysis = await aiRecommendationEngine.analyzeJobCompatibility(resume, {
          title: job.title,
          company: job.company,
          description: job.description,
          requirements: job.requirements,
          location: job.location,
          salaryMin: job.salaryMin,
          salaryMax: job.salaryMax,
          skills: job.skills
        })
      }

      return NextResponse.json({
        success: true,
        job,
        analysis: jobAnalysis,
        compatibility: compatibilityAnalysis
      })
    }

    if (action === 'generate-questions') {
      const { jobId } = body

      const jobs = await jobMatchingService.searchJobs({ limit: 1000 })
      const job = jobs.find(j => j.id === jobId)

      if (!job) {
        return NextResponse.json(
          { error: 'Job not found' },
          { status: 404 }
        )
      }

      const questions = await aiRecommendationEngine.generateInterviewQuestions({
        title: job.title,
        company: job.company,
        description: job.description,
        requirements: job.requirements,
        location: job.location,
        salaryMin: job.salaryMin,
        salaryMax: job.salaryMax,
        skills: job.skills
      }, {})

      return NextResponse.json({
        success: true,
        questions
      })
    }

    if (action === 'calculate-match') {
      const { jobId } = body

      const matchScore = await jobMatchingService.calculateMatchScore(session.user.id, jobId)

      return NextResponse.json({
        success: true,
        matchScore
      })
    }

    return NextResponse.json(
      { error: 'Invalid action' },
      { status: 400 }
    )
  } catch (error) {
    console.error('Job analysis error:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const action = body.action

    if (action === 'update-recommendation') {
      const { recommendationId, status } = body

      if (!['save', 'dismiss'].includes(status)) {
        return NextResponse.json(
          { error: 'Invalid status' },
          { status: 400 }
        )
      }

      const success = await jobMatchingService.updateRecommendationStatus(
        recommendationId,
        session.user.id,
        status
      )

      if (!success) {
        return NextResponse.json(
          { error: 'Recommendation not found or access denied' },
          { status: 404 }
        )
      }

      return NextResponse.json({
        success: true,
        message: `Recommendation ${status}d successfully`
      })
    }

    if (action === 'mark-viewed') {
      const { recommendationId } = body

      const success = await jobMatchingService.markRecommendationViewed(
        recommendationId,
        session.user.id
      )

      if (!success) {
        return NextResponse.json(
          { error: 'Recommendation not found or access denied' },
          { status: 404 }
        )
      }

      return NextResponse.json({
        success: true,
        message: 'Recommendation marked as viewed'
      })
    }

    return NextResponse.json(
      { error: 'Invalid action' },
      { status: 400 }
    )
  } catch (error) {
    console.error('Job recommendation update error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
