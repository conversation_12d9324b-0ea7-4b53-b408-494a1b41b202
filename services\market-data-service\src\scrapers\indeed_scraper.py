"""
Indeed Job Scraper
Implements job scraping from Indeed.com
Part of FR-5.2: Job Market Data Ingestion Service
"""

import asyncio
import logging
from typing import List, Optional
from urllib.parse import urlencode, quote
import re
from datetime import datetime

from .base_scraper import BaseScraper, JobPosting

class IndeedScraper(BaseScraper):
    """Indeed job scraper using public job search"""
    
    def __init__(self):
        super().__init__(
            source_name="indeed",
            base_url="https://www.indeed.com",
            rate_limit=1.5  # Be respectful to Indeed
        )
        
        # Indeed-specific headers
        self.session.headers.update({
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Cache-Control': 'max-age=0'
        })

    def build_search_url(self, search_term: str, location: str, page: int = 1) -> str:
        """Build Indeed job search URL"""
        params = {
            'q': search_term,
            'l': location,
            'start': (page - 1) * 10,  # Indeed shows 10-15 jobs per page
            'sort': 'date',  # Sort by date posted
            'fromage': '7',  # Jobs posted in last 7 days
            'filter': '0',  # Include all job types
            'radius': '25'  # 25 mile radius
        }
        
        query_string = urlencode(params, quote_via=quote)
        return f"{self.base_url}/jobs?{query_string}"

    async def scrape_jobs(self, search_terms: List[str], locations: List[str], max_pages: int = 5) -> List[JobPosting]:
        """Scrape jobs from Indeed"""
        all_jobs = []
        
        for search_term in search_terms:
            for location in locations:
                self.logger.info(f"Scraping Indeed for '{search_term}' in '{location}'")
                
                jobs = await self._scrape_search_results(search_term, location, max_pages)
                all_jobs.extend(jobs)
                
                # Add delay between different search terms
                await asyncio.sleep(2)
        
        # Process and validate jobs
        return await self.process_job_batch(all_jobs)

    async def _scrape_search_results(self, search_term: str, location: str, max_pages: int) -> List[JobPosting]:
        """Scrape search results for a specific term and location"""
        jobs = []
        
        for page in range(1, max_pages + 1):
            try:
                url = self.build_search_url(search_term, location, page)
                self.logger.debug(f"Scraping page {page}: {url}")
                
                soup = await self.scrape_with_retry(url)
                if not soup:
                    self.logger.warning(f"Failed to get page {page} for {search_term} in {location}")
                    continue
                
                page_jobs = self._parse_job_listings(soup, location)
                if not page_jobs:
                    self.logger.info(f"No more jobs found on page {page}, stopping")
                    break
                
                jobs.extend(page_jobs)
                self.logger.info(f"Found {len(page_jobs)} jobs on page {page}")
                
                # Check if we've reached the end
                if len(page_jobs) < 8:  # Indeed usually shows 10+ per page
                    break
                    
            except Exception as e:
                self.logger.error(f"Error scraping page {page}: {e}")
                continue
        
        return jobs

    def _parse_job_listings(self, soup, location: str) -> List[JobPosting]:
        """Parse job listings from Indeed search results page"""
        jobs = []
        
        # Indeed job cards have specific selectors
        job_cards = soup.find_all('div', {'class': re.compile(r'job_seen_beacon|jobsearch-SerpJobCard|slider_container')})
        
        if not job_cards:
            # Try alternative selectors
            job_cards = soup.find_all('a', {'data-jk': True})  # Indeed job key attribute
        
        for card in job_cards:
            try:
                job = self._parse_job_card(card, location)
                if job:
                    jobs.append(job)
            except Exception as e:
                self.logger.debug(f"Error parsing job card: {e}")
                continue
        
        return jobs

    def _parse_job_card(self, card, location: str) -> Optional[JobPosting]:
        """Parse individual job card"""
        try:
            # Extract title
            title_elem = card.find('h2', {'class': re.compile(r'jobTitle')})
            if not title_elem:
                title_elem = card.find('a', {'data-jk': True})
            
            if not title_elem:
                return None
            
            title_link = title_elem.find('a') if title_elem.name != 'a' else title_elem
            title = self.clean_text(title_link.get_text()) if title_link else self.clean_text(title_elem.get_text())
            
            # Extract company
            company_elem = card.find('span', {'class': re.compile(r'companyName')})
            if not company_elem:
                company_elem = card.find('a', {'data-testid': 'company-name'})
            
            if not company_elem:
                return None
            
            company = self.clean_text(company_elem.get_text())
            
            # Extract location
            location_elem = card.find('div', {'class': re.compile(r'companyLocation')})
            if not location_elem:
                location_elem = card.find('div', {'data-testid': 'job-location'})
            
            job_location = location if not location_elem else self.clean_text(location_elem.get_text())
            
            # Extract job URL and ID
            external_url = None
            external_id = None
            
            if title_link and title_link.get('href'):
                href = title_link['href']
                if href.startswith('/'):
                    external_url = f"{self.base_url}{href}"
                else:
                    external_url = href
                
                # Extract job ID from URL or data attribute
                job_id_match = re.search(r'jk=([a-f0-9]+)', href)
                if job_id_match:
                    external_id = job_id_match.group(1)
                elif title_link.get('data-jk'):
                    external_id = title_link['data-jk']
            
            # Extract posted date
            date_elem = card.find('span', {'class': re.compile(r'date')})
            if not date_elem:
                date_elem = card.find('span', {'data-testid': 'job-age'})
            
            posted_date = None
            if date_elem:
                date_text = self.clean_text(date_elem.get_text())
                posted_date = self.parse_date(date_text)
            
            # Extract salary if available
            salary_elem = card.find('span', {'class': re.compile(r'salary|estimated-salary')})
            if not salary_elem:
                salary_elem = card.find('div', {'data-testid': 'attribute_snippet_testid'})
            
            salary_min, salary_max = None, None
            if salary_elem:
                salary_text = self.clean_text(salary_elem.get_text())
                salary_min, salary_max = self.extract_salary_range(salary_text)
            
            # Extract job snippet/description
            snippet_elem = card.find('div', {'class': re.compile(r'summary')})
            if not snippet_elem:
                snippet_elem = card.find('div', {'data-testid': 'job-snippet'})
            
            description = f"Job posting for {title} at {company} in {job_location}"
            if snippet_elem:
                snippet_text = self.clean_text(snippet_elem.get_text())
                description = f"{description}. {snippet_text}"
            
            # Extract job type and experience level
            job_type = self.extract_job_type(f"{title} {description}")
            experience_level = self.extract_experience_level(f"{title} {description}")
            
            # Extract additional attributes
            attributes = self._extract_job_attributes(card)
            
            return JobPosting(
                title=title,
                company=company,
                description=description,
                location=job_location,
                salary_min=salary_min,
                salary_max=salary_max,
                job_type=job_type,
                experience_level=experience_level,
                source=self.source_name,
                external_id=external_id,
                external_url=external_url,
                posted_date=posted_date,
                requirements=attributes.get('requirements'),
                industry=attributes.get('industry')
            )
            
        except Exception as e:
            self.logger.debug(f"Error parsing job card: {e}")
            return None

    def _extract_job_attributes(self, card) -> dict:
        """Extract additional job attributes from Indeed card"""
        attributes = {}
        
        # Look for job type indicators
        job_type_elem = card.find('span', {'class': re.compile(r'jobType|employment-type')})
        if job_type_elem:
            attributes['job_type'] = self.clean_text(job_type_elem.get_text())
        
        # Look for urgency indicators
        urgency_elem = card.find('span', {'class': re.compile(r'urgently|hiring')})
        if urgency_elem:
            attributes['urgency'] = True
        
        # Look for benefits
        benefits_elem = card.find('div', {'class': re.compile(r'benefits|perks')})
        if benefits_elem:
            attributes['benefits'] = self.clean_text(benefits_elem.get_text())
        
        # Look for rating
        rating_elem = card.find('span', {'class': re.compile(r'rating')})
        if rating_elem:
            rating_text = self.clean_text(rating_elem.get_text())
            rating_match = re.search(r'(\d+\.?\d*)', rating_text)
            if rating_match:
                attributes['company_rating'] = float(rating_match.group(1))
        
        return attributes

    async def fetch_job_details(self, job_url: str) -> Optional[dict]:
        """Fetch detailed job information from job page"""
        try:
            soup = await self.scrape_with_retry(job_url)
            if not soup:
                return None
            
            # Extract full job description
            description_elem = soup.find('div', {'class': re.compile(r'jobsearch-jobDescriptionText|jobDescriptionText')})
            description = ""
            if description_elem:
                description = self.clean_text(description_elem.get_text())
            
            # Extract job details section
            details = {}
            details_section = soup.find('div', {'class': re.compile(r'jobDetails|job-details')})
            if details_section:
                # Extract job type
                job_type_elem = details_section.find('span', string=re.compile(r'Job Type', re.I))
                if job_type_elem:
                    job_type_value = job_type_elem.find_next_sibling()
                    if job_type_value:
                        details['job_type'] = self.clean_text(job_type_value.get_text())
                
                # Extract experience level
                exp_elem = details_section.find('span', string=re.compile(r'Experience', re.I))
                if exp_elem:
                    exp_value = exp_elem.find_next_sibling()
                    if exp_value:
                        details['experience_required'] = self.clean_text(exp_value.get_text())
            
            # Extract skills
            skills = self.extract_skills_from_text(description)
            
            # Extract company information
            company_info = {}
            company_section = soup.find('div', {'class': re.compile(r'company|employer')})
            if company_section:
                # Company size
                size_elem = company_section.find('div', string=re.compile(r'employees', re.I))
                if size_elem:
                    company_info['size'] = self.clean_text(size_elem.get_text())
                
                # Industry
                industry_elem = company_section.find('div', string=re.compile(r'industry', re.I))
                if industry_elem:
                    company_info['industry'] = self.clean_text(industry_elem.get_text())
            
            return {
                'description': description,
                'skills': skills,
                'job_details': details,
                'company_info': company_info
            }
            
        except Exception as e:
            self.logger.error(f"Error fetching job details from {job_url}: {e}")
            return None

    async def enrich_job_data(self, jobs: List[JobPosting]) -> List[JobPosting]:
        """Enrich job data by fetching detailed information"""
        enriched_jobs = []
        
        for i, job in enumerate(jobs):
            try:
                if job.external_url and i < 5:  # Limit detailed fetching to first 5 jobs
                    details = await self.fetch_job_details(job.external_url)
                    if details:
                        job.description = details.get('description', job.description)
                        job.skills = details.get('skills', job.skills)
                        
                        # Update job details
                        job_details = details.get('job_details', {})
                        if 'job_type' in job_details:
                            job.job_type = job_details['job_type']
                        if 'experience_required' in job_details:
                            job.experience_level = self.extract_experience_level(job_details['experience_required'])
                        
                        # Update company info
                        company_info = details.get('company_info', {})
                        if 'industry' in company_info:
                            job.industry = company_info['industry']
                
                enriched_jobs.append(job)
                
                # Rate limiting for detail fetching
                if job.external_url:
                    await asyncio.sleep(2)
                    
            except Exception as e:
                self.logger.error(f"Error enriching job data: {e}")
                enriched_jobs.append(job)  # Add original job even if enrichment fails
        
        return enriched_jobs

    def _extract_indeed_specific_data(self, soup) -> dict:
        """Extract Indeed-specific metadata"""
        data = {}
        
        # Extract apply method
        apply_elem = soup.find('div', {'class': re.compile(r'apply|application')})
        if apply_elem:
            if 'indeed' in apply_elem.get_text().lower():
                data['apply_method'] = 'indeed'
            else:
                data['apply_method'] = 'external'
        
        # Extract posting freshness
        freshness_elem = soup.find('span', {'class': re.compile(r'date|posted')})
        if freshness_elem:
            data['posting_freshness'] = self.clean_text(freshness_elem.get_text())
        
        # Extract view count if available
        views_elem = soup.find('span', string=re.compile(r'view', re.I))
        if views_elem:
            views_text = self.clean_text(views_elem.get_text())
            views_match = re.search(r'(\d+)', views_text)
            if views_match:
                data['view_count'] = int(views_match.group(1))
        
        return data
