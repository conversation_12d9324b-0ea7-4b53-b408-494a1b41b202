# Database
DATABASE_URL="postgresql://username:password@localhost:5432/careercraft"

# NextAuth.js
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-secret-key-here"

# OAuth Providers
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"
GITHUB_CLIENT_ID="your-github-client-id"
GITHUB_CLIENT_SECRET="your-github-client-secret"

# AI Services
OPENAI_API_KEY="sk-your-openai-api-key-here"

# LinkedIn Integration
LINKEDIN_CLIENT_ID="your-linkedin-client-id"
LINKEDIN_CLIENT_SECRET="your-linkedin-client-secret"
LINKEDIN_REDIRECT_URI="http://localhost:3000/api/linkedin/callback"

# Stripe Payment Processing
STRIPE_PUBLISHABLE_KEY="pk_test_your_stripe_publishable_key"
STRIPE_SECRET_KEY="sk_test_your_stripe_secret_key"
STRIPE_WEBHOOK_SECRET="whsec_your_webhook_secret"

# Stripe Price IDs (replace with your actual price IDs from Stripe Dashboard)
STRIPE_PRICE_PRO_MONTHLY="price_pro_monthly_id"
STRIPE_PRICE_PRO_YEARLY="price_pro_yearly_id"
STRIPE_PRICE_BUSINESS_MONTHLY="price_business_monthly_id"
STRIPE_PRICE_BUSINESS_YEARLY="price_business_yearly_id"
STRIPE_PRICE_ENTERPRISE_MONTHLY="price_enterprise_monthly_id"
STRIPE_PRICE_ENTERPRISE_YEARLY="price_enterprise_yearly_id"

# Optional: Alternative AI Providers
# ANTHROPIC_API_KEY="your-anthropic-api-key"
# GEMINI_API_KEY="your-gemini-api-key"

# Email Service (for notifications)
# SMTP_HOST="smtp.gmail.com"
# SMTP_PORT="587"
# SMTP_USER="<EMAIL>"
# SMTP_PASS="your-app-password"

# File Storage (for resume exports)
# AWS_ACCESS_KEY_ID="your-aws-access-key"
# AWS_SECRET_ACCESS_KEY="your-aws-secret-key"
# AWS_REGION="us-east-1"
# AWS_S3_BUCKET="careercraft-files"

# Analytics (optional)
# GOOGLE_ANALYTICS_ID="G-XXXXXXXXXX"
# MIXPANEL_TOKEN="your-mixpanel-token"

# Rate Limiting
# REDIS_URL="redis://localhost:6379"

# Feature Flags
# ENABLE_AI_FEATURES="true"
# ENABLE_PREMIUM_FEATURES="true"
# ENABLE_ANALYTICS="true"
