'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { 
  Target, 
  TrendingUp, 
  AlertTriangle, 
  CheckCircle, 
  Plus, 
  X,
  Search,
  Zap,
  RefreshCw,
  Lightbulb,
  BarChart3
} from 'lucide-react'

interface KeywordAnalysis {
  keyword: string
  frequency: number
  density: number
  importance: 'high' | 'medium' | 'low'
  found: boolean
  suggestions: string[]
}

interface ATSAnalysisResult {
  overallScore: number
  keywordScore: number
  formattingScore: number
  contentScore: number
  missingKeywords: string[]
  foundKeywords: string[]
  suggestions: string[]
  keywordAnalysis: KeywordAnalysis[]
}

interface KeywordOptimizerProps {
  resumeContent: string
  jobDescription?: string
  onOptimizationApply?: (optimizedContent: string) => void
}

export function KeywordOptimizer({ resumeContent, jobDescription, onOptimizationApply }: KeywordOptimizerProps) {
  const [isAnalyzing, setIsAnalyzing] = useState(false)
  const [analysisResult, setAnalysisResult] = useState<ATSAnalysisResult | null>(null)
  const [selectedKeywords, setSelectedKeywords] = useState<string[]>([])
  const [customKeywords, setCustomKeywords] = useState<string[]>([])
  const [newKeyword, setNewKeyword] = useState('')

  useEffect(() => {
    if (resumeContent && jobDescription) {
      analyzeKeywords()
    }
  }, [resumeContent, jobDescription])

  const analyzeKeywords = async () => {
    if (!resumeContent || !jobDescription) return

    setIsAnalyzing(true)
    try {
      // Simulate AI analysis - replace with actual API call
      await new Promise(resolve => setTimeout(resolve, 2000))

      // Mock analysis result
      const mockResult: ATSAnalysisResult = {
        overallScore: 78,
        keywordScore: 72,
        formattingScore: 85,
        contentScore: 76,
        missingKeywords: [
          'React', 'TypeScript', 'Node.js', 'AWS', 'Agile', 'Scrum',
          'CI/CD', 'Docker', 'Kubernetes', 'GraphQL'
        ],
        foundKeywords: [
          'JavaScript', 'Python', 'Git', 'REST API', 'MongoDB',
          'Leadership', 'Team Management', 'Problem Solving'
        ],
        suggestions: [
          'Add more technical skills mentioned in the job description',
          'Include specific frameworks and tools',
          'Mention relevant certifications',
          'Use action verbs from the job posting'
        ],
        keywordAnalysis: [
          {
            keyword: 'React',
            frequency: 0,
            density: 0,
            importance: 'high',
            found: false,
            suggestions: ['Add React experience in skills section', 'Mention React projects in experience']
          },
          {
            keyword: 'JavaScript',
            frequency: 3,
            density: 2.1,
            importance: 'high',
            found: true,
            suggestions: ['Good frequency, consider adding specific JS frameworks']
          },
          {
            keyword: 'Leadership',
            frequency: 2,
            density: 1.4,
            importance: 'medium',
            found: true,
            suggestions: ['Provide specific leadership examples with metrics']
          }
        ]
      }

      setAnalysisResult(mockResult)
    } catch (error) {
      console.error('Error analyzing keywords:', error)
    } finally {
      setIsAnalyzing(false)
    }
  }

  const addCustomKeyword = () => {
    if (newKeyword.trim() && !customKeywords.includes(newKeyword.trim())) {
      setCustomKeywords(prev => [...prev, newKeyword.trim()])
      setNewKeyword('')
    }
  }

  const removeCustomKeyword = (keyword: string) => {
    setCustomKeywords(prev => prev.filter(k => k !== keyword))
  }

  const toggleKeywordSelection = (keyword: string) => {
    setSelectedKeywords(prev => 
      prev.includes(keyword) 
        ? prev.filter(k => k !== keyword)
        : [...prev, keyword]
    )
  }

  const optimizeWithSelectedKeywords = async () => {
    if (selectedKeywords.length === 0) return

    try {
      // Simulate optimization - replace with actual API call
      const optimizedContent = `${resumeContent}\n\nOptimized with keywords: ${selectedKeywords.join(', ')}`
      
      if (onOptimizationApply) {
        onOptimizationApply(optimizedContent)
      }
    } catch (error) {
      console.error('Error optimizing with keywords:', error)
    }
  }

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600'
    if (score >= 60) return 'text-yellow-600'
    return 'text-red-600'
  }

  const getScoreBarColor = (score: number) => {
    if (score >= 80) return 'bg-green-500'
    if (score >= 60) return 'bg-yellow-500'
    return 'bg-red-500'
  }

  const getImportanceColor = (importance: KeywordAnalysis['importance']) => {
    switch (importance) {
      case 'high': return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
      case 'medium': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400'
      case 'low': return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400'
    }
  }

  if (!jobDescription) {
    return (
      <Card className="glass-card">
        <CardContent className="text-center py-8">
          <Search className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">No Job Description</h3>
          <p className="text-muted-foreground">
            Add a job description to get AI-powered keyword optimization suggestions
          </p>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card className="glass-card">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Target className="w-5 h-5 text-blue-600" />
            <span>Keyword Optimization</span>
          </CardTitle>
          <CardDescription>
            Optimize your resume with AI-powered keyword analysis for better ATS compatibility
          </CardDescription>
        </CardHeader>
      </Card>

      {/* Analysis Button */}
      {!analysisResult && (
        <Card className="glass-card">
          <CardContent className="text-center py-8">
            <Button 
              onClick={analyzeKeywords}
              disabled={isAnalyzing}
              size="lg"
              className="glass-card bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white border-0"
            >
              {isAnalyzing ? (
                <>
                  <RefreshCw className="w-5 h-5 mr-2 animate-spin" />
                  Analyzing Keywords...
                </>
              ) : (
                <>
                  <Zap className="w-5 h-5 mr-2" />
                  Analyze Keywords
                </>
              )}
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Analysis Results */}
      {analysisResult && (
        <>
          {/* Score Overview */}
          <Card className="glass-card">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <BarChart3 className="w-5 h-5 text-green-600" />
                <span>ATS Compatibility Scores</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Overall Score</span>
                    <span className={`text-lg font-bold ${getScoreColor(analysisResult.overallScore)}`}>
                      {analysisResult.overallScore}%
                    </span>
                  </div>
                  <Progress 
                    value={analysisResult.overallScore} 
                    className="h-2"
                  />
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Keywords</span>
                    <span className={`text-lg font-bold ${getScoreColor(analysisResult.keywordScore)}`}>
                      {analysisResult.keywordScore}%
                    </span>
                  </div>
                  <Progress 
                    value={analysisResult.keywordScore} 
                    className="h-2"
                  />
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Formatting</span>
                    <span className={`text-lg font-bold ${getScoreColor(analysisResult.formattingScore)}`}>
                      {analysisResult.formattingScore}%
                    </span>
                  </div>
                  <Progress 
                    value={analysisResult.formattingScore} 
                    className="h-2"
                  />
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Content</span>
                    <span className={`text-lg font-bold ${getScoreColor(analysisResult.contentScore)}`}>
                      {analysisResult.contentScore}%
                    </span>
                  </div>
                  <Progress 
                    value={analysisResult.contentScore} 
                    className="h-2"
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Missing Keywords */}
          <Card className="glass-card">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <AlertTriangle className="w-5 h-5 text-red-600" />
                <span>Missing Keywords</span>
              </CardTitle>
              <CardDescription>
                Important keywords from the job description that are missing from your resume
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-2 mb-4">
                {analysisResult.missingKeywords.map((keyword) => (
                  <Badge
                    key={keyword}
                    variant="outline"
                    className={`cursor-pointer transition-colors ${
                      selectedKeywords.includes(keyword)
                        ? 'bg-blue-100 text-blue-800 border-blue-300 dark:bg-blue-900/20 dark:text-blue-400'
                        : 'hover:bg-red-50 dark:hover:bg-red-900/10'
                    }`}
                    onClick={() => toggleKeywordSelection(keyword)}
                  >
                    {keyword}
                    {selectedKeywords.includes(keyword) && (
                      <CheckCircle className="w-3 h-3 ml-1" />
                    )}
                  </Badge>
                ))}
              </div>

              {selectedKeywords.length > 0 && (
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">
                      Selected keywords ({selectedKeywords.length})
                    </span>
                    <Button
                      onClick={optimizeWithSelectedKeywords}
                      size="sm"
                      className="glass-card bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white border-0"
                    >
                      <Plus className="w-3 h-3 mr-1" />
                      Add to Resume
                    </Button>
                  </div>
                  <div className="flex flex-wrap gap-1">
                    {selectedKeywords.map((keyword) => (
                      <Badge key={keyword} className="bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400">
                        {keyword}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Found Keywords */}
          <Card className="glass-card">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <CheckCircle className="w-5 h-5 text-green-600" />
                <span>Found Keywords</span>
              </CardTitle>
              <CardDescription>
                Keywords from the job description that are already in your resume
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-2">
                {analysisResult.foundKeywords.map((keyword) => (
                  <Badge
                    key={keyword}
                    className="bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400"
                  >
                    <CheckCircle className="w-3 h-3 mr-1" />
                    {keyword}
                  </Badge>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Detailed Keyword Analysis */}
          <Card className="glass-card">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <TrendingUp className="w-5 h-5 text-purple-600" />
                <span>Detailed Keyword Analysis</span>
              </CardTitle>
              <CardDescription>
                In-depth analysis of keyword usage and optimization opportunities
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {analysisResult.keywordAnalysis.map((analysis, index) => (
                  <div key={index} className="glass-input p-4 rounded-lg">
                    <div className="flex items-start justify-between mb-2">
                      <div className="flex items-center space-x-2">
                        <span className="font-medium">{analysis.keyword}</span>
                        <Badge className={getImportanceColor(analysis.importance)}>
                          {analysis.importance}
                        </Badge>
                        {analysis.found ? (
                          <CheckCircle className="w-4 h-4 text-green-600" />
                        ) : (
                          <X className="w-4 h-4 text-red-600" />
                        )}
                      </div>
                      <div className="text-right text-sm text-muted-foreground">
                        {analysis.found ? (
                          <>
                            <div>Frequency: {analysis.frequency}</div>
                            <div>Density: {analysis.density}%</div>
                          </>
                        ) : (
                          <div>Not found</div>
                        )}
                      </div>
                    </div>
                    
                    <div className="space-y-1">
                      {analysis.suggestions.map((suggestion, suggestionIndex) => (
                        <div key={suggestionIndex} className="flex items-start space-x-2 text-sm">
                          <Lightbulb className="w-3 h-3 text-yellow-500 mt-0.5 flex-shrink-0" />
                          <span className="text-muted-foreground">{suggestion}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Custom Keywords */}
          <Card className="glass-card">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Plus className="w-5 h-5 text-blue-600" />
                <span>Custom Keywords</span>
              </CardTitle>
              <CardDescription>
                Add your own keywords that are important for this role
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex space-x-2">
                <input
                  type="text"
                  value={newKeyword}
                  onChange={(e) => setNewKeyword(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && addCustomKeyword()}
                  placeholder="Add custom keyword..."
                  className="flex-1 px-3 py-2 glass-input rounded-md"
                />
                <Button onClick={addCustomKeyword} size="sm" className="glass-card">
                  <Plus className="w-4 h-4" />
                </Button>
              </div>

              {customKeywords.length > 0 && (
                <div className="flex flex-wrap gap-2">
                  {customKeywords.map((keyword) => (
                    <Badge
                      key={keyword}
                      variant="outline"
                      className="cursor-pointer hover:bg-red-50 dark:hover:bg-red-900/10"
                    >
                      {keyword}
                      <X 
                        className="w-3 h-3 ml-1" 
                        onClick={() => removeCustomKeyword(keyword)}
                      />
                    </Badge>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Optimization Suggestions */}
          <Card className="glass-card">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Lightbulb className="w-5 h-5 text-yellow-600" />
                <span>Optimization Suggestions</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {analysisResult.suggestions.map((suggestion, index) => (
                  <div key={index} className="flex items-start space-x-3 p-3 glass-input rounded-lg">
                    <Lightbulb className="w-4 h-4 text-yellow-500 mt-0.5 flex-shrink-0" />
                    <span className="text-sm">{suggestion}</span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </>
      )}
    </div>
  )
}
