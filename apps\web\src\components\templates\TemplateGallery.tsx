'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { 
  Eye, 
  Star, 
  Crown, 
  Search, 
  Filter,
  Palette,
  Briefcase,
  GraduationCap,
  Code,
  Heart,
  Users,
  TrendingUp
} from 'lucide-react'

interface Template {
  id: string
  name: string
  description: string
  category: 'modern' | 'classic' | 'creative' | 'minimal' | 'professional'
  isPremium: boolean
  isPopular: boolean
  rating: number
  downloads: number
  preview: string
  colors: string[]
  features: string[]
  suitableFor: string[]
}

interface TemplateGalleryProps {
  onSelectTemplate?: (template: Template) => void
  showSelectButton?: boolean
}

export function TemplateGallery({ onSelectTemplate, showSelectButton = false }: TemplateGalleryProps) {
  const router = useRouter()
  const [templates, setTemplates] = useState<Template[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [categoryFilter, setCategoryFilter] = useState<string>('all')
  const [selectedTemplate, setSelectedTemplate] = useState<string | null>(null)

  useEffect(() => {
    fetchTemplates()
  }, [])

  const fetchTemplates = async () => {
    try {
      // Mock data for now - replace with actual API call
      const mockTemplates: Template[] = [
        {
          id: 'modern-1',
          name: 'Modern Professional',
          description: 'Clean and contemporary design perfect for tech and business professionals',
          category: 'modern',
          isPremium: false,
          isPopular: true,
          rating: 4.8,
          downloads: 1250,
          preview: '/templates/modern-1.jpg',
          colors: ['#3B82F6', '#1E40AF', '#F3F4F6'],
          features: ['ATS Optimized', 'Clean Layout', 'Professional Typography'],
          suitableFor: ['Software Engineer', 'Product Manager', 'Business Analyst']
        },
        {
          id: 'classic-1',
          name: 'Classic Executive',
          description: 'Traditional and elegant design for senior-level positions',
          category: 'classic',
          isPremium: true,
          isPopular: false,
          rating: 4.9,
          downloads: 890,
          preview: '/templates/classic-1.jpg',
          colors: ['#1F2937', '#374151', '#F9FAFB'],
          features: ['Executive Style', 'Formal Layout', 'Premium Typography'],
          suitableFor: ['Executive', 'Director', 'Senior Manager']
        },
        {
          id: 'creative-1',
          name: 'Creative Designer',
          description: 'Bold and artistic design for creative professionals',
          category: 'creative',
          isPremium: true,
          isPopular: true,
          rating: 4.7,
          downloads: 2100,
          preview: '/templates/creative-1.jpg',
          colors: ['#8B5CF6', '#A855F7', '#F3E8FF'],
          features: ['Creative Layout', 'Color Accents', 'Portfolio Section'],
          suitableFor: ['Graphic Designer', 'UX Designer', 'Creative Director']
        },
        {
          id: 'minimal-1',
          name: 'Minimal Clean',
          description: 'Simple and focused design that highlights your content',
          category: 'minimal',
          isPremium: false,
          isPopular: false,
          rating: 4.6,
          downloads: 750,
          preview: '/templates/minimal-1.jpg',
          colors: ['#000000', '#6B7280', '#FFFFFF'],
          features: ['Minimal Design', 'Focus on Content', 'Easy to Read'],
          suitableFor: ['Any Profession', 'Entry Level', 'Career Change']
        },
        {
          id: 'professional-1',
          name: 'Corporate Professional',
          description: 'Sophisticated design for corporate environments',
          category: 'professional',
          isPremium: false,
          isPopular: true,
          rating: 4.8,
          downloads: 1680,
          preview: '/templates/professional-1.jpg',
          colors: ['#059669', '#047857', '#F0FDF4'],
          features: ['Corporate Style', 'Professional Layout', 'Industry Standard'],
          suitableFor: ['Finance', 'Consulting', 'Legal', 'Healthcare']
        },
        {
          id: 'modern-2',
          name: 'Tech Innovator',
          description: 'Modern design with tech-focused elements',
          category: 'modern',
          isPremium: true,
          isPopular: false,
          rating: 4.7,
          downloads: 920,
          preview: '/templates/modern-2.jpg',
          colors: ['#6366F1', '#4F46E5', '#EEF2FF'],
          features: ['Tech Focused', 'Modern Icons', 'Skills Visualization'],
          suitableFor: ['Software Developer', 'Data Scientist', 'DevOps Engineer']
        }
      ]

      // Filter templates based on search and category
      let filteredTemplates = mockTemplates

      if (searchQuery) {
        filteredTemplates = filteredTemplates.filter(template =>
          template.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          template.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
          template.suitableFor.some(role => role.toLowerCase().includes(searchQuery.toLowerCase()))
        )
      }

      if (categoryFilter !== 'all') {
        filteredTemplates = filteredTemplates.filter(template =>
          template.category === categoryFilter
        )
      }

      setTemplates(filteredTemplates)
    } catch (error) {
      console.error('Failed to fetch templates:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const getCategoryIcon = (category: Template['category']) => {
    switch (category) {
      case 'modern':
        return <TrendingUp className="w-4 h-4" />
      case 'classic':
        return <Briefcase className="w-4 h-4" />
      case 'creative':
        return <Palette className="w-4 h-4" />
      case 'minimal':
        return <Heart className="w-4 h-4" />
      case 'professional':
        return <Users className="w-4 h-4" />
      default:
        return <Briefcase className="w-4 h-4" />
    }
  }

  const handlePreview = (templateId: string) => {
    router.push(`/dashboard/templates/${templateId}/preview`)
  }

  const handleSelectTemplate = (template: Template) => {
    if (onSelectTemplate) {
      onSelectTemplate(template)
    } else {
      router.push(`/dashboard/resumes/new?template=${template.id}`)
    }
  }

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(6)].map((_, i) => (
            <Card key={i} className="glass-card animate-pulse">
              <CardContent className="p-0">
                <div className="h-64 bg-gray-200 rounded-t-lg"></div>
                <div className="p-4 space-y-2">
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Search and Filters */}
      <div className="glass-panel p-4 rounded-2xl">
        <div className="flex flex-col lg:flex-row lg:items-center gap-4">
          {/* Search */}
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
            <Input
              placeholder="Search templates..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 glass-input"
            />
          </div>

          {/* Category Filter */}
          <Select value={categoryFilter} onValueChange={setCategoryFilter}>
            <SelectTrigger className="w-[180px] glass-input">
              <Filter className="w-4 h-4 mr-2" />
              <SelectValue placeholder="Category" />
            </SelectTrigger>
            <SelectContent className="glass-card">
              <SelectItem value="all">All Categories</SelectItem>
              <SelectItem value="modern">Modern</SelectItem>
              <SelectItem value="classic">Classic</SelectItem>
              <SelectItem value="creative">Creative</SelectItem>
              <SelectItem value="minimal">Minimal</SelectItem>
              <SelectItem value="professional">Professional</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Templates Grid */}
      {templates.length === 0 ? (
        <div className="text-center py-12">
          <div className="glass-panel p-8 rounded-2xl max-w-md mx-auto">
            <Palette className="w-16 h-16 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">No templates found</h3>
            <p className="text-muted-foreground">
              Try adjusting your search or filters
            </p>
          </div>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {templates.map((template) => (
            <Card 
              key={template.id} 
              className={`glass-card hover:scale-105 transition-all duration-200 group cursor-pointer ${
                selectedTemplate === template.id ? 'ring-2 ring-blue-500' : ''
              }`}
              onClick={() => setSelectedTemplate(template.id)}
            >
              <CardContent className="p-0">
                {/* Template Preview */}
                <div className="relative h-64 bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-800 dark:to-gray-900 rounded-t-lg overflow-hidden">
                  {/* Mock preview */}
                  <div className="absolute inset-4 bg-white dark:bg-gray-800 rounded shadow-lg p-4">
                    <div className="space-y-2">
                      <div className="h-3 bg-gray-300 dark:bg-gray-600 rounded w-3/4"></div>
                      <div className="h-2 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
                      <div className="space-y-1 mt-4">
                        <div className="h-2 bg-gray-200 dark:bg-gray-700 rounded w-full"></div>
                        <div className="h-2 bg-gray-200 dark:bg-gray-700 rounded w-4/5"></div>
                        <div className="h-2 bg-gray-200 dark:bg-gray-700 rounded w-3/5"></div>
                      </div>
                    </div>
                  </div>

                  {/* Badges */}
                  <div className="absolute top-2 left-2 flex space-x-1">
                    {template.isPremium && (
                      <Badge className="bg-yellow-500 text-white">
                        <Crown className="w-3 h-3 mr-1" />
                        Premium
                      </Badge>
                    )}
                    {template.isPopular && (
                      <Badge className="bg-red-500 text-white">
                        <Star className="w-3 h-3 mr-1" />
                        Popular
                      </Badge>
                    )}
                  </div>

                  {/* Preview Button */}
                  <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                    <Button
                      size="sm"
                      variant="secondary"
                      onClick={(e) => {
                        e.stopPropagation()
                        handlePreview(template.id)
                      }}
                      className="glass-card"
                    >
                      <Eye className="w-3 h-3 mr-1" />
                      Preview
                    </Button>
                  </div>
                </div>

                {/* Template Info */}
                <div className="p-4">
                  <div className="flex items-start justify-between mb-2">
                    <div>
                      <CardTitle className="text-lg font-semibold line-clamp-1">
                        {template.name}
                      </CardTitle>
                      <div className="flex items-center space-x-2 mt-1">
                        <Badge variant="secondary" className="glass-input text-xs">
                          {getCategoryIcon(template.category)}
                          <span className="ml-1 capitalize">{template.category}</span>
                        </Badge>
                        <div className="flex items-center text-xs text-muted-foreground">
                          <Star className="w-3 h-3 mr-1 fill-yellow-400 text-yellow-400" />
                          {template.rating}
                        </div>
                      </div>
                    </div>
                  </div>

                  <CardDescription className="line-clamp-2 mb-3">
                    {template.description}
                  </CardDescription>

                  {/* Features */}
                  <div className="flex flex-wrap gap-1 mb-3">
                    {template.features.slice(0, 2).map((feature) => (
                      <Badge key={feature} variant="outline" className="text-xs">
                        {feature}
                      </Badge>
                    ))}
                    {template.features.length > 2 && (
                      <Badge variant="outline" className="text-xs">
                        +{template.features.length - 2} more
                      </Badge>
                    )}
                  </div>

                  {/* Stats */}
                  <div className="flex items-center justify-between text-xs text-muted-foreground mb-4">
                    <span>{template.downloads.toLocaleString()} downloads</span>
                    <div className="flex items-center space-x-1">
                      {template.colors.slice(0, 3).map((color, index) => (
                        <div
                          key={index}
                          className="w-3 h-3 rounded-full border border-gray-300"
                          style={{ backgroundColor: color }}
                        />
                      ))}
                    </div>
                  </div>

                  {/* Action Button */}
                  {showSelectButton ? (
                    <Button 
                      className="w-full glass-card bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white border-0"
                      onClick={(e) => {
                        e.stopPropagation()
                        handleSelectTemplate(template)
                      }}
                    >
                      Select Template
                    </Button>
                  ) : (
                    <Button 
                      className="w-full glass-card bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white border-0"
                      onClick={(e) => {
                        e.stopPropagation()
                        handleSelectTemplate(template)
                      }}
                    >
                      Use This Template
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  )
}
