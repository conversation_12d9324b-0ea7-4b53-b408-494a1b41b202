/**
 * Diff Engine for Version Control
 * 
 * Implements algorithms for calculating differences between resume versions
 * and generating visual diffs for comparison.
 */

import { z } from 'zod'

// Diff operation types
export const DiffOperationSchema = z.object({
  type: z.enum(['add', 'remove', 'modify', 'move']),
  path: z.array(z.string()),
  oldValue: z.any().optional(),
  newValue: z.any().optional(),
  index: z.number().optional()
})

export const VersionDiffSchema = z.object({
  operations: z.array(DiffOperationSchema),
  summary: z.object({
    additions: z.number(),
    deletions: z.number(),
    modifications: z.number(),
    moves: z.number()
  }),
  metadata: z.object({
    fromVersion: z.number(),
    toVersion: z.number(),
    calculatedAt: z.number(),
    complexity: z.enum(['low', 'medium', 'high'])
  })
})

export type DiffOperation = z.infer<typeof DiffOperationSchema>
export type VersionDiff = z.infer<typeof VersionDiffSchema>

export interface DiffOptions {
  ignoreWhitespace?: boolean
  ignoreCase?: boolean
  contextLines?: number
  maxDepth?: number
}

export class DiffEngine {
  private options: Required<DiffOptions>

  constructor(options: DiffOptions = {}) {
    this.options = {
      ignoreWhitespace: false,
      ignoreCase: false,
      contextLines: 3,
      maxDepth: 10,
      ...options
    }
  }

  /**
   * Calculate diff between two resume versions
   */
  calculateDiff(oldContent: any, newContent: any, fromVersion: number, toVersion: number): VersionDiff {
    const operations: DiffOperation[] = []
    
    // Recursively compare objects
    this.compareObjects(oldContent, newContent, [], operations, 0)
    
    // Calculate summary statistics
    const summary = this.calculateSummary(operations)
    
    // Determine complexity
    const complexity = this.determineComplexity(operations)
    
    return {
      operations,
      summary,
      metadata: {
        fromVersion,
        toVersion,
        calculatedAt: Date.now(),
        complexity
      }
    }
  }

  /**
   * Apply diff operations to content
   */
  applyDiff(content: any, diff: VersionDiff): any {
    let result = JSON.parse(JSON.stringify(content))
    
    // Sort operations by path depth (deepest first) to avoid conflicts
    const sortedOps = [...diff.operations].sort((a, b) => b.path.length - a.path.length)
    
    for (const operation of sortedOps) {
      result = this.applyOperation(result, operation)
    }
    
    return result
  }

  /**
   * Reverse diff operations
   */
  reverseDiff(diff: VersionDiff): VersionDiff {
    const reversedOps: DiffOperation[] = diff.operations.map(op => {
      switch (op.type) {
        case 'add':
          return { ...op, type: 'remove', oldValue: op.newValue, newValue: undefined }
        case 'remove':
          return { ...op, type: 'add', oldValue: undefined, newValue: op.oldValue }
        case 'modify':
          return { ...op, oldValue: op.newValue, newValue: op.oldValue }
        case 'move':
          // Move operations are symmetric
          return op
        default:
          return op
      }
    }).reverse()

    return {
      operations: reversedOps,
      summary: {
        additions: diff.summary.deletions,
        deletions: diff.summary.additions,
        modifications: diff.summary.modifications,
        moves: diff.summary.moves
      },
      metadata: {
        ...diff.metadata,
        fromVersion: diff.metadata.toVersion,
        toVersion: diff.metadata.fromVersion,
        calculatedAt: Date.now()
      }
    }
  }

  /**
   * Merge multiple diffs into a single diff
   */
  mergeDiffs(diffs: VersionDiff[]): VersionDiff {
    if (diffs.length === 0) {
      throw new Error('Cannot merge empty diff array')
    }
    
    if (diffs.length === 1) {
      return diffs[0]
    }

    let mergedOps: DiffOperation[] = []
    let totalSummary = {
      additions: 0,
      deletions: 0,
      modifications: 0,
      moves: 0
    }

    for (const diff of diffs) {
      mergedOps = mergedOps.concat(diff.operations)
      totalSummary.additions += diff.summary.additions
      totalSummary.deletions += diff.summary.deletions
      totalSummary.modifications += diff.summary.modifications
      totalSummary.moves += diff.summary.moves
    }

    // Optimize merged operations
    mergedOps = this.optimizeOperations(mergedOps)

    return {
      operations: mergedOps,
      summary: totalSummary,
      metadata: {
        fromVersion: diffs[0].metadata.fromVersion,
        toVersion: diffs[diffs.length - 1].metadata.toVersion,
        calculatedAt: Date.now(),
        complexity: this.determineComplexity(mergedOps)
      }
    }
  }

  /**
   * Generate text-based diff for display
   */
  generateTextDiff(oldText: string, newText: string): string[] {
    const oldLines = oldText.split('\n')
    const newLines = newText.split('\n')
    
    const diff = this.calculateLineDiff(oldLines, newLines)
    const result: string[] = []
    
    for (const op of diff) {
      switch (op.type) {
        case 'add':
          result.push(`+ ${op.newValue}`)
          break
        case 'remove':
          result.push(`- ${op.oldValue}`)
          break
        case 'modify':
          result.push(`- ${op.oldValue}`)
          result.push(`+ ${op.newValue}`)
          break
        default:
          result.push(`  ${op.oldValue || op.newValue}`)
      }
    }
    
    return result
  }

  private compareObjects(oldObj: any, newObj: any, path: string[], operations: DiffOperation[], depth: number) {
    if (depth > this.options.maxDepth) {
      return
    }

    // Handle null/undefined cases
    if (oldObj === null || oldObj === undefined) {
      if (newObj !== null && newObj !== undefined) {
        operations.push({
          type: 'add',
          path: [...path],
          newValue: newObj
        })
      }
      return
    }

    if (newObj === null || newObj === undefined) {
      operations.push({
        type: 'remove',
        path: [...path],
        oldValue: oldObj
      })
      return
    }

    // Handle primitive values
    if (typeof oldObj !== 'object' || typeof newObj !== 'object') {
      if (this.valuesEqual(oldObj, newObj)) {
        return
      }
      
      operations.push({
        type: 'modify',
        path: [...path],
        oldValue: oldObj,
        newValue: newObj
      })
      return
    }

    // Handle arrays
    if (Array.isArray(oldObj) && Array.isArray(newObj)) {
      this.compareArrays(oldObj, newObj, path, operations, depth)
      return
    }

    // Handle objects
    if (Array.isArray(oldObj) !== Array.isArray(newObj)) {
      operations.push({
        type: 'modify',
        path: [...path],
        oldValue: oldObj,
        newValue: newObj
      })
      return
    }

    // Compare object properties
    const allKeys = new Set([...Object.keys(oldObj), ...Object.keys(newObj)])
    
    for (const key of allKeys) {
      const newPath = [...path, key]
      
      if (!(key in oldObj)) {
        operations.push({
          type: 'add',
          path: newPath,
          newValue: newObj[key]
        })
      } else if (!(key in newObj)) {
        operations.push({
          type: 'remove',
          path: newPath,
          oldValue: oldObj[key]
        })
      } else {
        this.compareObjects(oldObj[key], newObj[key], newPath, operations, depth + 1)
      }
    }
  }

  private compareArrays(oldArr: any[], newArr: any[], path: string[], operations: DiffOperation[], depth: number) {
    // Simple array comparison - can be enhanced with LCS algorithm
    const maxLength = Math.max(oldArr.length, newArr.length)
    
    for (let i = 0; i < maxLength; i++) {
      const newPath = [...path, i.toString()]
      
      if (i >= oldArr.length) {
        operations.push({
          type: 'add',
          path: newPath,
          newValue: newArr[i],
          index: i
        })
      } else if (i >= newArr.length) {
        operations.push({
          type: 'remove',
          path: newPath,
          oldValue: oldArr[i],
          index: i
        })
      } else {
        this.compareObjects(oldArr[i], newArr[i], newPath, operations, depth + 1)
      }
    }
  }

  private calculateLineDiff(oldLines: string[], newLines: string[]): DiffOperation[] {
    // Simplified line-based diff using Myers algorithm concept
    const operations: DiffOperation[] = []
    let oldIndex = 0
    let newIndex = 0
    
    while (oldIndex < oldLines.length || newIndex < newLines.length) {
      if (oldIndex >= oldLines.length) {
        operations.push({
          type: 'add',
          path: [newIndex.toString()],
          newValue: newLines[newIndex]
        })
        newIndex++
      } else if (newIndex >= newLines.length) {
        operations.push({
          type: 'remove',
          path: [oldIndex.toString()],
          oldValue: oldLines[oldIndex]
        })
        oldIndex++
      } else if (this.linesEqual(oldLines[oldIndex], newLines[newIndex])) {
        // Lines are equal, continue
        oldIndex++
        newIndex++
      } else {
        // Lines differ, determine if it's a modification or add/remove
        operations.push({
          type: 'modify',
          path: [oldIndex.toString()],
          oldValue: oldLines[oldIndex],
          newValue: newLines[newIndex]
        })
        oldIndex++
        newIndex++
      }
    }
    
    return operations
  }

  private applyOperation(content: any, operation: DiffOperation): any {
    const result = JSON.parse(JSON.stringify(content))
    const path = operation.path
    
    if (path.length === 0) {
      return operation.newValue
    }
    
    let current = result
    for (let i = 0; i < path.length - 1; i++) {
      if (!current[path[i]]) {
        current[path[i]] = {}
      }
      current = current[path[i]]
    }
    
    const lastKey = path[path.length - 1]
    
    switch (operation.type) {
      case 'add':
        if (Array.isArray(current)) {
          current.splice(parseInt(lastKey), 0, operation.newValue)
        } else {
          current[lastKey] = operation.newValue
        }
        break
      case 'remove':
        if (Array.isArray(current)) {
          current.splice(parseInt(lastKey), 1)
        } else {
          delete current[lastKey]
        }
        break
      case 'modify':
        current[lastKey] = operation.newValue
        break
    }
    
    return result
  }

  private optimizeOperations(operations: DiffOperation[]): DiffOperation[] {
    // Remove redundant operations and merge similar ones
    const optimized: DiffOperation[] = []
    const pathMap = new Map<string, DiffOperation>()
    
    for (const op of operations) {
      const pathKey = op.path.join('.')
      const existing = pathMap.get(pathKey)
      
      if (existing) {
        // Merge operations on the same path
        if (existing.type === 'add' && op.type === 'remove') {
          // Add followed by remove = no operation
          pathMap.delete(pathKey)
        } else if (existing.type === 'remove' && op.type === 'add') {
          // Remove followed by add = modify
          pathMap.set(pathKey, {
            type: 'modify',
            path: op.path,
            oldValue: existing.oldValue,
            newValue: op.newValue
          })
        } else {
          // Keep the latest operation
          pathMap.set(pathKey, op)
        }
      } else {
        pathMap.set(pathKey, op)
      }
    }
    
    return Array.from(pathMap.values())
  }

  private calculateSummary(operations: DiffOperation[]) {
    return operations.reduce(
      (summary, op) => {
        switch (op.type) {
          case 'add':
            summary.additions++
            break
          case 'remove':
            summary.deletions++
            break
          case 'modify':
            summary.modifications++
            break
          case 'move':
            summary.moves++
            break
        }
        return summary
      },
      { additions: 0, deletions: 0, modifications: 0, moves: 0 }
    )
  }

  private determineComplexity(operations: DiffOperation[]): 'low' | 'medium' | 'high' {
    const totalOps = operations.length
    
    if (totalOps <= 5) return 'low'
    if (totalOps <= 20) return 'medium'
    return 'high'
  }

  private valuesEqual(a: any, b: any): boolean {
    if (this.options.ignoreCase && typeof a === 'string' && typeof b === 'string') {
      return a.toLowerCase() === b.toLowerCase()
    }
    
    if (this.options.ignoreWhitespace && typeof a === 'string' && typeof b === 'string') {
      return a.trim() === b.trim()
    }
    
    return a === b
  }

  private linesEqual(a: string, b: string): boolean {
    return this.valuesEqual(a, b)
  }
}

export default DiffEngine
