"""
AI Client for Job Processing
Handles OpenAI API interactions for job analysis and vectorization
"""

import asyncio
import logging
from typing import List, Optional, Dict, Any
import json
import openai
from openai import AsyncOpenAI

from .config import Config

class AIClient:
    """Client for AI-powered job analysis and vectorization"""
    
    def __init__(self):
        self.config = Config()
        self.logger = logging.getLogger('ai_client')
        
        # Initialize OpenAI client
        openai_config = self.config.get_openai_config()
        if openai_config['api_key']:
            self.client = AsyncOpenAI(api_key=openai_config['api_key'])
            self.model = openai_config['model']
            self.embedding_model = openai_config['embedding_model']
        else:
            self.client = None
            self.logger.warning("OpenAI API key not provided - AI features disabled")
    
    async def analyze_job_posting(self, prompt: str) -> Optional[str]:
        """Analyze job posting using GPT model"""
        if not self.client:
            return None
        
        try:
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {
                        "role": "system",
                        "content": "You are an expert job market analyst. Analyze job postings and extract structured information. Always respond with valid JSON."
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                temperature=0.1,
                max_tokens=1000,
                timeout=30
            )
            
            return response.choices[0].message.content
            
        except Exception as e:
            self.logger.error(f"Error in job analysis: {e}")
            return None
    
    async def create_embedding(self, text: str) -> Optional[List[float]]:
        """Create vector embedding for text"""
        if not self.client:
            return None
        
        try:
            # Truncate text if too long (OpenAI has token limits)
            max_chars = 8000  # Approximate token limit
            if len(text) > max_chars:
                text = text[:max_chars] + "..."
            
            response = await self.client.embeddings.create(
                model=self.embedding_model,
                input=text,
                encoding_format="float"
            )
            
            return response.data[0].embedding
            
        except Exception as e:
            self.logger.error(f"Error creating embedding: {e}")
            return None
    
    async def extract_skills_from_text(self, text: str) -> List[str]:
        """Extract skills from job text using AI"""
        if not self.client:
            return []
        
        prompt = f"""
        Extract all technical and professional skills from this job posting text.
        Return only a JSON array of skill names, no explanations.
        Focus on specific technologies, tools, programming languages, frameworks, and professional competencies.
        
        Text: {text[:2000]}
        """
        
        try:
            response = await self.analyze_job_posting(prompt)
            if response:
                skills = json.loads(response)
                return skills if isinstance(skills, list) else []
        except Exception as e:
            self.logger.error(f"Error extracting skills: {e}")
        
        return []
    
    async def classify_job_industry(self, job_title: str, company: str, description: str) -> Optional[str]:
        """Classify job industry using AI"""
        if not self.client:
            return None
        
        prompt = f"""
        Classify the industry for this job posting. Choose the most appropriate industry from this list:
        Technology, Finance, Healthcare, Retail, Education, Manufacturing, Media, Consulting, Government, Non-profit, Other
        
        Job Title: {job_title}
        Company: {company}
        Description: {description[:500]}
        
        Return only the industry name, no explanation.
        """
        
        try:
            response = await self.analyze_job_posting(prompt)
            if response:
                # Clean response to get just the industry name
                industry = response.strip().strip('"').strip("'")
                return industry
        except Exception as e:
            self.logger.error(f"Error classifying industry: {e}")
        
        return None
    
    async def estimate_salary_range(self, job_title: str, location: str, experience_level: str, skills: List[str]) -> Optional[Dict[str, int]]:
        """Estimate salary range using AI"""
        if not self.client:
            return None
        
        skills_text = ", ".join(skills[:10]) if skills else "Not specified"
        
        prompt = f"""
        Estimate the salary range for this job in USD. Consider the job title, location, experience level, and skills.
        
        Job Title: {job_title}
        Location: {location}
        Experience Level: {experience_level}
        Key Skills: {skills_text}
        
        Return a JSON object with "min" and "max" salary values in USD (annual).
        Example: {{"min": 80000, "max": 120000}}
        """
        
        try:
            response = await self.analyze_job_posting(prompt)
            if response:
                salary_data = json.loads(response)
                if isinstance(salary_data, dict) and 'min' in salary_data and 'max' in salary_data:
                    return salary_data
        except Exception as e:
            self.logger.error(f"Error estimating salary: {e}")
        
        return None
    
    async def analyze_job_requirements(self, description: str) -> Optional[Dict[str, Any]]:
        """Analyze job requirements and qualifications"""
        if not self.client:
            return None
        
        prompt = f"""
        Analyze this job description and extract structured information about requirements.
        
        Description: {description[:1500]}
        
        Return a JSON object with:
        - "education": Required education level
        - "experience_years": Required years of experience (number or range)
        - "required_skills": Array of must-have skills
        - "preferred_skills": Array of nice-to-have skills
        - "certifications": Array of required/preferred certifications
        - "soft_skills": Array of soft skills mentioned
        
        Example:
        {{
            "education": "Bachelor's degree in Computer Science",
            "experience_years": "3-5",
            "required_skills": ["Python", "SQL", "Git"],
            "preferred_skills": ["AWS", "Docker"],
            "certifications": ["AWS Certified"],
            "soft_skills": ["Communication", "Teamwork"]
        }}
        """
        
        try:
            response = await self.analyze_job_posting(prompt)
            if response:
                return json.loads(response)
        except Exception as e:
            self.logger.error(f"Error analyzing requirements: {e}")
        
        return None
    
    async def generate_job_summary(self, job_title: str, company: str, description: str) -> Optional[str]:
        """Generate a concise job summary"""
        if not self.client:
            return None
        
        prompt = f"""
        Create a concise 2-3 sentence summary of this job posting that highlights the key responsibilities and requirements.
        
        Job Title: {job_title}
        Company: {company}
        Description: {description[:1000]}
        
        Return only the summary text, no additional formatting.
        """
        
        try:
            response = await self.analyze_job_posting(prompt)
            return response.strip() if response else None
        except Exception as e:
            self.logger.error(f"Error generating summary: {e}")
        
        return None
    
    async def batch_create_embeddings(self, texts: List[str]) -> List[Optional[List[float]]]:
        """Create embeddings for multiple texts efficiently"""
        if not self.client or not texts:
            return [None] * len(texts)
        
        embeddings = []
        batch_size = 100  # OpenAI batch limit
        
        for i in range(0, len(texts), batch_size):
            batch = texts[i:i + batch_size]
            
            try:
                # Truncate texts in batch
                truncated_batch = []
                for text in batch:
                    if len(text) > 8000:
                        text = text[:8000] + "..."
                    truncated_batch.append(text)
                
                response = await self.client.embeddings.create(
                    model=self.embedding_model,
                    input=truncated_batch,
                    encoding_format="float"
                )
                
                batch_embeddings = [item.embedding for item in response.data]
                embeddings.extend(batch_embeddings)
                
                # Rate limiting
                await asyncio.sleep(1)
                
            except Exception as e:
                self.logger.error(f"Error in batch embedding creation: {e}")
                # Add None for failed batch
                embeddings.extend([None] * len(batch))
        
        return embeddings
    
    async def compare_job_similarity(self, job1_text: str, job2_text: str) -> Optional[float]:
        """Compare similarity between two job postings"""
        if not self.client:
            return None
        
        try:
            # Create embeddings for both jobs
            embedding1 = await self.create_embedding(job1_text)
            embedding2 = await self.create_embedding(job2_text)
            
            if embedding1 and embedding2:
                # Calculate cosine similarity
                import numpy as np
                
                vec1 = np.array(embedding1)
                vec2 = np.array(embedding2)
                
                # Cosine similarity
                similarity = np.dot(vec1, vec2) / (np.linalg.norm(vec1) * np.linalg.norm(vec2))
                return float(similarity)
            
        except Exception as e:
            self.logger.error(f"Error comparing job similarity: {e}")
        
        return None
    
    def is_available(self) -> bool:
        """Check if AI client is available and configured"""
        return self.client is not None
    
    async def test_connection(self) -> bool:
        """Test OpenAI API connection"""
        if not self.client:
            return False
        
        try:
            # Simple test with minimal token usage
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=[{"role": "user", "content": "Hello"}],
                max_tokens=5,
                timeout=10
            )
            return bool(response.choices)
        except Exception as e:
            self.logger.error(f"AI connection test failed: {e}")
            return False
