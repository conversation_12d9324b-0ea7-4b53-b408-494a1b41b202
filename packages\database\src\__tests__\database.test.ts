import { describe, it, expect, beforeAll, afterAll, beforeEach } from '@jest/globals';
import { prisma, connectDB, disconnectDB, healthCheck, cleanupDatabase, seedDatabase, withTransaction } from '../index';

describe('Database Connection and Operations', () => {
  beforeAll(async () => {
    await connectDB();
  });

  afterAll(async () => {
    await cleanupDatabase();
    await disconnectDB();
  });

  beforeEach(async () => {
    await cleanupDatabase();
  });

  describe('Connection Management', () => {
    it('should connect to database successfully', async () => {
      // Connection is already established in beforeAll
      const result = await prisma.$queryRaw`SELECT 1 as test`;
      expect(result).toBeDefined();
    });

    it('should perform health check successfully', async () => {
      const health = await healthCheck();
      
      expect(health.status).toBe('healthy');
      expect(health.timestamp).toBeDefined();
      expect(health.details).toBeDefined();
      expect(health.details?.connection).toBe(true);
      expect(health.details?.queryTime).toBeGreaterThan(0);
      expect(health.details?.version).toBeDefined();
    });
  });

  describe('User Operations', () => {
    it('should create a user successfully', async () => {
      const userData = {
        email: '<EMAIL>',
        name: 'Test User',
      };

      const user = await prisma.user.create({
        data: userData,
      });

      expect(user.id).toBeDefined();
      expect(user.email).toBe(userData.email);
      expect(user.name).toBe(userData.name);
      expect(user.createdAt).toBeDefined();
      expect(user.updatedAt).toBeDefined();
    });

    it('should create user with profile', async () => {
      const user = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          name: 'Profile User',
          profiles: {
            create: {
              firstName: 'John',
              lastName: 'Doe',
              phone: '+**********',
              location: 'New York, NY',
            },
          },
        },
        include: {
          profiles: true,
        },
      });

      expect(user.profiles).toHaveLength(1);
      expect(user.profiles[0].firstName).toBe('John');
      expect(user.profiles[0].lastName).toBe('Doe');
    });

    it('should enforce unique email constraint', async () => {
      const email = '<EMAIL>';
      
      await prisma.user.create({
        data: { email, name: 'First User' },
      });

      await expect(
        prisma.user.create({
          data: { email, name: 'Second User' },
        })
      ).rejects.toThrow();
    });
  });

  describe('Resume Operations', () => {
    let userId: string;

    beforeEach(async () => {
      const user = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          name: 'Resume User',
        },
      });
      userId = user.id;
    });

    it('should create a resume with all sections', async () => {
      const resume = await prisma.resume.create({
        data: {
          userId,
          title: 'Software Engineer Resume',
          description: 'My professional resume',
          experiences: {
            create: [
              {
                company: 'Tech Corp',
                position: 'Senior Developer',
                location: 'San Francisco, CA',
                startDate: new Date('2022-01-01'),
                endDate: new Date('2023-12-31'),
                description: 'Led development of web applications',
                achievements: ['Increased performance by 40%', 'Led team of 5 developers'],
                displayOrder: 0,
              },
            ],
          },
          educations: {
            create: [
              {
                institution: 'University of Technology',
                degree: 'Bachelor of Science',
                field: 'Computer Science',
                location: 'Boston, MA',
                startDate: new Date('2018-09-01'),
                endDate: new Date('2022-05-31'),
                gpa: '3.8',
                displayOrder: 0,
              },
            ],
          },
          skills: {
            create: [
              {
                name: 'JavaScript',
                category: 'Programming Languages',
                level: 'EXPERT',
                displayOrder: 0,
              },
              {
                name: 'React',
                category: 'Frameworks',
                level: 'ADVANCED',
                displayOrder: 1,
              },
            ],
          },
          projects: {
            create: [
              {
                name: 'E-commerce Platform',
                description: 'Built a full-stack e-commerce solution',
                url: 'https://example.com',
                githubUrl: 'https://github.com/user/project',
                technologies: ['React', 'Node.js', 'PostgreSQL'],
                startDate: new Date('2023-01-01'),
                endDate: new Date('2023-06-30'),
                displayOrder: 0,
              },
            ],
          },
        },
        include: {
          experiences: true,
          educations: true,
          skills: true,
          projects: true,
        },
      });

      expect(resume.id).toBeDefined();
      expect(resume.title).toBe('Software Engineer Resume');
      expect(resume.experiences).toHaveLength(1);
      expect(resume.educations).toHaveLength(1);
      expect(resume.skills).toHaveLength(2);
      expect(resume.projects).toHaveLength(1);
      
      // Verify experience data
      expect(resume.experiences[0].company).toBe('Tech Corp');
      expect(resume.experiences[0].achievements).toEqual(['Increased performance by 40%', 'Led team of 5 developers']);
      
      // Verify skill levels
      expect(resume.skills.find(s => s.name === 'JavaScript')?.level).toBe('EXPERT');
      expect(resume.skills.find(s => s.name === 'React')?.level).toBe('ADVANCED');
    });

    it('should handle resume status transitions', async () => {
      const resume = await prisma.resume.create({
        data: {
          userId,
          title: 'Draft Resume',
          status: 'DRAFT',
        },
      });

      expect(resume.status).toBe('DRAFT');

      const publishedResume = await prisma.resume.update({
        where: { id: resume.id },
        data: { status: 'PUBLISHED' },
      });

      expect(publishedResume.status).toBe('PUBLISHED');
    });

    it('should generate unique public URLs', async () => {
      const resume1 = await prisma.resume.create({
        data: {
          userId,
          title: 'Public Resume 1',
          isPublic: true,
          publicUrl: 'unique-url-1',
        },
      });

      const resume2 = await prisma.resume.create({
        data: {
          userId,
          title: 'Public Resume 2',
          isPublic: true,
          publicUrl: 'unique-url-2',
        },
      });

      expect(resume1.publicUrl).toBe('unique-url-1');
      expect(resume2.publicUrl).toBe('unique-url-2');

      // Should enforce unique constraint
      await expect(
        prisma.resume.create({
          data: {
            userId,
            title: 'Duplicate URL',
            publicUrl: 'unique-url-1',
          },
        })
      ).rejects.toThrow();
    });
  });

  describe('Template Operations', () => {
    it('should seed templates successfully', async () => {
      await seedDatabase();

      const templates = await prisma.template.findMany();
      expect(templates.length).toBeGreaterThanOrEqual(2);

      const modernTemplate = templates.find(t => t.name === 'Modern Professional');
      expect(modernTemplate).toBeDefined();
      expect(modernTemplate?.isPremium).toBe(false);
      expect(modernTemplate?.isActive).toBe(true);
      expect(modernTemplate?.config).toBeDefined();
    });

    it('should validate template configuration structure', async () => {
      await seedDatabase();

      const template = await prisma.template.findFirst();
      expect(template?.config).toBeDefined();
      
      const config = template?.config as any;
      expect(config.layout).toBeDefined();
      expect(config.colors).toBeDefined();
      expect(config.fonts).toBeDefined();
      expect(config.spacing).toBeDefined();
      expect(config.sections).toBeDefined();
    });
  });

  describe('Transaction Operations', () => {
    let userId: string;

    beforeEach(async () => {
      const user = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          name: 'Transaction User',
        },
      });
      userId = user.id;
    });

    it('should handle successful transactions', async () => {
      const result = await withTransaction(async (tx) => {
        const resume = await tx.resume.create({
          data: {
            userId,
            title: 'Transaction Resume',
          },
        });

        await tx.experience.create({
          data: {
            resumeId: resume.id,
            company: 'Transaction Corp',
            position: 'Developer',
            startDate: new Date(),
            displayOrder: 0,
          },
        });

        return resume;
      });

      expect(result.id).toBeDefined();
      
      // Verify both records were created
      const resume = await prisma.resume.findUnique({
        where: { id: result.id },
        include: { experiences: true },
      });

      expect(resume?.experiences).toHaveLength(1);
    });

    it('should rollback failed transactions', async () => {
      await expect(
        withTransaction(async (tx) => {
          await tx.resume.create({
            data: {
              userId,
              title: 'Failed Transaction Resume',
            },
          });

          // This should cause the transaction to fail
          throw new Error('Intentional failure');
        })
      ).rejects.toThrow('Intentional failure');

      // Verify no resume was created
      const resumes = await prisma.resume.findMany({
        where: { userId },
      });

      expect(resumes).toHaveLength(0);
    });
  });

  describe('AI Generation Tracking', () => {
    let userId: string;

    beforeEach(async () => {
      const user = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          name: 'AI User',
        },
      });
      userId = user.id;
    });

    it('should track AI content generation', async () => {
      const aiGeneration = await prisma.aiGeneration.create({
        data: {
          userId,
          type: 'SUMMARY',
          prompt: 'Generate a professional summary for a software engineer',
          response: 'Experienced software engineer with 5+ years...',
          metadata: {
            model: 'gpt-4',
            tokens: 150,
            processingTime: 2.5,
          },
        },
      });

      expect(aiGeneration.id).toBeDefined();
      expect(aiGeneration.type).toBe('SUMMARY');
      expect(aiGeneration.metadata).toBeDefined();
    });

    it('should support different AI generation types', async () => {
      const types = ['BULLET_POINT', 'SUMMARY', 'COVER_LETTER', 'REWRITE', 'KEYWORDS'] as const;

      for (const type of types) {
        await prisma.aiGeneration.create({
          data: {
            userId,
            type,
            prompt: `Test prompt for ${type}`,
            response: `Test response for ${type}`,
          },
        });
      }

      const generations = await prisma.aiGeneration.findMany({
        where: { userId },
      });

      expect(generations).toHaveLength(types.length);
      expect(generations.map(g => g.type)).toEqual(expect.arrayContaining(types));
    });
  });
});
