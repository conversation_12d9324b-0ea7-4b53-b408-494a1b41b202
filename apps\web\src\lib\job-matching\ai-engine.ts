/**
 * AI Recommendation Engine
 * 
 * Advanced AI-powered job matching and recommendation algorithms
 */

import { z } from 'zod'

// AI engine schemas
export const SkillExtractionSchema = z.object({
  skills: z.array(z.object({
    name: z.string(),
    category: z.string(),
    confidence: z.number(),
    context: z.string().optional()
  })),
  experience: z.array(z.object({
    title: z.string(),
    company: z.string(),
    duration: z.string(),
    skills: z.array(z.string())
  })),
  education: z.array(z.object({
    degree: z.string(),
    field: z.string(),
    institution: z.string()
  }))
})

export const JobAnalysisSchema = z.object({
  requiredSkills: z.array(z.string()),
  preferredSkills: z.array(z.string()),
  experienceLevel: z.string(),
  responsibilities: z.array(z.string()),
  qualifications: z.array(z.string()),
  benefits: z.array(z.string()),
  companyInfo: z.object({
    size: z.string().optional(),
    industry: z.string().optional(),
    culture: z.array(z.string()).optional()
  })
})

export const CompatibilityAnalysisSchema = z.object({
  overallScore: z.number(),
  skillsScore: z.number(),
  experienceScore: z.number(),
  educationScore: z.number(),
  locationScore: z.number(),
  salaryScore: z.number(),
  strengths: z.array(z.string()),
  weaknesses: z.array(z.string()),
  recommendations: z.array(z.string())
})

export type SkillExtraction = z.infer<typeof SkillExtractionSchema>
export type JobAnalysis = z.infer<typeof JobAnalysisSchema>
export type CompatibilityAnalysis = z.infer<typeof CompatibilityAnalysisSchema>

export interface ResumeData {
  personalInfo: any
  sections: any
  title: string
}

export interface JobPostingData {
  title: string
  company: string
  description: string
  requirements?: string
  location?: string
  salaryMin?: number
  salaryMax?: number
  skills?: string[]
}

export interface CareerPathPrediction {
  nextRoles: Array<{
    title: string
    probability: number
    timeframe: string
    requiredSkills: string[]
    salaryRange: { min: number; max: number }
  }>
  skillGaps: Array<{
    skill: string
    importance: number
    learningResources: string[]
  }>
  marketTrends: Array<{
    trend: string
    impact: string
    timeline: string
  }>
}

export class AIRecommendationEngine {
  private skillKeywords: Map<string, string[]>
  private experienceLevels: Map<string, number>

  constructor() {
    this.initializeSkillKeywords()
    this.initializeExperienceLevels()
  }

  /**
   * Extract skills and experience from resume
   */
  async extractSkillsFromResume(resume: ResumeData): Promise<SkillExtraction> {
    const skills: Array<{ name: string; category: string; confidence: number; context?: string }> = []
    const experience: Array<{ title: string; company: string; duration: string; skills: string[] }> = []
    const education: Array<{ degree: string; field: string; institution: string }> = []

    // Extract from resume sections
    if (resume.sections) {
      const sections = typeof resume.sections === 'string' ? JSON.parse(resume.sections) : resume.sections

      // Extract skills from skills section
      if (sections.skills) {
        for (const skillGroup of sections.skills) {
          if (skillGroup.items) {
            for (const skill of skillGroup.items) {
              const category = this.categorizeSkill(skill.name || skill)
              skills.push({
                name: skill.name || skill,
                category,
                confidence: 0.9,
                context: skillGroup.name
              })
            }
          }
        }
      }

      // Extract experience
      if (sections.experience) {
        for (const exp of sections.experience) {
          const extractedSkills = this.extractSkillsFromText(exp.description || '')
          experience.push({
            title: exp.position || exp.title || '',
            company: exp.company || '',
            duration: this.formatDuration(exp.startDate, exp.endDate),
            skills: extractedSkills
          })

          // Add skills found in experience
          for (const skill of extractedSkills) {
            if (!skills.find(s => s.name.toLowerCase() === skill.toLowerCase())) {
              skills.push({
                name: skill,
                category: this.categorizeSkill(skill),
                confidence: 0.7,
                context: 'experience'
              })
            }
          }
        }
      }

      // Extract education
      if (sections.education) {
        for (const edu of sections.education) {
          education.push({
            degree: edu.degree || '',
            field: edu.field || edu.major || '',
            institution: edu.institution || edu.school || ''
          })
        }
      }
    }

    return {
      skills,
      experience,
      education
    }
  }

  /**
   * Analyze job posting requirements
   */
  async analyzeJobPosting(jobPosting: JobPostingData): Promise<JobAnalysis> {
    const description = jobPosting.description + ' ' + (jobPosting.requirements || '')
    
    const requiredSkills = this.extractSkillsFromText(description, true)
    const preferredSkills = this.extractSkillsFromText(description, false)
    const responsibilities = this.extractResponsibilities(description)
    const qualifications = this.extractQualifications(description)
    const benefits = this.extractBenefits(description)

    return {
      requiredSkills,
      preferredSkills,
      experienceLevel: this.determineExperienceLevel(description),
      responsibilities,
      qualifications,
      benefits,
      companyInfo: {
        size: this.extractCompanySize(description),
        industry: this.extractIndustry(jobPosting.company, description),
        culture: this.extractCultureKeywords(description)
      }
    }
  }

  /**
   * Analyze compatibility between resume and job
   */
  async analyzeJobCompatibility(resume: ResumeData, jobPosting: JobPostingData): Promise<CompatibilityAnalysis> {
    const resumeAnalysis = await this.extractSkillsFromResume(resume)
    const jobAnalysis = await this.analyzeJobPosting(jobPosting)

    // Calculate skill match score
    const userSkills = resumeAnalysis.skills.map(s => s.name.toLowerCase())
    const requiredSkills = jobAnalysis.requiredSkills.map(s => s.toLowerCase())
    const preferredSkills = jobAnalysis.preferredSkills.map(s => s.toLowerCase())

    const requiredMatches = requiredSkills.filter(skill => 
      userSkills.some(userSkill => this.skillsMatch(userSkill, skill))
    )
    const preferredMatches = preferredSkills.filter(skill => 
      userSkills.some(userSkill => this.skillsMatch(userSkill, skill))
    )

    const skillsScore = Math.round(
      (requiredMatches.length / Math.max(requiredSkills.length, 1)) * 70 +
      (preferredMatches.length / Math.max(preferredSkills.length, 1)) * 30
    )

    // Calculate experience score
    const experienceScore = this.calculateExperienceScore(resumeAnalysis.experience, jobAnalysis.experienceLevel)

    // Calculate education score
    const educationScore = this.calculateEducationScore(resumeAnalysis.education, jobAnalysis.qualifications)

    // Calculate location score (simplified)
    const locationScore = 85 // Assume good match for now

    // Calculate salary score
    const salaryScore = this.calculateSalaryScore(jobPosting.salaryMin, jobPosting.salaryMax)

    // Overall score (weighted average)
    const overallScore = Math.round(
      skillsScore * 0.4 +
      experienceScore * 0.3 +
      educationScore * 0.15 +
      locationScore * 0.1 +
      salaryScore * 0.05
    )

    // Generate strengths and weaknesses
    const strengths: string[] = []
    const weaknesses: string[] = []
    const recommendations: string[] = []

    if (skillsScore >= 70) {
      strengths.push('Strong skill match with job requirements')
    } else {
      weaknesses.push('Some required skills are missing')
      recommendations.push('Consider developing skills in: ' + requiredSkills.filter(skill => 
        !userSkills.some(userSkill => this.skillsMatch(userSkill, skill))
      ).slice(0, 3).join(', '))
    }

    if (experienceScore >= 70) {
      strengths.push('Experience level aligns well with job requirements')
    } else {
      weaknesses.push('Experience level may not fully match requirements')
      recommendations.push('Highlight transferable skills and relevant projects')
    }

    if (educationScore >= 70) {
      strengths.push('Educational background is relevant')
    }

    return {
      overallScore,
      skillsScore,
      experienceScore,
      educationScore,
      locationScore,
      salaryScore,
      strengths,
      weaknesses,
      recommendations
    }
  }

  /**
   * Predict career path based on user profile
   */
  async predictCareerPath(userProfile: any): Promise<CareerPathPrediction> {
    // This would typically use ML models, but for now we'll use rule-based logic
    const currentRole = userProfile.currentTitle || 'Software Developer'
    const skills = userProfile.skills || []
    const experience = userProfile.experience || []

    const nextRoles = this.generateNextRoles(currentRole, skills, experience)
    const skillGaps = this.identifySkillGaps(skills, nextRoles)
    const marketTrends = this.getMarketTrends(currentRole)

    return {
      nextRoles,
      skillGaps,
      marketTrends
    }
  }

  /**
   * Generate interview questions based on job posting
   */
  async generateInterviewQuestions(jobPosting: JobPostingData, userProfile: any): Promise<string[]> {
    const questions: string[] = []
    const jobAnalysis = await this.analyzeJobPosting(jobPosting)

    // General questions
    questions.push(`Tell me about your experience with ${jobPosting.title} roles.`)
    questions.push(`Why are you interested in working at ${jobPosting.company}?`)

    // Skill-based questions
    for (const skill of jobAnalysis.requiredSkills.slice(0, 3)) {
      questions.push(`Can you describe your experience with ${skill}?`)
    }

    // Behavioral questions
    questions.push('Describe a challenging project you worked on and how you overcame obstacles.')
    questions.push('How do you handle working under tight deadlines?')
    questions.push('Tell me about a time you had to learn a new technology quickly.')

    // Company-specific questions
    if (jobAnalysis.companyInfo.culture) {
      questions.push('How do you handle working in a collaborative team environment?')
    }

    return questions
  }

  // Private helper methods
  private initializeSkillKeywords(): void {
    this.skillKeywords = new Map([
      ['programming', ['javascript', 'python', 'java', 'c++', 'c#', 'php', 'ruby', 'go', 'rust', 'swift']],
      ['web', ['react', 'angular', 'vue', 'html', 'css', 'node.js', 'express', 'django', 'flask']],
      ['database', ['sql', 'mysql', 'postgresql', 'mongodb', 'redis', 'elasticsearch']],
      ['cloud', ['aws', 'azure', 'gcp', 'docker', 'kubernetes', 'terraform']],
      ['mobile', ['ios', 'android', 'react native', 'flutter', 'swift', 'kotlin']],
      ['data', ['machine learning', 'data science', 'pandas', 'numpy', 'tensorflow', 'pytorch']],
      ['design', ['figma', 'sketch', 'adobe', 'ui/ux', 'photoshop', 'illustrator']],
      ['project management', ['agile', 'scrum', 'kanban', 'jira', 'confluence', 'project management']]
    ])
  }

  private initializeExperienceLevels(): void {
    this.experienceLevels = new Map([
      ['entry', 0],
      ['junior', 1],
      ['mid', 3],
      ['senior', 5],
      ['lead', 7],
      ['principal', 10],
      ['executive', 15]
    ])
  }

  private categorizeSkill(skill: string): string {
    const skillLower = skill.toLowerCase()
    
    for (const [category, keywords] of this.skillKeywords) {
      if (keywords.some(keyword => skillLower.includes(keyword) || keyword.includes(skillLower))) {
        return category
      }
    }
    
    return 'other'
  }

  private extractSkillsFromText(text: string, requiredOnly = false): string[] {
    const skills: string[] = []
    const textLower = text.toLowerCase()

    // Look for skill keywords
    for (const [category, keywords] of this.skillKeywords) {
      for (const keyword of keywords) {
        if (textLower.includes(keyword)) {
          skills.push(keyword)
        }
      }
    }

    // Look for common skill patterns
    const skillPatterns = [
      /\b([A-Z][a-z]+(?:\.[a-z]+)*)\b/g, // Technology names like React.js
      /\b([a-z]+(?:-[a-z]+)*)\b/g // Hyphenated skills
    ]

    for (const pattern of skillPatterns) {
      const matches = text.match(pattern)
      if (matches) {
        skills.push(...matches.slice(0, 5)) // Limit to avoid noise
      }
    }

    return [...new Set(skills)] // Remove duplicates
  }

  private extractResponsibilities(text: string): string[] {
    const responsibilities: string[] = []
    const lines = text.split('\n')

    for (const line of lines) {
      if (line.trim().match(/^[•\-\*]/) || line.includes('responsible for') || line.includes('will')) {
        responsibilities.push(line.trim())
      }
    }

    return responsibilities.slice(0, 10) // Limit to top 10
  }

  private extractQualifications(text: string): string[] {
    const qualifications: string[] = []
    const qualificationKeywords = ['degree', 'bachelor', 'master', 'phd', 'certification', 'experience', 'years']

    const lines = text.split('\n')
    for (const line of lines) {
      if (qualificationKeywords.some(keyword => line.toLowerCase().includes(keyword))) {
        qualifications.push(line.trim())
      }
    }

    return qualifications.slice(0, 5)
  }

  private extractBenefits(text: string): string[] {
    const benefits: string[] = []
    const benefitKeywords = ['health', 'dental', 'vision', '401k', 'vacation', 'pto', 'remote', 'flexible']

    for (const keyword of benefitKeywords) {
      if (text.toLowerCase().includes(keyword)) {
        benefits.push(keyword)
      }
    }

    return benefits
  }

  private determineExperienceLevel(text: string): string {
    const textLower = text.toLowerCase()
    
    if (textLower.includes('entry') || textLower.includes('junior') || textLower.includes('0-2 years')) {
      return 'entry'
    } else if (textLower.includes('senior') || textLower.includes('5+ years')) {
      return 'senior'
    } else if (textLower.includes('lead') || textLower.includes('principal')) {
      return 'lead'
    } else {
      return 'mid'
    }
  }

  private extractCompanySize(text: string): string | undefined {
    if (text.includes('startup') || text.includes('small')) return 'small'
    if (text.includes('enterprise') || text.includes('large')) return 'large'
    return undefined
  }

  private extractIndustry(company: string, text: string): string | undefined {
    const industries = ['technology', 'healthcare', 'finance', 'education', 'retail', 'manufacturing']
    const textLower = (company + ' ' + text).toLowerCase()
    
    return industries.find(industry => textLower.includes(industry))
  }

  private extractCultureKeywords(text: string): string[] {
    const cultureKeywords = ['collaborative', 'innovative', 'fast-paced', 'flexible', 'remote-friendly']
    const textLower = text.toLowerCase()
    
    return cultureKeywords.filter(keyword => textLower.includes(keyword))
  }

  private skillsMatch(userSkill: string, jobSkill: string): boolean {
    return userSkill.includes(jobSkill) || jobSkill.includes(userSkill) || 
           this.calculateStringSimilarity(userSkill, jobSkill) > 0.8
  }

  private calculateStringSimilarity(str1: string, str2: string): number {
    const longer = str1.length > str2.length ? str1 : str2
    const shorter = str1.length > str2.length ? str2 : str1
    
    if (longer.length === 0) return 1.0
    
    const editDistance = this.levenshteinDistance(longer, shorter)
    return (longer.length - editDistance) / longer.length
  }

  private levenshteinDistance(str1: string, str2: string): number {
    const matrix = []
    
    for (let i = 0; i <= str2.length; i++) {
      matrix[i] = [i]
    }
    
    for (let j = 0; j <= str1.length; j++) {
      matrix[0][j] = j
    }
    
    for (let i = 1; i <= str2.length; i++) {
      for (let j = 1; j <= str1.length; j++) {
        if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
          matrix[i][j] = matrix[i - 1][j - 1]
        } else {
          matrix[i][j] = Math.min(
            matrix[i - 1][j - 1] + 1,
            matrix[i][j - 1] + 1,
            matrix[i - 1][j] + 1
          )
        }
      }
    }
    
    return matrix[str2.length][str1.length]
  }

  private calculateExperienceScore(userExperience: any[], requiredLevel: string): number {
    const userYears = userExperience.length * 1.5 // Rough estimate
    const requiredYears = this.experienceLevels.get(requiredLevel) || 3
    
    if (userYears >= requiredYears) {
      return 100
    } else {
      return Math.round((userYears / requiredYears) * 100)
    }
  }

  private calculateEducationScore(userEducation: any[], qualifications: string[]): number {
    if (userEducation.length === 0) return 50 // Neutral score
    
    const hasRelevantDegree = userEducation.some(edu => 
      qualifications.some(qual => 
        qual.toLowerCase().includes(edu.field.toLowerCase()) ||
        qual.toLowerCase().includes(edu.degree.toLowerCase())
      )
    )
    
    return hasRelevantDegree ? 90 : 70
  }

  private calculateSalaryScore(salaryMin?: number, salaryMax?: number): number {
    // Simplified salary scoring - in reality this would consider user's salary expectations
    return 80
  }

  private formatDuration(startDate: string, endDate: string): string {
    if (!startDate) return 'Unknown'
    if (!endDate || endDate === 'Present') return `${startDate} - Present`
    return `${startDate} - ${endDate}`
  }

  private generateNextRoles(currentRole: string, skills: string[], experience: any[]): any[] {
    // Simplified career progression logic
    const progressionMap: Record<string, string[]> = {
      'Software Developer': ['Senior Software Developer', 'Tech Lead', 'Software Architect'],
      'Senior Software Developer': ['Tech Lead', 'Engineering Manager', 'Principal Engineer'],
      'Data Analyst': ['Senior Data Analyst', 'Data Scientist', 'Analytics Manager'],
      'Product Manager': ['Senior Product Manager', 'Director of Product', 'VP of Product']
    }

    const nextTitles = progressionMap[currentRole] || ['Senior ' + currentRole]
    
    return nextTitles.map(title => ({
      title,
      probability: 0.7,
      timeframe: '1-2 years',
      requiredSkills: ['leadership', 'communication', 'strategic thinking'],
      salaryRange: { min: 80000, max: 120000 }
    }))
  }

  private identifySkillGaps(userSkills: string[], nextRoles: any[]): any[] {
    const allRequiredSkills = nextRoles.flatMap(role => role.requiredSkills)
    const missingSkills = allRequiredSkills.filter(skill => 
      !userSkills.some(userSkill => userSkill.toLowerCase().includes(skill.toLowerCase()))
    )

    return missingSkills.map(skill => ({
      skill,
      importance: 0.8,
      learningResources: ['Online courses', 'Certification programs', 'Mentorship']
    }))
  }

  private getMarketTrends(currentRole: string): any[] {
    return [
      {
        trend: 'AI and Machine Learning integration',
        impact: 'High demand for AI skills',
        timeline: '2024-2025'
      },
      {
        trend: 'Remote work normalization',
        impact: 'Increased competition for remote positions',
        timeline: 'Ongoing'
      },
      {
        trend: 'Cloud-first development',
        impact: 'Cloud skills becoming essential',
        timeline: '2024-2026'
      }
    ]
  }
}

export const aiRecommendationEngine = new AIRecommendationEngine()
