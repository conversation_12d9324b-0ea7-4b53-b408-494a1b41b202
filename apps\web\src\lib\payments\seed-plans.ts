/**
 * Subscription Plans Seed Data
 * 
 * Creates default subscription plans for CareerCraft SaaS
 */

import { prisma } from '@/lib/db'

export const SUBSCRIPTION_PLANS = [
  {
    id: 'free',
    name: 'Free',
    description: 'Perfect for getting started with resume building',
    priceMonthly: 0,
    priceYearly: 0,
    stripePriceIdMonthly: null,
    stripePriceIdYearly: null,
    features: JSON.stringify({
      // Resume Management
      maxResumes: 1,
      maxTemplates: 3,
      customTemplates: false,
      templateMarketplaceSelling: false,
      
      // AI Features
      aiSuggestionsLimit: 5,
      advancedAI: false,
      jobMatching: false,
      interviewPrep: false,
      
      // Collaboration
      maxCollaborators: 0,
      realTimeEditing: false,
      versionControl: false,
      commentSystem: false,
      
      // Export & Sharing
      pdfExport: true,
      customBranding: false,
      publicProfiles: false,
      linkedinIntegration: false,
      
      // Analytics & Insights
      basicAnalytics: true,
      advancedAnalytics: false,
      marketInsights: false,
      
      // Support & Services
      supportLevel: 'standard',
      apiAccess: false,
      ssoIntegration: false
    }),
    maxResumes: 1,
    maxTemplates: 3,
    maxCollaborators: 0,
    aiSuggestionsLimit: 5,
    isActive: true,
    sortOrder: 1
  },
  {
    id: 'pro',
    name: 'Pro',
    description: 'Ideal for professionals and job seekers',
    priceMonthly: 9.99,
    priceYearly: 99.99,
    stripePriceIdMonthly: 'price_pro_monthly', // Replace with actual Stripe price IDs
    stripePriceIdYearly: 'price_pro_yearly',
    features: JSON.stringify({
      // Resume Management
      maxResumes: -1, // unlimited
      maxTemplates: -1, // unlimited
      customTemplates: true,
      templateMarketplaceSelling: false,
      
      // AI Features
      aiSuggestionsLimit: -1, // unlimited
      advancedAI: true,
      jobMatching: true,
      interviewPrep: true,
      
      // Collaboration
      maxCollaborators: 3,
      realTimeEditing: true,
      versionControl: true,
      commentSystem: true,
      
      // Export & Sharing
      pdfExport: true,
      customBranding: true,
      publicProfiles: true,
      linkedinIntegration: true,
      
      // Analytics & Insights
      basicAnalytics: true,
      advancedAnalytics: false,
      marketInsights: false,
      
      // Support & Services
      supportLevel: 'priority',
      apiAccess: false,
      ssoIntegration: false
    }),
    maxResumes: -1,
    maxTemplates: -1,
    maxCollaborators: 3,
    aiSuggestionsLimit: -1,
    isActive: true,
    sortOrder: 2
  },
  {
    id: 'business',
    name: 'Business',
    description: 'Perfect for teams and growing businesses',
    priceMonthly: 19.99,
    priceYearly: 199.99,
    stripePriceIdMonthly: 'price_business_monthly',
    stripePriceIdYearly: 'price_business_yearly',
    features: JSON.stringify({
      // Resume Management
      maxResumes: -1, // unlimited
      maxTemplates: -1, // unlimited
      customTemplates: true,
      templateMarketplaceSelling: true,
      
      // AI Features
      aiSuggestionsLimit: -1, // unlimited
      advancedAI: true,
      jobMatching: true,
      interviewPrep: true,
      
      // Collaboration
      maxCollaborators: -1, // unlimited
      realTimeEditing: true,
      versionControl: true,
      commentSystem: true,
      
      // Export & Sharing
      pdfExport: true,
      customBranding: true,
      publicProfiles: true,
      linkedinIntegration: true,
      
      // Analytics & Insights
      basicAnalytics: true,
      advancedAnalytics: true,
      marketInsights: true,
      
      // Support & Services
      supportLevel: 'priority',
      apiAccess: true,
      ssoIntegration: false
    }),
    maxResumes: -1,
    maxTemplates: -1,
    maxCollaborators: -1,
    aiSuggestionsLimit: -1,
    isActive: true,
    sortOrder: 3
  },
  {
    id: 'enterprise',
    name: 'Enterprise',
    description: 'Advanced features for large organizations',
    priceMonthly: 99.99,
    priceYearly: 999.99,
    stripePriceIdMonthly: 'price_enterprise_monthly',
    stripePriceIdYearly: 'price_enterprise_yearly',
    features: JSON.stringify({
      // Resume Management
      maxResumes: -1, // unlimited
      maxTemplates: -1, // unlimited
      customTemplates: true,
      templateMarketplaceSelling: true,
      
      // AI Features
      aiSuggestionsLimit: -1, // unlimited
      advancedAI: true,
      jobMatching: true,
      interviewPrep: true,
      
      // Collaboration
      maxCollaborators: -1, // unlimited
      realTimeEditing: true,
      versionControl: true,
      commentSystem: true,
      
      // Export & Sharing
      pdfExport: true,
      customBranding: true,
      publicProfiles: true,
      linkedinIntegration: true,
      
      // Analytics & Insights
      basicAnalytics: true,
      advancedAnalytics: true,
      marketInsights: true,
      
      // Support & Services
      supportLevel: 'dedicated',
      apiAccess: true,
      ssoIntegration: true
    }),
    maxResumes: -1,
    maxTemplates: -1,
    maxCollaborators: -1,
    aiSuggestionsLimit: -1,
    isActive: true,
    sortOrder: 4
  }
]

export const SAMPLE_COUPONS = [
  {
    id: 'welcome20',
    code: 'WELCOME20',
    name: '20% Off First Month',
    description: 'Get 20% off your first month subscription',
    discountType: 'percentage',
    discountValue: 20,
    stripeCouponId: 'welcome20_coupon',
    maxRedemptions: 1000,
    currentRedemptions: 0,
    validFrom: new Date(),
    validUntil: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000), // 90 days from now
    isActive: true
  },
  {
    id: 'student50',
    code: 'STUDENT50',
    name: '50% Student Discount',
    description: 'Special discount for students',
    discountType: 'percentage',
    discountValue: 50,
    stripeCouponId: 'student50_coupon',
    maxRedemptions: 500,
    currentRedemptions: 0,
    validFrom: new Date(),
    validUntil: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year from now
    isActive: true
  },
  {
    id: 'blackfriday',
    code: 'BLACKFRIDAY',
    name: 'Black Friday Special',
    description: '30% off all plans for Black Friday',
    discountType: 'percentage',
    discountValue: 30,
    stripeCouponId: 'blackfriday_coupon',
    maxRedemptions: 2000,
    currentRedemptions: 0,
    validFrom: new Date('2024-11-25'),
    validUntil: new Date('2024-11-30'),
    isActive: false // Will be activated during Black Friday
  }
]

/**
 * Seed subscription plans in the database
 */
export async function seedSubscriptionPlans() {
  try {
    console.log('Seeding subscription plans...')

    // Create or update subscription plans
    for (const plan of SUBSCRIPTION_PLANS) {
      await prisma.subscriptionPlan.upsert({
        where: { id: plan.id },
        update: plan,
        create: plan
      })
      console.log(`✅ Created/updated plan: ${plan.name}`)
    }

    // Create sample coupons
    for (const coupon of SAMPLE_COUPONS) {
      await prisma.coupon.upsert({
        where: { id: coupon.id },
        update: coupon,
        create: coupon
      })
      console.log(`✅ Created/updated coupon: ${coupon.code}`)
    }

    console.log('✅ Subscription plans seeded successfully!')
  } catch (error) {
    console.error('❌ Error seeding subscription plans:', error)
    throw error
  }
}

/**
 * Remove all subscription plans (for testing)
 */
export async function clearSubscriptionPlans() {
  try {
    console.log('Clearing subscription plans...')

    await prisma.couponUsage.deleteMany()
    await prisma.coupon.deleteMany()
    await prisma.featureUsage.deleteMany()
    await prisma.payment.deleteMany()
    await prisma.userSubscription.deleteMany()
    await prisma.subscriptionPlan.deleteMany()

    console.log('✅ Subscription plans cleared successfully!')
  } catch (error) {
    console.error('❌ Error clearing subscription plans:', error)
    throw error
  }
}

// Run seeding if this file is executed directly
if (require.main === module) {
  seedSubscriptionPlans()
    .then(() => {
      console.log('Seeding completed!')
      process.exit(0)
    })
    .catch((error) => {
      console.error('Seeding failed:', error)
      process.exit(1)
    })
}
