/**
 * Payment System & SaaS Monetization Validation Script
 * 
 * Validates Stripe integration, subscription management, and feature access control
 */

const fs = require('fs')
const path = require('path')

class PaymentSystemValidator {
  constructor() {
    this.results = {
      files: { checked: 0, missing: 0, present: 0 },
      services: { checked: 0, missing: 0, present: 0 },
      apis: { checked: 0, missing: 0, present: 0 },
      components: { checked: 0, missing: 0, present: 0 },
      tests: { checked: 0, missing: 0, present: 0 },
      config: { checked: 0, missing: 0, present: 0 }
    }
  }

  log(message, type = 'info') {
    const colors = {
      info: '\x1b[36m',    // Cyan
      success: '\x1b[32m', // Green
      error: '\x1b[31m',   // Red
      warning: '\x1b[33m', // Yellow
      reset: '\x1b[0m'     // Reset
    }
    
    console.log(`${colors[type]}${message}${colors.reset}`)
  }

  checkFileExists(filePath, description) {
    const fullPath = path.join(__dirname, filePath)
    const exists = fs.existsSync(fullPath)
    
    if (exists) {
      this.log(`✅ ${description}: Found`, 'success')
      return true
    } else {
      this.log(`❌ ${description}: Missing (${filePath})`, 'error')
      return false
    }
  }

  checkFileContent(filePath, searchTerms, description) {
    const fullPath = path.join(__dirname, filePath)
    
    if (!fs.existsSync(fullPath)) {
      this.log(`❌ ${description}: File not found`, 'error')
      return false
    }

    try {
      const content = fs.readFileSync(fullPath, 'utf8')
      const missingTerms = searchTerms.filter(term => !content.includes(term))
      
      if (missingTerms.length === 0) {
        this.log(`✅ ${description}: All required content found`, 'success')
        return true
      } else {
        this.log(`⚠️  ${description}: Missing content: ${missingTerms.join(', ')}`, 'warning')
        return false
      }
    } catch (error) {
      this.log(`❌ ${description}: Error reading file - ${error.message}`, 'error')
      return false
    }
  }

  validateFileStructure() {
    this.log('\n💳 Validating Payment System File Structure...', 'info')
    
    const requiredFiles = [
      'src/lib/payments/stripe-service.ts',
      'src/lib/payments/subscription-service.ts',
      'src/lib/payments/feature-gate.ts',
      'src/lib/payments/webhook-handler.ts',
      'src/lib/payments/seed-plans.ts',
      'src/app/api/payments/route.ts',
      'src/app/api/webhooks/stripe/route.ts',
      'src/app/api/features/route.ts',
      'src/components/payments/PricingPage.tsx',
      'src/components/payments/BillingDashboard.tsx',
      'src/components/payments/FeatureGate.tsx',
      'src/test/payments/payment-system.test.ts',
      'docs/features/payment-system-spec.md'
    ]

    let present = 0
    let total = requiredFiles.length

    requiredFiles.forEach(file => {
      this.results.files.checked++
      if (this.checkFileExists(file, `File: ${file}`)) {
        present++
        this.results.files.present++
      } else {
        this.results.files.missing++
      }
    })

    this.log(`📊 File Structure: ${present}/${total} files present`, present === total ? 'success' : 'warning')
    return present === total
  }

  validatePaymentServices() {
    this.log('\n💰 Validating Payment Services...', 'info')
    
    const serviceChecks = [
      {
        file: 'src/lib/payments/stripe-service.ts',
        description: 'Stripe Service Implementation',
        requiredContent: ['StripeService', 'createCustomer', 'createSubscription', 'createPaymentIntent', 'verifyWebhookSignature', 'createCheckoutSession', 'createBillingPortalSession']
      },
      {
        file: 'src/lib/payments/subscription-service.ts',
        description: 'Subscription Service Implementation',
        requiredContent: ['SubscriptionService', 'getPlans', 'getUserSubscription', 'createSubscription', 'updateSubscription', 'cancelSubscription', 'getPaymentHistory']
      },
      {
        file: 'src/lib/payments/feature-gate.ts',
        description: 'Feature Gate Implementation',
        requiredContent: ['FeatureGate', 'checkAccess', 'checkUsageLimit', 'trackUsage', 'canUseFeature', 'FEATURES']
      },
      {
        file: 'src/lib/payments/webhook-handler.ts',
        description: 'Webhook Handler Implementation',
        requiredContent: ['WebhookHandler', 'handleWebhook', 'handleSubscriptionCreated', 'handlePaymentSucceeded', 'handleInvoicePaymentSucceeded']
      }
    ]

    let passed = 0
    let total = serviceChecks.length

    serviceChecks.forEach(check => {
      this.results.services.checked++
      if (this.checkFileContent(check.file, check.requiredContent, check.description)) {
        passed++
        this.results.services.present++
      } else {
        this.results.services.missing++
      }
    })

    this.log(`📊 Payment Services: ${passed}/${total} checks passed`, passed === total ? 'success' : 'warning')
    return passed === total
  }

  validatePaymentAPIs() {
    this.log('\n🌐 Validating Payment API Routes...', 'info')
    
    const apiChecks = [
      {
        file: 'src/app/api/payments/route.ts',
        description: 'Payment API Routes',
        requiredContent: ['GET', 'POST', 'PUT', 'DELETE', 'plans', 'subscription', 'payment-history', 'billing-portal', 'create-subscription', 'create-checkout-session', 'update-subscription', 'cancel-subscription']
      },
      {
        file: 'src/app/api/webhooks/stripe/route.ts',
        description: 'Stripe Webhook API',
        requiredContent: ['POST', 'verifyWebhookSignature', 'handleWebhook', 'stripe-signature']
      },
      {
        file: 'src/app/api/features/route.ts',
        description: 'Feature Access API Routes',
        requiredContent: ['GET', 'POST', 'PUT', 'check-access', 'check-usage', 'can-use', 'track-usage', 'usage-analytics']
      }
    ]

    let passed = 0
    let total = apiChecks.length

    apiChecks.forEach(check => {
      this.results.apis.checked++
      if (this.checkFileContent(check.file, check.requiredContent, check.description)) {
        passed++
        this.results.apis.present++
      } else {
        this.results.apis.missing++
      }
    })

    this.log(`📊 Payment APIs: ${passed}/${total} checks passed`, passed === total ? 'success' : 'warning')
    return passed === total
  }

  validatePaymentComponents() {
    this.log('\n⚛️  Validating Payment Components...', 'info')
    
    const componentChecks = [
      {
        file: 'src/components/payments/PricingPage.tsx',
        description: 'Pricing Page Component',
        requiredContent: ['PricingPage', 'handlePlanSelect', 'formatPrice', 'getSavings', 'getFeatureList']
      },
      {
        file: 'src/components/payments/BillingDashboard.tsx',
        description: 'Billing Dashboard Component',
        requiredContent: ['BillingDashboard', 'handleManageBilling', 'handleCancelSubscription', 'handleReactivateSubscription', 'getStatusBadge']
      },
      {
        file: 'src/components/payments/FeatureGate.tsx',
        description: 'Feature Gate Component',
        requiredContent: ['FeatureGate', 'useFeatureAccess', 'FeatureGuard', 'checkFeatureAccess', 'handleUpgrade']
      }
    ]

    let passed = 0
    let total = componentChecks.length

    componentChecks.forEach(check => {
      this.results.components.checked++
      if (this.checkFileContent(check.file, check.requiredContent, check.description)) {
        passed++
        this.results.components.present++
      } else {
        this.results.components.missing++
      }
    })

    this.log(`📊 Payment Components: ${passed}/${total} checks passed`, passed === total ? 'success' : 'warning')
    return passed === total
  }

  validatePaymentTests() {
    this.log('\n🧪 Validating Payment Test Files...', 'info')
    
    const testChecks = [
      {
        file: 'src/test/payments/payment-system.test.ts',
        description: 'Payment System Tests',
        requiredContent: ['describe', 'it', 'expect', 'StripeService', 'SubscriptionService', 'FeatureGate', 'WebhookHandler', 'createCustomer', 'createSubscription', 'checkAccess', 'handleWebhook']
      }
    ]

    let passed = 0
    let total = testChecks.length

    testChecks.forEach(check => {
      this.results.tests.checked++
      if (this.checkFileContent(check.file, check.requiredContent, check.description)) {
        passed++
        this.results.tests.present++
      } else {
        this.results.tests.missing++
      }
    })

    this.log(`📊 Payment Tests: ${passed}/${total} checks passed`, passed === total ? 'success' : 'warning')
    return passed === total
  }

  validateConfiguration() {
    this.log('\n⚙️  Validating Payment Configuration...', 'info')
    
    const configChecks = [
      {
        file: 'package.json',
        description: 'Package.json Payment Dependencies',
        requiredContent: ['stripe', 'test:payments', 'seed:plans']
      },
      {
        file: '.env.example',
        description: 'Environment Variables Configuration',
        requiredContent: ['STRIPE_PUBLISHABLE_KEY', 'STRIPE_SECRET_KEY', 'STRIPE_WEBHOOK_SECRET', 'STRIPE_PRICE_PRO_MONTHLY', 'STRIPE_PRICE_PRO_YEARLY']
      },
      {
        file: 'packages/database/prisma/schema.prisma',
        description: 'Database Schema Payment Models',
        requiredContent: ['SubscriptionPlan', 'UserSubscription', 'Payment', 'FeatureUsage', 'BillingEvent', 'Coupon', 'CouponUsage']
      }
    ]

    let passed = 0
    let total = configChecks.length

    configChecks.forEach(check => {
      this.results.config.checked++
      if (this.checkFileContent(check.file, check.requiredContent, check.description)) {
        passed++
        this.results.config.present++
      } else {
        this.results.config.missing++
      }
    })

    this.log(`📊 Payment Configuration: ${passed}/${total} checks passed`, passed === total ? 'success' : 'warning')
    return passed === total
  }

  generateReport() {
    this.log('\n📊 Payment System & SaaS Monetization Validation Report', 'info')
    this.log('=' .repeat(70), 'info')
    
    const categories = ['files', 'services', 'apis', 'components', 'tests', 'config']
    let totalChecked = 0
    let totalPresent = 0
    let totalMissing = 0

    categories.forEach(category => {
      const result = this.results[category]
      totalChecked += result.checked
      totalPresent += result.present
      totalMissing += result.missing

      const status = result.missing === 0 ? '✅' : '⚠️ '
      const categoryName = category.charAt(0).toUpperCase() + category.slice(1)
      
      this.log(
        `${status} ${categoryName}: ${result.present}/${result.checked} present`,
        result.missing === 0 ? 'success' : 'warning'
      )
    })

    this.log('=' .repeat(70), 'info')
    this.log(`📈 Overall: ${totalPresent}/${totalChecked} checks passed`, 
             totalMissing === 0 ? 'success' : 'warning')
    
    const completionRate = totalChecked > 0 ? (totalPresent / totalChecked * 100).toFixed(1) : 0
    this.log(`📊 Completion Rate: ${completionRate}%`, 
             completionRate >= 90 ? 'success' : completionRate >= 70 ? 'warning' : 'error')

    // Recommendations
    this.log('\n💡 Payment System & SaaS Monetization Status:', 'info')
    if (totalMissing === 0) {
      this.log('🎉 Complete Stripe payment system implemented!', 'success')
      this.log('💰 Ready for SaaS monetization and subscription management', 'success')
      this.log('🔒 Feature access control and usage tracking enabled', 'success')
    } else {
      this.log(`🔧 ${totalMissing} items need attention`, 'warning')
      if (this.results.files.missing > 0) {
        this.log('📁 Create missing payment system files', 'warning')
      }
      if (this.results.services.missing > 0) {
        this.log('💳 Implement missing payment services', 'warning')
      }
      if (this.results.tests.missing > 0) {
        this.log('🧪 Add comprehensive payment system tests', 'warning')
      }
      if (this.results.config.missing > 0) {
        this.log('⚙️  Update configuration for Stripe integration', 'warning')
      }
    }

    // Next steps
    this.log('\n🚀 Next Steps for Payment System:', 'info')
    this.log('1. Set up Stripe account and get API keys', 'info')
    this.log('2. Create subscription products and prices in Stripe Dashboard', 'info')
    this.log('3. Configure webhook endpoints in Stripe', 'info')
    this.log('4. Run database migrations for payment models', 'info')
    this.log('5. Seed subscription plans with: npm run seed:plans', 'info')
    this.log('6. Test payment flows in Stripe test mode', 'info')
    this.log('7. Implement feature gates throughout the application', 'info')

    return totalMissing === 0
  }

  async validate() {
    this.log('🚀 Starting Payment System & SaaS Monetization Validation...', 'info')
    this.log('📅 ' + new Date().toISOString(), 'info')
    
    const results = [
      this.validateFileStructure(),
      this.validatePaymentServices(),
      this.validatePaymentAPIs(),
      this.validatePaymentComponents(),
      this.validatePaymentTests(),
      this.validateConfiguration()
    ]

    const allValid = this.generateReport()
    
    this.log('\n🏁 Payment System Validation Complete!', 'info')
    return allValid
  }
}

// Run validation if this script is executed directly
if (require.main === module) {
  const validator = new PaymentSystemValidator()
  validator.validate()
    .then(success => {
      process.exit(success ? 0 : 1)
    })
    .catch(error => {
      console.error('Validation failed:', error)
      process.exit(1)
    })
}

module.exports = PaymentSystemValidator
