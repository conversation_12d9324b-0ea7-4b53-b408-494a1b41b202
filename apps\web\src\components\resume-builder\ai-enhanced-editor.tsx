'use client';

import { useState } from 'react';
import { Resume, ResumeSectionType } from '@careercraft/shared/types/resume';
import { ContentType } from '@careercraft/shared/types/ai';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Icons } from '@/components/ui/icons';
import { ContentGeneratorPanel } from '@/components/ai/content-generator-panel';
import { ATSAnalysisPanel } from '@/components/ai/ats-analysis-panel';
import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';

interface AIEnhancedEditorProps {
  resume: Resume;
  onResumeUpdate: (resume: Resume) => void;
  selectedSection?: ResumeSectionType;
  className?: string;
}

export function AIEnhancedEditor({
  resume,
  onResumeUpdate,
  selectedSection,
  className,
}: AIEnhancedEditorProps) {
  const [activeTab, setActiveTab] = useState('content-generator');
  const [selectedContentType, setSelectedContentType] = useState<ContentType>(ContentType.PROFESSIONAL_SUMMARY);

  const handleContentGenerated = (content: string) => {
    // Apply the generated content to the appropriate section
    const updatedResume = { ...resume };

    switch (selectedContentType) {
      case ContentType.PROFESSIONAL_SUMMARY:
        updatedResume.personalInfo.summary = content;
        break;
      
      case ContentType.WORK_EXPERIENCE_DESCRIPTION:
        // This would need more context about which work experience item to update
        toast.info('Please manually apply the content to the specific work experience item');
        break;
      
      default:
        toast.info('Please manually apply the generated content to your resume');
        break;
    }

    onResumeUpdate(updatedResume);
  };

  const getContentTypeForSection = (sectionType: ResumeSectionType): ContentType => {
    switch (sectionType) {
      case ResumeSectionType.WORK_EXPERIENCE:
        return ContentType.WORK_EXPERIENCE_DESCRIPTION;
      case ResumeSectionType.EDUCATION:
        return ContentType.ACHIEVEMENT_BULLET;
      case ResumeSectionType.SKILLS:
        return ContentType.SKILLS_SUGGESTION;
      default:
        return ContentType.PROFESSIONAL_SUMMARY;
    }
  };

  const getContextFromResume = () => {
    return {
      firstName: resume.personalInfo.firstName,
      lastName: resume.personalInfo.lastName,
      currentRole: resume.personalInfo.title,
      industry: resume.personalInfo.industry,
      skills: resume.personalInfo.skills || [],
      existingContent: resume.personalInfo.summary,
    };
  };

  const getAvailableContentTypes = () => {
    return [
      {
        type: ContentType.PROFESSIONAL_SUMMARY,
        label: 'Professional Summary',
        description: 'Generate a compelling professional summary',
        icon: Icons.user,
      },
      {
        type: ContentType.WORK_EXPERIENCE_DESCRIPTION,
        label: 'Work Experience',
        description: 'Create impactful job descriptions',
        icon: Icons.briefcase,
      },
      {
        type: ContentType.ACHIEVEMENT_BULLET,
        label: 'Achievement Bullets',
        description: 'Generate quantifiable achievements',
        icon: Icons.target,
      },
      {
        type: ContentType.SKILLS_SUGGESTION,
        label: 'Skills Suggestions',
        description: 'Get relevant skill recommendations',
        icon: Icons.zap,
      },
      {
        type: ContentType.COVER_LETTER,
        label: 'Cover Letter',
        description: 'Create a personalized cover letter',
        icon: Icons.fileText,
      },
      {
        type: ContentType.LINKEDIN_HEADLINE,
        label: 'LinkedIn Headline',
        description: 'Craft an attention-grabbing headline',
        icon: Icons.linkedin,
      },
    ];
  };

  return (
    <div className={cn('space-y-6', className)}>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Icons.sparkles className="h-5 w-5 text-primary" />
            AI-Powered Resume Enhancement
          </CardTitle>
          <CardDescription>
            Use artificial intelligence to improve your resume content and optimize for ATS systems
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="content-generator">Content Generator</TabsTrigger>
              <TabsTrigger value="ats-analysis">ATS Analysis</TabsTrigger>
              <TabsTrigger value="optimization">Optimization</TabsTrigger>
            </TabsList>

            <TabsContent value="content-generator" className="space-y-6">
              {/* Content Type Selection */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Choose Content Type</CardTitle>
                  <CardDescription>
                    Select what type of content you want to generate
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {getAvailableContentTypes().map((contentType) => (
                      <Card
                        key={contentType.type}
                        className={cn(
                          'cursor-pointer transition-all duration-200 hover:shadow-md',
                          selectedContentType === contentType.type && 'ring-2 ring-primary'
                        )}
                        onClick={() => setSelectedContentType(contentType.type)}
                      >
                        <CardContent className="p-4">
                          <div className="flex items-start gap-3">
                            <contentType.icon className="h-5 w-5 text-primary mt-0.5" />
                            <div className="space-y-1">
                              <h3 className="font-medium text-sm">{contentType.label}</h3>
                              <p className="text-xs text-muted-foreground">
                                {contentType.description}
                              </p>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Content Generator */}
              <ContentGeneratorPanel
                contentType={selectedContentType}
                initialContext={getContextFromResume()}
                onContentGenerated={handleContentGenerated}
              />
            </TabsContent>

            <TabsContent value="ats-analysis" className="space-y-6">
              <ATSAnalysisPanel
                resume={resume}
                onRecommendationApply={(recommendation) => {
                  toast.info(`Recommendation: ${recommendation.title}`);
                }}
              />
            </TabsContent>

            <TabsContent value="optimization" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Resume Optimization</CardTitle>
                  <CardDescription>
                    Advanced optimization features and recommendations
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <Card>
                        <CardContent className="p-4">
                          <div className="flex items-center gap-3">
                            <Icons.target className="h-8 w-8 text-primary" />
                            <div>
                              <h3 className="font-medium">Keyword Optimization</h3>
                              <p className="text-sm text-muted-foreground">
                                Optimize keywords for specific job descriptions
                              </p>
                            </div>
                          </div>
                          <Button variant="outline" size="sm" className="mt-3 w-full">
                            Coming Soon
                          </Button>
                        </CardContent>
                      </Card>

                      <Card>
                        <CardContent className="p-4">
                          <div className="flex items-center gap-3">
                            <Icons.barChart className="h-8 w-8 text-primary" />
                            <div>
                              <h3 className="font-medium">Industry Analysis</h3>
                              <p className="text-sm text-muted-foreground">
                                Compare against industry standards
                              </p>
                            </div>
                          </div>
                          <Button variant="outline" size="sm" className="mt-3 w-full">
                            Coming Soon
                          </Button>
                        </CardContent>
                      </Card>

                      <Card>
                        <CardContent className="p-4">
                          <div className="flex items-center gap-3">
                            <Icons.lightBulb className="h-8 w-8 text-primary" />
                            <div>
                              <h3 className="font-medium">Smart Suggestions</h3>
                              <p className="text-sm text-muted-foreground">
                                AI-powered improvement suggestions
                              </p>
                            </div>
                          </div>
                          <Button variant="outline" size="sm" className="mt-3 w-full">
                            Coming Soon
                          </Button>
                        </CardContent>
                      </Card>

                      <Card>
                        <CardContent className="p-4">
                          <div className="flex items-center gap-3">
                            <Icons.shield className="h-8 w-8 text-primary" />
                            <div>
                              <h3 className="font-medium">Format Validation</h3>
                              <p className="text-sm text-muted-foreground">
                                Ensure ATS-friendly formatting
                              </p>
                            </div>
                          </div>
                          <Button variant="outline" size="sm" className="mt-3 w-full">
                            Coming Soon
                          </Button>
                        </CardContent>
                      </Card>
                    </div>

                    <div className="text-center py-8">
                      <Icons.construction className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                      <h3 className="text-lg font-medium mb-2">Advanced Features Coming Soon</h3>
                      <p className="text-muted-foreground max-w-md mx-auto">
                        We're working on advanced AI optimization features that will help you create 
                        the perfect resume for any job application.
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Quick AI Actions</CardTitle>
          <CardDescription>
            Common AI-powered improvements for your resume
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button
              variant="outline"
              className="h-auto p-4 flex flex-col items-center gap-2"
              onClick={() => {
                setSelectedContentType(ContentType.PROFESSIONAL_SUMMARY);
                setActiveTab('content-generator');
              }}
            >
              <Icons.user className="h-6 w-6" />
              <span className="text-sm">Improve Summary</span>
            </Button>

            <Button
              variant="outline"
              className="h-auto p-4 flex flex-col items-center gap-2"
              onClick={() => {
                setActiveTab('ats-analysis');
              }}
            >
              <Icons.shield className="h-6 w-6" />
              <span className="text-sm">Check ATS Score</span>
            </Button>

            <Button
              variant="outline"
              className="h-auto p-4 flex flex-col items-center gap-2"
              onClick={() => {
                setSelectedContentType(ContentType.ACHIEVEMENT_BULLET);
                setActiveTab('content-generator');
              }}
            >
              <Icons.target className="h-6 w-6" />
              <span className="text-sm">Add Achievements</span>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
