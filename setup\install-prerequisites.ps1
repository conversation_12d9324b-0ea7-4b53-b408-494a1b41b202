# CareerCraft Local Development - Prerequisites Installation (Windows)
# Run this script as Administrator in PowerShell

Write-Host "🚀 CareerCraft Local Development Setup - Windows" -ForegroundColor Green
Write-Host "=================================================" -ForegroundColor Green

# Check if running as Administrator
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "❌ Please run this script as Administrator" -ForegroundColor Red
    exit 1
}

# Install Chocolatey if not installed
if (!(Get-Command choco -ErrorAction SilentlyContinue)) {
    Write-Host "📦 Installing Chocolatey..." -ForegroundColor Yellow
    Set-ExecutionPolicy Bypass -Scope Process -Force
    [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072
    iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
}

# Install Node.js
Write-Host "📦 Installing Node.js..." -ForegroundColor Yellow
choco install nodejs -y

# Install Git
Write-Host "📦 Installing Git..." -ForegroundColor Yellow
choco install git -y

# Install PostgreSQL
Write-Host "📦 Installing PostgreSQL..." -ForegroundColor Yellow
choco install postgresql -y --params '/Password:postgres'

# Install Redis
Write-Host "📦 Installing Redis..." -ForegroundColor Yellow
choco install redis-64 -y

# Install VS Code
Write-Host "📦 Installing VS Code..." -ForegroundColor Yellow
choco install vscode -y

# Install Stripe CLI
Write-Host "📦 Installing Stripe CLI..." -ForegroundColor Yellow
choco install stripe-cli -y

# Refresh environment variables
Write-Host "🔄 Refreshing environment variables..." -ForegroundColor Yellow
refreshenv

# Verify installations
Write-Host "✅ Verifying installations..." -ForegroundColor Green

try {
    $nodeVersion = node --version
    Write-Host "✅ Node.js: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Node.js installation failed" -ForegroundColor Red
}

try {
    $npmVersion = npm --version
    Write-Host "✅ npm: $npmVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ npm not available" -ForegroundColor Red
}

try {
    $gitVersion = git --version
    Write-Host "✅ Git: $gitVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Git installation failed" -ForegroundColor Red
}

try {
    $psqlVersion = psql --version
    Write-Host "✅ PostgreSQL: $psqlVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ PostgreSQL installation failed" -ForegroundColor Red
}

try {
    redis-cli ping | Out-Null
    Write-Host "✅ Redis: Available" -ForegroundColor Green
} catch {
    Write-Host "❌ Redis installation failed" -ForegroundColor Red
}

try {
    $stripeVersion = stripe --version
    Write-Host "✅ Stripe CLI: $stripeVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Stripe CLI installation failed" -ForegroundColor Red
}

Write-Host ""
Write-Host "🎉 Prerequisites installation complete!" -ForegroundColor Green
Write-Host "📝 Next steps:" -ForegroundColor Yellow
Write-Host "   1. Restart your terminal/PowerShell" -ForegroundColor White
Write-Host "   2. Run: npm install -g @prisma/cli" -ForegroundColor White
Write-Host "   3. Run: npm install -g typescript" -ForegroundColor White
Write-Host "   4. Continue with project setup" -ForegroundColor White

# Start services
Write-Host "🚀 Starting services..." -ForegroundColor Yellow
Start-Service postgresql-x64-14
Start-Service Redis

Write-Host "✅ All services started!" -ForegroundColor Green
