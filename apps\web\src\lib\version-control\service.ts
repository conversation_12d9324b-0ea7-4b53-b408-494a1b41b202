/**
 * Version Control Service
 * 
 * Database operations and business logic for resume version control,
 * backup management, and history tracking.
 */

import { prisma } from '@/lib/db'
import { z } from 'zod'
import DiffEngine, { VersionDiff } from './diff-engine'

// Version control schemas
export const CreateVersionSchema = z.object({
  resumeId: z.string(),
  userId: z.string(),
  versionName: z.string().optional(),
  changeType: z.enum(['auto', 'manual', 'rollback']).default('auto'),
  changeSummary: z.string().optional(),
  metadata: z.record(z.any()).optional()
})

export const CreateBackupSchema = z.object({
  resumeId: z.string(),
  userId: z.string(),
  backupName: z.string().optional(),
  backupType: z.enum(['manual', 'auto', 'scheduled']).default('manual'),
  expiresAt: z.date().optional(),
  metadata: z.record(z.any()).optional()
})

export const RollbackOptionsSchema = z.object({
  createBackup: z.boolean().default(true),
  backupName: z.string().optional()
})

export type CreateVersionRequest = z.infer<typeof CreateVersionSchema>
export type CreateBackupRequest = z.infer<typeof CreateBackupSchema>
export type RollbackOptions = z.infer<typeof RollbackOptionsSchema>

export interface ResumeVersionInfo {
  id: string
  resumeId: string
  versionNumber: number
  versionName?: string
  changeSummary?: string
  changeType: string
  createdBy: string
  createdAt: Date
  metadata?: any
  creator: {
    id: string
    name: string | null
    image: string | null
  }
}

export interface ResumeBackupInfo {
  id: string
  resumeId: string
  backupName?: string
  backupType: string
  createdBy: string
  createdAt: Date
  expiresAt?: Date
  metadata?: any
  creator: {
    id: string
    name: string | null
    image: string | null
  }
}

export interface VersionActivity {
  id: string
  activityType: string
  userId: string
  activityData?: any
  createdAt: Date
  user: {
    id: string
    name: string | null
    image: string | null
  }
}

export class VersionControlService {
  private diffEngine: DiffEngine

  constructor() {
    this.diffEngine = new DiffEngine({
      ignoreWhitespace: true,
      contextLines: 3,
      maxDepth: 10
    })
  }

  /**
   * Create a new version of a resume
   */
  async createVersion(request: CreateVersionRequest): Promise<ResumeVersionInfo> {
    const { resumeId, userId, versionName, changeType, changeSummary, metadata } = 
      CreateVersionSchema.parse(request)

    // Get current resume data
    const resume = await prisma.resume.findFirst({
      where: {
        id: resumeId,
        userId: userId
      }
    })

    if (!resume) {
      throw new Error('Resume not found or access denied')
    }

    // Get the next version number
    const lastVersion = await prisma.resumeVersion.findFirst({
      where: { resumeId },
      orderBy: { versionNumber: 'desc' }
    })

    const versionNumber = (lastVersion?.versionNumber || 0) + 1

    // Create content snapshot
    const contentSnapshot = {
      personalInfo: resume.personalInfo ? JSON.parse(resume.personalInfo) : null,
      sections: resume.sections ? JSON.parse(resume.sections) : null,
      template: resume.template,
      theme: resume.theme,
      title: resume.title,
      updatedAt: resume.updatedAt
    }

    // Create version record
    const version = await prisma.resumeVersion.create({
      data: {
        resumeId,
        versionNumber,
        versionName,
        contentSnapshot: JSON.stringify(contentSnapshot),
        changeSummary,
        changeType,
        createdBy: userId,
        metadata: metadata ? JSON.stringify(metadata) : null
      },
      include: {
        creator: {
          select: {
            id: true,
            name: true,
            image: true
          }
        }
      }
    })

    // Log activity
    await this.logActivity(resumeId, version.id, 'created', userId, {
      versionNumber,
      changeType,
      changeSummary
    })

    return {
      id: version.id,
      resumeId: version.resumeId,
      versionNumber: version.versionNumber,
      versionName: version.versionName,
      changeSummary: version.changeSummary,
      changeType: version.changeType,
      createdBy: version.createdBy,
      createdAt: version.createdAt,
      metadata: version.metadata ? JSON.parse(version.metadata) : null,
      creator: version.creator
    }
  }

  /**
   * Get versions for a resume
   */
  async getVersions(resumeId: string, userId: string, limit = 50): Promise<ResumeVersionInfo[]> {
    // Verify user has access to resume
    const resume = await prisma.resume.findFirst({
      where: {
        id: resumeId,
        userId: userId
      }
    })

    if (!resume) {
      throw new Error('Resume not found or access denied')
    }

    const versions = await prisma.resumeVersion.findMany({
      where: { resumeId },
      orderBy: { versionNumber: 'desc' },
      take: limit,
      include: {
        creator: {
          select: {
            id: true,
            name: true,
            image: true
          }
        }
      }
    })

    return versions.map(version => ({
      id: version.id,
      resumeId: version.resumeId,
      versionNumber: version.versionNumber,
      versionName: version.versionName,
      changeSummary: version.changeSummary,
      changeType: version.changeType,
      createdBy: version.createdBy,
      createdAt: version.createdAt,
      metadata: version.metadata ? JSON.parse(version.metadata) : null,
      creator: version.creator
    }))
  }

  /**
   * Get a specific version
   */
  async getVersion(versionId: string, userId: string): Promise<any | null> {
    const version = await prisma.resumeVersion.findUnique({
      where: { id: versionId },
      include: {
        resume: true,
        creator: {
          select: {
            id: true,
            name: true,
            image: true
          }
        }
      }
    })

    if (!version || version.resume.userId !== userId) {
      return null
    }

    return {
      ...version,
      contentSnapshot: JSON.parse(version.contentSnapshot),
      metadata: version.metadata ? JSON.parse(version.metadata) : null
    }
  }

  /**
   * Compare two versions
   */
  async compareVersions(resumeId: string, fromVersion: number, toVersion: number, userId: string): Promise<VersionDiff> {
    // Verify user has access
    const resume = await prisma.resume.findFirst({
      where: {
        id: resumeId,
        userId: userId
      }
    })

    if (!resume) {
      throw new Error('Resume not found or access denied')
    }

    // Check for cached comparison
    const cached = await prisma.versionComparison.findUnique({
      where: {
        resumeId_versionFrom_versionTo: {
          resumeId,
          versionFrom: fromVersion,
          versionTo: toVersion
        }
      }
    })

    if (cached) {
      return JSON.parse(cached.diffData)
    }

    // Get versions
    const [fromVersionData, toVersionData] = await Promise.all([
      prisma.resumeVersion.findFirst({
        where: { resumeId, versionNumber: fromVersion }
      }),
      prisma.resumeVersion.findFirst({
        where: { resumeId, versionNumber: toVersion }
      })
    ])

    if (!fromVersionData || !toVersionData) {
      throw new Error('One or both versions not found')
    }

    // Calculate diff
    const fromContent = JSON.parse(fromVersionData.contentSnapshot)
    const toContent = JSON.parse(toVersionData.contentSnapshot)
    const diff = this.diffEngine.calculateDiff(fromContent, toContent, fromVersion, toVersion)

    // Cache the result
    await prisma.versionComparison.create({
      data: {
        resumeId,
        versionFrom: fromVersion,
        versionTo: toVersion,
        diffData: JSON.stringify(diff)
      }
    }).catch(() => {
      // Ignore cache errors
    })

    return diff
  }

  /**
   * Rollback to a specific version
   */
  async rollbackToVersion(resumeId: string, versionId: string, userId: string, options: RollbackOptions = {}): Promise<boolean> {
    const { createBackup, backupName } = RollbackOptionsSchema.parse(options)

    // Get current resume and target version
    const [resume, targetVersion] = await Promise.all([
      prisma.resume.findFirst({
        where: {
          id: resumeId,
          userId: userId
        }
      }),
      prisma.resumeVersion.findUnique({
        where: { id: versionId }
      })
    ])

    if (!resume || !targetVersion || targetVersion.resumeId !== resumeId) {
      throw new Error('Resume or version not found')
    }

    try {
      await prisma.$transaction(async (tx) => {
        // Create backup if requested
        if (createBackup) {
          await this.createBackup({
            resumeId,
            userId,
            backupName: backupName || `Pre-rollback backup ${new Date().toISOString()}`,
            backupType: 'auto'
          })
        }

        // Parse target version content
        const targetContent = JSON.parse(targetVersion.contentSnapshot)

        // Update resume with target version content
        await tx.resume.update({
          where: { id: resumeId },
          data: {
            personalInfo: targetContent.personalInfo ? JSON.stringify(targetContent.personalInfo) : null,
            sections: targetContent.sections ? JSON.stringify(targetContent.sections) : null,
            template: targetContent.template,
            theme: targetContent.theme,
            title: targetContent.title,
            updatedAt: new Date()
          }
        })

        // Create a new version for the rollback
        await this.createVersion({
          resumeId,
          userId,
          changeType: 'rollback',
          changeSummary: `Rolled back to version ${targetVersion.versionNumber}`,
          metadata: {
            rolledBackFrom: targetVersion.versionNumber,
            rolledBackTo: targetVersion.id
          }
        })

        // Log activity
        await this.logActivity(resumeId, versionId, 'restored', userId, {
          targetVersion: targetVersion.versionNumber,
          createBackup
        })
      })

      return true
    } catch (error) {
      console.error('Rollback failed:', error)
      return false
    }
  }

  /**
   * Preview rollback changes
   */
  async previewRollback(resumeId: string, versionId: string, userId: string): Promise<VersionDiff> {
    // Get current resume and target version
    const [resume, targetVersion] = await Promise.all([
      prisma.resume.findFirst({
        where: {
          id: resumeId,
          userId: userId
        }
      }),
      prisma.resumeVersion.findUnique({
        where: { id: versionId }
      })
    ])

    if (!resume || !targetVersion || targetVersion.resumeId !== resumeId) {
      throw new Error('Resume or version not found')
    }

    // Create current content snapshot
    const currentContent = {
      personalInfo: resume.personalInfo ? JSON.parse(resume.personalInfo) : null,
      sections: resume.sections ? JSON.parse(resume.sections) : null,
      template: resume.template,
      theme: resume.theme,
      title: resume.title
    }

    const targetContent = JSON.parse(targetVersion.contentSnapshot)

    // Calculate diff from current to target
    return this.diffEngine.calculateDiff(currentContent, targetContent, 0, targetVersion.versionNumber)
  }

  /**
   * Create backup
   */
  async createBackup(request: CreateBackupRequest): Promise<ResumeBackupInfo> {
    const { resumeId, userId, backupName, backupType, expiresAt, metadata } = 
      CreateBackupSchema.parse(request)

    // Get current resume data
    const resume = await prisma.resume.findFirst({
      where: {
        id: resumeId,
        userId: userId
      }
    })

    if (!resume) {
      throw new Error('Resume not found or access denied')
    }

    // Create backup data
    const backupData = {
      personalInfo: resume.personalInfo ? JSON.parse(resume.personalInfo) : null,
      sections: resume.sections ? JSON.parse(resume.sections) : null,
      template: resume.template,
      theme: resume.theme,
      title: resume.title,
      createdAt: resume.createdAt,
      updatedAt: resume.updatedAt
    }

    // Create backup record
    const backup = await prisma.resumeBackup.create({
      data: {
        resumeId,
        backupName,
        backupData: JSON.stringify(backupData),
        backupType,
        createdBy: userId,
        expiresAt,
        metadata: metadata ? JSON.stringify(metadata) : null
      },
      include: {
        creator: {
          select: {
            id: true,
            name: true,
            image: true
          }
        }
      }
    })

    return {
      id: backup.id,
      resumeId: backup.resumeId,
      backupName: backup.backupName,
      backupType: backup.backupType,
      createdBy: backup.createdBy,
      createdAt: backup.createdAt,
      expiresAt: backup.expiresAt,
      metadata: backup.metadata ? JSON.parse(backup.metadata) : null,
      creator: backup.creator
    }
  }

  /**
   * Get backups for a resume
   */
  async getBackups(resumeId: string, userId: string, limit = 20): Promise<ResumeBackupInfo[]> {
    // Verify user has access
    const resume = await prisma.resume.findFirst({
      where: {
        id: resumeId,
        userId: userId
      }
    })

    if (!resume) {
      throw new Error('Resume not found or access denied')
    }

    const backups = await prisma.resumeBackup.findMany({
      where: { 
        resumeId,
        OR: [
          { expiresAt: null },
          { expiresAt: { gt: new Date() } }
        ]
      },
      orderBy: { createdAt: 'desc' },
      take: limit,
      include: {
        creator: {
          select: {
            id: true,
            name: true,
            image: true
          }
        }
      }
    })

    return backups.map(backup => ({
      id: backup.id,
      resumeId: backup.resumeId,
      backupName: backup.backupName,
      backupType: backup.backupType,
      createdBy: backup.createdBy,
      createdAt: backup.createdAt,
      expiresAt: backup.expiresAt,
      metadata: backup.metadata ? JSON.parse(backup.metadata) : null,
      creator: backup.creator
    }))
  }

  /**
   * Restore from backup
   */
  async restoreFromBackup(backupId: string, userId: string): Promise<boolean> {
    const backup = await prisma.resumeBackup.findUnique({
      where: { id: backupId },
      include: { resume: true }
    })

    if (!backup || backup.resume.userId !== userId) {
      throw new Error('Backup not found or access denied')
    }

    if (backup.expiresAt && backup.expiresAt < new Date()) {
      throw new Error('Backup has expired')
    }

    try {
      await prisma.$transaction(async (tx) => {
        const backupData = JSON.parse(backup.backupData)

        // Update resume with backup data
        await tx.resume.update({
          where: { id: backup.resumeId },
          data: {
            personalInfo: backupData.personalInfo ? JSON.stringify(backupData.personalInfo) : null,
            sections: backupData.sections ? JSON.stringify(backupData.sections) : null,
            template: backupData.template,
            theme: backupData.theme,
            title: backupData.title,
            updatedAt: new Date()
          }
        })

        // Create version for the restore
        await this.createVersion({
          resumeId: backup.resumeId,
          userId,
          changeType: 'rollback',
          changeSummary: `Restored from backup: ${backup.backupName || 'Unnamed backup'}`,
          metadata: {
            restoredFromBackup: backup.id,
            backupCreatedAt: backup.createdAt
          }
        })
      })

      return true
    } catch (error) {
      console.error('Restore failed:', error)
      return false
    }
  }

  /**
   * Get version activities
   */
  async getVersionActivities(resumeId: string, userId: string, limit = 50): Promise<VersionActivity[]> {
    // Verify user has access
    const resume = await prisma.resume.findFirst({
      where: {
        id: resumeId,
        userId: userId
      }
    })

    if (!resume) {
      throw new Error('Resume not found or access denied')
    }

    const activities = await prisma.versionActivity.findMany({
      where: { resumeId },
      orderBy: { createdAt: 'desc' },
      take: limit,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            image: true
          }
        }
      }
    })

    return activities.map(activity => ({
      id: activity.id,
      activityType: activity.activityType,
      userId: activity.userId,
      activityData: activity.activityData ? JSON.parse(activity.activityData) : null,
      createdAt: activity.createdAt,
      user: activity.user
    }))
  }

  /**
   * Delete old versions (cleanup)
   */
  async cleanupOldVersions(resumeId: string, keepCount = 50): Promise<number> {
    const versionsToDelete = await prisma.resumeVersion.findMany({
      where: { resumeId },
      orderBy: { versionNumber: 'desc' },
      skip: keepCount,
      select: { id: true }
    })

    if (versionsToDelete.length === 0) {
      return 0
    }

    const result = await prisma.resumeVersion.deleteMany({
      where: {
        id: {
          in: versionsToDelete.map(v => v.id)
        }
      }
    })

    return result.count
  }

  /**
   * Delete expired backups
   */
  async cleanupExpiredBackups(): Promise<number> {
    const result = await prisma.resumeBackup.deleteMany({
      where: {
        expiresAt: {
          lt: new Date()
        }
      }
    })

    return result.count
  }

  private async logActivity(resumeId: string, versionId: string, activityType: string, userId: string, activityData?: any): Promise<void> {
    await prisma.versionActivity.create({
      data: {
        resumeId,
        versionId,
        activityType,
        userId,
        activityData: activityData ? JSON.stringify(activityData) : null
      }
    }).catch(() => {
      // Ignore logging errors
    })
  }
}

export const versionControlService = new VersionControlService()
