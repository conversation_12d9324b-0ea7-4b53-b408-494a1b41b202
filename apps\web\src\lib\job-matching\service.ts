/**
 * Job Matching Service
 * 
 * Core service for job discovery, matching, and application management
 */

import { prisma } from '@/lib/db'
import { z } from 'zod'

// Job matching schemas
export const JobSearchCriteriaSchema = z.object({
  query: z.string().optional(),
  location: z.string().optional(),
  remoteType: z.enum(['remote', 'hybrid', 'on-site']).optional(),
  employmentType: z.enum(['full-time', 'part-time', 'contract', 'internship']).optional(),
  experienceLevel: z.enum(['entry', 'mid', 'senior', 'executive']).optional(),
  salaryMin: z.number().optional(),
  salaryMax: z.number().optional(),
  skills: z.array(z.string()).optional(),
  companies: z.array(z.string()).optional(),
  limit: z.number().default(20),
  offset: z.number().default(0)
})

export const JobApplicationDataSchema = z.object({
  userId: z.string(),
  jobPostingId: z.string(),
  resumeId: z.string().optional(),
  coverLetterId: z.string().optional(),
  notes: z.string().optional(),
  customFields: z.record(z.any()).optional()
})

export const UserJobPreferencesSchema = z.object({
  userId: z.string(),
  preferredTitles: z.array(z.string()).optional(),
  preferredCompanies: z.array(z.string()).optional(),
  preferredLocations: z.array(z.string()).optional(),
  salaryMin: z.number().optional(),
  salaryMax: z.number().optional(),
  employmentTypes: z.array(z.string()).optional(),
  remotePreferences: z.array(z.string()).optional(),
  experienceLevel: z.string().optional(),
  industryPreferences: z.array(z.string()).optional(),
  companySizePreferences: z.array(z.string()).optional(),
  notificationPreferences: z.record(z.boolean()).optional()
})

export type JobSearchCriteria = z.infer<typeof JobSearchCriteriaSchema>
export type JobApplicationData = z.infer<typeof JobApplicationDataSchema>
export type UserJobPreferences = z.infer<typeof UserJobPreferencesSchema>

export interface JobPosting {
  id: string
  externalId?: string
  title: string
  company: string
  description: string
  requirements?: string
  location?: string
  salaryMin?: number
  salaryMax?: number
  employmentType?: string
  remoteType?: string
  experienceLevel?: string
  skills?: string[]
  benefits?: string[]
  postedDate?: Date
  expiresDate?: Date
  source?: string
  sourceUrl?: string
  isActive: boolean
  createdAt: Date
  updatedAt: Date
}

export interface JobApplication {
  id: string
  userId: string
  jobPostingId: string
  resumeId?: string
  coverLetterId?: string
  status: string
  appliedDate: Date
  lastUpdated: Date
  notes?: string
  interviewDates?: any[]
  followUpDate?: Date
  salaryOffered?: number
  metadata?: any
  jobPosting?: JobPosting
}

export interface JobRecommendation {
  id: string
  userId: string
  jobPostingId: string
  matchScore: number
  reasoning?: any
  isViewed: boolean
  isSaved: boolean
  isDismissed: boolean
  recommendedAt: Date
  viewedAt?: Date
  jobPosting?: JobPosting
}

export interface SkillGapAnalysis {
  requiredSkills: string[]
  userSkills: string[]
  missingSkills: string[]
  matchingSkills: string[]
  skillGapScore: number
  recommendations: string[]
}

export interface MarketInsights {
  averageSalary: number
  salaryRange: { min: number; max: number }
  demandScore: number
  competitionLevel: string
  trendingSkills: string[]
  topCompanies: string[]
  locationInsights: any[]
}

export class JobMatchingService {
  /**
   * Search for jobs based on criteria
   */
  async searchJobs(criteria: JobSearchCriteria): Promise<JobPosting[]> {
    const { query, location, remoteType, employmentType, experienceLevel, salaryMin, salaryMax, skills, companies, limit, offset } = 
      JobSearchCriteriaSchema.parse(criteria)

    const whereClause: any = {
      isActive: true
    }

    // Text search in title, company, or description
    if (query) {
      whereClause.OR = [
        { title: { contains: query, mode: 'insensitive' } },
        { company: { contains: query, mode: 'insensitive' } },
        { description: { contains: query, mode: 'insensitive' } }
      ]
    }

    // Location filter
    if (location) {
      whereClause.location = { contains: location, mode: 'insensitive' }
    }

    // Remote type filter
    if (remoteType) {
      whereClause.remoteType = remoteType
    }

    // Employment type filter
    if (employmentType) {
      whereClause.employmentType = employmentType
    }

    // Experience level filter
    if (experienceLevel) {
      whereClause.experienceLevel = experienceLevel
    }

    // Salary range filter
    if (salaryMin || salaryMax) {
      whereClause.AND = []
      if (salaryMin) {
        whereClause.AND.push({
          OR: [
            { salaryMin: { gte: salaryMin } },
            { salaryMax: { gte: salaryMin } }
          ]
        })
      }
      if (salaryMax) {
        whereClause.AND.push({
          OR: [
            { salaryMin: { lte: salaryMax } },
            { salaryMax: { lte: salaryMax } }
          ]
        })
      }
    }

    // Company filter
    if (companies && companies.length > 0) {
      whereClause.company = { in: companies }
    }

    const jobs = await prisma.jobPosting.findMany({
      where: whereClause,
      orderBy: [
        { postedDate: 'desc' },
        { createdAt: 'desc' }
      ],
      take: limit,
      skip: offset
    })

    return jobs.map(job => ({
      ...job,
      skills: job.skills ? JSON.parse(job.skills) : [],
      benefits: job.benefits ? JSON.parse(job.benefits) : []
    }))
  }

  /**
   * Get personalized job recommendations for a user
   */
  async getRecommendations(userId: string, limit = 10): Promise<JobRecommendation[]> {
    // Get user preferences
    const preferences = await prisma.userJobPreferences.findUnique({
      where: { userId }
    })

    // Get user's recent applications to avoid duplicates
    const recentApplications = await prisma.jobApplication.findMany({
      where: { userId },
      select: { jobPostingId: true }
    })

    const appliedJobIds = recentApplications.map(app => app.jobPostingId)

    // Get existing recommendations that haven't been dismissed
    const recommendations = await prisma.jobRecommendation.findMany({
      where: {
        userId,
        isDismissed: false,
        jobPostingId: { notIn: appliedJobIds }
      },
      include: {
        jobPosting: true
      },
      orderBy: [
        { matchScore: 'desc' },
        { recommendedAt: 'desc' }
      ],
      take: limit
    })

    return recommendations.map(rec => ({
      ...rec,
      reasoning: rec.reasoning ? JSON.parse(rec.reasoning) : null,
      jobPosting: rec.jobPosting ? {
        ...rec.jobPosting,
        skills: rec.jobPosting.skills ? JSON.parse(rec.jobPosting.skills) : [],
        benefits: rec.jobPosting.benefits ? JSON.parse(rec.jobPosting.benefits) : []
      } : undefined
    }))
  }

  /**
   * Calculate match score between user profile and job posting
   */
  async calculateMatchScore(userId: string, jobPostingId: string): Promise<number> {
    // Get user's resume and skills
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        resumes: {
          where: { status: 'PUBLISHED' },
          orderBy: { updatedAt: 'desc' },
          take: 1
        },
        skillAssessments: true,
        jobPreferences: true
      }
    })

    if (!user || user.resumes.length === 0) {
      return 0
    }

    const resume = user.resumes[0]
    const jobPosting = await prisma.jobPosting.findUnique({
      where: { id: jobPostingId }
    })

    if (!jobPosting) {
      return 0
    }

    let score = 0
    let totalWeight = 0

    // Skills matching (40% weight)
    const skillWeight = 40
    const userSkills = user.skillAssessments.map(s => s.skillName.toLowerCase())
    const jobSkills = jobPosting.skills ? JSON.parse(jobPosting.skills).map((s: string) => s.toLowerCase()) : []
    
    if (jobSkills.length > 0) {
      const matchingSkills = userSkills.filter(skill => 
        jobSkills.some(jobSkill => jobSkill.includes(skill) || skill.includes(jobSkill))
      )
      const skillMatchRatio = matchingSkills.length / jobSkills.length
      score += skillMatchRatio * skillWeight
    }
    totalWeight += skillWeight

    // Location matching (20% weight)
    const locationWeight = 20
    if (user.jobPreferences?.preferredLocations && jobPosting.location) {
      const preferredLocations = JSON.parse(user.jobPreferences.preferredLocations)
      const locationMatch = preferredLocations.some((loc: string) => 
        jobPosting.location?.toLowerCase().includes(loc.toLowerCase())
      )
      if (locationMatch) {
        score += locationWeight
      }
    }
    totalWeight += locationWeight

    // Salary matching (20% weight)
    const salaryWeight = 20
    if (user.jobPreferences?.salaryMin && jobPosting.salaryMax) {
      if (jobPosting.salaryMax >= user.jobPreferences.salaryMin) {
        score += salaryWeight
      }
    }
    totalWeight += salaryWeight

    // Experience level matching (20% weight)
    const experienceWeight = 20
    if (user.jobPreferences?.experienceLevel && jobPosting.experienceLevel) {
      if (user.jobPreferences.experienceLevel === jobPosting.experienceLevel) {
        score += experienceWeight
      }
    }
    totalWeight += experienceWeight

    return totalWeight > 0 ? Math.round((score / totalWeight) * 100) : 0
  }

  /**
   * Create a job application
   */
  async applyToJob(applicationData: JobApplicationData): Promise<JobApplication> {
    const { userId, jobPostingId, resumeId, coverLetterId, notes, customFields } = 
      JobApplicationDataSchema.parse(applicationData)

    // Check if user already applied to this job
    const existingApplication = await prisma.jobApplication.findFirst({
      where: {
        userId,
        jobPostingId
      }
    })

    if (existingApplication) {
      throw new Error('You have already applied to this job')
    }

    // Verify job posting exists and is active
    const jobPosting = await prisma.jobPosting.findUnique({
      where: { id: jobPostingId }
    })

    if (!jobPosting || !jobPosting.isActive) {
      throw new Error('Job posting not found or no longer active')
    }

    // Create application
    const application = await prisma.jobApplication.create({
      data: {
        userId,
        jobPostingId,
        resumeId,
        coverLetterId,
        notes,
        metadata: customFields ? JSON.stringify(customFields) : null,
        status: 'applied',
        appliedDate: new Date(),
        lastUpdated: new Date()
      },
      include: {
        jobPosting: true
      }
    })

    return {
      ...application,
      metadata: application.metadata ? JSON.parse(application.metadata) : null,
      interviewDates: application.interviewDates ? JSON.parse(application.interviewDates) : null,
      jobPosting: application.jobPosting ? {
        ...application.jobPosting,
        skills: application.jobPosting.skills ? JSON.parse(application.jobPosting.skills) : [],
        benefits: application.jobPosting.benefits ? JSON.parse(application.jobPosting.benefits) : []
      } : undefined
    }
  }

  /**
   * Update application status
   */
  async updateApplicationStatus(applicationId: string, status: string, userId: string): Promise<boolean> {
    const validStatuses = ['applied', 'screening', 'interview', 'offer', 'rejected', 'withdrawn']
    
    if (!validStatuses.includes(status)) {
      throw new Error('Invalid application status')
    }

    const result = await prisma.jobApplication.updateMany({
      where: {
        id: applicationId,
        userId // Ensure user owns the application
      },
      data: {
        status,
        lastUpdated: new Date()
      }
    })

    return result.count > 0
  }

  /**
   * Get user's job applications
   */
  async getApplications(userId: string, filters?: { status?: string; limit?: number }): Promise<JobApplication[]> {
    const whereClause: any = { userId }
    
    if (filters?.status) {
      whereClause.status = filters.status
    }

    const applications = await prisma.jobApplication.findMany({
      where: whereClause,
      include: {
        jobPosting: true
      },
      orderBy: { appliedDate: 'desc' },
      take: filters?.limit || 50
    })

    return applications.map(app => ({
      ...app,
      metadata: app.metadata ? JSON.parse(app.metadata) : null,
      interviewDates: app.interviewDates ? JSON.parse(app.interviewDates) : null,
      jobPosting: app.jobPosting ? {
        ...app.jobPosting,
        skills: app.jobPosting.skills ? JSON.parse(app.jobPosting.skills) : [],
        benefits: app.jobPosting.benefits ? JSON.parse(app.jobPosting.benefits) : []
      } : undefined
    }))
  }

  /**
   * Save or update user job preferences
   */
  async updateJobPreferences(preferences: UserJobPreferences): Promise<boolean> {
    const { userId, ...prefs } = UserJobPreferencesSchema.parse(preferences)

    const data = {
      preferredTitles: prefs.preferredTitles ? JSON.stringify(prefs.preferredTitles) : null,
      preferredCompanies: prefs.preferredCompanies ? JSON.stringify(prefs.preferredCompanies) : null,
      preferredLocations: prefs.preferredLocations ? JSON.stringify(prefs.preferredLocations) : null,
      salaryMin: prefs.salaryMin,
      salaryMax: prefs.salaryMax,
      employmentTypes: prefs.employmentTypes ? JSON.stringify(prefs.employmentTypes) : null,
      remotePreferences: prefs.remotePreferences ? JSON.stringify(prefs.remotePreferences) : null,
      experienceLevel: prefs.experienceLevel,
      industryPreferences: prefs.industryPreferences ? JSON.stringify(prefs.industryPreferences) : null,
      companySizePreferences: prefs.companySizePreferences ? JSON.stringify(prefs.companySizePreferences) : null,
      notificationPreferences: prefs.notificationPreferences ? JSON.stringify(prefs.notificationPreferences) : null,
      updatedAt: new Date()
    }

    await prisma.userJobPreferences.upsert({
      where: { userId },
      update: data,
      create: {
        userId,
        ...data,
        createdAt: new Date()
      }
    })

    return true
  }

  /**
   * Generate job recommendations for a user
   */
  async generateRecommendations(userId: string): Promise<number> {
    // Get user preferences and profile
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        jobPreferences: true,
        skillAssessments: true
      }
    })

    if (!user) {
      throw new Error('User not found')
    }

    // Get recent job postings that match user preferences
    const searchCriteria: JobSearchCriteria = {
      limit: 100
    }

    if (user.jobPreferences) {
      const prefs = user.jobPreferences
      if (prefs.preferredLocations) {
        const locations = JSON.parse(prefs.preferredLocations)
        if (locations.length > 0) {
          searchCriteria.location = locations[0] // Use first preferred location
        }
      }
      if (prefs.experienceLevel) {
        searchCriteria.experienceLevel = prefs.experienceLevel as any
      }
      if (prefs.salaryMin) {
        searchCriteria.salaryMin = prefs.salaryMin
      }
    }

    const jobs = await this.searchJobs(searchCriteria)

    // Calculate match scores and create recommendations
    let recommendationsCreated = 0

    for (const job of jobs) {
      // Check if recommendation already exists
      const existingRec = await prisma.jobRecommendation.findUnique({
        where: {
          userId_jobPostingId: {
            userId,
            jobPostingId: job.id
          }
        }
      })

      if (existingRec) {
        continue
      }

      // Calculate match score
      const matchScore = await this.calculateMatchScore(userId, job.id)

      // Only create recommendations with decent match scores
      if (matchScore >= 30) {
        await prisma.jobRecommendation.create({
          data: {
            userId,
            jobPostingId: job.id,
            matchScore,
            reasoning: JSON.stringify({
              skillMatch: matchScore >= 60,
              locationMatch: true,
              salaryMatch: true
            }),
            recommendedAt: new Date()
          }
        })
        recommendationsCreated++
      }
    }

    return recommendationsCreated
  }

  /**
   * Mark recommendation as viewed
   */
  async markRecommendationViewed(recommendationId: string, userId: string): Promise<boolean> {
    const result = await prisma.jobRecommendation.updateMany({
      where: {
        id: recommendationId,
        userId
      },
      data: {
        isViewed: true,
        viewedAt: new Date()
      }
    })

    return result.count > 0
  }

  /**
   * Save or dismiss recommendation
   */
  async updateRecommendationStatus(recommendationId: string, userId: string, action: 'save' | 'dismiss'): Promise<boolean> {
    const updateData = action === 'save' 
      ? { isSaved: true }
      : { isDismissed: true }

    const result = await prisma.jobRecommendation.updateMany({
      where: {
        id: recommendationId,
        userId
      },
      data: updateData
    })

    return result.count > 0
  }
}

export const jobMatchingService = new JobMatchingService()
