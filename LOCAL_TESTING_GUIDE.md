# 🚀 CareerCraft - Local Testing Guide

This guide will help you set up and test all the features we've built so far in your local development environment.

## 📋 Prerequisites

Before starting, ensure you have:

- **Node.js 18+** installed
- **npm** or **yarn** package manager
- **PostgreSQL** (optional - SQLite will be used as fallback)
- **Git** for version control

## 🛠️ Quick Setup

### 1. Run the Setup Script

```bash
cd careercraft-v2
./scripts/setup-local-dev.sh
```

This script will:
- ✅ Check Node.js version
- ✅ Install all dependencies
- ✅ Create `.env.local` with default configuration
- ✅ Set up database (PostgreSQL or SQLite)
- ✅ Run database migrations
- ✅ Seed with sample data
- ✅ Build all packages
- ✅ Run comprehensive tests
- ✅ Create demo user account

### 2. Start Development Server

```bash
npm run dev
```

The application will be available at: **http://localhost:3000**

## 🧪 Feature Testing Checklist

### ✅ **1. Homepage & Navigation**

**URL:** `http://localhost:3000`

**Test:**
- [ ] Homepage loads correctly
- [ ] Navigation menu works
- [ ] Hero section displays
- [ ] Feature cards are visible
- [ ] Call-to-action buttons work
- [ ] Footer links function

**Expected:** Clean, professional homepage with working navigation

---

### ✅ **2. Authentication System**

**URL:** `http://localhost:3000/auth/signin`

**Test:**
- [ ] Sign-in page loads
- [ ] Demo account login works:
  - Email: `<EMAIL>`
  - Password: `password123`
- [ ] Sign-up form validation
- [ ] Password reset flow
- [ ] OAuth providers (if configured)
- [ ] Session persistence

**Expected:** Secure authentication with proper redirects

---

### ✅ **3. Dashboard**

**URL:** `http://localhost:3000/dashboard`

**Test:**
- [ ] Dashboard loads after login
- [ ] Resume cards display
- [ ] Quick stats show
- [ ] Recent activity visible
- [ ] Navigation sidebar works
- [ ] User profile accessible

**Expected:** Personalized dashboard with user data

---

### ✅ **4. Resume Builder**

**URL:** `http://localhost:3000/dashboard/resumes/new`

**Test:**
- [ ] Resume builder loads
- [ ] Template selection works
- [ ] Personal info form functions
- [ ] Work experience section:
  - [ ] Add new experience
  - [ ] Edit existing entries
  - [ ] Drag & drop reordering
  - [ ] Delete entries
- [ ] Education section works
- [ ] Skills section functions
- [ ] Real-time preview updates
- [ ] Save functionality

**Expected:** Fully functional resume builder with live preview

---

### ✅ **5. Template System**

**URL:** `http://localhost:3000/templates`

**Test:**
- [ ] Template gallery loads
- [ ] Template filtering works
- [ ] Template categories function
- [ ] Template preview displays
- [ ] Template selection works
- [ ] Template customization:
  - [ ] Color scheme changes
  - [ ] Font selection
  - [ ] Layout options
  - [ ] Spacing adjustments

**Expected:** Professional templates with customization options

---

### ✅ **6. AI Content Generation**

**URL:** `http://localhost:3000/ai-demo`

**Test:**
- [ ] AI demo page loads
- [ ] Content generator panel works
- [ ] Content type selection functions
- [ ] Context form validation
- [ ] Generation options work
- [ ] Content generation produces results
- [ ] Multiple suggestions display
- [ ] Content editing works
- [ ] Apply content functionality

**Expected:** AI generates relevant, professional content

---

### ✅ **7. ATS Optimization**

**URL:** `http://localhost:3000/ai-demo` (ATS Analysis tab)

**Test:**
- [ ] ATS analysis panel loads
- [ ] Resume analysis runs
- [ ] ATS score displays (0-100)
- [ ] Issues are identified
- [ ] Recommendations show
- [ ] Keyword analysis works
- [ ] Improvement suggestions display

**Expected:** Comprehensive ATS analysis with actionable insights

---

### ✅ **8. PDF Export**

**Test in Resume Builder:**
- [ ] Export button appears
- [ ] Export options panel opens
- [ ] Format selection works (PDF, DOCX, HTML)
- [ ] Quality settings function
- [ ] Page size options work
- [ ] Margin customization
- [ ] Export process completes
- [ ] Download link works

**Expected:** High-quality exports in multiple formats

---

### ✅ **9. Database Operations**

**Test via UI:**
- [ ] Create new resume
- [ ] Save resume data
- [ ] Load existing resume
- [ ] Update resume sections
- [ ] Delete resume
- [ ] User profile updates
- [ ] Template preferences save

**Expected:** Persistent data storage and retrieval

---

### ✅ **10. Responsive Design**

**Test on different screen sizes:**
- [ ] Desktop (1920x1080)
- [ ] Laptop (1366x768)
- [ ] Tablet (768x1024)
- [ ] Mobile (375x667)

**Expected:** Responsive design that works on all devices

## 🔧 Manual Testing Commands

### Run Individual Test Suites

```bash
# Database layer tests
npm run test:db

# Authentication tests
npm run test:auth

# UI component tests
npm run test:ui

# Resume builder tests
npm run test:resume

# Template system tests
npm run test:templates

# AI features tests
npm run test:ai
```

### Database Operations

```bash
# Reset database
npm run db:reset

# View database
npm run db:studio

# Run migrations
npm run db:push

# Seed sample data
npm run db:seed
```

### Development Tools

```bash
# Type checking
npm run type-check

# Linting
npm run lint

# Format code
npm run format

# Build packages
npm run build:packages
```

## 🐛 Troubleshooting

### Common Issues

**1. Database Connection Error**
```bash
# Check if PostgreSQL is running
pg_ctl status

# Or use SQLite (update .env.local)
DATABASE_URL="file:./dev.db"
```

**2. Port Already in Use**
```bash
# Kill process on port 3000
npx kill-port 3000

# Or use different port
npm run dev -- -p 3001
```

**3. Missing Dependencies**
```bash
# Clean install
rm -rf node_modules package-lock.json
npm install
```

**4. Build Errors**
```bash
# Clean build
npm run clean
npm run build:packages
```

## 📊 Expected Test Results

When everything is working correctly, you should see:

```bash
✅ Database Tests: 12/12 passed
✅ Authentication Tests: 8/8 passed  
✅ UI Component Tests: 15/15 passed
✅ Resume Builder Tests: 10/10 passed
✅ Template System Tests: 8/8 passed
✅ AI Features Tests: 8/8 passed

🎉 All systems operational!
```

## 🎯 Key Features to Validate

### Core Functionality
- [ ] User registration and authentication
- [ ] Resume creation and editing
- [ ] Template selection and customization
- [ ] PDF export functionality
- [ ] Data persistence

### Advanced Features
- [ ] AI content generation
- [ ] ATS optimization analysis
- [ ] Real-time preview
- [ ] Responsive design
- [ ] Error handling

### Performance
- [ ] Page load times < 3 seconds
- [ ] Smooth interactions
- [ ] No console errors
- [ ] Proper loading states

## 📞 Support

If you encounter any issues:

1. Check the console for error messages
2. Verify your `.env.local` configuration
3. Ensure all dependencies are installed
4. Run the test suites to identify problems
5. Check the database connection

## 🚀 Ready for Production Testing

Once all features are working locally:

1. ✅ All test suites pass
2. ✅ Core user flows work end-to-end
3. ✅ AI features generate quality content
4. ✅ Export functionality works
5. ✅ No critical console errors
6. ✅ Responsive design verified

**You're ready to proceed to Step 8: Real-time Collaboration & Sharing!** 🎉
