import React from 'react'
import { render, RenderOptions } from '@testing-library/react'
import { SessionProvider } from 'next-auth/react'
import { ThemeProvider } from 'next-themes'

// Mock session data
export const mockSession = {
  user: {
    id: 'test-user-id',
    name: 'Test User',
    email: '<EMAIL>',
    image: 'https://example.com/avatar.jpg',
  },
  expires: '2024-12-31',
}

// Mock resume data
export const mockResumes = [
  {
    id: '1',
    title: 'Software Engineer Resume',
    description: 'Full-stack developer position at tech companies',
    status: 'PUBLISHED' as const,
    templateId: 'modern-1',
    templateName: 'Modern Professional',
    isPublic: true,
    publicUrl: 'https://careercraft.com/resume/abc123',
    createdAt: new Date('2024-01-08'),
    updatedAt: new Date('2024-01-15T10:00:00Z'),
    sections: {
      personalInfo: { name: '<PERSON>', email: '<EMAIL>' },
      experience: [{ company: 'Tech Corp', position: 'Senior Developer' }],
      education: [{ institution: 'University', degree: 'Computer Science' }],
      skills: ['React', 'Node.js', 'TypeScript']
    }
  },
  {
    id: '2',
    title: 'Product Manager Resume',
    description: 'Product management roles in SaaS companies',
    status: 'DRAFT' as const,
    templateId: 'classic-1',
    templateName: 'Classic Professional',
    isPublic: false,
    createdAt: new Date('2024-01-12'),
    updatedAt: new Date('2024-01-15T05:00:00Z'),
    sections: {
      personalInfo: { name: 'Jane Smith', email: '<EMAIL>' },
      experience: [{ company: 'SaaS Inc', position: 'Product Manager' }],
      education: [{ institution: 'Business School', degree: 'MBA' }],
      skills: ['Product Strategy', 'Analytics', 'Leadership']
    }
  }
]

// Mock template data
export const mockTemplates = [
  {
    id: 'modern-1',
    name: 'Modern Professional',
    description: 'Clean and contemporary design perfect for tech and business professionals',
    category: 'modern' as const,
    isPremium: false,
    isPopular: true,
    rating: 4.8,
    downloads: 1250,
    preview: '/templates/modern-1.jpg',
    colors: ['#3B82F6', '#1E40AF', '#F3F4F6'],
    features: ['ATS Optimized', 'Clean Layout', 'Professional Typography'],
    suitableFor: ['Software Engineer', 'Product Manager', 'Business Analyst']
  },
  {
    id: 'creative-1',
    name: 'Creative Designer',
    description: 'Bold and artistic design for creative professionals',
    category: 'creative' as const,
    isPremium: true,
    isPopular: true,
    rating: 4.7,
    downloads: 2100,
    preview: '/templates/creative-1.jpg',
    colors: ['#8B5CF6', '#A855F7', '#F3E8FF'],
    features: ['Creative Layout', 'Color Accents', 'Portfolio Section'],
    suitableFor: ['Graphic Designer', 'UX Designer', 'Creative Director']
  }
]

// Mock dashboard stats
export const mockDashboardStats = {
  totalResumes: 3,
  totalDownloads: 12,
  totalViews: 45,
  activeResumes: 2
}

// Mock recent activity
export const mockRecentActivity = [
  {
    id: '1',
    type: 'created' as const,
    resumeTitle: 'Software Engineer Resume',
    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
    description: 'Created new resume'
  },
  {
    id: '2',
    type: 'downloaded' as const,
    resumeTitle: 'Product Manager Resume',
    timestamp: new Date(Date.now() - 5 * 60 * 60 * 1000),
    description: 'Downloaded as PDF'
  }
]

// Custom render function with providers
interface CustomRenderOptions extends Omit<RenderOptions, 'wrapper'> {
  session?: any
  theme?: string
}

export function renderWithProviders(
  ui: React.ReactElement,
  {
    session = mockSession,
    theme = 'light',
    ...renderOptions
  }: CustomRenderOptions = {}
) {
  function Wrapper({ children }: { children: React.ReactNode }) {
    return (
      <SessionProvider session={session}>
        <ThemeProvider
          attribute="class"
          defaultTheme={theme}
          enableSystem={false}
          disableTransitionOnChange
        >
          {children}
        </ThemeProvider>
      </SessionProvider>
    )
  }

  return render(ui, { wrapper: Wrapper, ...renderOptions })
}

// Helper to create mock API responses
export const createMockApiResponse = (data: any, status = 200) => {
  return Promise.resolve({
    ok: status >= 200 && status < 300,
    status,
    json: () => Promise.resolve(data),
    text: () => Promise.resolve(JSON.stringify(data)),
  } as Response)
}

// Helper to wait for async operations
export const waitFor = (ms: number) => new Promise(resolve => setTimeout(resolve, ms))

// Helper to create mock events
export const createMockEvent = (type: string, properties: any = {}) => {
  return {
    type,
    preventDefault: vi.fn(),
    stopPropagation: vi.fn(),
    target: { value: '' },
    currentTarget: { value: '' },
    ...properties,
  }
}

// Helper to mock router push
export const mockRouterPush = vi.fn()

// Helper to mock console methods
export const mockConsole = {
  log: vi.fn(),
  error: vi.fn(),
  warn: vi.fn(),
  info: vi.fn(),
}

// Test data generators
export const generateMockResume = (overrides: Partial<typeof mockResumes[0]> = {}) => ({
  ...mockResumes[0],
  ...overrides,
  id: overrides.id || `test-resume-${Date.now()}`,
})

export const generateMockTemplate = (overrides: Partial<typeof mockTemplates[0]> = {}) => ({
  ...mockTemplates[0],
  ...overrides,
  id: overrides.id || `test-template-${Date.now()}`,
})

// Custom matchers for testing
export const customMatchers = {
  toHaveGlassEffect: (element: HTMLElement) => {
    const hasGlassClass = element.classList.contains('glass-card') || 
                         element.classList.contains('glass-panel') || 
                         element.classList.contains('glass-input')
    
    return {
      pass: hasGlassClass,
      message: () => hasGlassClass 
        ? `Expected element not to have glass effect classes`
        : `Expected element to have glass effect classes (glass-card, glass-panel, or glass-input)`
    }
  },
  
  toBeResponsive: (element: HTMLElement) => {
    const hasResponsiveClasses = element.className.includes('sm:') || 
                                element.className.includes('md:') || 
                                element.className.includes('lg:') || 
                                element.className.includes('xl:')
    
    return {
      pass: hasResponsiveClasses,
      message: () => hasResponsiveClasses
        ? `Expected element not to have responsive classes`
        : `Expected element to have responsive classes (sm:, md:, lg:, xl:)`
    }
  }
}

// Export everything for easy importing
export * from '@testing-library/react'
export { renderWithProviders as render }
