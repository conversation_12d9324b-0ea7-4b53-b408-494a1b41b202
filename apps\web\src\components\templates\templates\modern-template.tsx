'use client';

import { ResumeSectionType, WorkExperience, Education } from '@careercraft/shared/types/resume';
import { TemplateRenderContext } from '@careercraft/shared/types/template';
import { Icons } from '@/components/ui/icons';
import { cn } from '@/lib/utils';

interface ModernTemplateProps {
  context: TemplateRenderContext;
  interactive?: boolean;
  preview?: boolean;
}

export function ModernTemplate({ context, interactive = false, preview = false }: ModernTemplateProps) {
  const { template, resume } = context;
  const { personalInfo, sections } = resume;
  const { style, layout } = template;

  // Helper functions
  const formatDate = (dateString: string) => {
    if (!dateString) return '';
    const date = new Date(dateString + '-01');
    return date.toLocaleDateString('en-US', { year: 'numeric', month: 'short' });
  };

  const formatDateRange = (startDate: string, endDate?: string, isCurrent?: boolean) => {
    const start = formatDate(startDate);
    if (isCurrent) return `${start} - Present`;
    if (!endDate) return start;
    return `${start} - ${formatDate(endDate)}`;
  };

  const getWorkExperience = () => {
    const workSection = sections.find(s => s.type === ResumeSectionType.WORK_EXPERIENCE);
    return (workSection?.data as WorkExperience[]) || [];
  };

  const getEducation = () => {
    const educationSection = sections.find(s => s.type === ResumeSectionType.EDUCATION);
    return (educationSection?.data as Education[]) || [];
  };

  const getVisibleSections = () => {
    return sections.filter(section => section.isVisible).sort((a, b) => a.order - b.order);
  };

  // Style variables
  const cssVariables = {
    '--primary-color': style.colors.primary,
    '--secondary-color': style.colors.secondary,
    '--accent-color': style.colors.accent,
    '--text-color': style.colors.text,
    '--text-light-color': style.colors.textLight,
    '--background-color': style.colors.background,
    '--border-color': style.colors.border,
    '--divider-color': style.colors.divider,
    '--font-family': style.fontFamily,
    '--font-size-h1': `${style.fontSize.heading1}px`,
    '--font-size-h2': `${style.fontSize.heading2}px`,
    '--font-size-h3': `${style.fontSize.heading3}px`,
    '--font-size-body': `${style.fontSize.body}px`,
    '--font-size-small': `${style.fontSize.small}px`,
    '--line-height-normal': style.lineHeight.normal,
    '--spacing-sm': `${style.spacing.sm}px`,
    '--spacing-md': `${style.spacing.md}px`,
    '--spacing-lg': `${style.spacing.lg}px`,
    '--page-margin-top': `${style.layout.pageMargin.top}px`,
    '--page-margin-right': `${style.layout.pageMargin.right}px`,
    '--page-margin-bottom': `${style.layout.pageMargin.bottom}px`,
    '--page-margin-left': `${style.layout.pageMargin.left}px`,
    '--section-spacing': `${style.layout.sectionSpacing}px`,
    '--item-spacing': `${style.layout.itemSpacing}px`,
  } as React.CSSProperties;

  return (
    <div 
      className="modern-template w-full max-w-4xl mx-auto bg-white text-gray-900 print:max-w-none print:mx-0"
      style={cssVariables}
    >
      {/* Header Section */}
      <header 
        className={cn(
          'relative overflow-hidden',
          style.components.header.alignment === 'center' && 'text-center',
          style.components.header.alignment === 'right' && 'text-right'
        )}
        style={{
          backgroundColor: style.components.header.backgroundColor || 'transparent',
          padding: `${style.spacing.lg}px ${style.layout.pageMargin.left}px`,
        }}
      >
        {/* Background accent */}
        <div 
          className="absolute top-0 left-0 w-full h-2"
          style={{ backgroundColor: style.colors.primary }}
        />
        
        <div className="relative z-10">
          {/* Name */}
          <h1 
            className="font-bold mb-2"
            style={{
              fontSize: style.fontSize.heading1,
              fontWeight: style.fontWeight.bold,
              color: style.colors.text,
              lineHeight: style.lineHeight.tight,
            }}
          >
            {personalInfo.firstName} {personalInfo.lastName}
          </h1>

          {/* Contact Information */}
          <div 
            className={cn(
              'flex flex-wrap gap-4 text-sm',
              style.components.contact.layout === 'vertical' && 'flex-col gap-2',
              style.components.contact.layout === 'grid' && 'grid grid-cols-2 md:grid-cols-3 gap-2',
              style.components.header.alignment === 'center' && 'justify-center',
              style.components.header.alignment === 'right' && 'justify-end'
            )}
            style={{
              fontSize: style.fontSize.small,
              color: style.colors.textLight,
            }}
          >
            {personalInfo.email && (
              <div className="flex items-center gap-1">
                {style.components.contact.showIcons && <Icons.mail className="h-4 w-4" />}
                <span>{personalInfo.email}</span>
              </div>
            )}
            {personalInfo.phone && (
              <div className="flex items-center gap-1">
                {style.components.contact.showIcons && <Icons.phone className="h-4 w-4" />}
                <span>{personalInfo.phone}</span>
              </div>
            )}
            {personalInfo.location && (
              <div className="flex items-center gap-1">
                {style.components.contact.showIcons && <Icons.mapPin className="h-4 w-4" />}
                <span>{personalInfo.location}</span>
              </div>
            )}
            {personalInfo.website && (
              <div className="flex items-center gap-1">
                {style.components.contact.showIcons && <Icons.externalLink className="h-4 w-4" />}
                <a 
                  href={personalInfo.website} 
                  className="hover:underline"
                  style={{ color: style.colors.primary }}
                >
                  Website
                </a>
              </div>
            )}
            {personalInfo.linkedin && (
              <div className="flex items-center gap-1">
                {style.components.contact.showIcons && <Icons.linkedin className="h-4 w-4" />}
                <a 
                  href={personalInfo.linkedin} 
                  className="hover:underline"
                  style={{ color: style.colors.primary }}
                >
                  LinkedIn
                </a>
              </div>
            )}
          </div>
        </div>

        {style.components.header.showDivider && (
          <div 
            className="mt-4 h-px w-full"
            style={{ backgroundColor: style.colors.divider }}
          />
        )}
      </header>

      {/* Main Content */}
      <main 
        className="px-8 py-6"
        style={{
          paddingLeft: style.layout.pageMargin.left,
          paddingRight: style.layout.pageMargin.right,
        }}
      >
        {/* Professional Summary */}
        {personalInfo.summary && (
          <section 
            className="mb-8"
            style={{ marginBottom: style.layout.sectionSpacing }}
          >
            <h2 
              className={cn(
                'font-semibold mb-3',
                style.components.section.titleCase === 'uppercase' && 'uppercase',
                style.components.section.titleCase === 'lowercase' && 'lowercase',
                style.components.section.titleCase === 'capitalize' && 'capitalize'
              )}
              style={{
                fontSize: style.fontSize.heading2,
                fontWeight: style.fontWeight.semibold,
                color: style.colors.primary,
                marginBottom: style.spacing.md,
                ...(style.components.section.titleStyle === 'underline' && {
                  borderBottom: `2px solid ${style.colors.primary}`,
                  paddingBottom: style.spacing.sm,
                }),
                ...(style.components.section.titleStyle === 'background' && {
                  backgroundColor: style.colors.primary,
                  color: 'white',
                  padding: `${style.spacing.sm}px ${style.spacing.md}px`,
                  borderRadius: '4px',
                }),
                ...(style.components.section.titleStyle === 'border' && {
                  border: `2px solid ${style.colors.primary}`,
                  padding: `${style.spacing.sm}px ${style.spacing.md}px`,
                  borderRadius: '4px',
                }),
              }}
            >
              Professional Summary
            </h2>
            <p 
              className="leading-relaxed"
              style={{
                fontSize: style.fontSize.body,
                lineHeight: style.lineHeight.relaxed,
                color: style.colors.text,
              }}
            >
              {personalInfo.summary}
            </p>
          </section>
        )}

        {/* Work Experience */}
        {getWorkExperience().length > 0 && (
          <section 
            className="mb-8"
            style={{ marginBottom: style.layout.sectionSpacing }}
          >
            <h2 
              className={cn(
                'font-semibold mb-4',
                style.components.section.titleCase === 'uppercase' && 'uppercase',
                style.components.section.titleCase === 'lowercase' && 'lowercase',
                style.components.section.titleCase === 'capitalize' && 'capitalize'
              )}
              style={{
                fontSize: style.fontSize.heading2,
                fontWeight: style.fontWeight.semibold,
                color: style.colors.primary,
                marginBottom: style.spacing.md,
                ...(style.components.section.titleStyle === 'underline' && {
                  borderBottom: `2px solid ${style.colors.primary}`,
                  paddingBottom: style.spacing.sm,
                }),
                ...(style.components.section.titleStyle === 'background' && {
                  backgroundColor: style.colors.primary,
                  color: 'white',
                  padding: `${style.spacing.sm}px ${style.spacing.md}px`,
                  borderRadius: '4px',
                }),
                ...(style.components.section.titleStyle === 'border' && {
                  border: `2px solid ${style.colors.primary}`,
                  padding: `${style.spacing.sm}px ${style.spacing.md}px`,
                  borderRadius: '4px',
                }),
              }}
            >
              Work Experience
            </h2>
            <div className="space-y-6">
              {getWorkExperience().map((experience, index) => (
                <div 
                  key={experience.id} 
                  className="relative"
                  style={{ marginBottom: style.layout.itemSpacing }}
                >
                  {/* Timeline indicator */}
                  <div 
                    className="absolute left-0 top-2 w-3 h-3 rounded-full border-2"
                    style={{
                      backgroundColor: style.colors.background,
                      borderColor: style.colors.primary,
                    }}
                  />
                  
                  <div className="ml-6">
                    <div className="flex flex-col md:flex-row md:justify-between md:items-start mb-2">
                      <div>
                        <h3 
                          className="font-medium"
                          style={{
                            fontSize: style.fontSize.heading3,
                            fontWeight: style.fontWeight.medium,
                            color: style.colors.text,
                          }}
                        >
                          {experience.position}
                        </h3>
                        <p 
                          className="font-medium"
                          style={{
                            fontSize: style.fontSize.body,
                            fontWeight: style.fontWeight.medium,
                            color: style.colors.primary,
                          }}
                        >
                          {experience.company}
                        </p>
                      </div>
                      <div 
                        className="text-sm md:text-right"
                        style={{
                          fontSize: style.fontSize.small,
                          color: style.colors.textLight,
                        }}
                      >
                        <p>{formatDateRange(experience.startDate, experience.endDate, experience.isCurrentRole)}</p>
                        <p>{experience.location}</p>
                      </div>
                    </div>
                    
                    <p 
                      className="mb-3 leading-relaxed"
                      style={{
                        fontSize: style.fontSize.body,
                        lineHeight: style.lineHeight.normal,
                        color: style.colors.text,
                        marginBottom: style.spacing.sm,
                      }}
                    >
                      {experience.description}
                    </p>
                    
                    {experience.achievements.length > 0 && (
                      <ul 
                        className="space-y-1 mb-3"
                        style={{
                          marginLeft: style.components.list.indentation,
                          marginBottom: style.spacing.sm,
                        }}
                      >
                        {experience.achievements.map((achievement, achievementIndex) => (
                          <li 
                            key={achievementIndex}
                            className="flex items-start"
                            style={{
                              fontSize: style.fontSize.body,
                              lineHeight: style.lineHeight.normal,
                              color: style.colors.text,
                            }}
                          >
                            {style.components.list.bulletStyle === 'bullet' && (
                              <span 
                                className="inline-block w-2 h-2 rounded-full mt-2 mr-3 flex-shrink-0"
                                style={{ backgroundColor: style.colors.primary }}
                              />
                            )}
                            {style.components.list.bulletStyle === 'dash' && (
                              <span 
                                className="inline-block mt-2 mr-3 flex-shrink-0"
                                style={{ color: style.colors.primary }}
                              >
                                –
                              </span>
                            )}
                            {style.components.list.bulletStyle === 'arrow' && (
                              <span 
                                className="inline-block mt-2 mr-3 flex-shrink-0"
                                style={{ color: style.colors.primary }}
                              >
                                →
                              </span>
                            )}
                            <span>{achievement}</span>
                          </li>
                        ))}
                      </ul>
                    )}
                    
                    {experience.technologies && experience.technologies.length > 0 && (
                      <div className="flex flex-wrap gap-2">
                        {experience.technologies.map((tech, techIndex) => (
                          <span
                            key={techIndex}
                            className="px-2 py-1 rounded text-xs"
                            style={{
                              backgroundColor: `${style.colors.primary}15`,
                              color: style.colors.primary,
                              fontSize: style.fontSize.small,
                            }}
                          >
                            {tech}
                          </span>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </section>
        )}

        {/* Education */}
        {getEducation().length > 0 && (
          <section 
            className="mb-8"
            style={{ marginBottom: style.layout.sectionSpacing }}
          >
            <h2 
              className={cn(
                'font-semibold mb-4',
                style.components.section.titleCase === 'uppercase' && 'uppercase',
                style.components.section.titleCase === 'lowercase' && 'lowercase',
                style.components.section.titleCase === 'capitalize' && 'capitalize'
              )}
              style={{
                fontSize: style.fontSize.heading2,
                fontWeight: style.fontWeight.semibold,
                color: style.colors.primary,
                marginBottom: style.spacing.md,
                ...(style.components.section.titleStyle === 'underline' && {
                  borderBottom: `2px solid ${style.colors.primary}`,
                  paddingBottom: style.spacing.sm,
                }),
              }}
            >
              Education
            </h2>
            <div className="space-y-4">
              {getEducation().map((education) => (
                <div 
                  key={education.id}
                  style={{ marginBottom: style.layout.itemSpacing }}
                >
                  <div className="flex flex-col md:flex-row md:justify-between md:items-start">
                    <div>
                      <h3 
                        className="font-medium"
                        style={{
                          fontSize: style.fontSize.heading3,
                          fontWeight: style.fontWeight.medium,
                          color: style.colors.text,
                        }}
                      >
                        {education.degree}
                      </h3>
                      <p 
                        style={{
                          fontSize: style.fontSize.body,
                          color: style.colors.textLight,
                        }}
                      >
                        {education.field}
                      </p>
                      <p 
                        className="font-medium"
                        style={{
                          fontSize: style.fontSize.body,
                          fontWeight: style.fontWeight.medium,
                          color: style.colors.primary,
                        }}
                      >
                        {education.institution}
                      </p>
                    </div>
                    <div 
                      className="text-sm md:text-right"
                      style={{
                        fontSize: style.fontSize.small,
                        color: style.colors.textLight,
                      }}
                    >
                      <p>{formatDateRange(education.startDate, education.endDate, education.isCurrentlyEnrolled)}</p>
                      <p>{education.location}</p>
                    </div>
                  </div>
                  
                  {education.gpa && (
                    <p 
                      className="mt-1"
                      style={{
                        fontSize: style.fontSize.small,
                        color: style.colors.textLight,
                      }}
                    >
                      GPA: {education.gpa}
                    </p>
                  )}
                  
                  {education.honors && education.honors.length > 0 && (
                    <p 
                      className="mt-1"
                      style={{
                        fontSize: style.fontSize.small,
                        color: style.colors.text,
                      }}
                    >
                      <span className="font-medium">Honors: </span>
                      {education.honors.join(', ')}
                    </p>
                  )}
                </div>
              ))}
            </div>
          </section>
        )}
      </main>
    </div>
  );
}
