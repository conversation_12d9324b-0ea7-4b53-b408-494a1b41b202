/**
 * Collaboration API Routes Integration Tests
 * 
 * Tests for collaboration session and comment API endpoints
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { NextRequest } from 'next/server'
import { getServerSession } from 'next-auth'
import { 
  GET as sessionGet, 
  POST as sessionPost, 
  PUT as sessionPut, 
  DELETE as sessionDelete 
} from '@/app/api/collaboration/session/route'
import { 
  GET as commentsGet, 
  POST as commentsPost, 
  PUT as commentsPut 
} from '@/app/api/collaboration/comments/route'

// Mock dependencies
vi.mock('next-auth', () => ({
  getServerSession: vi.fn()
}))

vi.mock('@/lib/auth', () => ({
  authOptions: {}
}))

vi.mock('@/lib/collaboration/service', () => ({
  collaborationService: {
    createSession: vi.fn(),
    getSession: vi.fn(),
    getSessionByToken: vi.fn(),
    getUserSessions: vi.fn(),
    inviteUser: vi.fn(),
    removeUser: vi.fn(),
    deleteSession: vi.fn(),
    getUserPermission: vi.fn(),
    createComment: vi.fn(),
    getComments: vi.fn(),
    resolveComment: vi.fn()
  }
}))

describe('Collaboration API Routes', () => {
  const mockSession = {
    user: {
      id: 'test-user-id',
      email: '<EMAIL>',
      name: 'Test User'
    }
  }

  beforeEach(() => {
    vi.clearAllMocks()
    ;(getServerSession as any).mockResolvedValue(mockSession)
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('Session API Routes', () => {
    describe('POST /api/collaboration/session', () => {
      it('should create collaboration session successfully', async () => {
        const { collaborationService } = await import('@/lib/collaboration/service')
        
        const mockCollaborationSession = {
          id: 'session-123',
          resumeId: 'resume-123',
          ownerId: 'test-user-id',
          sessionToken: 'token-123',
          expiresAt: new Date(),
          createdAt: new Date(),
          permissions: [],
          activeUsers: 0
        }

        ;(collaborationService.createSession as any).mockResolvedValue(mockCollaborationSession)

        const requestBody = {
          resumeId: 'resume-123',
          expiresIn: 24 * 60 * 60 * 1000
        }

        const request = new NextRequest('http://localhost:3000/api/collaboration/session', {
          method: 'POST',
          body: JSON.stringify(requestBody),
          headers: { 'Content-Type': 'application/json' }
        })

        const response = await sessionPost(request)
        const data = await response.json()

        expect(response.status).toBe(200)
        expect(data.success).toBe(true)
        expect(data.session.id).toBe('session-123')
        expect(data.message).toBe('Collaboration session created successfully')
      })

      it('should require authentication', async () => {
        ;(getServerSession as any).mockResolvedValue(null)

        const request = new NextRequest('http://localhost:3000/api/collaboration/session', {
          method: 'POST',
          body: JSON.stringify({ resumeId: 'resume-123' }),
          headers: { 'Content-Type': 'application/json' }
        })

        const response = await sessionPost(request)
        const data = await response.json()

        expect(response.status).toBe(401)
        expect(data.error).toBe('Unauthorized')
      })

      it('should validate request data', async () => {
        const request = new NextRequest('http://localhost:3000/api/collaboration/session', {
          method: 'POST',
          body: JSON.stringify({ invalidField: 'value' }),
          headers: { 'Content-Type': 'application/json' }
        })

        const response = await sessionPost(request)
        const data = await response.json()

        expect(response.status).toBe(400)
        expect(data.error).toBe('Invalid request data')
      })

      it('should handle service errors', async () => {
        const { collaborationService } = await import('@/lib/collaboration/service')
        ;(collaborationService.createSession as any).mockRejectedValue(new Error('Resume not found'))

        const request = new NextRequest('http://localhost:3000/api/collaboration/session', {
          method: 'POST',
          body: JSON.stringify({ resumeId: 'nonexistent-resume' }),
          headers: { 'Content-Type': 'application/json' }
        })

        const response = await sessionPost(request)
        const data = await response.json()

        expect(response.status).toBe(500)
        expect(data.error).toBe('Resume not found')
      })
    })

    describe('GET /api/collaboration/session', () => {
      it('should get session by ID', async () => {
        const { collaborationService } = await import('@/lib/collaboration/service')
        
        const mockSession = {
          id: 'session-123',
          resumeId: 'resume-123',
          ownerId: 'test-user-id',
          sessionToken: 'token-123',
          expiresAt: new Date(),
          createdAt: new Date(),
          permissions: [],
          activeUsers: 0
        }

        ;(collaborationService.getSession as any).mockResolvedValue(mockSession)
        ;(collaborationService.getUserPermission as any).mockResolvedValue('admin')

        const request = new NextRequest('http://localhost:3000/api/collaboration/session?sessionId=session-123')

        const response = await sessionGet(request)
        const data = await response.json()

        expect(response.status).toBe(200)
        expect(data.session.id).toBe('session-123')
      })

      it('should get session by token', async () => {
        const { collaborationService } = await import('@/lib/collaboration/service')
        
        const mockSession = {
          id: 'session-123',
          sessionToken: 'token-123'
        }

        ;(collaborationService.getSessionByToken as any).mockResolvedValue(mockSession)

        const request = new NextRequest('http://localhost:3000/api/collaboration/session?sessionToken=token-123')

        const response = await sessionGet(request)
        const data = await response.json()

        expect(response.status).toBe(200)
        expect(data.session.sessionToken).toBe('token-123')
      })

      it('should list user sessions', async () => {
        const { collaborationService } = await import('@/lib/collaboration/service')
        
        const mockSessions = [
          { id: 'session-1', resumeId: 'resume-1' },
          { id: 'session-2', resumeId: 'resume-2' }
        ]

        ;(collaborationService.getUserSessions as any).mockResolvedValue(mockSessions)

        const request = new NextRequest('http://localhost:3000/api/collaboration/session?action=list')

        const response = await sessionGet(request)
        const data = await response.json()

        expect(response.status).toBe(200)
        expect(data.sessions).toHaveLength(2)
      })

      it('should return 404 for non-existent session', async () => {
        const { collaborationService } = await import('@/lib/collaboration/service')
        ;(collaborationService.getSession as any).mockResolvedValue(null)

        const request = new NextRequest('http://localhost:3000/api/collaboration/session?sessionId=nonexistent')

        const response = await sessionGet(request)
        const data = await response.json()

        expect(response.status).toBe(404)
        expect(data.error).toBe('Session not found')
      })

      it('should return 403 for unauthorized access', async () => {
        const { collaborationService } = await import('@/lib/collaboration/service')
        
        const mockSession = { id: 'session-123' }
        ;(collaborationService.getSession as any).mockResolvedValue(mockSession)
        ;(collaborationService.getUserPermission as any).mockResolvedValue(null)

        const request = new NextRequest('http://localhost:3000/api/collaboration/session?sessionId=session-123')

        const response = await sessionGet(request)
        const data = await response.json()

        expect(response.status).toBe(403)
        expect(data.error).toBe('Access denied')
      })
    })

    describe('PUT /api/collaboration/session', () => {
      it('should invite user to session', async () => {
        const { collaborationService } = await import('@/lib/collaboration/service')
        ;(collaborationService.inviteUser as any).mockResolvedValue(true)

        const requestBody = {
          action: 'invite',
          sessionId: 'session-123',
          userId: 'user-456',
          permissionLevel: 'edit'
        }

        const request = new NextRequest('http://localhost:3000/api/collaboration/session', {
          method: 'PUT',
          body: JSON.stringify(requestBody),
          headers: { 'Content-Type': 'application/json' }
        })

        const response = await sessionPut(request)
        const data = await response.json()

        expect(response.status).toBe(200)
        expect(data.success).toBe(true)
        expect(data.message).toBe('User invited successfully')
      })

      it('should remove user from session', async () => {
        const { collaborationService } = await import('@/lib/collaboration/service')
        ;(collaborationService.removeUser as any).mockResolvedValue(true)

        const requestBody = {
          action: 'remove',
          sessionId: 'session-123',
          userId: 'user-456'
        }

        const request = new NextRequest('http://localhost:3000/api/collaboration/session', {
          method: 'PUT',
          body: JSON.stringify(requestBody),
          headers: { 'Content-Type': 'application/json' }
        })

        const response = await sessionPut(request)
        const data = await response.json()

        expect(response.status).toBe(200)
        expect(data.success).toBe(true)
        expect(data.message).toBe('User removed successfully')
      })

      it('should handle invalid action', async () => {
        const requestBody = {
          action: 'invalid',
          sessionId: 'session-123'
        }

        const request = new NextRequest('http://localhost:3000/api/collaboration/session', {
          method: 'PUT',
          body: JSON.stringify(requestBody),
          headers: { 'Content-Type': 'application/json' }
        })

        const response = await sessionPut(request)
        const data = await response.json()

        expect(response.status).toBe(400)
        expect(data.error).toBe('Invalid action')
      })
    })

    describe('DELETE /api/collaboration/session', () => {
      it('should delete session as owner', async () => {
        const { collaborationService } = await import('@/lib/collaboration/service')
        
        const mockSession = {
          id: 'session-123',
          ownerId: 'test-user-id'
        }

        ;(collaborationService.getSession as any).mockResolvedValue(mockSession)
        ;(collaborationService.deleteSession as any).mockResolvedValue(true)

        const request = new NextRequest('http://localhost:3000/api/collaboration/session?sessionId=session-123', {
          method: 'DELETE'
        })

        const response = await sessionDelete(request)
        const data = await response.json()

        expect(response.status).toBe(200)
        expect(data.success).toBe(true)
        expect(data.message).toBe('Session deleted successfully')
      })

      it('should deny deletion for non-owner', async () => {
        const { collaborationService } = await import('@/lib/collaboration/service')
        
        const mockSession = {
          id: 'session-123',
          ownerId: 'other-user-id'
        }

        ;(collaborationService.getSession as any).mockResolvedValue(mockSession)

        const request = new NextRequest('http://localhost:3000/api/collaboration/session?sessionId=session-123', {
          method: 'DELETE'
        })

        const response = await sessionDelete(request)
        const data = await response.json()

        expect(response.status).toBe(403)
        expect(data.error).toBe('Access denied')
      })

      it('should require session ID', async () => {
        const request = new NextRequest('http://localhost:3000/api/collaboration/session', {
          method: 'DELETE'
        })

        const response = await sessionDelete(request)
        const data = await response.json()

        expect(response.status).toBe(400)
        expect(data.error).toBe('Session ID required')
      })
    })
  })

  describe('Comments API Routes', () => {
    describe('POST /api/collaboration/comments', () => {
      it('should create comment successfully', async () => {
        const { collaborationService } = await import('@/lib/collaboration/service')
        
        const mockComment = {
          id: 'comment-123',
          userId: 'test-user-id',
          sectionPath: 'experience.0',
          content: 'Great experience!',
          parentId: null,
          isResolved: false,
          createdAt: new Date(),
          updatedAt: new Date(),
          user: {
            id: 'test-user-id',
            name: 'Test User',
            image: 'avatar.jpg'
          },
          replies: []
        }

        ;(collaborationService.createComment as any).mockResolvedValue(mockComment)

        const requestBody = {
          sessionId: 'session-123',
          sectionPath: 'experience.0',
          content: 'Great experience!'
        }

        const request = new NextRequest('http://localhost:3000/api/collaboration/comments', {
          method: 'POST',
          body: JSON.stringify(requestBody),
          headers: { 'Content-Type': 'application/json' }
        })

        const response = await commentsPost(request)
        const data = await response.json()

        expect(response.status).toBe(200)
        expect(data.success).toBe(true)
        expect(data.comment.content).toBe('Great experience!')
      })

      it('should handle permission errors', async () => {
        const { collaborationService } = await import('@/lib/collaboration/service')
        ;(collaborationService.createComment as any).mockRejectedValue(
          new Error('Insufficient permissions to comment')
        )

        const requestBody = {
          sessionId: 'session-123',
          sectionPath: 'experience.0',
          content: 'Great experience!'
        }

        const request = new NextRequest('http://localhost:3000/api/collaboration/comments', {
          method: 'POST',
          body: JSON.stringify(requestBody),
          headers: { 'Content-Type': 'application/json' }
        })

        const response = await commentsPost(request)
        const data = await response.json()

        expect(response.status).toBe(500)
        expect(data.error).toBe('Insufficient permissions to comment')
      })
    })

    describe('GET /api/collaboration/comments', () => {
      it('should get comments for session', async () => {
        const { collaborationService } = await import('@/lib/collaboration/service')
        
        const mockComments = [
          {
            id: 'comment-1',
            content: 'First comment',
            sectionPath: 'experience.0',
            replies: []
          },
          {
            id: 'comment-2',
            content: 'Second comment',
            sectionPath: 'skills',
            replies: []
          }
        ]

        ;(collaborationService.getUserPermission as any).mockResolvedValue('view')
        ;(collaborationService.getComments as any).mockResolvedValue(mockComments)

        const request = new NextRequest('http://localhost:3000/api/collaboration/comments?sessionId=session-123')

        const response = await commentsGet(request)
        const data = await response.json()

        expect(response.status).toBe(200)
        expect(data.comments).toHaveLength(2)
      })

      it('should filter comments by section path', async () => {
        const { collaborationService } = await import('@/lib/collaboration/service')
        
        ;(collaborationService.getUserPermission as any).mockResolvedValue('view')
        ;(collaborationService.getComments as any).mockResolvedValue([])

        const request = new NextRequest('http://localhost:3000/api/collaboration/comments?sessionId=session-123&sectionPath=experience.0')

        const response = await commentsGet(request)

        expect(response.status).toBe(200)
        expect(collaborationService.getComments).toHaveBeenCalledWith('session-123', 'experience.0')
      })

      it('should deny access without permission', async () => {
        const { collaborationService } = await import('@/lib/collaboration/service')
        ;(collaborationService.getUserPermission as any).mockResolvedValue(null)

        const request = new NextRequest('http://localhost:3000/api/collaboration/comments?sessionId=session-123')

        const response = await commentsGet(request)
        const data = await response.json()

        expect(response.status).toBe(403)
        expect(data.error).toBe('Access denied')
      })

      it('should require session ID', async () => {
        const request = new NextRequest('http://localhost:3000/api/collaboration/comments')

        const response = await commentsGet(request)
        const data = await response.json()

        expect(response.status).toBe(400)
        expect(data.error).toBe('Session ID required')
      })
    })

    describe('PUT /api/collaboration/comments', () => {
      it('should resolve comment', async () => {
        const { collaborationService } = await import('@/lib/collaboration/service')
        ;(collaborationService.resolveComment as any).mockResolvedValue(true)

        const requestBody = {
          action: 'resolve',
          commentId: 'comment-123'
        }

        const request = new NextRequest('http://localhost:3000/api/collaboration/comments', {
          method: 'PUT',
          body: JSON.stringify(requestBody),
          headers: { 'Content-Type': 'application/json' }
        })

        const response = await commentsPut(request)
        const data = await response.json()

        expect(response.status).toBe(200)
        expect(data.success).toBe(true)
        expect(data.message).toBe('Comment resolved successfully')
      })

      it('should handle invalid action', async () => {
        const requestBody = {
          action: 'invalid',
          commentId: 'comment-123'
        }

        const request = new NextRequest('http://localhost:3000/api/collaboration/comments', {
          method: 'PUT',
          body: JSON.stringify(requestBody),
          headers: { 'Content-Type': 'application/json' }
        })

        const response = await commentsPut(request)
        const data = await response.json()

        expect(response.status).toBe(400)
        expect(data.error).toBe('Invalid action')
      })
    })
  })
})
