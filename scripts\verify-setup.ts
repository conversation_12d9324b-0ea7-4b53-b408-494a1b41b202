#!/usr/bin/env tsx

/**
 * Setup Verification Script
 * 
 * This script verifies that all components are working correctly
 */

import { config } from 'dotenv';
import { join } from 'path';
import { existsSync } from 'fs';

// Load environment variables
config({ path: join(__dirname, '../.env.local') });

interface VerificationResult {
  component: string;
  status: 'PASS' | 'FAIL' | 'WARNING';
  message: string;
  details?: string;
}

class SetupVerifier {
  private results: VerificationResult[] = [];

  private addResult(component: string, status: 'PASS' | 'FAIL' | 'WARNING', message: string, details?: string) {
    this.results.push({ component, status, message, details });
  }

  async verifyEnvironment(): Promise<void> {
    console.log('🔍 Verifying Environment Setup...\n');

    // Check Node.js version
    const nodeVersion = process.version;
    const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
    
    if (majorVersion >= 18) {
      this.addResult('Node.js', 'PASS', `Version ${nodeVersion} is supported`);
    } else {
      this.addResult('Node.js', 'FAIL', `Version ${nodeVersion} is too old. Requires 18+`);
    }

    // Check environment file
    const envPath = join(__dirname, '../.env.local');
    if (existsSync(envPath)) {
      this.addResult('Environment', 'PASS', '.env.local file exists');
    } else {
      this.addResult('Environment', 'FAIL', '.env.local file missing');
    }

    // Check required directories
    const requiredDirs = [
      'apps/web',
      'packages/database',
      'packages/shared',
      'scripts',
    ];

    for (const dir of requiredDirs) {
      const dirPath = join(__dirname, '..', dir);
      if (existsSync(dirPath)) {
        this.addResult('Directory Structure', 'PASS', `${dir} exists`);
      } else {
        this.addResult('Directory Structure', 'FAIL', `${dir} missing`);
      }
    }

    // Check package.json files
    const packageFiles = [
      'package.json',
      'apps/web/package.json',
      'packages/database/package.json',
      'packages/shared/package.json',
    ];

    for (const file of packageFiles) {
      const filePath = join(__dirname, '..', file);
      if (existsSync(filePath)) {
        this.addResult('Package Files', 'PASS', `${file} exists`);
      } else {
        this.addResult('Package Files', 'FAIL', `${file} missing`);
      }
    }
  }

  async verifyDatabase(): Promise<void> {
    console.log('🗄️  Verifying Database Setup...\n');

    try {
      const { PrismaClient } = await import('@careercraft/database');
      const prisma = new PrismaClient();

      // Test database connection
      await prisma.$connect();
      this.addResult('Database', 'PASS', 'Database connection successful');

      // Check if tables exist
      try {
        const userCount = await prisma.user.count();
        this.addResult('Database Schema', 'PASS', `User table exists with ${userCount} records`);
      } catch (error) {
        this.addResult('Database Schema', 'WARNING', 'Tables may not be created yet', 'Run: npm run db:push');
      }

      await prisma.$disconnect();
    } catch (error) {
      this.addResult('Database', 'FAIL', 'Database connection failed', error instanceof Error ? error.message : 'Unknown error');
    }
  }

  async verifyDependencies(): Promise<void> {
    console.log('📦 Verifying Dependencies...\n');

    try {
      // Check if node_modules exists
      const nodeModulesPath = join(__dirname, '../node_modules');
      if (existsSync(nodeModulesPath)) {
        this.addResult('Dependencies', 'PASS', 'node_modules directory exists');
      } else {
        this.addResult('Dependencies', 'FAIL', 'node_modules missing', 'Run: npm install');
      }

      // Check critical dependencies
      const criticalDeps = [
        'next',
        'react',
        'typescript',
        '@prisma/client',
        'tailwindcss',
      ];

      for (const dep of criticalDeps) {
        try {
          require.resolve(dep);
          this.addResult('Critical Dependencies', 'PASS', `${dep} is available`);
        } catch (error) {
          this.addResult('Critical Dependencies', 'FAIL', `${dep} is missing`);
        }
      }
    } catch (error) {
      this.addResult('Dependencies', 'FAIL', 'Dependency check failed', error instanceof Error ? error.message : 'Unknown error');
    }
  }

  async verifyBuild(): Promise<void> {
    console.log('🔨 Verifying Build Setup...\n');

    // Check if TypeScript config exists
    const tsConfigPath = join(__dirname, '../tsconfig.json');
    if (existsSync(tsConfigPath)) {
      this.addResult('TypeScript', 'PASS', 'tsconfig.json exists');
    } else {
      this.addResult('TypeScript', 'FAIL', 'tsconfig.json missing');
    }

    // Check if Tailwind config exists
    const tailwindConfigPath = join(__dirname, '../tailwind.config.js');
    if (existsSync(tailwindConfigPath)) {
      this.addResult('Tailwind CSS', 'PASS', 'tailwind.config.js exists');
    } else {
      this.addResult('Tailwind CSS', 'WARNING', 'tailwind.config.js missing');
    }

    // Check if Next.js config exists
    const nextConfigPath = join(__dirname, '../apps/web/next.config.js');
    if (existsSync(nextConfigPath)) {
      this.addResult('Next.js', 'PASS', 'next.config.js exists');
    } else {
      this.addResult('Next.js', 'WARNING', 'next.config.js missing');
    }
  }

  async verifyFeatures(): Promise<void> {
    console.log('🚀 Verifying Feature Components...\n');

    const featureComponents = [
      'apps/web/src/components/ui',
      'apps/web/src/components/resume-builder',
      'apps/web/src/components/templates',
      'apps/web/src/components/ai',
      'apps/web/src/lib/auth.ts',
      'packages/shared/src/types',
      'packages/shared/src/schemas',
    ];

    for (const component of featureComponents) {
      const componentPath = join(__dirname, '..', component);
      if (existsSync(componentPath)) {
        this.addResult('Feature Components', 'PASS', `${component} exists`);
      } else {
        this.addResult('Feature Components', 'FAIL', `${component} missing`);
      }
    }
  }

  async runAllVerifications(): Promise<void> {
    console.log('🔍 CareerCraft - Setup Verification\n');
    console.log('=====================================\n');

    await this.verifyEnvironment();
    await this.verifyDependencies();
    await this.verifyBuild();
    await this.verifyFeatures();
    await this.verifyDatabase();

    this.printSummary();
  }

  private printSummary(): void {
    console.log('\n📊 Verification Summary');
    console.log('========================\n');

    const passed = this.results.filter(r => r.status === 'PASS').length;
    const failed = this.results.filter(r => r.status === 'FAIL').length;
    const warnings = this.results.filter(r => r.status === 'WARNING').length;
    const total = this.results.length;

    console.log(`Total Checks: ${total}`);
    console.log(`✅ Passed: ${passed}`);
    console.log(`⚠️  Warnings: ${warnings}`);
    console.log(`❌ Failed: ${failed}`);
    console.log(`Success Rate: ${((passed / total) * 100).toFixed(1)}%\n`);

    // Group results by status
    const failedResults = this.results.filter(r => r.status === 'FAIL');
    const warningResults = this.results.filter(r => r.status === 'WARNING');

    if (failedResults.length > 0) {
      console.log('❌ Failed Checks:');
      failedResults.forEach(result => {
        console.log(`  - ${result.component}: ${result.message}`);
        if (result.details) {
          console.log(`    Details: ${result.details}`);
        }
      });
      console.log('');
    }

    if (warningResults.length > 0) {
      console.log('⚠️  Warnings:');
      warningResults.forEach(result => {
        console.log(`  - ${result.component}: ${result.message}`);
        if (result.details) {
          console.log(`    Suggestion: ${result.details}`);
        }
      });
      console.log('');
    }

    if (failed === 0) {
      console.log('🎉 All critical checks passed! Your setup is ready for development.');
      console.log('\nNext steps:');
      console.log('1. Run: npm run dev');
      console.log('2. Open: http://localhost:3000');
      console.log('3. Test the features using the Local Testing Guide');
    } else {
      console.log('⚠️  Some critical checks failed. Please fix the issues above before proceeding.');
      console.log('\nCommon fixes:');
      console.log('- Run: npm install');
      console.log('- Run: npm run db:push');
      console.log('- Check your .env.local file');
    }
  }
}

// Run verification
async function main() {
  const verifier = new SetupVerifier();
  await verifier.runAllVerifications();
}

if (require.main === module) {
  main().catch(console.error);
}
