/**
 * Collaboration Service
 * 
 * Database operations and business logic for real-time collaboration features
 */

import { prisma } from '@/lib/db'
import { z } from 'zod'
import { nanoid } from 'nanoid'

// Collaboration schemas
export const CreateSessionSchema = z.object({
  resumeId: z.string(),
  ownerId: z.string(),
  expiresIn: z.number().optional().default(24 * 60 * 60 * 1000) // 24 hours
})

export const InviteUserSchema = z.object({
  sessionId: z.string(),
  userId: z.string(),
  permissionLevel: z.enum(['view', 'comment', 'edit', 'admin']),
  grantedBy: z.string()
})

export const CreateCommentSchema = z.object({
  sessionId: z.string(),
  userId: z.string(),
  sectionPath: z.string(),
  content: z.string(),
  parentId: z.string().optional()
})

export const UpdatePresenceSchema = z.object({
  sessionId: z.string(),
  userId: z.string(),
  status: z.enum(['active', 'idle', 'away']),
  sectionPath: z.string().optional(),
  position: z.number().optional()
})

export type CreateSessionRequest = z.infer<typeof CreateSessionSchema>
export type InviteUserRequest = z.infer<typeof InviteUserSchema>
export type CreateCommentRequest = z.infer<typeof CreateCommentSchema>
export type UpdatePresenceRequest = z.infer<typeof UpdatePresenceSchema>

export interface CollaborationSessionInfo {
  id: string
  resumeId: string
  ownerId: string
  sessionToken: string
  expiresAt: Date
  createdAt: Date
  permissions: Array<{
    userId: string
    permissionLevel: string
    user: {
      id: string
      name: string | null
      email: string
      image: string | null
    }
  }>
  activeUsers: number
}

export interface CommentInfo {
  id: string
  userId: string
  sectionPath: string
  content: string
  parentId: string | null
  isResolved: boolean
  createdAt: Date
  updatedAt: Date
  user: {
    id: string
    name: string | null
    image: string | null
  }
  replies: CommentInfo[]
}

export class CollaborationService {
  /**
   * Create a new collaboration session
   */
  async createSession(request: CreateSessionRequest): Promise<CollaborationSessionInfo> {
    const { resumeId, ownerId, expiresIn } = CreateSessionSchema.parse(request)
    
    // Check if user owns the resume
    const resume = await prisma.resume.findFirst({
      where: {
        id: resumeId,
        userId: ownerId
      }
    })

    if (!resume) {
      throw new Error('Resume not found or access denied')
    }

    // Generate unique session token
    const sessionToken = nanoid(32)
    const expiresAt = new Date(Date.now() + expiresIn)

    // Create collaboration session
    const session = await prisma.collaborationSession.create({
      data: {
        resumeId,
        ownerId,
        sessionToken,
        expiresAt
      },
      include: {
        permissions: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
                image: true
              }
            }
          }
        }
      }
    })

    // Create owner permission
    await prisma.collaborationPermission.create({
      data: {
        sessionId: session.id,
        userId: ownerId,
        permissionLevel: 'admin',
        grantedBy: ownerId
      }
    })

    return {
      id: session.id,
      resumeId: session.resumeId,
      ownerId: session.ownerId,
      sessionToken: session.sessionToken,
      expiresAt: session.expiresAt,
      createdAt: session.createdAt,
      permissions: session.permissions,
      activeUsers: 0
    }
  }

  /**
   * Get collaboration session by token
   */
  async getSessionByToken(sessionToken: string): Promise<CollaborationSessionInfo | null> {
    const session = await prisma.collaborationSession.findUnique({
      where: { sessionToken },
      include: {
        permissions: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
                image: true
              }
            }
          }
        }
      }
    })

    if (!session) return null

    // Check if session is expired
    if (session.expiresAt < new Date()) {
      await this.deleteSession(session.id)
      return null
    }

    return {
      id: session.id,
      resumeId: session.resumeId,
      ownerId: session.ownerId,
      sessionToken: session.sessionToken,
      expiresAt: session.expiresAt,
      createdAt: session.createdAt,
      permissions: session.permissions,
      activeUsers: 0 // Will be populated by WebSocket server
    }
  }

  /**
   * Get collaboration session by ID
   */
  async getSession(sessionId: string): Promise<CollaborationSessionInfo | null> {
    const session = await prisma.collaborationSession.findUnique({
      where: { id: sessionId },
      include: {
        permissions: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
                image: true
              }
            }
          }
        }
      }
    })

    if (!session) return null

    return {
      id: session.id,
      resumeId: session.resumeId,
      ownerId: session.ownerId,
      sessionToken: session.sessionToken,
      expiresAt: session.expiresAt,
      createdAt: session.createdAt,
      permissions: session.permissions,
      activeUsers: 0
    }
  }

  /**
   * Invite user to collaboration session
   */
  async inviteUser(request: InviteUserRequest): Promise<boolean> {
    const { sessionId, userId, permissionLevel, grantedBy } = InviteUserSchema.parse(request)

    // Check if granter has admin permission
    const granterPermission = await prisma.collaborationPermission.findFirst({
      where: {
        sessionId,
        userId: grantedBy,
        permissionLevel: 'admin'
      }
    })

    if (!granterPermission) {
      throw new Error('Insufficient permissions to invite users')
    }

    // Check if user already has permission
    const existingPermission = await prisma.collaborationPermission.findFirst({
      where: {
        sessionId,
        userId
      }
    })

    if (existingPermission) {
      // Update existing permission
      await prisma.collaborationPermission.update({
        where: { id: existingPermission.id },
        data: {
          permissionLevel,
          grantedBy
        }
      })
    } else {
      // Create new permission
      await prisma.collaborationPermission.create({
        data: {
          sessionId,
          userId,
          permissionLevel,
          grantedBy
        }
      })
    }

    return true
  }

  /**
   * Remove user from collaboration session
   */
  async removeUser(sessionId: string, userId: string, removedBy: string): Promise<boolean> {
    // Check if remover has admin permission
    const removerPermission = await prisma.collaborationPermission.findFirst({
      where: {
        sessionId,
        userId: removedBy,
        permissionLevel: 'admin'
      }
    })

    if (!removerPermission) {
      throw new Error('Insufficient permissions to remove users')
    }

    // Remove user permission
    await prisma.collaborationPermission.deleteMany({
      where: {
        sessionId,
        userId
      }
    })

    return true
  }

  /**
   * Check user permission for session
   */
  async getUserPermission(sessionId: string, userId: string): Promise<string | null> {
    const permission = await prisma.collaborationPermission.findFirst({
      where: {
        sessionId,
        userId
      }
    })

    return permission?.permissionLevel || null
  }

  /**
   * Record collaboration change
   */
  async recordChange(sessionId: string, userId: string, changeType: string, changeData: any): Promise<void> {
    await prisma.collaborationChange.create({
      data: {
        sessionId,
        userId,
        changeType,
        changeData: JSON.stringify(changeData)
      }
    })
  }

  /**
   * Get collaboration changes
   */
  async getChanges(sessionId: string, limit = 50): Promise<any[]> {
    const changes = await prisma.collaborationChange.findMany({
      where: { sessionId },
      orderBy: { timestamp: 'desc' },
      take: limit,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            image: true
          }
        }
      }
    })

    return changes.map(change => ({
      id: change.id,
      userId: change.userId,
      changeType: change.changeType,
      changeData: JSON.parse(change.changeData),
      timestamp: change.timestamp,
      user: change.user
    }))
  }

  /**
   * Create comment
   */
  async createComment(request: CreateCommentRequest): Promise<CommentInfo> {
    const { sessionId, userId, sectionPath, content, parentId } = CreateCommentSchema.parse(request)

    // Check if user has comment permission
    const permission = await this.getUserPermission(sessionId, userId)
    if (!permission || !['comment', 'edit', 'admin'].includes(permission)) {
      throw new Error('Insufficient permissions to comment')
    }

    const comment = await prisma.collaborationComment.create({
      data: {
        sessionId,
        userId,
        sectionPath,
        content,
        parentId
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            image: true
          }
        }
      }
    })

    return {
      id: comment.id,
      userId: comment.userId,
      sectionPath: comment.sectionPath,
      content: comment.content,
      parentId: comment.parentId,
      isResolved: comment.isResolved,
      createdAt: comment.createdAt,
      updatedAt: comment.updatedAt,
      user: comment.user,
      replies: []
    }
  }

  /**
   * Get comments for session
   */
  async getComments(sessionId: string, sectionPath?: string): Promise<CommentInfo[]> {
    const where: any = { sessionId }
    if (sectionPath) {
      where.sectionPath = sectionPath
    }

    const comments = await prisma.collaborationComment.findMany({
      where,
      orderBy: { createdAt: 'asc' },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            image: true
          }
        }
      }
    })

    // Build comment tree
    const commentMap = new Map<string, CommentInfo>()
    const rootComments: CommentInfo[] = []

    comments.forEach(comment => {
      const commentInfo: CommentInfo = {
        id: comment.id,
        userId: comment.userId,
        sectionPath: comment.sectionPath,
        content: comment.content,
        parentId: comment.parentId,
        isResolved: comment.isResolved,
        createdAt: comment.createdAt,
        updatedAt: comment.updatedAt,
        user: comment.user,
        replies: []
      }

      commentMap.set(comment.id, commentInfo)

      if (comment.parentId) {
        const parent = commentMap.get(comment.parentId)
        if (parent) {
          parent.replies.push(commentInfo)
        }
      } else {
        rootComments.push(commentInfo)
      }
    })

    return rootComments
  }

  /**
   * Resolve comment
   */
  async resolveComment(commentId: string, userId: string): Promise<boolean> {
    const comment = await prisma.collaborationComment.findUnique({
      where: { id: commentId },
      include: { session: true }
    })

    if (!comment) {
      throw new Error('Comment not found')
    }

    // Check permission
    const permission = await this.getUserPermission(comment.sessionId, userId)
    if (!permission || !['edit', 'admin'].includes(permission)) {
      throw new Error('Insufficient permissions to resolve comment')
    }

    await prisma.collaborationComment.update({
      where: { id: commentId },
      data: { isResolved: true }
    })

    return true
  }

  /**
   * Update user presence
   */
  async updatePresence(request: UpdatePresenceRequest): Promise<void> {
    const { sessionId, userId, status, sectionPath, position } = UpdatePresenceSchema.parse(request)

    await prisma.collaborationPresence.upsert({
      where: {
        sessionId_userId: {
          sessionId,
          userId
        }
      },
      update: {
        status,
        lastSeen: new Date()
      },
      create: {
        sessionId,
        userId,
        status,
        lastSeen: new Date()
      }
    })

    // Update cursor position if provided
    if (sectionPath !== undefined && position !== undefined) {
      await prisma.collaborationCursor.upsert({
        where: {
          sessionId_userId: {
            sessionId,
            userId
          }
        },
        update: {
          sectionPath,
          position,
          updatedAt: new Date()
        },
        create: {
          sessionId,
          userId,
          sectionPath,
          position
        }
      })
    }
  }

  /**
   * Get active users in session
   */
  async getActiveUsers(sessionId: string): Promise<any[]> {
    const activeThreshold = new Date(Date.now() - 5 * 60 * 1000) // 5 minutes

    const presence = await prisma.collaborationPresence.findMany({
      where: {
        sessionId,
        lastSeen: {
          gte: activeThreshold
        }
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            image: true
          }
        }
      }
    })

    return presence.map(p => ({
      userId: p.userId,
      status: p.status,
      lastSeen: p.lastSeen,
      user: p.user
    }))
  }

  /**
   * Delete collaboration session
   */
  async deleteSession(sessionId: string): Promise<boolean> {
    try {
      await prisma.collaborationSession.delete({
        where: { id: sessionId }
      })
      return true
    } catch (error) {
      console.error('Error deleting collaboration session:', error)
      return false
    }
  }

  /**
   * Cleanup expired sessions
   */
  async cleanupExpiredSessions(): Promise<number> {
    const result = await prisma.collaborationSession.deleteMany({
      where: {
        expiresAt: {
          lt: new Date()
        }
      }
    })

    return result.count
  }

  /**
   * Get user's collaboration sessions
   */
  async getUserSessions(userId: string): Promise<CollaborationSessionInfo[]> {
    const permissions = await prisma.collaborationPermission.findMany({
      where: { userId },
      include: {
        session: {
          include: {
            permissions: {
              include: {
                user: {
                  select: {
                    id: true,
                    name: true,
                    email: true,
                    image: true
                  }
                }
              }
            }
          }
        }
      }
    })

    return permissions
      .filter(p => p.session.expiresAt > new Date())
      .map(p => ({
        id: p.session.id,
        resumeId: p.session.resumeId,
        ownerId: p.session.ownerId,
        sessionToken: p.session.sessionToken,
        expiresAt: p.session.expiresAt,
        createdAt: p.session.createdAt,
        permissions: p.session.permissions,
        activeUsers: 0
      }))
  }
}

export const collaborationService = new CollaborationService()
