{"name": "@careercraft/web", "version": "2.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "test": "vitest", "test:watch": "vitest --watch", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:headed": "playwright test --headed", "test:all": "npm run test && npm run test:e2e", "test:ai": "node scripts/test-ai-features.js", "test:ai:unit": "vitest src/test/ai/ai-services.test.ts", "test:ai:integration": "vitest src/test/ai/ai-api.test.ts", "test:ai:components": "vitest src/test/ai/ai-components.test.tsx", "test:ai:e2e": "playwright test e2e/ai-features.spec.ts", "test:ai:performance": "node scripts/test-ai-features.js --performance-only", "test:linkedin": "vitest src/test/linkedin/", "test:linkedin:client": "vitest src/test/linkedin/linkedin-client.test.ts", "test:linkedin:service": "vitest src/test/linkedin/linkedin-service.test.ts", "test:linkedin:api": "vitest src/test/linkedin/linkedin-api.test.ts", "test:linkedin:component": "vitest src/test/linkedin/linkedin-component.test.tsx", "test:collaboration": "vitest src/test/collaboration/", "test:collaboration:websocket": "vitest src/test/collaboration/websocket-server.test.ts", "test:collaboration:ot": "vitest src/test/collaboration/operational-transform.test.ts", "test:collaboration:service": "vitest src/test/collaboration/collaboration-service.test.ts", "test:collaboration:api": "vitest src/test/collaboration/collaboration-api.test.ts", "test:collaboration:components": "vitest src/test/collaboration/collaboration-components.test.tsx", "test:version-control": "vitest src/test/version-control/", "test:version-control:diff": "vitest src/test/version-control/diff-engine.test.ts", "test:version-control:service": "vitest src/test/version-control/version-control-service.test.ts", "test:version-control:api": "vitest src/test/version-control/version-control-api.test.ts", "test:version-control:components": "vitest src/test/version-control/version-control-components.test.tsx", "test:job-matching": "vitest src/test/job-matching/", "test:job-matching:service": "vitest src/test/job-matching/job-matching-service.test.ts", "test:job-matching:ai": "vitest src/test/job-matching/ai-engine.test.ts", "test:job-matching:api": "vitest src/test/job-matching/job-matching-api.test.ts", "test:job-matching:components": "vitest src/test/job-matching/job-matching-components.test.tsx", "test:template-sync": "vitest src/test/template-sync/", "test:template-sync:service": "vitest src/test/template-sync/template-sync-service.test.ts", "test:template-sync:marketplace": "vitest src/test/template-sync/marketplace-service.test.ts", "test:template-sync:cloud": "vitest src/test/template-sync/cloud-storage.test.ts", "test:template-sync:api": "vitest src/test/template-sync/template-sync-api.test.ts", "test:template-sync:components": "vitest src/test/template-sync/template-sync-components.test.tsx", "test:payments": "vitest src/test/payments/", "test:payments:system": "vitest src/test/payments/payment-system.test.ts", "test:career-intelligence": "vitest src/test/career-intelligence/", "test:career-intelligence:system": "vitest src/test/career-intelligence/career-intelligence.test.ts", "seed:plans": "tsx src/lib/payments/seed-plans.ts", "clean": "rm -rf .next dist test-results coverage ai-test-report.json"}, "dependencies": {"@prisma/client": "^5.7.0", "@hookform/resolvers": "^3.3.2", "@next-auth/prisma-adapter": "^1.0.7", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-collapsible": "^1.0.3", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slider": "^1.1.2", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.0.7", "bcryptjs": "^2.4.3", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "framer-motion": "^10.16.5", "lucide-react": "^0.294.0", "nanoid": "^5.0.4", "next": "^14.0.3", "next-auth": "^4.24.5", "next-themes": "^0.2.1", "openai": "^4.20.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.2", "sonner": "^1.2.4", "stripe": "^14.21.0", "tailwind-merge": "^2.0.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.22.4"}, "devDependencies": {"prisma": "^5.7.0", "@playwright/test": "^1.40.0", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.1", "@types/bcryptjs": "^2.4.6", "@types/node": "^20.10.0", "@types/react": "^18.2.38", "@types/react-dom": "^18.2.17", "@vitejs/plugin-react": "^4.2.0", "@vitest/coverage-v8": "^1.0.0", "@vitest/ui": "^1.0.0", "autoprefixer": "^10.4.16", "date-fns": "^2.30.0", "eslint": "^8.54.0", "eslint-config-next": "^14.0.3", "jsdom": "^23.0.0", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "typescript": "^5.3.0", "vitest": "^1.0.0"}}