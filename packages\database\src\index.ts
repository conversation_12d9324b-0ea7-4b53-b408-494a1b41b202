import { PrismaClient, Prisma } from './generated';

declare global {
  // eslint-disable-next-line no-var
  var __prisma: PrismaClient | undefined;
}

// Enhanced Prisma client with connection pooling and error handling
export const prisma =
  globalThis.__prisma ??
  new PrismaClient({
    log: process.env.NODE_ENV === 'development'
      ? ['query', 'error', 'warn', 'info']
      : ['error'],
    errorFormat: 'pretty',
    datasources: {
      db: {
        url: process.env.DATABASE_URL,
      },
    },
  });

if (process.env.NODE_ENV !== 'production') {
  globalThis.__prisma = prisma;
}

// Re-export Prisma types
export * from './generated';
export type { Prisma } from './generated';

// Database connection management
export async function connectDB(): Promise<void> {
  try {
    await prisma.$connect();
    console.log('✅ Database connected successfully');

    // Test the connection with a simple query
    await prisma.$queryRaw`SELECT 1 as test`;
    console.log('✅ Database connection verified');
  } catch (error) {
    console.error('❌ Database connection failed:', error);
    throw new Error(`Database connection failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

export async function disconnectDB(): Promise<void> {
  try {
    await prisma.$disconnect();
    console.log('✅ Database disconnected successfully');
  } catch (error) {
    console.error('❌ Database disconnection failed:', error);
    throw new Error(`Database disconnection failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

// Comprehensive health check
export async function healthCheck(): Promise<{
  status: 'healthy' | 'unhealthy';
  timestamp: string;
  details?: {
    connection: boolean;
    queryTime: number;
    version?: string;
  };
  error?: string;
}> {
  const timestamp = new Date().toISOString();

  try {
    const startTime = Date.now();

    // Test basic connection
    await prisma.$queryRaw`SELECT 1 as test`;

    // Get database version
    const versionResult = await prisma.$queryRaw<Array<{ version: string }>>`SELECT version() as version`;
    const version = versionResult[0]?.version;

    const queryTime = Date.now() - startTime;

    return {
      status: 'healthy',
      timestamp,
      details: {
        connection: true,
        queryTime,
        version,
      },
    };
  } catch (error) {
    return {
      status: 'unhealthy',
      timestamp,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

// Database transaction helper
export async function withTransaction<T>(
  fn: (tx: Prisma.TransactionClient) => Promise<T>
): Promise<T> {
  return await prisma.$transaction(fn);
}

// Database cleanup for testing
export async function cleanupDatabase(): Promise<void> {
  if (process.env.NODE_ENV !== 'test') {
    throw new Error('Database cleanup is only allowed in test environment');
  }

  // Delete in reverse order of dependencies
  await prisma.aiGeneration.deleteMany();
  await prisma.resumeAnalytics.deleteMany();
  await prisma.coverLetter.deleteMany();
  await prisma.customSection.deleteMany();
  await prisma.language.deleteMany();
  await prisma.certification.deleteMany();
  await prisma.project.deleteMany();
  await prisma.skill.deleteMany();
  await prisma.education.deleteMany();
  await prisma.experience.deleteMany();
  await prisma.resume.deleteMany();
  await prisma.template.deleteMany();
  await prisma.userProfile.deleteMany();
  await prisma.session.deleteMany();
  await prisma.account.deleteMany();
  await prisma.user.deleteMany();
  await prisma.verificationToken.deleteMany();
}

// Seed data for development/testing
export async function seedDatabase(): Promise<void> {
  console.log('🌱 Seeding database...');

  // Create sample templates
  const templates = await Promise.all([
    prisma.template.upsert({
      where: { id: 'template-modern' },
      update: {},
      create: {
        id: 'template-modern',
        name: 'Modern Professional',
        description: 'Clean, modern design perfect for tech and creative roles',
        category: 'Professional',
        isPremium: false,
        isActive: true,
        config: {
          layout: 'single-column',
          colors: {
            primary: '#2563eb',
            secondary: '#64748b',
            text: '#1e293b',
            background: '#ffffff',
          },
          fonts: {
            heading: 'Inter',
            body: 'Inter',
          },
          spacing: {
            section: 24,
            item: 16,
          },
          sections: {
            order: ['summary', 'experience', 'education', 'skills', 'projects'],
            visibility: {
              summary: true,
              experience: true,
              education: true,
              skills: true,
              projects: true,
            },
          },
        },
      },
    }),
    prisma.template.upsert({
      where: { id: 'template-classic' },
      update: {},
      create: {
        id: 'template-classic',
        name: 'Classic Executive',
        description: 'Traditional format ideal for corporate and executive positions',
        category: 'Executive',
        isPremium: false,
        isActive: true,
        config: {
          layout: 'two-column',
          colors: {
            primary: '#1f2937',
            secondary: '#6b7280',
            text: '#111827',
            background: '#ffffff',
          },
          fonts: {
            heading: 'Georgia',
            body: 'Georgia',
          },
          spacing: {
            section: 20,
            item: 12,
          },
          sections: {
            order: ['summary', 'experience', 'education', 'skills'],
            visibility: {
              summary: true,
              experience: true,
              education: true,
              skills: true,
              projects: false,
            },
          },
        },
      },
    }),
  ]);

  console.log(`✅ Created ${templates.length} templates`);
  console.log('🌱 Database seeding completed');
}
