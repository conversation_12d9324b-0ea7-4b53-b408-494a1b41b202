/**
 * Template Validation Schemas
 * 
 * Zod schemas for validating template data throughout the application.
 */

import { z } from 'zod';
import { TemplateCategory } from '../types/template';

// Color validation
const colorSchema = z.string().regex(/^#[0-9A-F]{6}$/i, 'Invalid color format');

// Font size schema
const fontSizeSchema = z.object({
  heading1: z.number().min(16).max(48),
  heading2: z.number().min(14).max(36),
  heading3: z.number().min(12).max(24),
  body: z.number().min(8).max(18),
  small: z.number().min(6).max(14),
});

// Font weight schema
const fontWeightSchema = z.object({
  normal: z.number().min(100).max(900),
  medium: z.number().min(100).max(900),
  semibold: z.number().min(100).max(900),
  bold: z.number().min(100).max(900),
});

// Line height schema
const lineHeightSchema = z.object({
  tight: z.number().min(1).max(2),
  normal: z.number().min(1).max(2),
  relaxed: z.number().min(1).max(2),
});

// Colors schema
const colorsSchema = z.object({
  primary: colorSchema,
  secondary: colorSchema,
  accent: colorSchema,
  text: colorSchema,
  textLight: colorSchema,
  background: colorSchema,
  border: colorSchema,
  divider: colorSchema,
});

// Spacing schema
const spacingSchema = z.object({
  xs: z.number().min(0).max(50),
  sm: z.number().min(0).max(50),
  md: z.number().min(0).max(50),
  lg: z.number().min(0).max(50),
  xl: z.number().min(0).max(50),
  xxl: z.number().min(0).max(50),
});

// Layout schema
const layoutSchema = z.object({
  pageMargin: z.object({
    top: z.number().min(0).max(100),
    right: z.number().min(0).max(100),
    bottom: z.number().min(0).max(100),
    left: z.number().min(0).max(100),
  }),
  sectionSpacing: z.number().min(0).max(100),
  itemSpacing: z.number().min(0).max(50),
  columnGap: z.number().min(0).max(50),
});

// Components schema
const componentsSchema = z.object({
  header: z.object({
    alignment: z.enum(['left', 'center', 'right']),
    showDivider: z.boolean(),
    backgroundColor: colorSchema.optional(),
  }),
  section: z.object({
    titleStyle: z.enum(['underline', 'background', 'border', 'plain']),
    titleCase: z.enum(['uppercase', 'lowercase', 'capitalize', 'normal']),
    spacing: z.enum(['compact', 'normal', 'spacious']),
  }),
  list: z.object({
    bulletStyle: z.enum(['bullet', 'dash', 'arrow', 'none']),
    indentation: z.number().min(0).max(50),
  }),
  contact: z.object({
    layout: z.enum(['horizontal', 'vertical', 'grid']),
    showIcons: z.boolean(),
    separator: z.string().max(10),
  }),
});

// Template style schema
export const templateStyleSchema = z.object({
  fontFamily: z.string().min(1, 'Font family is required').max(100),
  fontSize: fontSizeSchema,
  fontWeight: fontWeightSchema,
  lineHeight: lineHeightSchema,
  colors: colorsSchema,
  spacing: spacingSchema,
  layout: layoutSchema,
  components: componentsSchema,
});

// Template column schema
const templateColumnSchema = z.object({
  id: z.string().min(1, 'Column ID is required'),
  width: z.number().min(1).max(100),
  sections: z.array(z.string()).max(20),
  order: z.number().min(0),
});

// Template header config schema
const templateHeaderConfigSchema = z.object({
  height: z.number().min(50).max(300),
  sections: z.array(z.string()).max(10),
  style: z.enum(['minimal', 'prominent', 'creative']),
});

// Template footer config schema
const templateFooterConfigSchema = z.object({
  height: z.number().min(20).max(100),
  content: z.string().max(200),
  style: z.enum(['minimal', 'detailed']),
});

// Template layout schema
export const templateLayoutSchema = z.object({
  type: z.enum(['single-column', 'two-column', 'three-column', 'sidebar']),
  columns: z.array(templateColumnSchema).min(1).max(3),
  header: templateHeaderConfigSchema,
  footer: templateFooterConfigSchema.optional(),
});

// Template schema
export const templateSchema = z.object({
  id: z.string(),
  name: z.string().min(1, 'Template name is required').max(100),
  description: z.string().min(1, 'Description is required').max(500),
  category: z.nativeEnum(TemplateCategory),
  tags: z.array(z.string().max(50)).max(20),
  previewImage: z.string().url('Invalid preview image URL'),
  thumbnailImage: z.string().url('Invalid thumbnail image URL'),
  isPremium: z.boolean(),
  isPopular: z.boolean(),
  isNew: z.boolean(),
  difficulty: z.enum(['beginner', 'intermediate', 'advanced']),
  style: templateStyleSchema,
  layout: templateLayoutSchema,
  supportedSections: z.array(z.string()).min(1),
  requiredSections: z.array(z.string()).min(1),
  customizable: z.object({
    colors: z.boolean(),
    fonts: z.boolean(),
    layout: z.boolean(),
    spacing: z.boolean(),
  }),
  usageCount: z.number().min(0),
  rating: z.number().min(0).max(5),
  reviewCount: z.number().min(0),
  createdAt: z.string(),
  updatedAt: z.string(),
  createdBy: z.string(),
  isOfficial: z.boolean(),
});

// Template customization schema
export const templateCustomizationSchema = z.object({
  templateId: z.string().min(1, 'Template ID is required'),
  userId: z.string().min(1, 'User ID is required'),
  customizations: z.object({
    style: templateStyleSchema.partial().optional(),
    layout: templateLayoutSchema.partial().optional(),
    sectionOrder: z.array(z.string()).optional(),
    hiddenSections: z.array(z.string()).optional(),
  }),
  name: z.string().max(100).optional(),
  isDefault: z.boolean(),
  createdAt: z.string(),
  updatedAt: z.string(),
});

// Font option schema
export const fontOptionSchema = z.object({
  id: z.string(),
  name: z.string().min(1, 'Font name is required').max(100),
  family: z.string().min(1, 'Font family is required').max(100),
  category: z.enum(['serif', 'sans-serif', 'monospace', 'display']),
  weights: z.array(z.number().min(100).max(900)).min(1),
  googleFont: z.boolean(),
  previewText: z.string().max(200),
});

// Color scheme schema
export const colorSchemeSchema = z.object({
  id: z.string(),
  name: z.string().min(1, 'Color scheme name is required').max(100),
  description: z.string().max(300),
  colors: colorsSchema,
  category: z.enum(['professional', 'creative', 'modern', 'classic']),
  previewImage: z.string().url().optional(),
});

// Export options schema
export const exportOptionsSchema = z.object({
  format: z.enum(['pdf', 'docx', 'html', 'png', 'jpg']),
  quality: z.enum(['draft', 'standard', 'high', 'print']),
  pageSize: z.enum(['a4', 'letter', 'legal']),
  orientation: z.enum(['portrait', 'landscape']),
  margins: z.object({
    top: z.number().min(0).max(100),
    right: z.number().min(0).max(100),
    bottom: z.number().min(0).max(100),
    left: z.number().min(0).max(100),
  }),
  includeMetadata: z.boolean(),
  watermark: z.object({
    text: z.string().max(100),
    opacity: z.number().min(0).max(1),
    position: z.enum(['center', 'corner']),
  }).optional(),
});

// Export job schema
export const exportJobSchema = z.object({
  id: z.string(),
  userId: z.string(),
  resumeId: z.string(),
  templateId: z.string(),
  options: exportOptionsSchema,
  status: z.enum(['pending', 'processing', 'completed', 'failed']),
  progress: z.number().min(0).max(100),
  downloadUrl: z.string().url().optional(),
  error: z.string().optional(),
  createdAt: z.string(),
  completedAt: z.string().optional(),
  expiresAt: z.string().optional(),
});

// Template review schema
export const templateReviewSchema = z.object({
  id: z.string(),
  templateId: z.string(),
  userId: z.string(),
  rating: z.number().min(1).max(5),
  comment: z.string().max(1000),
  helpful: z.number().min(0),
  createdAt: z.string(),
  updatedAt: z.string(),
});

// Template collection schema
export const templateCollectionSchema = z.object({
  id: z.string(),
  name: z.string().min(1, 'Collection name is required').max(100),
  description: z.string().max(500),
  templateIds: z.array(z.string()).max(50),
  category: z.string().max(50),
  isOfficial: z.boolean(),
  createdBy: z.string(),
  createdAt: z.string(),
  updatedAt: z.string(),
});

// Form schemas for creating/updating
export const createTemplateSchema = templateSchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
  usageCount: true,
  rating: true,
  reviewCount: true,
});

export const updateTemplateSchema = templateSchema.partial().omit({
  id: true,
  createdAt: true,
  createdBy: true,
});

export const createCustomizationSchema = templateCustomizationSchema.omit({
  userId: true,
  createdAt: true,
  updatedAt: true,
});

export const updateCustomizationSchema = templateCustomizationSchema.partial().omit({
  templateId: true,
  userId: true,
  createdAt: true,
});

// Export types inferred from schemas
export type TemplateStyleInput = z.infer<typeof templateStyleSchema>;
export type TemplateLayoutInput = z.infer<typeof templateLayoutSchema>;
export type TemplateInput = z.infer<typeof createTemplateSchema>;
export type TemplateUpdateInput = z.infer<typeof updateTemplateSchema>;
export type TemplateCustomizationInput = z.infer<typeof createCustomizationSchema>;
export type FontOptionInput = z.infer<typeof fontOptionSchema>;
export type ColorSchemeInput = z.infer<typeof colorSchemeSchema>;
export type ExportOptionsInput = z.infer<typeof exportOptionsSchema>;
export type TemplateReviewInput = z.infer<typeof templateReviewSchema>;

// Validation helper functions
export function validateTemplateStyle(style: any): { isValid: boolean; errors: string[] } {
  const result = templateStyleSchema.safeParse(style);
  return {
    isValid: result.success,
    errors: result.success ? [] : result.error.errors.map(e => e.message),
  };
}

export function validateTemplateLayout(layout: any): { isValid: boolean; errors: string[] } {
  const result = templateLayoutSchema.safeParse(layout);
  return {
    isValid: result.success,
    errors: result.success ? [] : result.error.errors.map(e => e.message),
  };
}

export function validateExportOptions(options: any): { isValid: boolean; errors: string[] } {
  const result = exportOptionsSchema.safeParse(options);
  return {
    isValid: result.success,
    errors: result.success ? [] : result.error.errors.map(e => e.message),
  };
}
