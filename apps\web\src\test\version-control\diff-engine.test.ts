/**
 * Diff Engine Unit Tests
 * 
 * Tests for version control diff calculation and application
 */

import { describe, it, expect, beforeEach } from 'vitest'
import DiffEngine, { DiffOperation, VersionDiff } from '@/lib/version-control/diff-engine'

describe('DiffEngine', () => {
  let diffEngine: DiffEngine

  beforeEach(() => {
    diffEngine = new DiffEngine()
  })

  describe('calculateDiff', () => {
    it('should detect no changes for identical objects', () => {
      const oldContent = { name: '<PERSON>', age: 30 }
      const newContent = { name: '<PERSON>', age: 30 }

      const diff = diffEngine.calculateDiff(oldContent, newContent, 1, 2)

      expect(diff.operations).toHaveLength(0)
      expect(diff.summary.additions).toBe(0)
      expect(diff.summary.deletions).toBe(0)
      expect(diff.summary.modifications).toBe(0)
      expect(diff.metadata.complexity).toBe('low')
    })

    it('should detect property additions', () => {
      const oldContent = { name: '<PERSON>' }
      const newContent = { name: '<PERSON>', age: 30 }

      const diff = diffEngine.calculateDiff(oldContent, newContent, 1, 2)

      expect(diff.operations).toHaveLength(1)
      expect(diff.operations[0]).toEqual({
        type: 'add',
        path: ['age'],
        newValue: 30
      })
      expect(diff.summary.additions).toBe(1)
    })

    it('should detect property deletions', () => {
      const oldContent = { name: 'John', age: 30 }
      const newContent = { name: 'John' }

      const diff = diffEngine.calculateDiff(oldContent, newContent, 1, 2)

      expect(diff.operations).toHaveLength(1)
      expect(diff.operations[0]).toEqual({
        type: 'remove',
        path: ['age'],
        oldValue: 30
      })
      expect(diff.summary.deletions).toBe(1)
    })

    it('should detect property modifications', () => {
      const oldContent = { name: 'John', age: 30 }
      const newContent = { name: 'Jane', age: 30 }

      const diff = diffEngine.calculateDiff(oldContent, newContent, 1, 2)

      expect(diff.operations).toHaveLength(1)
      expect(diff.operations[0]).toEqual({
        type: 'modify',
        path: ['name'],
        oldValue: 'John',
        newValue: 'Jane'
      })
      expect(diff.summary.modifications).toBe(1)
    })

    it('should handle nested object changes', () => {
      const oldContent = {
        personalInfo: { name: 'John', age: 30 },
        skills: ['JavaScript']
      }
      const newContent = {
        personalInfo: { name: 'Jane', age: 30, city: 'NYC' },
        skills: ['JavaScript', 'React']
      }

      const diff = diffEngine.calculateDiff(oldContent, newContent, 1, 2)

      expect(diff.operations).toHaveLength(3)
      
      // Check for name modification
      const nameChange = diff.operations.find(op => 
        op.path.join('.') === 'personalInfo.name' && op.type === 'modify'
      )
      expect(nameChange).toBeDefined()
      expect(nameChange?.oldValue).toBe('John')
      expect(nameChange?.newValue).toBe('Jane')

      // Check for city addition
      const cityChange = diff.operations.find(op => 
        op.path.join('.') === 'personalInfo.city' && op.type === 'add'
      )
      expect(cityChange).toBeDefined()
      expect(cityChange?.newValue).toBe('NYC')

      // Check for skills addition
      const skillsChange = diff.operations.find(op => 
        op.path.join('.') === 'skills.1' && op.type === 'add'
      )
      expect(skillsChange).toBeDefined()
      expect(skillsChange?.newValue).toBe('React')
    })

    it('should handle array changes', () => {
      const oldContent = { items: ['a', 'b', 'c'] }
      const newContent = { items: ['a', 'x', 'c', 'd'] }

      const diff = diffEngine.calculateDiff(oldContent, newContent, 1, 2)

      expect(diff.operations.length).toBeGreaterThan(0)
      
      // Should detect modification of second item
      const modifyOp = diff.operations.find(op => 
        op.path.join('.') === 'items.1' && op.type === 'modify'
      )
      expect(modifyOp).toBeDefined()
      expect(modifyOp?.oldValue).toBe('b')
      expect(modifyOp?.newValue).toBe('x')

      // Should detect addition of fourth item
      const addOp = diff.operations.find(op => 
        op.path.join('.') === 'items.3' && op.type === 'add'
      )
      expect(addOp).toBeDefined()
      expect(addOp?.newValue).toBe('d')
    })

    it('should determine complexity correctly', () => {
      // Low complexity (few changes)
      const lowDiff = diffEngine.calculateDiff({ a: 1 }, { a: 2 }, 1, 2)
      expect(lowDiff.metadata.complexity).toBe('low')

      // High complexity (many changes)
      const oldContent = {}
      const newContent = Object.fromEntries(
        Array.from({ length: 25 }, (_, i) => [`key${i}`, `value${i}`])
      )
      const highDiff = diffEngine.calculateDiff(oldContent, newContent, 1, 2)
      expect(highDiff.metadata.complexity).toBe('high')
    })
  })

  describe('applyDiff', () => {
    it('should apply add operations', () => {
      const content = { name: 'John' }
      const diff: VersionDiff = {
        operations: [{
          type: 'add',
          path: ['age'],
          newValue: 30
        }],
        summary: { additions: 1, deletions: 0, modifications: 0, moves: 0 },
        metadata: { fromVersion: 1, toVersion: 2, calculatedAt: Date.now(), complexity: 'low' }
      }

      const result = diffEngine.applyDiff(content, diff)

      expect(result).toEqual({ name: 'John', age: 30 })
    })

    it('should apply remove operations', () => {
      const content = { name: 'John', age: 30 }
      const diff: VersionDiff = {
        operations: [{
          type: 'remove',
          path: ['age'],
          oldValue: 30
        }],
        summary: { additions: 0, deletions: 1, modifications: 0, moves: 0 },
        metadata: { fromVersion: 1, toVersion: 2, calculatedAt: Date.now(), complexity: 'low' }
      }

      const result = diffEngine.applyDiff(content, diff)

      expect(result).toEqual({ name: 'John' })
    })

    it('should apply modify operations', () => {
      const content = { name: 'John', age: 30 }
      const diff: VersionDiff = {
        operations: [{
          type: 'modify',
          path: ['name'],
          oldValue: 'John',
          newValue: 'Jane'
        }],
        summary: { additions: 0, deletions: 0, modifications: 1, moves: 0 },
        metadata: { fromVersion: 1, toVersion: 2, calculatedAt: Date.now(), complexity: 'low' }
      }

      const result = diffEngine.applyDiff(content, diff)

      expect(result).toEqual({ name: 'Jane', age: 30 })
    })

    it('should apply multiple operations', () => {
      const content = { name: 'John', age: 30 }
      const diff: VersionDiff = {
        operations: [
          {
            type: 'modify',
            path: ['name'],
            oldValue: 'John',
            newValue: 'Jane'
          },
          {
            type: 'add',
            path: ['city'],
            newValue: 'NYC'
          },
          {
            type: 'remove',
            path: ['age'],
            oldValue: 30
          }
        ],
        summary: { additions: 1, deletions: 1, modifications: 1, moves: 0 },
        metadata: { fromVersion: 1, toVersion: 2, calculatedAt: Date.now(), complexity: 'medium' }
      }

      const result = diffEngine.applyDiff(content, diff)

      expect(result).toEqual({ name: 'Jane', city: 'NYC' })
    })

    it('should handle nested path operations', () => {
      const content = { 
        personalInfo: { name: 'John', age: 30 },
        skills: ['JavaScript']
      }
      const diff: VersionDiff = {
        operations: [
          {
            type: 'modify',
            path: ['personalInfo', 'name'],
            oldValue: 'John',
            newValue: 'Jane'
          },
          {
            type: 'add',
            path: ['skills', '1'],
            newValue: 'React',
            index: 1
          }
        ],
        summary: { additions: 1, deletions: 0, modifications: 1, moves: 0 },
        metadata: { fromVersion: 1, toVersion: 2, calculatedAt: Date.now(), complexity: 'low' }
      }

      const result = diffEngine.applyDiff(content, diff)

      expect(result.personalInfo.name).toBe('Jane')
      expect(result.skills).toEqual(['JavaScript', 'React'])
    })
  })

  describe('reverseDiff', () => {
    it('should reverse add operations to remove', () => {
      const diff: VersionDiff = {
        operations: [{
          type: 'add',
          path: ['age'],
          newValue: 30
        }],
        summary: { additions: 1, deletions: 0, modifications: 0, moves: 0 },
        metadata: { fromVersion: 1, toVersion: 2, calculatedAt: Date.now(), complexity: 'low' }
      }

      const reversed = diffEngine.reverseDiff(diff)

      expect(reversed.operations[0]).toEqual({
        type: 'remove',
        path: ['age'],
        oldValue: 30,
        newValue: undefined
      })
      expect(reversed.summary.deletions).toBe(1)
      expect(reversed.summary.additions).toBe(0)
    })

    it('should reverse remove operations to add', () => {
      const diff: VersionDiff = {
        operations: [{
          type: 'remove',
          path: ['age'],
          oldValue: 30
        }],
        summary: { additions: 0, deletions: 1, modifications: 0, moves: 0 },
        metadata: { fromVersion: 1, toVersion: 2, calculatedAt: Date.now(), complexity: 'low' }
      }

      const reversed = diffEngine.reverseDiff(diff)

      expect(reversed.operations[0]).toEqual({
        type: 'add',
        path: ['age'],
        oldValue: undefined,
        newValue: 30
      })
      expect(reversed.summary.additions).toBe(1)
      expect(reversed.summary.deletions).toBe(0)
    })

    it('should reverse modify operations', () => {
      const diff: VersionDiff = {
        operations: [{
          type: 'modify',
          path: ['name'],
          oldValue: 'John',
          newValue: 'Jane'
        }],
        summary: { additions: 0, deletions: 0, modifications: 1, moves: 0 },
        metadata: { fromVersion: 1, toVersion: 2, calculatedAt: Date.now(), complexity: 'low' }
      }

      const reversed = diffEngine.reverseDiff(diff)

      expect(reversed.operations[0]).toEqual({
        type: 'modify',
        path: ['name'],
        oldValue: 'Jane',
        newValue: 'John'
      })
      expect(reversed.metadata.fromVersion).toBe(2)
      expect(reversed.metadata.toVersion).toBe(1)
    })
  })

  describe('mergeDiffs', () => {
    it('should merge multiple diffs', () => {
      const diff1: VersionDiff = {
        operations: [{
          type: 'add',
          path: ['name'],
          newValue: 'John'
        }],
        summary: { additions: 1, deletions: 0, modifications: 0, moves: 0 },
        metadata: { fromVersion: 1, toVersion: 2, calculatedAt: Date.now(), complexity: 'low' }
      }

      const diff2: VersionDiff = {
        operations: [{
          type: 'add',
          path: ['age'],
          newValue: 30
        }],
        summary: { additions: 1, deletions: 0, modifications: 0, moves: 0 },
        metadata: { fromVersion: 2, toVersion: 3, calculatedAt: Date.now(), complexity: 'low' }
      }

      const merged = diffEngine.mergeDiffs([diff1, diff2])

      expect(merged.operations).toHaveLength(2)
      expect(merged.summary.additions).toBe(2)
      expect(merged.metadata.fromVersion).toBe(1)
      expect(merged.metadata.toVersion).toBe(3)
    })

    it('should handle empty diff array', () => {
      expect(() => diffEngine.mergeDiffs([])).toThrow('Cannot merge empty diff array')
    })

    it('should return single diff unchanged', () => {
      const diff: VersionDiff = {
        operations: [{
          type: 'add',
          path: ['name'],
          newValue: 'John'
        }],
        summary: { additions: 1, deletions: 0, modifications: 0, moves: 0 },
        metadata: { fromVersion: 1, toVersion: 2, calculatedAt: Date.now(), complexity: 'low' }
      }

      const merged = diffEngine.mergeDiffs([diff])

      expect(merged).toEqual(diff)
    })
  })

  describe('generateTextDiff', () => {
    it('should generate text diff for string changes', () => {
      const oldText = 'Hello\nWorld\nTest'
      const newText = 'Hello\nUniverse\nTest\nNew Line'

      const textDiff = diffEngine.generateTextDiff(oldText, newText)

      expect(textDiff).toContain('  Hello')
      expect(textDiff).toContain('- World')
      expect(textDiff).toContain('+ Universe')
      expect(textDiff).toContain('  Test')
      expect(textDiff).toContain('+ New Line')
    })

    it('should handle identical text', () => {
      const text = 'Hello\nWorld'
      const textDiff = diffEngine.generateTextDiff(text, text)

      expect(textDiff).toEqual(['  Hello', '  World'])
    })

    it('should handle empty strings', () => {
      const textDiff = diffEngine.generateTextDiff('', 'Hello')

      expect(textDiff).toEqual(['+ Hello'])
    })
  })

  describe('options handling', () => {
    it('should ignore whitespace when configured', () => {
      const engine = new DiffEngine({ ignoreWhitespace: true })
      const oldContent = { text: 'Hello World' }
      const newContent = { text: ' Hello World ' }

      const diff = engine.calculateDiff(oldContent, newContent, 1, 2)

      expect(diff.operations).toHaveLength(0)
    })

    it('should ignore case when configured', () => {
      const engine = new DiffEngine({ ignoreCase: true })
      const oldContent = { text: 'Hello' }
      const newContent = { text: 'HELLO' }

      const diff = engine.calculateDiff(oldContent, newContent, 1, 2)

      expect(diff.operations).toHaveLength(0)
    })

    it('should respect max depth option', () => {
      const engine = new DiffEngine({ maxDepth: 1 })
      const oldContent = { 
        level1: { 
          level2: { 
            level3: 'old' 
          } 
        } 
      }
      const newContent = { 
        level1: { 
          level2: { 
            level3: 'new' 
          } 
        } 
      }

      const diff = engine.calculateDiff(oldContent, newContent, 1, 2)

      // Should not detect deep changes due to maxDepth limit
      expect(diff.operations.length).toBeLessThanOrEqual(1)
    })
  })

  describe('edge cases', () => {
    it('should handle null and undefined values', () => {
      const oldContent = { value: null }
      const newContent = { value: undefined }

      const diff = diffEngine.calculateDiff(oldContent, newContent, 1, 2)

      expect(diff.operations).toHaveLength(1)
      expect(diff.operations[0].type).toBe('remove')
    })

    it('should handle type changes', () => {
      const oldContent = { value: 'string' }
      const newContent = { value: 123 }

      const diff = diffEngine.calculateDiff(oldContent, newContent, 1, 2)

      expect(diff.operations).toHaveLength(1)
      expect(diff.operations[0].type).toBe('modify')
      expect(diff.operations[0].oldValue).toBe('string')
      expect(diff.operations[0].newValue).toBe(123)
    })

    it('should handle circular references gracefully', () => {
      const oldContent: any = { name: 'test' }
      oldContent.self = oldContent

      const newContent = { name: 'test', age: 30 }

      // Should not throw error
      expect(() => {
        diffEngine.calculateDiff(oldContent, newContent, 1, 2)
      }).not.toThrow()
    })
  })
})
