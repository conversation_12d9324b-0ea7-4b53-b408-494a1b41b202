'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  FileText, 
  Plus, 
  Download, 
  Eye, 
  TrendingUp, 
  Clock,
  Star,
  Zap,
  Target,
  Calendar
} from 'lucide-react'

interface DashboardStats {
  totalResumes: number
  totalDownloads: number
  totalViews: number
  activeResumes: number
}

interface RecentActivity {
  id: string
  type: 'created' | 'updated' | 'downloaded' | 'viewed'
  resumeTitle: string
  timestamp: Date
  description: string
}

export function DashboardOverview() {
  const { data: session } = useSession()
  const router = useRouter()
  const [stats, setStats] = useState<DashboardStats>({
    totalResumes: 0,
    totalDownloads: 0,
    totalViews: 0,
    activeResumes: 0
  })
  const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // Simulate API call - replace with actual API call
    const fetchDashboardData = async () => {
      try {
        // Mock data for now
        setStats({
          totalResumes: 3,
          totalDownloads: 12,
          totalViews: 45,
          activeResumes: 2
        })

        setRecentActivity([
          {
            id: '1',
            type: 'created',
            resumeTitle: 'Software Engineer Resume',
            timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
            description: 'Created new resume'
          },
          {
            id: '2',
            type: 'downloaded',
            resumeTitle: 'Product Manager Resume',
            timestamp: new Date(Date.now() - 5 * 60 * 60 * 1000), // 5 hours ago
            description: 'Downloaded as PDF'
          },
          {
            id: '3',
            type: 'updated',
            resumeTitle: 'Data Scientist Resume',
            timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000), // 1 day ago
            description: 'Updated work experience'
          }
        ])
      } catch (error) {
        console.error('Failed to fetch dashboard data:', error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchDashboardData()
  }, [])

  const getActivityIcon = (type: RecentActivity['type']) => {
    switch (type) {
      case 'created':
        return <Plus className="w-4 h-4 text-green-600" />
      case 'updated':
        return <FileText className="w-4 h-4 text-blue-600" />
      case 'downloaded':
        return <Download className="w-4 h-4 text-purple-600" />
      case 'viewed':
        return <Eye className="w-4 h-4 text-orange-600" />
      default:
        return <Clock className="w-4 h-4 text-gray-600" />
    }
  }

  const getActivityColor = (type: RecentActivity['type']) => {
    switch (type) {
      case 'created':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
      case 'updated':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400'
      case 'downloaded':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400'
      case 'viewed':
        return 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400'
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'
    }
  }

  const formatTimeAgo = (date: Date) => {
    const now = new Date()
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))
    
    if (diffInHours < 1) return 'Just now'
    if (diffInHours < 24) return `${diffInHours}h ago`
    const diffInDays = Math.floor(diffInHours / 24)
    if (diffInDays < 7) return `${diffInDays}d ago`
    return date.toLocaleDateString()
  }

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <Card key={i} className="glass-card animate-pulse">
              <CardContent className="p-6">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-8 bg-gray-200 rounded w-1/2"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Welcome Section */}
      <div className="glass-panel p-6 rounded-2xl">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              Welcome back, {session?.user?.name?.split(' ')[0] || 'User'}! 👋
            </h1>
            <p className="text-muted-foreground mt-2">
              Ready to craft your perfect resume? Let's make it happen.
            </p>
          </div>
          <Button 
            size="lg"
            className="glass-card bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white border-0"
            onClick={() => router.push('/dashboard/resumes/new')}
          >
            <Plus className="w-5 h-5 mr-2" />
            Create Resume
          </Button>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="glass-card hover:scale-105 transition-transform duration-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Resumes</p>
                <p className="text-3xl font-bold">{stats.totalResumes}</p>
              </div>
              <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
                <FileText className="w-6 h-6 text-white" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="glass-card hover:scale-105 transition-transform duration-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Downloads</p>
                <p className="text-3xl font-bold">{stats.totalDownloads}</p>
              </div>
              <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center">
                <Download className="w-6 h-6 text-white" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="glass-card hover:scale-105 transition-transform duration-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Profile Views</p>
                <p className="text-3xl font-bold">{stats.totalViews}</p>
              </div>
              <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg flex items-center justify-center">
                <Eye className="w-6 h-6 text-white" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="glass-card hover:scale-105 transition-transform duration-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Active Resumes</p>
                <p className="text-3xl font-bold">{stats.activeResumes}</p>
              </div>
              <div className="w-12 h-12 bg-gradient-to-br from-orange-500 to-orange-600 rounded-lg flex items-center justify-center">
                <TrendingUp className="w-6 h-6 text-white" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions & Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Quick Actions */}
        <Card className="glass-card">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Zap className="w-5 h-5 text-yellow-500" />
              <span>Quick Actions</span>
            </CardTitle>
            <CardDescription>
              Get started with these common tasks
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            <Button 
              variant="outline" 
              className="w-full justify-start glass-input"
              onClick={() => router.push('/dashboard/resumes/new')}
            >
              <Plus className="w-4 h-4 mr-2" />
              Create New Resume
            </Button>
            <Button 
              variant="outline" 
              className="w-full justify-start glass-input"
              onClick={() => router.push('/dashboard/templates')}
            >
              <Star className="w-4 h-4 mr-2" />
              Browse Templates
            </Button>
            <Button 
              variant="outline" 
              className="w-full justify-start glass-input"
              onClick={() => router.push('/dashboard/resumes')}
            >
              <FileText className="w-4 h-4 mr-2" />
              View All Resumes
            </Button>
            <Button 
              variant="outline" 
              className="w-full justify-start glass-input"
              onClick={() => router.push('/dashboard/analytics')}
            >
              <Target className="w-4 h-4 mr-2" />
              View Analytics
            </Button>
          </CardContent>
        </Card>

        {/* Recent Activity */}
        <Card className="glass-card">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Clock className="w-5 h-5 text-blue-500" />
              <span>Recent Activity</span>
            </CardTitle>
            <CardDescription>
              Your latest resume activities
            </CardDescription>
          </CardHeader>
          <CardContent>
            {recentActivity.length > 0 ? (
              <div className="space-y-4">
                {recentActivity.map((activity) => (
                  <div key={activity.id} className="flex items-start space-x-3 p-3 rounded-lg glass-input">
                    <div className="flex-shrink-0 mt-1">
                      {getActivityIcon(activity.type)}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2">
                        <p className="text-sm font-medium truncate">
                          {activity.resumeTitle}
                        </p>
                        <Badge variant="secondary" className={getActivityColor(activity.type)}>
                          {activity.type}
                        </Badge>
                      </div>
                      <p className="text-xs text-muted-foreground">
                        {activity.description}
                      </p>
                      <p className="text-xs text-muted-foreground mt-1">
                        {formatTimeAgo(activity.timestamp)}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-6">
                <Calendar className="w-12 h-12 text-muted-foreground mx-auto mb-2" />
                <p className="text-sm text-muted-foreground">No recent activity</p>
                <p className="text-xs text-muted-foreground">
                  Create your first resume to get started!
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
