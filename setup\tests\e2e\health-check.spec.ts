import { test, expect } from '@playwright/test'

test.describe('Health Check', () => {
  test('should return healthy status', async ({ request }) => {
    const response = await request.get('/api/health')
    
    expect(response.status()).toBe(200)
    
    const data = await response.json()
    expect(data.status).toBe('healthy')
    expect(data.services.database).toBe('connected')
    expect(data.timestamp).toBeDefined()
    expect(data.version).toBe('1.0.0')
  })

  test('should have all required services configured', async ({ request }) => {
    const response = await request.get('/api/health')
    const data = await response.json()
    
    // Check that services are at least configured or show proper status
    expect(['configured', 'not_configured']).toContain(data.services.openai)
    expect(['configured', 'not_configured']).toContain(data.services.stripe)
    expect(['connected', 'not_configured', 'error']).toContain(data.services.redis)
  })
})
