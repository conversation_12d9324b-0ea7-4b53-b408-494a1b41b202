'use client';

import { useState, useCallback, useRef } from 'react';
import { useSession } from 'next-auth/react';
import { 
  Resume, 
  ResumeSectionType, 
  WorkExperience, 
  Education,
  ResumeSection,
} from '@careercraft/shared/types/resume';
import { 
  PersonalInfoInput, 
  WorkExperienceInput, 
  EducationInput 
} from '@careercraft/shared/schemas/resume';
import { nanoid } from 'nanoid';

interface UseResumeBuilderReturn {
  resume: Resume | null;
  isLoading: boolean;
  error: string | null;
  updatePersonalInfo: (data: PersonalInfoInput) => Promise<void>;
  addWorkExperience: (data: WorkExperienceInput) => Promise<void>;
  updateWorkExperience: (id: string, data: WorkExperienceInput) => Promise<void>;
  removeWorkExperience: (id: string) => Promise<void>;
  addEducation: (data: EducationInput) => Promise<void>;
  updateEducation: (id: string, data: EducationInput) => Promise<void>;
  removeEducation: (id: string) => Promise<void>;
  updateSection: (sectionId: string, data: any) => Promise<void>;
  reorderSections: (sections: ResumeSection[]) => Promise<void>;
  saveResume: () => Promise<Resume | null>;
  loadResume: (resumeId: string) => Promise<void>;
}

export function useResumeBuilder(
  resumeId?: string,
  templateId?: string
): UseResumeBuilderReturn {
  const { data: session } = useSession();
  const [resume, setResume] = useState<Resume | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const saveTimeoutRef = useRef<NodeJS.Timeout>();

  // Initialize a new resume
  const initializeResume = useCallback((templateId?: string) => {
    if (!session?.user?.id) return null;

    const newResume: Resume = {
      id: nanoid(),
      userId: session.user.id,
      title: 'Untitled Resume',
      templateId: templateId || 'default',
      personalInfo: {
        firstName: '',
        lastName: '',
        email: session.user.email || '',
        phone: '',
        location: '',
        website: '',
        linkedin: '',
        github: '',
        portfolio: '',
        summary: '',
      },
      sections: [
        {
          id: nanoid(),
          type: ResumeSectionType.PERSONAL_INFO,
          title: 'Personal Information',
          isVisible: true,
          order: 0,
          data: {},
        },
        {
          id: nanoid(),
          type: ResumeSectionType.WORK_EXPERIENCE,
          title: 'Work Experience',
          isVisible: true,
          order: 1,
          data: [],
        },
        {
          id: nanoid(),
          type: ResumeSectionType.EDUCATION,
          title: 'Education',
          isVisible: true,
          order: 2,
          data: [],
        },
        {
          id: nanoid(),
          type: ResumeSectionType.SKILLS,
          title: 'Skills',
          isVisible: true,
          order: 3,
          data: [],
        },
      ],
      settings: {
        isPublic: false,
        allowComments: false,
        seoOptimized: true,
        atsOptimized: true,
        targetJobTitle: '',
        targetCompany: '',
        keywords: [],
      },
      metadata: {
        version: 1,
        lastEditedBy: session.user.id,
        wordCount: 0,
        pageCount: 1,
        completionPercentage: 0,
      },
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    return newResume;
  }, [session]);

  // Auto-save functionality
  const autoSave = useCallback(async (updatedResume: Resume) => {
    if (saveTimeoutRef.current) {
      clearTimeout(saveTimeoutRef.current);
    }

    saveTimeoutRef.current = setTimeout(async () => {
      try {
        await fetch('/api/resumes', {
          method: resumeId ? 'PUT' : 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(updatedResume),
        });
      } catch (error) {
        console.error('Auto-save failed:', error);
      }
    }, 2000); // Auto-save after 2 seconds of inactivity
  }, [resumeId]);

  // Update personal information
  const updatePersonalInfo = useCallback(async (data: PersonalInfoInput) => {
    if (!resume) {
      const newResume = initializeResume(templateId);
      if (!newResume) throw new Error('Failed to initialize resume');
      
      newResume.personalInfo = { ...newResume.personalInfo, ...data };
      setResume(newResume);
      await autoSave(newResume);
      return;
    }

    const updatedResume = {
      ...resume,
      personalInfo: { ...resume.personalInfo, ...data },
      updatedAt: new Date().toISOString(),
    };

    setResume(updatedResume);
    await autoSave(updatedResume);
  }, [resume, initializeResume, templateId, autoSave]);

  // Add work experience
  const addWorkExperience = useCallback(async (data: WorkExperienceInput) => {
    if (!resume) throw new Error('Resume not initialized');

    const workSection = resume.sections.find(s => s.type === ResumeSectionType.WORK_EXPERIENCE);
    if (!workSection) throw new Error('Work experience section not found');

    const currentExperiences = (workSection.data as WorkExperience[]) || [];
    const newExperience: WorkExperience = {
      ...data,
      id: data.id || nanoid(),
    };

    const updatedSections = resume.sections.map(section =>
      section.type === ResumeSectionType.WORK_EXPERIENCE
        ? { ...section, data: [...currentExperiences, newExperience] }
        : section
    );

    const updatedResume = {
      ...resume,
      sections: updatedSections,
      updatedAt: new Date().toISOString(),
    };

    setResume(updatedResume);
    await autoSave(updatedResume);
  }, [resume, autoSave]);

  // Update work experience
  const updateWorkExperience = useCallback(async (id: string, data: WorkExperienceInput) => {
    if (!resume) throw new Error('Resume not initialized');

    const workSection = resume.sections.find(s => s.type === ResumeSectionType.WORK_EXPERIENCE);
    if (!workSection) throw new Error('Work experience section not found');

    const currentExperiences = (workSection.data as WorkExperience[]) || [];
    const updatedExperiences = currentExperiences.map(exp =>
      exp.id === id ? { ...exp, ...data } : exp
    );

    const updatedSections = resume.sections.map(section =>
      section.type === ResumeSectionType.WORK_EXPERIENCE
        ? { ...section, data: updatedExperiences }
        : section
    );

    const updatedResume = {
      ...resume,
      sections: updatedSections,
      updatedAt: new Date().toISOString(),
    };

    setResume(updatedResume);
    await autoSave(updatedResume);
  }, [resume, autoSave]);

  // Remove work experience
  const removeWorkExperience = useCallback(async (id: string) => {
    if (!resume) throw new Error('Resume not initialized');

    const workSection = resume.sections.find(s => s.type === ResumeSectionType.WORK_EXPERIENCE);
    if (!workSection) throw new Error('Work experience section not found');

    const currentExperiences = (workSection.data as WorkExperience[]) || [];
    const filteredExperiences = currentExperiences.filter(exp => exp.id !== id);

    const updatedSections = resume.sections.map(section =>
      section.type === ResumeSectionType.WORK_EXPERIENCE
        ? { ...section, data: filteredExperiences }
        : section
    );

    const updatedResume = {
      ...resume,
      sections: updatedSections,
      updatedAt: new Date().toISOString(),
    };

    setResume(updatedResume);
    await autoSave(updatedResume);
  }, [resume, autoSave]);

  // Add education
  const addEducation = useCallback(async (data: EducationInput) => {
    if (!resume) throw new Error('Resume not initialized');

    const educationSection = resume.sections.find(s => s.type === ResumeSectionType.EDUCATION);
    if (!educationSection) throw new Error('Education section not found');

    const currentEducation = (educationSection.data as Education[]) || [];
    const newEducation: Education = {
      ...data,
      id: data.id || nanoid(),
    };

    const updatedSections = resume.sections.map(section =>
      section.type === ResumeSectionType.EDUCATION
        ? { ...section, data: [...currentEducation, newEducation] }
        : section
    );

    const updatedResume = {
      ...resume,
      sections: updatedSections,
      updatedAt: new Date().toISOString(),
    };

    setResume(updatedResume);
    await autoSave(updatedResume);
  }, [resume, autoSave]);

  // Update education
  const updateEducation = useCallback(async (id: string, data: EducationInput) => {
    if (!resume) throw new Error('Resume not initialized');

    const educationSection = resume.sections.find(s => s.type === ResumeSectionType.EDUCATION);
    if (!educationSection) throw new Error('Education section not found');

    const currentEducation = (educationSection.data as Education[]) || [];
    const updatedEducation = currentEducation.map(edu =>
      edu.id === id ? { ...edu, ...data } : edu
    );

    const updatedSections = resume.sections.map(section =>
      section.type === ResumeSectionType.EDUCATION
        ? { ...section, data: updatedEducation }
        : section
    );

    const updatedResume = {
      ...resume,
      sections: updatedSections,
      updatedAt: new Date().toISOString(),
    };

    setResume(updatedResume);
    await autoSave(updatedResume);
  }, [resume, autoSave]);

  // Remove education
  const removeEducation = useCallback(async (id: string) => {
    if (!resume) throw new Error('Resume not initialized');

    const educationSection = resume.sections.find(s => s.type === ResumeSectionType.EDUCATION);
    if (!educationSection) throw new Error('Education section not found');

    const currentEducation = (educationSection.data as Education[]) || [];
    const filteredEducation = currentEducation.filter(edu => edu.id !== id);

    const updatedSections = resume.sections.map(section =>
      section.type === ResumeSectionType.EDUCATION
        ? { ...section, data: filteredEducation }
        : section
    );

    const updatedResume = {
      ...resume,
      sections: updatedSections,
      updatedAt: new Date().toISOString(),
    };

    setResume(updatedResume);
    await autoSave(updatedResume);
  }, [resume, autoSave]);

  // Update section
  const updateSection = useCallback(async (sectionId: string, data: any) => {
    if (!resume) throw new Error('Resume not initialized');

    const updatedSections = resume.sections.map(section =>
      section.id === sectionId ? { ...section, data } : section
    );

    const updatedResume = {
      ...resume,
      sections: updatedSections,
      updatedAt: new Date().toISOString(),
    };

    setResume(updatedResume);
    await autoSave(updatedResume);
  }, [resume, autoSave]);

  // Reorder sections
  const reorderSections = useCallback(async (sections: ResumeSection[]) => {
    if (!resume) throw new Error('Resume not initialized');

    const updatedResume = {
      ...resume,
      sections,
      updatedAt: new Date().toISOString(),
    };

    setResume(updatedResume);
    await autoSave(updatedResume);
  }, [resume, autoSave]);

  // Save resume
  const saveResume = useCallback(async (): Promise<Resume | null> => {
    if (!resume) throw new Error('No resume to save');

    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch('/api/resumes', {
        method: resumeId ? 'PUT' : 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(resume),
      });

      if (!response.ok) {
        throw new Error('Failed to save resume');
      }

      const savedResume = await response.json();
      setResume(savedResume);
      return savedResume;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to save resume';
      setError(errorMessage);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [resume, resumeId]);

  // Load resume
  const loadResume = useCallback(async (resumeId: string) => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch(`/api/resumes/${resumeId}`);
      
      if (!response.ok) {
        throw new Error('Failed to load resume');
      }

      const loadedResume = await response.json();
      setResume(loadedResume);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to load resume';
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Initialize resume if not provided
  if (!resume && !resumeId && session?.user?.id) {
    const newResume = initializeResume(templateId);
    if (newResume) {
      setResume(newResume);
    }
  }

  return {
    resume,
    isLoading,
    error,
    updatePersonalInfo,
    addWorkExperience,
    updateWorkExperience,
    removeWorkExperience,
    addEducation,
    updateEducation,
    removeEducation,
    updateSection,
    reorderSections,
    saveResume,
    loadResume,
  };
}
