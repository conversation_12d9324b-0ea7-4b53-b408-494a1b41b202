# User Flow Diagrams

## Overview

This document contains comprehensive user flow diagrams for CareerCraft, covering all major user journeys from onboarding to advanced features.

## 1. User Onboarding Flow

```mermaid
flowchart TD
    A[Landing Page] --> B{User Action}
    B -->|Sign Up| C[Registration Form]
    B -->|Sign In| D[Login Form]
    B -->|Continue as Guest| E[Limited Features]
    
    C --> F{Registration Method}
    F -->|Email| G[Email/Password Form]
    F -->|Google OAuth| H[Google Auth]
    F -->|GitHub OAuth| I[GitHub Auth]
    
    G --> J[Email Verification]
    H --> K[Profile Setup]
    I --> K
    J --> L[Verification Email Sent]
    L --> M[Click Email Link]
    M --> K
    
    D --> N{Login Method}
    N -->|Email| O[Email/Password]
    N -->|Google| H
    N -->|GitHub| I
    
    O --> P{Credentials Valid?}
    P -->|Yes| Q[Dashboard]
    P -->|No| R[Error Message]
    R --> D
    
    K --> S[Welcome Tutorial]
    S --> T[Create First Resume]
    T --> Q
    
    E --> U[Guest Dashboard]
    U --> V{Want Full Features?}
    V -->|Yes| C
    V -->|No| W[Limited Resume Builder]
```

## 2. Resume Creation Flow

```mermaid
flowchart TD
    A[Dashboard] --> B[Create New Resume]
    B --> C{Start Method}
    
    C -->|From Scratch| D[Choose Template]
    C -->|Import LinkedIn| E[LinkedIn Integration]
    C -->|Upload Existing| F[File Upload]
    C -->|Duplicate Existing| G[Select Resume to Copy]
    
    D --> H[Template Selection]
    E --> I[LinkedIn Data Import]
    F --> J[Parse Uploaded Resume]
    G --> K[Copy Resume Data]
    
    H --> L[Resume Editor]
    I --> M{Import Successful?}
    M -->|Yes| L
    M -->|No| N[Manual Entry]
    N --> L
    
    J --> O{Parse Successful?}
    O -->|Yes| L
    O -->|No| N
    
    K --> L
    
    L --> P[Personal Information]
    P --> Q[Professional Summary]
    Q --> R[Work Experience]
    R --> S[Education]
    S --> T[Skills]
    T --> U[Additional Sections]
    
    U --> V{AI Assistance Needed?}
    V -->|Yes| W[AI Content Generation]
    V -->|No| X[Manual Content Entry]
    
    W --> Y[Review AI Suggestions]
    Y --> Z{Accept Suggestions?}
    Z -->|Yes| AA[Apply to Resume]
    Z -->|No| AB[Edit Manually]
    Z -->|Regenerate| W
    
    X --> AA
    AB --> AA
    AA --> AC[Preview Resume]
    AC --> AD{Satisfied?}
    AD -->|Yes| AE[Save Resume]
    AD -->|No| AF[Continue Editing]
    AF --> L
    
    AE --> AG[Resume Saved]
    AG --> AH{Next Action}
    AH -->|Export PDF| AI[Generate PDF]
    AH -->|Share Link| AJ[Generate Share Link]
    AH -->|Create Cover Letter| AK[Cover Letter Builder]
    AH -->|Back to Dashboard| AL[Dashboard]
```

## 3. AI Content Generation Flow

```mermaid
flowchart TD
    A[Resume Editor] --> B{AI Feature Selected}
    
    B -->|Generate Summary| C[Professional Summary AI]
    B -->|Improve Bullet Points| D[Experience Enhancement AI]
    B -->|Keyword Optimization| E[ATS Keyword AI]
    B -->|Rewrite Content| F[Content Rewriter AI]
    
    C --> G[Analyze User Profile]
    G --> H[Generate Summary Options]
    H --> I[Present 3-5 Variations]
    
    D --> J[Analyze Job Description]
    J --> K[Extract Key Requirements]
    K --> L[Generate STAR Format Bullets]
    L --> M[Present Suggestions]
    
    E --> N[Job Description Input]
    N --> O[Extract Keywords]
    O --> P[Analyze Current Resume]
    P --> Q[Suggest Keyword Integration]
    
    F --> R[Select Content to Rewrite]
    R --> S[Choose Rewrite Style]
    S --> T[Generate Alternatives]
    T --> U[Present Options]
    
    I --> V{User Action}
    M --> V
    Q --> V
    U --> V
    
    V -->|Accept| W[Apply to Resume]
    V -->|Edit| X[Manual Editing]
    V -->|Regenerate| Y[New AI Request]
    V -->|Reject| Z[Keep Original]
    
    W --> AA[Content Applied]
    X --> AB[Save Edited Version]
    Y --> AC{Which Feature?}
    AC --> C
    AC --> D
    AC --> E
    AC --> F
    Z --> AD[No Changes]
    
    AA --> AE[Continue Editing]
    AB --> AE
    AD --> AE
    AE --> AF[Resume Editor]
```

## 4. Cover Letter Generation Flow

```mermaid
flowchart TD
    A[Dashboard/Resume] --> B[Create Cover Letter]
    B --> C{Creation Method}
    
    C -->|AI Generated| D[Job Description Input]
    C -->|From Template| E[Template Selection]
    C -->|From Scratch| F[Blank Editor]
    
    D --> G[Select Source Resume]
    G --> H[AI Analysis]
    H --> I[Extract Job Requirements]
    I --> J[Match Resume Experience]
    J --> K[Generate Cover Letter]
    K --> L[Present Draft]
    
    E --> M[Choose Template Style]
    M --> N[Template Customization]
    N --> O[Fill Template Fields]
    O --> P[Template Preview]
    
    F --> Q[Manual Writing]
    Q --> R[Content Entry]
    
    L --> S{User Review}
    P --> S
    R --> S
    
    S -->|Approve| T[Save Cover Letter]
    S -->|Edit| U[Manual Editing]
    S -->|Regenerate| V[New AI Generation]
    
    U --> W[Edit Content]
    W --> X[Save Changes]
    V --> D
    
    T --> Y[Cover Letter Saved]
    X --> Y
    Y --> Z{Next Action}
    
    Z -->|Export PDF| AA[Generate PDF]
    Z -->|Copy Text| BB[Copy to Clipboard]
    Z -->|Email| CC[Email Integration]
    Z -->|Back to Dashboard| DD[Dashboard]
```

## 5. Resume Sharing & Analytics Flow

```mermaid
flowchart TD
    A[Resume Dashboard] --> B[Select Resume]
    B --> C{Sharing Option}
    
    C -->|Public Link| D[Generate Public URL]
    C -->|PDF Download| E[Export PDF]
    C -->|Email Share| F[Email Integration]
    C -->|Social Share| G[Social Media Links]
    
    D --> H[Configure Privacy Settings]
    H --> I{Allow Public Access?}
    I -->|Yes| J[Create Shareable Link]
    I -->|No| K[Password Protection]
    K --> L[Set Password]
    L --> J
    
    J --> M[Link Generated]
    M --> N[Copy Link]
    N --> O[Share Link]
    
    E --> P[PDF Generation]
    P --> Q[Download PDF]
    
    F --> R[Email Composition]
    R --> S[Send Email]
    
    G --> T[Social Platform Selection]
    T --> U[Post to Social Media]
    
    O --> V[Link Accessed]
    Q --> W[PDF Downloaded]
    S --> X[Email Sent]
    U --> Y[Social Post Created]
    
    V --> Z[Track View Analytics]
    W --> AA[Track Download Analytics]
    X --> BB[Track Email Analytics]
    Y --> CC[Track Social Analytics]
    
    Z --> DD[Analytics Dashboard]
    AA --> DD
    BB --> DD
    CC --> DD
    
    DD --> EE[View Metrics]
    EE --> FF{Insights Available}
    FF -->|Yes| GG[Show Recommendations]
    FF -->|No| HH[Collect More Data]
    
    GG --> II[Optimization Suggestions]
    HH --> JJ[Continue Tracking]
```

## 6. Premium Features Flow

```mermaid
flowchart TD
    A[Free User] --> B{Premium Feature Accessed}
    
    B -->|Advanced Templates| C[Premium Template Gallery]
    B -->|AI Enhancements| D[Advanced AI Features]
    B -->|Analytics| E[Detailed Analytics]
    B -->|Collaboration| F[Team Features]
    
    C --> G[Upgrade Prompt]
    D --> G
    E --> G
    F --> G
    
    G --> H{User Decision}
    H -->|Upgrade| I[Pricing Plans]
    H -->|Cancel| J[Return to Free Features]
    
    I --> K[Plan Selection]
    K --> L[Payment Processing]
    L --> M{Payment Successful?}
    
    M -->|Yes| N[Account Upgraded]
    M -->|No| O[Payment Error]
    O --> P[Retry Payment]
    P --> L
    
    N --> Q[Premium Features Unlocked]
    Q --> R{Feature Access}
    
    R -->|Templates| S[Access Premium Templates]
    R -->|AI| T[Advanced AI Tools]
    R -->|Analytics| U[Detailed Reports]
    R -->|Collaboration| V[Team Workspace]
    
    S --> W[Enhanced Resume Creation]
    T --> X[Superior AI Assistance]
    U --> Y[Comprehensive Insights]
    V --> Z[Team Collaboration]
    
    J --> AA[Continue with Free Plan]
```

## Key User Personas & Journeys

### 1. The Career Accelerator (Primary Target)
- **Goal**: Create ATS-optimized resume quickly
- **Journey**: Sign up → Import LinkedIn → AI enhancement → Export PDF
- **Pain Points**: Time constraints, ATS optimization
- **Success Metrics**: Resume completion time < 15 minutes

### 2. The Career Switcher
- **Goal**: Reframe experience for new industry
- **Journey**: Manual entry → AI rewriting → Industry-specific keywords
- **Pain Points**: Translating skills, industry knowledge
- **Success Metrics**: Successful skill translation, keyword optimization

### 3. The New Graduate
- **Goal**: Create first professional resume
- **Journey**: Template selection → Guided entry → AI suggestions
- **Pain Points**: Lack of experience, professional formatting
- **Success Metrics**: Professional-looking resume, confidence boost
