/**
 * Template Sync & Cloud Management Features Validation Script
 * 
 * Validates template sync, marketplace, and cloud management implementation
 */

const fs = require('fs')
const path = require('path')

class TemplateSyncFeaturesValidator {
  constructor() {
    this.results = {
      files: { checked: 0, missing: 0, present: 0 },
      services: { checked: 0, missing: 0, present: 0 },
      apis: { checked: 0, missing: 0, present: 0 },
      components: { checked: 0, missing: 0, present: 0 },
      tests: { checked: 0, missing: 0, present: 0 },
      config: { checked: 0, missing: 0, present: 0 }
    }
  }

  log(message, type = 'info') {
    const colors = {
      info: '\x1b[36m',    // Cyan
      success: '\x1b[32m', // Green
      error: '\x1b[31m',   // Red
      warning: '\x1b[33m', // Yellow
      reset: '\x1b[0m'     // Reset
    }
    
    console.log(`${colors[type]}${message}${colors.reset}`)
  }

  checkFileExists(filePath, description) {
    const fullPath = path.join(__dirname, filePath)
    const exists = fs.existsSync(fullPath)
    
    if (exists) {
      this.log(`✅ ${description}: Found`, 'success')
      return true
    } else {
      this.log(`❌ ${description}: Missing (${filePath})`, 'error')
      return false
    }
  }

  checkFileContent(filePath, searchTerms, description) {
    const fullPath = path.join(__dirname, filePath)
    
    if (!fs.existsSync(fullPath)) {
      this.log(`❌ ${description}: File not found`, 'error')
      return false
    }

    try {
      const content = fs.readFileSync(fullPath, 'utf8')
      const missingTerms = searchTerms.filter(term => !content.includes(term))
      
      if (missingTerms.length === 0) {
        this.log(`✅ ${description}: All required content found`, 'success')
        return true
      } else {
        this.log(`⚠️  ${description}: Missing content: ${missingTerms.join(', ')}`, 'warning')
        return false
      }
    } catch (error) {
      this.log(`❌ ${description}: Error reading file - ${error.message}`, 'error')
      return false
    }
  }

  validateFileStructure() {
    this.log('\n☁️ Validating Template Sync File Structure...', 'info')
    
    const requiredFiles = [
      'src/lib/template-sync/service.ts',
      'src/lib/template-sync/marketplace-service.ts',
      'src/lib/template-sync/cloud-storage.ts',
      'src/app/api/template-sync/route.ts',
      'src/app/api/template-marketplace/route.ts',
      'src/components/template-sync/TemplateSyncDashboard.tsx',
      'src/components/template-sync/TemplateMarketplace.tsx',
      'src/components/template-sync/CloudTemplateBuilder.tsx',
      'src/test/template-sync/template-sync-service.test.ts',
      'src/test/template-sync/marketplace-service.test.ts',
      'src/test/template-sync/cloud-storage.test.ts',
      'src/test/template-sync/template-sync-api.test.ts',
      'src/test/template-sync/template-sync-components.test.tsx',
      'docs/features/template-sync-spec.md'
    ]

    let present = 0
    let total = requiredFiles.length

    requiredFiles.forEach(file => {
      this.results.files.checked++
      if (this.checkFileExists(file, `File: ${file}`)) {
        present++
        this.results.files.present++
      } else {
        this.results.files.missing++
      }
    })

    this.log(`📊 File Structure: ${present}/${total} files present`, present === total ? 'success' : 'warning')
    return present === total
  }

  validateTemplateSyncServices() {
    this.log('\n🔄 Validating Template Sync Services...', 'info')
    
    const serviceChecks = [
      {
        file: 'src/lib/template-sync/service.ts',
        description: 'Template Sync Service Implementation',
        requiredContent: ['TemplateSyncService', 'syncUserTemplates', 'syncSingleTemplate', 'detectConflicts', 'resolveConflict', 'createTemplateVersion', 'getVersionHistory', 'rollbackToVersion']
      },
      {
        file: 'src/lib/template-sync/marketplace-service.ts',
        description: 'Template Marketplace Service Implementation',
        requiredContent: ['TemplateMarketplaceService', 'searchTemplates', 'getFeaturedTemplates', 'publishTemplate', 'purchaseTemplate', 'addReview', 'getTemplateReviews', 'trackTemplateUsage']
      },
      {
        file: 'src/lib/template-sync/cloud-storage.ts',
        description: 'Cloud Storage Service Implementation',
        requiredContent: ['CloudStorageService', 'LocalStorageProvider', 'S3StorageProvider', 'uploadTemplate', 'downloadTemplate', 'syncTemplate', 'batchSyncTemplates']
      }
    ]

    let passed = 0
    let total = serviceChecks.length

    serviceChecks.forEach(check => {
      this.results.services.checked++
      if (this.checkFileContent(check.file, check.requiredContent, check.description)) {
        passed++
        this.results.services.present++
      } else {
        this.results.services.missing++
      }
    })

    this.log(`📊 Template Sync Services: ${passed}/${total} checks passed`, passed === total ? 'success' : 'warning')
    return passed === total
  }

  validateTemplateSyncAPIs() {
    this.log('\n🌐 Validating Template Sync API Routes...', 'info')
    
    const apiChecks = [
      {
        file: 'src/app/api/template-sync/route.ts',
        description: 'Template Sync API Routes',
        requiredContent: ['GET', 'POST', 'PUT', 'DELETE', 'sync', 'conflicts', 'history', 'resolve-conflict', 'rollback', 'upload-cloud', 'download-cloud', 'batch-sync']
      },
      {
        file: 'src/app/api/template-marketplace/route.ts',
        description: 'Template Marketplace API Routes',
        requiredContent: ['GET', 'POST', 'PUT', 'DELETE', 'search', 'featured', 'publish', 'purchase', 'review', 'create-collection', 'track-usage', 'analytics']
      }
    ]

    let passed = 0
    let total = apiChecks.length

    apiChecks.forEach(check => {
      this.results.apis.checked++
      if (this.checkFileContent(check.file, check.requiredContent, check.description)) {
        passed++
        this.results.apis.present++
      } else {
        this.results.apis.missing++
      }
    })

    this.log(`📊 Template Sync APIs: ${passed}/${total} checks passed`, passed === total ? 'success' : 'warning')
    return passed === total
  }

  validateTemplateSyncComponents() {
    this.log('\n⚛️  Validating Template Sync Components...', 'info')
    
    const componentChecks = [
      {
        file: 'src/components/template-sync/TemplateSyncDashboard.tsx',
        description: 'Template Sync Dashboard Component',
        requiredContent: ['TemplateSyncDashboard', 'syncUserTemplates', 'loadConflicts', 'resolveConflict', 'loadVersionHistory', 'rollbackToVersion']
      },
      {
        file: 'src/components/template-sync/TemplateMarketplace.tsx',
        description: 'Template Marketplace Component',
        requiredContent: ['TemplateMarketplace', 'searchTemplates', 'purchaseTemplate', 'addReview', 'trackTemplateView', 'getFeaturedTemplates']
      },
      {
        file: 'src/components/template-sync/CloudTemplateBuilder.tsx',
        description: 'Cloud Template Builder Component',
        requiredContent: ['CloudTemplateBuilder', 'addElement', 'updateElement', 'deleteElement', 'saveTemplate', 'exportTemplate', 'undo', 'redo']
      }
    ]

    let passed = 0
    let total = componentChecks.length

    componentChecks.forEach(check => {
      this.results.components.checked++
      if (this.checkFileContent(check.file, check.requiredContent, check.description)) {
        passed++
        this.results.components.present++
      } else {
        this.results.components.missing++
      }
    })

    this.log(`📊 Template Sync Components: ${passed}/${total} checks passed`, passed === total ? 'success' : 'warning')
    return passed === total
  }

  validateTemplateSyncTests() {
    this.log('\n🧪 Validating Template Sync Test Files...', 'info')
    
    const testChecks = [
      {
        file: 'src/test/template-sync/template-sync-service.test.ts',
        description: 'Template Sync Service Tests',
        requiredContent: ['describe', 'it', 'expect', 'TemplateSyncService', 'syncUserTemplates', 'detectConflicts', 'resolveConflict', 'createTemplateVersion']
      },
      {
        file: 'src/test/template-sync/marketplace-service.test.ts',
        description: 'Marketplace Service Tests',
        requiredContent: ['describe', 'it', 'expect', 'TemplateMarketplaceService', 'searchTemplates', 'publishTemplate', 'purchaseTemplate', 'addReview']
      },
      {
        file: 'src/test/template-sync/cloud-storage.test.ts',
        description: 'Cloud Storage Tests',
        requiredContent: ['describe', 'it', 'expect', 'CloudStorageService', 'LocalStorageProvider', 'upload', 'download', 'syncTemplate']
      },
      {
        file: 'src/test/template-sync/template-sync-api.test.ts',
        description: 'Template Sync API Tests',
        requiredContent: ['describe', 'it', 'expect', 'NextRequest', 'template-sync', 'template-marketplace', 'GET', 'POST']
      },
      {
        file: 'src/test/template-sync/template-sync-components.test.tsx',
        description: 'Template Sync Component Tests',
        requiredContent: ['describe', 'it', 'expect', 'render', 'TemplateSyncDashboard', 'TemplateMarketplace', 'CloudTemplateBuilder']
      }
    ]

    let passed = 0
    let total = testChecks.length

    testChecks.forEach(check => {
      this.results.tests.checked++
      if (this.checkFileContent(check.file, check.requiredContent, check.description)) {
        passed++
        this.results.tests.present++
      } else {
        this.results.tests.missing++
      }
    })

    this.log(`📊 Template Sync Tests: ${passed}/${total} checks passed`, passed === total ? 'success' : 'warning')
    return passed === total
  }

  validateConfiguration() {
    this.log('\n⚙️  Validating Template Sync Configuration...', 'info')
    
    const configChecks = [
      {
        file: 'package.json',
        description: 'Package.json Template Sync Scripts',
        requiredContent: ['test:template-sync', 'test:template-sync:service', 'test:template-sync:marketplace', 'test:template-sync:cloud', 'test:template-sync:api', 'test:template-sync:components']
      },
      {
        file: 'packages/database/prisma/schema.prisma',
        description: 'Database Schema Extensions',
        requiredContent: ['TemplateCloudStorage', 'TemplateVersion', 'TemplateMarketplace', 'TemplatePurchase', 'TemplateReview', 'TemplateCollection', 'TemplateShare', 'TemplateSyncConflict', 'TemplateUsageAnalytics']
      }
    ]

    let passed = 0
    let total = configChecks.length

    configChecks.forEach(check => {
      this.results.config.checked++
      if (this.checkFileContent(check.file, check.requiredContent, check.description)) {
        passed++
        this.results.config.present++
      } else {
        this.results.config.missing++
      }
    })

    this.log(`📊 Template Sync Configuration: ${passed}/${total} checks passed`, passed === total ? 'success' : 'warning')
    return passed === total
  }

  generateReport() {
    this.log('\n📊 Template Sync & Cloud Management Features Validation Report', 'info')
    this.log('=' .repeat(70), 'info')
    
    const categories = ['files', 'services', 'apis', 'components', 'tests', 'config']
    let totalChecked = 0
    let totalPresent = 0
    let totalMissing = 0

    categories.forEach(category => {
      const result = this.results[category]
      totalChecked += result.checked
      totalPresent += result.present
      totalMissing += result.missing

      const status = result.missing === 0 ? '✅' : '⚠️ '
      const categoryName = category.charAt(0).toUpperCase() + category.slice(1)
      
      this.log(
        `${status} ${categoryName}: ${result.present}/${result.checked} present`,
        result.missing === 0 ? 'success' : 'warning'
      )
    })

    this.log('=' .repeat(70), 'info')
    this.log(`📈 Overall: ${totalPresent}/${totalChecked} checks passed`, 
             totalMissing === 0 ? 'success' : 'warning')
    
    const completionRate = totalChecked > 0 ? (totalPresent / totalChecked * 100).toFixed(1) : 0
    this.log(`📊 Completion Rate: ${completionRate}%`, 
             completionRate >= 90 ? 'success' : completionRate >= 70 ? 'warning' : 'error')

    // Recommendations
    this.log('\n💡 Template Sync & Cloud Management Status:', 'info')
    if (totalMissing === 0) {
      this.log('🎉 All template sync and cloud management features are properly implemented!', 'success')
      this.log('☁️ Ready for cloud-based template synchronization and marketplace', 'success')
    } else {
      this.log(`🔧 ${totalMissing} items need attention`, 'warning')
      if (this.results.files.missing > 0) {
        this.log('📁 Create missing files and implement required functionality', 'warning')
      }
      if (this.results.tests.missing > 0) {
        this.log('🧪 Add comprehensive tests for template sync features', 'warning')
      }
      if (this.results.config.missing > 0) {
        this.log('⚙️  Update configuration files for template sync', 'warning')
      }
    }

    return totalMissing === 0
  }

  async validate() {
    this.log('🚀 Starting Template Sync & Cloud Management Features Validation...', 'info')
    this.log('📅 ' + new Date().toISOString(), 'info')
    
    const results = [
      this.validateFileStructure(),
      this.validateTemplateSyncServices(),
      this.validateTemplateSyncAPIs(),
      this.validateTemplateSyncComponents(),
      this.validateTemplateSyncTests(),
      this.validateConfiguration()
    ]

    const allValid = this.generateReport()
    
    this.log('\n🏁 Template Sync & Cloud Management Validation Complete!', 'info')
    return allValid
  }
}

// Run validation if this script is executed directly
if (require.main === module) {
  const validator = new TemplateSyncFeaturesValidator()
  validator.validate()
    .then(success => {
      process.exit(success ? 0 : 1)
    })
    .catch(error => {
      console.error('Validation failed:', error)
      process.exit(1)
    })
}

module.exports = TemplateSyncFeaturesValidator
