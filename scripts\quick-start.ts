#!/usr/bin/env tsx

/**
 * Quick Start Script for CareerCraft
 * 
 * This script performs a quick setup and verification
 */

import { execSync } from 'child_process';
import { existsSync, writeFileSync } from 'fs';
import { join } from 'path';

const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

function log(message: string, color: keyof typeof colors = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function runCommand(command: string, description: string): boolean {
  try {
    log(`🔄 ${description}...`, 'blue');
    execSync(command, { stdio: 'inherit' });
    log(`✅ ${description} completed`, 'green');
    return true;
  } catch (error) {
    log(`❌ ${description} failed`, 'red');
    return false;
  }
}

async function quickStart() {
  log('🚀 CareerCraft - Quick Start Setup', 'cyan');
  log('=====================================\n', 'cyan');

  // Check Node.js version
  const nodeVersion = process.version;
  const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
  
  if (majorVersion < 18) {
    log(`❌ Node.js ${nodeVersion} is too old. Please install Node.js 18+`, 'red');
    process.exit(1);
  }
  
  log(`✅ Node.js ${nodeVersion} is supported`, 'green');

  // Create .env.local if it doesn't exist
  const envPath = join(process.cwd(), '.env.local');
  if (!existsSync(envPath)) {
    log('📝 Creating .env.local file...', 'blue');
    
    const envContent = `# Database
DATABASE_URL="file:./dev.db"

# NextAuth.js
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-secret-key-change-this-in-production"

# OAuth Providers (Optional - for testing auth)
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"
GITHUB_ID="your-github-client-id"
GITHUB_SECRET="your-github-client-secret"

# AI Services (Optional - for testing AI features)
OPENAI_API_KEY="your-openai-api-key"

# Feature Flags
ENABLE_AI_FEATURES="true"
ENABLE_PREMIUM_FEATURES="true"
ENABLE_ANALYTICS="false"
`;
    
    writeFileSync(envPath, envContent);
    log('✅ Created .env.local file', 'green');
    log('ℹ️  The app will work with mock data even without real API keys', 'yellow');
  } else {
    log('✅ .env.local file already exists', 'green');
  }

  // Install dependencies
  if (!runCommand('npm install', 'Installing dependencies')) {
    process.exit(1);
  }

  // Build packages
  if (!runCommand('npm run build:packages', 'Building packages')) {
    log('⚠️  Build failed, but continuing...', 'yellow');
  }

  // Setup database
  if (!runCommand('npm run db:push', 'Setting up database')) {
    log('⚠️  Database setup failed, but continuing...', 'yellow');
  }

  // Seed database
  if (!runCommand('npm run db:seed', 'Seeding database with sample data')) {
    log('⚠️  Database seeding failed, but continuing...', 'yellow');
  }

  // Run verification
  log('\n🔍 Running setup verification...', 'blue');
  try {
    execSync('npm run verify', { stdio: 'inherit' });
  } catch (error) {
    log('⚠️  Some verification checks failed, but you can still proceed', 'yellow');
  }

  // Success message
  log('\n🎉 Quick Start Setup Complete!', 'green');
  log('================================\n', 'green');
  
  log('Next steps:', 'bright');
  log('1. Start the development server:', 'cyan');
  log('   npm run dev\n', 'bright');
  
  log('2. Open your browser and visit:', 'cyan');
  log('   http://localhost:3000\n', 'bright');
  
  log('3. Test with demo accounts:', 'cyan');
  log('   • <EMAIL>', 'bright');
  log('   • <EMAIL>', 'bright');
  log('   • <EMAIL>\n', 'bright');
  
  log('4. Explore the features:', 'cyan');
  log('   • Homepage: http://localhost:3000', 'bright');
  log('   • Templates: http://localhost:3000/templates', 'bright');
  log('   • AI Demo: http://localhost:3000/ai-demo', 'bright');
  log('   • Dashboard: http://localhost:3000/dashboard\n', 'bright');
  
  log('5. Run tests to verify everything works:', 'cyan');
  log('   npm run test:db', 'bright');
  log('   npm run test:auth', 'bright');
  log('   npm run test:ui', 'bright');
  log('   npm run test:resume', 'bright');
  log('   npm run test:templates', 'bright');
  log('   npm run test:ai\n', 'bright');
  
  log('📖 For detailed testing instructions, see:', 'cyan');
  log('   README_LOCAL_TESTING.md\n', 'bright');
  
  log('Happy coding! 🚀', 'magenta');
}

// Run quick start
if (require.main === module) {
  quickStart().catch(console.error);
}
