import { describe, it, expect, jest, beforeEach } from '@jest/globals';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { PersonalInfoForm } from '../personal-info-form';

const mockOnSubmit = jest.fn();
const mockOnCancel = jest.fn();

describe('PersonalInfoForm', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  const defaultProps = {
    onSubmit: mockOnSubmit,
    onCancel: mockOnCancel,
  };

  it('should render all form fields', () => {
    render(<PersonalInfoForm {...defaultProps} />);

    expect(screen.getByLabelText(/first name/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/last name/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/email/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/phone/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/location/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/website/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/linkedin/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/github/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/portfolio/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/professional summary/i)).toBeInTheDocument();
  });

  it('should show required field indicators', () => {
    render(<PersonalInfoForm {...defaultProps} />);

    const requiredFields = screen.getAllByText('*');
    expect(requiredFields).toHaveLength(5); // firstName, lastName, email, phone, location
  });

  it('should populate form with initial data', () => {
    const initialData = {
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      phone: '+****************',
      location: 'San Francisco, CA',
      website: 'https://johndoe.com',
      linkedin: 'https://linkedin.com/in/johndoe',
      github: 'https://github.com/johndoe',
      portfolio: 'https://portfolio.johndoe.com',
      summary: 'Experienced software engineer with 5+ years of experience.',
    };

    render(<PersonalInfoForm {...defaultProps} initialData={initialData} />);

    expect(screen.getByDisplayValue('John')).toBeInTheDocument();
    expect(screen.getByDisplayValue('Doe')).toBeInTheDocument();
    expect(screen.getByDisplayValue('<EMAIL>')).toBeInTheDocument();
    expect(screen.getByDisplayValue('+****************')).toBeInTheDocument();
    expect(screen.getByDisplayValue('San Francisco, CA')).toBeInTheDocument();
    expect(screen.getByDisplayValue('https://johndoe.com')).toBeInTheDocument();
    expect(screen.getByDisplayValue('https://linkedin.com/in/johndoe')).toBeInTheDocument();
    expect(screen.getByDisplayValue('https://github.com/johndoe')).toBeInTheDocument();
    expect(screen.getByDisplayValue('https://portfolio.johndoe.com')).toBeInTheDocument();
    expect(screen.getByDisplayValue('Experienced software engineer with 5+ years of experience.')).toBeInTheDocument();
  });

  it('should validate required fields', async () => {
    const user = userEvent.setup();
    render(<PersonalInfoForm {...defaultProps} />);

    const submitButton = screen.getByRole('button', { name: /save personal information/i });
    
    // Try to submit without filling required fields
    await user.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(/first name is required/i)).toBeInTheDocument();
      expect(screen.getByText(/last name is required/i)).toBeInTheDocument();
      expect(screen.getByText(/invalid email address/i)).toBeInTheDocument();
      expect(screen.getByText(/phone number must be at least 10 digits/i)).toBeInTheDocument();
      expect(screen.getByText(/location is required/i)).toBeInTheDocument();
    });

    expect(mockOnSubmit).not.toHaveBeenCalled();
  });

  it('should validate email format', async () => {
    const user = userEvent.setup();
    render(<PersonalInfoForm {...defaultProps} />);

    const emailInput = screen.getByLabelText(/email/i);
    await user.type(emailInput, 'invalid-email');

    await waitFor(() => {
      expect(screen.getByText(/invalid email address/i)).toBeInTheDocument();
    });
  });

  it('should validate URL formats for optional links', async () => {
    const user = userEvent.setup();
    render(<PersonalInfoForm {...defaultProps} />);

    const websiteInput = screen.getByLabelText(/website/i);
    await user.type(websiteInput, 'not-a-url');

    await waitFor(() => {
      expect(screen.getByText(/invalid website url/i)).toBeInTheDocument();
    });
  });

  it('should show character count for summary', async () => {
    const user = userEvent.setup();
    render(<PersonalInfoForm {...defaultProps} />);

    const summaryInput = screen.getByLabelText(/professional summary/i);
    expect(screen.getByText('0/500 characters')).toBeInTheDocument();

    await user.type(summaryInput, 'Test summary');

    await waitFor(() => {
      expect(screen.getByText('12/500 characters')).toBeInTheDocument();
    });
  });

  it('should submit form with valid data', async () => {
    const user = userEvent.setup();
    render(<PersonalInfoForm {...defaultProps} />);

    // Fill in required fields
    await user.type(screen.getByLabelText(/first name/i), 'John');
    await user.type(screen.getByLabelText(/last name/i), 'Doe');
    await user.type(screen.getByLabelText(/email/i), '<EMAIL>');
    await user.type(screen.getByLabelText(/phone/i), '+****************');
    await user.type(screen.getByLabelText(/location/i), 'San Francisco, CA');

    const submitButton = screen.getByRole('button', { name: /save personal information/i });
    await user.click(submitButton);

    await waitFor(() => {
      expect(mockOnSubmit).toHaveBeenCalledWith({
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        phone: '+****************',
        location: 'San Francisco, CA',
        website: '',
        linkedin: '',
        github: '',
        portfolio: '',
        summary: '',
      });
    });
  });

  it('should disable submit button when form is invalid', () => {
    render(<PersonalInfoForm {...defaultProps} />);

    const submitButton = screen.getByRole('button', { name: /save personal information/i });
    expect(submitButton).toBeDisabled();
  });

  it('should show loading state', () => {
    render(<PersonalInfoForm {...defaultProps} isLoading={true} />);

    const submitButton = screen.getByRole('button', { name: /save personal information/i });
    expect(submitButton).toBeDisabled();
    expect(screen.getByRole('img', { hidden: true })).toBeInTheDocument(); // Loading spinner
  });

  it('should show unsaved changes indicator', async () => {
    const user = userEvent.setup();
    render(<PersonalInfoForm {...defaultProps} />);

    const firstNameInput = screen.getByLabelText(/first name/i);
    await user.type(firstNameInput, 'John');

    await waitFor(() => {
      expect(screen.getByText(/you have unsaved changes/i)).toBeInTheDocument();
    });
  });

  it('should call onCancel when cancel button is clicked', async () => {
    const user = userEvent.setup();
    render(<PersonalInfoForm {...defaultProps} />);

    const cancelButton = screen.getByRole('button', { name: /cancel/i });
    await user.click(cancelButton);

    expect(mockOnCancel).toHaveBeenCalled();
  });

  it('should not show cancel button when onCancel is not provided', () => {
    render(<PersonalInfoForm onSubmit={mockOnSubmit} />);

    expect(screen.queryByRole('button', { name: /cancel/i })).not.toBeInTheDocument();
  });

  it('should handle empty optional fields correctly', async () => {
    const user = userEvent.setup();
    render(<PersonalInfoForm {...defaultProps} />);

    // Fill in required fields only
    await user.type(screen.getByLabelText(/first name/i), 'John');
    await user.type(screen.getByLabelText(/last name/i), 'Doe');
    await user.type(screen.getByLabelText(/email/i), '<EMAIL>');
    await user.type(screen.getByLabelText(/phone/i), '+****************');
    await user.type(screen.getByLabelText(/location/i), 'San Francisco, CA');

    const submitButton = screen.getByRole('button', { name: /save personal information/i });
    await user.click(submitButton);

    await waitFor(() => {
      expect(mockOnSubmit).toHaveBeenCalledWith(
        expect.objectContaining({
          website: '',
          linkedin: '',
          github: '',
          portfolio: '',
          summary: '',
        })
      );
    });
  });

  it('should validate summary length limit', async () => {
    const user = userEvent.setup();
    render(<PersonalInfoForm {...defaultProps} />);

    const summaryInput = screen.getByLabelText(/professional summary/i);
    const longSummary = 'a'.repeat(501); // Exceeds 500 character limit
    
    await user.type(summaryInput, longSummary);

    await waitFor(() => {
      expect(screen.getByText(/summary too long/i)).toBeInTheDocument();
    });
  });

  it('should apply custom className', () => {
    const { container } = render(
      <PersonalInfoForm {...defaultProps} className="custom-class" />
    );

    expect(container.firstChild).toHaveClass('custom-class');
  });
});
