@echo off
echo.
echo ========================================
echo   CareerCraft - Demo Server
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [INFO] Python not found, trying alternative methods...
    goto :try_node
)

echo [INFO] Starting demo server with Python...
echo [INFO] Demo will be available at: http://localhost:8000
echo [INFO] Press Ctrl+C to stop the server
echo.

REM Start Python HTTP server
python -m http.server 8000
goto :end

:try_node
REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [INFO] Node.js not found, trying npx...
    goto :try_npx
)

echo [INFO] Starting demo server with Node.js...
echo [INFO] Demo will be available at: http://localhost:8000
echo [INFO] Press Ctrl+C to stop the server
echo.

REM Create a simple Node.js server
echo const http = require('http'); > temp_server.js
echo const fs = require('fs'); >> temp_server.js
echo const path = require('path'); >> temp_server.js
echo. >> temp_server.js
echo const server = http.createServer((req, res) => { >> temp_server.js
echo   let filePath = req.url === '/' ? './demo.html' : '.' + req.url; >> temp_server.js
echo   const extname = path.extname(filePath).toLowerCase(); >> temp_server.js
echo   const mimeTypes = { >> temp_server.js
echo     '.html': 'text/html', >> temp_server.js
echo     '.css': 'text/css', >> temp_server.js
echo     '.js': 'text/javascript' >> temp_server.js
echo   }; >> temp_server.js
echo   const contentType = mimeTypes[extname] ^|^| 'application/octet-stream'; >> temp_server.js
echo   fs.readFile(filePath, (err, content) => { >> temp_server.js
echo     if (err) { >> temp_server.js
echo       res.writeHead(404); >> temp_server.js
echo       res.end('File not found'); >> temp_server.js
echo     } else { >> temp_server.js
echo       res.writeHead(200, { 'Content-Type': contentType }); >> temp_server.js
echo       res.end(content, 'utf-8'); >> temp_server.js
echo     } >> temp_server.js
echo   }); >> temp_server.js
echo }); >> temp_server.js
echo. >> temp_server.js
echo server.listen(8000, () => { >> temp_server.js
echo   console.log('Demo server running at http://localhost:8000'); >> temp_server.js
echo }); >> temp_server.js

node temp_server.js
del temp_server.js
goto :end

:try_npx
echo [INFO] Trying to start server with npx...
npx http-server -p 8000
goto :end

:end
echo.
echo [INFO] Demo server stopped.
echo [INFO] You can also open demo.html directly in your browser.
pause
