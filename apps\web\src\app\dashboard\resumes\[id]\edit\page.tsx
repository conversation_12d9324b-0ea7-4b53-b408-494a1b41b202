'use client'

import { useEffect, useState } from 'react'
import { ResumeEditor } from '@/components/resume/ResumeEditor'
import { DashboardLayout } from '@/components/dashboard/DashboardLayout'
import { LoadingPage } from '@/components/ui/loading'

interface EditResumePageProps {
  params: {
    id: string;
  };
}

interface Resume {
  id: string
  title: string
  description?: string
  templateId?: string
  personalInfo?: any
  experience?: any[]
  education?: any[]
  skills?: any[]
  achievements?: string[]
  certifications?: string[]
  createdAt: Date
  updatedAt: Date
}

export default function EditResumePage({ params }: EditResumePageProps) {
  const resumeId = params.id
  const [resume, setResume] = useState<Resume | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (resumeId) {
      loadResume()
    }
  }, [resumeId])

  const loadResume = async () => {
    try {
      setIsLoading(true)

      // Mock data for now - replace with actual API call
      await new Promise(resolve => setTimeout(resolve, 1000))

      const mockResume: Resume = {
        id: resumeId,
        title: 'Software Engineer Resume',
        description: 'Full-stack developer position at tech companies',
        templateId: 'modern-1',
        personalInfo: {
          fullName: 'John Doe',
          email: '<EMAIL>',
          phone: '+****************',
          location: 'San Francisco, CA',
          linkedin: 'linkedin.com/in/johndoe',
          github: 'github.com/johndoe',
          summary: 'Experienced software engineer with 5+ years developing scalable web applications using React, Node.js, and cloud technologies. Proven track record of improving system performance and leading cross-functional teams.'
        },
        experience: [
          {
            id: '1',
            company: 'Tech Corp',
            position: 'Senior Software Engineer',
            location: 'San Francisco, CA',
            startDate: '2022-01',
            endDate: '',
            current: true,
            description: 'Lead development of customer-facing web applications using React and Node.js',
            achievements: [
              'Improved application performance by 40%',
              'Led team of 4 developers',
              'Implemented CI/CD pipeline'
            ]
          }
        ],
        education: [
          {
            id: '1',
            institution: 'University of California, Berkeley',
            degree: 'Bachelor of Science',
            field: 'Computer Science',
            startDate: '2016-09',
            endDate: '2020-05',
            gpa: '3.8',
            achievements: ['Magna Cum Laude', 'Dean\'s List']
          }
        ],
        skills: [
          {
            id: '1',
            category: 'Programming Languages',
            skills: ['JavaScript', 'TypeScript', 'Python', 'Java']
          },
          {
            id: '2',
            category: 'Frameworks & Libraries',
            skills: ['React', 'Node.js', 'Express', 'Django']
          }
        ],
        achievements: [
          'Led migration to microservices architecture, reducing deployment time by 60%',
          'Mentored 3 junior developers and established code review processes'
        ],
        certifications: [
          'AWS Certified Solutions Architect',
          'Google Cloud Professional Developer'
        ],
        createdAt: new Date('2024-01-01'),
        updatedAt: new Date('2024-01-15')
      }

      setResume(mockResume)
    } catch (error) {
      console.error('Error loading resume:', error)
      setError('Failed to load resume')
    } finally {
      setIsLoading(false)
    }
  }

  if (isLoading) {
    return (
      <DashboardLayout currentPage="resumes">
        <LoadingPage message="Loading AI-powered resume editor..." />
      </DashboardLayout>
    )
  }

  if (error) {
    return (
      <DashboardLayout currentPage="resumes">
        <div className="flex items-center justify-center h-64">
          <div className="text-center glass-panel p-8 rounded-2xl">
            <h2 className="text-xl font-semibold text-red-600 mb-2">Error</h2>
            <p className="text-muted-foreground">{error}</p>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  if (!resume) {
    return (
      <DashboardLayout currentPage="resumes">
        <div className="flex items-center justify-center h-64">
          <div className="text-center glass-panel p-8 rounded-2xl">
            <h2 className="text-xl font-semibold mb-2">Resume Not Found</h2>
            <p className="text-muted-foreground">The resume you're looking for doesn't exist.</p>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <ResumeEditor
      resumeId={resumeId}
      templateId={resume.templateId}
      initialData={{
        personalInfo: resume.personalInfo,
        experience: resume.experience || [],
        education: resume.education || [],
        skills: resume.skills || [],
        achievements: resume.achievements || [],
        certifications: resume.certifications || []
      }}
    />
  )
}
