# CareerCraft Clean Setup Script (Windows PowerShell)
# Run as Administrator

Write-Host "🚀 CareerCraft Clean Setup" -ForegroundColor Green
Write-Host "==========================" -ForegroundColor Green

# Check Administrator privileges
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "❌ Please run PowerShell as Administrator" -ForegroundColor Red
    exit 1
}

# Step 1: Install Chocolatey
Write-Host "📦 Installing Chocolatey package manager..." -ForegroundColor Yellow
if (!(Get-Command choco -ErrorAction SilentlyContinue)) {
    Set-ExecutionPolicy Bypass -Scope Process -Force
    [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072
    iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
    Write-Host "✅ Chocolatey installed" -ForegroundColor Green
} else {
    Write-Host "✅ Chocolatey already installed" -ForegroundColor Green
}

# Step 2: Install prerequisites
Write-Host "📦 Installing prerequisites..." -ForegroundColor Yellow
choco install nodejs postgresql git -y
Write-Host "✅ Prerequisites installed" -ForegroundColor Green

# Refresh environment
refreshenv

# Step 3: Verify installations
Write-Host "🔍 Verifying installations..." -ForegroundColor Yellow
try {
    $nodeVersion = node --version
    Write-Host "✅ Node.js: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Node.js not found" -ForegroundColor Red
}

try {
    $npmVersion = npm --version
    Write-Host "✅ npm: $npmVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ npm not found" -ForegroundColor Red
}

# Step 4: Create project
$projectName = "careercraft-local"
Write-Host "📁 Creating project: $projectName" -ForegroundColor Yellow

if (Test-Path $projectName) {
    Write-Host "⚠️ Directory exists. Removing..." -ForegroundColor Yellow
    Remove-Item -Recurse -Force $projectName
}

# Create Next.js project
Write-Host "📦 Creating Next.js project..." -ForegroundColor Yellow
npx create-next-app@latest $projectName --typescript --tailwind --eslint --app --src-dir --import-alias "@/*"

# Navigate to project
Set-Location $projectName

# Step 5: Install dependencies
Write-Host "📦 Installing dependencies..." -ForegroundColor Yellow

# Core dependencies
npm install @prisma/client prisma
npm install next-auth "@auth/prisma-adapter"
npm install stripe "@stripe/stripe-js"
npm install openai
npm install "@radix-ui/react-dialog" "@radix-ui/react-dropdown-menu"
npm install lucide-react
npm install "@hookform/resolvers" react-hook-form zod
npm install axios swr
npm install recharts date-fns clsx tailwind-merge

# Dev dependencies
npm install -D "@types/node" "@types/react" "@types/react-dom"
npm install -D jest "@testing-library/react" "@testing-library/jest-dom"
npm install -D playwright "@playwright/test"
npm install -D cross-env concurrently tsx

Write-Host "✅ Dependencies installed" -ForegroundColor Green

# Step 6: Create environment file
Write-Host "📝 Creating environment file..." -ForegroundColor Yellow

$envContent = @'
# Database
DATABASE_URL="postgresql://careercraft_user:local_password@localhost:5432/careercraft_local"

# NextAuth
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-local-secret-key-change-this-in-production"

# Google OAuth (Get from Google Cloud Console)
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"

# Stripe (Test Mode - Get from Stripe Dashboard)
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY="pk_test_..."
STRIPE_SECRET_KEY="sk_test_..."

# OpenAI (Get from OpenAI Platform)
OPENAI_API_KEY="sk-..."

# App Settings
NODE_ENV="development"
NEXT_PUBLIC_APP_URL="http://localhost:3000"
'@

$envContent | Out-File -FilePath ".env.local" -Encoding UTF8
Write-Host "✅ Environment file created" -ForegroundColor Green

# Step 7: Create Prisma schema
Write-Host "📋 Setting up Prisma..." -ForegroundColor Yellow

if (!(Test-Path "prisma")) {
    New-Item -ItemType Directory -Path "prisma"
}

$prismaSchema = @'
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  name      String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("users")
}

model Resume {
  id        String   @id @default(cuid())
  userId    String
  title     String
  content   Json
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  user User @relation(fields: [userId], references: [id])

  @@map("resumes")
}
'@

$prismaSchema | Out-File -FilePath "prisma/schema.prisma" -Encoding UTF8
Write-Host "✅ Prisma schema created" -ForegroundColor Green

# Step 8: Database setup
Write-Host "🗄️ Setting up database..." -ForegroundColor Yellow

# Start PostgreSQL service
try {
    Start-Service postgresql-x64-14 -ErrorAction SilentlyContinue
    Write-Host "✅ PostgreSQL service started" -ForegroundColor Green
} catch {
    Write-Host "⚠️ Could not start PostgreSQL service automatically" -ForegroundColor Yellow
    Write-Host "   Please start it manually from Windows Services" -ForegroundColor Yellow
}

# Create database and user
Write-Host "👤 Creating database user and database..." -ForegroundColor Yellow
try {
    & psql -U postgres -c "CREATE USER careercraft_user WITH PASSWORD 'local_password';" 2>$null
    & psql -U postgres -c "CREATE DATABASE careercraft_local OWNER careercraft_user;" 2>$null
    & psql -U postgres -c "GRANT ALL PRIVILEGES ON DATABASE careercraft_local TO careercraft_user;" 2>$null
    Write-Host "✅ Database setup completed" -ForegroundColor Green
} catch {
    Write-Host "⚠️ Database setup may need manual configuration" -ForegroundColor Yellow
    Write-Host "   Please ensure PostgreSQL is running and accessible" -ForegroundColor Yellow
}

# Generate Prisma client
Write-Host "⚙️ Generating Prisma client..." -ForegroundColor Yellow
npx prisma generate
npx prisma db push

# Step 9: Create startup script
Write-Host "🚀 Creating startup script..." -ForegroundColor Yellow

# Create batch file content as separate lines to avoid syntax issues
"@echo off" | Out-File -FilePath "start-dev.bat" -Encoding ASCII
"echo Starting CareerCraft Development" | Out-File -FilePath "start-dev.bat" -Encoding ASCII -Append
"echo ================================" | Out-File -FilePath "start-dev.bat" -Encoding ASCII -Append
"echo." | Out-File -FilePath "start-dev.bat" -Encoding ASCII -Append
"echo Checking PostgreSQL..." | Out-File -FilePath "start-dev.bat" -Encoding ASCII -Append
"pg_isready -h localhost -p 5432 >nul 2>&1" | Out-File -FilePath "start-dev.bat" -Encoding ASCII -Append
"if %errorlevel% neq 0 (" | Out-File -FilePath "start-dev.bat" -Encoding ASCII -Append
"    echo PostgreSQL not running. Please start PostgreSQL service." | Out-File -FilePath "start-dev.bat" -Encoding ASCII -Append
"    pause" | Out-File -FilePath "start-dev.bat" -Encoding ASCII -Append
"    exit /b 1" | Out-File -FilePath "start-dev.bat" -Encoding ASCII -Append
")" | Out-File -FilePath "start-dev.bat" -Encoding ASCII -Append
"echo." | Out-File -FilePath "start-dev.bat" -Encoding ASCII -Append
"echo Starting development server..." | Out-File -FilePath "start-dev.bat" -Encoding ASCII -Append
"echo Visit: http://localhost:3000" | Out-File -FilePath "start-dev.bat" -Encoding ASCII -Append
"echo." | Out-File -FilePath "start-dev.bat" -Encoding ASCII -Append
"npm run dev" | Out-File -FilePath "start-dev.bat" -Encoding ASCII -Append
Write-Host "✅ Startup script created" -ForegroundColor Green

# Step 10: Final summary
Write-Host ""
Write-Host "🎉 Setup Complete!" -ForegroundColor Green
Write-Host "==================" -ForegroundColor Green
Write-Host ""
Write-Host "📁 Project created in: careercraft-local" -ForegroundColor Yellow
Write-Host "📝 Next steps:" -ForegroundColor Yellow
Write-Host "   1. Add your API keys to .env.local" -ForegroundColor White
Write-Host "   2. Run: start-dev.bat" -ForegroundColor White
Write-Host "   3. Visit: http://localhost:3000" -ForegroundColor White
Write-Host ""
Write-Host "🔧 Useful commands:" -ForegroundColor Yellow
Write-Host "   npm run dev       - Start development server" -ForegroundColor White
Write-Host "   npx prisma studio - Open database admin" -ForegroundColor White
Write-Host ""
Write-Host "✅ Setup script completed successfully!" -ForegroundColor Green
