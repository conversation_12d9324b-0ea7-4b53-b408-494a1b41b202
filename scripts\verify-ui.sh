#!/bin/bash

# CareerCraft UI Components Verification Script
# This script verifies that the UI components and layouts are working correctly

set -e

echo "🎨 Verifying CareerCraft UI Components"
echo "========================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check UI component files exist
check_ui_components() {
    print_status "Checking UI component files..."
    
    local failed=0
    local components=(
        "components/layout/header.tsx"
        "components/layout/footer.tsx"
        "components/layout/main-layout.tsx"
        "components/layout/dashboard-layout.tsx"
        "components/ui/button.tsx"
        "components/ui/input.tsx"
        "components/ui/card.tsx"
        "components/ui/loading.tsx"
        "components/ui/error-boundary.tsx"
        "components/ui/icons.tsx"
        "components/ui/dropdown-menu.tsx"
        "components/ui/avatar.tsx"
        "components/ui/scroll-area.tsx"
        "components/ui/separator.tsx"
        "components/ui/alert.tsx"
        "components/ui/checkbox.tsx"
        "components/ui/toaster.tsx"
    )
    
    for component in "${components[@]}"; do
        if [ -f "apps/web/src/$component" ]; then
            print_success "$component exists"
        else
            print_error "$component not found"
            failed=1
        fi
    done
    
    return $failed
}

# Check provider components
check_providers() {
    print_status "Checking provider components..."
    
    local failed=0
    local providers=(
        "components/providers/theme-provider.tsx"
        "components/providers/session-provider.tsx"
        "components/providers/index.tsx"
    )
    
    for provider in "${providers[@]}"; do
        if [ -f "apps/web/src/$provider" ]; then
            print_success "$provider exists"
        else
            print_error "$provider not found"
            failed=1
        fi
    done
    
    return $failed
}

# Check UI dependencies
check_ui_dependencies() {
    print_status "Checking UI dependencies..."
    
    local failed=0
    local dependencies=(
        "@radix-ui/react-avatar"
        "@radix-ui/react-checkbox"
        "@radix-ui/react-dropdown-menu"
        "@radix-ui/react-label"
        "@radix-ui/react-scroll-area"
        "@radix-ui/react-separator"
        "@radix-ui/react-slot"
        "class-variance-authority"
        "clsx"
        "lucide-react"
        "next-themes"
        "tailwind-merge"
        "tailwindcss-animate"
    )
    
    cd apps/web
    
    for dep in "${dependencies[@]}"; do
        if npm list "$dep" > /dev/null 2>&1; then
            print_success "$dep is installed"
        else
            print_error "$dep is not installed"
            failed=1
        fi
    done
    
    cd ../..
    return $failed
}

# Test TypeScript compilation
test_typescript() {
    print_status "Testing TypeScript compilation..."
    
    cd apps/web
    if npm run type-check > /dev/null 2>&1; then
        print_success "TypeScript compilation passed"
        cd ../..
        return 0
    else
        print_error "TypeScript compilation failed"
        cd ../..
        return 1
    fi
}

# Test component imports
test_component_imports() {
    print_status "Testing component imports..."
    
    # Create a temporary test file
    local test_file="apps/web/src/test-ui-imports.ts"
    
    cat > "$test_file" << 'EOF'
// Layout components
import { Header } from '@/components/layout/header';
import { Footer } from '@/components/layout/footer';
import { MainLayout } from '@/components/layout/main-layout';
import { DashboardLayout } from '@/components/layout/dashboard-layout';

// UI components
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { LoadingSpinner, LoadingPage } from '@/components/ui/loading';
import { ErrorBoundary } from '@/components/ui/error-boundary';
import { Icons } from '@/components/ui/icons';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';

// Providers
import { ThemeProvider } from '@/components/providers/theme-provider';
import { SessionProvider } from '@/components/providers/session-provider';

export {};
EOF

    cd apps/web
    if npx tsc --noEmit "$test_file" > /dev/null 2>&1; then
        print_success "Component imports are valid"
        rm -f "$test_file"
        cd ../..
        return 0
    else
        print_error "Component imports failed"
        rm -f "$test_file"
        cd ../..
        return 1
    fi
}

# Test UI component tests
test_ui_tests() {
    print_status "Running UI component tests..."
    
    cd apps/web
    if npm run test -- --testPathPattern="components" --passWithNoTests > /dev/null 2>&1; then
        print_success "UI component tests passed"
        cd ../..
        return 0
    else
        print_error "UI component tests failed"
        cd ../..
        return 1
    fi
}

# Test responsive design classes
test_responsive_design() {
    print_status "Checking responsive design implementation..."
    
    local failed=0
    local responsive_patterns=("md:" "lg:" "sm:" "xl:" "hidden.*md:" "flex.*md:" "grid.*md:")
    
    for pattern in "${responsive_patterns[@]}"; do
        if grep -r "$pattern" apps/web/src/components/layout/ > /dev/null 2>&1; then
            print_success "Found responsive pattern: $pattern"
        else
            print_warning "Responsive pattern not found: $pattern"
        fi
    done
    
    return 0
}

# Test accessibility features
test_accessibility() {
    print_status "Checking accessibility features..."
    
    local accessibility_patterns=("aria-label" "aria-labelledby" "aria-describedby" "role=" "sr-only")
    local found_features=0
    
    for pattern in "${accessibility_patterns[@]}"; do
        if grep -r "$pattern" apps/web/src/components/ > /dev/null 2>&1; then
            print_success "Found accessibility feature: $pattern"
            ((found_features++))
        fi
    done
    
    if [ $found_features -gt 0 ]; then
        print_success "Accessibility features implemented ($found_features found)"
        return 0
    else
        print_warning "No accessibility features found"
        return 0
    fi
}

# Test theme support
test_theme_support() {
    print_status "Checking theme support..."
    
    if grep -r "useTheme\|ThemeProvider\|next-themes" apps/web/src/components/ > /dev/null 2>&1; then
        print_success "Theme support implemented"
        return 0
    else
        print_error "Theme support not found"
        return 1
    fi
}

# Test pages using layouts
test_page_layouts() {
    print_status "Checking page layouts..."
    
    local failed=0
    
    # Check if dashboard page uses DashboardLayout
    if [ -f "apps/web/src/app/dashboard/page.tsx" ]; then
        if grep -q "DashboardLayout" apps/web/src/app/dashboard/page.tsx; then
            print_success "Dashboard page uses DashboardLayout"
        else
            print_error "Dashboard page doesn't use DashboardLayout"
            failed=1
        fi
    else
        print_warning "Dashboard page not found"
    fi
    
    # Check if auth pages exist
    if [ -f "apps/web/src/app/auth/signin/page.tsx" ]; then
        print_success "Sign-in page exists"
    else
        print_error "Sign-in page not found"
        failed=1
    fi
    
    if [ -f "apps/web/src/app/auth/signup/page.tsx" ]; then
        print_success "Sign-up page exists"
    else
        print_error "Sign-up page not found"
        failed=1
    fi
    
    return $failed
}

# Test development server startup
test_dev_server() {
    print_status "Testing development server startup..."
    
    # Start the development server in background
    print_status "Starting development server..."
    npm run dev > /dev/null 2>&1 &
    DEV_PID=$!
    
    # Wait for server to start
    sleep 15
    
    local failed=0
    
    # Test if server is responding
    if curl -f http://localhost:3000 > /dev/null 2>&1; then
        print_success "Development server is responding"
    else
        print_error "Development server is not responding"
        failed=1
    fi
    
    # Test specific pages
    local pages=("/" "/auth/signin" "/auth/signup")
    
    for page in "${pages[@]}"; do
        if curl -f "http://localhost:3000$page" > /dev/null 2>&1; then
            print_success "Page $page is accessible"
        else
            print_error "Page $page is not accessible"
            failed=1
        fi
    done
    
    # Stop the development server
    kill $DEV_PID 2>/dev/null || true
    sleep 2
    
    return $failed
}

# Main verification function
main() {
    echo
    print_status "Starting UI components verification process..."
    echo
    
    local failed=0
    
    check_ui_components || failed=1
    check_providers || failed=1
    check_ui_dependencies || failed=1
    test_typescript || failed=1
    test_component_imports || failed=1
    test_responsive_design || failed=1
    test_accessibility || failed=1
    test_theme_support || failed=1
    test_page_layouts || failed=1
    test_ui_tests || failed=1
    test_dev_server || failed=1
    
    echo
    if [ $failed -eq 0 ]; then
        print_success "🎉 All UI component verification checks passed!"
        echo
        print_status "Your CareerCraft UI components are ready!"
        echo
        print_status "Available UI features:"
        echo "✅ Responsive layout system"
        echo "✅ Theme switching (light/dark)"
        echo "✅ Loading states and skeletons"
        echo "✅ Error boundaries"
        echo "✅ Accessible components"
        echo "✅ Dashboard and main layouts"
        echo "✅ Authentication pages"
        echo "✅ Professional design system"
        echo
        print_status "Test the UI components:"
        echo "1. Start development server: npm run dev"
        echo "2. Visit homepage: http://localhost:3000"
        echo "3. Test authentication: http://localhost:3000/auth/signin"
        echo "4. Test dashboard: http://localhost:3000/dashboard"
        echo
    else
        print_error "❌ Some UI component verification checks failed"
        echo
        print_status "Please fix the issues above and run the verification again"
        echo
        exit 1
    fi
}

# Run main function
main
