import { describe, it, expect, beforeAll, afterAll, beforeEach, jest } from '@jest/globals';
import { hashPassword, verifyPassword, createUser, loginSchema, signupSchema } from '../auth';
import { prisma, connectDB, disconnectDB, cleanupDatabase } from '@careercraft/database';
import bcrypt from 'bcryptjs';

// Mock bcrypt for consistent testing
jest.mock('bcryptjs');
const mockedBcrypt = bcrypt as jest.Mocked<typeof bcrypt>;

describe('Authentication Functions', () => {
  beforeAll(async () => {
    await connectDB();
  });

  afterAll(async () => {
    await cleanupDatabase();
    await disconnectDB();
  });

  beforeEach(async () => {
    await cleanupDatabase();
    jest.clearAllMocks();
  });

  describe('Password Hashing', () => {
    it('should hash password correctly', async () => {
      const password = 'testPassword123';
      const hashedPassword = 'hashedPassword123';
      
      mockedBcrypt.hash.mockResolvedValue(hashedPassword);

      const result = await hashPassword(password);

      expect(mockedBcrypt.hash).toHaveBeenCalledWith(password, 12);
      expect(result).toBe(hashedPassword);
    });

    it('should verify password correctly', async () => {
      const password = 'testPassword123';
      const hashedPassword = 'hashedPassword123';
      
      mockedBcrypt.compare.mockResolvedValue(true);

      const result = await verifyPassword(password, hashedPassword);

      expect(mockedBcrypt.compare).toHaveBeenCalledWith(password, hashedPassword);
      expect(result).toBe(true);
    });

    it('should return false for incorrect password', async () => {
      const password = 'wrongPassword';
      const hashedPassword = 'hashedPassword123';
      
      mockedBcrypt.compare.mockResolvedValue(false);

      const result = await verifyPassword(password, hashedPassword);

      expect(result).toBe(false);
    });
  });

  describe('Validation Schemas', () => {
    describe('loginSchema', () => {
      it('should validate correct login data', () => {
        const validData = {
          email: '<EMAIL>',
          password: 'password123',
        };

        const result = loginSchema.safeParse(validData);
        expect(result.success).toBe(true);
      });

      it('should reject invalid email', () => {
        const invalidData = {
          email: 'invalid-email',
          password: 'password123',
        };

        const result = loginSchema.safeParse(invalidData);
        expect(result.success).toBe(false);
        if (!result.success) {
          expect(result.error.errors[0].message).toBe('Invalid email address');
        }
      });

      it('should reject short password', () => {
        const invalidData = {
          email: '<EMAIL>',
          password: '123',
        };

        const result = loginSchema.safeParse(invalidData);
        expect(result.success).toBe(false);
        if (!result.success) {
          expect(result.error.errors[0].message).toBe('Password must be at least 8 characters');
        }
      });
    });

    describe('signupSchema', () => {
      it('should validate correct signup data', () => {
        const validData = {
          email: '<EMAIL>',
          password: 'Password123',
          name: 'Test User',
        };

        const result = signupSchema.safeParse(validData);
        expect(result.success).toBe(true);
      });

      it('should reject invalid email', () => {
        const invalidData = {
          email: 'invalid-email',
          password: 'Password123',
          name: 'Test User',
        };

        const result = signupSchema.safeParse(invalidData);
        expect(result.success).toBe(false);
      });

      it('should reject short name', () => {
        const invalidData = {
          email: '<EMAIL>',
          password: 'Password123',
          name: 'T',
        };

        const result = signupSchema.safeParse(invalidData);
        expect(result.success).toBe(false);
        if (!result.success) {
          expect(result.error.errors[0].message).toBe('Name must be at least 2 characters');
        }
      });

      it('should reject short password', () => {
        const invalidData = {
          email: '<EMAIL>',
          password: '123',
          name: 'Test User',
        };

        const result = signupSchema.safeParse(invalidData);
        expect(result.success).toBe(false);
      });
    });
  });

  describe('User Creation', () => {
    beforeEach(() => {
      // Mock bcrypt for user creation tests
      mockedBcrypt.hash.mockResolvedValue('hashedPassword123');
    });

    it('should create user successfully', async () => {
      const userData = {
        email: '<EMAIL>',
        password: 'Password123',
        name: 'Test User',
      };

      const user = await createUser(userData);

      expect(user.email).toBe(userData.email);
      expect(user.name).toBe(userData.name);
      expect(user.id).toBeDefined();

      // Verify user was created in database
      const dbUser = await prisma.user.findUnique({
        where: { id: user.id },
        include: {
          accounts: true,
          profiles: true,
        },
      });

      expect(dbUser).toBeDefined();
      expect(dbUser?.email).toBe(userData.email);
      expect(dbUser?.accounts).toHaveLength(1);
      expect(dbUser?.accounts[0].provider).toBe('credentials');
      expect(dbUser?.accounts[0].refresh_token).toBe('hashedPassword123');
      expect(dbUser?.profiles).toHaveLength(1);
      expect(dbUser?.profiles[0].firstName).toBe('Test');
      expect(dbUser?.profiles[0].lastName).toBe('User');
    });

    it('should create user profile with correct name parsing', async () => {
      const userData = {
        email: '<EMAIL>',
        password: 'Password123',
        name: 'John Michael Doe',
      };

      const user = await createUser(userData);

      const profile = await prisma.userProfile.findUnique({
        where: { userId: user.id },
      });

      expect(profile?.firstName).toBe('John');
      expect(profile?.lastName).toBe('Michael Doe');
    });

    it('should handle single name correctly', async () => {
      const userData = {
        email: '<EMAIL>',
        password: 'Password123',
        name: 'Madonna',
      };

      const user = await createUser(userData);

      const profile = await prisma.userProfile.findUnique({
        where: { userId: user.id },
      });

      expect(profile?.firstName).toBe('Madonna');
      expect(profile?.lastName).toBe('');
    });

    it('should reject duplicate email', async () => {
      const userData = {
        email: '<EMAIL>',
        password: 'Password123',
        name: 'First User',
      };

      // Create first user
      await createUser(userData);

      // Try to create second user with same email
      const duplicateUserData = {
        email: '<EMAIL>',
        password: 'Password456',
        name: 'Second User',
      };

      await expect(createUser(duplicateUserData)).rejects.toThrow(
        'User with this email already exists'
      );
    });

    it('should validate input data', async () => {
      const invalidUserData = {
        email: 'invalid-email',
        password: '123',
        name: 'T',
      };

      await expect(createUser(invalidUserData)).rejects.toThrow();
    });

    it('should handle database transaction rollback on error', async () => {
      const userData = {
        email: '<EMAIL>',
        password: 'Password123',
        name: 'Transaction User',
      };

      // Mock bcrypt to throw an error during hashing
      mockedBcrypt.hash.mockRejectedValue(new Error('Hashing failed'));

      await expect(createUser(userData)).rejects.toThrow('Hashing failed');

      // Verify no user was created
      const user = await prisma.user.findUnique({
        where: { email: userData.email },
      });

      expect(user).toBeNull();
    });
  });

  describe('Database Integration', () => {
    it('should create all related records in transaction', async () => {
      const userData = {
        email: '<EMAIL>',
        password: 'Password123',
        name: 'Integration Test User',
      };

      mockedBcrypt.hash.mockResolvedValue('hashedPassword123');

      const user = await createUser(userData);

      // Verify user
      const dbUser = await prisma.user.findUnique({
        where: { id: user.id },
      });
      expect(dbUser).toBeDefined();

      // Verify account
      const account = await prisma.account.findFirst({
        where: { userId: user.id },
      });
      expect(account).toBeDefined();
      expect(account?.provider).toBe('credentials');
      expect(account?.type).toBe('credentials');
      expect(account?.refresh_token).toBe('hashedPassword123');

      // Verify profile
      const profile = await prisma.userProfile.findUnique({
        where: { userId: user.id },
      });
      expect(profile).toBeDefined();
      expect(profile?.firstName).toBe('Integration');
      expect(profile?.lastName).toBe('Test User');
    });

    it('should handle concurrent user creation', async () => {
      const userData1 = {
        email: '<EMAIL>',
        password: 'Password123',
        name: 'Concurrent User 1',
      };

      const userData2 = {
        email: '<EMAIL>',
        password: 'Password123',
        name: 'Concurrent User 2',
      };

      mockedBcrypt.hash.mockResolvedValue('hashedPassword123');

      // Create users concurrently
      const [user1, user2] = await Promise.all([
        createUser(userData1),
        createUser(userData2),
      ]);

      expect(user1.email).toBe(userData1.email);
      expect(user2.email).toBe(userData2.email);
      expect(user1.id).not.toBe(user2.id);

      // Verify both users exist in database
      const users = await prisma.user.findMany({
        where: {
          email: {
            in: [userData1.email, userData2.email],
          },
        },
      });

      expect(users).toHaveLength(2);
    });
  });

  describe('Error Handling', () => {
    it('should handle database connection errors', async () => {
      // Temporarily disconnect from database
      await prisma.$disconnect();

      const userData = {
        email: '<EMAIL>',
        password: 'Password123',
        name: 'Error User',
      };

      await expect(createUser(userData)).rejects.toThrow();

      // Reconnect for cleanup
      await connectDB();
    });

    it('should handle invalid input gracefully', async () => {
      const invalidInputs = [
        null,
        undefined,
        {},
        { email: '<EMAIL>' }, // missing password and name
        { password: 'Password123' }, // missing email and name
        { name: 'Test User' }, // missing email and password
      ];

      for (const input of invalidInputs) {
        await expect(createUser(input as any)).rejects.toThrow();
      }
    });
  });
});
