import { describe, it, expect } from '@jest/globals';
import { render, screen } from '@testing-library/react';
import {
  LoadingSpinner,
  LoadingPage,
  LoadingCard,
  LoadingButton,
  Skeleton,
  LoadingTable,
  LoadingList,
} from '../loading';

describe('Loading Components', () => {
  describe('LoadingSpinner', () => {
    it('should render with default size', () => {
      render(<LoadingSpinner />);
      
      const spinner = screen.getByRole('img', { hidden: true });
      expect(spinner).toBeInTheDocument();
      expect(spinner).toHaveClass('animate-spin', 'h-6', 'w-6');
    });

    it('should render with small size', () => {
      render(<LoadingSpinner size="sm" />);
      
      const spinner = screen.getByRole('img', { hidden: true });
      expect(spinner).toHaveClass('h-4', 'w-4');
    });

    it('should render with large size', () => {
      render(<LoadingSpinner size="lg" />);
      
      const spinner = screen.getByRole('img', { hidden: true });
      expect(spinner).toHaveClass('h-8', 'w-8');
    });

    it('should apply custom className', () => {
      render(<LoadingSpinner className="custom-class" />);
      
      const spinner = screen.getByRole('img', { hidden: true });
      expect(spinner).toHaveClass('custom-class');
    });
  });

  describe('LoadingPage', () => {
    it('should render with default message', () => {
      render(<LoadingPage />);
      
      expect(screen.getByText('Loading...')).toBeInTheDocument();
      expect(screen.getByRole('img', { hidden: true })).toBeInTheDocument();
    });

    it('should render with custom message', () => {
      render(<LoadingPage message="Loading your data..." />);
      
      expect(screen.getByText('Loading your data...')).toBeInTheDocument();
    });

    it('should apply custom className', () => {
      const { container } = render(<LoadingPage className="custom-page-class" />);
      
      expect(container.firstChild).toHaveClass('custom-page-class');
    });

    it('should have proper layout structure', () => {
      const { container } = render(<LoadingPage />);
      
      expect(container.firstChild).toHaveClass(
        'flex',
        'flex-col',
        'items-center',
        'justify-center',
        'min-h-[400px]',
        'space-y-4'
      );
    });
  });

  describe('LoadingCard', () => {
    it('should render skeleton content', () => {
      const { container } = render(<LoadingCard />);
      
      // Should have card structure
      expect(container.firstChild).toHaveClass('rounded-lg', 'border', 'bg-card', 'p-6');
      
      // Should have skeleton elements
      const skeletons = container.querySelectorAll('.animate-pulse');
      expect(skeletons.length).toBeGreaterThan(0);
    });

    it('should apply custom className', () => {
      const { container } = render(<LoadingCard className="custom-card-class" />);
      
      expect(container.firstChild).toHaveClass('custom-card-class');
    });
  });

  describe('LoadingButton', () => {
    it('should render children when not loading', () => {
      render(<LoadingButton isLoading={false}>Click me</LoadingButton>);
      
      expect(screen.getByText('Click me')).toBeInTheDocument();
      expect(screen.queryByRole('img', { hidden: true })).not.toBeInTheDocument();
    });

    it('should show spinner when loading', () => {
      render(<LoadingButton isLoading={true}>Click me</LoadingButton>);
      
      expect(screen.getByText('Click me')).toBeInTheDocument();
      expect(screen.getByRole('img', { hidden: true })).toBeInTheDocument();
    });

    it('should apply opacity to text when loading', () => {
      render(<LoadingButton isLoading={true}>Click me</LoadingButton>);
      
      const text = screen.getByText('Click me');
      expect(text).toHaveClass('opacity-70');
    });

    it('should apply custom className', () => {
      const { container } = render(
        <LoadingButton className="custom-button-class">Click me</LoadingButton>
      );
      
      expect(container.firstChild).toHaveClass('custom-button-class');
    });
  });

  describe('Skeleton', () => {
    it('should render with default classes', () => {
      const { container } = render(<Skeleton />);
      
      expect(container.firstChild).toHaveClass('animate-pulse', 'rounded-md', 'bg-muted');
    });

    it('should apply custom className', () => {
      const { container } = render(<Skeleton className="h-4 w-full" />);
      
      expect(container.firstChild).toHaveClass('h-4', 'w-full');
    });
  });

  describe('LoadingTable', () => {
    it('should render with default rows and columns', () => {
      const { container } = render(<LoadingTable />);
      
      // Should have skeleton elements for header + rows
      const skeletons = container.querySelectorAll('.animate-pulse');
      expect(skeletons.length).toBe(24); // 4 columns * 6 rows (1 header + 5 data rows)
    });

    it('should render with custom rows and columns', () => {
      const { container } = render(<LoadingTable rows={3} columns={2} />);
      
      const skeletons = container.querySelectorAll('.animate-pulse');
      expect(skeletons.length).toBe(8); // 2 columns * 4 rows (1 header + 3 data rows)
    });

    it('should apply custom className', () => {
      const { container } = render(<LoadingTable className="custom-table-class" />);
      
      expect(container.firstChild).toHaveClass('custom-table-class');
    });

    it('should have proper grid structure', () => {
      const { container } = render(<LoadingTable columns={3} />);
      
      const grids = container.querySelectorAll('[style*="grid-template-columns"]');
      grids.forEach(grid => {
        expect(grid).toHaveStyle('grid-template-columns: repeat(3, 1fr)');
      });
    });
  });

  describe('LoadingList', () => {
    it('should render with default number of items', () => {
      const { container } = render(<LoadingList />);
      
      // Should have 5 items by default, each with avatar and text skeletons
      const avatarSkeletons = container.querySelectorAll('.rounded-full');
      expect(avatarSkeletons.length).toBe(5);
    });

    it('should render with custom number of items', () => {
      const { container } = render(<LoadingList items={3} />);
      
      const avatarSkeletons = container.querySelectorAll('.rounded-full');
      expect(avatarSkeletons.length).toBe(3);
    });

    it('should apply custom className', () => {
      const { container } = render(<LoadingList className="custom-list-class" />);
      
      expect(container.firstChild).toHaveClass('custom-list-class');
    });

    it('should have proper list item structure', () => {
      const { container } = render(<LoadingList items={1} />);
      
      // Each item should have avatar and text content
      const listItem = container.querySelector('.flex.items-center.space-x-3');
      expect(listItem).toBeInTheDocument();
      
      const avatar = container.querySelector('.h-10.w-10.rounded-full');
      expect(avatar).toBeInTheDocument();
      
      const textContent = container.querySelector('.space-y-2.flex-1');
      expect(textContent).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('should have proper ARIA attributes for loading states', () => {
      render(<LoadingPage />);
      
      // Loading spinners should be hidden from screen readers
      const spinner = screen.getByRole('img', { hidden: true });
      expect(spinner).toBeInTheDocument();
    });

    it('should provide meaningful loading text', () => {
      render(<LoadingPage message="Loading your dashboard..." />);
      
      expect(screen.getByText('Loading your dashboard...')).toBeInTheDocument();
    });
  });

  describe('Animation Classes', () => {
    it('should apply animate-pulse to skeleton elements', () => {
      const { container } = render(<Skeleton />);
      
      expect(container.firstChild).toHaveClass('animate-pulse');
    });

    it('should apply animate-spin to spinner', () => {
      render(<LoadingSpinner />);
      
      const spinner = screen.getByRole('img', { hidden: true });
      expect(spinner).toHaveClass('animate-spin');
    });
  });

  describe('Performance', () => {
    it('should render large lists efficiently', () => {
      const startTime = performance.now();
      render(<LoadingList items={100} />);
      const endTime = performance.now();
      
      // Should render quickly (less than 100ms for 100 items)
      expect(endTime - startTime).toBeLessThan(100);
    });

    it('should render large tables efficiently', () => {
      const startTime = performance.now();
      render(<LoadingTable rows={50} columns={10} />);
      const endTime = performance.now();
      
      // Should render quickly
      expect(endTime - startTime).toBeLessThan(100);
    });
  });
});
