'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Textarea } from '@/components/ui/textarea'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { 
  Sparkles, 
  Wand2, 
  Target, 
  TrendingUp, 
  CheckCircle, 
  AlertCircle,
  Lightbulb,
  Zap,
  RefreshCw,
  Copy,
  ThumbsUp,
  ThumbsDown
} from 'lucide-react'

interface AIAssistantProps {
  resumeContent?: string
  onContentUpdate?: (content: string) => void
  onSuggestionApply?: (suggestion: string) => void
}

interface AISuggestion {
  id: string
  type: 'optimization' | 'keyword' | 'achievement' | 'grammar'
  title: string
  description: string
  originalText?: string
  suggestedText?: string
  impact: 'high' | 'medium' | 'low'
  applied?: boolean
}

interface ATSScore {
  overall: number
  keywords: number
  formatting: number
  content: number
  suggestions: string[]
}

export function AIAssistant({ resumeContent, onContentUpdate, onSuggestionApply }: AIAssistantProps) {
  const [isAnalyzing, setIsAnalyzing] = useState(false)
  const [isGenerating, setIsGenerating] = useState(false)
  const [suggestions, setSuggestions] = useState<AISuggestion[]>([])
  const [atsScore, setATSScore] = useState<ATSScore | null>(null)
  const [jobDescription, setJobDescription] = useState('')
  const [targetRole, setTargetRole] = useState('')
  const [generationType, setGenerationType] = useState<'summary' | 'experience' | 'skills' | 'achievement'>('summary')
  const [generationContext, setGenerationContext] = useState('')

  useEffect(() => {
    if (resumeContent) {
      analyzeResume()
    }
  }, [resumeContent])

  const analyzeResume = async () => {
    if (!resumeContent) return

    setIsAnalyzing(true)
    try {
      // Simulate AI analysis - replace with actual API call
      await new Promise(resolve => setTimeout(resolve, 2000))

      // Mock ATS score
      const mockScore: ATSScore = {
        overall: 78,
        keywords: 72,
        formatting: 85,
        content: 76,
        suggestions: [
          'Add more industry-specific keywords',
          'Quantify achievements with numbers',
          'Use stronger action verbs',
          'Include relevant certifications'
        ]
      }
      setATSScore(mockScore)

      // Mock suggestions
      const mockSuggestions: AISuggestion[] = [
        {
          id: '1',
          type: 'keyword',
          title: 'Missing Key Skills',
          description: 'Add React, TypeScript, and Node.js to match job requirements',
          impact: 'high',
          suggestedText: 'React, TypeScript, Node.js, GraphQL, AWS'
        },
        {
          id: '2',
          type: 'achievement',
          title: 'Quantify Impact',
          description: 'Add specific metrics to your achievements',
          originalText: 'Improved application performance',
          suggestedText: 'Improved application performance by 40%, reducing load times from 3.2s to 1.9s',
          impact: 'high'
        },
        {
          id: '3',
          type: 'optimization',
          title: 'Stronger Action Verbs',
          description: 'Replace weak verbs with more impactful alternatives',
          originalText: 'Worked on developing features',
          suggestedText: 'Architected and implemented scalable features',
          impact: 'medium'
        }
      ]
      setSuggestions(mockSuggestions)

    } catch (error) {
      console.error('Error analyzing resume:', error)
    } finally {
      setIsAnalyzing(false)
    }
  }

  // Alias for ATS analysis
  const analyzeATS = analyzeResume

  const generateContent = async () => {
    if (!generationContext) return

    setIsGenerating(true)
    try {
      // Simulate AI content generation - replace with actual API call
      await new Promise(resolve => setTimeout(resolve, 1500))

      let generatedContent = ''
      switch (generationType) {
        case 'summary':
          generatedContent = 'Experienced software engineer with 5+ years developing scalable web applications using React, Node.js, and cloud technologies. Proven track record of improving system performance by 40% and leading cross-functional teams to deliver high-impact products.'
          break
        case 'experience':
          generatedContent = '• Architected and implemented microservices architecture, reducing system downtime by 60%\n• Led development of customer-facing dashboard, increasing user engagement by 35%\n• Mentored 3 junior developers and established code review processes'
          break
        case 'skills':
          generatedContent = 'Technical: React, TypeScript, Node.js, Python, AWS, Docker, Kubernetes\nSoft Skills: Leadership, Problem-solving, Communication, Project Management'
          break
        case 'achievement':
          generatedContent = 'Optimized database queries and implemented caching strategies, resulting in 50% faster page load times and improved user satisfaction scores from 3.2 to 4.6/5'
          break
      }

      if (onContentUpdate) {
        onContentUpdate(generatedContent)
      }

    } catch (error) {
      console.error('Error generating content:', error)
    } finally {
      setIsGenerating(false)
    }
  }

  const applySuggestion = (suggestion: AISuggestion) => {
    if (suggestion.suggestedText && onSuggestionApply) {
      onSuggestionApply(suggestion.suggestedText)
    }
    
    // Mark suggestion as applied
    setSuggestions(prev => 
      prev.map(s => s.id === suggestion.id ? { ...s, applied: true } : s)
    )
  }

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600'
    if (score >= 60) return 'text-yellow-600'
    return 'text-red-600'
  }

  const getImpactColor = (impact: AISuggestion['impact']) => {
    switch (impact) {
      case 'high': return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
      case 'medium': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400'
      case 'low': return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400'
    }
  }

  return (
    <div className="space-y-6">
      {/* AI Assistant Header */}
      <Card className="glass-card">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Sparkles className="w-5 h-5 text-purple-600" />
            <span>AI Resume Assistant</span>
          </CardTitle>
          <CardDescription>
            Get AI-powered suggestions to optimize your resume for better ATS scores and impact
          </CardDescription>
        </CardHeader>
      </Card>

      {/* ATS Score Dashboard */}
      {atsScore && (
        <Card className="glass-card">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Target className="w-5 h-5 text-blue-600" />
              <span>ATS Compatibility Score</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
              <div className="text-center">
                <div className={`text-3xl font-bold ${getScoreColor(atsScore.overall)}`}>
                  {atsScore.overall}%
                </div>
                <div className="text-sm text-muted-foreground">Overall</div>
              </div>
              <div className="text-center">
                <div className={`text-3xl font-bold ${getScoreColor(atsScore.keywords)}`}>
                  {atsScore.keywords}%
                </div>
                <div className="text-sm text-muted-foreground">Keywords</div>
              </div>
              <div className="text-center">
                <div className={`text-3xl font-bold ${getScoreColor(atsScore.formatting)}`}>
                  {atsScore.formatting}%
                </div>
                <div className="text-sm text-muted-foreground">Formatting</div>
              </div>
              <div className="text-center">
                <div className={`text-3xl font-bold ${getScoreColor(atsScore.content)}`}>
                  {atsScore.content}%
                </div>
                <div className="text-sm text-muted-foreground">Content</div>
              </div>
            </div>

            <div className="space-y-2">
              <h4 className="font-medium">Quick Improvements:</h4>
              {atsScore.suggestions.map((suggestion, index) => (
                <div key={index} className="flex items-center space-x-2 text-sm">
                  <Lightbulb className="w-4 h-4 text-yellow-500" />
                  <span>{suggestion}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Job Description Input */}
      <Card className="glass-card">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <TrendingUp className="w-5 h-5 text-green-600" />
            <span>Job-Specific Optimization</span>
          </CardTitle>
          <CardDescription>
            Paste a job description to get targeted optimization suggestions
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="target-role">Target Role</Label>
            <Input
              id="target-role"
              placeholder="e.g., Senior Software Engineer"
              value={targetRole}
              onChange={(e) => setTargetRole(e.target.value)}
              className="glass-input"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="job-description">Job Description</Label>
            <Textarea
              id="job-description"
              placeholder="Paste the job description here..."
              value={jobDescription}
              onChange={(e) => setJobDescription(e.target.value)}
              className="glass-input min-h-[120px]"
            />
          </div>
          <Button 
            onClick={analyzeResume}
            disabled={isAnalyzing || !jobDescription}
            className="w-full glass-card bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white border-0"
          >
            {isAnalyzing ? (
              <>
                <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                Analyzing...
              </>
            ) : (
              <>
                <Zap className="w-4 h-4 mr-2" />
                Analyze & Optimize
              </>
            )}
          </Button>
        </CardContent>
      </Card>

      {/* AI Suggestions */}
      {suggestions.length > 0 && (
        <Card className="glass-card">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Wand2 className="w-5 h-5 text-purple-600" />
              <span>AI Suggestions</span>
            </CardTitle>
            <CardDescription>
              Apply these AI-generated improvements to enhance your resume
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {suggestions.map((suggestion) => (
                <div key={suggestion.id} className="glass-input p-4 rounded-lg">
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex items-center space-x-2">
                      <h4 className="font-medium">{suggestion.title}</h4>
                      <Badge className={getImpactColor(suggestion.impact)}>
                        {suggestion.impact} impact
                      </Badge>
                      {suggestion.applied && (
                        <Badge className="bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400">
                          <CheckCircle className="w-3 h-3 mr-1" />
                          Applied
                        </Badge>
                      )}
                    </div>
                  </div>
                  
                  <p className="text-sm text-muted-foreground mb-3">
                    {suggestion.description}
                  </p>

                  {suggestion.originalText && (
                    <div className="space-y-2 mb-3">
                      <div className="text-xs font-medium text-red-600">Original:</div>
                      <div className="text-sm bg-red-50 dark:bg-red-900/10 p-2 rounded border-l-2 border-red-200">
                        {suggestion.originalText}
                      </div>
                    </div>
                  )}

                  {suggestion.suggestedText && (
                    <div className="space-y-2 mb-3">
                      <div className="text-xs font-medium text-green-600">Suggested:</div>
                      <div className="text-sm bg-green-50 dark:bg-green-900/10 p-2 rounded border-l-2 border-green-200">
                        {suggestion.suggestedText}
                      </div>
                    </div>
                  )}

                  <div className="flex items-center space-x-2">
                    <Button
                      size="sm"
                      onClick={() => applySuggestion(suggestion)}
                      disabled={suggestion.applied}
                      className="glass-card bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white border-0"
                    >
                      {suggestion.applied ? (
                        <>
                          <CheckCircle className="w-3 h-3 mr-1" />
                          Applied
                        </>
                      ) : (
                        <>
                          <CheckCircle className="w-3 h-3 mr-1" />
                          Apply
                        </>
                      )}
                    </Button>
                    
                    {suggestion.suggestedText && (
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => navigator.clipboard.writeText(suggestion.suggestedText!)}
                        className="glass-input"
                      >
                        <Copy className="w-3 h-3 mr-1" />
                        Copy
                      </Button>
                    )}

                    <div className="flex items-center space-x-1 ml-auto">
                      <Button size="sm" variant="ghost" className="p-1">
                        <ThumbsUp className="w-3 h-3" />
                      </Button>
                      <Button size="sm" variant="ghost" className="p-1">
                        <ThumbsDown className="w-3 h-3" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Content Generation */}
      <Card className="glass-card">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Sparkles className="w-5 h-5 text-blue-600" />
            <span>AI Content Generator</span>
          </CardTitle>
          <CardDescription>
            Generate professional content for different resume sections
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="generation-type">Content Type</Label>
              <Select value={generationType} onValueChange={(value: any) => setGenerationType(value)}>
                <SelectTrigger className="glass-input">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="glass-card">
                  <SelectItem value="summary">Professional Summary</SelectItem>
                  <SelectItem value="experience">Work Experience</SelectItem>
                  <SelectItem value="skills">Skills Section</SelectItem>
                  <SelectItem value="achievement">Achievement Statement</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="generation-context">Context & Details</Label>
            <Textarea
              id="generation-context"
              placeholder="Provide context for content generation (role, company, achievements, etc.)"
              value={generationContext}
              onChange={(e) => setGenerationContext(e.target.value)}
              className="glass-input"
              rows={3}
            />
          </div>

          <Button 
            onClick={generateContent}
            disabled={isGenerating || !generationContext}
            className="w-full glass-card bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white border-0"
          >
            {isGenerating ? (
              <>
                <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                Generating...
              </>
            ) : (
              <>
                <Sparkles className="w-4 h-4 mr-2" />
                Generate Content
              </>
            )}
          </Button>
        </CardContent>
      </Card>
    </div>
  )
}
