'use client'

import { useState, useEffect } from 'react'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import { 
  Users, 
  Circle, 
  Clock, 
  Eye,
  MessageCircle,
  Edit,
  Crown,
  Wifi,
  WifiOff
} from 'lucide-react'
import { useCollaborationStore } from '@/lib/collaboration/store'
import { useCollaboration } from './CollaborationProvider'
import { formatDistanceToNow } from 'date-fns'

interface UserPresenceProps {
  className?: string
  showDetails?: boolean
}

export function UserPresence({ className, showDetails = true }: UserPresenceProps) {
  const { isConnected, isConnecting } = useCollaboration()
  const { activeUsers, userPermissions } = useCollaborationStore()
  const [isOpen, setIsOpen] = useState(false)

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'text-green-500'
      case 'idle': return 'text-yellow-500'
      case 'away': return 'text-gray-500'
      default: return 'text-gray-400'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return <Circle className="w-2 h-2 fill-current" />
      case 'idle': return <Clock className="w-2 h-2" />
      case 'away': return <Circle className="w-2 h-2 fill-current opacity-50" />
      default: return <Circle className="w-2 h-2" />
    }
  }

  const getPermissionIcon = (permissionLevel: string) => {
    switch (permissionLevel) {
      case 'admin': return <Crown className="w-3 h-3 text-yellow-500" />
      case 'edit': return <Edit className="w-3 h-3 text-blue-500" />
      case 'comment': return <MessageCircle className="w-3 h-3 text-green-500" />
      case 'view': return <Eye className="w-3 h-3 text-gray-500" />
      default: return null
    }
  }

  const getUserPermission = (userId: string) => {
    return userPermissions.find(p => p.userId === userId)?.permissionLevel || 'view'
  }

  if (!isConnected && !isConnecting) {
    return null
  }

  const connectionStatus = isConnecting ? 'Connecting...' : isConnected ? 'Connected' : 'Disconnected'
  const ConnectionIcon = isConnected ? Wifi : WifiOff

  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      {/* Connection Status */}
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <div className="flex items-center space-x-1">
              <ConnectionIcon className={`w-4 h-4 ${isConnected ? 'text-green-500' : 'text-red-500'}`} />
              {showDetails && (
                <span className={`text-xs ${isConnected ? 'text-green-600' : 'text-red-600'}`}>
                  {connectionStatus}
                </span>
              )}
            </div>
          </TooltipTrigger>
          <TooltipContent>
            <p>Collaboration {connectionStatus.toLowerCase()}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>

      {/* Active Users */}
      {activeUsers.length > 0 && (
        <Popover open={isOpen} onOpenChange={setIsOpen}>
          <PopoverTrigger asChild>
            <Button variant="ghost" size="sm" className="h-8 px-2">
              <div className="flex items-center space-x-1">
                <Users className="w-4 h-4" />
                <span className="text-sm font-medium">{activeUsers.length}</span>
                
                {/* User Avatars */}
                <div className="flex -space-x-1">
                  {activeUsers.slice(0, 3).map((user) => (
                    <TooltipProvider key={user.userId}>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <div className="relative">
                            <Avatar className="w-6 h-6 border-2 border-background">
                              <AvatarImage src={user.userAvatar} alt={user.userName} />
                              <AvatarFallback className="text-xs">
                                {user.userName.charAt(0).toUpperCase()}
                              </AvatarFallback>
                            </Avatar>
                            <div className={`absolute -bottom-0.5 -right-0.5 w-2 h-2 rounded-full border border-background ${getStatusColor(user.status)}`}>
                              {getStatusIcon(user.status)}
                            </div>
                          </div>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>{user.userName} ({user.status})</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  ))}
                  
                  {activeUsers.length > 3 && (
                    <div className="w-6 h-6 rounded-full bg-muted border-2 border-background flex items-center justify-center">
                      <span className="text-xs font-medium">+{activeUsers.length - 3}</span>
                    </div>
                  )}
                </div>
              </div>
            </Button>
          </PopoverTrigger>
          
          <PopoverContent className="w-80 p-0" align="end">
            <Card className="border-0 shadow-none">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm flex items-center space-x-2">
                  <Users className="w-4 h-4" />
                  <span>Active Collaborators ({activeUsers.length})</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {activeUsers.map((user) => {
                  const permission = getUserPermission(user.userId)
                  const lastSeenText = formatDistanceToNow(new Date(user.lastSeen), { addSuffix: true })
                  
                  return (
                    <div key={user.userId} className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="relative">
                          <Avatar className="w-8 h-8">
                            <AvatarImage src={user.userAvatar} alt={user.userName} />
                            <AvatarFallback>
                              {user.userName.charAt(0).toUpperCase()}
                            </AvatarFallback>
                          </Avatar>
                          <div className={`absolute -bottom-0.5 -right-0.5 w-3 h-3 rounded-full border-2 border-background ${getStatusColor(user.status)}`}>
                            {getStatusIcon(user.status)}
                          </div>
                        </div>
                        
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center space-x-2">
                            <p className="text-sm font-medium truncate">{user.userName}</p>
                            {getPermissionIcon(permission)}
                          </div>
                          <div className="flex items-center space-x-2">
                            <Badge variant="secondary" className="text-xs capitalize">
                              {user.status}
                            </Badge>
                            <span className="text-xs text-muted-foreground">
                              {lastSeenText}
                            </span>
                          </div>
                          
                          {user.cursor && (
                            <p className="text-xs text-muted-foreground truncate">
                              Editing: {user.cursor.sectionPath}
                            </p>
                          )}
                        </div>
                      </div>
                    </div>
                  )
                })}
                
                {activeUsers.length === 0 && (
                  <div className="text-center py-4">
                    <Users className="w-8 h-8 mx-auto text-muted-foreground mb-2" />
                    <p className="text-sm text-muted-foreground">No active collaborators</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </PopoverContent>
        </Popover>
      )}
    </div>
  )
}

// Compact version for toolbar
export function UserPresenceCompact({ className }: { className?: string }) {
  return <UserPresence className={className} showDetails={false} />
}

// Individual user cursor indicator
export function UserCursor({ 
  userId, 
  userName, 
  userAvatar, 
  position 
}: { 
  userId: string
  userName: string
  userAvatar?: string
  position: { x: number; y: number }
}) {
  const colors = [
    'bg-blue-500',
    'bg-green-500', 
    'bg-purple-500',
    'bg-orange-500',
    'bg-pink-500',
    'bg-indigo-500'
  ]
  
  const colorIndex = userId.charCodeAt(0) % colors.length
  const cursorColor = colors[colorIndex]

  return (
    <div 
      className="absolute pointer-events-none z-50"
      style={{ 
        left: position.x, 
        top: position.y,
        transform: 'translate(-2px, -2px)'
      }}
    >
      {/* Cursor */}
      <div className={`w-0.5 h-5 ${cursorColor}`} />
      
      {/* User label */}
      <div className={`mt-1 px-2 py-1 rounded text-xs text-white ${cursorColor} whitespace-nowrap`}>
        <div className="flex items-center space-x-1">
          {userAvatar && (
            <Avatar className="w-3 h-3">
              <AvatarImage src={userAvatar} alt={userName} />
              <AvatarFallback className="text-xs">
                {userName.charAt(0).toUpperCase()}
              </AvatarFallback>
            </Avatar>
          )}
          <span>{userName}</span>
        </div>
      </div>
    </div>
  )
}
