/**
 * Collaboration Features Validation Script
 * 
 * Validates real-time collaboration implementation
 */

const fs = require('fs')
const path = require('path')

class CollaborationFeaturesValidator {
  constructor() {
    this.results = {
      files: { checked: 0, missing: 0, present: 0 },
      services: { checked: 0, missing: 0, present: 0 },
      apis: { checked: 0, missing: 0, present: 0 },
      components: { checked: 0, missing: 0, present: 0 },
      tests: { checked: 0, missing: 0, present: 0 },
      config: { checked: 0, missing: 0, present: 0 }
    }
  }

  log(message, type = 'info') {
    const colors = {
      info: '\x1b[36m',    // Cyan
      success: '\x1b[32m', // Green
      error: '\x1b[31m',   // Red
      warning: '\x1b[33m', // Yellow
      reset: '\x1b[0m'     // Reset
    }
    
    console.log(`${colors[type]}${message}${colors.reset}`)
  }

  checkFileExists(filePath, description) {
    const fullPath = path.join(__dirname, filePath)
    const exists = fs.existsSync(fullPath)
    
    if (exists) {
      this.log(`✅ ${description}: Found`, 'success')
      return true
    } else {
      this.log(`❌ ${description}: Missing (${filePath})`, 'error')
      return false
    }
  }

  checkFileContent(filePath, searchTerms, description) {
    const fullPath = path.join(__dirname, filePath)
    
    if (!fs.existsSync(fullPath)) {
      this.log(`❌ ${description}: File not found`, 'error')
      return false
    }

    try {
      const content = fs.readFileSync(fullPath, 'utf8')
      const missingTerms = searchTerms.filter(term => !content.includes(term))
      
      if (missingTerms.length === 0) {
        this.log(`✅ ${description}: All required content found`, 'success')
        return true
      } else {
        this.log(`⚠️  ${description}: Missing content: ${missingTerms.join(', ')}`, 'warning')
        return false
      }
    } catch (error) {
      this.log(`❌ ${description}: Error reading file - ${error.message}`, 'error')
      return false
    }
  }

  validateFileStructure() {
    this.log('\n🤝 Validating Collaboration File Structure...', 'info')
    
    const requiredFiles = [
      'src/lib/collaboration/websocket-server.ts',
      'src/lib/collaboration/websocket-client.ts',
      'src/lib/collaboration/operational-transform.ts',
      'src/lib/collaboration/service.ts',
      'src/lib/collaboration/store.ts',
      'src/app/api/collaboration/session/route.ts',
      'src/app/api/collaboration/comments/route.ts',
      'src/components/collaboration/CollaborationProvider.tsx',
      'src/components/collaboration/UserPresence.tsx',
      'src/test/collaboration/websocket-server.test.ts',
      'src/test/collaboration/operational-transform.test.ts',
      'src/test/collaboration/collaboration-service.test.ts',
      'src/test/collaboration/collaboration-api.test.ts',
      'src/test/collaboration/collaboration-components.test.tsx'
    ]

    let present = 0
    let total = requiredFiles.length

    requiredFiles.forEach(file => {
      this.results.files.checked++
      if (this.checkFileExists(file, `File: ${file}`)) {
        present++
        this.results.files.present++
      } else {
        this.results.files.missing++
      }
    })

    this.log(`📊 File Structure: ${present}/${total} files present`, present === total ? 'success' : 'warning')
    return present === total
  }

  validateCollaborationServices() {
    this.log('\n🔧 Validating Collaboration Services...', 'info')
    
    const serviceChecks = [
      {
        file: 'src/lib/collaboration/websocket-server.ts',
        description: 'WebSocket Server Implementation',
        requiredContent: ['CollaborationWebSocketServer', 'handleConnection', 'handleMessage', 'broadcastToSession']
      },
      {
        file: 'src/lib/collaboration/websocket-client.ts',
        description: 'WebSocket Client Implementation',
        requiredContent: ['CollaborationWebSocketClient', 'connect', 'sendChange', 'sendPresence']
      },
      {
        file: 'src/lib/collaboration/operational-transform.ts',
        description: 'Operational Transform Implementation',
        requiredContent: ['OperationalTransform', 'transform', 'apply', 'compose']
      },
      {
        file: 'src/lib/collaboration/service.ts',
        description: 'Collaboration Service Implementation',
        requiredContent: ['CollaborationService', 'createSession', 'inviteUser', 'createComment']
      },
      {
        file: 'src/lib/collaboration/store.ts',
        description: 'Collaboration State Management',
        requiredContent: ['useCollaborationStore', 'CollaborationState', 'sendChange', 'updateUserPresence']
      }
    ]

    let passed = 0
    let total = serviceChecks.length

    serviceChecks.forEach(check => {
      this.results.services.checked++
      if (this.checkFileContent(check.file, check.requiredContent, check.description)) {
        passed++
        this.results.services.present++
      } else {
        this.results.services.missing++
      }
    })

    this.log(`📊 Collaboration Services: ${passed}/${total} checks passed`, passed === total ? 'success' : 'warning')
    return passed === total
  }

  validateCollaborationAPIs() {
    this.log('\n🌐 Validating Collaboration API Routes...', 'info')
    
    const apiChecks = [
      {
        file: 'src/app/api/collaboration/session/route.ts',
        description: 'Collaboration Session API',
        requiredContent: ['GET', 'POST', 'PUT', 'DELETE', 'createSession', 'inviteUser']
      },
      {
        file: 'src/app/api/collaboration/comments/route.ts',
        description: 'Collaboration Comments API',
        requiredContent: ['GET', 'POST', 'PUT', 'createComment', 'getComments', 'resolveComment']
      }
    ]

    let passed = 0
    let total = apiChecks.length

    apiChecks.forEach(check => {
      this.results.apis.checked++
      if (this.checkFileContent(check.file, check.requiredContent, check.description)) {
        passed++
        this.results.apis.present++
      } else {
        this.results.apis.missing++
      }
    })

    this.log(`📊 Collaboration APIs: ${passed}/${total} checks passed`, passed === total ? 'success' : 'warning')
    return passed === total
  }

  validateCollaborationComponents() {
    this.log('\n⚛️  Validating Collaboration Components...', 'info')
    
    const componentChecks = [
      {
        file: 'src/components/collaboration/CollaborationProvider.tsx',
        description: 'Collaboration Provider Component',
        requiredContent: ['CollaborationProvider', 'useCollaboration', 'startCollaboration', 'joinCollaboration']
      },
      {
        file: 'src/components/collaboration/UserPresence.tsx',
        description: 'User Presence Component',
        requiredContent: ['UserPresence', 'activeUsers', 'getStatusColor', 'getPermissionIcon']
      }
    ]

    let passed = 0
    let total = componentChecks.length

    componentChecks.forEach(check => {
      this.results.components.checked++
      if (this.checkFileContent(check.file, check.requiredContent, check.description)) {
        passed++
        this.results.components.present++
      } else {
        this.results.components.missing++
      }
    })

    this.log(`📊 Collaboration Components: ${passed}/${total} checks passed`, passed === total ? 'success' : 'warning')
    return passed === total
  }

  validateCollaborationTests() {
    this.log('\n🧪 Validating Collaboration Test Files...', 'info')
    
    const testChecks = [
      {
        file: 'src/test/collaboration/websocket-server.test.ts',
        description: 'WebSocket Server Tests',
        requiredContent: ['describe', 'it', 'expect', 'CollaborationWebSocketServer', 'handleMessage']
      },
      {
        file: 'src/test/collaboration/operational-transform.test.ts',
        description: 'Operational Transform Tests',
        requiredContent: ['describe', 'it', 'expect', 'OperationalTransform', 'transform']
      },
      {
        file: 'src/test/collaboration/collaboration-service.test.ts',
        description: 'Collaboration Service Tests',
        requiredContent: ['describe', 'it', 'expect', 'CollaborationService', 'createSession']
      },
      {
        file: 'src/test/collaboration/collaboration-api.test.ts',
        description: 'Collaboration API Tests',
        requiredContent: ['describe', 'it', 'expect', 'NextRequest', 'collaboration/session']
      },
      {
        file: 'src/test/collaboration/collaboration-components.test.tsx',
        description: 'Collaboration Component Tests',
        requiredContent: ['describe', 'it', 'expect', 'render', 'CollaborationProvider']
      }
    ]

    let passed = 0
    let total = testChecks.length

    testChecks.forEach(check => {
      this.results.tests.checked++
      if (this.checkFileContent(check.file, check.requiredContent, check.description)) {
        passed++
        this.results.tests.present++
      } else {
        this.results.tests.missing++
      }
    })

    this.log(`📊 Collaboration Tests: ${passed}/${total} checks passed`, passed === total ? 'success' : 'warning')
    return passed === total
  }

  validateConfiguration() {
    this.log('\n⚙️  Validating Collaboration Configuration...', 'info')
    
    const configChecks = [
      {
        file: 'package.json',
        description: 'Package.json Collaboration Scripts',
        requiredContent: ['test:collaboration', 'test:collaboration:websocket', 'test:collaboration:service']
      },
      {
        file: 'packages/database/prisma/schema.prisma',
        description: 'Database Schema',
        requiredContent: ['CollaborationSession', 'CollaborationComment', 'CollaborationPresence', 'CollaborationCursor']
      }
    ]

    let passed = 0
    let total = configChecks.length

    configChecks.forEach(check => {
      this.results.config.checked++
      if (this.checkFileContent(check.file, check.requiredContent, check.description)) {
        passed++
        this.results.config.present++
      } else {
        this.results.config.missing++
      }
    })

    this.log(`📊 Collaboration Configuration: ${passed}/${total} checks passed`, passed === total ? 'success' : 'warning')
    return passed === total
  }

  generateReport() {
    this.log('\n📊 Collaboration Features Validation Report', 'info')
    this.log('=' .repeat(60), 'info')
    
    const categories = ['files', 'services', 'apis', 'components', 'tests', 'config']
    let totalChecked = 0
    let totalPresent = 0
    let totalMissing = 0

    categories.forEach(category => {
      const result = this.results[category]
      totalChecked += result.checked
      totalPresent += result.present
      totalMissing += result.missing

      const status = result.missing === 0 ? '✅' : '⚠️ '
      const categoryName = category.charAt(0).toUpperCase() + category.slice(1)
      
      this.log(
        `${status} ${categoryName}: ${result.present}/${result.checked} present`,
        result.missing === 0 ? 'success' : 'warning'
      )
    })

    this.log('=' .repeat(60), 'info')
    this.log(`📈 Overall: ${totalPresent}/${totalChecked} checks passed`, 
             totalMissing === 0 ? 'success' : 'warning')
    
    const completionRate = totalChecked > 0 ? (totalPresent / totalChecked * 100).toFixed(1) : 0
    this.log(`📊 Completion Rate: ${completionRate}%`, 
             completionRate >= 90 ? 'success' : completionRate >= 70 ? 'warning' : 'error')

    // Recommendations
    this.log('\n💡 Collaboration Features Status:', 'info')
    if (totalMissing === 0) {
      this.log('🎉 All collaboration features are properly implemented!', 'success')
      this.log('✨ Ready for real-time collaborative editing', 'success')
    } else {
      this.log(`🔧 ${totalMissing} items need attention`, 'warning')
      if (this.results.files.missing > 0) {
        this.log('📁 Create missing files and implement required functionality', 'warning')
      }
      if (this.results.tests.missing > 0) {
        this.log('🧪 Add comprehensive tests for collaboration features', 'warning')
      }
      if (this.results.config.missing > 0) {
        this.log('⚙️  Update configuration files for collaboration', 'warning')
      }
    }

    return totalMissing === 0
  }

  async validate() {
    this.log('🚀 Starting Collaboration Features Validation...', 'info')
    this.log('📅 ' + new Date().toISOString(), 'info')
    
    const results = [
      this.validateFileStructure(),
      this.validateCollaborationServices(),
      this.validateCollaborationAPIs(),
      this.validateCollaborationComponents(),
      this.validateCollaborationTests(),
      this.validateConfiguration()
    ]

    const allValid = this.generateReport()
    
    this.log('\n🏁 Collaboration Validation Complete!', 'info')
    return allValid
  }
}

// Run validation if this script is executed directly
if (require.main === module) {
  const validator = new CollaborationFeaturesValidator()
  validator.validate()
    .then(success => {
      process.exit(success ? 0 : 1)
    })
    .catch(error => {
      console.error('Validation failed:', error)
      process.exit(1)
    })
}

module.exports = CollaborationFeaturesValidator
