import { describe, it, expect, vi, beforeEach } from 'vitest'
import { screen, fireEvent, waitFor } from '@testing-library/react'
import { LoginForm } from '../LoginForm'
import { render, createMockApiResponse } from '@/test/utils'

// Mock next-auth/react
const mockSignIn = vi.fn()
const mockGetSession = vi.fn()

vi.mock('next-auth/react', () => ({
  signIn: mockSignIn,
  getSession: mockGetSession,
}))

// Mock next/navigation
const mockPush = vi.fn()
const mockRefresh = vi.fn()

vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: mockPush,
    refresh: mockRefresh,
  }),
}))

describe('LoginForm', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    mockSignIn.mockResolvedValue({ ok: true })
    mockGetSession.mockResolvedValue({})
  })

  it('renders login form with all fields', () => {
    render(<LoginForm />)

    expect(screen.getByText('Welcome to CareerCraft')).toBeInTheDocument()
    expect(screen.getByText('Sign in to your account to continue')).toBeInTheDocument()
    
    expect(screen.getByLabelText('Email')).toBeInTheDocument()
    expect(screen.getByLabelText('Password')).toBeInTheDocument()
    
    expect(screen.getByRole('button', { name: /sign in/i })).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /google/i })).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /github/i })).toBeInTheDocument()
  })

  it('handles form submission with valid credentials', async () => {
    render(<LoginForm />)

    const emailInput = screen.getByLabelText('Email')
    const passwordInput = screen.getByLabelText('Password')
    const submitButton = screen.getByRole('button', { name: /sign in/i })

    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } })
    fireEvent.change(passwordInput, { target: { value: 'password123' } })
    fireEvent.click(submitButton)

    await waitFor(() => {
      expect(mockSignIn).toHaveBeenCalledWith('credentials', {
        email: '<EMAIL>',
        password: 'password123',
        redirect: false,
      })
    })

    await waitFor(() => {
      expect(mockGetSession).toHaveBeenCalled()
      expect(mockPush).toHaveBeenCalledWith('/dashboard')
      expect(mockRefresh).toHaveBeenCalled()
    })
  })

  it('displays error message for invalid credentials', async () => {
    mockSignIn.mockResolvedValue({ error: 'CredentialsSignin' })

    render(<LoginForm />)

    const emailInput = screen.getByLabelText('Email')
    const passwordInput = screen.getByLabelText('Password')
    const submitButton = screen.getByRole('button', { name: /sign in/i })

    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } })
    fireEvent.change(passwordInput, { target: { value: 'wrongpassword' } })
    fireEvent.click(submitButton)

    await waitFor(() => {
      expect(screen.getByText('Invalid email or password')).toBeInTheDocument()
    })
  })

  it('shows loading state during form submission', async () => {
    mockSignIn.mockImplementation(() => new Promise(resolve => setTimeout(resolve, 1000)))

    render(<LoginForm />)

    const emailInput = screen.getByLabelText('Email')
    const passwordInput = screen.getByLabelText('Password')
    const submitButton = screen.getByRole('button', { name: /sign in/i })

    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } })
    fireEvent.change(passwordInput, { target: { value: 'password123' } })
    fireEvent.click(submitButton)

    await waitFor(() => {
      expect(screen.getByText('Signing in...')).toBeInTheDocument()
      expect(submitButton).toBeDisabled()
    })
  })

  it('handles Google OAuth sign in', async () => {
    render(<LoginForm />)

    const googleButton = screen.getByRole('button', { name: /google/i })
    fireEvent.click(googleButton)

    await waitFor(() => {
      expect(mockSignIn).toHaveBeenCalledWith('google', { callbackUrl: '/dashboard' })
    })
  })

  it('handles GitHub OAuth sign in', async () => {
    render(<LoginForm />)

    const githubButton = screen.getByRole('button', { name: /github/i })
    fireEvent.click(githubButton)

    await waitFor(() => {
      expect(mockSignIn).toHaveBeenCalledWith('github', { callbackUrl: '/dashboard' })
    })
  })

  it('uses custom callback URL when provided', async () => {
    render(<LoginForm callbackUrl="/custom-redirect" />)

    const googleButton = screen.getByRole('button', { name: /google/i })
    fireEvent.click(googleButton)

    await waitFor(() => {
      expect(mockSignIn).toHaveBeenCalledWith('google', { callbackUrl: '/custom-redirect' })
    })
  })

  it('validates required fields', async () => {
    render(<LoginForm />)

    const submitButton = screen.getByRole('button', { name: /sign in/i })
    fireEvent.click(submitButton)

    // Form should not submit without required fields
    expect(mockSignIn).not.toHaveBeenCalled()
  })

  it('disables form during OAuth sign in', async () => {
    mockSignIn.mockImplementation(() => new Promise(resolve => setTimeout(resolve, 1000)))

    render(<LoginForm />)

    const googleButton = screen.getByRole('button', { name: /google/i })
    fireEvent.click(googleButton)

    await waitFor(() => {
      const emailInput = screen.getByLabelText('Email')
      const passwordInput = screen.getByLabelText('Password')
      const submitButton = screen.getByRole('button', { name: /sign in/i })

      expect(emailInput).toBeDisabled()
      expect(passwordInput).toBeDisabled()
      expect(submitButton).toBeDisabled()
    })
  })

  it('handles OAuth sign in errors', async () => {
    mockSignIn.mockRejectedValue(new Error('OAuth error'))

    render(<LoginForm />)

    const googleButton = screen.getByRole('button', { name: /google/i })
    fireEvent.click(googleButton)

    await waitFor(() => {
      expect(screen.getByText('Failed to sign in with google')).toBeInTheDocument()
    })
  })

  it('navigates to sign up page', async () => {
    render(<LoginForm />)

    const signUpLink = screen.getByRole('button', { name: /sign up/i })
    fireEvent.click(signUpLink)

    expect(mockPush).toHaveBeenCalledWith('/auth/register')
  })

  it('navigates to forgot password page', async () => {
    render(<LoginForm />)

    const forgotPasswordLink = screen.getByRole('button', { name: /forgot your password/i })
    fireEvent.click(forgotPasswordLink)

    expect(mockPush).toHaveBeenCalledWith('/auth/forgot-password')
  })

  it('applies glassmorphism styling', () => {
    render(<LoginForm />)

    const formCard = document.querySelector('.glass-card')
    expect(formCard).toBeInTheDocument()

    const glassInputs = document.querySelectorAll('.glass-input')
    expect(glassInputs.length).toBeGreaterThan(0)
  })

  it('has proper form accessibility', () => {
    render(<LoginForm />)

    const emailInput = screen.getByLabelText('Email')
    const passwordInput = screen.getByLabelText('Password')

    expect(emailInput).toHaveAttribute('type', 'email')
    expect(emailInput).toHaveAttribute('required')
    expect(passwordInput).toHaveAttribute('type', 'password')
    expect(passwordInput).toHaveAttribute('required')
  })

  it('shows input icons', () => {
    render(<LoginForm />)

    // Check for mail and lock icons (mocked as div elements)
    const icons = document.querySelectorAll('[data-testid="mock-icon"]')
    expect(icons.length).toBeGreaterThan(0)
  })

  it('handles unexpected errors gracefully', async () => {
    mockSignIn.mockRejectedValue(new Error('Network error'))

    render(<LoginForm />)

    const emailInput = screen.getByLabelText('Email')
    const passwordInput = screen.getByLabelText('Password')
    const submitButton = screen.getByRole('button', { name: /sign in/i })

    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } })
    fireEvent.change(passwordInput, { target: { value: 'password123' } })
    fireEvent.click(submitButton)

    await waitFor(() => {
      expect(screen.getByText('An unexpected error occurred')).toBeInTheDocument()
    })
  })

  it('clears error message on new submission', async () => {
    mockSignIn.mockResolvedValueOnce({ error: 'CredentialsSignin' })
      .mockResolvedValueOnce({ ok: true })

    render(<LoginForm />)

    const emailInput = screen.getByLabelText('Email')
    const passwordInput = screen.getByLabelText('Password')
    const submitButton = screen.getByRole('button', { name: /sign in/i })

    // First submission with error
    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } })
    fireEvent.change(passwordInput, { target: { value: 'wrongpassword' } })
    fireEvent.click(submitButton)

    await waitFor(() => {
      expect(screen.getByText('Invalid email or password')).toBeInTheDocument()
    })

    // Second submission should clear error
    fireEvent.change(passwordInput, { target: { value: 'correctpassword' } })
    fireEvent.click(submitButton)

    await waitFor(() => {
      expect(screen.queryByText('Invalid email or password')).not.toBeInTheDocument()
    })
  })
})
