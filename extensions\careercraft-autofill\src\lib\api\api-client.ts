/**
 * API Client
 * 
 * Handles communication with the CareerCraft platform API,
 * including authentication, data retrieval, and error handling.
 */

export interface UserProfile {
  id: string
  email: string
  firstName: string
  lastName: string
  phone?: string
  address?: string
  city?: string
  state?: string
  zipCode?: string
  country?: string
  currentTitle?: string
  currentCompany?: string
  yearsExperience?: number
  education?: string
  skills?: string[]
  resume?: string
  portfolio?: string
  linkedin?: string
  github?: string
}

export interface AutofillRequest {
  formFields: FormFieldRequest[]
  jobDescription?: string
  customization?: boolean
}

export interface FormFieldRequest {
  type: string
  label: string
  mappedTo?: string
  required: boolean
}

export interface AutofillResponse {
  [fieldName: string]: any
}

export interface ApplicationTrackingData {
  jobTitle: string
  company: string
  source: string
  url?: string
  appliedAt?: Date
  status?: string
  notes?: string
}

export class ApiClient {
  private baseUrl: string
  private authToken: string | null = null
  private requestQueue: Map<string, Promise<any>> = new Map()

  constructor() {
    this.baseUrl = process.env.CAREERCRAFT_API_URL || 'https://careercraft.onlinejobsearchhelp.com/api'
  }

  /**
   * Set authentication token
   */
  setAuthToken(token: string): void {
    this.authToken = token
  }

  /**
   * Clear authentication token
   */
  clearAuthToken(): void {
    this.authToken = null
  }

  /**
   * Get user profile
   */
  async getUserProfile(): Promise<UserProfile> {
    return this.makeRequest<UserProfile>('GET', '/user/profile')
  }

  /**
   * Update user profile
   */
  async updateUserProfile(profile: Partial<UserProfile>): Promise<UserProfile> {
    return this.makeRequest<UserProfile>('PUT', '/user/profile', profile)
  }

  /**
   * Get autofill data for form
   */
  async getAutofillData(request: AutofillRequest): Promise<AutofillResponse> {
    return this.makeRequest<AutofillResponse>('POST', '/autofill/generate', request)
  }

  /**
   * Track job application
   */
  async trackApplication(data: ApplicationTrackingData): Promise<{ success: boolean }> {
    return this.makeRequest<{ success: boolean }>('POST', '/applications/track', data)
  }

  /**
   * Get application history
   */
  async getApplicationHistory(): Promise<ApplicationTrackingData[]> {
    return this.makeRequest<ApplicationTrackingData[]>('GET', '/applications/history')
  }

  /**
   * Get user analytics
   */
  async getUserAnalytics(): Promise<{
    totalApplications: number
    successRate: number
    averageResponseTime: number
    topCompanies: string[]
    topSkills: string[]
  }> {
    return this.makeRequest('GET', '/analytics/user')
  }

  /**
   * Validate authentication token
   */
  async validateToken(): Promise<{ valid: boolean; expiresAt?: Date }> {
    try {
      const response = await this.makeRequest<{ valid: boolean; expiresAt: string }>('GET', '/auth/validate')
      return {
        valid: response.valid,
        expiresAt: response.expiresAt ? new Date(response.expiresAt) : undefined
      }
    } catch (error) {
      return { valid: false }
    }
  }

  /**
   * Refresh authentication token
   */
  async refreshToken(): Promise<{ token: string; expiresAt: Date }> {
    return this.makeRequest<{ token: string; expiresAt: string }>('POST', '/auth/refresh')
      .then(response => ({
        token: response.token,
        expiresAt: new Date(response.expiresAt)
      }))
  }

  /**
   * Get job recommendations
   */
  async getJobRecommendations(limit: number = 10): Promise<{
    id: string
    title: string
    company: string
    location: string
    url: string
    matchScore: number
    reasons: string[]
  }[]> {
    return this.makeRequest('GET', `/recommendations/jobs?limit=${limit}`)
  }

  /**
   * Submit feedback
   */
  async submitFeedback(feedback: {
    type: 'bug' | 'feature' | 'general'
    message: string
    rating?: number
    metadata?: any
  }): Promise<{ success: boolean }> {
    return this.makeRequest('POST', '/feedback', feedback)
  }

  /**
   * Make HTTP request with error handling and retry logic
   */
  private async makeRequest<T>(
    method: string,
    endpoint: string,
    data?: any,
    retries: number = 3
  ): Promise<T> {
    const requestKey = `${method}:${endpoint}:${JSON.stringify(data)}`
    
    // Check if identical request is already in progress
    if (this.requestQueue.has(requestKey)) {
      return this.requestQueue.get(requestKey)
    }

    const requestPromise = this.executeRequest<T>(method, endpoint, data, retries)
    this.requestQueue.set(requestKey, requestPromise)

    try {
      const result = await requestPromise
      return result
    } finally {
      this.requestQueue.delete(requestKey)
    }
  }

  /**
   * Execute HTTP request
   */
  private async executeRequest<T>(
    method: string,
    endpoint: string,
    data?: any,
    retries: number = 3
  ): Promise<T> {
    const url = `${this.baseUrl}${endpoint}`
    
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'User-Agent': 'CareerCraft-Extension/1.0.0'
    }

    if (this.authToken) {
      headers['Authorization'] = `Bearer ${this.authToken}`
    }

    const config: RequestInit = {
      method,
      headers,
      mode: 'cors',
      credentials: 'omit'
    }

    if (data && method !== 'GET') {
      config.body = JSON.stringify(data)
    }

    for (let attempt = 0; attempt <= retries; attempt++) {
      try {
        const response = await fetch(url, config)
        
        if (!response.ok) {
          await this.handleErrorResponse(response, attempt, retries)
          continue
        }

        const contentType = response.headers.get('content-type')
        if (contentType && contentType.includes('application/json')) {
          return await response.json()
        } else {
          return await response.text() as any
        }
      } catch (error) {
        if (attempt === retries) {
          throw new Error(`Network error after ${retries + 1} attempts: ${error.message}`)
        }
        
        // Wait before retry with exponential backoff
        await this.delay(Math.pow(2, attempt) * 1000)
      }
    }

    throw new Error('Request failed after all retry attempts')
  }

  /**
   * Handle error responses
   */
  private async handleErrorResponse(response: Response, attempt: number, maxRetries: number): Promise<void> {
    const status = response.status
    let errorMessage = `HTTP ${status}: ${response.statusText}`

    try {
      const errorData = await response.json()
      errorMessage = errorData.message || errorData.error || errorMessage
    } catch {
      // Response is not JSON, use status text
    }

    // Handle specific error codes
    switch (status) {
      case 401:
        this.authToken = null
        throw new Error('Authentication required. Please sign in again.')
      
      case 403:
        throw new Error('Access forbidden. Please check your permissions.')
      
      case 404:
        throw new Error('Resource not found.')
      
      case 429:
        // Rate limited - wait longer before retry
        if (attempt < maxRetries) {
          await this.delay(5000 + Math.random() * 5000) // 5-10 seconds
          return
        }
        throw new Error('Rate limit exceeded. Please try again later.')
      
      case 500:
      case 502:
      case 503:
      case 504:
        // Server errors - retry with backoff
        if (attempt < maxRetries) {
          await this.delay(Math.pow(2, attempt) * 1000)
          return
        }
        throw new Error('Server error. Please try again later.')
      
      default:
        throw new Error(errorMessage)
    }
  }

  /**
   * Delay execution
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  /**
   * Check if client is authenticated
   */
  isAuthenticated(): boolean {
    return this.authToken !== null
  }

  /**
   * Get current auth token
   */
  getAuthToken(): string | null {
    return this.authToken
  }

  /**
   * Test API connectivity
   */
  async testConnection(): Promise<{ success: boolean; latency: number }> {
    const startTime = Date.now()
    
    try {
      await this.makeRequest('GET', '/health')
      const latency = Date.now() - startTime
      return { success: true, latency }
    } catch (error) {
      const latency = Date.now() - startTime
      return { success: false, latency }
    }
  }

  /**
   * Get API status
   */
  async getApiStatus(): Promise<{
    status: 'online' | 'offline' | 'maintenance'
    version: string
    features: string[]
  }> {
    return this.makeRequest('GET', '/status')
  }

  /**
   * Upload file (resume, cover letter, etc.)
   */
  async uploadFile(file: File, type: 'resume' | 'cover-letter' | 'portfolio'): Promise<{
    url: string
    filename: string
    size: number
  }> {
    const formData = new FormData()
    formData.append('file', file)
    formData.append('type', type)

    const headers: Record<string, string> = {}
    if (this.authToken) {
      headers['Authorization'] = `Bearer ${this.authToken}`
    }

    const response = await fetch(`${this.baseUrl}/upload`, {
      method: 'POST',
      headers,
      body: formData,
      mode: 'cors',
      credentials: 'omit'
    })

    if (!response.ok) {
      throw new Error(`Upload failed: ${response.statusText}`)
    }

    return response.json()
  }

  /**
   * Get extension configuration
   */
  async getExtensionConfig(): Promise<{
    features: {
      autofill: boolean
      tracking: boolean
      analytics: boolean
      customization: boolean
    }
    limits: {
      applicationsPerDay: number
      applicationsPerMonth: number
    }
    supportedSites: string[]
  }> {
    return this.makeRequest('GET', '/extension/config')
  }

  /**
   * Report extension error
   */
  async reportError(error: {
    message: string
    stack?: string
    url?: string
    userAgent?: string
    timestamp?: Date
    metadata?: any
  }): Promise<{ success: boolean }> {
    return this.makeRequest('POST', '/extension/error', {
      ...error,
      timestamp: error.timestamp || new Date()
    })
  }

  /**
   * Get usage statistics
   */
  async getUsageStats(): Promise<{
    applicationsThisMonth: number
    applicationsToday: number
    successRate: number
    averageFormFillTime: number
    topSites: { site: string; count: number }[]
  }> {
    return this.makeRequest('GET', '/extension/stats')
  }
}
