# 🧪 CareerCraft Local Testing Guide

## 🚀 Quick Start (5 Minutes)

### Prerequisites Check
Before starting, ensure you have:
- [ ] **Node.js 18+** installed
- [ ] **PostgreSQL** installed and running
- [ ] **Git** installed
- [ ] **Terminal/Command Prompt** access

### 1. Run Setup Script

#### Windows (PowerS<PERSON> as Administrator):
```powershell
# First, install prerequisites
.\setup\install-prerequisites.ps1

# Then run main setup
.\setup-local-testing.sh
```

#### macOS/Linux:
```bash
# Make script executable and run
chmod +x setup-local-testing.sh
./setup-local-testing.sh
```

### 2. Configure API Keys

Edit `careercraft-local/.env.local`:

```bash
# Required for authentication
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"

# Required for AI features
OPENAI_API_KEY="sk-your-openai-key"

# Required for payments
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY="pk_test_..."
STRIPE_SECRET_KEY="sk_test_..."
```

### 3. Start Development

```bash
cd careercraft-local
./start-dev.sh
```

Visit: **http://localhost:3000** 🎉

---

## 🧪 Testing All Epic Features

### Epic 8.0: Payment System Testing

#### Test Subscription Creation:
```bash
# 1. Start Stripe webhook listener
npm run stripe:listen

# 2. Test payment flow in browser
# Visit: http://localhost:3000/pricing
# Use test card: ****************
```

#### Test API Endpoints:
```bash
# Health check
curl http://localhost:3000/api/health

# Subscription status
curl http://localhost:3000/api/subscriptions \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### Epic 1.0: Career Intelligence Testing

#### Test AI Content Generation:
```bash
# Test OpenAI integration
curl -X POST http://localhost:3000/api/ai/generate \
  -H "Content-Type: application/json" \
  -d '{"prompt": "Generate a professional summary for a software engineer"}'
```

#### Test Career Analysis:
```bash
# Test profile analysis
curl -X POST http://localhost:3000/api/career/analyze \
  -H "Content-Type: application/json" \
  -d '{"experience": "5 years", "skills": ["React", "Node.js"]}'
```

### Epic 6.0: Browser Extension Testing

#### Build Extension:
```bash
# Navigate to extension directory (when implemented)
cd browser-extension
npm run build:dev

# Load in Chrome:
# 1. Open chrome://extensions/
# 2. Enable Developer mode
# 3. Click "Load unpacked"
# 4. Select the build folder
```

### Database Testing

#### View Database:
```bash
# Open Prisma Studio
npm run db:studio
# Visit: http://localhost:5555
```

#### Reset Database:
```bash
# Reset and reseed
npm run db:reset
```

---

## 🧪 Automated Testing

### Unit Tests:
```bash
# Run all unit tests
npm run test

# Run with coverage
npm run test:coverage

# Watch mode
npm run test:watch
```

### Integration Tests:
```bash
# Test API endpoints
npm run test:integration
```

### End-to-End Tests:
```bash
# Run E2E tests
npm run test:e2e

# Run with UI
npm run test:e2e:ui
```

---

## 🔧 Development Tools

### Database Management:
```bash
# View database
npm run db:studio

# Generate Prisma client
npm run db:generate

# Push schema changes
npm run db:push

# Create migration
npm run db:migrate
```

### Code Quality:
```bash
# Lint code
npm run lint

# Format code
npm run format

# Type check
npm run type-check
```

### Stripe Testing:
```bash
# Listen for webhooks
npm run stripe:listen

# Test webhook events
stripe trigger payment_intent.succeeded
stripe trigger customer.subscription.created
```

---

## 📊 Testing Checklist

### ✅ Core Features
- [ ] User authentication (Google/GitHub)
- [ ] Database operations (CRUD)
- [ ] API endpoints responding
- [ ] Health check passing

### ✅ Epic 8.0: Payment System
- [ ] Subscription creation
- [ ] Payment processing
- [ ] Webhook handling
- [ ] Usage tracking
- [ ] Billing dashboard

### ✅ Epic 1.0: Career Intelligence
- [ ] AI content generation
- [ ] Profile analysis
- [ ] Career insights
- [ ] Market data collection

### ✅ Epic 6.0: Browser Extension
- [ ] Extension builds successfully
- [ ] Form detection works
- [ ] Autofill functionality
- [ ] Data synchronization

### ✅ Epic 2.0: Collaboration
- [ ] Real-time editing
- [ ] Comment system
- [ ] Version control
- [ ] User permissions

### ✅ Epic 3.0: Template System
- [ ] Template rendering
- [ ] Marketplace functionality
- [ ] Custom template creation
- [ ] Template sharing

### ✅ Epic 4.0: Integrations
- [ ] LinkedIn API connection
- [ ] GitHub integration
- [ ] Job board APIs
- [ ] External data sync

### ✅ Epic 5.0: Mobile Features
- [ ] Responsive design
- [ ] Mobile-optimized UI
- [ ] Touch interactions
- [ ] Offline capabilities

### ✅ Epic 7.0: Advanced AI
- [ ] Advanced content generation
- [ ] Personalized recommendations
- [ ] Interview preparation
- [ ] Skill analysis

---

## 🐛 Troubleshooting

### Database Issues:
```bash
# Check PostgreSQL status
pg_isready -h localhost -p 5432

# Restart PostgreSQL
# macOS: brew services restart postgresql@14
# Linux: sudo systemctl restart postgresql
```

### API Key Issues:
```bash
# Verify environment variables
node -e "console.log(process.env.OPENAI_API_KEY ? 'OpenAI: ✅' : 'OpenAI: ❌')"
node -e "console.log(process.env.STRIPE_SECRET_KEY ? 'Stripe: ✅' : 'Stripe: ❌')"
```

### Port Conflicts:
```bash
# Check what's running on port 3000
lsof -i :3000

# Kill process if needed
kill -9 $(lsof -t -i:3000)
```

### Clear Cache:
```bash
# Clear Next.js cache
rm -rf .next

# Clear node_modules
rm -rf node_modules
npm install
```

---

## 📈 Performance Testing

### Load Testing:
```bash
# Install artillery
npm install -g artillery

# Run load test
artillery quick --count 10 --num 5 http://localhost:3000/api/health
```

### Memory Usage:
```bash
# Monitor memory usage
node --inspect server.js
# Open chrome://inspect in Chrome
```

---

## 🎯 Next Steps After Local Testing

1. **✅ Validate All Features** - Ensure all Epic 1-8 features work locally
2. **📊 Performance Optimization** - Optimize based on local testing results
3. **🔒 Security Testing** - Run security scans and penetration tests
4. **📱 Mobile Testing** - Test on various devices and screen sizes
5. **🚀 Deployment Preparation** - Prepare for Hostinger deployment

---

## 🆘 Getting Help

### Common Commands:
```bash
# Check service status
npm run health-check

# View logs
tail -f logs/app.log

# Database status
npm run db:status

# Full system check
npm run system:check
```

### Support Resources:
- 📚 **Documentation**: `/docs` folder
- 🐛 **Issues**: Check console logs and error messages
- 💬 **Community**: Stack Overflow, GitHub Discussions
- 📧 **Direct Support**: Create GitHub issue with logs

---

**🎉 Happy Testing! Your complete CareerCraft local environment is ready for comprehensive Epic 1-8 feature testing!**
