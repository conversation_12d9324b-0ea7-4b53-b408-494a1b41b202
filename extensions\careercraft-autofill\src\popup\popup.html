<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CareerCraft Autofill</title>
    <style>
        body {
            width: 320px;
            height: 480px;
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #ffffff;
            overflow: hidden;
        }
        
        #root {
            width: 100%;
            height: 100%;
        }
        
        /* Loading state */
        .loading {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
            flex-direction: column;
            gap: 16px;
        }
        
        .loading-spinner {
            width: 32px;
            height: 32px;
            border: 3px solid #e5e7eb;
            border-top: 3px solid #4f46e5;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .loading-text {
            color: #6b7280;
            font-size: 14px;
        }
        
        /* Error state */
        .error {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
            flex-direction: column;
            gap: 16px;
            padding: 20px;
            text-align: center;
        }
        
        .error-icon {
            width: 48px;
            height: 48px;
            color: #dc2626;
        }
        
        .error-title {
            font-size: 16px;
            font-weight: 600;
            color: #111827;
            margin: 0;
        }
        
        .error-message {
            font-size: 14px;
            color: #6b7280;
            margin: 0;
            line-height: 1.5;
        }
        
        .error-button {
            background: #4f46e5;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 14px;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        
        .error-button:hover {
            background: #4338ca;
        }
    </style>
</head>
<body>
    <div id="root">
        <div class="loading">
            <div class="loading-spinner"></div>
            <div class="loading-text">Loading CareerCraft...</div>
        </div>
    </div>
    
    <script>
        // Show error state if React fails to load
        setTimeout(() => {
            const root = document.getElementById('root');
            if (root && root.innerHTML.includes('loading')) {
                root.innerHTML = `
                    <div class="error">
                        <svg class="error-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                        </svg>
                        <h3 class="error-title">Failed to Load</h3>
                        <p class="error-message">CareerCraft extension failed to initialize. Please try refreshing or reinstalling the extension.</p>
                        <button class="error-button" onclick="window.location.reload()">Retry</button>
                    </div>
                `;
            }
        }, 5000);
    </script>
</body>
</html>
