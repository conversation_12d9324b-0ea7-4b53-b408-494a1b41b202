@echo off
setlocal enabledelayedexpansion

:: CareerCraft Setup Verification Script for Windows
:: This script verifies that the database setup is working correctly

echo.
echo 🔍 Verifying CareerCraft Database Setup
echo ==========================================
echo.

set "failed=0"

:: Check if .env.local exists
echo [INFO] Checking environment configuration...
if exist ".env.local" (
    echo [SUCCESS] .env.local file exists
    
    findstr /C:"DATABASE_URL" .env.local >nul
    if !errorlevel! equ 0 (
        echo [SUCCESS] DATABASE_URL is configured
    ) else (
        echo [ERROR] DATABASE_URL not found in .env.local
        set "failed=1"
    )
    
    findstr /C:"NEXTAUTH_SECRET" .env.local >nul
    if !errorlevel! equ 0 (
        echo [SUCCESS] NEXTAUTH_SECRET is configured
    ) else (
        echo [WARNING] NEXTAUTH_SECRET not found in .env.local
    )
) else (
    echo [ERROR] .env.local file not found
    echo [ERROR] Please run the setup script first: scripts\setup.bat
    set "failed=1"
)

:: Check if dependencies are installed
echo [INFO] Checking dependencies...
if exist "node_modules" (
    echo [SUCCESS] Node modules are installed
) else (
    echo [ERROR] Node modules not found. Run: npm install
    set "failed=1"
)

:: Check if Prisma client is generated
if exist "packages\database\src\generated" (
    echo [SUCCESS] Prisma client is generated
) else (
    echo [WARNING] Prisma client not generated. Run: npm run db:generate
)

:: Test TypeScript type checking
echo [INFO] Running TypeScript type checking...
call npm run type-check >nul 2>&1
if !errorlevel! equ 0 (
    echo [SUCCESS] TypeScript type checking passed
) else (
    echo [ERROR] TypeScript type checking failed
    set "failed=1"
)

:: Test linting
echo [INFO] Running code linting...
call npm run lint >nul 2>&1
if !errorlevel! equ 0 (
    echo [SUCCESS] Code linting passed
) else (
    echo [WARNING] Code linting found issues (run 'npm run lint:fix' to auto-fix)
)

:: Test database connection
echo [INFO] Testing database connection...
call npm run test:db >nul 2>&1
if !errorlevel! equ 0 (
    echo [SUCCESS] Database tests passed
) else (
    echo [ERROR] Database tests failed
    echo [ERROR] Please check your database configuration and ensure PostgreSQL is running
    set "failed=1"
)

:: Test API endpoints (only if previous tests passed)
if !failed! equ 0 (
    echo [INFO] Testing API endpoints...
    echo [INFO] Starting development server...
    
    :: Start dev server in background
    start /B npm run dev >nul 2>&1
    
    :: Wait for server to start
    timeout /t 10 /nobreak >nul
    
    :: Test health endpoint
    curl -f http://localhost:3000/api/health >nul 2>&1
    if !errorlevel! equ 0 (
        echo [SUCCESS] Health endpoint is responding
    ) else (
        echo [ERROR] Health endpoint is not responding
        set "failed=1"
    )
    
    :: Stop development server
    taskkill /F /IM node.exe >nul 2>&1
    timeout /t 2 /nobreak >nul
)

:: Final results
echo.
if !failed! equ 0 (
    echo [SUCCESS] 🎉 All verification checks passed!
    echo.
    echo [INFO] Your CareerCraft setup is ready for development!
    echo.
    echo [INFO] Next steps:
    echo 1. Start development server: npm run dev
    echo 2. Open browser: http://localhost:3000
    echo 3. Check health endpoint: http://localhost:3000/api/health
    echo 4. Open Prisma Studio: npm run db:studio
    echo.
) else (
    echo [ERROR] ❌ Some verification checks failed
    echo.
    echo [INFO] Please fix the issues above and run the verification again
    echo.
    exit /b 1
)

echo.
pause
