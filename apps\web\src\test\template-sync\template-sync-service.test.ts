/**
 * Template Sync Service Unit Tests
 * 
 * Tests for template synchronization, versioning, and conflict resolution
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { TemplateSyncService } from '@/lib/template-sync/service'

// Mock Prisma
const mockPrisma = {
  template: {
    findMany: vi.fn(),
    findUnique: vi.fn(),
    update: vi.fn()
  },
  templateCloudStorage: {
    findMany: vi.fn(),
    upsert: vi.fn(),
    count: vi.fn()
  },
  templateVersion: {
    findMany: vi.fn(),
    findFirst: vi.fn(),
    findUnique: vi.fn(),
    create: vi.fn()
  },
  templateSyncConflict: {
    findMany: vi.fn(),
    findUnique: vi.fn(),
    update: vi.fn(),
    count: vi.fn()
  }
}

vi.mock('@/lib/db', () => ({
  prisma: mockPrisma
}))

describe('TemplateSyncService', () => {
  let service: TemplateSyncService

  beforeEach(() => {
    service = new TemplateSyncService()
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('syncUserTemplates', () => {
    const mockTemplates = [
      {
        id: 'template-1',
        name: 'Professional Resume',
        description: 'Clean professional template',
        config: JSON.stringify({ layout: 'single-column' }),
        cloudStorage: [],
        versions: []
      },
      {
        id: 'template-2',
        name: 'Creative Portfolio',
        description: 'Creative design template',
        config: JSON.stringify({ layout: 'multi-column' }),
        cloudStorage: [{
          checksum: 'abc123',
          syncStatus: 'synced'
        }],
        versions: [{
          versionNumber: 1,
          templateData: JSON.stringify({ name: 'Creative Portfolio' })
        }]
      }
    ]

    beforeEach(() => {
      mockPrisma.template.findMany.mockResolvedValue(mockTemplates)
      mockPrisma.templateSyncConflict.findMany.mockResolvedValue([])
    })

    it('should sync all user templates successfully', async () => {
      const result = await service.syncUserTemplates('user-1')

      expect(mockPrisma.template.findMany).toHaveBeenCalledWith({
        where: {
          OR: [
            { createdBy: 'user-1' },
            {
              shares: {
                some: {
                  sharedWith: 'user-1',
                  permissionLevel: { in: ['edit', 'admin'] }
                }
              }
            }
          ]
        },
        include: {
          cloudStorage: {
            where: { userId: 'user-1' }
          },
          versions: {
            orderBy: { versionNumber: 'desc' },
            take: 1
          }
        }
      })

      expect(result.success).toBe(true)
      expect(result.syncedTemplates).toBe(2)
      expect(result.errors).toHaveLength(0)
      expect(result.conflicts).toHaveLength(0)
    })

    it('should handle sync errors gracefully', async () => {
      // Mock an error for one template
      vi.spyOn(service, 'syncSingleTemplate')
        .mockResolvedValueOnce(undefined)
        .mockRejectedValueOnce(new Error('Sync failed'))

      const result = await service.syncUserTemplates('user-1')

      expect(result.success).toBe(true)
      expect(result.syncedTemplates).toBe(1)
      expect(result.errors).toHaveLength(1)
      expect(result.errors[0].templateId).toBe('template-2')
      expect(result.errors[0].error).toBe('Sync failed')
    })

    it('should detect and return conflicts', async () => {
      const mockConflicts = [
        {
          id: 'conflict-1',
          templateId: 'template-1',
          userId: 'user-1',
          conflictType: 'concurrent_edit',
          localVersion: JSON.stringify({ name: 'Local Version' }),
          remoteVersion: JSON.stringify({ name: 'Remote Version' }),
          createdAt: new Date()
        }
      ]

      mockPrisma.templateSyncConflict.findMany.mockResolvedValue(mockConflicts)

      const result = await service.syncUserTemplates('user-1')

      expect(result.conflicts).toHaveLength(1)
      expect(result.conflicts[0].conflictType).toBe('concurrent_edit')
    })
  })

  describe('syncSingleTemplate', () => {
    const mockTemplate = {
      id: 'template-1',
      name: 'Test Template',
      config: JSON.stringify({ layout: 'test' }),
      cloudStorage: [],
      versions: []
    }

    beforeEach(() => {
      mockPrisma.template.findUnique.mockResolvedValue(mockTemplate)
      mockPrisma.templateVersion.create.mockResolvedValue({ id: 'version-1' })
      mockPrisma.templateCloudStorage.upsert.mockResolvedValue({})
    })

    it('should sync template when no cloud storage exists', async () => {
      await service.syncSingleTemplate('template-1', 'user-1')

      expect(mockPrisma.templateVersion.create).toHaveBeenCalled()
      expect(mockPrisma.templateCloudStorage.upsert).toHaveBeenCalled()
    })

    it('should skip sync when template is already in sync', async () => {
      const templateWithStorage = {
        ...mockTemplate,
        cloudStorage: [{
          checksum: service['calculateChecksum'](mockTemplate),
          syncStatus: 'synced'
        }]
      }

      mockPrisma.template.findUnique.mockResolvedValue(templateWithStorage)

      await service.syncSingleTemplate('template-1', 'user-1')

      // Should not create new version if already in sync
      expect(mockPrisma.templateVersion.create).not.toHaveBeenCalled()
    })

    it('should throw error for non-existent template', async () => {
      mockPrisma.template.findUnique.mockResolvedValue(null)

      await expect(service.syncSingleTemplate('non-existent', 'user-1'))
        .rejects.toThrow('Template not found')
    })
  })

  describe('detectConflicts', () => {
    it('should return unresolved conflicts for user', async () => {
      const mockConflicts = [
        {
          id: 'conflict-1',
          templateId: 'template-1',
          conflictType: 'concurrent_edit',
          localVersion: JSON.stringify({ name: 'Local' }),
          remoteVersion: JSON.stringify({ name: 'Remote' }),
          createdAt: new Date(),
          template: { id: 'template-1', name: 'Test Template' }
        }
      ]

      mockPrisma.templateSyncConflict.findMany.mockResolvedValue(mockConflicts)

      const conflicts = await service.detectConflicts('user-1')

      expect(mockPrisma.templateSyncConflict.findMany).toHaveBeenCalledWith({
        where: {
          userId: 'user-1',
          resolvedAt: null
        },
        include: {
          template: true
        }
      })

      expect(conflicts).toHaveLength(1)
      expect(conflicts[0].conflictType).toBe('concurrent_edit')
    })

    it('should return empty array when no conflicts exist', async () => {
      mockPrisma.templateSyncConflict.findMany.mockResolvedValue([])

      const conflicts = await service.detectConflicts('user-1')

      expect(conflicts).toHaveLength(0)
    })
  })

  describe('resolveConflict', () => {
    const mockConflict = {
      id: 'conflict-1',
      templateId: 'template-1',
      userId: 'user-1',
      localVersion: JSON.stringify({ name: 'Local Version', description: 'Local desc' }),
      remoteVersion: JSON.stringify({ name: 'Remote Version', description: 'Remote desc' }),
      template: { id: 'template-1' }
    }

    beforeEach(() => {
      mockPrisma.templateSyncConflict.findUnique.mockResolvedValue(mockConflict)
      mockPrisma.template.update.mockResolvedValue({})
      mockPrisma.templateSyncConflict.update.mockResolvedValue({})
      mockPrisma.templateVersion.create.mockResolvedValue({ id: 'version-1' })
    })

    it('should resolve conflict by preferring local version', async () => {
      const resolution = {
        conflictId: 'conflict-1',
        resolution: 'prefer_local' as const
      }

      await service.resolveConflict('conflict-1', resolution)

      expect(mockPrisma.template.update).toHaveBeenCalledWith({
        where: { id: 'template-1' },
        data: expect.objectContaining({
          name: 'Local Version',
          description: 'Local desc'
        })
      })

      expect(mockPrisma.templateSyncConflict.update).toHaveBeenCalledWith({
        where: { id: 'conflict-1' },
        data: {
          resolutionStrategy: 'prefer_local',
          resolvedAt: expect.any(Date)
        }
      })
    })

    it('should resolve conflict by preferring remote version', async () => {
      const resolution = {
        conflictId: 'conflict-1',
        resolution: 'prefer_remote' as const
      }

      await service.resolveConflict('conflict-1', resolution)

      expect(mockPrisma.template.update).toHaveBeenCalledWith({
        where: { id: 'template-1' },
        data: expect.objectContaining({
          name: 'Remote Version',
          description: 'Remote desc'
        })
      })
    })

    it('should resolve conflict with manual merge', async () => {
      const resolution = {
        conflictId: 'conflict-1',
        resolution: 'manual_merge' as const,
        mergedData: {
          name: 'Merged Version',
          description: 'Merged description'
        }
      }

      await service.resolveConflict('conflict-1', resolution)

      expect(mockPrisma.template.update).toHaveBeenCalledWith({
        where: { id: 'template-1' },
        data: expect.objectContaining({
          name: 'Merged Version',
          description: 'Merged description'
        })
      })
    })

    it('should throw error for non-existent conflict', async () => {
      mockPrisma.templateSyncConflict.findUnique.mockResolvedValue(null)

      const resolution = {
        conflictId: 'non-existent',
        resolution: 'prefer_local' as const
      }

      await expect(service.resolveConflict('non-existent', resolution))
        .rejects.toThrow('Conflict not found')
    })

    it('should throw error for manual merge without merged data', async () => {
      const resolution = {
        conflictId: 'conflict-1',
        resolution: 'manual_merge' as const
      }

      await expect(service.resolveConflict('conflict-1', resolution))
        .rejects.toThrow('Merged data required for manual resolution')
    })
  })

  describe('createTemplateVersion', () => {
    beforeEach(() => {
      mockPrisma.templateVersion.findFirst.mockResolvedValue({
        versionNumber: 2
      })
      mockPrisma.templateVersion.create.mockResolvedValue({
        id: 'version-3'
      })
    })

    it('should create new template version with incremented version number', async () => {
      const versionData = {
        templateId: 'template-1',
        templateData: { name: 'Test Template' },
        changesSummary: 'Updated layout'
      }

      const versionId = await service.createTemplateVersion('template-1', versionData, 'user-1')

      expect(mockPrisma.templateVersion.create).toHaveBeenCalledWith({
        data: {
          templateId: 'template-1',
          versionNumber: 3,
          versionName: 'Version 3',
          changesSummary: 'Updated layout',
          templateData: JSON.stringify({ name: 'Test Template' }),
          createdBy: 'user-1'
        }
      })

      expect(versionId).toBe('version-3')
    })

    it('should create first version when no previous versions exist', async () => {
      mockPrisma.templateVersion.findFirst.mockResolvedValue(null)

      const versionData = {
        templateId: 'template-1',
        templateData: { name: 'Test Template' }
      }

      await service.createTemplateVersion('template-1', versionData, 'user-1')

      expect(mockPrisma.templateVersion.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          versionNumber: 1,
          versionName: 'Version 1'
        })
      })
    })

    it('should use custom version name when provided', async () => {
      const versionData = {
        templateId: 'template-1',
        templateData: { name: 'Test Template' },
        versionName: 'Custom Version Name'
      }

      await service.createTemplateVersion('template-1', versionData, 'user-1')

      expect(mockPrisma.templateVersion.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          versionName: 'Custom Version Name'
        })
      })
    })
  })

  describe('getVersionHistory', () => {
    const mockVersions = [
      {
        id: 'version-2',
        versionNumber: 2,
        versionName: 'Version 2',
        changesSummary: 'Updated styles',
        templateData: JSON.stringify({ name: 'Template v2' }),
        createdAt: new Date(),
        creator: { id: 'user-1', name: 'John Doe', email: '<EMAIL>' }
      },
      {
        id: 'version-1',
        versionNumber: 1,
        versionName: 'Version 1',
        changesSummary: 'Initial version',
        templateData: JSON.stringify({ name: 'Template v1' }),
        createdAt: new Date(),
        creator: { id: 'user-1', name: 'John Doe', email: '<EMAIL>' }
      }
    ]

    beforeEach(() => {
      mockPrisma.templateVersion.findMany.mockResolvedValue(mockVersions)
    })

    it('should return version history in descending order', async () => {
      const history = await service.getVersionHistory('template-1')

      expect(mockPrisma.templateVersion.findMany).toHaveBeenCalledWith({
        where: { templateId: 'template-1' },
        include: {
          creator: {
            select: {
              id: true,
              name: true,
              email: true
            }
          }
        },
        orderBy: { versionNumber: 'desc' }
      })

      expect(history).toHaveLength(2)
      expect(history[0].versionNumber).toBe(2)
      expect(history[1].versionNumber).toBe(1)
      expect(history[0].templateData).toEqual({ name: 'Template v2' })
    })

    it('should return empty array for template with no versions', async () => {
      mockPrisma.templateVersion.findMany.mockResolvedValue([])

      const history = await service.getVersionHistory('template-1')

      expect(history).toHaveLength(0)
    })
  })

  describe('rollbackToVersion', () => {
    const mockVersion = {
      id: 'version-1',
      templateId: 'template-1',
      versionNumber: 1,
      templateData: JSON.stringify({
        name: 'Rollback Template',
        description: 'Rolled back version'
      })
    }

    beforeEach(() => {
      mockPrisma.templateVersion.findUnique.mockResolvedValue(mockVersion)
      mockPrisma.template.update.mockResolvedValue({})
      mockPrisma.templateVersion.create.mockResolvedValue({ id: 'new-version' })
    })

    it('should rollback template to specified version', async () => {
      await service.rollbackToVersion('template-1', 'version-1', 'user-1')

      expect(mockPrisma.template.update).toHaveBeenCalledWith({
        where: { id: 'template-1' },
        data: expect.objectContaining({
          name: 'Rollback Template',
          description: 'Rolled back version'
        })
      })

      expect(mockPrisma.templateVersion.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          changesSummary: 'Rolled back to version 1'
        })
      })
    })

    it('should throw error for non-existent version', async () => {
      mockPrisma.templateVersion.findUnique.mockResolvedValue(null)

      await expect(service.rollbackToVersion('template-1', 'non-existent', 'user-1'))
        .rejects.toThrow('Version not found')
    })

    it('should throw error for version from different template', async () => {
      const wrongTemplateVersion = {
        ...mockVersion,
        templateId: 'different-template'
      }

      mockPrisma.templateVersion.findUnique.mockResolvedValue(wrongTemplateVersion)

      await expect(service.rollbackToVersion('template-1', 'version-1', 'user-1'))
        .rejects.toThrow('Version not found')
    })
  })

  describe('getSyncStatus', () => {
    const mockCloudStorage = [
      {
        templateId: 'template-1',
        syncStatus: 'synced',
        lastSynced: new Date('2024-01-15'),
        template: { id: 'template-1', name: 'Template 1', updatedAt: new Date() }
      },
      {
        templateId: 'template-2',
        syncStatus: 'pending',
        lastSynced: new Date('2024-01-14'),
        template: { id: 'template-2', name: 'Template 2', updatedAt: new Date() }
      },
      {
        templateId: 'template-3',
        syncStatus: 'error',
        lastSynced: new Date('2024-01-13'),
        template: { id: 'template-3', name: 'Template 3', updatedAt: new Date() }
      }
    ]

    beforeEach(() => {
      mockPrisma.templateCloudStorage.findMany.mockResolvedValue(mockCloudStorage)
      mockPrisma.templateSyncConflict.count.mockResolvedValue(2)
    })

    it('should return comprehensive sync status', async () => {
      const status = await service.getSyncStatus('user-1')

      expect(status.totalTemplates).toBe(3)
      expect(status.syncedTemplates).toBe(1)
      expect(status.pendingSync).toBe(1)
      expect(status.syncErrors).toBe(1)
      expect(status.conflicts).toBe(2)
      expect(status.lastSyncTime).toEqual(new Date('2024-01-15'))
    })

    it('should handle empty sync status', async () => {
      mockPrisma.templateCloudStorage.findMany.mockResolvedValue([])
      mockPrisma.templateSyncConflict.count.mockResolvedValue(0)

      const status = await service.getSyncStatus('user-1')

      expect(status.totalTemplates).toBe(0)
      expect(status.syncedTemplates).toBe(0)
      expect(status.conflicts).toBe(0)
    })
  })

  describe('calculateChecksum', () => {
    it('should generate consistent checksum for same data', () => {
      const data1 = { name: 'Test', description: 'Description' }
      const data2 = { description: 'Description', name: 'Test' }

      const checksum1 = service['calculateChecksum'](data1)
      const checksum2 = service['calculateChecksum'](data2)

      expect(checksum1).toBe(checksum2)
      expect(typeof checksum1).toBe('string')
      expect(checksum1.length).toBe(32) // MD5 hash length
    })

    it('should generate different checksums for different data', () => {
      const data1 = { name: 'Test1' }
      const data2 = { name: 'Test2' }

      const checksum1 = service['calculateChecksum'](data1)
      const checksum2 = service['calculateChecksum'](data2)

      expect(checksum1).not.toBe(checksum2)
    })
  })

  describe('error handling', () => {
    it('should handle database errors gracefully', async () => {
      mockPrisma.template.findMany.mockRejectedValue(new Error('Database error'))

      await expect(service.syncUserTemplates('user-1'))
        .rejects.toThrow('Failed to sync templates')
    })

    it('should handle invalid template data', async () => {
      const invalidTemplate = {
        id: 'template-1',
        name: null, // Invalid data
        cloudStorage: [],
        versions: []
      }

      mockPrisma.template.findUnique.mockResolvedValue(invalidTemplate)

      // Should handle gracefully without throwing
      await expect(service.syncSingleTemplate('template-1', 'user-1'))
        .resolves.not.toThrow()
    })
  })
})
