'use client';

import { ResumeSectionType, WorkExperience, Education } from '@careercraft/shared/types/resume';
import { TemplateRenderContext } from '@careercraft/shared/types/template';
import { cn } from '@/lib/utils';

interface ClassicTemplateProps {
  context: TemplateRenderContext;
  interactive?: boolean;
  preview?: boolean;
}

export function ClassicTemplate({ context, interactive = false, preview = false }: ClassicTemplateProps) {
  const { template, resume } = context;
  const { personalInfo, sections } = resume;
  const { style, layout } = template;

  // Helper functions
  const formatDate = (dateString: string) => {
    if (!dateString) return '';
    const date = new Date(dateString + '-01');
    return date.toLocaleDateString('en-US', { year: 'numeric', month: 'short' });
  };

  const formatDateRange = (startDate: string, endDate?: string, isCurrent?: boolean) => {
    const start = formatDate(startDate);
    if (isCurrent) return `${start} - Present`;
    if (!endDate) return start;
    return `${start} - ${formatDate(endDate)}`;
  };

  const getWorkExperience = () => {
    const workSection = sections.find(s => s.type === ResumeSectionType.WORK_EXPERIENCE);
    return (workSection?.data as WorkExperience[]) || [];
  };

  const getEducation = () => {
    const educationSection = sections.find(s => s.type === ResumeSectionType.EDUCATION);
    return (educationSection?.data as Education[]) || [];
  };

  // Style variables
  const cssVariables = {
    '--primary-color': style.colors.primary,
    '--secondary-color': style.colors.secondary,
    '--text-color': style.colors.text,
    '--text-light-color': style.colors.textLight,
    '--background-color': style.colors.background,
    '--border-color': style.colors.border,
    '--divider-color': style.colors.divider,
    '--font-family': style.fontFamily,
    '--font-size-h1': `${style.fontSize.heading1}px`,
    '--font-size-h2': `${style.fontSize.heading2}px`,
    '--font-size-h3': `${style.fontSize.heading3}px`,
    '--font-size-body': `${style.fontSize.body}px`,
    '--font-size-small': `${style.fontSize.small}px`,
    '--line-height-normal': style.lineHeight.normal,
    '--spacing-sm': `${style.spacing.sm}px`,
    '--spacing-md': `${style.spacing.md}px`,
    '--spacing-lg': `${style.spacing.lg}px`,
    '--page-margin-top': `${style.layout.pageMargin.top}px`,
    '--page-margin-right': `${style.layout.pageMargin.right}px`,
    '--page-margin-bottom': `${style.layout.pageMargin.bottom}px`,
    '--page-margin-left': `${style.layout.pageMargin.left}px`,
    '--section-spacing': `${style.layout.sectionSpacing}px`,
    '--item-spacing': `${style.layout.itemSpacing}px`,
  } as React.CSSProperties;

  return (
    <div 
      className="classic-template w-full max-w-4xl mx-auto bg-white text-gray-900 print:max-w-none print:mx-0"
      style={cssVariables}
    >
      {/* Header Section */}
      <header 
        className={cn(
          'text-center border-b-2 pb-6 mb-8',
          style.components.header.alignment === 'left' && 'text-left',
          style.components.header.alignment === 'right' && 'text-right'
        )}
        style={{
          borderColor: style.colors.primary,
          padding: `${style.spacing.lg}px ${style.layout.pageMargin.left}px`,
        }}
      >
        {/* Name */}
        <h1 
          className="font-bold mb-2 uppercase tracking-wide"
          style={{
            fontSize: style.fontSize.heading1,
            fontWeight: style.fontWeight.bold,
            color: style.colors.primary,
            lineHeight: style.lineHeight.tight,
            letterSpacing: '0.1em',
          }}
        >
          {personalInfo.firstName} {personalInfo.lastName}
        </h1>

        {/* Contact Information */}
        <div 
          className="flex flex-wrap justify-center gap-4 text-sm"
          style={{
            fontSize: style.fontSize.small,
            color: style.colors.text,
          }}
        >
          {personalInfo.email && <span>{personalInfo.email}</span>}
          {personalInfo.phone && <span>{personalInfo.phone}</span>}
          {personalInfo.location && <span>{personalInfo.location}</span>}
          {personalInfo.website && (
            <a 
              href={personalInfo.website} 
              className="hover:underline"
              style={{ color: style.colors.primary }}
            >
              {personalInfo.website.replace(/^https?:\/\//, '')}
            </a>
          )}
        </div>
      </header>

      {/* Main Content */}
      <main 
        className="px-8"
        style={{
          paddingLeft: style.layout.pageMargin.left,
          paddingRight: style.layout.pageMargin.right,
        }}
      >
        {/* Professional Summary */}
        {personalInfo.summary && (
          <section 
            className="mb-8"
            style={{ marginBottom: style.layout.sectionSpacing }}
          >
            <h2 
              className="font-bold mb-3 uppercase tracking-wide border-b pb-2"
              style={{
                fontSize: style.fontSize.heading2,
                fontWeight: style.fontWeight.bold,
                color: style.colors.primary,
                marginBottom: style.spacing.md,
                borderColor: style.colors.primary,
                letterSpacing: '0.05em',
              }}
            >
              Professional Summary
            </h2>
            <p 
              className="leading-relaxed text-justify"
              style={{
                fontSize: style.fontSize.body,
                lineHeight: style.lineHeight.relaxed,
                color: style.colors.text,
              }}
            >
              {personalInfo.summary}
            </p>
          </section>
        )}

        {/* Work Experience */}
        {getWorkExperience().length > 0 && (
          <section 
            className="mb-8"
            style={{ marginBottom: style.layout.sectionSpacing }}
          >
            <h2 
              className="font-bold mb-4 uppercase tracking-wide border-b pb-2"
              style={{
                fontSize: style.fontSize.heading2,
                fontWeight: style.fontWeight.bold,
                color: style.colors.primary,
                marginBottom: style.spacing.md,
                borderColor: style.colors.primary,
                letterSpacing: '0.05em',
              }}
            >
              Professional Experience
            </h2>
            <div className="space-y-6">
              {getWorkExperience().map((experience) => (
                <div 
                  key={experience.id}
                  style={{ marginBottom: style.layout.itemSpacing }}
                >
                  <div className="flex justify-between items-start mb-2">
                    <div>
                      <h3 
                        className="font-semibold"
                        style={{
                          fontSize: style.fontSize.heading3,
                          fontWeight: style.fontWeight.semibold,
                          color: style.colors.text,
                        }}
                      >
                        {experience.position}
                      </h3>
                      <p 
                        className="font-medium italic"
                        style={{
                          fontSize: style.fontSize.body,
                          fontWeight: style.fontWeight.medium,
                          color: style.colors.primary,
                          fontStyle: 'italic',
                        }}
                      >
                        {experience.company}, {experience.location}
                      </p>
                    </div>
                    <div 
                      className="text-sm text-right"
                      style={{
                        fontSize: style.fontSize.small,
                        color: style.colors.textLight,
                      }}
                    >
                      <p>{formatDateRange(experience.startDate, experience.endDate, experience.isCurrentRole)}</p>
                    </div>
                  </div>
                  
                  <p 
                    className="mb-3 leading-relaxed"
                    style={{
                      fontSize: style.fontSize.body,
                      lineHeight: style.lineHeight.normal,
                      color: style.colors.text,
                      marginBottom: style.spacing.sm,
                    }}
                  >
                    {experience.description}
                  </p>
                  
                  {experience.achievements.length > 0 && (
                    <ul 
                      className="space-y-1"
                      style={{
                        marginLeft: style.components.list.indentation,
                      }}
                    >
                      {experience.achievements.map((achievement, index) => (
                        <li 
                          key={index}
                          className="flex items-start"
                          style={{
                            fontSize: style.fontSize.body,
                            lineHeight: style.lineHeight.normal,
                            color: style.colors.text,
                          }}
                        >
                          <span 
                            className="inline-block mt-2 mr-3 flex-shrink-0"
                            style={{ color: style.colors.primary }}
                          >
                            •
                          </span>
                          <span>{achievement}</span>
                        </li>
                      ))}
                    </ul>
                  )}
                </div>
              ))}
            </div>
          </section>
        )}

        {/* Education */}
        {getEducation().length > 0 && (
          <section 
            className="mb-8"
            style={{ marginBottom: style.layout.sectionSpacing }}
          >
            <h2 
              className="font-bold mb-4 uppercase tracking-wide border-b pb-2"
              style={{
                fontSize: style.fontSize.heading2,
                fontWeight: style.fontWeight.bold,
                color: style.colors.primary,
                marginBottom: style.spacing.md,
                borderColor: style.colors.primary,
                letterSpacing: '0.05em',
              }}
            >
              Education
            </h2>
            <div className="space-y-4">
              {getEducation().map((education) => (
                <div 
                  key={education.id}
                  style={{ marginBottom: style.layout.itemSpacing }}
                >
                  <div className="flex justify-between items-start">
                    <div>
                      <h3 
                        className="font-semibold"
                        style={{
                          fontSize: style.fontSize.heading3,
                          fontWeight: style.fontWeight.semibold,
                          color: style.colors.text,
                        }}
                      >
                        {education.degree} in {education.field}
                      </h3>
                      <p 
                        className="font-medium italic"
                        style={{
                          fontSize: style.fontSize.body,
                          fontWeight: style.fontWeight.medium,
                          color: style.colors.primary,
                          fontStyle: 'italic',
                        }}
                      >
                        {education.institution}, {education.location}
                      </p>
                      {education.gpa && (
                        <p 
                          style={{
                            fontSize: style.fontSize.small,
                            color: style.colors.textLight,
                          }}
                        >
                          GPA: {education.gpa}
                        </p>
                      )}
                    </div>
                    <div 
                      className="text-sm text-right"
                      style={{
                        fontSize: style.fontSize.small,
                        color: style.colors.textLight,
                      }}
                    >
                      <p>{formatDateRange(education.startDate, education.endDate, education.isCurrentlyEnrolled)}</p>
                    </div>
                  </div>
                  
                  {education.honors && education.honors.length > 0 && (
                    <p 
                      className="mt-1"
                      style={{
                        fontSize: style.fontSize.small,
                        color: style.colors.text,
                      }}
                    >
                      <span className="font-medium">Honors: </span>
                      {education.honors.join(', ')}
                    </p>
                  )}
                </div>
              ))}
            </div>
          </section>
        )}
      </main>
    </div>
  );
}
