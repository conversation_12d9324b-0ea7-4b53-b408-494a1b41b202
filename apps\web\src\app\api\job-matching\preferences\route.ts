/**
 * Job Matching API - Preferences
 * 
 * Handles user job preferences and settings
 */

import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { jobMatchingService, UserJobPreferencesSchema } from '@/lib/job-matching/service'
import { aiRecommendationEngine } from '@/lib/job-matching/ai-engine'
import { prisma } from '@/lib/db'
import { z } from 'zod'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const action = searchParams.get('action')

    if (action === 'get' || !action) {
      // Get user's job preferences
      const preferences = await prisma.userJobPreferences.findUnique({
        where: { userId: session.user.id }
      })

      if (!preferences) {
        return NextResponse.json({
          success: true,
          preferences: null
        })
      }

      // Parse JSON fields
      const parsedPreferences = {
        ...preferences,
        preferredTitles: preferences.preferredTitles ? JSON.parse(preferences.preferredTitles) : [],
        preferredCompanies: preferences.preferredCompanies ? JSON.parse(preferences.preferredCompanies) : [],
        preferredLocations: preferences.preferredLocations ? JSON.parse(preferences.preferredLocations) : [],
        employmentTypes: preferences.employmentTypes ? JSON.parse(preferences.employmentTypes) : [],
        remotePreferences: preferences.remotePreferences ? JSON.parse(preferences.remotePreferences) : [],
        industryPreferences: preferences.industryPreferences ? JSON.parse(preferences.industryPreferences) : [],
        companySizePreferences: preferences.companySizePreferences ? JSON.parse(preferences.companySizePreferences) : [],
        notificationPreferences: preferences.notificationPreferences ? JSON.parse(preferences.notificationPreferences) : {}
      }

      return NextResponse.json({
        success: true,
        preferences: parsedPreferences
      })
    }

    if (action === 'career-path') {
      // Get career path predictions
      const userProfile = {
        currentTitle: 'Software Developer',
        skills: ['JavaScript', 'React', 'Node.js'],
        experience: []
      }

      const careerPath = await aiRecommendationEngine.predictCareerPath(userProfile)

      return NextResponse.json({
        success: true,
        careerPath
      })
    }

    if (action === 'skill-suggestions') {
      // Get skill suggestions based on user's current profile
      const skillSuggestions = [
        { skill: 'TypeScript', demand: 'High', category: 'Programming' },
        { skill: 'AWS', demand: 'High', category: 'Cloud' },
        { skill: 'Docker', demand: 'Medium', category: 'DevOps' },
        { skill: 'GraphQL', demand: 'Medium', category: 'API' },
        { skill: 'Machine Learning', demand: 'High', category: 'AI/ML' }
      ]

      return NextResponse.json({
        success: true,
        suggestions: skillSuggestions
      })
    }

    if (action === 'market-insights') {
      // Get job market insights
      const insights = {
        averageSalary: 95000,
        salaryRange: { min: 70000, max: 130000 },
        demandScore: 85,
        competitionLevel: 'Medium',
        trendingSkills: ['React', 'TypeScript', 'AWS', 'Python', 'Docker'],
        topCompanies: ['Google', 'Microsoft', 'Amazon', 'Meta', 'Apple'],
        locationInsights: [
          { location: 'San Francisco', averageSalary: 140000, jobCount: 1250 },
          { location: 'New York', averageSalary: 125000, jobCount: 980 },
          { location: 'Seattle', averageSalary: 130000, jobCount: 750 },
          { location: 'Austin', averageSalary: 110000, jobCount: 650 },
          { location: 'Remote', averageSalary: 105000, jobCount: 2100 }
        ]
      }

      return NextResponse.json({
        success: true,
        insights
      })
    }

    return NextResponse.json(
      { error: 'Invalid action' },
      { status: 400 }
    )
  } catch (error) {
    console.error('Get preferences error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const action = body.action

    if (action === 'update' || !action) {
      const preferences = {
        ...body,
        userId: session.user.id
      }

      const success = await jobMatchingService.updateJobPreferences(preferences)

      if (!success) {
        return NextResponse.json(
          { error: 'Failed to update preferences' },
          { status: 500 }
        )
      }

      return NextResponse.json({
        success: true,
        message: 'Preferences updated successfully'
      })
    }

    if (action === 'analyze-resume') {
      const { resumeId } = body

      if (!resumeId) {
        return NextResponse.json(
          { error: 'Resume ID required' },
          { status: 400 }
        )
      }

      // Mock resume data for analysis
      const resumeData = {
        personalInfo: { name: 'John Doe' },
        sections: {
          skills: [
            { name: 'Technical Skills', items: ['JavaScript', 'React', 'Node.js', 'Python'] }
          ],
          experience: [
            {
              position: 'Software Developer',
              company: 'Tech Corp',
              startDate: '2022-01',
              endDate: 'Present',
              description: 'Developed web applications using React and Node.js'
            }
          ],
          education: [
            {
              degree: 'Bachelor of Science',
              field: 'Computer Science',
              institution: 'University of Technology'
            }
          ]
        },
        title: 'Software Developer Resume'
      }

      const analysis = await aiRecommendationEngine.extractSkillsFromResume(resumeData)

      return NextResponse.json({
        success: true,
        analysis
      })
    }

    if (action === 'save-search') {
      const { searchCriteria, name } = body

      if (!searchCriteria || !name) {
        return NextResponse.json(
          { error: 'Search criteria and name required' },
          { status: 400 }
        )
      }

      // This would save the search criteria for future use
      // For now, we'll simulate success
      return NextResponse.json({
        success: true,
        message: 'Search saved successfully'
      })
    }

    if (action === 'set-alerts') {
      const { alertSettings } = body

      if (!alertSettings) {
        return NextResponse.json(
          { error: 'Alert settings required' },
          { status: 400 }
        )
      }

      // This would configure job alerts based on user preferences
      // For now, we'll simulate success
      return NextResponse.json({
        success: true,
        message: 'Job alerts configured successfully'
      })
    }

    return NextResponse.json(
      { error: 'Invalid action' },
      { status: 400 }
    )
  } catch (error) {
    console.error('Update preferences error:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const action = body.action

    if (action === 'update-notifications') {
      const { notificationSettings } = body

      // Update notification preferences
      const preferences = {
        userId: session.user.id,
        notificationPreferences: notificationSettings
      }

      const success = await jobMatchingService.updateJobPreferences(preferences)

      return NextResponse.json({
        success,
        message: success ? 'Notification settings updated' : 'Failed to update settings'
      })
    }

    if (action === 'update-salary-range') {
      const { salaryMin, salaryMax } = body

      if (typeof salaryMin !== 'number' || typeof salaryMax !== 'number') {
        return NextResponse.json(
          { error: 'Valid salary range required' },
          { status: 400 }
        )
      }

      const preferences = {
        userId: session.user.id,
        salaryMin,
        salaryMax
      }

      const success = await jobMatchingService.updateJobPreferences(preferences)

      return NextResponse.json({
        success,
        message: success ? 'Salary preferences updated' : 'Failed to update preferences'
      })
    }

    return NextResponse.json(
      { error: 'Invalid action' },
      { status: 400 }
    )
  } catch (error) {
    console.error('Update preferences error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
