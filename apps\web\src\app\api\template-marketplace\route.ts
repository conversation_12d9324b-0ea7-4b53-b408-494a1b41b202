/**
 * Template Marketplace API Routes
 * 
 * Handles template marketplace operations, discovery, and transactions
 */

import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { 
  templateMarketplaceService, 
  TemplateSearchSchema, 
  PublishTemplateSchema, 
  TemplateReviewSchema,
  TemplateCollectionSchema 
} from '@/lib/template-sync/marketplace-service'
import { z } from 'zod'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const action = searchParams.get('action')

    if (action === 'search') {
      // Search templates in marketplace
      const searchCriteria = {
        query: searchParams.get('query') || undefined,
        category: searchParams.get('category') || undefined,
        tags: searchParams.get('tags')?.split(',') || undefined,
        priceRange: {
          min: searchParams.get('priceMin') ? parseFloat(searchParams.get('priceMin')!) : undefined,
          max: searchParams.get('priceMax') ? parseFloat(searchParams.get('priceMax')!) : undefined
        },
        difficulty: searchParams.get('difficulty') as any || undefined,
        sortBy: searchParams.get('sortBy') as any || 'popular',
        limit: parseInt(searchParams.get('limit') || '20'),
        offset: parseInt(searchParams.get('offset') || '0')
      }

      const result = await templateMarketplaceService.searchTemplates(searchCriteria)
      
      return NextResponse.json({
        success: true,
        ...result
      })
    }

    if (action === 'featured') {
      // Get featured templates
      const limit = parseInt(searchParams.get('limit') || '10')
      const templates = await templateMarketplaceService.getFeaturedTemplates(limit)
      
      return NextResponse.json({
        success: true,
        templates
      })
    }

    if (action === 'categories') {
      // Get template categories
      const categories = [
        { id: 'professional', name: 'Professional', count: 45 },
        { id: 'creative', name: 'Creative', count: 32 },
        { id: 'modern', name: 'Modern', count: 28 },
        { id: 'classic', name: 'Classic', count: 21 },
        { id: 'minimal', name: 'Minimal', count: 19 },
        { id: 'academic', name: 'Academic', count: 15 },
        { id: 'technical', name: 'Technical', count: 12 },
        { id: 'executive', name: 'Executive', count: 8 }
      ]
      
      return NextResponse.json({
        success: true,
        categories
      })
    }

    if (action === 'reviews') {
      // Get template reviews
      const templateId = searchParams.get('templateId')
      const limit = parseInt(searchParams.get('limit') || '20')
      const offset = parseInt(searchParams.get('offset') || '0')
      
      if (!templateId) {
        return NextResponse.json(
          { error: 'Template ID required' },
          { status: 400 }
        )
      }

      const reviews = await templateMarketplaceService.getTemplateReviews(templateId, limit, offset)
      
      return NextResponse.json({
        success: true,
        reviews
      })
    }

    if (action === 'analytics') {
      // Get template analytics (requires authentication)
      const session = await getServerSession(authOptions)
      
      if (!session?.user?.id) {
        return NextResponse.json(
          { error: 'Unauthorized' },
          { status: 401 }
        )
      }

      const templateId = searchParams.get('templateId')
      
      if (!templateId) {
        return NextResponse.json(
          { error: 'Template ID required' },
          { status: 400 }
        )
      }

      const analytics = await templateMarketplaceService.getTemplateAnalytics(templateId, session.user.id)
      
      return NextResponse.json({
        success: true,
        analytics
      })
    }

    if (action === 'collections') {
      // Get template collections
      const session = await getServerSession(authOptions)
      
      if (session?.user?.id) {
        const collections = await templateMarketplaceService.getUserCollections(session.user.id)
        
        return NextResponse.json({
          success: true,
          collections
        })
      } else {
        // Return public collections only
        const collections = await templateMarketplaceService.getUserCollections('')
        
        return NextResponse.json({
          success: true,
          collections: collections.filter(c => c.isPublic)
        })
      }
    }

    return NextResponse.json(
      { error: 'Invalid action' },
      { status: 400 }
    )
  } catch (error) {
    console.error('Template marketplace GET error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const action = body.action

    if (action === 'publish') {
      // Publish template to marketplace
      const publishData = PublishTemplateSchema.parse(body)
      
      const listingId = await templateMarketplaceService.publishTemplate(publishData, session.user.id)
      
      return NextResponse.json({
        success: true,
        listingId,
        message: 'Template submitted for review'
      })
    }

    if (action === 'purchase') {
      // Purchase template
      const { templateId } = body
      
      if (!templateId) {
        return NextResponse.json(
          { error: 'Template ID required' },
          { status: 400 }
        )
      }

      const result = await templateMarketplaceService.purchaseTemplate(templateId, session.user.id)
      
      return NextResponse.json({
        success: result.success,
        purchaseId: result.purchaseId,
        templateData: result.templateData,
        message: result.success ? 'Template purchased successfully' : 'Purchase failed'
      })
    }

    if (action === 'review') {
      // Add template review
      const reviewData = TemplateReviewSchema.parse(body)
      
      await templateMarketplaceService.addReview(reviewData, session.user.id)
      
      return NextResponse.json({
        success: true,
        message: 'Review added successfully'
      })
    }

    if (action === 'create-collection') {
      // Create template collection
      const collectionData = TemplateCollectionSchema.parse(body)
      
      const collectionId = await templateMarketplaceService.createCollection(collectionData, session.user.id)
      
      return NextResponse.json({
        success: true,
        collectionId,
        message: 'Collection created successfully'
      })
    }

    if (action === 'track-usage') {
      // Track template usage
      const { templateId, actionType, metadata } = body
      
      if (!templateId || !actionType) {
        return NextResponse.json(
          { error: 'Template ID and action type required' },
          { status: 400 }
        )
      }

      await templateMarketplaceService.trackTemplateUsage(
        templateId,
        session.user.id,
        actionType,
        metadata
      )
      
      return NextResponse.json({
        success: true,
        message: 'Usage tracked successfully'
      })
    }

    if (action === 'report-template') {
      // Report inappropriate template
      const { templateId, reason, description } = body
      
      if (!templateId || !reason) {
        return NextResponse.json(
          { error: 'Template ID and reason required' },
          { status: 400 }
        )
      }

      // This would create a report record in the database
      // For now, we'll just return success
      return NextResponse.json({
        success: true,
        message: 'Template reported successfully'
      })
    }

    return NextResponse.json(
      { error: 'Invalid action' },
      { status: 400 }
    )
  } catch (error) {
    console.error('Template marketplace POST error:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const action = body.action

    if (action === 'update-listing') {
      // Update marketplace listing
      const { templateId, updates } = body
      
      if (!templateId) {
        return NextResponse.json(
          { error: 'Template ID required' },
          { status: 400 }
        )
      }

      // This would update the marketplace listing
      // For now, we'll just return success
      return NextResponse.json({
        success: true,
        message: 'Listing updated successfully'
      })
    }

    if (action === 'update-review') {
      // Update template review
      const reviewData = TemplateReviewSchema.parse(body)
      
      await templateMarketplaceService.addReview(reviewData, session.user.id)
      
      return NextResponse.json({
        success: true,
        message: 'Review updated successfully'
      })
    }

    if (action === 'feature-template') {
      // Feature/unfeature template (admin only)
      const { templateId, featured } = body
      
      if (!templateId || typeof featured !== 'boolean') {
        return NextResponse.json(
          { error: 'Template ID and featured status required' },
          { status: 400 }
        )
      }

      // Check if user is admin
      // This would be implemented with proper role checking
      
      return NextResponse.json({
        success: true,
        message: `Template ${featured ? 'featured' : 'unfeatured'} successfully`
      })
    }

    if (action === 'approve-template') {
      // Approve template for marketplace (admin only)
      const { templateId, approved } = body
      
      if (!templateId || typeof approved !== 'boolean') {
        return NextResponse.json(
          { error: 'Template ID and approval status required' },
          { status: 400 }
        )
      }

      // Check if user is admin
      // This would be implemented with proper role checking
      
      return NextResponse.json({
        success: true,
        message: `Template ${approved ? 'approved' : 'rejected'} successfully`
      })
    }

    return NextResponse.json(
      { error: 'Invalid action' },
      { status: 400 }
    )
  } catch (error) {
    console.error('Template marketplace PUT error:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const action = searchParams.get('action')
    const templateId = searchParams.get('templateId')

    if (action === 'unpublish') {
      // Unpublish template from marketplace
      if (!templateId) {
        return NextResponse.json(
          { error: 'Template ID required' },
          { status: 400 }
        )
      }

      // This would remove the template from marketplace
      // For now, we'll just return success
      return NextResponse.json({
        success: true,
        message: 'Template unpublished successfully'
      })
    }

    if (action === 'delete-review') {
      // Delete template review
      if (!templateId) {
        return NextResponse.json(
          { error: 'Template ID required' },
          { status: 400 }
        )
      }

      // This would delete the user's review for the template
      // For now, we'll just return success
      return NextResponse.json({
        success: true,
        message: 'Review deleted successfully'
      })
    }

    if (action === 'delete-collection') {
      // Delete template collection
      const collectionId = searchParams.get('collectionId')
      
      if (!collectionId) {
        return NextResponse.json(
          { error: 'Collection ID required' },
          { status: 400 }
        )
      }

      // This would delete the collection
      // For now, we'll just return success
      return NextResponse.json({
        success: true,
        message: 'Collection deleted successfully'
      })
    }

    return NextResponse.json(
      { error: 'Invalid action' },
      { status: 400 }
    )
  } catch (error) {
    console.error('Template marketplace DELETE error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
