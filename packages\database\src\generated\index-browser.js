
Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('./runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 5.22.0
 * Query Engine version: 605197351a3c8bdd595af2d2a9bc3025bca48ea2
 */
Prisma.prismaVersion = {
  client: "5.22.0",
  engine: "605197351a3c8bdd595af2d2a9bc3025bca48ea2"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.NotFoundError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`NotFoundError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  Serializable: 'Serializable'
});

exports.Prisma.UserScalarFieldEnum = {
  id: 'id',
  email: 'email',
  name: 'name',
  image: 'image',
  emailVerified: 'emailVerified',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.AccountScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  type: 'type',
  provider: 'provider',
  providerAccountId: 'providerAccountId',
  refresh_token: 'refresh_token',
  access_token: 'access_token',
  expires_at: 'expires_at',
  token_type: 'token_type',
  scope: 'scope',
  id_token: 'id_token',
  session_state: 'session_state'
};

exports.Prisma.SessionScalarFieldEnum = {
  id: 'id',
  sessionToken: 'sessionToken',
  userId: 'userId',
  expires: 'expires'
};

exports.Prisma.VerificationTokenScalarFieldEnum = {
  identifier: 'identifier',
  token: 'token',
  expires: 'expires'
};

exports.Prisma.UserProfileScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  firstName: 'firstName',
  lastName: 'lastName',
  phone: 'phone',
  location: 'location',
  website: 'website',
  linkedinUrl: 'linkedinUrl',
  githubUrl: 'githubUrl',
  bio: 'bio',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ResumeScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  title: 'title',
  description: 'description',
  templateId: 'templateId',
  isPublic: 'isPublic',
  publicUrl: 'publicUrl',
  status: 'status',
  personalInfo: 'personalInfo',
  sections: 'sections',
  settings: 'settings',
  metadata: 'metadata',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ExperienceScalarFieldEnum = {
  id: 'id',
  resumeId: 'resumeId',
  company: 'company',
  position: 'position',
  location: 'location',
  startDate: 'startDate',
  endDate: 'endDate',
  isCurrent: 'isCurrent',
  description: 'description',
  achievements: 'achievements',
  displayOrder: 'displayOrder',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.EducationScalarFieldEnum = {
  id: 'id',
  resumeId: 'resumeId',
  institution: 'institution',
  degree: 'degree',
  field: 'field',
  location: 'location',
  startDate: 'startDate',
  endDate: 'endDate',
  gpa: 'gpa',
  description: 'description',
  displayOrder: 'displayOrder',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SkillScalarFieldEnum = {
  id: 'id',
  resumeId: 'resumeId',
  name: 'name',
  category: 'category',
  level: 'level',
  displayOrder: 'displayOrder',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ProjectScalarFieldEnum = {
  id: 'id',
  resumeId: 'resumeId',
  name: 'name',
  description: 'description',
  url: 'url',
  githubUrl: 'githubUrl',
  technologies: 'technologies',
  startDate: 'startDate',
  endDate: 'endDate',
  displayOrder: 'displayOrder',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.TemplateScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  category: 'category',
  tags: 'tags',
  isPremium: 'isPremium',
  isActive: 'isActive',
  isPublic: 'isPublic',
  isOfficial: 'isOfficial',
  config: 'config',
  preview: 'preview',
  thumbnail: 'thumbnail',
  difficulty: 'difficulty',
  usageCount: 'usageCount',
  rating: 'rating',
  reviewCount: 'reviewCount',
  createdBy: 'createdBy',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.TemplateCloudStorageScalarFieldEnum = {
  id: 'id',
  templateId: 'templateId',
  userId: 'userId',
  cloudUrl: 'cloudUrl',
  storageProvider: 'storageProvider',
  fileSize: 'fileSize',
  checksum: 'checksum',
  lastSynced: 'lastSynced',
  syncStatus: 'syncStatus',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.TemplateVersionScalarFieldEnum = {
  id: 'id',
  templateId: 'templateId',
  versionNumber: 'versionNumber',
  versionName: 'versionName',
  changesSummary: 'changesSummary',
  templateData: 'templateData',
  createdBy: 'createdBy',
  createdAt: 'createdAt'
};

exports.Prisma.TemplateMarketplaceScalarFieldEnum = {
  id: 'id',
  templateId: 'templateId',
  sellerId: 'sellerId',
  price: 'price',
  isFeatured: 'isFeatured',
  downloadCount: 'downloadCount',
  status: 'status',
  approvedBy: 'approvedBy',
  approvedAt: 'approvedAt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.TemplatePurchaseScalarFieldEnum = {
  id: 'id',
  templateMarketplaceId: 'templateMarketplaceId',
  userId: 'userId',
  price: 'price',
  paymentMethod: 'paymentMethod',
  paymentId: 'paymentId',
  purchasedAt: 'purchasedAt'
};

exports.Prisma.TemplateReviewScalarFieldEnum = {
  id: 'id',
  templateId: 'templateId',
  userId: 'userId',
  rating: 'rating',
  reviewText: 'reviewText',
  isVerifiedPurchase: 'isVerifiedPurchase',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.TemplateCollectionScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  createdBy: 'createdBy',
  isPublic: 'isPublic',
  isOfficial: 'isOfficial',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.TemplateCollectionItemScalarFieldEnum = {
  id: 'id',
  collectionId: 'collectionId',
  templateId: 'templateId',
  orderIndex: 'orderIndex',
  addedAt: 'addedAt'
};

exports.Prisma.TemplateShareScalarFieldEnum = {
  id: 'id',
  templateId: 'templateId',
  sharedBy: 'sharedBy',
  sharedWith: 'sharedWith',
  permissionLevel: 'permissionLevel',
  shareToken: 'shareToken',
  expiresAt: 'expiresAt',
  createdAt: 'createdAt'
};

exports.Prisma.TemplateSyncConflictScalarFieldEnum = {
  id: 'id',
  templateId: 'templateId',
  userId: 'userId',
  conflictType: 'conflictType',
  localVersion: 'localVersion',
  remoteVersion: 'remoteVersion',
  resolutionStrategy: 'resolutionStrategy',
  resolvedAt: 'resolvedAt',
  createdAt: 'createdAt'
};

exports.Prisma.TemplateUsageAnalyticsScalarFieldEnum = {
  id: 'id',
  templateId: 'templateId',
  userId: 'userId',
  actionType: 'actionType',
  deviceType: 'deviceType',
  userAgent: 'userAgent',
  ipAddress: 'ipAddress',
  metadata: 'metadata',
  createdAt: 'createdAt'
};

exports.Prisma.UserProfileVectorScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  resumeId: 'resumeId',
  profileVector: 'profileVector',
  skillsExtracted: 'skillsExtracted',
  experienceLevel: 'experienceLevel',
  primaryRole: 'primaryRole',
  industries: 'industries',
  locations: 'locations',
  lastUpdated: 'lastUpdated',
  createdAt: 'createdAt'
};

exports.Prisma.MarketAnalysisScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  resumeId: 'resumeId',
  analysisType: 'analysisType',
  salaryEstimateMin: 'salaryEstimateMin',
  salaryEstimateMax: 'salaryEstimateMax',
  confidenceLevel: 'confidenceLevel',
  marketFitScore: 'marketFitScore',
  skillGaps: 'skillGaps',
  careerOpportunities: 'careerOpportunities',
  competitiveAnalysis: 'competitiveAnalysis',
  recommendations: 'recommendations',
  dataPoints: 'dataPoints',
  lastRefreshed: 'lastRefreshed',
  createdAt: 'createdAt'
};

exports.Prisma.JobMatchScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  resumeId: 'resumeId',
  jobPostingId: 'jobPostingId',
  compatibilityScore: 'compatibilityScore',
  skillsScore: 'skillsScore',
  experienceScore: 'experienceScore',
  locationScore: 'locationScore',
  salaryScore: 'salaryScore',
  matchReasons: 'matchReasons',
  skillGaps: 'skillGaps',
  recommendations: 'recommendations',
  isBookmarked: 'isBookmarked',
  isApplied: 'isApplied',
  createdAt: 'createdAt'
};

exports.Prisma.SubscriptionPlanScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  priceMonthly: 'priceMonthly',
  priceYearly: 'priceYearly',
  stripePriceIdMonthly: 'stripePriceIdMonthly',
  stripePriceIdYearly: 'stripePriceIdYearly',
  features: 'features',
  maxResumes: 'maxResumes',
  maxTemplates: 'maxTemplates',
  maxCollaborators: 'maxCollaborators',
  aiSuggestionsLimit: 'aiSuggestionsLimit',
  isActive: 'isActive',
  sortOrder: 'sortOrder',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.UserSubscriptionScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  planId: 'planId',
  stripeCustomerId: 'stripeCustomerId',
  stripeSubscriptionId: 'stripeSubscriptionId',
  status: 'status',
  currentPeriodStart: 'currentPeriodStart',
  currentPeriodEnd: 'currentPeriodEnd',
  cancelAtPeriodEnd: 'cancelAtPeriodEnd',
  canceledAt: 'canceledAt',
  trialStart: 'trialStart',
  trialEnd: 'trialEnd',
  metadata: 'metadata',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.PaymentScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  subscriptionId: 'subscriptionId',
  stripePaymentIntentId: 'stripePaymentIntentId',
  amount: 'amount',
  currency: 'currency',
  status: 'status',
  paymentMethod: 'paymentMethod',
  description: 'description',
  metadata: 'metadata',
  createdAt: 'createdAt'
};

exports.Prisma.FeatureUsageScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  featureName: 'featureName',
  usageCount: 'usageCount',
  usageDate: 'usageDate',
  metadata: 'metadata',
  createdAt: 'createdAt'
};

exports.Prisma.BillingEventScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  eventType: 'eventType',
  stripeEventId: 'stripeEventId',
  data: 'data',
  processed: 'processed',
  createdAt: 'createdAt'
};

exports.Prisma.CouponScalarFieldEnum = {
  id: 'id',
  code: 'code',
  name: 'name',
  description: 'description',
  discountType: 'discountType',
  discountValue: 'discountValue',
  stripeCouponId: 'stripeCouponId',
  maxRedemptions: 'maxRedemptions',
  currentRedemptions: 'currentRedemptions',
  validFrom: 'validFrom',
  validUntil: 'validUntil',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.CouponUsageScalarFieldEnum = {
  id: 'id',
  couponId: 'couponId',
  userId: 'userId',
  subscriptionId: 'subscriptionId',
  usedAt: 'usedAt'
};

exports.Prisma.CoverLetterScalarFieldEnum = {
  id: 'id',
  resumeId: 'resumeId',
  title: 'title',
  content: 'content',
  jobTitle: 'jobTitle',
  company: 'company',
  jobDescription: 'jobDescription',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.AIGenerationScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  type: 'type',
  prompt: 'prompt',
  response: 'response',
  metadata: 'metadata',
  createdAt: 'createdAt'
};

exports.Prisma.LinkedInProfileScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  linkedinId: 'linkedinId',
  profileData: 'profileData',
  lastSynced: 'lastSynced',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.LinkedInImportScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  resumeId: 'resumeId',
  profileId: 'profileId',
  importedData: 'importedData',
  importStatus: 'importStatus',
  createdAt: 'createdAt'
};

exports.Prisma.CollaborationSessionScalarFieldEnum = {
  id: 'id',
  resumeId: 'resumeId',
  ownerId: 'ownerId',
  sessionToken: 'sessionToken',
  expiresAt: 'expiresAt',
  createdAt: 'createdAt'
};

exports.Prisma.CollaborationPermissionScalarFieldEnum = {
  id: 'id',
  sessionId: 'sessionId',
  userId: 'userId',
  permissionLevel: 'permissionLevel',
  grantedBy: 'grantedBy',
  createdAt: 'createdAt'
};

exports.Prisma.CollaborationChangeScalarFieldEnum = {
  id: 'id',
  sessionId: 'sessionId',
  userId: 'userId',
  changeType: 'changeType',
  changeData: 'changeData',
  timestamp: 'timestamp'
};

exports.Prisma.CollaborationCommentScalarFieldEnum = {
  id: 'id',
  sessionId: 'sessionId',
  userId: 'userId',
  parentId: 'parentId',
  sectionPath: 'sectionPath',
  content: 'content',
  isResolved: 'isResolved',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.CollaborationCursorScalarFieldEnum = {
  id: 'id',
  sessionId: 'sessionId',
  userId: 'userId',
  sectionPath: 'sectionPath',
  position: 'position',
  updatedAt: 'updatedAt'
};

exports.Prisma.CollaborationPresenceScalarFieldEnum = {
  id: 'id',
  sessionId: 'sessionId',
  userId: 'userId',
  status: 'status',
  lastSeen: 'lastSeen'
};

exports.Prisma.ResumeVersionScalarFieldEnum = {
  id: 'id',
  resumeId: 'resumeId',
  versionNumber: 'versionNumber',
  versionName: 'versionName',
  contentSnapshot: 'contentSnapshot',
  changeSummary: 'changeSummary',
  changeType: 'changeType',
  createdBy: 'createdBy',
  createdAt: 'createdAt',
  metadata: 'metadata'
};

exports.Prisma.VersionComparisonScalarFieldEnum = {
  id: 'id',
  resumeId: 'resumeId',
  versionFrom: 'versionFrom',
  versionTo: 'versionTo',
  diffData: 'diffData',
  createdAt: 'createdAt'
};

exports.Prisma.ResumeBackupScalarFieldEnum = {
  id: 'id',
  resumeId: 'resumeId',
  backupName: 'backupName',
  backupData: 'backupData',
  backupType: 'backupType',
  createdBy: 'createdBy',
  createdAt: 'createdAt',
  expiresAt: 'expiresAt',
  metadata: 'metadata'
};

exports.Prisma.VersionActivityScalarFieldEnum = {
  id: 'id',
  resumeId: 'resumeId',
  versionId: 'versionId',
  activityType: 'activityType',
  userId: 'userId',
  activityData: 'activityData',
  createdAt: 'createdAt'
};

exports.Prisma.JobPostingScalarFieldEnum = {
  id: 'id',
  externalId: 'externalId',
  title: 'title',
  company: 'company',
  description: 'description',
  requirements: 'requirements',
  location: 'location',
  salaryMin: 'salaryMin',
  salaryMax: 'salaryMax',
  employmentType: 'employmentType',
  remoteType: 'remoteType',
  experienceLevel: 'experienceLevel',
  skills: 'skills',
  benefits: 'benefits',
  postedDate: 'postedDate',
  expiresDate: 'expiresDate',
  source: 'source',
  sourceUrl: 'sourceUrl',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.JobApplicationScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  jobPostingId: 'jobPostingId',
  resumeId: 'resumeId',
  coverLetterId: 'coverLetterId',
  status: 'status',
  appliedDate: 'appliedDate',
  lastUpdated: 'lastUpdated',
  notes: 'notes',
  interviewDates: 'interviewDates',
  followUpDate: 'followUpDate',
  salaryOffered: 'salaryOffered',
  metadata: 'metadata',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.JobRecommendationScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  jobPostingId: 'jobPostingId',
  matchScore: 'matchScore',
  reasoning: 'reasoning',
  isViewed: 'isViewed',
  isSaved: 'isSaved',
  isDismissed: 'isDismissed',
  recommendedAt: 'recommendedAt',
  viewedAt: 'viewedAt',
  createdAt: 'createdAt'
};

exports.Prisma.UserJobPreferencesScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  preferredTitles: 'preferredTitles',
  preferredCompanies: 'preferredCompanies',
  preferredLocations: 'preferredLocations',
  salaryMin: 'salaryMin',
  salaryMax: 'salaryMax',
  employmentTypes: 'employmentTypes',
  remotePreferences: 'remotePreferences',
  experienceLevel: 'experienceLevel',
  industryPreferences: 'industryPreferences',
  companySizePreferences: 'companySizePreferences',
  notificationPreferences: 'notificationPreferences',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SkillAssessmentScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  skillName: 'skillName',
  proficiencyLevel: 'proficiencyLevel',
  assessmentScore: 'assessmentScore',
  assessmentDate: 'assessmentDate',
  verified: 'verified',
  verificationSource: 'verificationSource',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.InterviewPreparationScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  jobApplicationId: 'jobApplicationId',
  interviewType: 'interviewType',
  scheduledDate: 'scheduledDate',
  preparationNotes: 'preparationNotes',
  questionsPracticed: 'questionsPracticed',
  mockInterviewScores: 'mockInterviewScores',
  feedback: 'feedback',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};


exports.Prisma.ModelName = {
  User: 'User',
  Account: 'Account',
  Session: 'Session',
  VerificationToken: 'VerificationToken',
  UserProfile: 'UserProfile',
  Resume: 'Resume',
  Experience: 'Experience',
  Education: 'Education',
  Skill: 'Skill',
  Project: 'Project',
  Template: 'Template',
  TemplateCloudStorage: 'TemplateCloudStorage',
  TemplateVersion: 'TemplateVersion',
  TemplateMarketplace: 'TemplateMarketplace',
  TemplatePurchase: 'TemplatePurchase',
  TemplateReview: 'TemplateReview',
  TemplateCollection: 'TemplateCollection',
  TemplateCollectionItem: 'TemplateCollectionItem',
  TemplateShare: 'TemplateShare',
  TemplateSyncConflict: 'TemplateSyncConflict',
  TemplateUsageAnalytics: 'TemplateUsageAnalytics',
  UserProfileVector: 'UserProfileVector',
  MarketAnalysis: 'MarketAnalysis',
  JobMatch: 'JobMatch',
  SubscriptionPlan: 'SubscriptionPlan',
  UserSubscription: 'UserSubscription',
  Payment: 'Payment',
  FeatureUsage: 'FeatureUsage',
  BillingEvent: 'BillingEvent',
  Coupon: 'Coupon',
  CouponUsage: 'CouponUsage',
  CoverLetter: 'CoverLetter',
  AIGeneration: 'AIGeneration',
  LinkedInProfile: 'LinkedInProfile',
  LinkedInImport: 'LinkedInImport',
  CollaborationSession: 'CollaborationSession',
  CollaborationPermission: 'CollaborationPermission',
  CollaborationChange: 'CollaborationChange',
  CollaborationComment: 'CollaborationComment',
  CollaborationCursor: 'CollaborationCursor',
  CollaborationPresence: 'CollaborationPresence',
  ResumeVersion: 'ResumeVersion',
  VersionComparison: 'VersionComparison',
  ResumeBackup: 'ResumeBackup',
  VersionActivity: 'VersionActivity',
  JobPosting: 'JobPosting',
  JobApplication: 'JobApplication',
  JobRecommendation: 'JobRecommendation',
  UserJobPreferences: 'UserJobPreferences',
  SkillAssessment: 'SkillAssessment',
  InterviewPreparation: 'InterviewPreparation'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }
        
        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
