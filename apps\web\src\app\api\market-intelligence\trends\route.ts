/**
 * Market Trends Analysis API
 * Provides detailed trend analysis and predictions
 * Part of Milestone 1.3: Market Analysis Engine
 */

import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/database'
import { logger } from '@/lib/logger'
import { rateLimit } from '@/lib/rate-limit'

export async function GET(request: NextRequest) {
  try {
    // Rate limiting
    const rateLimitResult = await rateLimit(request)
    if (!rateLimitResult.success) {
      return NextResponse.json(
        { error: 'Rate limit exceeded' },
        { status: 429 }
      )
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url)
    const trendType = searchParams.get('type') || 'all'
    const timeframe = searchParams.get('timeframe') || '30d'
    const industry = searchParams.get('industry')
    const location = searchParams.get('location')
    const skill = searchParams.get('skill')

    logger.info('Market trends request', {
      trendType,
      timeframe,
      industry,
      location,
      skill
    })

    let trends = {}

    switch (trendType) {
      case 'salary':
        trends = await getSalaryTrends(timeframe, industry, location)
        break
      case 'skills':
        trends = await getSkillTrends(timeframe, skill)
        break
      case 'demand':
        trends = await getDemandTrends(timeframe, industry, location)
        break
      case 'industry':
        trends = await getIndustryTrends(timeframe)
        break
      case 'location':
        trends = await getLocationTrends(timeframe)
        break
      case 'all':
      default:
        trends = {
          salary: await getSalaryTrends(timeframe, industry, location),
          skills: await getSkillTrends(timeframe, skill),
          demand: await getDemandTrends(timeframe, industry, location),
          industry: await getIndustryTrends(timeframe),
          location: await getLocationTrends(timeframe)
        }
        break
    }

    return NextResponse.json({
      success: true,
      data: trends,
      meta: {
        trendType,
        timeframe,
        filters: { industry, location, skill },
        generatedAt: new Date().toISOString()
      }
    })

  } catch (error) {
    logger.error('Market trends API error', { error })
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch market trends',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      },
      { status: 500 }
    )
  }
}

// Helper functions for different trend types
async function getSalaryTrends(timeframe: string, industry?: string, location?: string) {
  const daysBack = parseTimeframe(timeframe)
  const cutoffDate = new Date(Date.now() - daysBack * 24 * 60 * 60 * 1000)

  const filters: any = {
    scrapedAt: { gte: cutoffDate },
    salaryMin: { not: null },
    salaryMax: { not: null },
    isActive: true
  }

  if (industry) {
    filters.industry = { contains: industry, mode: 'insensitive' }
  }

  if (location) {
    filters.location = { contains: location, mode: 'insensitive' }
  }

  const jobs = await prisma.jobPosting.findMany({
    where: filters,
    select: {
      salaryMin: true,
      salaryMax: true,
      experienceLevel: true,
      scrapedAt: true,
      industry: true,
      location: true
    }
  })

  // Group by time periods
  const trends = groupByTimePeriod(jobs, daysBack, (job) => ({
    avgSalary: (job.salaryMin + job.salaryMax) / 2,
    minSalary: job.salaryMin,
    maxSalary: job.salaryMax
  }))

  // Calculate by experience level
  const byExperience = groupByField(jobs, 'experienceLevel', (job) => ({
    avgSalary: (job.salaryMin + job.salaryMax) / 2,
    count: 1
  }))

  return {
    timeline: trends,
    byExperience,
    summary: {
      totalJobs: jobs.length,
      avgSalary: jobs.reduce((sum, job) => sum + (job.salaryMin + job.salaryMax) / 2, 0) / jobs.length,
      salaryRange: {
        min: Math.min(...jobs.map(job => job.salaryMin)),
        max: Math.max(...jobs.map(job => job.salaryMax))
      }
    }
  }
}

async function getSkillTrends(timeframe: string, skill?: string) {
  const daysBack = parseTimeframe(timeframe)
  const cutoffDate = new Date(Date.now() - daysBack * 24 * 60 * 60 * 1000)

  const filters: any = {
    scrapedAt: { gte: cutoffDate },
    skills: { not: null },
    isActive: true
  }

  if (skill) {
    filters.skills = { has: skill }
  }

  const jobs = await prisma.jobPosting.findMany({
    where: filters,
    select: {
      skills: true,
      scrapedAt: true,
      salaryMin: true,
      salaryMax: true,
      experienceLevel: true
    }
  })

  // Count skill occurrences
  const skillCounts = new Map<string, { count: number; salaries: number[]; levels: string[] }>()
  
  jobs.forEach(job => {
    if (job.skills && Array.isArray(job.skills)) {
      job.skills.forEach(skillName => {
        if (!skillCounts.has(skillName)) {
          skillCounts.set(skillName, { count: 0, salaries: [], levels: [] })
        }
        const skillData = skillCounts.get(skillName)!
        skillData.count++
        
        if (job.salaryMin && job.salaryMax) {
          skillData.salaries.push((job.salaryMin + job.salaryMax) / 2)
        }
        
        if (job.experienceLevel) {
          skillData.levels.push(job.experienceLevel)
        }
      })
    }
  })

  // Convert to sorted array
  const skillTrends = Array.from(skillCounts.entries())
    .map(([skillName, data]) => ({
      skill: skillName,
      demand: data.count,
      avgSalary: data.salaries.length > 0 
        ? data.salaries.reduce((sum, sal) => sum + sal, 0) / data.salaries.length 
        : 0,
      growth: calculateGrowthRate(skillName, daysBack), // Simplified
      experienceLevels: countOccurrences(data.levels)
    }))
    .sort((a, b) => b.demand - a.demand)
    .slice(0, 50)

  return {
    topSkills: skillTrends.slice(0, 20),
    emergingSkills: skillTrends.filter(s => s.growth > 15).slice(0, 10),
    highPayingSkills: skillTrends.sort((a, b) => b.avgSalary - a.avgSalary).slice(0, 15),
    summary: {
      totalSkills: skillCounts.size,
      totalJobs: jobs.length,
      avgSkillsPerJob: jobs.reduce((sum, job) => sum + (job.skills?.length || 0), 0) / jobs.length
    }
  }
}

async function getDemandTrends(timeframe: string, industry?: string, location?: string) {
  const daysBack = parseTimeframe(timeframe)
  const cutoffDate = new Date(Date.now() - daysBack * 24 * 60 * 60 * 1000)

  const filters: any = {
    scrapedAt: { gte: cutoffDate },
    isActive: true
  }

  if (industry) {
    filters.industry = { contains: industry, mode: 'insensitive' }
  }

  if (location) {
    filters.location = { contains: location, mode: 'insensitive' }
  }

  const jobs = await prisma.jobPosting.findMany({
    where: filters,
    select: {
      scrapedAt: true,
      title: true,
      company: true,
      experienceLevel: true,
      jobType: true
    }
  })

  // Group by time periods
  const demandTimeline = groupByTimePeriod(jobs, daysBack, () => ({ count: 1 }))

  // Group by job titles
  const titleCounts = groupByField(jobs, 'title', () => ({ count: 1 }))
  const topRoles = Object.entries(titleCounts)
    .sort(([,a], [,b]) => b.count - a.count)
    .slice(0, 20)
    .map(([title, data]) => ({ title, demand: data.count }))

  // Group by companies
  const companyCounts = groupByField(jobs, 'company', () => ({ count: 1 }))
  const topHiringCompanies = Object.entries(companyCounts)
    .sort(([,a], [,b]) => b.count - a.count)
    .slice(0, 15)
    .map(([company, data]) => ({ company, jobCount: data.count }))

  return {
    timeline: demandTimeline,
    topRoles,
    topHiringCompanies,
    byJobType: groupByField(jobs, 'jobType', () => ({ count: 1 })),
    byExperienceLevel: groupByField(jobs, 'experienceLevel', () => ({ count: 1 })),
    summary: {
      totalJobs: jobs.length,
      avgJobsPerDay: jobs.length / daysBack,
      uniqueCompanies: new Set(jobs.map(job => job.company)).size,
      uniqueRoles: new Set(jobs.map(job => job.title)).size
    }
  }
}

async function getIndustryTrends(timeframe: string) {
  const daysBack = parseTimeframe(timeframe)
  const cutoffDate = new Date(Date.now() - daysBack * 24 * 60 * 60 * 1000)

  const jobs = await prisma.jobPosting.findMany({
    where: {
      scrapedAt: { gte: cutoffDate },
      industry: { not: null },
      isActive: true
    },
    select: {
      industry: true,
      scrapedAt: true,
      salaryMin: true,
      salaryMax: true
    }
  })

  const industryData = groupByField(jobs, 'industry', (job) => ({
    count: 1,
    salary: job.salaryMin && job.salaryMax ? (job.salaryMin + job.salaryMax) / 2 : 0
  }))

  const industryTrends = Object.entries(industryData)
    .map(([industry, data]) => ({
      industry,
      jobCount: data.count,
      avgSalary: data.salary > 0 ? data.salary / data.count : 0,
      growth: calculateGrowthRate(industry, daysBack) // Simplified
    }))
    .sort((a, b) => b.jobCount - a.jobCount)

  return {
    industries: industryTrends,
    fastestGrowing: industryTrends.filter(i => i.growth > 10).slice(0, 10),
    highestPaying: industryTrends.sort((a, b) => b.avgSalary - a.avgSalary).slice(0, 10),
    summary: {
      totalIndustries: industryTrends.length,
      totalJobs: jobs.length
    }
  }
}

async function getLocationTrends(timeframe: string) {
  const daysBack = parseTimeframe(timeframe)
  const cutoffDate = new Date(Date.now() - daysBack * 24 * 60 * 60 * 1000)

  const jobs = await prisma.jobPosting.findMany({
    where: {
      scrapedAt: { gte: cutoffDate },
      location: { not: null },
      isActive: true
    },
    select: {
      location: true,
      scrapedAt: true,
      salaryMin: true,
      salaryMax: true
    }
  })

  const locationData = groupByField(jobs, 'location', (job) => ({
    count: 1,
    salary: job.salaryMin && job.salaryMax ? (job.salaryMin + job.salaryMax) / 2 : 0
  }))

  const locationTrends = Object.entries(locationData)
    .map(([location, data]) => ({
      location,
      jobCount: data.count,
      avgSalary: data.salary > 0 ? data.salary / data.count : 0,
      growth: calculateGrowthRate(location, daysBack) // Simplified
    }))
    .sort((a, b) => b.jobCount - a.jobCount)
    .slice(0, 30)

  return {
    locations: locationTrends,
    fastestGrowing: locationTrends.filter(l => l.growth > 5).slice(0, 10),
    highestPaying: locationTrends.sort((a, b) => b.avgSalary - a.avgSalary).slice(0, 10),
    summary: {
      totalLocations: locationTrends.length,
      totalJobs: jobs.length
    }
  }
}

// Utility functions
function parseTimeframe(timeframe: string): number {
  const match = timeframe.match(/(\d+)([dwmy])/)
  if (!match) return 30

  const [, num, unit] = match
  const value = parseInt(num)

  switch (unit) {
    case 'd': return value
    case 'w': return value * 7
    case 'm': return value * 30
    case 'y': return value * 365
    default: return 30
  }
}

function groupByTimePeriod(jobs: any[], daysBack: number, mapper: (job: any) => any) {
  const periods = Math.min(daysBack, 30) // Max 30 data points
  const periodSize = daysBack / periods
  const now = Date.now()
  
  const groups = new Map()
  
  for (let i = 0; i < periods; i++) {
    const periodStart = now - (i + 1) * periodSize * 24 * 60 * 60 * 1000
    const periodEnd = now - i * periodSize * 24 * 60 * 60 * 1000
    const periodJobs = jobs.filter(job => {
      const jobTime = new Date(job.scrapedAt).getTime()
      return jobTime >= periodStart && jobTime < periodEnd
    })
    
    const periodData = periodJobs.map(mapper)
    const aggregated = aggregateData(periodData)
    
    groups.set(new Date(periodStart).toISOString().split('T')[0], aggregated)
  }
  
  return Object.fromEntries(groups)
}

function groupByField(jobs: any[], field: string, mapper: (job: any) => any) {
  const groups = new Map()
  
  jobs.forEach(job => {
    const key = job[field] || 'Unknown'
    if (!groups.has(key)) {
      groups.set(key, [])
    }
    groups.get(key).push(mapper(job))
  })
  
  const result = {}
  groups.forEach((values, key) => {
    result[key] = aggregateData(values)
  })
  
  return result
}

function aggregateData(data: any[]) {
  if (data.length === 0) return { count: 0 }
  
  const result = { count: data.length }
  
  // Aggregate numeric fields
  const numericFields = ['avgSalary', 'minSalary', 'maxSalary', 'salary']
  numericFields.forEach(field => {
    const values = data.map(item => item[field]).filter(val => val != null && !isNaN(val))
    if (values.length > 0) {
      result[field] = values.reduce((sum, val) => sum + val, 0) / values.length
    }
  })
  
  return result
}

function countOccurrences(items: string[]) {
  const counts = {}
  items.forEach(item => {
    counts[item] = (counts[item] || 0) + 1
  })
  return counts
}

function calculateGrowthRate(item: string, daysBack: number): number {
  // Simplified growth calculation
  // In a real implementation, this would compare with historical data
  return Math.random() * 30 - 5 // -5% to 25% growth
}
