/**
 * Feature Gate Component
 * 
 * Controls access to premium features based on subscription status
 */

'use client'

import { useState, useEffect, ReactNode } from 'react'
import { useSession } from 'next-auth/react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Lock, Zap, Crown, ArrowRight } from 'lucide-react'
import { toast } from 'sonner'

interface FeatureGateProps {
  feature: string
  children: ReactNode
  fallback?: ReactNode
  showUsage?: boolean
  trackUsage?: boolean
  usageAmount?: number
  metadata?: Record<string, string>
}

interface FeatureAccess {
  hasAccess: boolean
  reason?: string
  upgradeRequired?: boolean
  suggestedPlan?: string
}

interface FeatureUsage {
  allowed: boolean
  remaining: number | 'unlimited'
  limit: number | 'unlimited'
  resetDate?: Date
}

export function FeatureGate({
  feature,
  children,
  fallback,
  showUsage = false,
  trackUsage = false,
  usageAmount = 1,
  metadata
}: FeatureGateProps) {
  const { data: session } = useSession()
  const [access, setAccess] = useState<FeatureAccess | null>(null)
  const [usage, setUsage] = useState<FeatureUsage | null>(null)
  const [loading, setLoading] = useState(true)
  const [hasTrackedUsage, setHasTrackedUsage] = useState(false)

  useEffect(() => {
    if (session?.user) {
      checkFeatureAccess()
    } else {
      setLoading(false)
    }
  }, [session, feature])

  const checkFeatureAccess = async () => {
    try {
      const [accessResponse, usageResponse] = await Promise.all([
        fetch(`/api/features?action=check-access&feature=${feature}`),
        fetch(`/api/features?action=check-usage&feature=${feature}`)
      ])

      if (accessResponse.ok) {
        const accessData = await accessResponse.json()
        setAccess(accessData.access)
      }

      if (usageResponse.ok) {
        const usageData = await usageResponse.json()
        setUsage(usageData.usage)
      }
    } catch (error) {
      console.error('Error checking feature access:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleTrackUsage = async () => {
    if (!trackUsage || hasTrackedUsage || !session?.user) return

    try {
      const response = await fetch('/api/features', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'track-usage',
          feature,
          amount: usageAmount,
          metadata
        })
      })

      if (response.ok) {
        setHasTrackedUsage(true)
        // Refresh usage data
        const usageResponse = await fetch(`/api/features?action=check-usage&feature=${feature}`)
        if (usageResponse.ok) {
          const usageData = await usageResponse.json()
          setUsage(usageData.usage)
        }
      } else {
        const errorData = await response.json()
        if (errorData.upgradeRequired || errorData.usageExceeded) {
          // Show upgrade prompt instead of error
          return
        }
        toast.error(errorData.error || 'Failed to track usage')
      }
    } catch (error) {
      console.error('Error tracking usage:', error)
    }
  }

  const handleUpgrade = () => {
    const planParam = access?.suggestedPlan ? `?plan=${access.suggestedPlan}` : ''
    window.location.href = `/pricing${planParam}`
  }

  // Track usage when component mounts and user has access
  useEffect(() => {
    if (access?.hasAccess && usage?.allowed && trackUsage && !hasTrackedUsage) {
      handleTrackUsage()
    }
  }, [access, usage, trackUsage, hasTrackedUsage])

  if (loading) {
    return (
      <div className="animate-pulse">
        <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
        <div className="h-4 bg-gray-200 rounded w-1/2"></div>
      </div>
    )
  }

  // If user is not signed in, show sign-in prompt
  if (!session?.user) {
    return (
      <Card className="border-dashed">
        <CardContent className="flex flex-col items-center justify-center py-8">
          <Lock className="h-12 w-12 text-gray-400 mb-4" />
          <h3 className="text-lg font-semibold mb-2">Sign In Required</h3>
          <p className="text-gray-600 text-center mb-4">
            Please sign in to access this feature
          </p>
          <Button onClick={() => window.location.href = '/auth/signin'}>
            Sign In
          </Button>
        </CardContent>
      </Card>
    )
  }

  // If user doesn't have access, show upgrade prompt
  if (!access?.hasAccess) {
    if (fallback) {
      return <>{fallback}</>
    }

    return (
      <Card className="border-dashed border-blue-200 bg-blue-50/50">
        <CardContent className="flex flex-col items-center justify-center py-8">
          <div className="p-3 bg-blue-100 rounded-full mb-4">
            {access?.suggestedPlan === 'enterprise' ? (
              <Crown className="h-8 w-8 text-blue-600" />
            ) : (
              <Zap className="h-8 w-8 text-blue-600" />
            )}
          </div>
          <h3 className="text-lg font-semibold mb-2">Premium Feature</h3>
          <p className="text-gray-600 text-center mb-4">
            {access?.reason || 'This feature requires a premium subscription'}
          </p>
          <Button onClick={handleUpgrade} className="flex items-center gap-2">
            Upgrade Now
            <ArrowRight className="h-4 w-4" />
          </Button>
        </CardContent>
      </Card>
    )
  }

  // If user has access but exceeded usage limits
  if (!usage?.allowed) {
    return (
      <Card className="border-dashed border-yellow-200 bg-yellow-50/50">
        <CardContent className="flex flex-col items-center justify-center py-8">
          <div className="p-3 bg-yellow-100 rounded-full mb-4">
            <Lock className="h-8 w-8 text-yellow-600" />
          </div>
          <h3 className="text-lg font-semibold mb-2">Usage Limit Reached</h3>
          <p className="text-gray-600 text-center mb-4">
            You've reached your monthly limit for this feature.
            {usage?.resetDate && (
              <> Resets on {new Date(usage.resetDate).toLocaleDateString()}.</>
            )}
          </p>
          <Button onClick={handleUpgrade} variant="outline">
            Upgrade for More Usage
          </Button>
        </CardContent>
      </Card>
    )
  }

  // User has access - render the feature with optional usage display
  return (
    <div>
      {showUsage && usage && usage.limit !== 'unlimited' && (
        <div className="mb-4 p-3 bg-gray-50 rounded-lg">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium">Feature Usage</span>
            <Badge variant="secondary">
              {usage.remaining}/{usage.limit} remaining
            </Badge>
          </div>
          <Progress 
            value={usage.limit === 'unlimited' ? 100 : 
                   ((Number(usage.limit) - Number(usage.remaining)) / Number(usage.limit)) * 100} 
            className="h-2"
          />
          {usage.resetDate && (
            <p className="text-xs text-gray-500 mt-1">
              Resets on {new Date(usage.resetDate).toLocaleDateString()}
            </p>
          )}
        </div>
      )}
      {children}
    </div>
  )
}

/**
 * Hook for checking feature access
 */
export function useFeatureAccess(feature: string) {
  const { data: session } = useSession()
  const [access, setAccess] = useState<FeatureAccess | null>(null)
  const [usage, setUsage] = useState<FeatureUsage | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (session?.user) {
      checkAccess()
    } else {
      setLoading(false)
    }
  }, [session, feature])

  const checkAccess = async () => {
    try {
      const response = await fetch(`/api/features?action=can-use&feature=${feature}`)
      if (response.ok) {
        const data = await response.json()
        setAccess(data.canUse)
      }

      const usageResponse = await fetch(`/api/features?action=check-usage&feature=${feature}`)
      if (usageResponse.ok) {
        const usageData = await usageResponse.json()
        setUsage(usageData.usage)
      }
    } catch (error) {
      console.error('Error checking feature access:', error)
    } finally {
      setLoading(false)
    }
  }

  const trackUsage = async (amount: number = 1, metadata?: Record<string, string>) => {
    if (!session?.user) return false

    try {
      const response = await fetch('/api/features', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'check-and-track',
          feature,
          amount,
          metadata
        })
      })

      const data = await response.json()
      
      if (data.tracked) {
        // Refresh usage data
        await checkAccess()
      }

      return data.success
    } catch (error) {
      console.error('Error tracking usage:', error)
      return false
    }
  }

  return {
    hasAccess: access?.allowed || false,
    canUse: access?.allowed && usage?.allowed,
    access,
    usage,
    loading,
    trackUsage,
    refresh: checkAccess
  }
}

/**
 * Simple feature gate for inline usage
 */
export function FeatureGuard({ 
  feature, 
  children, 
  fallback 
}: { 
  feature: string
  children: ReactNode
  fallback?: ReactNode 
}) {
  const { canUse, loading } = useFeatureAccess(feature)

  if (loading) {
    return <div className="animate-pulse h-4 bg-gray-200 rounded"></div>
  }

  if (!canUse) {
    return fallback || null
  }

  return <>{children}</>
}
