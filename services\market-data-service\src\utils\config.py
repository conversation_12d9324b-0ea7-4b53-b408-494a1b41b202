"""
Configuration Management for Market Data Service
Handles environment variables and service configuration
"""

import os
from typing import Dict, Any, List
from dataclasses import dataclass
import json

@dataclass
class ScraperConfig:
    """Configuration for individual scrapers"""
    enabled: bool = True
    max_pages: int = 5
    rate_limit: float = 1.0
    max_jobs_per_run: int = 1000

class Config:
    """Main configuration class for Market Data Service"""
    
    def __init__(self):
        self.load_environment()
        self.setup_scraper_configs()
    
    def load_environment(self):
        """Load configuration from environment variables"""
        # Database configuration
        self.database_url = os.getenv(
            'DATABASE_URL',
            'postgresql://user:password@localhost:5432/careercraft'
        )
        
        # OpenAI configuration
        self.openai_api_key = os.getenv('OPENAI_API_KEY')
        self.openai_model = os.getenv('OPENAI_MODEL', 'gpt-4')
        self.openai_embedding_model = os.getenv('OPENAI_EMBEDDING_MODEL', 'text-embedding-ada-002')
        
        # Service configuration
        self.enable_ai_processing = os.getenv('ENABLE_AI_PROCESSING', 'true').lower() == 'true'
        self.enable_vectorization = os.getenv('ENABLE_VECTORIZATION', 'true').lower() == 'true'
        self.log_level = os.getenv('LOG_LEVEL', 'INFO')
        
        # Scraping configuration
        self.max_concurrent_scrapers = int(os.getenv('MAX_CONCURRENT_SCRAPERS', '3'))
        self.default_max_pages = int(os.getenv('DEFAULT_MAX_PAGES', '5'))
        self.default_rate_limit = float(os.getenv('DEFAULT_RATE_LIMIT', '1.5'))
        
        # Storage configuration
        self.max_job_age_days = int(os.getenv('MAX_JOB_AGE_DAYS', '30'))
        self.batch_size = int(os.getenv('BATCH_SIZE', '50'))
        
        # Monitoring configuration
        self.sentry_dsn = os.getenv('SENTRY_DSN')
        self.enable_metrics = os.getenv('ENABLE_METRICS', 'false').lower() == 'true'
        
        # Redis configuration (for caching and queues)
        self.redis_url = os.getenv('REDIS_URL', 'redis://localhost:6379/0')
        
        # Proxy configuration (if needed for scraping)
        self.proxy_url = os.getenv('PROXY_URL')
        self.use_proxy = os.getenv('USE_PROXY', 'false').lower() == 'true'
    
    def setup_scraper_configs(self):
        """Setup individual scraper configurations"""
        self.scraper_configs = {
            'linkedin': ScraperConfig(
                enabled=os.getenv('LINKEDIN_SCRAPER_ENABLED', 'true').lower() == 'true',
                max_pages=int(os.getenv('LINKEDIN_MAX_PAGES', '5')),
                rate_limit=float(os.getenv('LINKEDIN_RATE_LIMIT', '2.0')),
                max_jobs_per_run=int(os.getenv('LINKEDIN_MAX_JOBS', '500'))
            ),
            'indeed': ScraperConfig(
                enabled=os.getenv('INDEED_SCRAPER_ENABLED', 'true').lower() == 'true',
                max_pages=int(os.getenv('INDEED_MAX_PAGES', '5')),
                rate_limit=float(os.getenv('INDEED_RATE_LIMIT', '1.5')),
                max_jobs_per_run=int(os.getenv('INDEED_MAX_JOBS', '500'))
            ),
            'company': ScraperConfig(
                enabled=os.getenv('COMPANY_SCRAPER_ENABLED', 'true').lower() == 'true',
                max_pages=int(os.getenv('COMPANY_MAX_PAGES', '3')),
                rate_limit=float(os.getenv('COMPANY_RATE_LIMIT', '2.0')),
                max_jobs_per_run=int(os.getenv('COMPANY_MAX_JOBS', '200'))
            )
        }
    
    def get_database_url(self) -> str:
        """Get database connection URL"""
        return self.database_url
    
    def get_openai_config(self) -> Dict[str, str]:
        """Get OpenAI configuration"""
        return {
            'api_key': self.openai_api_key,
            'model': self.openai_model,
            'embedding_model': self.openai_embedding_model
        }
    
    def is_scraper_enabled(self, scraper_name: str) -> bool:
        """Check if a specific scraper is enabled"""
        config = self.scraper_configs.get(scraper_name)
        return config.enabled if config else False
    
    def get_max_pages(self, scraper_name: str) -> int:
        """Get max pages for a specific scraper"""
        config = self.scraper_configs.get(scraper_name)
        return config.max_pages if config else self.default_max_pages
    
    def get_rate_limit(self, scraper_name: str) -> float:
        """Get rate limit for a specific scraper"""
        config = self.scraper_configs.get(scraper_name)
        return config.rate_limit if config else self.default_rate_limit
    
    def get_max_jobs_per_run(self, scraper_name: str) -> int:
        """Get max jobs per run for a specific scraper"""
        config = self.scraper_configs.get(scraper_name)
        return config.max_jobs_per_run if config else 1000
    
    def get_search_terms(self) -> List[str]:
        """Get default search terms"""
        default_terms = [
            'Software Engineer',
            'Data Scientist',
            'Product Manager',
            'UX Designer',
            'DevOps Engineer',
            'Frontend Developer',
            'Backend Developer',
            'Full Stack Developer',
            'Machine Learning Engineer',
            'Technical Program Manager'
        ]
        
        # Allow override from environment
        env_terms = os.getenv('SEARCH_TERMS')
        if env_terms:
            try:
                return json.loads(env_terms)
            except json.JSONDecodeError:
                return env_terms.split(',')
        
        return default_terms
    
    def get_search_locations(self) -> List[str]:
        """Get default search locations"""
        default_locations = [
            'San Francisco, CA',
            'New York, NY',
            'Seattle, WA',
            'Austin, TX',
            'Boston, MA',
            'Los Angeles, CA',
            'Chicago, IL',
            'Denver, CO',
            'Remote',
            'United States'
        ]
        
        # Allow override from environment
        env_locations = os.getenv('SEARCH_LOCATIONS')
        if env_locations:
            try:
                return json.loads(env_locations)
            except json.JSONDecodeError:
                return env_locations.split(',')
        
        return default_locations
    
    def get_schedule_config(self) -> Dict[str, Any]:
        """Get scheduling configuration"""
        return {
            'full_scraping_time': os.getenv('FULL_SCRAPING_TIME', '02:00'),
            'incremental_interval_hours': int(os.getenv('INCREMENTAL_INTERVAL_HOURS', '4')),
            'cleanup_day': os.getenv('CLEANUP_DAY', 'sunday'),
            'cleanup_time': os.getenv('CLEANUP_TIME', '03:00'),
            'timezone': os.getenv('TIMEZONE', 'UTC')
        }
    
    def get_monitoring_config(self) -> Dict[str, Any]:
        """Get monitoring and alerting configuration"""
        return {
            'sentry_dsn': self.sentry_dsn,
            'enable_metrics': self.enable_metrics,
            'slack_webhook': os.getenv('SLACK_WEBHOOK'),
            'email_alerts': os.getenv('EMAIL_ALERTS', 'false').lower() == 'true',
            'alert_email': os.getenv('ALERT_EMAIL'),
            'min_jobs_threshold': int(os.getenv('MIN_JOBS_THRESHOLD', '100')),
            'max_error_rate': float(os.getenv('MAX_ERROR_RATE', '0.1'))
        }
    
    def validate_config(self) -> List[str]:
        """Validate configuration and return list of issues"""
        issues = []
        
        # Check required configurations
        if not self.database_url:
            issues.append("DATABASE_URL is required")
        
        if self.enable_ai_processing and not self.openai_api_key:
            issues.append("OPENAI_API_KEY is required when AI processing is enabled")
        
        # Check scraper configurations
        enabled_scrapers = [name for name, config in self.scraper_configs.items() if config.enabled]
        if not enabled_scrapers:
            issues.append("At least one scraper must be enabled")
        
        # Check numeric values
        if self.max_concurrent_scrapers < 1:
            issues.append("MAX_CONCURRENT_SCRAPERS must be at least 1")
        
        if self.default_max_pages < 1:
            issues.append("DEFAULT_MAX_PAGES must be at least 1")
        
        if self.batch_size < 1:
            issues.append("BATCH_SIZE must be at least 1")
        
        return issues
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary for logging/debugging"""
        return {
            'database_url': self.database_url[:20] + '...' if self.database_url else None,
            'openai_api_key': '***' if self.openai_api_key else None,
            'enable_ai_processing': self.enable_ai_processing,
            'enable_vectorization': self.enable_vectorization,
            'log_level': self.log_level,
            'max_concurrent_scrapers': self.max_concurrent_scrapers,
            'default_max_pages': self.default_max_pages,
            'default_rate_limit': self.default_rate_limit,
            'scraper_configs': {
                name: {
                    'enabled': config.enabled,
                    'max_pages': config.max_pages,
                    'rate_limit': config.rate_limit
                }
                for name, config in self.scraper_configs.items()
            }
        }
    
    @classmethod
    def from_file(cls, config_file: str) -> 'Config':
        """Load configuration from JSON file"""
        config = cls()
        
        if os.path.exists(config_file):
            with open(config_file, 'r') as f:
                file_config = json.load(f)
            
            # Override environment variables with file values
            for key, value in file_config.items():
                if hasattr(config, key):
                    setattr(config, key, value)
        
        return config
