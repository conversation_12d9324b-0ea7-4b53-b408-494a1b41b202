@echo off
echo.
echo ========================================
echo   CareerCraft - npm Issue Fixer
echo ========================================
echo.

echo [INFO] Attempting to fix npm installation issues...
echo.

REM Step 1: Clear npm cache
echo [STEP 1] Clearing npm cache...
npm cache clean --force
if %errorlevel% neq 0 (
    echo [WARNING] Cache clean failed, continuing...
)

REM Step 2: Remove node_modules if exists
echo [STEP 2] Removing existing node_modules...
if exist "node_modules" (
    rmdir /s /q node_modules
    echo [SUCCESS] Removed node_modules directory
) else (
    echo [INFO] No node_modules directory found
)

if exist "apps\web\node_modules" (
    rmdir /s /q apps\web\node_modules
    echo [SUCCESS] Removed apps\web\node_modules directory
)

REM Step 3: Remove package-lock.json files
echo [STEP 3] Removing lock files...
if exist "package-lock.json" (
    del package-lock.json
    echo [SUCCESS] Removed root package-lock.json
)

if exist "apps\web\package-lock.json" (
    del apps\web\package-lock.json
    echo [SUCCESS] Removed web app package-lock.json
)

REM Step 4: Set npm configuration
echo [STEP 4] Configuring npm settings...
npm config set registry https://registry.npmjs.org/
npm config set fetch-retries 5
npm config set fetch-retry-factor 2
npm config set fetch-retry-mintimeout 10000
npm config set fetch-retry-maxtimeout 60000
echo [SUCCESS] Updated npm configuration

REM Step 5: Try installing with different strategies
echo [STEP 5] Attempting installation with legacy peer deps...
cd apps\web

REM Create a minimal package.json for testing
echo [INFO] Creating minimal package.json for testing...
echo { > package-minimal.json
echo   "name": "@careercraft/web", >> package-minimal.json
echo   "version": "2.0.0", >> package-minimal.json
echo   "private": true, >> package-minimal.json
echo   "scripts": { >> package-minimal.json
echo     "dev": "next dev", >> package-minimal.json
echo     "build": "next build", >> package-minimal.json
echo     "start": "next start" >> package-minimal.json
echo   }, >> package-minimal.json
echo   "dependencies": { >> package-minimal.json
echo     "next": "^14.0.3", >> package-minimal.json
echo     "react": "^18.2.0", >> package-minimal.json
echo     "react-dom": "^18.2.0", >> package-minimal.json
echo     "typescript": "^5.3.0" >> package-minimal.json
echo   }, >> package-minimal.json
echo   "devDependencies": { >> package-minimal.json
echo     "@types/node": "^20.10.0", >> package-minimal.json
echo     "@types/react": "^18.2.38", >> package-minimal.json
echo     "@types/react-dom": "^18.2.17", >> package-minimal.json
echo     "eslint": "^8.54.0", >> package-minimal.json
echo     "eslint-config-next": "^14.0.3" >> package-minimal.json
echo   } >> package-minimal.json
echo } >> package-minimal.json

echo [INFO] Trying minimal installation first...
copy package-minimal.json package.json
npm install --legacy-peer-deps --verbose

if %errorlevel% equ 0 (
    echo.
    echo [SUCCESS] Minimal installation successful!
    echo [INFO] You can now try running: npm run dev
    echo [INFO] The app will be available at: http://localhost:3000
    echo.
    echo [NEXT STEPS]
    echo 1. Run: npm run dev
    echo 2. Open: http://localhost:3000
    echo 3. If it works, we can add more dependencies gradually
    echo.
) else (
    echo.
    echo [ERROR] Installation still failed. Trying alternative approaches...
    echo.
    
    REM Try with yarn
    echo [ALTERNATIVE 1] Trying with yarn...
    where yarn >nul 2>&1
    if %errorlevel% equ 0 (
        yarn install
        if %errorlevel% equ 0 (
            echo [SUCCESS] Yarn installation successful!
            echo [INFO] Run: yarn dev
            goto :success
        )
    ) else (
        echo [INFO] Yarn not found, installing yarn...
        npm install -g yarn
        if %errorlevel% equ 0 (
            yarn install
            if %errorlevel% equ 0 (
                echo [SUCCESS] Yarn installation successful!
                echo [INFO] Run: yarn dev
                goto :success
            )
        )
    )
    
    REM Try with pnpm
    echo [ALTERNATIVE 2] Trying with pnpm...
    where pnpm >nul 2>&1
    if %errorlevel% equ 0 (
        pnpm install
        if %errorlevel% equ 0 (
            echo [SUCCESS] pnpm installation successful!
            echo [INFO] Run: pnpm dev
            goto :success
        )
    ) else (
        echo [INFO] pnpm not found, installing pnpm...
        npm install -g pnpm
        if %errorlevel% equ 0 (
            pnpm install
            if %errorlevel% equ 0 (
                echo [SUCCESS] pnpm installation successful!
                echo [INFO] Run: pnpm dev
                goto :success
            )
        )
    )
    
    echo.
    echo [FINAL RECOMMENDATION]
    echo If all package managers fail, this suggests:
    echo 1. Antivirus software interference
    echo 2. Corporate firewall/proxy issues
    echo 3. Disk space or permission problems
    echo 4. Network connectivity issues
    echo.
    echo SOLUTIONS:
    echo 1. Try running as Administrator
    echo 2. Temporarily disable antivirus
    echo 3. Check available disk space
    echo 4. Try on a different network
    echo 5. Use the HTML demo instead: start-demo.bat
    echo.
    goto :end
)

:success
echo.
echo ========================================
echo   Installation Successful!
echo ========================================
echo.
echo Next steps:
echo 1. Start the development server:
echo    npm run dev  (or yarn dev / pnpm dev)
echo.
echo 2. Open your browser:
echo    http://localhost:3000
echo.
echo 3. If you encounter any issues:
echo    - Check the console for errors
echo    - Try the HTML demo: start-demo.bat
echo    - Review the QUICK_START_GUIDE.md
echo.

:end
cd ..\..
echo [INFO] Fix script completed.
echo [INFO] Check the output above for next steps.
pause
