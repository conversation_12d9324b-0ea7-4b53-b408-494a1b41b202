# CareerCraft Integration Testing Guide

## Overview
This guide provides comprehensive procedures for integration testing across all CareerCraft system components, ensuring seamless interaction between Milestones 1.1, 1.2, and 1.3.

## Integration Test Architecture

### System Integration Points
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  Profile        │    │  Market Data    │    │  Market         │
│  Vectorization  │◄──►│  Service        │◄──►│  Analysis       │
│  (Milestone 1.1)│    │  (Milestone 1.2)│    │  (Milestone 1.3)│
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 ▼
                    ┌─────────────────┐
                    │   Shared        │
                    │   Database      │
                    │   (PostgreSQL)  │
                    └─────────────────┘
```

### Integration Test Scenarios

#### 1. End-to-End Career Intelligence Flow
**Test ID**: INT-001  
**Description**: Complete user journey from resume upload to career insights  
**Components**: All milestones  

**Test Steps**:
1. **User Authentication**
   ```typescript
   // Authenticate user
   const session = await signIn('credentials', {
     email: '<EMAIL>',
     password: 'testpassword'
   })
   expect(session.user).toBeDefined()
   ```

2. **Resume Upload & Processing**
   ```typescript
   // Upload resume
   const resume = await uploadResume(testResumeFile)
   expect(resume.id).toBeDefined()
   
   // Verify resume processing
   const processedResume = await getResume(resume.id)
   expect(processedResume.content).toBeDefined()
   ```

3. **Profile Vector Generation**
   ```typescript
   // Generate profile vector
   const vectorResult = await generateProfileVector(resume.id)
   expect(vectorResult.vector).toHaveLength(1536)
   expect(vectorResult.skills).toBeInstanceOf(Array)
   expect(vectorResult.experienceLevel).toBeDefined()
   ```

4. **Market Data Integration**
   ```typescript
   // Fetch relevant market data
   const marketData = await fetchMarketData({
     skills: vectorResult.skills,
     experienceLevel: vectorResult.experienceLevel,
     location: 'San Francisco, CA'
   })
   expect(marketData.jobs.length).toBeGreaterThan(0)
   ```

5. **Market Analysis Generation**
   ```typescript
   // Generate market analysis
   const analysis = await generateMarketAnalysis({
     userId: session.user.id,
     profileVector: vectorResult,
     marketData: marketData
   })
   expect(analysis.insights).toBeDefined()
   expect(analysis.predictions).toBeDefined()
   expect(analysis.recommendations).toBeDefined()
   ```

6. **Career Insights Dashboard**
   ```typescript
   // Render dashboard with insights
   const dashboard = render(<CareerInsightsDashboard userId={session.user.id} />)
   expect(dashboard.getByText('Career Insights')).toBeInTheDocument()
   expect(dashboard.getByText('Market Analysis')).toBeInTheDocument()
   ```

**Expected Results**:
- ✅ Complete workflow executes without errors
- ✅ Data flows correctly between all components
- ✅ User receives comprehensive career insights
- ✅ Performance meets SLA requirements (<15 seconds total)

#### 2. Real-time Data Synchronization
**Test ID**: INT-002  
**Description**: Verify real-time updates across system components  
**Components**: Market Data Service, Market Analysis Engine  

**Test Steps**:
1. **Trigger Market Data Update**
   ```python
   # Start market data scraping
   scraping_result = await market_data_service.run_incremental_update()
   expect(scraping_result.jobs_processed).toBeGreaterThan(0)
   ```

2. **Verify Database Updates**
   ```typescript
   // Check database for new job postings
   const newJobs = await prisma.jobPosting.findMany({
     where: { scrapedAt: { gte: testStartTime } }
   })
   expect(newJobs.length).toBeGreaterThan(0)
   ```

3. **Trigger Market Analysis Refresh**
   ```typescript
   // Generate updated market analysis
   const updatedAnalysis = await generateMarketAnalysis({
     analysisType: 'REAL_TIME',
     forceRefresh: true
   })
   expect(updatedAnalysis.generatedAt).toBeAfter(testStartTime)
   ```

4. **Verify UI Updates**
   ```typescript
   // Check dashboard reflects new data
   const dashboard = render(<MarketDashboard />)
   await waitFor(() => {
     expect(dashboard.getByText(/Updated/)).toBeInTheDocument()
   })
   ```

#### 3. Cross-Service Error Handling
**Test ID**: INT-003  
**Description**: Verify graceful error handling across service boundaries  
**Components**: All milestones  

**Test Scenarios**:

##### Database Connection Failure
```typescript
// Simulate database failure
await mockDatabaseFailure()

// Attempt profile vectorization
const result = await generateProfileVector(resumeId)
expect(result.error).toBeDefined()
expect(result.fallbackData).toBeDefined()

// Verify system recovery
await restoreDatabase()
const retryResult = await generateProfileVector(resumeId)
expect(retryResult.success).toBe(true)
```

##### OpenAI API Failure
```typescript
// Mock OpenAI API failure
mockOpenAIFailure()

// Test AI-dependent operations
const vectorResult = await generateProfileVector(resumeId)
expect(vectorResult.vector).toBeDefined() // Should use fallback

const analysisResult = await generateMarketAnalysis(params)
expect(analysisResult.insights).toBeDefined() // Should use rule-based fallback
```

##### Redis Cache Failure
```typescript
// Simulate Redis failure
await mockRedisFailure()

// Test caching-dependent operations
const analysis1 = await generateMarketAnalysis(params)
const analysis2 = await generateMarketAnalysis(params)

// Should work without cache, but may be slower
expect(analysis1).toBeDefined()
expect(analysis2).toBeDefined()
```

#### 4. Performance Integration Testing
**Test ID**: INT-004  
**Description**: Verify system performance under integrated load  
**Components**: All milestones  

**Load Test Scenarios**:

##### Concurrent User Load
```typescript
// Simulate 100 concurrent users
const concurrentUsers = 100
const promises = []

for (let i = 0; i < concurrentUsers; i++) {
  promises.push(simulateUserJourney(i))
}

const results = await Promise.allSettled(promises)
const successRate = results.filter(r => r.status === 'fulfilled').length / concurrentUsers

expect(successRate).toBeGreaterThan(0.95) // 95% success rate
```

##### Data Processing Load
```typescript
// Process large batch of resumes
const largeBatch = generateTestResumes(1000)
const startTime = Date.now()

const results = await Promise.all(
  largeBatch.map(resume => generateProfileVector(resume.id))
)

const duration = Date.now() - startTime
const avgProcessingTime = duration / largeBatch.length

expect(avgProcessingTime).toBeLessThan(5000) // <5 seconds per resume
expect(results.filter(r => r.success).length).toBeGreaterThan(950) // 95% success
```

#### 5. Data Consistency Testing
**Test ID**: INT-005  
**Description**: Verify data consistency across all system components  
**Components**: All milestones  

**Consistency Checks**:

##### Profile Vector Consistency
```typescript
// Generate profile vector
const vector1 = await generateProfileVector(resumeId)

// Retrieve from database
const storedVector = await prisma.userProfileVector.findFirst({
  where: { resumeId }
})

// Verify consistency
expect(vector1.vector).toEqual(storedVector.profileVector)
expect(vector1.skills).toEqual(storedVector.skillsExtracted)
```

##### Market Data Consistency
```typescript
// Check job data consistency across services
const jobsFromScraper = await getJobsFromScraper('linkedin')
const jobsFromDatabase = await prisma.jobPosting.findMany({
  where: { source: 'linkedin' }
})

// Verify data integrity
jobsFromScraper.forEach(scrapedJob => {
  const dbJob = jobsFromDatabase.find(j => j.externalId === scrapedJob.id)
  expect(dbJob).toBeDefined()
  expect(dbJob.title).toBe(scrapedJob.title)
  expect(dbJob.company).toBe(scrapedJob.company)
})
```

##### Analysis Data Consistency
```typescript
// Generate analysis
const analysis = await generateMarketAnalysis(params)

// Verify analysis data matches source data
const sourceJobs = await getJobsForAnalysis(params)
expect(analysis.metrics.totalJobs).toBe(sourceJobs.length)

const avgSalary = sourceJobs.reduce((sum, job) => 
  sum + (job.salaryMin + job.salaryMax) / 2, 0) / sourceJobs.length
expect(analysis.metrics.averageSalary).toBeCloseTo(avgSalary, 0)
```

## Integration Test Environment Setup

### Test Environment Configuration
```yaml
# docker-compose.test.yml
version: '3.8'
services:
  web-test:
    build: ./apps/web
    environment:
      - NODE_ENV=test
      - DATABASE_URL=***********************************/careercraft_test
      - REDIS_URL=redis://redis-test:6379
    depends_on:
      - db-test
      - redis-test

  market-data-test:
    build: ./services/market-data-service
    environment:
      - DATABASE_URL=***********************************/careercraft_test
      - REDIS_URL=redis://redis-test:6379
    depends_on:
      - db-test
      - redis-test

  db-test:
    image: postgres:14
    environment:
      - POSTGRES_DB=careercraft_test
      - POSTGRES_USER=test
      - POSTGRES_PASSWORD=test

  redis-test:
    image: redis:6-alpine
```

### Test Data Setup
```typescript
// Setup test data
export async function setupIntegrationTestData() {
  // Create test users
  const testUsers = await createTestUsers(10)
  
  // Create test resumes
  const testResumes = await createTestResumes(testUsers, 50)
  
  // Create test job postings
  const testJobs = await createTestJobPostings(1000)
  
  // Generate test profile vectors
  const testVectors = await generateTestProfileVectors(testResumes)
  
  return {
    users: testUsers,
    resumes: testResumes,
    jobs: testJobs,
    vectors: testVectors
  }
}
```

## Test Execution Procedures

### Automated Integration Testing
```bash
# Run full integration test suite
npm run test:integration

# Run specific integration test
npm run test:integration -- --grep "End-to-End Career Intelligence"

# Run integration tests with coverage
npm run test:integration:coverage

# Run performance integration tests
npm run test:integration:performance
```

### Manual Integration Testing
```bash
# Start test environment
docker-compose -f docker-compose.test.yml up -d

# Run manual test scenarios
npm run test:manual:integration

# Generate test report
npm run test:report:integration
```

## Test Monitoring & Reporting

### Real-time Monitoring
- **Test Execution Dashboard**: Live test status
- **Performance Metrics**: Response times, throughput
- **Error Tracking**: Failed test analysis
- **Resource Usage**: CPU, memory, database connections

### Test Reports
- **Integration Test Summary**: Pass/fail rates
- **Performance Benchmarks**: SLA compliance
- **Error Analysis**: Root cause analysis
- **Trend Analysis**: Performance over time

## Continuous Integration

### CI/CD Pipeline Integration
```yaml
# .github/workflows/integration-tests.yml
name: Integration Tests
on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

jobs:
  integration-tests:
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:14
        env:
          POSTGRES_PASSWORD: test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
      redis:
        image: redis:6
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - run: npm ci
      - run: npm run test:integration
      - run: npm run test:integration:performance
```

## Best Practices

### Integration Test Design
1. **Test Isolation**: Each test should be independent
2. **Data Cleanup**: Clean test data after each test
3. **Realistic Scenarios**: Use production-like test data
4. **Error Simulation**: Test failure scenarios
5. **Performance Validation**: Include performance assertions

### Maintenance
1. **Regular Updates**: Keep tests current with system changes
2. **Test Data Refresh**: Update test data regularly
3. **Environment Sync**: Maintain test environment parity
4. **Documentation**: Keep test documentation updated

## Troubleshooting

### Common Integration Issues
1. **Service Communication**: Network connectivity problems
2. **Data Synchronization**: Timing issues between services
3. **Resource Contention**: Database connection limits
4. **Environment Differences**: Configuration mismatches

### Debug Procedures
1. **Log Analysis**: Check service logs for errors
2. **Network Testing**: Verify service connectivity
3. **Data Validation**: Check data consistency
4. **Performance Profiling**: Identify bottlenecks
