/**
 * Career Intelligence Service
 * 
 * Main service for career intelligence features including profile vectorization,
 * market analysis, and career insights
 * Implements Epic 5.0: Career Intelligence Engine
 */

import { prisma } from '@/lib/db'
import { ProfileVectorizer, type ProfileVector } from './profile-vectorizer'

export interface CareerInsights {
  salaryEstimate: {
    min: number
    max: number
    confidence: number
    currency: string
  }
  marketFit: {
    score: number
    description: string
    strengths: string[]
    improvements: string[]
  }
  skillGaps: {
    missing: string[]
    recommended: string[]
    trending: string[]
  }
  careerPaths: {
    current: string
    nextSteps: Array<{
      role: string
      probability: number
      requirements: string[]
      timeframe: string
    }>
    alternatives: Array<{
      role: string
      transferableSkills: string[]
      additionalSkills: string[]
    }>
  }
  marketData: {
    totalJobs: number
    averageSalary: number
    topCompanies: string[]
    hotSkills: string[]
    locations: Array<{
      city: string
      jobCount: number
      averageSalary: number
    }>
  }
}

export class CareerIntelligenceService {
  private vectorizer: ProfileVectorizer

  constructor() {
    this.vectorizer = new ProfileVectorizer()
  }

  /**
   * Generate comprehensive career insights for a user's resume
   */
  async generateCareerInsights(userId: string, resumeId: string): Promise<CareerInsights> {
    try {
      // Step 1: Create or update profile vector
      const profileVector = await this.createOrUpdateProfileVector(userId, resumeId)
      
      // Step 2: Analyze market fit and salary
      const marketAnalysis = await this.analyzeMarketFit(userId, resumeId, profileVector)
      
      // Step 3: Identify skill gaps
      const skillGaps = await this.analyzeSkillGaps(profileVector)
      
      // Step 4: Generate career path recommendations
      const careerPaths = await this.generateCareerPaths(profileVector)
      
      // Step 5: Compile market data
      const marketData = await this.compileMarketData(profileVector)

      // Step 6: Store analysis results
      await this.storeAnalysisResults(userId, resumeId, {
        salaryEstimate: marketAnalysis.salaryEstimate,
        marketFit: marketAnalysis.marketFit,
        skillGaps,
        careerPaths,
        marketData
      })

      return {
        salaryEstimate: marketAnalysis.salaryEstimate,
        marketFit: marketAnalysis.marketFit,
        skillGaps,
        careerPaths,
        marketData
      }
    } catch (error) {
      console.error('Error generating career insights:', error)
      throw new Error('Failed to generate career insights')
    }
  }

  /**
   * Create or update profile vector for a resume
   */
  async createOrUpdateProfileVector(userId: string, resumeId: string): Promise<ProfileVector> {
    try {
      // Extract profile data from resume
      const profileData = await this.vectorizer.extractProfileData(resumeId)
      
      // Generate text representation
      const profileText = this.vectorizer.generateProfileText(profileData)
      
      // Create vector embedding
      const vector = await this.vectorizer.createEmbedding(profileText)
      
      // Extract additional metadata
      const skillsExtracted = await this.vectorizer.extractSkills(profileText)
      const experienceLevel = this.vectorizer.determineExperienceLevel(profileData.experience)
      const primaryRole = this.vectorizer.extractPrimaryRole(profileData.experience)
      const industries = this.vectorizer.extractIndustries(profileData.experience)
      const locations = this.vectorizer.extractLocations(profileData)

      const profileVector: ProfileVector = {
        vector,
        skillsExtracted,
        experienceLevel,
        primaryRole,
        industries,
        locations
      }

      // Store in database
      await prisma.userProfileVector.upsert({
        where: {
          userId_resumeId: {
            userId,
            resumeId
          }
        },
        update: {
          profileVector: JSON.stringify(vector),
          skillsExtracted: JSON.stringify(skillsExtracted),
          experienceLevel,
          primaryRole,
          industries: JSON.stringify(industries),
          locations: JSON.stringify(locations),
          lastUpdated: new Date()
        },
        create: {
          userId,
          resumeId,
          profileVector: JSON.stringify(vector),
          skillsExtracted: JSON.stringify(skillsExtracted),
          experienceLevel,
          primaryRole,
          industries: JSON.stringify(industries),
          locations: JSON.stringify(locations)
        }
      })

      return profileVector
    } catch (error) {
      console.error('Error creating profile vector:', error)
      throw new Error('Failed to create profile vector')
    }
  }

  /**
   * Analyze market fit and salary estimation
   */
  private async analyzeMarketFit(userId: string, resumeId: string, profileVector: ProfileVector) {
    // This is a simplified implementation
    // In production, this would use sophisticated ML algorithms and real market data
    
    const baseScore = 0.75 // Base market fit score
    const experienceMultiplier = this.getExperienceMultiplier(profileVector.experienceLevel)
    const skillsBonus = Math.min(profileVector.skillsExtracted.length * 0.02, 0.2)
    
    const marketFitScore = Math.min(baseScore + skillsBonus, 1.0)
    
    // Salary estimation based on role and experience
    const baseSalary = this.getBaseSalaryForRole(profileVector.primaryRole)
    const salaryMin = Math.round(baseSalary * experienceMultiplier * 0.9)
    const salaryMax = Math.round(baseSalary * experienceMultiplier * 1.3)
    
    return {
      salaryEstimate: {
        min: salaryMin,
        max: salaryMax,
        confidence: 0.85,
        currency: 'USD'
      },
      marketFit: {
        score: marketFitScore,
        description: this.getMarketFitDescription(marketFitScore),
        strengths: this.identifyStrengths(profileVector),
        improvements: this.identifyImprovements(profileVector)
      }
    }
  }

  /**
   * Analyze skill gaps based on market trends
   */
  private async analyzeSkillGaps(profileVector: ProfileVector) {
    // This would typically analyze current job market data
    // For now, we'll use predefined trending skills by role
    
    const trendingSkills = this.getTrendingSkillsForRole(profileVector.primaryRole)
    const currentSkills = new Set(profileVector.skillsExtracted.map(s => s.toLowerCase()))
    
    const missing = trendingSkills.filter(skill => 
      !currentSkills.has(skill.toLowerCase())
    )
    
    return {
      missing: missing.slice(0, 5), // Top 5 missing skills
      recommended: this.getRecommendedSkills(profileVector.primaryRole),
      trending: trendingSkills.slice(0, 8) // Top 8 trending skills
    }
  }

  /**
   * Generate career path recommendations
   */
  private async generateCareerPaths(profileVector: ProfileVector) {
    const nextSteps = this.getNextStepsForRole(profileVector.primaryRole, profileVector.experienceLevel)
    const alternatives = this.getAlternativeRoles(profileVector)
    
    return {
      current: profileVector.primaryRole,
      nextSteps,
      alternatives
    }
  }

  /**
   * Compile market data and trends
   */
  private async compileMarketData(profileVector: ProfileVector) {
    // In production, this would query real job market data
    // For now, we'll return mock data based on the profile
    
    return {
      totalJobs: Math.floor(Math.random() * 5000) + 1000,
      averageSalary: this.getBaseSalaryForRole(profileVector.primaryRole),
      topCompanies: this.getTopCompaniesForRole(profileVector.primaryRole),
      hotSkills: this.getTrendingSkillsForRole(profileVector.primaryRole),
      locations: [
        { city: 'San Francisco, CA', jobCount: 1250, averageSalary: 145000 },
        { city: 'New York, NY', jobCount: 980, averageSalary: 135000 },
        { city: 'Seattle, WA', jobCount: 750, averageSalary: 140000 },
        { city: 'Austin, TX', jobCount: 650, averageSalary: 125000 },
        { city: 'Remote', jobCount: 2100, averageSalary: 130000 }
      ]
    }
  }

  /**
   * Store analysis results in database
   */
  private async storeAnalysisResults(userId: string, resumeId: string, insights: CareerInsights) {
    await prisma.marketAnalysis.create({
      data: {
        userId,
        resumeId,
        analysisType: 'COMPREHENSIVE',
        salaryEstimateMin: insights.salaryEstimate.min,
        salaryEstimateMax: insights.salaryEstimate.max,
        confidenceLevel: insights.salaryEstimate.confidence,
        marketFitScore: insights.marketFit.score,
        skillGaps: JSON.stringify(insights.skillGaps),
        careerOpportunities: JSON.stringify(insights.careerPaths),
        competitiveAnalysis: JSON.stringify(insights.marketData),
        recommendations: JSON.stringify({
          strengths: insights.marketFit.strengths,
          improvements: insights.marketFit.improvements
        }),
        dataPoints: insights.marketData.totalJobs
      }
    })
  }

  // Helper methods for calculations and data
  private getExperienceMultiplier(level: string): number {
    switch (level) {
      case 'ENTRY': return 0.8
      case 'MID': return 1.0
      case 'SENIOR': return 1.4
      case 'EXECUTIVE': return 2.0
      default: return 1.0
    }
  }

  private getBaseSalaryForRole(role: string): number {
    const roleKeywords = role.toLowerCase()
    
    if (roleKeywords.includes('engineer') || roleKeywords.includes('developer')) {
      return 95000
    }
    if (roleKeywords.includes('manager') || roleKeywords.includes('director')) {
      return 120000
    }
    if (roleKeywords.includes('designer')) {
      return 85000
    }
    if (roleKeywords.includes('analyst')) {
      return 75000
    }
    if (roleKeywords.includes('consultant')) {
      return 90000
    }
    
    return 80000 // Default base salary
  }

  private getMarketFitDescription(score: number): string {
    if (score >= 0.9) return 'Excellent market fit - you\'re highly competitive'
    if (score >= 0.8) return 'Strong market fit - well-positioned for opportunities'
    if (score >= 0.7) return 'Good market fit - some areas for improvement'
    if (score >= 0.6) return 'Moderate market fit - consider skill development'
    return 'Developing market fit - focus on key skills and experience'
  }

  private identifyStrengths(profileVector: ProfileVector): string[] {
    const strengths = []
    
    if (profileVector.skillsExtracted.length > 10) {
      strengths.push('Diverse technical skill set')
    }
    if (profileVector.experienceLevel === 'SENIOR' || profileVector.experienceLevel === 'EXECUTIVE') {
      strengths.push('Strong experience level')
    }
    if (profileVector.industries.length > 1) {
      strengths.push('Cross-industry experience')
    }
    
    strengths.push('Relevant professional background')
    return strengths
  }

  private identifyImprovements(profileVector: ProfileVector): string[] {
    const improvements = []
    
    if (profileVector.skillsExtracted.length < 8) {
      improvements.push('Expand technical skill set')
    }
    if (profileVector.experienceLevel === 'ENTRY') {
      improvements.push('Gain more professional experience')
    }
    
    improvements.push('Stay current with industry trends')
    improvements.push('Consider additional certifications')
    
    return improvements
  }

  private getTrendingSkillsForRole(role: string): string[] {
    const roleKeywords = role.toLowerCase()
    
    if (roleKeywords.includes('engineer') || roleKeywords.includes('developer')) {
      return ['React', 'TypeScript', 'AWS', 'Docker', 'Kubernetes', 'GraphQL', 'Next.js', 'Python']
    }
    if (roleKeywords.includes('data')) {
      return ['Python', 'SQL', 'Machine Learning', 'Tableau', 'Apache Spark', 'TensorFlow', 'R', 'Power BI']
    }
    if (roleKeywords.includes('designer')) {
      return ['Figma', 'Adobe Creative Suite', 'Sketch', 'Prototyping', 'User Research', 'Design Systems']
    }
    
    return ['Communication', 'Leadership', 'Project Management', 'Agile', 'Problem Solving']
  }

  private getRecommendedSkills(role: string): string[] {
    return this.getTrendingSkillsForRole(role).slice(0, 4)
  }

  private getNextStepsForRole(role: string, experienceLevel: string) {
    // Simplified career progression logic
    const baseRole = role.toLowerCase()
    
    if (experienceLevel === 'ENTRY') {
      return [
        {
          role: `Mid-level ${role}`,
          probability: 0.8,
          requirements: ['2-3 years experience', 'Proven track record'],
          timeframe: '2-3 years'
        }
      ]
    }
    
    if (experienceLevel === 'MID') {
      return [
        {
          role: `Senior ${role}`,
          probability: 0.7,
          requirements: ['5+ years experience', 'Leadership skills'],
          timeframe: '3-5 years'
        },
        {
          role: `Lead ${role}`,
          probability: 0.6,
          requirements: ['Team leadership', 'Technical expertise'],
          timeframe: '4-6 years'
        }
      ]
    }
    
    return [
      {
        role: 'Director/VP',
        probability: 0.6,
        requirements: ['Strategic thinking', 'Team management'],
        timeframe: '5-7 years'
      }
    ]
  }

  private getAlternativeRoles(profileVector: ProfileVector) {
    // Simplified alternative role suggestions
    return [
      {
        role: 'Technical Consultant',
        transferableSkills: profileVector.skillsExtracted.slice(0, 3),
        additionalSkills: ['Client Communication', 'Business Analysis']
      },
      {
        role: 'Product Manager',
        transferableSkills: ['Technical Knowledge', 'Problem Solving'],
        additionalSkills: ['Product Strategy', 'Market Research']
      }
    ]
  }

  private getTopCompaniesForRole(role: string): string[] {
    const roleKeywords = role.toLowerCase()
    
    if (roleKeywords.includes('engineer') || roleKeywords.includes('developer')) {
      return ['Google', 'Microsoft', 'Amazon', 'Meta', 'Apple', 'Netflix', 'Uber', 'Airbnb']
    }
    
    return ['Google', 'Microsoft', 'Amazon', 'Apple', 'Meta', 'Tesla', 'Salesforce', 'Adobe']
  }
}
